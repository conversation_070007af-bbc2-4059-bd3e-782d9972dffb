package com.cfpamf.ms.insur.admin.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.enums.whale.WhaleCodeMsgEnum;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyResult;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyVo;
import com.cfpamf.ms.insur.admin.job.monitor.BusinessMonitorJob;
import com.cfpamf.ms.insur.admin.pk.config.PkMqConfig;
import com.cfpamf.ms.insur.admin.pojo.dto.order.whale.PolicyCorrectCheck;
import com.cfpamf.ms.insur.admin.pojo.vo.order.RealIndicatorsMqVO;
import com.cfpamf.ms.insur.admin.service.order.WhaleOrderService;
import com.cfpamf.ms.insur.admin.service.order.YgOrderService;
import com.cfpamf.ms.insur.admin.service.whale.tool.WhaleDataRevisionService;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.config.mq.RabbitMqUtils;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.common.dao.monitor.BusinessMonitorMapper;
import com.cfpamf.ms.insur.common.pojo.monitor.BusinessMonitor;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.GroupInsuredFixVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicyReferrerFixVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicySettlementTimeFixVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PreservationCheckResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 小鲸向海回调接口
 *
 * <AUTHOR>
 */
@Api(value = "第三方回调接口(小鲸)", tags = {"第三方回调接口(小鲸)"})
@RequestMapping({BaseConstants.ADMIN_VERSION + "/public"})
@RestController
@Slf4j
public class ThirdPartyWhaleCallbackController {

    @Autowired
    YgOrderService ygOrderService;

    @Autowired
    WhaleOrderService whaleOrderService;

    @Autowired
    RabbitMqUtils rabbitMqUtils;

    @Autowired
    private WhaleDataRevisionService whaleDataRevisionService;

    @ApiOperation(value = "阳光橙两全保险-回调", tags = "CORE")
    @PostMapping(value = "/order/whale/ygibao/accept")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "阳光橙两全保险")
    public void hand(HttpServletRequest request, HttpServletResponse response) throws IOException {

        ygOrderService.handAcceptSuccess(request, response);
    }

    @ApiOperation(value = "小鲸保单事件-回调", tags = "CORE")
    @GetMapping(value = "/order/whale/policy/event")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "小鲸保单事件")
    public void state(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("小鲸保单事件回调{}", JSON.toJSONString(request.getParameterMap()));
        whaleOrderService.handAcceptSuccess(request, response);
    }


    @ApiOperation(value = "小鲸续期保单事件-回调", tags = "CORE")
    @PostMapping(value = "/whale/policy/renewal-term")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "小鲸续期保单数据")
    public List<RenewalTermNotifyResult> renewalTermPolicy(@RequestBody List<RenewalTermNotifyVo> data) {
        log.info("小鲸推送续期保单数据-{}", JSON.toJSONString(data));
        return whaleOrderService.handleRenewalTermPolicy(data);
    }

    @ApiOperation(value = "小鲸新增注册客户数回调-回调", tags = "CORE")
    @PostMapping(value = "/whale/customer/reg")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void renewalTermPolicy(@RequestBody @Valid RealIndicatorsMqVO indicatorsMqVO) {
        log.info("小鲸新增注册客户数回调-回调-{}", JSON.toJSONString(indicatorsMqVO));
        rabbitMqUtils.sendMessage(JSON.toJSON(indicatorsMqVO), PkMqConfig.PK_EXCHANGE, PkMqConfig.PK_INSURANCE_ROUTING);
    }

    @ApiOperation(value = "查询团险在保人员信息")
    @GetMapping("/whale/active/insured/list")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public Collection<GroupInsuredFixVo> activeInsuredList(@RequestParam("policyNo")String policyNo) {
        log.info("查询团险在保人员信息-{}", policyNo);
        return whaleOrderService.listActiveInsured(policyNo);
    }

    @ApiOperation(value = "查询团险在保人员信息")
    @GetMapping(value = "/whale/vehicle/business/score",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public String vehicleBusinessScore(@RequestParam("policyNo")String policyNo) {
        log.info("查询车险风险等级信息-{}", policyNo);
        return whaleOrderService.vehicleBusinessScore(policyNo);
    }

    @ApiOperation(value = "查询保单渠道推荐人信息")
    @PostMapping(value = "/whale/referrer/info",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public List<PolicyReferrerFixVo> policyReferrerInfo(@RequestBody Map<String,List<String>> data) {
        log.info("查询保单渠道推荐人信息-{}", data);
        List<String> policyNoList = data.get("policyNoList");
        return whaleOrderService.policyReferrerInfo(policyNoList);
    }

    @ApiOperation(value = "查询批改单渠道推荐人信息")
    @PostMapping(value = "/whale/correct/referrer/info",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public List<PolicyReferrerFixVo> policyCorrectReferrerInfo(@RequestBody Map<String,List<String>> data) {
        log.info("查询批改渠道推荐人信息-{}", data);
        List<String> endorsementNoList = data.get("endorsementNoList");
        return whaleOrderService.policyCorrectReferrerInfo(endorsementNoList);
    }

    @ApiOperation(value = "获取保单结算时间")
    @PostMapping(value = "/whale/policy/settlementTime",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public Collection<PolicySettlementTimeFixVo> policySettlementTime(@RequestBody Map<String,List<String>> data) {
        log.info("获取保单结算时间-{}", data);
        if (CollectionUtils.isEmpty(data.get("data"))) {
            return Collections.emptyList();
        }
        return whaleOrderService.policySettlementTime(data.get("data"));
    }

    @ApiOperation(value = "获取批改单结算时间")
    @PostMapping(value = "/whale/correct/settlementTime",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public Collection<PolicySettlementTimeFixVo> correctSettlementTime(@RequestBody Map<String,List<String>> data) {
        log.info("获取批改单结算时间-{}", data);
        if (CollectionUtils.isEmpty(data.get("data"))) {
            return Collections.emptyList();
        }
        return whaleOrderService.correctSettlementTime(data.get("data"));
    }

    @ApiOperation(value = "小鲸修改推荐人前验证")
    @PostMapping(value = "/whale/correct/referrer/checkBeforePreservation",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public PreservationCheckResultVo checkBeforePreservation(@RequestBody ChannelRecommenderChangeVo param){
        log.info("小鲸修改推荐人前验证入参：{}", param);
        return whaleOrderService.checkBeforePreservation(param);
    }

    @ApiOperation(value = "小鲸修改推荐人前验证")
    @PostMapping(value = "/whale/correct/referrer/checkBeforePreservation/batch",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public List<PreservationCheckResultVo> checkBeforePreservationBatch(@RequestBody String param){
        log.info("小鲸修改推荐人前验证入参：{}", param);
        JSONObject json = JSON.parseObject(param);
        JSONArray data = json.getJSONArray("data");
        List<ChannelRecommenderChangeVo> list =data.toJavaList(ChannelRecommenderChangeVo.class);
        return whaleOrderService.checkBeforePreservationBatch(list);
    }

    @ApiOperation(value = "小鲸修改推荐人前验证")
    @PostMapping(value = "/whale/correct/check",produces = {"application/json;charset=UTF-8"})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public PreservationCheckResultVo checkCorrect(@RequestBody PolicyCorrectCheck param){
        log.info("小鲸保全前置校验：{}", param);
        return whaleOrderService.checkCorrect(param);
    }

    @ApiOperation(value = "小鲸数据修定事件", tags = "CORE")
    @PostMapping(value = "/whale/dataRevision")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "小鲸数据修定事件")
    public JSONObject dataRevision(@RequestBody JSONObject req) throws IOException {
        log.info("小鲸数据修定事件{}", JSON.toJSONString(req));
        final JSONObject res = new JSONObject();
        try {
            whaleDataRevisionService.dataRevision(req,res);
            res.put("code", WhaleCodeMsgEnum.SUCCESS.getCode());
            res.put("msg", WhaleCodeMsgEnum.SUCCESS.getDesc());
        } catch (MSBizNormalException e) {
            log.info("数据修订业务异常：",e);
            res.put("code", WhaleCodeMsgEnum.BUSINESS_ANOMALY.getCode());
            res.put("msg", e.getMessage());
        }
        return res;
    }

}
