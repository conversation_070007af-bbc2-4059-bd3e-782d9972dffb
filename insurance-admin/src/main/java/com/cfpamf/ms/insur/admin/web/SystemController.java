package com.cfpamf.ms.insur.admin.web;

import com.cfpamf.ms.insur.admin.pojo.po.SystemFix;
import com.cfpamf.ms.insur.admin.service.SystemFixService;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.service.EnumDictionaryService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR> 2020/11/25 17:06
 */
@Api(value = "SystemController", tags = {"系统内部方法"})
@RequestMapping(BaseConstants.ADMIN_VERSION + "/system")
@RestController
public class SystemController {


    @Autowired
    RedisUtil<String, Object> util;

    @Autowired
    SystemFixService fixService;

    @Autowired
    EnumDictionaryService dictionaryService;


    @ApiOperation("清楚缓存")
    @PostMapping("/clear/cache")
    public void clearRedis(@RequestParam("pattern") String pattern) {
        util.removePattern(pattern);
    }

    @ApiOperation("bug修复")
    @PostMapping("/fix")
    public Object fixData(@RequestBody SystemFix systemFix) {
        String loginUserId = HttpRequestUtil.getUserId();
        return fixService.fix(systemFix,loginUserId);
    }


    /**
     * 获取某个业务key的字典数据
     *
     * @param code
     * @return
     */
    @ApiOperation("获取枚举配置")
    @GetMapping("/dictionary/{code}")
    public Map<String, Object> dictByCode(@PathVariable("code") String code) {
        return dictionaryService.dictByCode(code);
    }

    /**
     * 获取某个业务key的字典数据
     *
     * @return
     */
    @ApiOperation("获取所有枚举配置")
    @GetMapping("/dictionary")
    public Map<String, Map<String, Object>> allDict() {
        return dictionaryService.allDict();
    }
}
