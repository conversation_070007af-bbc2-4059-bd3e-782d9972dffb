package com.cfpamf.ms.insur.admin.renewal.controller;

import com.cfpamf.ms.insur.admin.event.OrderRenewCommissionEvent;
import com.cfpamf.ms.insur.admin.event.handler.OrderRenewCommissionHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.SmOrderRenewalFollowDto;
import com.cfpamf.ms.insur.admin.renewal.enums.RenewalOrderStatusType;
import com.cfpamf.ms.insur.admin.renewal.exception.RenewalConfigBusinessException;
import com.cfpamf.ms.insur.admin.renewal.form.OverRenewalReasonForm;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.form.WxOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.service.InsuranceRenewService;
import com.cfpamf.ms.insur.admin.renewal.service.RenewalOrderService;
import com.cfpamf.ms.insur.admin.renewal.service.SmOrderRenewalFollowService;
import com.cfpamf.ms.insur.admin.renewal.vo.*;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.util.ExcelBuilderUtil;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.renewal.RenewalBasicQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.cfpamf.ms.insur.base.constant.BaseConstants.DATE_FORMAT_YYYY_MM_DD;

/**
 * 续保订单控制器
 *
 * <AUTHOR>
 * @date 2021/5/13 10:04
 */
@Slf4j
@Api(value = "保险续保订单(凤巢)", tags = {"保险续保订单(凤巢)"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/insurance/renewal"})
@RestController
public class InsuranceRenewalController {
    public final String XLSX_SUFFIX = ".xlsx";
    @Autowired
    private InsuranceRenewService insuranceRenewService;
    @Autowired
    private RenewalOrderService renewalOrderService;

    @Autowired
    private SmOrderRenewalFollowService smOrderRenewalFollowService;


    /**
     * 搜索微信端续保列表
     *
     * @param renewalOrderSearchForm
     * @return
     */
    @PostMapping("/search")
    @ApiOperation("搜索续保列表")
    @SystemLog(module = LogConstants.MODULE_SM_RENEWAL_ORDER_QUERY, actionType = LogActionType.QUERY, descrption = "搜索微信端续保列表")
    public PageInfo<InsuranceRenewedOrderVo> searchRenewalOrderVo(@RequestBody @Valid RenewalOrderSearchForm renewalOrderSearchForm) {
        return insuranceRenewService.searchRenewalOrderVo(renewalOrderSearchForm);
    }



    @GetMapping("/detail")
    @ApiOperation("查看订单详情")
    @SystemLog(module = LogConstants.MODULE_SM_RENEWAL_ORDER_QUERY, actionType = LogActionType.QUERY, descrption = "查看订单详情")
    public OrderDetailVo findByOrderInsuredId(@RequestParam(required = true)  String policyNo,@RequestParam(required = true)  String productAttrCode) {
        OrderDetailQuery query = new OrderDetailQuery();
        query.setPolicyNo(policyNo);
        query.setProductAttrCode(productAttrCode);
        return insuranceRenewService.findOrderByPolicyNo(query);
    }


    @PutMapping("/over/reason")
    @ApiOperation("增加断保原因")
    @SystemLog(module = LogConstants.MODULE_SM_RENEWAL_ORDER_QUERY, actionType = LogActionType.UPDATE, descrption = "增加断保原因")
    public void addOverRenewalReason(@RequestParam(required = true) String policyNo, @RequestParam(required = true) String productAttrCode, @RequestBody @Valid OverRenewalReasonForm overRenewalReasonForm) {
        insuranceRenewService.addOverRenewalReason(policyNo,productAttrCode, overRenewalReasonForm.getOverRenewalReason());
    }

    /**
     * 导出续保订单
     *
     * @param renewalOrderSearchForm
     * @return
     */
    @GetMapping("/report/{reportType}")
    @ApiOperation("导出续保订单")
    @ApiParam(name = "reportType",value = "over/waited/renewed")
    @SystemLog(module = LogConstants.MODULE_SM_RENEWAL_ORDER_QUERY, actionType = LogActionType.DOWNLOAD, descrption = "导出续保订单")
    public void reportRenewalOrderVo(@ModelAttribute @Valid RenewalOrderSearchForm renewalOrderSearchForm, @PathVariable String reportType, HttpServletResponse response) {
        try (OutputStream os = response.getOutputStream()) {
            //获取导出数据
            List excelVoList = insuranceRenewService.getReportRenewalOrderList(renewalOrderSearchForm, reportType);
            RenewalOrderStatusType renewalOrderStatusType = RenewalOrderStatusType.getRenewalOrderStatusType(reportType);

            if (Objects.isNull(renewalOrderStatusType)) {
                throw RenewalConfigBusinessException.RENEWAL_ORDER_REPORT_ERROR;
            }
            //配置响应头信息
            String fileName = URLEncoder.encode(renewalOrderStatusType.getReportExcelName() + new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD).format(new Date()) + XLSX_SUFFIX, StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            //构建导出
            ExcelBuilderUtil.newInstance()
                    .createSheet(renewalOrderStatusType.getReportExcelName())
                    .buildSheetHead(renewalOrderStatusType.getClazz())
                    .addSheetData(excelVoList)
                    .write(os);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw RenewalConfigBusinessException.RENEWAL_ORDER_REPORT_ERROR;
        }
    }
}
