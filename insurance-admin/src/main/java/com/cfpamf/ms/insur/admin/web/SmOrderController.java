package com.cfpamf.ms.insur.admin.web;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.bms.facade.vo.ModuleVO;
import com.cfpamf.ms.insur.admin.constant.order.EnumImportOrder;
import com.cfpamf.ms.insur.admin.enums.EnumOrderPayStatus;
import com.cfpamf.ms.insur.admin.enums.correct.CorrectProject;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.external.AutoOrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionRedoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.correct.BatchCorrectResult;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderPerson;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SimpleOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancel;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImport;
import com.cfpamf.ms.insur.admin.pojo.query.*;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.SmLongInsuranceCommissionDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.SmLongInsuranceCommissionVO;
import com.cfpamf.ms.insur.admin.pojo.vo.commission.SmPgLongInsuranceCommissionVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderMasterVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderNewVO;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.commission.CommissionManagerService;
import com.cfpamf.ms.insur.admin.service.correct.OrderCorrectService;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.bean.CodeName;
import com.cfpamf.ms.insur.base.bean.CommonResult;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.DownloadUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.base.util.ValidatorUtils;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 小额保险订单接口
 *
 * <AUTHOR>
 */
@Api(value = "保险订单管理接口", tags = {"保险订单管理接口"})
@RequestMapping({BaseConstants.ADMIN_VERSION + "/order"})
@RestController
@Slf4j
public class SmOrderController extends AbstractController {

    /**
     * 小额保险订单接口
     */
    @Autowired
    private SmOrderManageService orderManageService;

    /**
     * 小额保险订单接口
     */
    @Autowired
    private SmOrderCoreService orderCoreService;
    /**
     * 车险订单接口
     */
    @Autowired
    private SmCarOrderManageService carOrderManageService;
    /**
     * 团险订单接口
     */
    @Autowired
    private SmGroupOrderManageService groupOrderManageService;
    @Autowired
    private SmOrderConvertCommissionService smOrderConvertCommissionService;

    @Autowired
    private BmsConfig bmsConfig;
    @Autowired
    private CommissionManagerService commissionManagerService;

    @Autowired
    OrderPersonService personService;

    @Autowired
    private OrderCorrectService correctService;

    @Autowired
    private SmCommissionDetailService smCommissionDetailService;

    /**
     * 事件引擎
     */
    @Lazy
    @Autowired
    EventBusEngine eventBusEngine;

    /**
     * 查询订单分页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单分页列表")
    @PostMapping("/query")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单分页列表")
    public PageInfo<SmOrderListVO> getOrderByPage(@RequestBody SmOrderQuery query) {
        PageInfo<SmOrderListVO> orderByPage = orderManageService.getOrderByPage(query);
        orderByPage.getList().forEach(or -> or.setPayStatusName(EnumOrderPayStatus.getDesc(or.getPayStatus())));
        return orderByPage;
    }

    @ApiOperation(value = "查询订单总数")
    @PostMapping("/query/cnt")
    public Integer getOrderCnt(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderCnt(query);
    }

    /**
     * 查询车险订单分页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询车险订单分页列表")
    @PostMapping("/queryCarOrder")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询车险订单分页列表")
    public PageInfo<SmOrderListVO> getCarOrderByPage(@RequestBody SmOrderQuery query) {
        PageInfo<SmOrderListVO> orderByPage = orderManageService.getCarOrderByPage(query);
        orderByPage.getList().forEach(or -> or.setPayStatusName(EnumOrderPayStatus.getDesc(or.getPayStatus())));
        return orderByPage;
    }

    /**
     * 查询订单分页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单列表分页-新一个订单一行记录")
    @PostMapping("/v2/query")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单分页列表")
    public PageInfo<SmOrderNewVO> getOrderByPageNew(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderByPageV2(query);
    }


    /**
     * 查询订单分页列表汇总
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单分页列表汇总")
    @PostMapping("/query/smy")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单分页列表汇总")
    public SmOrderSummaryVO getOrderSummary(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderSummary(query);
    }

    /**
     * 下载小额保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载小额保险订单")
    @GetMapping("/download")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载小额保险订单")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_EXPORT)
    public void downloadOrders(SmOrderQuery query, HttpServletResponse response) {
        orderManageService.downloadOrders(query, response);
    }

    /**
     * 查询小额保险提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询小额保险提成页列表")
    @PostMapping("/query/commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询小额保险提成页列表")
    public PageInfo<SmOrderListVO> getOrderCommissionByPage(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderIncludeReconciliationByPage(query);
    }

    @ApiOperation(value = "查询小额保险提成页总数")
    @PostMapping("/query/commission/cnt")
    public Integer getOrderCommissionCntByPage(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderCommissionCntByPage(query);
    }


    /**
     * 查询小额保险提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询长险提成页列表")
    @PostMapping("/query/long/insurance/commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询长险提成页列表")
    public PageInfo<SmLongInsuranceCommissionVO> getLongInsuranceOrderCommissionByPage(@RequestBody SmOrderQuery query) {
        return commissionManagerService.queryCommission(query);
    }

    @ApiOperation(value = "查询长险提成总数")
    @PostMapping("/query/long/insurance/commission/cnt")
    public Integer countLongInsuranceOrderCommission(@RequestBody SmOrderQuery query) {
        return commissionManagerService.countLongInsuranceOrderCommission(query);
    }

    /**
     * T+1查询小额保险提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "T+1查询长险提成页列表")
    @PostMapping("/query/pgLong/insurance/commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "T+1查询长险提成页列表")
    public PageInfo<SmPgLongInsuranceCommissionVO> getSafePgLongInsuranceOrderCommissionByPage(@RequestBody SmOrderQuery query) {
        return commissionManagerService.querySafePgCommission(query);
    }

    @ApiOperation(value = "查询长险提成页列表-汇总数据")
    @PostMapping("/query/long/insurance/commission/summary")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询长险提成页列表-汇总数据")
    public SmOrderSummaryVO getLongInsuranceOrderCommissionSummary(@RequestBody SmOrderQuery query) {
        return commissionManagerService.queryLongCommissionSummary(query);
    }

    @ApiOperation(value = "T+1查询长险提成页列表-汇总数据")
    @PostMapping("/query/pgLong/insurance/commission/summary")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询长险提成页列表-汇总数据")
    public SmOrderSummaryVO getSafePgLongInsuranceOrderCommissionSummary(@RequestBody SmOrderQuery query) {
        return commissionManagerService.querySafePgLongCommissionSummary(query);
    }

    /**
     * 查询小额保险提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "下载长险提成")
    @GetMapping("/download/long/insurance/commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.DOWNLOAD, descrption = "下载长险提成页列表")
    public void downloadLongInsuranceOrderCommission(SmOrderQuery query, HttpServletResponse response) {
        commissionManagerService.downloadLongInsuranceOrderCommission(query, response);
    }
    /**
     * 查询小额保险提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "T+1下载长险提成")
    @GetMapping("/download/pgLong/insurance/commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.DOWNLOAD, descrption = "下载长险提成页列表")
    public void downloadSafePgLongInsuranceOrderCommission(SmOrderQuery query, HttpServletResponse response) {
        commissionManagerService.downloadSafePgLongInsuranceOrderCommission(query, response);
    }
    /**
     * 查询长险提成明细
     *
     * @param appStatus
     * @param insuredIdNumber
     * @param termNum
     * @param policyNo
     * @return
     */
    @ApiOperation(value = "查询长险提成明细")
    @GetMapping("/long/insurance/commission/detail")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询长险提成明细")
    public List<SmLongInsuranceCommissionDetailVO> getLongInsuranceOrderCommissionByPage(@RequestParam("policyNo") @ApiParam("保单号") String policyNo,
                                                                                         @RequestParam("insuredIdNumber") @ApiParam("被保人身份证号") String insuredIdNumber,
                                                                                         @RequestParam("termNum") @ApiParam("缴费期数") Integer termNum,
                                                                                         @RequestParam("appStatus") @ApiParam("保单状态") String appStatus) {
        return commissionManagerService.getCommissionDetail(policyNo, insuredIdNumber, appStatus, termNum);
    }

    /**
     * 查询小额保险提成列表汇总
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询小额保险提成列表汇总")
    @PostMapping("/query/commission/smy")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询小额保险提成列表汇总")
    public SmOrderSummaryVO getOrderCommissionSummary(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderSummary(query);
    }

    /**
     * 下载小额保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载小额保险提成")
    @GetMapping("/commission/download")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.DOWNLOAD, descrption = "下载小额保险提成")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_CMISON_EXPORT)
    public void downloadOrderCommission(SmOrderQuery query, HttpServletResponse response) {
        orderManageService.downloadOrders(query, response);
    }

    /**
     * 更新订单与泛华同步
     *
     * @param id
     */
    @ApiOperation(value = "更新订单与泛华同步")
    @PostMapping("/order/{id}")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.UPDATE, descrption = "更新订单与泛华同步")
    public void updateOrderInfo(@PathVariable String id) {
        orderCoreService.updateOrderPolicyInfo(id);
    }


    @ApiOperation("获取订单详情")
    @GetMapping("/{orderId}")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单详情")
    public OrderQueryResponse getOrderInfo(@PathVariable("orderId") String orderId) {
        return orderCoreService.getOrderInfo4Local(orderId);
    }

    /**
     * 批量更新订单与泛华同步
     *
     * @param ids
     */
    @ApiOperation(value = "批量更新订单与泛华同步")
    @PostMapping("/update/batch")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.UPDATE, descrption = "批量更新订单与泛华同步")
    public void batchUpdateFhOrderInfo(@RequestParam String[] ids) {
        Stream.of(ids).forEach(id -> orderCoreService.updateOrderPolicyInfo(id));
    }

    /**
     * 查询补单记录
     *
     * @return
     */
    @ApiOperation(value = "查询补单记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页数量", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "createDateStart", value = "创建时间开始", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "createDateEnd", value = "创建时间结束", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "spmDateStart", value = "补单时间开始", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "spmDateEnd", value = "补单时间结束", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "fhOrderId", value = "订单号", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "recommendPerson", value = "推荐人", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "applicantPersonName", value = "投保人", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "insuredPersonName", value = "被保人", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "agentName", value = "代理人", dataType = "string", paramType = "query")
    })
    @GetMapping("/supplement")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.QUERY, descrption = "查询补单记录")
    public PageInfo<SmOrderSupmtRecordVO> getSupplementOrder(OrderSupmtQuery query) {
        return orderManageService.getSupplementOrder(query);
    }

    /**
     * 订单补单同步到系统
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "订单补单同步到系统")
    @PostMapping("/supplement")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单补单同步到系统")
    public Object supplementOrder(@RequestBody SmOrderSupmtDTO dto) throws Exception {
        orderManageService.insertSupplementOrder(dto);
        orderManageService.pushSupplementSuccess(dto.getFhOrderId());
//        orderCoreService.updateOrderPolicyInfo(dto.getFhOrderId());
        return CommonResult.successResult(Boolean.TRUE);
    }

    /**
     * 订单批改项目列表
     *
     * @return
     */
    @ApiOperation(value = "订单批改项目列表")
    @GetMapping("/correct/fields")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.QUERY, descrption = "订单批改项目列表")
    public List<CodeName> getOrderCorrectFields() {
        return Stream.of(CorrectProject.values())
                .map(f -> new CodeName(f.getCode(), f.getName()))
                .collect(Collectors.toList());
    }

    /**
     * 查询订单批改记录列表
     *
     * @return
     */
    @ApiOperation(value = "查询订单批改记录列表",tags = "CORE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页数量", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "startDate", value = "批改时间From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "批改时间To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "policyNo", value = "保单号", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "fieldCode", value = "变更项目", dataType = "string", paramType = "query")
    })
    @GetMapping("/correct")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.QUERY, descrption = "查询订单批改记录列表")
    public PageInfo<SmOrderCorrectRecordVO> getOrderCorrectRecords(SmOrderCorrectQuery query) {
        return orderManageService.getOrderCorrectRecords(query);
    }

    /**
     * 订单批改接口
     *
     * @return
     */
    @ApiOperation(value = "订单批改接口")
    @PostMapping("/correct")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单批改接口")
    public void correctOrder(@RequestBody SmOrderCorrectDTO dto) {
        orderManageService.correctOrder(dto);
    }

    /**
     * 订单批改接口
     *
     * @return
     */
    @ApiOperation(value = "批量批改订单")
    @PutMapping("/batch/correct")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单批量批改接口")
    public BatchCorrectResult batchCorrectOrder(@RequestParam("file") String file) {
        String userId = HttpRequestUtil.getUserId();
        log.info("批量上传批改单:{},{}", userId, file);
        return correctService.batchCorrect(userId, file);
    }

    /**
     * 订单批改接口
     *
     * @return
     */
    @ApiOperation(value = "订单批改推荐人导入接口")
    @PostMapping("/correct/importCorrectOrderRecommender")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单批改推荐人导入接口")
    public void importCorrectOrderRecommender(@RequestBody SmOrderImportDTO dto) throws Exception {
        orderManageService.importCorrectOrderRecommender(dto, HttpRequestUtil.getUserId());
    }


    /**
     * 订单批改接口
     *
     * @return
     */
    @ApiOperation(value = "订单批改接口-保单状态（退保）")
    @PostMapping("/correct/appStatus")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单批改接口")
    public void correctOrderCancel(@RequestBody SmOrderCorrectCancelDTO dto) {
        orderManageService.correctOrderAppStatus(dto);
    }

    @ApiOperation(value = "订单的导入接口"
            , notes = " 从业务场景来说不是高频的接口 测试的时候不要高频的调用(会产生很多异步调用高频调用可能会造成背压) 提成以及客户信息是异步生成的")
    @PostMapping("/import")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单的导入接口")
    public SmOrderImportConfirmDTO importOrder(@RequestBody SmOrderImportDTO dto) throws Exception {

        ValidatorUtils.validateParam(dto);
        String userId = HttpRequestUtil.getUserId();
        String attrCode = dto.getProductAttrCode();
        log.info("开始导入订单数据|{}|{}", userId, dto);

        //车险
        if (Objects.equals(attrCode, EnumImportOrder.car.name())) {
            return carOrderManageService.importOrder(dto, userId);
        }

        //个人佣金
        if (Objects.equals(attrCode, EnumImportOrder.carCommission.name())) {
            return carOrderManageService.importOrderCommmission(dto, userId);
        }

        //个险（短险）
        if (Objects.equals(attrCode, EnumImportOrder.shortTermPersonalIns.name())) {
            return orderManageService.importShortTermPersonalIns(dto, userId);
        }

        //个险（长险）
        if (Objects.equals(attrCode, EnumImportOrder.longTermPersonalIns.name())) {
            return orderManageService.importLongTermPersonalIns(dto, userId);
        }

        //团险
        if (Objects.equals(attrCode, EnumImportOrder.groupIns.name())) {
            return orderManageService.importGroupOrder(dto, userId);
        }

        //团险佣金
        if (Objects.equals(attrCode, EnumImportOrder.groupCommission.name())) {
            return groupOrderManageService.importGroupOrderCommmission(dto, userId);
        }
        //折算比例导入
        if (Objects.equals(attrCode, EnumImportOrder.convertCommission.name())) {
            return smOrderConvertCommissionService.importOrderConvertCommission(dto, userId);
        }
        throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持该类型的导入");
    }


    @ApiOperation(value = "订单的导入确认接口")
    @GetMapping("/importConfirm")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.ADD, descrption = "订单的导入确认接口")
    public void importConfirm(@RequestParam("importId") Integer importId) throws Exception {
        orderManageService.importConfirm(importId, HttpRequestUtil.getUserId());
    }

    @ApiOperation(value = "订单导入列表查询")
    @GetMapping("/import")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.QUERY, descrption = "订单导入列表查询")
    public PageInfo<SmOrderImport> queryImport(SmOrderImportQuery query) {
        return orderManageService.importList(query);
    }

    @ApiOperation(value = "查询保单所有被保人")
    @GetMapping("/policy/insured")
    @SystemLog(module = LogConstants.MODULE_SM_SUMPT, actionType = LogActionType.QUERY, descrption = "保单被保人查询")
    public List<SmPolicyInsured> queryImport(@RequestParam("policyNo") String policyNo) {

        return orderManageService.listInsuredByPolicyNo(policyNo);
    }

    @ApiOperation(value = "下载保单[大家]")
    @GetMapping("/policy/download")
    public void downloadPolicy(@ApiParam("下载地址") @RequestParam("url") String url, HttpServletResponse response) throws IOException {

        DownloadUtil.webDownloadByUrl(url, response);
    }


    @ApiOperation("订单支付信息查询")
    @GetMapping("/{orderId}/payment")
    public QueryOrderVO paymentInfo(@PathVariable("orderId") String orderId) {
        QueryOrderVO paymentInfo = orderManageService.getPaymentInfo(orderId);
        if (Objects.isNull(paymentInfo)
                || Objects.isNull(paymentInfo.getOrderNo())) {
            return null;
        }
        return paymentInfo;
    }

    @ApiOperation("订单归属查询")
    @GetMapping("/{orderId}/order-master")
    public SmOrderMasterVO orderMaster(@PathVariable("orderId") String orderId) {
        return orderManageService.getOrderMaster(orderId);
    }

    @ApiOperation("退保信息查询")
    @GetMapping("/{orderId}/cancel")
    public List<SmCancel> cancel(@PathVariable("orderId") String orderId) {
        return orderManageService.getCancel(orderId);
    }

    @ApiOperation("订单产品保障信息快照查询")
    @GetMapping("/{orderId}/product-coverage")
    public List<SmProductCoverageAmountVO> productCoverage(@PathVariable("orderId") String orderId) {
        return orderManageService.getProductCoverage(orderId);
    }

    /*** begin 2021-06-23 s46 订单拆分  需求**/
    /**
     * 查询订单分页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单列表分页-新一个订单一行记录" ,tags = "CORE")
    @PostMapping("/v3/query")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单分页列表")
    public PageInfo<SmOrderV3ListVO> getOrderByPageV3(@RequestBody SmOrderV3Query query) {
        return orderManageService.getOrderByPageV3(query);
    }

    @ApiOperation(value = "查询订单列表分页-订单维度-总页数")
    @PostMapping("/v3/query/cnt")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单列表分页-订单维度-总页数")
    public Integer getOrderCntByPageV3(@RequestBody SmOrderV3Query query) {
        return orderManageService.getOrderCntByPageV3(query);
    }

    /**
     * 查询车险订单分页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询车险订单分页列表",tags = "CORE")
    @PostMapping("/v3/queryCarOrder")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询车险订单分页列表")
    public PageInfo<SmOrderV3ListVO> getCarOrderByPageV3(@RequestBody AutoOrderV3Query query) {
        PageInfo<SmOrderV3ListVO> orderByPage = orderManageService.getCarOrderByPageV3(query);
        if (!CollectionUtils.isEmpty(orderByPage.getList())) {
            orderByPage.getList().forEach(or -> or.setPayStatusName(EnumOrderPayStatus.getDesc(or.getPayStatus())));
        }
        return orderByPage;
    }

    @ApiOperation(value="获取订单详情",tags = "CORE")
    @GetMapping("/v3/{orderId}")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单详情")
    public OrderQueryResponse getOrderInfoV3(@PathVariable("orderId") String orderId,
                                             @RequestParam(value = "insuredSn", required = false) Integer insuredSn) {
        return orderCoreService.getOrderInfo4Local_V3(orderId, insuredSn);
    }

    @ApiOperation("获取车险订单详情")
    @GetMapping("/auto/v3/{orderId}")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询订单详情")
    public AutoOrderQueryResponse getAutoOrderInfoV3(@PathVariable("orderId") String orderId) {
        return orderCoreService.getAutoOrderInfo4Local_V3(orderId);
    }

    /**
     * 下载非车险保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载非车险保险订单")
    @GetMapping("/v3/download")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载非车险保险订单")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_EXPORT_NEW)
    public void downloadOrdersV3(SmOrderV3Query query, HttpServletResponse response) {
        orderManageService.downloadOrdersV3(query, response);
    }

    /**
     * 下载车险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载车险订单")
    @GetMapping("/v3/downloadCarOrder")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载车险订单")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_EXPORT_NEW)
    public void downloadCarOrdersV3(AutoOrderV3Query query, HttpServletResponse response) {
        orderManageService.downloadCarOrdersV3(query, response);
    }

    /**
     * 汇总V3版本
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询保险订单列表汇总V3")
    @PostMapping("/query/v3/smy")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询保险订单列表汇总V3")
    public SmOrderSummaryVO getOrderSummaryV3(@RequestBody SmOrderV3Query query) {
        return orderManageService.getOrderSummaryV3(query);
    }

    /**
     * 汇总V3版本
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询车险险订单列表汇总V3")
    @PostMapping("/query/auto/v3/smy")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询保险订单列表汇总V3")
    public SmOrderSummaryVO getAutoOrderSummaryV3(@RequestBody AutoOrderV3Query query) {
        return orderManageService.getAutoOrderSummaryV3(query);
    }

    /****** s50 分销订单 ********/
    /**
     * 查询订单分页列表s
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询分销订单分页列表")
    @PostMapping("/dist/query")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.QUERY, descrption = "查询分销订单分页列表")
    public PageInfo<SmOrderListVO> getDistOrderByPage(@RequestBody SmOrderQuery query) {
        PageInfo<SmOrderListVO> orderByPage = orderManageService.getOrderByPage(query);
        orderByPage.getList().forEach(or -> or.setPayStatusName(EnumOrderPayStatus.getDesc(or.getPayStatus())));
        return orderByPage;
    }

    @ApiOperation(value = "查询分销订单总数")
    @PostMapping("/dist/query/cnt")
    public Integer getDistOrderCnt(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderCnt(query);
    }


    /**
     * 下载小额保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载分销订单")
    @GetMapping("/dist/download")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载分销订单")
    @AuthValidate(value = ApiPermsEnum.SM_DIST_ORDER_EXPORT)
    public void downloadDistOrders(SmOrderQuery query, HttpServletResponse response) {
        List<ModuleVO> modules = ThreadUserUtil.USER_MODULES_TL.get();
        if (modules.stream().noneMatch(m -> Objects.equals(m.getModuleCode(), bmsConfig.getDistributionOrderDownloadUrl()))) {
            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
        orderManageService.downloadOrders(query, response);
    }

    /**
     * 下载小额保险订单
     *
     * @param fhOrderId
     * @param oldRecommendId
     */
    @ApiOperation(value = "下载分销订单")
    @GetMapping("/order/updateOrderRecommendByOrderId")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载分销订单")
    @AuthValidate(value = ApiPermsEnum.SM_DIST_ORDER_EXPORT)
    public void updateOrderRecommendByOrderId(@RequestParam("fhOrderId") String fhOrderId, @RequestParam("oldRecommendId") String oldRecommendId, @RequestParam("newRecommendId") String newRecommendId) {
        orderManageService.updateOrderRecommendByOrderId(fhOrderId, oldRecommendId, newRecommendId);
    }

    /******* s54 佣金改造提成列表*********/
    /**
     * 新佣金提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "新佣金提成列表",tags = "CORE")
    @PostMapping("/v3/query/commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "新佣金提成页列表")
    public PageInfo<SmOrderListVO> listOrderCommissionByPage(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderCommissionByPageV3(query);
    }

    @ApiOperation(value = "新佣金提成总数")
    @PostMapping("/v3/query/commission/cnt")
    public Integer countOrderCommission(@RequestBody SmOrderQuery query) {
        return orderManageService.countOrderCommission(query);
    }

    /******* s54 佣金改造提成列表*********/
    /**
     * T+1新佣金提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "T+1新佣金提成列表",tags = "CORE")
    @PostMapping("/v3/query/pgCommission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "T+1新佣金提成页列表")
    public PageInfo<SmOrderListVO> listSafePgOrderCommissionByPage(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderCommissionPgShortByPageV3(query);
    }

    /**
     * 新佣金提成页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "新佣金提成列表(加佣)")
    @PostMapping("/v3/query/add-commission")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "新佣金提成列表(加佣)")
    public PageInfo<SmOrderListVO> listOrderAddCommissionByPage(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderAddCommissionByPage(query);
    }

    /**
     * 下载小额保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载新加佣提成列表")
    @GetMapping("/v3/downloadAddCommission")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载新加佣提成列表")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_EXPORT)
    public void downloadAddCommission(SmOrderQuery query, HttpServletResponse response) {
        orderManageService.downloadAddCommission(query, response);
    }


    /**
     * 下载小额保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载新佣金提成列表")
    @GetMapping("/v3/downloadCommission")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载新佣金提成列表")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_EXPORT)
    public void downloadCommission(SmOrderQuery query, HttpServletResponse response) {
        orderManageService.downloadOrderCommissionV3(query, response);
    }
    /**
     * 下载T+1小额保险订单
     *
     * @param query
     * @param response
     */
    @ApiOperation(value = "下载T+1新佣金提成列表")
    @GetMapping("/v3/downloadSafePgCommission")
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.DOWNLOAD, descrption = "下载T+1新佣金提成列表")
    @AuthValidate(value = ApiPermsEnum.SM_ORDER_EXPORT)
    public void downloadSafePgCommission(SmOrderQuery query, HttpServletResponse response) {
        orderManageService.downloadPgOrderCommissionV3(query, response);
    }
    /**
     * 查询小额保险提成列表汇总
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询小额保险新提成列表汇总")
    @PostMapping("/v3/query/commission/smy")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询小额保险新提成列表汇总")
    public SmOrderSummaryVO getOrderCommissionSummaryV3(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderCommissionSummaryV3(query);
    }

    /**
     * 查询小额保险提成列表汇总
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询小额保险新提成列表汇总")
    @PostMapping("/v3/query/pgCommission/smy")
    @SystemLog(module = LogConstants.MODULE_SM_COMMISSION, actionType = LogActionType.QUERY, descrption = "查询小额保险新提成列表汇总")
    public SmOrderSummaryVO getOrderPgCommissionSummaryV3(@RequestBody SmOrderQuery query) {
        return orderManageService.getOrderPgCommissionSummaryV3(query);
    }

    @ApiOperation("查询订单人信息")
    @GetMapping("/person")
    public OrderPerson person(@RequestParam("fhOrderId") String fhOrderId) {
        return personService.persons(fhOrderId);
    }

    @ApiOperation("根据下单人或投保人查询订单简遍信息")
    @GetMapping("/selectByIdNumber")
    public List<SimpleOrderDTO> selectByIdNumber(@RequestParam("idNumber") String idNumber) {
        return personService.selectByIdNumber(idNumber);
    }

    @ApiOperation("触发佣金事件（用于测试保司未回传退保金额案例）")
    @GetMapping("/event")
    public void event(@RequestParam("orderId") String orderId) {
        //佣金计算触发
        eventBusEngine.publish(new OrderCommissionChangeEvent(orderId, Boolean.FALSE, Boolean.TRUE));
    }

    /**
     * 覆盖刷数场景重算佣金
     *
     * @param redoDto 佣金重算DTO
     */
    @PostMapping("/redoCommission")
    @ApiOperation("重算佣金（覆盖刷数场景）")
    public void redoCommission(@RequestBody CommissionRedoDTO redoDto) {
        smCommissionDetailService.redoCommission(redoDto);
    }
}
