package com.cfpamf.ms.insur.admin.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.CvgRespTypeEnum;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.aicheck.AiCheckWayEnum;
import com.cfpamf.ms.insur.admin.enums.product.ProductAttrEnum;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.product.*;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductNotify;
import com.cfpamf.ms.insur.admin.pojo.po.aicheck.AkProductQuestionnaireHis;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductConfirm;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductConfirmHistory;
import com.cfpamf.ms.insur.admin.pojo.query.ActivityProductQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmPlanPageQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmProductQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.product.*;
import com.cfpamf.ms.insur.admin.pojo.vo.product.basic.ProductAttrVo;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import com.cfpamf.ms.insur.admin.service.SmProductHistoryService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.SystemFileService;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireHisService;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireService;
import com.cfpamf.ms.insur.admin.service.product.ProductQueryService;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.bean.CommonResult;
import com.cfpamf.ms.insur.base.bean.IdName;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.ExcelBuilderUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.query.SmGlProductQuoteQuery;
import com.cfpamf.ms.mp.facade.api.MpNotifyFacade;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.cfpamf.ms.insur.base.constant.BaseConstants.DATE_FORMAT_YYYYMMDD;
import static com.cfpamf.ms.insur.base.util.ExcelBuilderUtil.XLSX_SUFFIX;

/**
 * 小额保险产品接口
 *
 * <AUTHOR>
 */
@Api(value = "保险产品管理接口", tags = {"保险产品管理接口"})
@RequestMapping(BaseConstants.ADMIN_VERSION + "/product")
@RestController
@Slf4j
public class SmProductController extends AbstractController {

    public static final String EXCEL_FILE_NAME_PREFIX = "产品列表";
    /**
     * 小额保险产品service
     */
    @Autowired
    private SmProductService service;

    /**
     * 小额保险产品service(快照)
     */
    @Autowired
    private SmProductHistoryService historyService;

    /**
     * 字典service
     */
    @Autowired
    private DictionaryService dService;

    @Autowired
    private AkProductQuestionnaireHisService akProductQuestionnaireHisService;

    @Autowired
    private AkProductQuestionnaireService akProductQuestionnaireService;

    @Autowired
    private MpNotifyFacade mpNotifyFacade;

    /**
     * 查询管理后端产品列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询管理后端产品列表")
    @PostMapping("/backend")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询管理后端产品列表")
    public PageInfo<SmProductListVO> getBackendProductByPage(@RequestBody SmProductQuery query) {
        return service.getBackendProductByPage(query);
    }

    /**
     * 查询管理后端最新产品列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询管理后端最新产品列表")
    @PostMapping("/newestBackend")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询管理后端最新产品列表")
    public PageInfo<SmProductListVO> getNewestBackendProductByPage(@RequestBody SmProductQuery query) {
        return service.getNewestProductList(query);
    }


    /**
     * 查询管理后端产品列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "导出产品列表")
    @GetMapping("/backend/download")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DOWNLOAD, descrption = "导出产品列表")
    public void downloadBackendProduct(SmProductQuery query, HttpServletResponse response) {

        List<SmProductExcelVO> smProductExcelVOList = service.getSmProductExcelVOList(query);
        try (OutputStream os = response.getOutputStream()) {
            //配置响应头信息
            String fileName = URLEncoder.encode(EXCEL_FILE_NAME_PREFIX + new SimpleDateFormat(DATE_FORMAT_YYYYMMDD).format(new Date()) + XLSX_SUFFIX, StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            //构建导出
            ExcelBuilderUtil.newInstance()
                    .createSheet(EXCEL_FILE_NAME_PREFIX)
                    .buildSheetHead(SmProductExcelVO.class)
                    .addSheetData(smProductExcelVOList)
                    .write(os);
        } catch (Exception e) {
            log.warn("文件下载失败", e);
            throw new MSBizNormalException("", "文件下载失败");
        }
    }

    /**
     * 修改产品推荐标记（1推荐 0不推荐）
     *
     * @param id
     * @param activeFlag
     */
    @ApiOperation(value = "修改产品推荐标记（1推荐 0不推荐）")
    @PutMapping("/{id}/activeFlag")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "修改产品推荐标记（1推荐 0不推荐）")
    public void updateProductActiveFlag(@PathVariable int id, @RequestParam int activeFlag) {
        service.updateProductActiveFlag(id, activeFlag);
    }

    /**
     * 查询活动产品列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询活动产品列表")
    @PostMapping("/simple")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询活动产品列表")
    public PageInfo<SmActivityProductVO> getActivityProducts(@RequestBody ActivityProductQuery query) {
        return service.getActivityProducts(query);
    }

    /**
     * 查询产品详情基本信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "S48-查询产品详情基本信息")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/basic")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品详情基本信息")
    public SmProductDetailVO getProductById(@PathVariable int id, @RequestParam(value = "version", required = false)
    Integer version) {
        //此接口 返回 productCategoryName，不能使用，没有返回所有类型名称
        return historyService.getProductById(id, version);
    }

    /**
     * 更新排序
     * 调整排序：
     * ①由N调整为M（M＞N）,排序为 N+1,N+2...M-1,M号 的产品，排序均减一
     * 比如某个产品由3调整为5，原来的4号产品变为3，原来的5号产品变为4；
     * ②由M调整为N（M＞N）,排序为 N,N+1...M-1 的产品，排序均+1
     * 比如某个产品由5调整为3，原来的3号产品变为4，原来的4号产品变为5；
     *
     * @param id
     * @param newSortNum
     * @return
     */
    @ApiOperation(value = "更新排序")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PutMapping("/{id}/sortNum")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "更新排序")
    public void updateSortNum(@PathVariable int id, @RequestParam int newSortNum) {
        if (newSortNum <= 0) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "序号错误");
        }
        service.updateSortNum(id, newSortNum);
    }

    /**
     * 保存产品基本信息
     *
     * @param vo
     * @return
     */
    @ApiOperation(value = "保存产品基本信息")
    @PostMapping("/basic")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品基本信息")
    public int saveProductBaseInfo(@RequestBody @Valid BasicProductVo vo) {
        return service.saveProductBaseInfo(vo);
    }

    /**
     * 查询所有产品计划
     *
     * @return
     */
    @ApiOperation(value = "查询所有产品计划列表")
    @GetMapping("/plan")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有产品计划列表")
    public List<SmPlanVO> getProductPlansList() {
        return historyService.getProductPlanList();
    }

    /**
     * 查询所有产品计划
     *
     * @return
     */
    @ApiOperation(value = "查询所有上线的产品计划列表")
    @GetMapping("/plan/online")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有上线的产品计划列表")
    public List<SmPlanVO> getOnlineProductPlansList() {
        return historyService.getOnlineProductPlansList();
    }

    @ApiOperation(value = "查询所有产品计划列表分页",tags = "CORE")
    @GetMapping("/plan/page")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有产品计划列表")
    public PageInfo<SmPlanVO> getProductPlansListPage(SmPlanPageQuery query) {
        return historyService.getProductPlanListPage(query);
    }

    /**
     * 查询产品计划
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品计划")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/plan")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品计划")
    public List<SmPlanVO> getProductPlansByPage(@PathVariable int id, @RequestParam(value = "version", required = false)
    Integer version) {
        return historyService.getProductPlans(id, version);
    }

    /**
     * 查询已发布的产品计划
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询已发布的产品计划")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/plan/release")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询已发布的产品计划")
    public List<SmPlanVO> getReleaseProductPlansByPage(@PathVariable int id) {
        return service.getProductPlans(id);
    }

    /**
     * 保存产品计划
     *
     * @param id
     * @param plans
     */
    @ApiOperation(value = "保存产品计划")
    @PostMapping("/{id}/plan")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品计划")
    public void saveProductPlan(@PathVariable int id, @RequestBody List<SmPlanDTO> plans) {
        service.saveProductPlan(id, plans);
    }

    /**
     * 删除产品计划
     *
     * @param id
     * @param planId
     */
    @ApiOperation(value = "删除产品计划")
    @DeleteMapping("/{id}/plan/{planId}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "删除产品计划")
    public void deleteProductPlan(@PathVariable int id, @PathVariable int planId) {
        historyService.deleteProductPlan(id, planId);
    }

    /**
     * 查询产品常见问题
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品常见问题")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/attentions")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品常见问题")
    public Object getProductAttention(@PathVariable int id,
                                      @RequestParam(value = "version", required = false) Integer version) {
        SmProductDetailVO detailVO = historyService.getProductById(id, version);
        return CommonResult.successResult(detailVO == null ? null : detailVO.getAttentions());
    }

    /**
     * 保存产品常见问题
     *
     * @param dto
     */
    @ApiOperation(value = "保存产品常见问题")
    @PostMapping("/attentions")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品常见问题")
    public void saveProductAttention(@RequestBody SmProductAttentionDTO dto) {
        service.saveProductAttention(dto);
    }

    /**
     * 查询产品保险条款
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品保险条款")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/clause")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品保险条款")
    public SmProductClauseMasterVO getProductClause(@PathVariable int id,
                                                    @RequestParam(value = "version", required = false) Integer version) {

//        service.getProductClause(id);
        return historyService.getProductClause(id, version);
    }

    /**
     * 保存产品保险条款
     *
     * @param id
     * @param clauses
     */
    @ApiOperation(value = "保存产品保险条款")
    @PostMapping("/{id}/clause")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品保险条款")
    public void saveProductClause(@PathVariable int id, @RequestBody SmProductClauseMasterDTO clauses) {
        service.saveProductClause(id, clauses, HttpRequestUtil.getUserId());
    }

    /**
     * 查询产品健康告知
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "S48-查询产品健康告知")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/healthNotification")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品健康告知")
    public SmProductNotificationDTO getProductHealthNotification(@PathVariable int id,
                                                                 @RequestParam(value = "version", required = false) Integer version) {
        SmProductDetailVO detailVO = historyService.getProductById(id, version);
        SmProductNotificationDTO dto = new SmProductNotificationDTO();
        dto.setProductId(detailVO.getId());
        dto.setAiCheck(detailVO.getAiCheck());
        dto.setHealthNotification(detailVO.getHealthNotification());
        dto.setAiCheckWay(detailVO.getAiCheckWay());
        version = historyService.defaultIfAbsent(detailVO.getId(), version);
        if (Objects.equals(detailVO.getAiCheckWay(), AiCheckWayEnum.QUESTION.getCode())) {
            AkProductQuestionnaireHis questionnaire = akProductQuestionnaireHisService.getAkProductQuestionaireHisPOByProductId(detailVO.getId(), version);
            if (questionnaire != null) {
                dto.setFileName(questionnaire.getFileName());
                dto.setFileUrl(questionnaire.getFileUrl());
            }
            List<QuestionDTO> questions = akProductQuestionnaireService.queryQuestions(id, version);
            dto.setQuestions(questions);
        }
        return dto;
//        return CommonResult.successResult(detailVO == null ? null : detailVO.getHealthNotification());
    }

    /**
     * 保存产品健康告知
     *
     * @param dto
     */
    @ApiOperation(value = "s48-保存产品健康告知")
    @PostMapping("/healthNotification")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品健康告知")
    public void saveProductHealthNotification(@RequestBody SmProductNotificationDTO dto) throws Exception {
        service.saveProductHealthNotification(dto);
    }

    /**
     * 查询团险产品介绍
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询团险产品介绍")
    @GetMapping("/{id}/introduce")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询团险产品介绍")
    public Object getProductIntroduce(@PathVariable int id,
                                      @RequestParam(value = "version", required = false) Integer version) {
        SmProductDetailVO detailVO = historyService.getProductById(id, version);
        return CommonResult.successResult(detailVO == null ? null : detailVO.getGlProductIntroduce());
    }

    /**
     * 保存团险产品介绍
     *
     * @param id
     * @param dto
     */
    @ApiOperation(value = "保存团险产品介绍")
    @PostMapping("/{id}/introduce")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品保险条款")
    public void saveProductIntroduce(@PathVariable int id, @RequestBody SmProductDTO dto) {
        dto.setProductId(id);
        service.saveProductIntroduce(dto);
    }

    /**
     * 查询团险产品投保须知
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询团险产品投保须知")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/notice")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询团险产品投保须知")
    public Object getProductNotice(@PathVariable int id,
                                   @RequestParam(value = "version", required = false) Integer version) {
        SmProductDetailVO detailVO = historyService.getProductById(id, version);
        return CommonResult.successResult(detailVO == null ? null : detailVO.getGlProductNotice());
    }

    /**
     * 保存团险产品投保须知
     *
     * @param id
     * @param dto
     */
    @ApiOperation(value = "保存团险产品投保须知")
    @PostMapping("/{id}/notice")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存团险产品投保须知")
    public void saveProductNotice(@PathVariable int id, @RequestBody SmProductDTO dto) {
        dto.setProductId(id);
        service.saveProductNotice(dto);
    }

    /**
     * 查询价格因素IdName对应关系
     *
     * @return
     */
    @ApiOperation(value = "查询价格因素IdName对应关系")
    @GetMapping("/price/factors")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询价格因素IdName对应关系")
    public Map<String, String> getPriceFactors() {
        List<DictionaryVO> vos = dService.getDictionarysByPage(DictionaryVO.TYPE_PRICE_FACTOR, null, null, true).getList();
        Map<String, String> treeMap = new LinkedHashMap<>();
        vos.forEach(v -> treeMap.put(v.getCode(), v.getName()));
        return treeMap;
    }

    /**
     * 查询产品价格因素字段code
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品价格因素字段code")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/price/factor/select")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品价格因素字段code")
    public List<String> getProductPriceFactor(@PathVariable int id,
                                              @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getProductPriceFactor(id, version);
    }

    /**
     * 保存产品价格因素code
     *
     * @param id
     * @param selects
     */
    @ApiOperation(value = "保存产品价格因素code")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{id}/price/factor/select")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品价格因素code")
    public void saveProductPriceFactor(@PathVariable int id, @RequestBody List<String> selects) {
        service.saveProductPriceFactor(id, selects);
    }

    /**
     * 查询产品保险价格选项
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品保险价格选项")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/price/factor/optionals")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品保险价格选项")
    public SmPriceFactorOptionalsVO getProductPriceFactorOptions(@PathVariable int id,
                                                                 @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getProductPriceFactorOptionals(id, version);
    }

    /**
     * 保存产品价格因素可选值
     *
     * @param optionals
     */
    @ApiOperation(value = "保存产品价格因素可选值")
    @PostMapping("/price/factor/optionals")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品价格因素可选值")
    public void saveProductFactorOptionals(@RequestBody List<SmFactorOptionalDTO> optionals) {
        service.saveProductFactorOptionals(optionals);
    }

    /**
     * 保存产品计划价格费率表
     *
     * @param prices
     */
    @ApiOperation(value = "保存产品计划价格费率表")
    @PostMapping("/plan/factor/price")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品计划价格费率表")
    public void savePlanFactorPrice(@RequestBody List<SmOptionalFieldPriceDTO> prices) {
        service.savePlanFactorPrice(prices);
    }

    /**
     * 查询产品计划价格费率表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品计划价格费率表")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/plan/price/factor")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品计划价格费率表")
    public List<SmPlanFactorPriceDT> getPlanFactorPrice(@PathVariable int id,
                                                        @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getPlanFactorPrice(id, version);
    }

    /**
     * 查询产品投保信息录入列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询产品投保信息录入列表")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/insureForm")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品投保信息录入列表")
    public Collection<SmProductFormFieldCombDTO> getProductFormFields(@PathVariable int id,
                                                                      @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getProductFormFields(id, version);
    }

    /**
     * 保存产品投保信息录入列表
     *
     * @param fields
     */
    @ApiOperation(value = "保存产品投保信息录入列表")
    @PostMapping("/insureForm")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品投保信息录入列表")
    public void saveProductInsureForm(@RequestBody List<SmProductFormFieldCombDTO> fields) {
        service.saveProductInsureForm(fields);
    }

    /**
     * 查询单个产品销售区域列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询单个产品销售区域列表")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/sales/org")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询单个产品销售区域列表")
    public Collection<SmProductSalesOrgVO> getProductSalesOrgsByProductId(@PathVariable int id) {
        return service.getProductSalesOrgsByProductId(id);
    }

    /**
     * 查询所有产品销售区域列表
     *
     * @return
     */
    @ApiOperation(value = "查询所有产品销售区域列表")
    @GetMapping("/sales/org")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有产品销售区域列表")
    public Collection<SmProductSalesOrgVO> getOnlineProductSalesOrgs() {
        return service.getOnlineProductSalesOrgs();
    }

    /**
     * 保存产品销售区域列表
     *
     * @param psos
     */
    @ApiOperation(value = "保存产品销售区域列表")
    @PostMapping("/sales/org")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品销售区域列表")
    public void saveProductSalesOrg(@RequestBody List<SmProductSalesOrgDTO> psos) {
        service.saveProductSalesOrgs(psos);
    }

    /**
     * 保存产品销售区域列表
     *
     * @param psos
     */
    @ApiOperation(value = "保存产品计划销售区域列表")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{id}/plan/sales/org")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "保存产品计划销售区域列表")
    public void savePlanSalesOrg(@PathVariable int id,
                                 @RequestBody @Valid List<SmPlanSalesOrgDTO> psos) {
        service.savePlanSalesOrgs(id, psos);
    }

    /**
     * 查询单个产品销售区域列表
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "查询单个产品计划销售区域列表")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{id}/plan/sales/org")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询单个产品计划销售区域列表")
    public Collection<SmPlanSalesOrgDTO> getPlanSaleOrgByProductId(@PathVariable int id) {
        return service.getPlanSaleOrgByProductId(id);
    }

    /**
     * 查询所有产品上线平台
     *
     * @return
     */
    @ApiOperation(value = "查询所有产品上线平台")
    @GetMapping("/online/platform")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有产品上线平台")
    public Collection<IdName> getProductOnlinePlatform() {
        return Arrays.asList(new IdName(EnumOrderSubChannel.XIANGZHU.getCode(),
                        EnumOrderSubChannel.XIANGZHU.getDesc()),
                new IdName(EnumOrderSubChannel.CAPP.getCode(), EnumOrderSubChannel.CAPP.getDesc()));
    }

    /**
     * 查询所有产品上线平台
     *
     * @param productId
     */
    @ApiOperation(value = "查询所有产品上线平台")
    @GetMapping("/{productId}/online/platform")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有产品上线平台")
    public List<String> getProductOnlinePlatform(@PathVariable int productId) {
        return service.getProductOnlinePlatform(productId);
    }

    /**
     * 保存所有产品上线平台
     *
     * @param productId
     * @param onlineChannels
     */
    @ApiOperation(value = "保存所有产品上线平台")
    @PostMapping("/{productId}/online/platform")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存所有产品上线平台")
    public void saveProductOnlinePlatform(@PathVariable int productId, @RequestBody List<String> onlineChannels) {
        service.saveProductOnlinePlatform(productId, String.join(",", onlineChannels));
    }

    /**
     * 修改保险产品状态
     *
     * @param id
     * @param status
     */
    @ApiOperation(value = "修改保险产品状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "status", value = "状态（1=上线;2=下线）", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "fixedTime", value = "定时上下线时间[yyyy-MM-dd HH:mi:ss]，为空时会立即执行上下线", required = false, dataType = "String")
    })
    @PutMapping("/{id}/change/{status}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "修改保险产品状态")
    public void updateProductStatus(@PathVariable int id,
                                    @PathVariable int status, @RequestBody Map<String, String> paramMap) {
        String operator = HttpRequestUtil.getUserId();
        String fixedTime = paramMap.get("fixedTime");
        service.onoff(id, status, fixedTime, operator);
    }

    /**
     * 修改保险产品状态
     *
     * @param data
     */
    @ApiOperation(value = "取消定时上线线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long"),
            @ApiImplicitParam(name = "status", value = "状态：1=上线;2=下线", required = true, dataType = "long"),
    })
    @PutMapping("/cancel/onoff")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "修改保险产品状态")
    public void cancelOnoff(@RequestBody CancelConfigDTO data) {
        int id = data.getId();
        int status = data.getStatus();
        service.cancelOnoff(id, status);
    }

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "产品图片文件上传")
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "产品图片文件上传")
    public Object upload(@RequestParam("file") MultipartFile file) {
        return CommonResult.successResult(SpringFactoryUtil.getBean(SystemFileService.class).uploadFile(file));
    }

    /**
     * 查询产品渠道
     *
     * @return
     */
    @ApiOperation(value = "查询产品渠道")
    @GetMapping("/channel")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品渠道")
    public List<DictionaryVO> getProductChannels() {
        return dService.getDictionarysByPage(DictionaryVO.TYPE_CHANNEL, null, null, true).getList();
    }

    /**
     * 查询产品类别
     *
     * @return
     */
    @ApiOperation(value = "查询产品类别")
    @GetMapping("/attr")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品渠道")
    public List<DictionaryVO> getProductAttrs() {
        return dService.getDictionarysByPage(DictionaryVO.TYPE_PRODUCT_ATTR, null, null, true).getList();
    }

    /**
     * 查询所有产品续保列表
     *
     * @return
     */
    @ApiOperation(value = "查询所有产品续保列表")
    @GetMapping("/renew")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询所有产品续保列表")
    public PageInfo<SmProductRenewVO> getProductRenews(@RequestParam(required = false) String productName, Pageable pageable) {
        return service.getProductRenews(productName, pageable);
    }

    /**
     * 查询单个产品续保列表
     *
     * @return
     */
    @ApiOperation(value = "查询单个产品续保列表")
    @GetMapping("/{id}/renew")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询单个产品续保列表")
    public List<SmProductRenewVO> getProductRenewsByProductId(@PathVariable int id) {
        return service.getProductRenewsByProductId(id);
    }

    /**
     * 保存产品续保列表
     *
     * @param id
     * @param renews
     */
    @ApiOperation(value = "保存产品续保列表")
    @PostMapping("/{id}/renew")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品销售区域列表")
    public void saveProductRenews(@PathVariable int id, @RequestBody List<SmProductRenewDTO> renews) {
        service.saveProductRenews(id, renews);
    }

    /**
     * 查询责任类型
     *
     * @return
     */
    @ApiOperation(value = "查询责任类型")
    @GetMapping("/coverage/cvgresptype")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询责任类型")
    public List<Map<String, String>> getProductCvgRespType() {
        return CvgRespTypeEnum.getAllMap();
    }

    /**
     * 查询产品保障项目
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询产品保障项目")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品保障项目")
    public List<SmProductCoverageVO> getProductCoverageList(@PathVariable int productId,
                                                            @RequestParam(value = "version", required = false)
                                                            Integer version) {
        return historyService.getProductCoverageList(productId, version);
    }

    /**
     * 查询产品保障项目保费
     * 该接口暂时未使用
     *
     * @param productId
     * @return
     * <AUTHOR>
     */
    @Deprecated
    @ApiOperation(value = "S48-查询产品计划责任表")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/duty/table")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品计划责任表")
    public List<DutyTableVO> getPlanCoverageTable(@PathVariable int productId,
                                                  @RequestParam(value = "version", required = false)
                                                  Integer version) {
        return historyService.getPlanDutyTable(productId, version);
    }

    /**
     * 保存产品保障项目
     *
     * @param productId
     * @param dtos
     */
    @ApiOperation(value = "s48-保存产品保障项目")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/coverage")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品保障项目")
    public void saveProductCoverage(@PathVariable int productId, @RequestBody List<SmProductCoverageDTO> dtos) {
        dtos.forEach(dto -> {
            CvgRespTypeEnum typeEnum = CvgRespTypeEnum.getByCode(dto.getCvgRespType());
            if (typeEnum == null) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "责任类型参数错误");
            }
        });
        service.saveProductCoverage(productId, dtos);
    }

    /**
     * 删除产品保障项目
     *
     * @param productId
     * @param spcId
     */
    @ApiOperation(value = "删除产品保障项目")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "spcId", value = "保障项目主键Id", required = true, dataType = "long", paramType = "path")
    })
    @DeleteMapping("/{productId}/coverage/{spcId}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "删除产品保障项目")
    public void deleteProductCoverage(@PathVariable int productId, @PathVariable int spcId) {
        service.deleteProductCoverage(productId, spcId);
    }

    /**
     * 查询产品保障项目保额
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询产品计划保额表")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage/amount")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品计划保额表")
    public List<SmProductCoverageAmountItemVO> getProductCoverageAmountList(@PathVariable int productId,
                                                                            @RequestParam(value = "version", required = false)
                                                                            Integer version) {
        return historyService.getProductCoverageAmountList(productId, version);
    }

    /**
     * 暫時未使用
     *
     * @param productId
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "S48-查询产品计划保额列表")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage/table")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品计划费率表")
    public List<CoverageTableVO> getProductPremiumTable(@PathVariable int productId,
                                                        @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getCoverageTable(productId, version);
    }

    /**
     * 保存产品保障项目保额
     *
     * @param productId
     * @param dtos
     */
    @ApiOperation(value = "保存产品保障项目保额")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/coverage/amount")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品保障项目保额")
    public void saveProductCoverageAmount(@PathVariable int productId, @RequestBody List<SmProductCoverageAmountItemDTO> dtos) {
        service.saveProductCoverageAmount(productId, dtos);
    }

    /**
     * 删除产品保障项目保额
     *
     * @param productId
     * @param spcaId
     */
    @ApiOperation(value = "删除产品保障项目保额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "spcaId", value = "保障项目保额主键Id", required = true, dataType = "long", paramType = "path")
    })
    @DeleteMapping("/{productId}/coverage/amount/{spcaId}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DELETE, descrption = "删除产品保障项目保额")
    public void deleteProductCoverageAmount(@PathVariable int productId, @PathVariable int spcaId) {
        service.deleteProductCoverageAmount(productId, spcaId);
    }

    /**
     * 查询产品保障项目保费
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询产品保障项目保费")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage/premium")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品保障项目保费")
    public List<SmProductCoveragePremiumItemVO> getProductCoveragePremiumList(@PathVariable int productId,
                                                                              @RequestParam(value = "version", required = false)
                                                                              Integer version) {
        return historyService.getProductCoveragePremiumList(productId, version);
    }

    /**
     * 查询产品保障项目保费
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "S48-查询产品计划-险责费率因子表")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/premium/table")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询产品计划-险责费率因子表")
    public PremiumPage getPremiumTable(@PathVariable int productId,
                                       @RequestParam(value = "version", required = false)
                                       Integer version) {
        return historyService.getPremiumPage(productId, version);
    }

    /**
     * 保存产品保障项目保费
     *
     * @param productId
     * @param table
     */
    @ApiOperation(value = "S48-保存费率表")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/premium/table")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存费率表")
    public void savePremiumTable(@PathVariable int productId, @RequestBody SmProductCoveragePremiumTable table) {
        service.savePremiumTable(productId, table);
    }

    /**
     * 品保障项目保费模板下载
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "产品保障项目保费模板下载")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage/premium/template")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DOWNLOAD, descrption = "品保障项目保费模板下载")
    public void downloadProductCoveragePremiumTemplate(@PathVariable int productId,
                                                       @RequestParam(value = "version", required = false) Integer version, HttpServletResponse resp) {

        historyService.downloadPremiumTableTemplate(productId, version, resp);
    }

    /**
     * 保障项目保费模板下载
     *
     * @param productId
     * @return
     */
//    @Deprecated
//    @ApiOperation(value = "S48-品保障项目保费模板下载")
//    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
//    @GetMapping("/{productId}/premium/table/template")
//    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DOWNLOAD, descrption = "品保障项目保费模板下载")
//    public void downloadPremiumTableTemplate(@PathVariable int productId,
//                                                       @RequestParam(value = "version", required = false) Integer version, HttpServletResponse resp) {
//        historyService.downloadPremiumTableTemplate(productId, version, resp);
//    }
    @ApiOperation(value = "S48-配置产品属性")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/premium/file")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "S48-配置产品属性")
    public void savePremiumFile(@PathVariable int productId, @RequestBody SmProductAttrDTO vo) {
        String attrCode = vo.getAttrCode();
        if (StringUtils.isNotBlank(attrCode) && !ProductAttrEnum.PREMIUM_FILE.getCode().equals(attrCode)) {
            throw new MSBizNormalException("-1", String.format("非法的产品属性值:%s", attrCode));
        }
        if (vo.getId() == null) {
            vo.setAttrCode(ProductAttrEnum.PREMIUM_FILE.getCode());
        }
        historyService.saveProductAttr(productId, vo);
    }

    /**
     * 导入品保障项目保费
     *
     * @param productId
     * @param file
     * @return
     */
    @ApiOperation(value = "导入品保障项目保费")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/coverage/premium/import")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PARAM, actionType = LogActionType.ADD, descrption = "导入品保障项目保费")
    public int importProductCoveragePremium(@PathVariable int productId, @RequestParam("file") MultipartFile file) {
        return service.importPremiumTable(productId, file);
    }

    /**
     * 导入品保障项目保费
     *
     * @param productId
     * @param file
     * @return
     */
    @Deprecated
    @ApiOperation(value = "S48-导入品保障项目保费表")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/import/premium/table")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PARAM, actionType = LogActionType.ADD, descrption = "导入品保障项目保费表")
    public int importPremiumTable(@PathVariable int productId, @RequestParam("file") MultipartFile file) {
        return service.importPremiumTable(productId, file);
    }

    /**
     * 生成产品保障项目保费
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "生成产品保障项目保费")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage/premium/generate")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "生成产品保障项目保费")
    public void generateProductCoveragePremium(@PathVariable int productId) {
        service.generateProductCoveragePremium(productId);
    }

    /**
     * 保存产品保障项目保费
     *
     * @param productId
     * @param dtos
     */
    @ApiOperation(value = "保存产品保障项目保费")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/coverage/premium")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品保障项目保费")
    public void saveProductCoveragePremium(@PathVariable int productId, @RequestBody List<SmProductCoveragePremiumItemDTO> dtos) {
        service.saveProductCoveragePremium(productId, dtos);
    }

    /**
     * 查询团险产品保费折扣比例
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询团险产品保费折扣比例")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/coverage/discount")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询团险产品保费折扣比例")
    public List<SmProductCoverageDiscountVO> getProductCoverageDiscountList(@PathVariable int productId,
                                                                            @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getProductCoverageDiscountList(productId, version);
    }

    @ApiOperation(value = "S48-查询责任（因子）列表")
    @ApiImplicitParam(name = "type", value = "0=责任列表,1=责任因子", required = true, dataType = "int", paramType = "path")
    @GetMapping("/{productId}/duty/list")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询责任字典信息")
    public List<SysDutyConfigDTO> listDuty(@RequestParam Integer type,
                                           @RequestParam(value = "version", required = false) Integer version,
                                           @PathVariable("productId") Integer productId) {
        return historyService.listDuty(productId, type, version);
    }

    /**
     * 查询团险产品保费折扣比例
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "S48-查询团险产品保费折扣比例")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/premium/flow")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询团险产品保费折扣比例")
    public PremiumFlowFactorVo getPremiumFactor(@PathVariable int productId,
                                                @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getPremiumFactor(productId, version);
    }

    /**
     * 保存团险产品保费折扣比例
     *
     * @param productId
     * @param vo
     */
    @ApiOperation(value = "S48-保存团险产品保费折扣比例")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/premium/flow")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存团险产品保费折扣比例")
    public void savePremiumFactor(@PathVariable int productId, @RequestBody PremiumFlowFactorReqVo vo) {
        service.savePremiumFactor(productId, vo);
    }

    /**
     * 保存团险产品保费折扣比例
     *
     * @param productId
     * @param dtos
     */
    @ApiOperation(value = "保存团险产品保费折扣比例")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/coverage/discount")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存团险产品保费折扣比例")
    public void saveProductCoverageDiscount(@PathVariable int productId, @RequestBody List<SmProductCoverageDiscountDTO> dtos) {
        service.saveProductCoverageDiscount(productId, dtos);
    }

    /**
     * 删除团险产品保费折扣比例
     *
     * @param productId
     * @param spcdId
     */
    @ApiOperation(value = "删除团险产品保费折扣比例")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "spcdId", value = "折扣Id", required = true, dataType = "long", paramType = "path")
    })
    @DeleteMapping("/{productId}/coverage/discount/{spcdId}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DELETE, descrption = "删除团险产品保费折扣比例")
    public void deleteProductCoverageDiscount(@PathVariable int productId, @PathVariable int spcdId) {
        service.deleteProductCoverageDiscount(productId, spcdId);
    }

    /**
     * 查询团险产品报价限制
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询团险产品报价限制")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/quote/limit")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询团险产品报价限制")
    public SmProductQuoteLimitVO getProductQuoteLimit(@PathVariable int productId,
                                                      @RequestParam(value = "version", required = false) Integer version) {

        return historyService.getProductQuoteLimit(productId, version);
    }

    /**
     * 保存团险产品报价限制
     *
     * @param productId
     * @param dto
     */
    @ApiOperation(value = "保存团险产品报价限制")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/quote/limit")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存团险产品保费折扣比例")
    public void saveProductQuoteLimit(@PathVariable int productId, @RequestBody SmProductQuoteLimitDTO dto) {
        service.saveProductQuoteLimit(productId, dto);
    }

    /**
     * 保存团险产品报价限制
     *
     * @param productId
     * @param spqlId
     */
    @ApiOperation(value = "保存团险产品报价限制")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "spqlId", value = "报价限制Id", required = true, dataType = "long", paramType = "path")
    })
    @DeleteMapping("/{productId}/quote/limit/{spqlId}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存团险产品保费折扣比例")
    public void deleteProductQuoteLimit(@PathVariable int productId, @PathVariable int spqlId,
                                        @RequestParam(value = "version", required = false) Integer version) {
        service.deleteProductQuoteLimit(productId, spqlId);
    }

    /**
     * 查询团险报价记录列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询团险报价记录列表")
    @PostMapping("/glp/quote/list")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询订单批改记录列表")
    public PageInfo<SmGlProductQuoteListVO> getGlProductQuotes(@RequestBody SmGlProductQuoteQuery query) {
        return service.getGlProductQuotes(query);
    }

    /**
     * 下载团险报价记录列表
     *
     * @param query
     * @param response
     */
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "创建时间开始", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "创建时间结束", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "quoteNo", value = "报价单号", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "customerName", value = "客户姓名", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "organizationName", value = "机构", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "userName", value = "客户经理", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "agentName", value = "代理人", dataType = "string", paramType = "query"),
    })
    @ApiOperation(value = "下载团险报价记录列表")
    @GetMapping("/glp/quote/list/download")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DOWNLOAD, descrption = "下载团险报价记录列表")
    public void downloadGlProductQuotes(SmGlProductQuoteQuery query, HttpServletResponse response) {
        service.downloadSmEmployeeSalesSmy(query, response);
    }


    @ApiOperation(value = "保存客户告知书配置")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/cust/notify")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存客户告知书配置")
    public void saveProductCustNotify(@PathVariable int productId, @RequestBody SmProductNotify dto) {
        service.saveProductCustNotify(productId, dto);
    }


    @ApiOperation(value = "获取客户告知书配置")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/cust/notify")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "获取客户告知书配置")
    public SmProductNotify getProductCustNotify(@PathVariable int productId,
                                                @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getProductCustNotify(productId, version);
    }

    @ApiOperation(value = "获取产品版本记录")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/versions")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "获取产品版本记录")
    public List<SmProductVersionVO> productVersionVOList(@PathVariable int productId) {

        return service.listVersionsByProduct(productId);
    }

    @ApiOperation(value = "发布新的产品版本")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/versions")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "发布新的产品版本")
    public void pushNewVersion(@PathVariable int productId) {
        service.pushNewVersion(productId, HttpRequestUtil.getUserId());
    }


    @ApiOperation(value = "保存产品条款-自主确认")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/clause/confirm")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品条款-自主确认")
    public void saveClauseConfirm(@PathVariable int productId,
                                  @RequestBody List<SmProductConfirm> confirms) {
        service.saveClauseConfirm(productId, confirms);
    }

    @ApiOperation(value = "查询产品条款-自主确认")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/clause/confirm")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "保存产品条款-自主确认")
    public List<SmProductConfirmHistory> getClauseConfirm(@PathVariable int productId,
                                                          @RequestParam(value = "version", required = false) Integer version) {
        return historyService.getClauseConfirm(productId, version);
    }

    @ApiOperation(value = "团险批改")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @PostMapping("/{productId}/endor")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "批改配置")
    public Integer endorConfig(@RequestBody ProductEndorVo vo, @PathVariable("productId") Integer productId) {
        vo.setProductId(productId);
        return service.saveEndorConfig(vo);
    }

    @ApiOperation(value = "团险批改")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/{productId}/endor")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "批改配置")
    public ProductEndorVo getEndorConfig(@PathVariable int productId) {
        return service.getEndorConfig(productId);
    }

//    @ApiOperation(value = "设置产品业务标签")
//    @PostMapping("/saveProductLabel")
//    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "设置产品业务标签")
//    public void saveProductLabel(@RequestBody ProductLabelDTO productLabelDTO) {
//        service.saveProductLabel(productLabelDTO);
//    }
//
//    @ApiOperation(value = "获取产品业务标签")
//    @GetMapping("/getProductLabels")
//    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "获取产品业务标签")
//    public ProductLabelDTO getProductLabels(@RequestParam("productId") Integer productId) {
//        return productQueryService.getProductLabels(productId);

//    }


    @ApiOperation("删除产品")
    @DeleteMapping("/{productId}")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.DELETE, descrption = "删除产品")
    public boolean deleteProduct(@PathVariable int productId) {
        return service.deleteProduct(productId, HttpRequestUtil.getUserId());
    }

    @ApiOperation("复制产品")
    @PostMapping("/copy")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.ADD, descrption = "复制产品")
    public boolean copyProduct(@RequestParam("productId") Integer productId) {
        return service.copyProduct(productId, HttpRequestUtil.getUserId());
    }

    @ApiOperation(value = "获取产品属性(从回溯表中查询)")
    @GetMapping("/attr/list")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "获取产品业务标签")
    public List<ProductAttrVo> listProductAttr(@RequestParam("productId") Integer productId,
                                               @RequestParam(value = "attrCode", required = false) String attrCode,
                                               @RequestParam(value = "version",required = false)Integer version) {
        return historyService.listProductAttr(productId, attrCode,version);
    }

    @ApiOperation(value = "配置产品属性")
    @PostMapping("/attr")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "配置产品属性")
    public int configAttr(@RequestBody SmProductAttrDTO attr) {
        Integer productId = attr.getProductId();
        if (productId == null) {
            throw new MSBizNormalException("-1", "产品Id不能为空");
        }
        return historyService.saveProductAttr(productId,attr);
    }

    @ApiOperation(value = "查询管理后端产品理赔工单配置信息")
    @GetMapping("/claim/info")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询管理后端产品列表")
    public SmProductListVO getBackendProductByPage(@RequestParam("productId") Integer productId) {
        return service.getProductClaimInfo(productId);
    }

    @ApiOperation(value = "更新管理后端产品理赔工单配置信息")
    @PostMapping("/claim/info")
    @SystemLog(module = LogConstants.MODULE_SM_CONFIG_PRODUCT, actionType = LogActionType.UPDATE, descrption = "更新管理后端产品理赔工单配置信息")
    public void saveBackendProductByPage(@RequestBody BasicProductVo productVo) {
        service.updateProductClaimInfo(productVo);
    }
}
