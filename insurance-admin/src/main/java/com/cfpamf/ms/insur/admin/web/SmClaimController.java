package com.cfpamf.ms.insur.admin.web;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.admin.claim.entity.ClaimImportHistoryQuery;
import com.cfpamf.ms.insur.admin.claim.entity.SmClaimImportResult;
import com.cfpamf.ms.insur.admin.config.ClaimEmailProperties;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementZaMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimFollowupType;
import com.cfpamf.ms.insur.admin.event.ClaimProcessNodeChangeEvent;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.event.handler.ClaimProcessNodeChangeHandler;
import com.cfpamf.ms.insur.admin.event.handler.WxPushMessageHandler;
import com.cfpamf.ms.insur.admin.external.client.InsuranceImageClient;
import com.cfpamf.ms.insur.admin.external.client.insurance.image.HtmlToImageReq;
import com.cfpamf.ms.insur.admin.external.client.insurance.image.HtmlToImageRes;
import com.cfpamf.ms.insur.admin.job.claim.ClaimRuleJobHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.ClaimProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileUnitDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFollowUpDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.claim.AiBusinessParams;
import com.cfpamf.ms.insur.admin.pojo.dto.claim.AiFileResponseVO;
import com.cfpamf.ms.insur.admin.pojo.form.claim.ChannelEmail;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimEmail;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimLabel;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimPosterInfo;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimApplyQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimSmyQuery;
import com.cfpamf.ms.insur.admin.pojo.query.claim.SmClaimEvaluationQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimEvaluationExcelVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimEvaluationPageVo;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.claim.SmClaimPosterApplyService;
import com.cfpamf.ms.insur.admin.service.claim.SmClaimPosterInfoService;
import com.cfpamf.ms.insur.admin.service.claim.base.SmClaimProgressTimelinessService;
import com.cfpamf.ms.insur.admin.service.claim.im.ImportClaimProgressServiceImpl;
import com.cfpamf.ms.insur.admin.service.claim.impl.ClaimProcessDistinctServiceImpl;
import com.cfpamf.ms.insur.admin.service.claim.za.kaiping.ZaKaiPingClaimProgressServiceImpl;
import com.cfpamf.ms.insur.admin.util.AssertUtills;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.bean.CommonResult;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.config.ClaimConfig;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.base.service.AIDifyService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveDefault;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.StepFinishNotifyDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.StepFinishReportVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimNotifyVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.evaluation.ClaimEvaluationVo;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 理赔接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Api(value = "保险理赔管理接口", tags = {"保险理赔管理接口"})
@RequestMapping(value = {BaseConstants.ADMIN_VERSION + "/claim"})
@RestController
public class SmClaimController extends AbstractController {

    private final String CLAIM_MANAGER = "ZHNX34439";
    /**
     * 订单理赔service
     */
    @Autowired
    private SmClaimServiceImpl service;

    @Autowired
    private ClaimEmailProperties claimEmailProperties;

    @Autowired
    private ClaimServiceConfig claimServiceConfig;

    @Autowired
    private ZaClaimServiceImpl zaClaimService;

    @Autowired
    private InsuranceImageClient insuranceImageClient;

    @Autowired
    private SmClaimPosterInfoService smClaimPosterInfoService;

    @Resource
    private SmClaimPosterApplyService smClaimPosterApplyService;

    @Autowired
    ZaKaiPingClaimProgressServiceImpl kaiPingClaimProgressService;

    /**
     * bms配置
     */
    @Autowired
    private BmsConfig bmsConfig;

    @Autowired
    private SmClaimProgressTimelinessService claimProgressTimelinessService;

    @Autowired
    private ImportClaimProgressServiceImpl importClaimProgressService;

    @Autowired
    private UserService userService;

    @Autowired
    private ClaimProcessDistinctServiceImpl distinctService;

    /**
     * 查询订单理赔列表
     *
     * @return
     */
    @ApiOperation(value = "查询订单理赔列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "reportDateStart", value = "报案时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "reportDateEnd", value = "报案时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "riskDateStart", value = "出险时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "riskDateEnd", value = "出险时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "finishDateStart", value = "结案时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "finishDateEnd", value = "结案时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "claimNo", value = "编号", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "custManager", value = "客户经理", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "policyNo", value = "保单号", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "insuredPerson", value = "被保人", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "riskType", value = "出险类型", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "claimState", value = "结案结果", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "otherReimbursement", value = "其他报销途径", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "reimbursementValue", value = "报销途径码值", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "settlement", value = "理赔专员", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "labelCode", value = "规则标签编码", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "labelValueCode", value = "规则标签编码值", dataType = "string", paramType = "query")
    })
    @GetMapping("")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询订单理赔列表")
    public PageInfo<SmClaimVO> getSmClaimByPage(SmClaimQuery query) {
        PageInfo<SmClaimVO> result = service.getSmClaimByPage(query);
        // 判断登录用户权限对客户敏感数据进行脱敏
        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);
        permissionUtil.maskCustomerSensitiveFields(result.getList(), bmsConfig.getCustomerSensitiveClaimList());
        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(result.getList(), SmClaimVO.class, FieldRemoveDefault.class);

        return result;

    }

    /**
     * 查询订单理赔详情
     *
     * @param claimId
     * @return
     */
    @GetMapping("/{claimId}")
    @ApiOperation(value = "查询订单理赔详情")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询订单理赔详情")
    public SmClaimVO getSmClaimByClaimId(@PathVariable int claimId) {
        return service.getSmClaimByClaimId(claimId);
    }

    /**
     * 查询订单理赔详理赔专员列表
     *
     * @return
     */
    @GetMapping("/settlement")
    @ApiOperation(value = "查询订单理赔详理赔专员列表")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询订单理赔详理赔专员列表")
    public List<ClaimConfig.User> getClaimSettlement() {
        return userService.getSettlements();
//        return SpringFactoryUtil.getBean(ClaimConfig.class).getSettlements();
    }

    /**
     * 查询订单理赔文件详情
     *
     * @param claimId
     * @return
     */
    @GetMapping("/{claimId}/file")
    @ApiOperation(value = "查询订单理赔文件详情")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询订单理赔文件详情")
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimId(@PathVariable int claimId) {
        return claimServiceConfig.findClaimByClaimId(claimId).getSmClaimFileCombByClaimId(claimId);
    }


    @GetMapping("/{claimId}/file/flag")
    @ApiOperation(value = "查询历史理赔文件详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimId", value = "理赔Id", dataType = "long", paramType = "path")
            ,@ApiImplicitParam(name = "newFlag", value = "1:新标签，0:历史标签", dataType = "long", paramType = "query")
    })
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询订单理赔文件详情")
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimId(@PathVariable int claimId, int newFlag) {

        SmClaim claim = distinctService.getByClaimId(claimId);
        List<SmClaimFileCombVO> fileCombVOList = claimServiceConfig
                .findClaimByClaimId(claimId)
                .getSmClaimFileCombByClaimId(claimId);

        ClaimServiceConfig.distintByNewFlag(newFlag, fileCombVOList, claim.getClaimState());

        return fileCombVOList;

    }


    /**
     * 查询订单理赔详情快递信息
     *
     * @param claimId
     * @return
     */
    @GetMapping("/{claimId}/express")
    @ApiOperation(value = "查询订单理赔详情快递信息")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询订单理赔详情快递信息")
    public List<SmClaimExpressVO> getClaimExpressByClaimId(@PathVariable int claimId) {
        return service.getClaimExpressByClaimId(claimId);
    }

    /**
     * 订单理赔列表下载
     *
     * @param query
     * @param resp
     */
    @ApiOperation(value = "订单理赔列表下载")
    @GetMapping("/download")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.DOWNLOAD, descrption = "订单理赔列表下载")
    @AuthValidate(value = ApiPermsEnum.SM_CLAIM_DOWNLOAD)
    public void downloadClaims(SmClaimQuery query, HttpServletResponse resp) {
        if ("stepCancel".equals(query.getClaimState())) {
            service.downloadCancelClaimList(query, resp);
        } else {
            service.downloadClaims(query, resp);
        }
    }

    /**
     * 查询理赔历史流程和下一步流程
     *
     * @return
     */
    @ApiOperation(value = "查询理赔历史流程和下一步流程")
    @ApiImplicitParam(name = "claimId", value = "理赔Id", dataType = "string", paramType = "path")
    @GetMapping("/{claimId}/progress")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询理赔历史流程和下一步流程")
    public SmClaimProgressVO getSmClaimProgress(@PathVariable int claimId) {
        return service.getClaimProgressForBackend(claimId);
    }

    /**
     * 操作理赔流程
     *
     * @return
     */
    @ApiOperation(value = "操作理赔流程", tags = "CORE")
    @PostMapping("/{claimId}/progress")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "操作理赔流程")
    public void saveProgress(@PathVariable int claimId, @RequestBody ProgressDTO dto) {
        dto.setClaimId(claimId);
        dto.setCreateBy(HttpRequestUtil.getUserName());
        claimServiceConfig.findClaimByClaimId(claimId).saveProgress(dto);
    }

    /**
     * 获取理赔邮件模板
     *
     * @param claimId
     * @return
     */
    @ApiOperation(value = "获取理赔邮件模板")
    @GetMapping("/{claimId}/email_template")
    public ClaimEmailTemplateVo getClaimEmailTemplateVo(@PathVariable int claimId) {
        return service.getClaimEmailTemplateVo(claimId);
    }

    /**
     * 操作理赔流程
     *
     * @return
     */
    @ApiOperation(value = "获取理赔邮件渠道列表")
    @GetMapping("/channel/email")
    public List<ChannelEmail> getChannelEmailInfo() {
        return claimEmailProperties.getChannel();
    }

    /**
     * 更改结案结果
     *
     * @return
     */
    @ApiOperation(value = "更改结案结果")
    @PutMapping("/{claimId}/result")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "更改结案结果")
    public void updateFinishResult(@PathVariable int claimId, @RequestBody ProgressDTO dto) {
        dto.setClaimId(claimId);
        dto.setCreateBy(HttpRequestUtil.getUserName());
        service.updateFinishResult(claimId, dto);
    }




    /**
     * 更改结案结果
     *
     * @return
     */
    @ApiOperation(value = "更改拒赔类型")
    @PutMapping("/reject/change/{claimId}")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "更改拒赔类型")
    public void updateRejectFinishRejectResult(@PathVariable int claimId, @RequestBody ProgressDTO dto) {
        dto.setClaimId(claimId);
        dto.setCreateBy(HttpRequestUtil.getUserName());
        service.updateRejectFinishResult(claimId, dto);
    }

    /**
     * 修改理赔备注
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改理赔备注")
    @PostMapping("/{claimId}")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "修改理赔备注")
    public void updateClaimNote(@PathVariable int claimId, @RequestBody WxClaimDTO dto) {
        dto.setId(claimId);
        service.updateClaimNote(dto);
    }

    /**
     * 修改理赔原因
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改理赔原因")
    @PostMapping("/{claimId}/risk_reason")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "修改理赔原因")
    public void updateClaimRiskReason(@PathVariable int claimId, @RequestBody WxClaimDTO dto) {
        dto.setId(claimId);
        service.updateClaimRiskReason(dto);
    }

    /**
     * 修改理赔出险类型
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改理赔出险类型")
    @PutMapping("/{claimId}/riskType")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "修改理赔出险类型")
    public void updateClaimRiskType(@PathVariable int claimId, @RequestBody WxClaimDTO dto) {
        dto.setId(claimId);
        service.updateClaimRiskType(dto);
    }

    /**
     * 修改理赔（出险时间、出险经过、出险类型）
     * 出险时间时，不能晚于报案时间，也就是提交报案申请的时间
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改理赔（出险时间、出险经过、出险类型）")
    @PutMapping("/{claimId}")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "修改理赔（出险时间、出险经过、出险类型）")
    public void updateClaimInfo(@PathVariable int claimId, @RequestBody WxClaimDTO dto) {
        dto.setId(claimId);
        service.updateClaimInfo(dto);
    }

    /**
     * 修改理赔专员
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改理赔专员")
    @PutMapping("/{claimId}/settlement")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "修改理赔专员")
    public void updateClaimSettlement(@PathVariable int claimId, @RequestBody WxClaimDTO dto) {
        dto.setId(claimId);
        service.updateClaimSettlement(dto);
    }

    /**
     * 下载理赔文件
     *
     * @param claimId
     * @return
     */
    @ApiOperation(value = "下载理赔文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimId", value = "理赔Id", dataType = "long", paramType = "path")
    })
    @GetMapping("/{claimId}/file/download")
    @SystemLog(module = LogConstants.MODULE_SM_REPORT_ORG, actionType = LogActionType.DOWNLOAD, descrption = "下载理赔文件")
    public void downloadClaimFiles(@PathVariable int claimId, HttpServletResponse response) {
        claimServiceConfig.downloadClaimFiles(claimId, response);
    }

    /**
     * 下载理赔文件
     *
     * @param claimId
     * @return
     */
    @ApiOperation(value = "下载理赔文件，区分新老")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimId", value = "理赔Id", dataType = "long", paramType = "path")
            ,@ApiImplicitParam(name = "newFlag", value = "1:新标签，0:历史标签", dataType = "long", paramType = "query")
    })
    @GetMapping("/{claimId}/file/download/flag")
    @SystemLog(module = LogConstants.MODULE_SM_REPORT_ORG, actionType = LogActionType.DOWNLOAD, descrption = "下载理赔文件，区分新老")
    public void downloadClaimFiles(@PathVariable int claimId, int newFlag, HttpServletResponse response) {
        claimServiceConfig.downloadClaimFilesByFlag(claimId, newFlag, response);
    }


    /**
     * 下载理赔文件
     *
     * @param cfIdList
     * @param response
     * @param claimId
     * @return
     */
    @ApiOperation(value = "部分下载理赔文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimId", value = "理赔Id", dataType = "long", paramType = "path")
    })
    @GetMapping("/{claimId}/file/part/download")
    @SystemLog(module = LogConstants.MODULE_SM_REPORT_ORG, actionType = LogActionType.DOWNLOAD, descrption = "下载理赔文件")
    public void downloadClaimFiles(@RequestParam List<Integer> cfIdList, @PathVariable int claimId, HttpServletResponse response) {
        claimServiceConfig.downloadPartClaimFiles(cfIdList, claimId, response);
    }

    /**
     * 查询订单理赔报表-区域理赔数据
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单理赔报表-区域理赔数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "分支", dataType = "string", paramType = "query")
    })
    @GetMapping("/summary/region")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM_ORG, actionType = LogActionType.QUERY, descrption = "查询订单理赔报表-区域")
    public SmyPageInfo<SmClaimRegionSummaryVO, SmClaimSummaryVO> getClaimSummaryByRegion(SmClaimSmyQuery query) {
        return service.getClaimSummaryByRegion(query);
    }

    /**
     * 查询订单理赔报表-区域理赔数据下载
     *
     * @param query
     * @param resp
     * @return
     */
    @ApiOperation(value = "查询订单理赔报表-区域理赔数据下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "分支", dataType = "string", paramType = "query")
    })
    @GetMapping("/summary/region/download")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM_ORG, actionType = LogActionType.DOWNLOAD, descrption = "查询订单理赔报表-区域下载")
    public void downloadSmClaimRegionSummary(SmClaimSmyQuery query, HttpServletResponse resp) {
        service.downloadSmClaimRegionSummary(query, resp);
    }

    /**
     * 查询订单理赔报表-机构理赔数据
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单理赔报表-机构理赔数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "分支", dataType = "string", paramType = "query")
    })
    @GetMapping("/summary/org")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM_ORG, actionType = LogActionType.QUERY, descrption = "查询订单理赔报表-区域")
    public SmyPageInfo<SmClaimOrgSummaryVO, SmClaimSummaryVO> getClaimSummaryByOrg(SmClaimSmyQuery query) {
        return service.getClaimSummaryByOrg(query);
    }

    /**
     * 查询订单理赔报表-区域下载
     *
     * @param query
     * @param resp
     * @return
     */
    @ApiOperation(value = "查询订单理赔报表-机构理赔数据下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "分支", dataType = "string", paramType = "query")
    })
    @GetMapping("/summary/org/download")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM_ORG, actionType = LogActionType.DOWNLOAD, descrption = "查询订单理赔报表-区域下载")
    public void downloadSmClaimOrgSummary(SmClaimSmyQuery query, HttpServletResponse resp) {
        service.downloadSmClaimOrgSummary(query, resp);
    }

    /**
     * 查询订单理赔-产品报表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询订单理赔-产品报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "productName", value = "产品名称", dataType = "string", paramType = "query")
    })
    @GetMapping("/summary/product")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM_PRODUCT, actionType = LogActionType.QUERY, descrption = "查询订单理赔报表-产品报表")
    public SmyPageInfo<SmClaimProductSummaryVO, SmClaimSummaryVO> getClaimSummaryByProduct(SmClaimSmyQuery query) {
        return service.getClaimSummaryByProduct(query);
    }

    /**
     * 查询订单理赔报表-产品下载
     *
     * @param query
     * @param resp
     * @return
     */
    @ApiOperation(value = "查询订单理赔报表-产品下载")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startDate", value = "开始时间 From", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "endDate", value = "结束时间 To", dataType = "date", paramType = "query"),
            @ApiImplicitParam(name = "regionName", value = "区域", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orgName", value = "分支", dataType = "string", paramType = "query")
    })
    @GetMapping("/summary/product/download")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM_PRODUCT, actionType = LogActionType.DOWNLOAD, descrption = "查询订单理赔报表-产品下载")
    public void downloadSmClaimProductSummary(SmClaimSmyQuery query, HttpServletResponse resp) {
        service.downloadSmClaimProductSummary(query, resp);
    }

    /**
     * 查询用户微信理赔案件更进列表
     *
     * @param claimId
     * @return
     */
    @ApiOperation(value = "查询用户微信理赔案件更进列表")
    @GetMapping("/{claimId}/followUp")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "查询用户微信理赔案件更进列表")
    public List<SmClaimFollowUpVO> getClaimFollowUpListById(@PathVariable int claimId) {
        return service.getClaimFollowUpListById(claimId);
    }

    /**
     * 保存用户微信理赔案件更进列表
     *
     * @param claimId
     * @param dto
     * @return
     */
    @ApiOperation(value = "保存用户微信理赔案件更进列表")
    @PostMapping("/{claimId}/followUp")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.ADD, descrption = "保存用户微信理赔案件更进列表")
    public void saveClaimFollowUp(@PathVariable int claimId, @RequestBody SmClaimFollowUpDTO dto) {
        dto.setClaimId(claimId);
        dto.setCreateBy(HttpRequestUtil.getUserName());
        dto.setCreateByCode(HttpRequestUtil.getUserId());
        dto.setType(EnumClaimFollowupType.FOLLOWUP.getCode());
        service.saveClaimFollowUp(dto);
    }

    @ApiOperation(value = "上传结案或者拒赔通知书")
    @GetMapping("/{claimId}/upload/finish")
    public void saveClaimFollowUp(@PathVariable int claimId, @RequestParam("url") String url) {
        service.updateFinishNotify(claimId, url);
    }

    @ApiOperation(value = "查询众安理赔详情")
    @GetMapping("/query/za-detail/{claimId}")
    public ZaClaimStatusDetailVO queryZaDetail(@PathVariable("claimId") int claimId) {
        return zaClaimService.searchZaClaim(claimId);
    }

    @ApiOperation(value = "验证众安理赔详情")
    @GetMapping("/validate/za")
    public void validateZaDetail() {
        zaClaimService.validateZaAuth();
    }

    @ApiOperation(value = "验证众安理赔详情")
    @GetMapping("/validate/zaReport")
    public void validateZaReport() {
        zaClaimService.validateZaReport();
    }

    @ApiOperation(value = "验证众安理赔详情")
    @GetMapping("/validate/task")
    public void validateZaTask() {
        zaClaimService.validateZaTask();
    }

    /**
     * 查询用户微信理赔案件更进列表
     *
     * @param fileUrl
     * @return
     */
    @ApiOperation(value = "理赔结案导入")
    @PostMapping("/case/closed/import")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "理赔结案导入")
    public Map<String, String> importCaseClosed(String fileUrl) {
        Map<String, String> map = Maps.newHashMap();
        map.put("id", service.parseCaseClosedFile(fileUrl));
        return map;
    }

    @ApiOperation(value = "理赔结案导入结果查询")
    @GetMapping("/case/closed/import/{batchId}")
    public SmClaimImportResult importCaseClosedResult(@PathVariable("batchId") String batchId) {
        return service.detailByBatchId(batchId);
    }

    @ApiOperation(value = "邮件重试")
    @PostMapping("/mail/retry")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "操作理赔流程")
    public void retry(@RequestBody List<Integer> ids) {
        log.info("邮件重试-{}", ids);

        if (Objects.equals(HttpRequestUtil.getUserId(), CLAIM_MANAGER)) {
            service.retry(ids);
        }
    }

    @ApiOperation(value = "邮件重试1")
    @PostMapping("/mail/retry/data")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "邮件重试1")
    public void retry(@RequestBody SmClaimEmail claimEmail) {
        log.info("邮件重试1-{}", JSONObject.toJSONString(claimEmail));

        if (Objects.equals(HttpRequestUtil.getUserId(), CLAIM_MANAGER)) {
            service.retryMail(claimEmail);
        }
    }

    @ApiOperation(value = "邮件新增重发")
    @PostMapping("/mail/retry/claim")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "邮件新增重发")
    public void addRetryMail(@RequestBody List<Integer> ids) {
        log.info("邮件新增重发-{}", JSONObject.toJSONString(ids));
        if (Lists.newArrayList(CLAIM_MANAGER, "ZHNX31932", "ZHNX32533", "ZHNX37681").contains(HttpRequestUtil.getUserId())) {
            service.retryNew(ids);
        }
    }


//    /**
//     * 查询用户微信理赔案件更进列表
//     *
//     * @param fileUrl
//     * @return
//     */
//    @ApiOperation(value = "理赔结案导入")
//    @PostMapping("/case/closed/import/check")
//    public List<CaseClosedCheckResultImport> checkCaseClosedFile(String fileUrl) {
//        return service.checkCaseClosedFile(fileUrl);
//    }


    /**
     * 查询用户微信理赔案件更进列表
     *
     * @param fileUrl
     * @return
     */
    @ApiOperation(value = "二维表导入导入")
    @PostMapping("/case/api/import")
    public void checkApiFile(String fileUrl, String processType) {
        claimServiceConfig.parseApiFile(fileUrl, processType);
    }


    /**
     * html转图片
     *
     * @param htmlToImageReq 图片转码请求
     * @return 图片转码结果
     */
    @ApiOperation(value = "html转图片")
    @PostMapping("/htmlToImageReq")
    public HtmlToImageRes htmlToImageReq(@RequestBody HtmlToImageReq htmlToImageReq) {
        final com.cfpamf.cmis.common.base.CommonResult<HtmlToImageRes> image = insuranceImageClient.image(htmlToImageReq);
        log.info("html转图片{}", image);
        AssertUtills.isTrue(Objects.nonNull(image), "图片转换失败");
        return image.getData();
    }


    /**
     * 根据理赔ID查询对应的理赔海报信息
     *
     * @param smClaimId 理赔ID
     * @return 理赔海报信息
     */
    @ApiOperation(value = "查询理赔海报信息")
    @GetMapping("/qrySmClaimPosterInfo/{smClaimId}")
    public SmClaimPosterInfo qrySmClaimPosterInfo(@PathVariable String smClaimId) {
        return smClaimPosterInfoService.qrySmClaimPosterInfo(smClaimId);
    }


    /**
     * 保存理赔海报信息
     *
     * @param smClaimPosterInfo 理赔海报信息
     */
    @ApiOperation(value = "保存理赔海报信息")
    @PostMapping("/saveSmClaimPosterInfo")
    public SmClaimPosterInfo saveSmClaimPosterInfo(@RequestBody SmClaimPosterInfo smClaimPosterInfo) {
        return smClaimPosterInfoService.saveSmClaimPosterInfo(smClaimPosterInfo);
    }

    @ApiOperation(value = "理赔快讯审批信息查询入口")
    @GetMapping("/posterApply/enter/{smClaimId}")
    public CommonResult<SmClaimPosterApplyDetailVO> posterApplyEnter(@PathVariable String smClaimId) {
        SmClaimPosterApplyDetailVO vo = smClaimPosterApplyService.qryApplyDetail(smClaimId);
        return CommonResult.successResult(vo);
    }

    @ApiOperation(value = "理赔快讯审批结果查看")
    @GetMapping("/posterApply/view/{smClaimId}")
    public CommonResult<SmClaimPosterApplyDetailVO> posterApplyView(@PathVariable String smClaimId){
        SmClaimPosterApplyDetailVO vo = smClaimPosterApplyService.viewApplyOrPoster(smClaimId);
        return CommonResult.successResult(vo);
    }



    @ApiOperation(value = "查询理赔快讯审批列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyIds", value = "保险公司", dataType = "body", paramType = "query"),
            @ApiImplicitParam(name = "accidentType", value = "出险类型", dataType = "body", paramType = "query"),
            @ApiImplicitParam(name = "insuranceProductsList", value = "投保产品", dataType = "body", paramType = "query"),
            @ApiImplicitParam(name = "regionCodes", value = "区域机构", dataType = "body", paramType = "query"),
            @ApiImplicitParam(name = "claimNo", value = "理赔编号", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "applyUserName", value = "申请人", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "applyTimeStart", value = "申请时间-起", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "applyTimeEnd", value = "申请时间-止", dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "approvalStatus", value = "审批状态", dataType = "string", paramType = "query")
    })
    @PostMapping("/posterApply/list")
    public CommonResult<PageInfo<SmClaimPosterApplyVO>> posterApplyList(@RequestBody SmClaimApplyQuery query) {
        PageInfo<SmClaimPosterApplyVO> pi = smClaimPosterApplyService.qryApplyList(query);
        return CommonResult.successResult(pi);
    }

    @ApiOperation(value = "理赔快讯审批操作")
    @PostMapping("/postApply/approve")
    public CommonResult<SmClaimPosterApplyDetailVO> postApplyApprove(@RequestBody SmClaimPosterApplyDetailVO vo) {
        return CommonResult.successResult(smClaimPosterApplyService.approval(vo));
    }

    @ApiOperation(value = "理赔快讯审批信息保存")
    @PostMapping("/postApply/save")
    public CommonResult<SmClaimPosterApplyDetailVO> postApplySave(@RequestBody SmClaimPosterApplyDetailVO vo){
        return CommonResult.successResult(smClaimPosterApplyService.saveApply(vo));
    }


    @PostMapping("/extract/timeliness")
    public void executeTimelinessFromClaimProgress(@RequestParam("startTime") String startTime, @RequestParam("endTime") String endTime) {
        LocalDateTime sTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime eTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        claimProgressTimelinessService.extract(sTime, eTime);
    }

    @PostMapping("/tool/extract")
    public static String toolTest(@RequestBody Map<String, String> map) {

        final String regex = "\\{.*\\}";
        if (!map.containsKey("s")) {
            return "";
        }
        String s= map.get("s").replace("\n", "");
        final Pattern pattern = Pattern.compile(regex, Pattern.MULTILINE);
        final Matcher matcher = pattern.matcher(s);

        if (matcher.find()) {
            return matcher.group(0);
        }
        return "";
    }

    public static void main(String[] args) {
        Map map = new HashMap();
        map.put("s","```json\n{\n  \"policyNo\": \"ZA20240423008\",\n  \"insuredName\": \"\"\n}\n```");
        toolTest(map);
    }


    @ApiOperation(value = "理赔结案导入")
    @PostMapping("/case/unreport/import")
    public Map<String, String> importUnreportClosed(String fileUrl) {
        Map<String, String> map = Maps.newHashMap();
        map.put("id", distinctService.parseCaseClosedFile(fileUrl, "CLAIM_IMPORT_CASE_CLOSED_ALL"));
        return map;
    }

    @ApiOperation(value = "理赔导入资料准备中案件")
    @PostMapping("/case/prepare/import")
    public Map<String, String> importPrepareClosed(String fileUrl) {
        Map<String, String> map = Maps.newHashMap();
        map.put("id", distinctService.parseCaseClosedFile(fileUrl, "CLAIM_IMPORT_STEP_PREPARE"));
        return map;
    }

    @ApiOperation(value = "导入历史纪录")
    @PostMapping("/import/history/page")
    public PageInfo<SmClaimImportResult> pageImportHistory(@RequestBody ClaimImportHistoryQuery query) {
        return distinctService.pageImportHistory(query);
    }




    @ApiOperation(value = "理赔标签自定义")
    @PostMapping("/custom/label")
    public Integer addLabel(@RequestBody SmClaimLabel label) {
        return service.addLabel(label);
    }

    @ApiOperation(value = "理赔标签删除")
    @PutMapping("/custom/label/delete/{id}")
    public void deleteLabel(@PathVariable("id") int labelId) {
        service.deleteLabelId(labelId);
    }

    @ApiOperation(value = "理赔标签自定义")
    @GetMapping("/kp/status/{claimId}")
    public Map<String, String> queryKpClaimStatus(@PathVariable("claimId") int claimId) {
        String result = kaiPingClaimProgressService.queryKpStatus(claimId);
        return MapUtil.builder("statusName", result).build();
    }

    @ApiOperation(value = "理赔历史文件标签处理")
    @PostMapping("/file/history/tag")
    public void handleHistoryFileTag(@RequestBody List<Integer> claimIds) {
        distinctService.handleHistoryFileApproveNode(claimIds);
    }

    @ApiOperation(value = "理赔历史文件标签手动处理")
    @PostMapping("/file/history/tag/manual")
    public void manualHandleHistoryFileTag(@RequestBody Map<String, List<String>> map) {
        log.info("当前处理人-{}", HttpRequestUtil.getUserId());
        distinctService.manualHandleNode(new HashSet<>(map.get("node")), map.get("cfId"));
    }

    @Autowired
    private SmClaimReimbursementZaMapper reimbursementZaMapper;

    @PostMapping("/za/handle/data")
    public void za(@RequestBody List<Integer> claimIdList) {
        log.info("当前执行人-{}", HttpRequestUtil.getUserId());
        reimbursementZaMapper.updateZaByClaimIdList(claimIdList);
    }

    @PostMapping("/follow/page")
    @ApiOperation(value = "催办跟进列表")
    public PageInfo<SmClaimFollowUpListVo> getSmClaimByPage(@RequestBody SmClaimFollowupQueryVo followupQueryVo) {

        return distinctService.followUpPageList(followupQueryVo);

    }

    @GetMapping("/follow/export")
    @ApiOperation(value = "催办跟进列表导出")
    public void getSmClaimExport(@ModelAttribute SmClaimFollowupQueryVo followupQueryVo,HttpServletResponse response) {
         distinctService.downloadFollowUp(followupQueryVo,response);
    }

    @PostMapping("/follow/reply")
    @ApiOperation(value = "催办列表回复")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "催办回复")
    public void reply(@RequestBody SmClaimFollowUpDTO follow) {
        follow.setCreateBy(HttpRequestUtil.getUserName());
        follow.setCreateByCode(HttpRequestUtil.getUserId());
        follow.setType(EnumClaimFollowupType.REPLY.getCode());
        service.saveClaimFollowUp(follow);
//        distinctService.hastenReply(follow);

    }

    @PostMapping("/update/file")
    @ApiOperation(value = "理赔资料更新")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "理赔资料更新")
    public void updateFile(@RequestBody SmClaimFileUnitDTO fileUnitDTO) {
        if (Objects.equals(HttpRequestUtil.getUserId(), CLAIM_MANAGER)) {
            distinctService.updateFileInfo(fileUnitDTO);
        }
    }

    @PostMapping("/manual/update/progress")
    @ApiOperation(value = "理赔进度更新")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "理赔进度更新")
    public void updateProgress(@RequestBody ProgressDTO progressDTO) {

        if (Objects.equals(HttpRequestUtil.getUserId(), CLAIM_MANAGER)) {
            distinctService.updateProgressInfo(progressDTO);
        }
    }

    @Autowired
    private ClaimProcessNodeChangeHandler claimProcessNodeChangeHandler;
    @PostMapping("/manual/syncAutoAddLabel")
    @ApiOperation(value = "手工打标签")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "手工打标签")
    public void syncAutoAddLabel(@RequestBody List<Integer> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return ;
        }
        ids.stream().forEach(t->{
            claimProcessNodeChangeHandler.handlerEvent(new ClaimProcessNodeChangeEvent(t));
        });
    }
    @Autowired
    private ClaimRuleJobHandler claimRuleJobHandler;
    @PostMapping("/manual/syncAutoAddLabel2")
    @ApiOperation(value = "手工打标签2")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "手工打标签2")
    public void syncAutoAddLabel2() {
        claimRuleJobHandler.syncAutoAddLabel();
    }


    @GetMapping("/evaluation/list")
    @ApiOperation(value = "理赔案件评价列表")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "理赔案件评价列表")
    public PageInfo<SmClaimEvaluationPageVo>  evaluationList(@ModelAttribute SmClaimEvaluationQuery query) {
        log.info("开始查询理赔案件评价列表:{}", JSONObject.toJSONString(query));
        return service.queryEvaluationPage(query);
    }

    @GetMapping("/evaluation/info")
    @ApiOperation(value = "理赔案件评价列表")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.QUERY, descrption = "理赔案件评价列表")
    public ClaimEvaluationVo evaluationInfo(@RequestParam("id") Integer id) {
        return service.queryEvaluation(id);
    }

    @GetMapping("/evaluation/list/export")
    @ApiOperation(value = "导出理赔案件评价列表")
    public void evaluationExport(@ModelAttribute SmClaimEvaluationQuery query,HttpServletResponse response){
        log.info("开始查询理赔案件评价列表:{}", JSONObject.toJSONString(query));
        int pageSize = 5000;
        query.setPage(1);
        query.setSize(pageSize);
        String fileName = "理赔案件评价列表";
        Class clazz = SmClaimEvaluationExcelVo.class;
        try (OutputStream os = response.getOutputStream()) {
            PageInfo<SmClaimEvaluationPageVo> pageInfo = service.queryEvaluationPage(query);
            if (pageInfo.getTotal() > 65535) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil excelReader = ExcelBuilderUtil.newInstance()
                    .createSheet("理赔案件评价列表")
                    .buildSheetHead(clazz);
            int nextPage = 1;
            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = service.queryEvaluationPage(query);
                List<SmClaimEvaluationPageVo> pageList = pageInfo.getList();
                if(CollectionUtil.isEmpty(pageList)){
                    break;
                }
                excelReader.addSheetData(convertVo(pageList));
                nextPage++;
            }
            excelReader.write(os);
        } catch (Exception e) {
            log.info("文件导出异常",e);
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    public List<SmClaimEvaluationExcelVo> convertVo(List<SmClaimEvaluationPageVo> data) {
        return data.stream()
                .map(entry->{
                    SmClaimEvaluationExcelVo vo = new SmClaimEvaluationExcelVo();
                    String score = entry.getEvaluationScore();
                    score = convertEvaluation(score);
                    entry.setEvaluationScore(score);
                    BeanUtils.copyProperties(entry,vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private String convertEvaluation(String score){
        switch (score){
            case "1":
                return "不满意";
            case "2":
                return "待提升";
            case "3":
                return "一般";
            case "4":
                return "满意";
            case "5":
                return "非常满意";
            default:
                return "";
        }
    }


    @PostMapping("/history/timeout/handle")
    public void syncUpdateHistory() {
        log.info("超时历史数据处理");
        distinctService.updateHistoryTimeout();
    }


    @Autowired
    private AIDifyService difyService;

    @PostMapping("/ai")
    @ApiOperation(value = "ai")
    public List<AiFileResponseVO> aiTest(@RequestBody AiBusinessParams params) {
        return difyService.executeClaimWorkFlow(params);
    }

    @PostMapping("/ai/result/del")
    @ApiOperation(value = "del")
    public void deleteByAiResultById(int id) {
        difyService.deleteById(id);
    }

    @PostMapping("/ai/url/rerun")
    public void aiUrlRerun() {
        if (Objects.equals(HttpRequestUtil.getUserId(), CLAIM_MANAGER)) {
            difyService.reRunUrl();
        }
    }




    @Autowired
    private SmClaimMapper claimMapper;
    @ApiOperation(value = "查看用户结案通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimId", value = "理赔Id", required = true, dataType = "string", paramType = "path"),
    })
    @GetMapping("/finish/notify/{claimId}")
    public StepFinishReportVO getWxClaimById(@PathVariable int claimId) {
        return distinctService.getStepFinish(claimId);
    }


    @Autowired
    RedisUtil<String, StepFinishNotifyDTO> util;

    @ApiOperation(value = "保存结案通知函")
    @PostMapping("/finish/notify/edit")
    public void getWxClaimById(@RequestBody StepFinishNotifyDTO finishNotifyDTO) {
        distinctService.addFinishNotify(finishNotifyDTO);
    }

    @ApiOperation(value = "查询结案通知函模板")
    @GetMapping("/finish/notify/query/{claimId}")
    public StepFinishNotifyDTO getNotifyDTO(@PathVariable int claimId) {
        return distinctService.selectFinishNotifyByClaimId(claimId);
    }


    @Autowired
    private WxPushMessageHandler pushMessageHandler;

    @ApiOperation(value = "触发结案通知函通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "claimId", value = "理赔", required = true, dataType = "string", paramType = "path"),
    })
    @PostMapping("/finish/notify/push")
    public void pushStepFinishReportMsg(@RequestBody ClaimProgressDTO dto) {
        WxClaimNotifyVO wcn = claimMapper.getClaimWxNotify(dto.getClaimId());
        if (Objects.isNull(wcn)) {
            throw new BizException("", "结案信息不存在");
        }
        WxClaimNotifyEvent event = new WxClaimNotifyEvent(dto.getClaimId(), null, null, null);
        pushMessageHandler.sendStepFinishReportMsg(event , wcn, true);
    }


    @PostMapping("/loan/reset")
    public void pushStepFinishReportMsg() {
        if (Objects.equals(HttpRequestUtil.getUserId(), CLAIM_MANAGER)) {
            claimMapper.truncateLoanCustomer();
        }
    }

    @PutMapping("/exclude/reject/sta")
    @ApiOperation(value = "案件机构驳回标签")
    @SystemLog(module = LogConstants.MODULE_SM_CLAIM, actionType = LogActionType.UPDATE, descrption = "案件机构驳回标签")
    public void excludeRejectSta(Integer claimId, Integer state) {

        if (Objects.isNull(claimId) || Objects.isNull(state)) {
            throw new BizException("", "参数不完整");
        }

        claimMapper.updateExclude(claimId, state);

    }
}
