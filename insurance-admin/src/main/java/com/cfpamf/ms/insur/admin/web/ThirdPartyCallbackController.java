package com.cfpamf.ms.insur.admin.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.ZhongAnGroupEnum;
import com.cfpamf.ms.insur.admin.event.handler.OrderAppSuccessSmsEventHandler;
import com.cfpamf.ms.insur.admin.external.dj.model.req.DJPayNotifyReq;
import com.cfpamf.ms.insur.admin.external.dj.model.req.DjPaySyncReq;
import com.cfpamf.ms.insur.admin.external.fx.model.FxRequestBodyDTO;
import com.cfpamf.ms.insur.admin.external.tk.model.TkReqBox;
import com.cfpamf.ms.insur.admin.external.tk.model.TkRespBox;
import com.cfpamf.ms.insur.admin.external.tk.model.TkRespGroupWrapper;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpEndorOrderReq;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpOrderReq;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpTicket;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpV2Req;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.job.SmOrderRenewalJobHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.order.JdalOrderNotifyDTO;
import com.cfpamf.ms.insur.admin.service.SmOrderCoreService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.admin.service.order.ZaOrderService;
import com.cfpamf.ms.insur.admin.util.IpUtil;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKMemberChangeNotify;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKPayNotify;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.callback.ZaOfflinePayNotify;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.callback.ZaPayNotify;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupEndorEffective;
import com.cfpamf.ms.pay.facade.dto.PayOrderNotifyDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 第三方回调接口（勿动）
 *
 * <AUTHOR>
 */
@Api(value = "第三方回调接口（勿动）", tags = {"第三方回调接口（勿动）"})
@RequestMapping({BaseConstants.ADMIN_VERSION + "/public", BaseConstants.ADMIN_VERSION_OLD})
@RestController
@Slf4j
public class ThirdPartyCallbackController {

    /**
     * 小额保险订单接口
     */
    @Autowired
    private SmOrderCoreService orderCoreService;

    @Autowired
    SmOrderServiceWrapper wrapper;

    @Autowired
    OrderAppSuccessSmsEventHandler eventHandler;

    @Autowired
    private ZaOrderServiceAdapter zaServerAdapter;;

    /**
     * 泛华小额保险支付回调接口
     *
     * @param orderSn
     * @param recommendId
     */
    @ApiOperation(value = "泛华小额保险支付回调接口",tags = "CORE")
    @PostMapping("/order/callback")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "泛华小额保险支付回调接口")
    public void handFhOrderPayedCallback(@RequestParam String orderSn, @RequestParam(required = false) String recommendId,
                                         HttpServletRequest request) {

        log.info("泛华支付回调接口handFhOrderPayedCallbackorderSn= {}, recommendId= {}", orderSn, recommendId);
        orderCoreService.handFhOrderPayedCallback(orderSn, recommendId, request.getQueryString());
    }

    /**
     * 中华联合小额保险支付回调接口
     *
     * @param xml
     */
    @ApiOperation(value = "中华联合小额保险支付回调接口",tags = "CORE")
    @PostMapping(value = "/order/cic/payCallback", consumes = "text/xml", produces = "text/xml")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "中华联合小额保险支付回调接口")
    public void handCicOrderPayedCallback(@RequestBody String xml, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("中华联合支付回调接口 handCicOrderPayedCallback xml= {}", xml);

        wrapper.handAsyncPayCallback(EnumChannel.CIC.getCode(), null, xml, request, response);
//        return orderCoreService.handCicOrderPayedCallback(xml);
    }

    /**
     * 中华保险支付POST重定向
     *
     * @param fhOrderId
     * @return
     */
    @ApiOperation(value = "中华保险支付POST重定向",tags = "CORE")
    @PostMapping("/order/cic/payRedirect")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "中华保险支付POST重定向")
    public void handCicOrderPayedRedirect(HttpServletRequest request, HttpServletResponse response, @RequestParam String fhOrderId) throws IOException {
        log.info("中华联合支付跳转接口 cicPayRedirect fhOrderId={}", fhOrderId);
//        orderCoreService.handCicOrderPayedRedirect(response, fhOrderId);
        wrapper.handSyncPayCallback(EnumChannel.CIC.getCode(), fhOrderId, null, request, response);

    }


    /**
     * 大家保险渠道小额保险支付回调接口 因为参数可以直接框架解析
     */
    @ApiOperation(value = "大家保险支付回调接口",tags = "CORE")
    @PostMapping(value = "/order/dj/payCallback/{orderId}")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "大家保险支付回调接口")
    public void handDjOrderPayedCallback(
            @PathVariable("orderId") String orderId, DJPayNotifyReq req, HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("支付回调接口-异步 handDjOrderPayedCallback orderId={},req= {}", orderId, req);
//        return orderCoreService.handCicOrderPayedCallback(xml);
        wrapper.handAsyncPayCallback(EnumChannel.DJ.getCode(), orderId, req, request, response);
    }

    /**
     * 大家保险支付POST重定向
     */
    @ApiOperation(value = "保险支付POST重定向(前端同步)",tags = "CORE")
    @PostMapping("/order/dj/payRedirect/{orderId}")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "大家支付POST重定向")
    public void handDjOrderPayedRedirect(@PathVariable("orderId") String orderId, DjPaySyncReq req, HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("支付-  同步回调地址 djPayRedirect fhOrderId={} ,params={}", orderId, req);
        wrapper.handSyncPayCallback(EnumChannel.DJ.getCode(), orderId, req, request, response);

//        orderCoreService.handCicOrderPayedRedirect(response, fhOrderId);
    }

    @ApiOperation(value = "国寿财小额保险支付后重定向地址",tags = "CORE")
    @PostMapping(value = "/order/gsc/payRedirect")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "国寿财POST重定向")
    public void handGscOrderPayRedirect(String req,
                                        String pid,
                                        HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("国寿财小额保险同步回调 handGscOrderPayRedirect xml= {}", req);

        wrapper.handSyncPayCallback(EnumChannel.GSC.getCode(), pid, req, request, response);
//
    }

    /**
     * 国寿财保险支付异步通知
     */
    @ApiOperation(value = "保险支付POST(异步通知)",tags = "CORE")
    @PostMapping("/order/gsc/payCallback")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "国寿财支付POST异步通知")
    public void handDjOrderPayeCallback(String req, HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("国寿财支付重定向-异步通知 payCallback xml={}", req);
        wrapper.handAsyncPayCallback(EnumChannel.GSC.getCode(), null, req, request, response);

//        orderCoreService.handCicOrderPayedRedirect(response, fhOrderId);
    }

    /**
     * 国寿财保险支付异步通知
     */
    @ApiOperation(value = "保险支付POST(异步通知)-中和农信",tags = "CORE")
    @PostMapping("/order/zhnx/payCallback")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "中和农信POST异步通知")
    public void handZhnxOrderPayeCallback(String req, HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("中和农信支付重定向-异步通知 payCallback xml={}", req);
        wrapper.handAsyncPayCallback(EnumChannel.GSC.getCode(), null, req, request, response);

//        orderCoreService.handCicOrderPayedRedirect(response, fhOrderId);
    }

    /**
     * 支付系统支付成功回调
     *
     * @param notifyDTO
     * @return
     */
    @ApiOperation(value = "支付系统支付成功回调",tags = "CORE")
    @PostMapping("/order/insurPay/callback")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "支付系统支付成功回调")
    public void handInsurPayedCallback(@RequestBody PayOrderNotifyDTO notifyDTO,
                                       HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("支付系统支付成功回调 handInsurPayedRedirect PayOrderNotifyDTO={}", JSON.toJSONString(notifyDTO));
        wrapper.handAsyncPayCallback(notifyDTO, request, response);
//        orderCoreService.handInsurPayedCallback(notifyDTO);
    }


    @Autowired
    ZaOrderService orderService;

    /**
     * 智能核保
     */
    @ApiOperation(value = "众安智能核保成功",tags = "CORE")
    @RequestMapping(value = "/order/ai_check/za/accept/{orderId}", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void handZaAICheckAccept(@PathVariable("orderId") String orderId,
                                    @RequestParam("familyQuestionnaireId")
                                            String familyQuestionnaireId,
                                    HttpServletRequest request
            , HttpServletResponse response) throws IOException {

        orderService.handAICheckAccept(orderId, familyQuestionnaireId, request, response);

    }

    /**
     * 智能核保
     */
    @ApiOperation(value = "众安智能核保失败",tags = "CORE")
    @RequestMapping(value = "/order/ai_check/za/fail/{orderId}", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void handZaAICheckFail(@PathVariable("orderId") String orderId,
                                  HttpServletRequest request, HttpServletResponse response) throws IOException {
        //跳转至  healthNoticePage
        orderService.handAICheckAcceptFail(orderId, null, request, response);

    }


    /**
     * 智能核保
     */
    @ApiOperation(value = "众安智能核保超时",tags = "CORE")
    @RequestMapping(value = "/order/ai_check/za/timeout/{orderId}", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void handZaAICheckTimeout(@PathVariable("orderId") String orderId, HttpServletRequest request, HttpServletResponse response) throws IOException {

        orderService.handAICheckAcceptTimeout(orderId, null, request, response);

    }

    @ApiOperation(value="渠道通用承包回调接口（目前只有信美 与众安渠道使用）",tags = "CORE")
    @PostMapping("/order/policy/accept/{channel}")
    public void handChannelCallBackAccept(@PathVariable("channel") String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        wrapper.handChannelCallBackAccept(channel, request, response);
    }


    /**
     * 复兴保险支付回调接口
     */
    @ApiOperation(value = "复兴保险支付回调接口",tags = "CORE")
    @PostMapping(value = "/order/fx/payCallback")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "复兴保险支付回调接口")
    public String handFxOrderPayedCallback(@RequestBody FxRequestBodyDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("复兴保险支付回调接口");

        wrapper.handAsyncPayCallback(EnumChannel.FX.getCode(), null, dto, request, response);
        return null;

    }

    /**
     * 复兴保险老支付回调接口
     */
    @ApiOperation(value = "复兴保险支付回调接口",tags = "CORE")
    @RequestMapping(value = "/order/fx/oldPayCallback", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "复兴保险支付回调接口")
    public String handOldFxOrderPayedCallback(@RequestParam("request_xml") String request_xml, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("复兴保险支付回调接口");
        request.setAttribute("fx_msg_type", "old");
        wrapper.handAsyncPayCallback(EnumChannel.FX.getCode(), null, request_xml, request, response);
        return null;
    }

    /**
     * 复兴保险支付POST重定向
     *
     * @return
     */
    @ApiOperation(value = "复兴保险支付POST重定向",tags = "CORE")
    @RequestMapping(value = "/order/fx/payRedirect", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "复兴保险支付POST重定向")
    public void handFxOrderPayedRedirect(@RequestParam(value = "orderId", required = false) String orderId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("复兴保险支付POST重定向：orderId:", orderId);
        wrapper.handSyncPayCallback(EnumChannel.FX.getCode(), orderId, null, request, response);
    }


    @ApiOperation(value = "众安团险推送",tags = "CORE")
    @PostMapping(value="/order/policy/accept/{channel}/group")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "众安团险回调接口")
    public void handChannelCallBackGroupAccept(@PathVariable("channel") String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("团险推送:{}", channel);
        wrapper.handChannelCallBackGroupAccept(channel, null, request, response);

    }

    /**
     * 京东安联回调接口
     */
    @ApiOperation(value = "京东安联支付回调接口",tags = "CORE")
    @RequestMapping(value = "/order/jdal/payCallback", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "京东安联支付回调接口")
    public String handJDALOrderPayedCallback(@RequestBody JdalOrderNotifyDTO dto, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("京东安联支付回调接口:{}", dto.getEncrypt());
        wrapper.handAsyncPayCallback(EnumChannel.JDAL.getCode(), null, dto.getEncrypt(), request, response);
        return null;
    }


    @ApiOperation(value="泰康雇主责任险回调接口",tags = "CORE")
    @PostMapping("/order/policy/accept/tk/employer")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "泰康雇主责任险回调接口")
    public void handChannelCallBackEmployerAccept(@RequestBody TkReqBox<TkEmpOrderReq> obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("泰康雇主责任险:{}", obj);
        wrapper.handChannelCallBackEmployerAccept(EnumChannel.TK.getCode(), obj, request, response);

    }

    @ApiOperation(value="泰康雇主责任险(新契约)回调接口-V2版本",tags = "CORE")
    @PostMapping("/order/policy/accept/tk/v2/employer")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "泰康雇主责任险回调新版本接口")
    public void handChannelCallBackV2EmployerAccept(@RequestBody TkEmpV2Req obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("泰康雇主责任险新版本:{}", JSON.toJSONString(obj));
        wrapper.handChannelCallBackV2EmployerAccept(EnumChannel.TK.getCode(), obj, request, response);

    }

    @ApiOperation(value="泰康雇主责任险(批改)回传接口-V1版本",tags = "CORE")
    @PostMapping("/order/policy/accept/tk/employerEndor")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "泰康雇主责任险批改回传接口")
    public void handChannelCallBackEmployerEndorAccept(@RequestBody TkReqBox<TkEmpEndorOrderReq> obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("泰康雇主责任险批改回传报文:{}", obj);
        wrapper.handChannelCallBackEmployerEndorAccept(EnumChannel.TK.getCode(), obj, request, response);

    }

    @ApiOperation(value="泰康雇主责任险(批改)回传接口-V2版本",tags = "CORE")
    @PostMapping("/order/policy/accept/tk/v2/employerEndor")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "泰康雇主责任险批改回传接口")
    public void handChannelCallBackV2EmployerEndorAccept(@RequestBody TkEmpV2Req obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("泰康雇主责任险新版本批改回传报文:{}", JSON.toJSONString(obj));
        wrapper.handChannelCallBackV2EmployerEndorAccept(EnumChannel.TK.getCode(), obj, request, response);

    }

    @ApiOperation(value= "泰康雇主责任险专票回传接口",tags = "CORE")
    @PostMapping("/order/policy/accept/tk/v2/employerTicket")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "泰康雇主责任险专票回传接口")
    public void handChannelCallBackV2EmployerTicketAccept(@RequestBody TkEmpTicket obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("泰康雇主责任险专票回传接口:{}", JSON.toJSONString(obj));
        wrapper.handChannelCallBackV2EmployerTicketAccept(EnumChannel.TK.getCode(), obj, request, response);

    }

    /**
     * 渠道保险下单成功重定向
     *
     * @return
     */
    @ApiOperation(value = "渠道保险下单成功重定向",tags = "CORE")
    @RequestMapping(value = "/order/{channel}/paySuccessRedirect", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "渠道保险下单成功重定向")
    public void handChannelPaySuccessRedirect(@PathVariable("channel") String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("渠道保险下单成功重定向");
        wrapper.handSyncPayCallback(channel, null, null, request, response);
    }

    /**
     * 保单绑卡信息回调
     *
     * @return
     */
    @ApiOperation(value = "保单绑卡信息回调",tags = "CORE")
    @RequestMapping(value = "/order/{channel}/handChannelBindCardCallback", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "保单绑卡信息回调")
    public void handChannelBindCardCallback(@PathVariable("channel") String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("保单绑卡信息回调");
        wrapper.handBindCardCallback(channel, request, response);
        //wrapper.handSyncPayCallback(channel, null, null, request, response);
    }

    /**
     * [见费单]-众安支付后回调通知出单
     * TODO 未对报文验签
     *
     * @return
     */
    @ApiOperation(value = "众安支付成功后回调通知出单",tags = "CORE")
    @RequestMapping(value = "/group/zapay/notify", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "渠道保险下单成功重定向")
    public void paySuccessNotify(@ModelAttribute ZaPayNotify notify,
                                 HttpServletRequest request,
                                 HttpServletResponse response) throws IOException {
        String ip = IpUtil.getIpAddr(request);
        log.info("{}-众安支付完成回调通知:消息={}", ip, notify);

        String orderId = notify.getOut_trade_no();
        wrapper.handSyncPayCallback(EnumChannel.ZA.getCode(), orderId, notify, request, response);
        response.getWriter().write("success");
    }


    @ApiOperation(value = "众安支付成功后回调通知出单")
    @RequestMapping(value = "/group/zapay/redo", method = {RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "渠道保险下单成功重定向")
    public void redoNotify(@RequestParam("orderId") String orderId, @RequestParam("channel") String channel) {
        log.info("众安支付完成回调通知:消息");
        wrapper.redoNotify(channel, orderId);
    }

    @ApiOperation(value = "众安执行生效操作保全(不处理我们这边的流程)")
    @RequestMapping(value = "/group/za/correct/active", method = {RequestMethod.POST})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "众安执行生效操作保全")
    public void correctActive(@RequestBody ZaGroupEndorEffective param) {
        log.info("众安执行生效操作保全:{}",param);
        zaServerAdapter.groupEndorEffective(param);
    }

    @ApiOperation(value = "众安支付成功后回调通知出单")
    @RequestMapping(value = "/selfpay/redo", method = {RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "渠道保险下单成功重定向")
    public void redoSelfPayNotify(@RequestParam("orderId") String orderId, @RequestParam("channel") String channel) {
        log.info("众安支付完成回调通知:消息");
        wrapper.redoSelfPayNotify(channel, orderId);
    }

    /**
     * H5方式保单绑卡重定向
     *
     * @return
     */
    @ApiOperation(value = "H5方式保单绑卡重定向",tags = "CORE")
    @RequestMapping(value = "/order/{channel}/handChannelBindRedirect", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "保单绑卡信息回调")
    public void handChannelBindRedirect(@PathVariable("channel") String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("H5方式保单绑卡重定向");
        wrapper.handChannelBindRedirect(channel, request, response);
        //wrapper.handSyncPayCallback(channel, null, null, request, response);
    }

    /*************  s50 续保 **************/
    /**
     * 续保后保单信息回调
     *
     * @return
     */
    @ApiOperation(value = "续保后保单信息回调",tags = "CORE")
    @RequestMapping(value = "/order/za/handChannelRenewalCallback", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "续保后保单信息回调")
    public void handChannelRenewalCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("续保后保单信息回调");
        wrapper.handRenewalCallback(EnumChannel.ZA.getCode(), request, response);
    }

    /**
     * 续保后保单信息回调
     *
     * @return
     */
    @ApiOperation(value = "待续保资质推送",tags = "CORE")
    @RequestMapping(value = "/order/za/handChannelWaitRenewalNotify", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "待续保资质推送")
    public void handChannelWaitRenewalNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("待续保资质推送");
        wrapper.handChannelWaitRenewalNotify(EnumChannel.ZA.getCode(), request, response);
    }

    @Autowired
    SmOrderRenewalJobHandler smOrderRenewalJobHandler;

    /**
     * 续保短信测试
     *
     * @return
     */
    @ApiOperation(value = "续保短信测试")
    @RequestMapping(value = "/order/za/testSendSms", method = {RequestMethod.POST, RequestMethod.GET})
    public void testSendSms(HttpServletRequest request, HttpServletResponse response) {
        smOrderRenewalJobHandler.execute();
    }

    /**
     * 第三方收银台公用重定向
     *
     * @param channel  渠道编码
     * @param orderId  订单id 不能传动态参数可以传一个默认值
     * @param request
     * @param response
     * @throws IOException
     */
    @ApiOperation(value = "小额保险共同用支付后重定向地址",tags = "CORE")
    @RequestMapping(value = "/order/common/{channel}/payRedirect/{orderId}")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void handCommonOrderPayRedirect(@PathVariable("channel") String channel,
                                           @PathVariable("orderId") String orderId,
                                           HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("第三方收银台公用重定向 handCommonOrderPayRedirect {} : {}", channel, orderId);

        wrapper.handSyncPayCallback(channel, orderId, null, request, response);
    }

    /**
     * 第三方收银台公用异步通知
     */
    @ApiOperation(value = "保险公用支付POST(异步通知)",tags = "CORE")
    @PostMapping("/order/common/{channel}/callback/{orderId}")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void handCommonOrderPayCallback(@PathVariable("channel") String channel,
                                           @PathVariable("orderId") String orderId, HttpServletResponse response, HttpServletRequest request) throws IOException {
        log.info("第三方收银台公用异步通知handCommonOrderPayCallback {} {} ", channel, orderId);
        wrapper.handAsyncPayCallback(channel, orderId, null, request, response);
    }

    @ApiOperation(value = "众安线下支付-出单通知接口",tags = "CORE")
    @PostMapping("/notify/zapay/offline")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void offlinePayNotify(@RequestBody ZaOfflinePayNotify data, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String ip = IpUtil.getIpAddr(request);
        log.info("众安线下支付回调通知:{},{}", ip, data);

        Object res = wrapper.handleOfflinePayNotify(EnumChannel.ZA.getCode(), data);

        response.setContentType("application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(res));
    }

    @ApiOperation(value = "泰康支付-出单通知接口(新契约和批增):请求来源于保险中台",tags = "CORE")
    @PostMapping("/v2/tkPay/group/notify")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void payNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String ip = IpUtil.getIpAddr(request);

        String reqBody = HttpRequestUtil.read(request);
        log.info("泰康出单回调通知:{},{}", ip, reqBody);
        TypeReference<TkRespGroupWrapper<TKPayNotify>> typeRef = new TypeReference<TkRespGroupWrapper<TKPayNotify>>() {
        };
        TkRespGroupWrapper<TKPayNotify> groupWrapper = JSON.parseObject(reqBody, typeRef);
        TKPayNotify body = null;
        if (groupWrapper != null) {
            body = groupWrapper.getRequestData();
        }

        response.setContentType("application/json;charset=UTF-8");
        if (body == null) {
            PrintWriter writer = response.getWriter();
            writer.write(JSON.toJSONString(TkRespBox.err("请求数据为空")));
            return;
        }
        Object res = wrapper.groupPayNotify(EnumChannel.TK_PAY.getCode(), body);
        TkRespBox<Object> box = TkRespBox.ok(res);

        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(box));
    }

    @ApiOperation(value = "批减通知接口",tags = "CORE")
    @PostMapping("/v2/tkPay/group/reduction/notify")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void reductionNotify(HttpServletRequest request, HttpServletResponse
            response) throws IOException {
        String ip = IpUtil.getIpAddr(request);

        String reqBody = HttpRequestUtil.read(request);
        log.info("泰康批减回调通知:{},{}", ip, reqBody);
        TypeReference<TkRespGroupWrapper<TKPayNotify>> typeRef = new TypeReference<TkRespGroupWrapper<TKPayNotify>>() {
        };
        TkRespGroupWrapper<TKPayNotify> groupWrapper = JSON.parseObject(reqBody, typeRef);

        Object res = wrapper.groupReductionNotify(EnumChannel.TK_PAY.getCode(), groupWrapper.getRequestData());

        response.setContentType("application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        TkRespBox<Object> box = TkRespBox.ok(res);
        writer.write(JSON.toJSONString(box));
    }

    @ApiOperation(value = "泰康-替换人员通知接口",tags = "CORE")
    @PostMapping("/v2/tkPay/group/replace/notify")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public void memberChangeNotify(HttpServletRequest request, HttpServletResponse
            response) throws IOException {
        String ip = IpUtil.getIpAddr(request);

        String reqBody = HttpRequestUtil.read(request);
        log.info("泰康替换回调通知:{},{}", ip, reqBody);
        TypeReference<TkRespGroupWrapper<TKMemberChangeNotify>> typeRef = new TypeReference<TkRespGroupWrapper<TKMemberChangeNotify>>() {
        };
        TkRespGroupWrapper<TKMemberChangeNotify> groupWrapper = JSON.parseObject(reqBody, typeRef);

        Object res = wrapper.memberChangeNotify(EnumChannel.TK_PAY.getCode(), groupWrapper.getRequestData());

        response.setContentType("application/json;charset=UTF-8");
        PrintWriter writer = response.getWriter();
        TkRespBox<Object> box = TkRespBox.ok(res);
        writer.write(JSON.toJSONString(box));
    }

    /**
     * 泰康H5续保后保单信息回调
     *
     * @return
     */
    @ApiOperation(value = "泰康百万医疗H5续保后保单信息回调",tags = "CORE")
    @RequestMapping(value = "/order/tk/handChannelRenewalCallback", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "泰康百万医疗H5续保后保单信息回调")
    public void tkH5HandChannelRenewalCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("续保后保单信息回调");
        wrapper.handRenewalCallback(EnumChannel.TK_PAY.getCode(), request, response);
    }

    /**
     * 众安团险续保后保单信息回调
     *
     * @return
     */
    @ApiOperation(value = "众安团险待续保资质推送",tags = "CORE")
    @RequestMapping(value = "/order/za/groupHandChannelWaitRenewalNotify", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "待续保资质推送")
    public void groupHandChannelWaitRenewalNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("待续保资质推送");
        request.setAttribute("groupType", ZhongAnGroupEnum.GROUP);
        wrapper.groupHandChannelWaitRenewalNotify(EnumChannel.ZA.getCode(), request, response);
    }


    /**
     * 众安团险续保后保单信息回调
     *
     * @return
     */
    @ApiOperation(value = "众安雇主团险待续保资质推送",tags = "CORE")
    @RequestMapping(value = "/order/za/group/employer/hand/channel/wait/renewal/notify", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER_NOTIFY, actionType = LogActionType.OTHER, descrption = "待续保资质推送")
    public void groupEmployerHandChannelWaitRenewalNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("待续保资质推送");
        request.setAttribute("groupType", ZhongAnGroupEnum.EMPLOYEE_GROUP);
        wrapper.groupHandChannelWaitRenewalNotify(EnumChannel.ZA.getCode(), request, response);
    }
}
