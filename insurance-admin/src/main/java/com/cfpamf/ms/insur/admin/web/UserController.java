package com.cfpamf.ms.insur.admin.web;

import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.pojo.dto.OrgPicExtraDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.UserDTO;
import com.cfpamf.ms.insur.admin.pojo.query.ChangeUserQuery;
import com.cfpamf.ms.insur.admin.pojo.query.OrgPicExtraQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 用户管理接口
 *
 * <AUTHOR>
 */
@Api(value = "保险员工管理接口", tags = {"保险员工管理接口"})
@RequestMapping(BaseConstants.ADMIN_VERSION + "/user")
@RestController
@Slf4j
public class UserController extends AbstractController {

    /**
     * 用户service
     */
    @Autowired
    private UserService userService;

    @PatchMapping("/bizCode")
    @ApiOperation("修改客户经理推荐码")
    public int updateBizCode(@RequestParam("idCard") String idCard,
                             @RequestParam("bizCode") String bizCode) {

        return userService.updateBizCode(idCard, bizCode);
    }

    @ApiOperation("未所有没有推荐码的用户生产推荐码")
    @PostMapping("/initBizCode")
    public int initBizCode() {
        return userService.initBizCodes();
    }

    /**
     * 查询用户分页列表
     *
     * @param keyword
     * @param pageable
     * @return
     */
    @ApiOperation(value = "查询用户分页列表")
    @GetMapping("/list")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.QUERY, descrption = "查询用户分页列表")
    @AuthValidate(value = ApiPermsEnum.INS_USER_QUERY)
    public PageInfo<UserVO> getUsersByPage(@RequestParam(required = false) String keyword,
                                           @RequestParam(required = false) boolean inOffice,
                                           Pageable pageable) {
        return userService.getUsersByPage(keyword, inOffice, pageable);
    }

    /**
     * 查询变更用户分页列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询变更用户分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/change/list")
    @AuthValidate(value = ApiPermsEnum.INS_USER_QUERY)
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.QUERY, descrption = "查询变更用户分页列表")
    public PageInfo<UserVO> getChangeUsersByPage(ChangeUserQuery query) {
        return userService.getChangeUsersByPage(query);
    }

    @ApiOperation(value = "测试")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/change/list1")
    @AuthValidate(value = ApiPermsEnum.INS_USER_QUERY)
    public UserDetailVO getUserDetailVO() {
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        return userDetailVO;
    }

    /**
     * 负责员工变更记录，负责员工查询
     *
     * @param keyword
     * @param pageable
     * @return
     */
    @ApiOperation(value = "负责员工变更记录，负责员工信息查询，inOffice=false，查用户所有职位信息，包括已离职信息")
    @GetMapping("/list/v2")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.QUERY, descrption = "查询用户分页列表")
    @AuthValidate(value = ApiPermsEnum.INS_USER_QUERY)
    public PageInfo<UserVO> getUsersByPageV2(@RequestParam(required = false) String keyword,
                                             @RequestParam(required = false) boolean inOffice,
                                             Pageable pageable) {
        return userService.getUsersByPageV2(keyword, inOffice, pageable);
    }

    /**
     * 新建用户
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "新建用户")
    @PostMapping("")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.ADD, descrption = "新建用户")
    public int addUser(@RequestBody UserDTO dto) {
        return userService.insertUser(dto);
    }

    /**
     * 修改用户
     *
     * @param id
     * @param dto
     */
    @ApiOperation(value = "修改用户")
    @PutMapping("/{id}")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.UPDATE, descrption = "修改用户")
    public void updateUser(@PathVariable int id, @RequestBody UserDTO dto) {
        userService.updateUser(id, dto);
    }

    /**
     * 微信用户解绑
     *
     * @param id
     */
    @ApiOperation(value = "微信用户解绑")
    @PutMapping("/{id}/unbind")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.UPDATE, descrption = "微信用户解绑")
    public void unbindUserWx(@PathVariable int id) {
        userService.unbindUser(id);
    }

    /**
     * 删除用户
     *
     * @param id
     */
    @ApiOperation(value = "删除用户")
    @DeleteMapping("/{id}")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.DELETE, descrption = "删除用户")
    public void deleteUser(@PathVariable int id) {
        userService.deleteUser(id);
    }

    /**
     * 同步用户信息
     *
     * @return
     */
    @ApiOperation(value = "同步用户信息")
    @GetMapping("/sync")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.OTHER, descrption = "同步用户信息")
    public void syncEmployees() {
        userService.syncEmployees();
    }

    /**
     * 微信绑定用户下载
     *
     * @param keyword
     * @param response
     */
//    @ApiOperation(value = "微信绑定用户下载")
//    @GetMapping("/download")
//    @SystemLog(module = LogConstants.MODULE_BASE_USER_SETTING, actionType = LogActionType.DOWNLOAD, descrption = "微信绑定用户下载")
//    @AuthValidate(value = ApiPermsEnum.SM_EMPLOYEE_DOWNLOAD)
//    public void downloadUsers(@RequestParam(required = false) String keyword, HttpServletResponse response) {
//        userService.downloadUsers(keyword, response);
//    }

    /**
     * 导入机构创新业务对接人与负责人信息表
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "导入机构创新业务对接人与负责人信息表")
    @PostMapping("/orgPic/import")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_ORG_PIC_SETTING, actionType = LogActionType.ADD, descrption = "导入机构创新业务对接人与负责人信息表")
    public void importOrgPic(@RequestParam("file") MultipartFile file) {
        userService.importOrgPic(file);
    }

    /**
     * 机构创新业务对接人分页查询
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "机构创新业务对接人分页查询")
    @GetMapping("/orgPic")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_ORG_PIC_SETTING, actionType = LogActionType.QUERY, descrption = "机构创新业务对接人分页查询")
    public PageInfo<OrgPicExtraVO> getOrgPicExtraByPage(OrgPicExtraQuery query) {
        return userService.getOrgPicExtraByPage(query);
    }

    /**
     * 新建机构创新业务对接人
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "新建机构创新业务对接人")
    @PostMapping("/orgPic")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_ORG_PIC_SETTING, actionType = LogActionType.ADD, descrption = "新建机构创新业务对接人")
    public void saveOrgPicExtra(@RequestBody OrgPicExtraDTO dto) {
        userService.insertOrgPicExtra(dto);
    }

    /**
     * 更新机构创新业务对接人
     *
     * @param id
     * @param dto
     * @return
     */
    @ApiOperation(value = "更新机构创新业务对接人")
    @PutMapping("/orgPic/{id}")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_ORG_PIC_SETTING, actionType = LogActionType.UPDATE, descrption = "更新机构创新业务对接人")
    public void updateOrgPicExtra(@PathVariable int id, @RequestBody OrgPicExtraDTO dto) {
        dto.setId(id);
        userService.updateOrgPicExtra(dto);
    }

    /**
     * 删除机构创新业务对接人
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除机构创新业务对接人")
    @DeleteMapping("/orgPic/{id}")
    @SystemLog(module = LogConstants.MODULE_BASE_USER_ORG_PIC_SETTING, actionType = LogActionType.DELETE, descrption = "删除机构创新业务对接人")
    public void deleteOrgPicExtra(@PathVariable int id) {
        userService.deleteOrgPicExtra(id);
    }
}
