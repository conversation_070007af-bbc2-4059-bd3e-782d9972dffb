package com.cfpamf.ms.insur.admin.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.external.tk.model.TkPolicyLose;
import com.cfpamf.ms.insur.admin.external.tk.model.TkReqWrapper;
import com.cfpamf.ms.insur.admin.external.tk.model.TkRespWrapper;
import com.cfpamf.ms.insur.admin.job.SmCustomerExtractHandler;
import com.cfpamf.ms.insur.admin.job.renewal.InsuranceRenewJobHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.ManualQueryOldCommissionDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.ManualOrderPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.ManualUpdatePolicyNoDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.service.RenewalManagerService;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupNotifyService;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupService;
import com.cfpamf.ms.insur.admin.service.commission.CommissionManagerService;
import com.cfpamf.ms.insur.admin.service.order.*;
import com.cfpamf.ms.insur.admin.service.order.group.GroupRuleHelper;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.LogActionType;
import com.cfpamf.ms.insur.base.constant.LogConstants;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * 第三方回调接口（勿动）
 *
 * <AUTHOR>
 */
@Api(value = "第三方回调接口（勿动）", tags = {"第三方回调接口（勿动）"})
@RequestMapping(BaseConstants.ADMIN_VERSION)
@RestController
@Slf4j
public class ManualThirdCallBackController {
    @Autowired
    SmOrderServiceWrapper wrapper;
    @Autowired
    private SmOrderGroupService smOrderGroupService;
    @Autowired
    private SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Autowired
    private ZaOrderService zaOrderService;

    @Autowired
    private RenewalManagerService renewalManagerService;
    @Autowired
    private FxOrderService fxOrderService;
    @Autowired
    private OrderCoreService orderCoreService;

    /**
     * 复兴保险支付回调接口
     */
    @ApiOperation(value = "手工处理复兴保险支付回调接口")
    @RequestMapping(value = "/order/fx/manualHandFxCallBack", method = {RequestMethod.POST, RequestMethod.GET})
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理复兴保险支付回调接口")
    public String manualHandFxCallBack(HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("手工处理复兴保险支付回调接口");
        wrapper.manualHandCallBack(EnumChannel.FX.getCode(), request, response);
        return null;

    }

    /**
     * 复兴保险支付回调接口
     */
    @ApiOperation(value = "手工处理众安回调")
    @GetMapping(value = "/order/tk/manualHandZaCallBack")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理众安回调")
    public String manualHandZaCallBack(@RequestParam("id") Integer id)  {
        log.info("手工处理众安回调");
        SmOrderGroupNotify notify = smOrderGroupNotifyService.getById(id);
        if (notify.getStatus() == 2) {
            return "只能处理未处理记录";
        }
        tkOrderService.manualDoV2(notify.getGroupPolicyNo());
        return "处理完成";

    }

    /**
     * 复兴保险支付回调接口
     */
    @ApiOperation(value = "手工处理众安续保保单回调")
    @GetMapping(value = "/order/za/manualHandRenewPolicy")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理众安续保保单回调")
    public String manualHandRenewPolicy(@RequestParam("userId") String userId, @RequestParam("policyNo") String policyNo,
                                        @RequestParam("password") String password) throws IOException {
        log.info("手工处理众安续保保单回调");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        zaOrderService.manualHandRenewPolicy(policyNo);
        return "处理完成";

    }

    /**
     * 手工处理绑卡回调
     */
    @ApiOperation(value = "手工处理绑卡回调")
    @GetMapping(value = "/order/za/manualHandBindCard")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理绑卡回调")
    public String manualHandBindCard(@RequestParam("userId") String userId, @RequestParam("policyNo") String policyNo,
                                     @RequestParam("password") String password) throws IOException {
        log.info("手工处理绑卡回调");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        zaOrderService.manualHandBindCard(policyNo);
        return "处理完成";

    }


    private void doExecute(SmOrderGroupNotify notify) {
        try {
            if (Objects.equals(GroupNotify.TypeEnum.ORDER.getCode(), notify.getType())) {
                smOrderGroupService.doGroupOrder(notify);
            } else {
                smOrderGroupService.doGroupEndorsement(notify);
            }
        } catch (Exception e) {
            log.warn("团险回调消息处理失败，policyNo={},enorsementNo={}", notify.getGroupPolicyNo(), notify.getEndorsementNo(), e);
        }
    }

    @Autowired
    private TkOrderService tkOrderService;

    /**
     * 手工处理泰康雇主批改回调
     */
    @ApiOperation(value = "手工处理泰康雇主批改回调")
    @GetMapping(value = "/order/tk/manualHandEmployerEndor")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理泰康雇主批改回调")
    public String manualHandEmployerEndor(@RequestParam("policyNo") String policyNo,
                                          @RequestParam("endorNo") String endorNo) throws IOException {
        tkOrderService.manualDoEndor(policyNo, endorNo);
        return "泰康雇主险补偿完成...";

    }

    @Autowired
    private GroupRuleHelper groupRuleHelper;

    @GetMapping(value = "/policy/correct/check")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER)
    public void policyCorrectCheck(@RequestParam("endorsementNo") String endorsementNo, @RequestParam("type") String type) {
        groupRuleHelper.setAddSubtractCheck(endorsementNo, type);
    }

    /**
     * 重新补偿泰康雇主责任险保单数据
     */
    @ApiOperation(value = "重新补偿泰康雇主责任险保单数据")
    @GetMapping(value = "/order/tk/retry")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理泰康雇主批改回调")
    public String tkEmployerRetry(@RequestParam("policyNo") String policyNo,
                                  @RequestParam(value = "endorsementNo",required = false)String endorsementNo,
                                  @RequestParam(value = "version",required = false) Integer version
    ) throws IOException {
        log.info("开始补偿泰康雇主责任险保单数据:{},{}",policyNo,endorsementNo);
        version= version == null? 2 : version;
        tkOrderService.retryApiFlow(policyNo,endorsementNo,version);
        return "处理完成";
    }



    /**
     * 手工处理泰康雇主批改回调
     */
    @ApiOperation(value = "手工处理泰康雇主新单回调")
    @GetMapping(value = "/order/tk/manualHandEmployerEndorV2")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理泰康雇主批改回调")
    public String manualHandEmployer(@RequestParam("policyNo") String policyNo) {
        log.info("手工处理泰康雇主新单回调");
        tkOrderService.manualDoV2(policyNo);
        return "处理完成";
    }

    @Autowired
    CommissionManagerService commissionManagerService;

    /**
     * 手工测试佣金计算
     */
    @ApiOperation(value = "手工测试佣金计算")
    @GetMapping(value = "/order/manualCalcCommission")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工测试佣金计算")
    public String manualCalcCommission(@RequestParam("userId") String userId,
                                       @RequestParam("fhOrderId") String fhOrderId,
                                       @RequestParam("password") String password) throws IOException {

        log.info("手工测试佣金计算");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        commissionManagerService.calcOrderCommission(fhOrderId, fhOrderId, Boolean.FALSE);
        return "处理完成";

    }

    /**
     * 手工测试续期佣金计算
     */
    @ApiOperation(value = "手工测试续期佣金计算")
    @GetMapping(value = "/order/manualCalcRenewalCommission")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工测试续期佣金计算")
    public String manualCalcRenewalCommission(@RequestParam("userId") String userId,
                                              @RequestParam("fhOrderId") String fhOrderId, @RequestParam("termNum") Integer termNum,
                                              @RequestParam("password") String password) throws IOException {

        log.info("手工测试续期佣金计算");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        if (termNum <= 1) {
            return "期数不正确";
        }
        commissionManagerService.calcOrderRenewCommission(fhOrderId + SmConstants.SEPARATOR_1 + termNum, fhOrderId, termNum);
        return "处理完成";

    }

    @Autowired
    private SmCustomerExtractHandler extractHandler;

    /**
     * 手工触发客户信息抽取
     */
    @ApiOperation(value = "手工触发客户信息抽取")
    @GetMapping(value = "/order/manualExtractCustomer")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工触发客户信息抽取")
    public String manualExtractCustomer(@RequestParam("userId") String userId,
                                        @RequestParam("fhOrderIds") String fhOrderIds,
                                        @RequestParam("password") String password) throws IOException {
        log.info("手工测试佣金计算");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        if (StringUtils.isBlank(fhOrderIds)) {
            return "";
        }
        extractHandler.extractCustomer(Arrays.asList(fhOrderIds.split(",")));
        return "处理完成";
    }

    @Autowired
    private XmOrderService xmOrderService;

    @ApiOperation(value = "信美回调测试")
    @GetMapping(value = "/order/testXmCallback")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    public String testXmCallback(@RequestParam("userId") String userId,
                                 @RequestParam("xmOrderNo") String xmOrderNo,
                                 @RequestParam("password") String password) throws IOException {
        log.info("手工测试佣金计算");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        xmOrderService.testCallback(xmOrderNo);
        return "处理完成";
    }

    /**
     * 手工更新佣金json串
     */
    @ApiOperation(value = "手工更新佣金json串")
    @GetMapping(value = "/order/manualUpdateCommissionJson")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工更新佣金json串")
    public String manualUpdateCommissionJson(@RequestParam("userId") String userId,
                                             @RequestParam("fhOrderId") String fhOrderId, @RequestParam("termNum") Integer termNum,
                                             @RequestParam("password") String password) throws IOException {

        log.info("手工测试佣金计算");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }

        commissionManagerService.manualUpdateCommissionJson(fhOrderId, termNum != null ? termNum : 1);

        //commissionManagerService.calcOrderCommission(fhOrderId);
        return "处理完成";

    }

    /**
     * 重新生成续保短链
     */
    @ApiOperation(value = "重新生成续保短链")
    @GetMapping(value = "/order/regenerateShortUrl")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "重新生成续保短链")
    public String regenerateShortUrl(@RequestParam("userId") String userId,
                                     @RequestParam("ids") String ids,
                                     @RequestParam("password") String password) throws IOException, InterruptedException {

        log.info("重新生成续保短链");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(userId, "CNBJ0409") || !Objects.equals(loginUserId, userId)) {
            return "无权限操作";
        }
        if (!Objects.equals(password, "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }
        if (StringUtils.isNotBlank(ids)) {
            String[] idArr = ids.split(",");
            for (String id : idArr) {
                renewalManagerService.regenerateShortUrl(Integer.valueOf(id), false);
                Thread.sleep(500L);
            }
        }


        return "处理完成";

    }


    /**
     * 手工同步佣金至新的佣金表
     */
    @ApiOperation(value = "手工同步佣金至新的佣金表")
    @PostMapping(value = "/order/manualCopyToNewCommission")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工同步佣金至新的佣金表")
    public String manualOldCommissionToNewCommission(@RequestBody ManualQueryOldCommissionDTO query) throws IOException {

        log.info("手工测试续期佣金计算");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(query.getUserId(), "CNBJ0409") || !Objects.equals(loginUserId, query.getUserId())) {
            return "无权限操作";
        }
        if (!Objects.equals(query.getPassword(), "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }

        commissionManagerService.manualOldCommissionToNewCommission(query);
        return "处理完成";

    }

    /**
     * 手工处理复星丢失交费期信息
     */
    @ApiOperation(value = "手工处理复星丢失交费期信息")
    @PostMapping(value = "/order/manualFxPayWay")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理复星丢失交费期信息")
    public String manualFxPayWay(@RequestBody ManualOrderPolicyDTO query) throws IOException {

        log.info("手工处理复星丢失交费期信息");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if (!Objects.equals(query.getUserId(), "CNBJ0409") || !Objects.equals(loginUserId, query.getUserId())) {
            return "无权限操作";
        }
        if (!Objects.equals(query.getPassword(), "CNBJ0409380508")) {
            return "用户名密码不匹配";
        }

        fxOrderService.manualFxPayWay(query.getFhOrderIds(), query.getChannel());
        return "处理完成";

    }

    /**
     * 手工处理保单信息
     */
    @ApiOperation(value = "手工处理保单信息")
    @PostMapping(value = "/order/manualUpdateP")
    @AuthValidate(value = ApiPermsEnum.PUBLIC)
    @SystemLog(module = LogConstants.MODULE_SM_ORDER, actionType = LogActionType.OTHER, descrption = "手工处理保单信息")
    public String manualUpdateP(@RequestBody ManualUpdatePolicyNoDTO dto) throws IOException {

        log.info("手工处理保单号变更信息");
        String loginUserId = HttpRequestUtil.getUserId();
        //todo 后续会维护一张手工处理问题的权限表
        if(!Objects.equals(dto.getUserId(),"CNBJ0409") || !Objects.equals(loginUserId,dto.getUserId())){
            return "无权限操作";
        }
        if(!Objects.equals(dto.getPassword(),"CNBJ0409380508")){
            return "用户名密码不匹配";
        }

        orderCoreService.batchUpdatePolicyNo(dto.getOrderType(),dto.getSourcePolicyNo(),dto.getToPolicyNo());
        return "处理完成";

    }


    @ApiOperation(value = "泰康退保数据补偿")
    @PostMapping(value = "/tk/refund")
    public TkRespWrapper handleTkRefund(@RequestBody TkReqWrapper<TkPolicyLose> data) {
        String loginUserId = HttpRequestUtil.getUserId();
        if (StringUtils.isBlank(loginUserId)) {
            throw new MSBizNormalException("-1","请登录系统再操作");
        }
        log.info("开始处理泰康退保流程");
        return tkOrderService.policyLose(data);
    }

    @Autowired
    InsuranceRenewJobHandler insuranceRenewJobHandler;
    @ApiOperation(value = "自动匹配转投保单")
    @PostMapping(value = "/renewal/insuranceRenew")
    public String InsuranceRenewJobHandler() {

        insuranceRenewJobHandler.autoMatcherTransferHandler();
        return "处理完成";
    }



}
