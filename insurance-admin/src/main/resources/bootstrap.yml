spring:
  application:
    name: insurance-admin
  cloud:
    config:
      enabled: false
      name: ${spring.application.name}
      failFast: true
      retry:
        initialInterval: 3000
        multiplier: 1.3
        maxInterval: 5000
        maxAttempts: 20
      uri: http://config-server.default/
  main:
    allow-bean-definition-overriding: true


project:
  name: ${spring.application.name}

ahas:
  namespace: prod
#  license: b2eef808232a4ad294f45e321c12ff1a
mapper:
  mappers:
    - com.cfpamf.ms.insur.base.dao.MyMappler

logging:
  level:
    ROOT: info
    com.cfpamf.ms.distribution.api: debug
    com.cfpamf.ms.insur.pay.facade.api: debug



nacos:
  config:
    bootstrap:
      enable: true
      log-enable: true
    type: yaml
    server-addr: @config_uri@ #连接地址，不同环境有不同的地址，建议采用变量名方式
    namespace: @namespace@ #配置文件对应的命名空间，填入ID
    group: safes #组名，默认是DEFAULT_GROUP，按需所建
    username: @username@ #连接nacos服务器的用户名
    password: @password@ #连接nacos服务器的密码
    data-ids: insurance,insurance-admin,application

