package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.tk.TkConsts;
import com.cfpamf.ms.insur.admin.external.tk.TkOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.tk.model.TkReqBox;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpEndorOrderReq;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpEndorStakeHolder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupNotifyService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class TkOrderServiceTest extends BaseTest {
    @InjectMocks
    TkOrderService tkOrderService;
    @Mock
    SmProductService productService;
    @Mock
    TkOrderServiceAdapter adapter;
    @Mock
    BusinessTokenService tokenService;
    @Mock
    SmOrderGroupNotifyService smOrderGroupNotifyService;
    @Mock
    ObjectMapper objectMapper;
    @Mock
    SmOrderMapper orderMapper;


    @Mock
    OrderCoreService orderCoreService;

    @Mock
    DictionaryService dictionaryService;



    @Test
    public void testGenTkEmployerProduct() {
        String url_1 = "http://test.cloud.tk.cn/tkpage/12N00068/#/";
        String url_2 = "http://test.cloud.tk.cn/tkpage/12N00068/#/?referralCode=CNBJ0409";
        String jobNumber = "CNBJ0409";
        SmProductDetailVO productDetailVO = new SmProductDetailVO();
        productDetailVO.setH5Url(url_1);
        Mockito.when(productService.getProductById(Mockito.anyInt())).thenReturn(productDetailVO);
        Mockito.when(adapter.genAgentEmployerUrl(productDetailVO.getH5Url(),jobNumber)).thenReturn(url_2);
        tkOrderService.genTkEmployerProduct(jobNumber,222,Boolean.FALSE);
    }


    @Test
    public void manualDoEndor() throws IOException {
        PowerMockito.when(smOrderGroupNotifyService.getOrderGroupNotifyByPolicyNo(Mockito.anyString(),Mockito.anyString())).thenReturn(this.createSmOrderGroupNotify());
        PowerMockito.when(smOrderGroupNotifyService.getNotifyMsg(Mockito.anyInt())).thenReturn(this.createSmOrderGroupNotifyMsg());
        PowerMockito.when(objectMapper.readValue(Mockito.anyString(), (TypeReference) Mockito.anyObject())).thenReturn(this.createReq());
        PowerMockito.when(orderMapper.listOrderInsuredByPolicyNo(Mockito.anyString())).thenReturn(this.createSmPolicyInsuredList());
        PowerMockito.when(orderMapper.getBaseOrderInfoByOrderId(Mockito.anyString())).thenReturn(this.createSmBaseOrderVO());
        PowerMockito.when(productService.getPlanById(Mockito.anyInt())).thenReturn(this.createSmPlanVO());
        PowerMockito.when(orderMapper.selectOrderApplicantByOrderId(Mockito.anyString())).thenReturn(this.createSmOrderApplicant());
        PowerMockito.when(adapter.cvtByEmployerEndorsementNotify(Mockito.anyString(),Mockito.any(TkEmpEndorOrderReq.class),Mockito.any(SmBaseOrderVO.class),Mockito.any(SmOrderApplicant.class),Mockito.any(SmPlanVO.class))).thenReturn(new ArrayList<>());


        PowerMockito.doNothing().when(orderCoreService).batchSaveOrderInfoGroup(Mockito.anyList());

        tkOrderService.manualDoEndor("H211112001303860105920","E211112002038910105920");
    }
    private SmOrderGroupNotify createSmOrderGroupNotify(){
        SmOrderGroupNotify notify = new SmOrderGroupNotify();
        notify.setStatus(1);
        return notify;
    }

    private List<SmPolicyInsured> createSmPolicyInsuredList(){
        List<SmPolicyInsured> lst = new ArrayList<>();
        SmPolicyInsured insured = new SmPolicyInsured();
        lst.add(insured);
        return lst;
    }

    private SmBaseOrderVO createSmBaseOrderVO(){
        SmBaseOrderVO vo = new SmBaseOrderVO();
        vo.setFhOrderId("TEST001");
        vo.setEndTime(DateUtil.getNow());
        vo.setStartTime(DateUtil.getNow());
        vo.setPlanId(533);
        return vo;
    }
    private SmOrderApplicant createSmOrderApplicant(){
        SmOrderApplicant applicant = new SmOrderApplicant();
        return applicant;
    }

    private SmOrderGroupNotifyMsg createSmOrderGroupNotifyMsg(){
        SmOrderGroupNotifyMsg notifyMsg = new SmOrderGroupNotifyMsg();
        return notifyMsg;
    }

    private SmPlanVO createSmPlanVO(){
        SmPlanVO vo = new SmPlanVO();
        return vo;
    }

    private TkReqBox<TkEmpEndorOrderReq> createReq(){
        TkReqBox<TkEmpEndorOrderReq> boxReq = new TkReqBox<TkEmpEndorOrderReq>();
        TkEmpEndorOrderReq req = new TkEmpEndorOrderReq();
        req.setEndorType(TkConsts.ENDOR_TYPE_INCREASE);
        req.setEndorDate(LocalDateTime.parse("2021-11-12T17:27:46"));
        req.setEndorNo("E211112002038910105920");
        req.setPolicyNo("H211112001303860105920");
        req.setUnderWriteEndDate(LocalDateTime.parse("2021-11-12T17:34:50"));
        List<TkEmpEndorStakeHolder> stakeholderList = new ArrayList<>();
        req.setStakeholderList(stakeholderList);
        TkEmpEndorStakeHolder holder = new TkEmpEndorStakeHolder();
        holder.setChangeGrossPremium("186.000000");
        holder.setCredentialNo("11010019950202788X");
        holder.setCredentialType("01");
        holder.setName("啊啊啊");
        holder.setOccupationLevel("1");
        stakeholderList.add(holder);

        holder = new TkEmpEndorStakeHolder();
        holder.setChangeGrossPremium("584.000000");
        holder.setCredentialNo("11010019950202789X");
        holder.setCredentialType("01");
        holder.setName("啊啊啊");
        holder.setOccupationLevel("1");
        stakeholderList.add(holder);

        boxReq.setRequestData(req);
        boxReq.setRequestId("********************************");
        boxReq.setRequestTime("2021-11-12 17:38:02");
        return boxReq;
    }


}
