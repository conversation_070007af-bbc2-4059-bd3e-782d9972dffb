package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderShareMapper;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderShare;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR> 2021/5/25 14:53
 */
/*@RunWith(SpringJUnit4ClassRunner.class)*/
public class SmOrderShareServiceTest {
    /*@InjectMocks
    SmOrderShareService service;

    @Mock
    SmOrderShareMapper shareMapper;

    @Test
    public void getShareByOrderId() {
        service.getShareByOrderId("!234");
    }

    @Test
    public void saveShare() {
        service.saveShare(JMockData.mock(SmOrderShare.class));
    }*/
}
