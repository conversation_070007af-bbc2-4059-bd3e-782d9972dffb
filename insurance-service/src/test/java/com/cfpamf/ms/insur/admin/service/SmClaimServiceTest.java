package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.config.ClaimEmailProperties;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCmpySettingMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimEmailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementZaMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.form.claim.ChannelEmail;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimEmail;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmClaimVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimEmailTemplateVo;
import com.cfpamf.ms.insur.admin.service.claim.ClaimRiskReasonService;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.base.util.email.JavaMailHelper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jsonzou.jmockdata.JMockData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.SimpleAsyncTaskExecutor;
import org.springframework.mock.web.MockHttpServletResponse;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;


@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AliYunOssUtil.class})
public class SmClaimServiceTest extends BaseTest {

    @InjectMocks
    private SmClaimServiceImpl claimService;

    /**
     * 发送邮件的账户
     */
    @Value("${spring.mail.username}")
    private String mainFrom;

    /**
     * 理赔工作流
     */
    @Mock
    private ClaimWorkflow workflow;

    /**
     * 小额保险理赔mapper
     */
    @Mock
    private SmClaimMapper mapper;

    /**
     * 保险公司配置mapper
     */
    @Mock
    private SmCmpySettingMapper cmpySettingMapper;

    /**
     * 小额保险订单mapper
     */
    @Mock
    private SmOrderMapper orderMapper;

    /**
     * bms配置
     */
    @Mock
    private BmsConfig bmsConfig;

    /**
     * Redis缓存
     */
    @Mock
    private RedisUtil<String, Integer> redisUtil;


    /**
     * bms服务
     */
    @Mock
    private BmsService bmsService;

    /**
     * 邮件发送帮助类
     */
    @Mock
    private JavaMailHelper javaMailHelper;

    /**
     * 渠道理赔邮箱配置
     */
    @Mock
    private ClaimEmailProperties claimEmailProperties;

    /**
     * 理赔邮件数据层接口
     */
    @Mock
    private SmClaimEmailMapper smClaimEmailMapper;

    @Mock
    private OSS OSS;

    @Mock
    ObjectMapper objectMapper;
    @Mock
    ClaimRiskReasonService claimRiskReasonService;

    @Mock
    ClaimServiceConfig claimServiceConfig;

    @Mock
    SmClaimReimbursementZaMapper claimReimbursementZaMapper;
    @Test
    public void testSendMail() {
        String json = "{\n" +
                "  \"claimEmailForm\": {\n" +
                "    \"claimFileIdList\": [\n" +
                "      0\n" +
                "    ],\n" +
                "    \"content\": \"string\",\n" +
                "    \"theme\": \"string\",\n" +
                "    \"allEnclosure\":1,\n" +
                "    \"autoSend\":1\n" +
                "  },\n" +
                "  \"claimId\": 14060\n" +
                "}";
        ChannelEmail channelEmail = new ChannelEmail();
        channelEmail.setCode("test");
        channelEmail.setName("test");
        channelEmail.setEmail("<EMAIL>");
        channelEmail.setCompanyId(1);
        SmClaimVO claimVO = new SmClaimVO();
        claimVO.setChannel("test");
        claimVO.setCompanyId(111);
        Mockito.when(mapper.listSmClaims(Mockito.any())).thenReturn(Lists.newArrayList(claimVO));
        Mockito.when(claimEmailProperties.getChannel()).thenReturn(Lists.newArrayList(channelEmail));
        try {
            claimService.sendClaimEmail(JSON.parseObject(json, ProgressDTO.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void testGetAttachmentMap() {
        SmClaimEmail mock = Mockito.mock(SmClaimEmail.class);
        try {
            claimService.getAttachmentMap(1, mock);
        } catch (Exception e) {

        }
    }

    @Test
    public void testGetClaimEmailTemplateVo() {
        SmClaimVO smClaimVO = new SmClaimVO();
        smClaimVO.setId(1);
        smClaimVO.setApplicantPersonName("1");
        smClaimVO.setAplicantIdTypeName("1");
        smClaimVO.setAplicantIdNumber("1");
        smClaimVO.setCompanyId(1);
        smClaimVO.setApplicantPersonGender("1");
        smClaimVO.setApplicantCellPhone("1");
        smClaimVO.setInsuredPersonName("1");
        smClaimVO.setInsuredCellPhone("1");
        smClaimVO.setInsuredIdNumber("1");
        smClaimVO.setApplicantEmail("1");
        smClaimVO.setInsuredIdTypeName("1");
        smClaimVO.setInsuredPersonGender("1");
        smClaimVO.setInsuredEmail("1");
        smClaimVO.setChannel("za");

        HashMap<String, Object> map = Maps.newHashMap();
        map.put("applicantCellPhone", "123");
        map.put("insuredIdNumber", "123");
        map.put("insuredPersonName", "123");
        Mockito.when(mapper.listSmClaims(Mockito.any())).thenReturn(Lists.newArrayList(smClaimVO));
        Mockito.when(objectMapper.convertValue(smClaimVO, Map.class)).thenReturn(map);
        ChannelEmail channelEmail = new ChannelEmail();
        channelEmail.setCode("za");
        channelEmail.setName("za");
        channelEmail.setEmail("<EMAIL>");
        channelEmail.setCompanyId(1);
        channelEmail.setThemeTemplate("'{insuredPersonName}+{insuredIdNumber}+保险理赔资料+{date}+{applicantCellPhone}'");
        channelEmail.setContentTemplate("'{insuredPersonName}+{insuredIdNumber}'");
        Mockito.when(claimEmailProperties.getChannel()).thenReturn(Lists.newArrayList(channelEmail));

        ClaimEmailTemplateVo claimEmailTemplateVo = claimService.getClaimEmailTemplateVo(1);
        Assert.assertEquals(claimEmailTemplateVo.getContent(), "'123+123'");
        Assert.assertEquals(claimEmailTemplateVo.getTheme(), "'123+123+保险理赔资料+" + DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDate.now()) + "+123'");
    }

    @Test
    public void testDownloadPartClaimFiles() {
        MockHttpServletResponse response = new MockHttpServletResponse();
        SmOrderInsuredVO smOrderInsuredVO = new SmOrderInsuredVO();
        smOrderInsuredVO.setFhOrderId("");
        smOrderInsuredVO.setChannel("");
        smOrderInsuredVO.setOccupationName("");
        smOrderInsuredVO.setRegionName("");
        smOrderInsuredVO.setRegionCode("");
        smOrderInsuredVO.setAreaName("");
        smOrderInsuredVO.setCompanyName("");
        smOrderInsuredVO.setInsuredCellPhone("");
        smOrderInsuredVO.setAgentName("");
        smOrderInsuredVO.setAgentMobile("");
        smOrderInsuredVO.setPlanFactorPriceOptionJson("");
        smOrderInsuredVO.setIsSecurity("");
        smOrderInsuredVO.setId(0);
        smOrderInsuredVO.setRelationship("");
        smOrderInsuredVO.setIsSecurity("");
        smOrderInsuredVO.setFlightNo("");
        smOrderInsuredVO.setFlightTime("");
        smOrderInsuredVO.setOccupationCode("");
        smOrderInsuredVO.setDestinationCountryText("");
        smOrderInsuredVO.setAnnualIncome("");
        smOrderInsuredVO.setStudentType("");
        smOrderInsuredVO.setSchoolType("");
        smOrderInsuredVO.setSchoolNature("");
        smOrderInsuredVO.setSchoolName("");
        smOrderInsuredVO.setSchoolClass("");
        smOrderInsuredVO.setStudentId("");
        smOrderInsuredVO.setHdrType("");
        smOrderInsuredVO.setIdPeriodStart("");
        smOrderInsuredVO.setIdPeriodEnd("");
        smOrderInsuredVO.setSurrenderTime(new Date());
        smOrderInsuredVO.setSmoke("");
        smOrderInsuredVO.setQuestionnaireId("");
        smOrderInsuredVO.setOldPolicyNo("");
        smOrderInsuredVO.setDuties(Lists.newArrayList());
        smOrderInsuredVO.setPersonName("");
        smOrderInsuredVO.setPersonGender("");
        smOrderInsuredVO.setIdType("");
        smOrderInsuredVO.setIdNumber("");
        smOrderInsuredVO.setBirthday("");
        smOrderInsuredVO.setCellPhone("");
        smOrderInsuredVO.setEmail("");
        smOrderInsuredVO.setArea("");
        smOrderInsuredVO.setAddress("");
        smOrderInsuredVO.setAreaName("");
        smOrderInsuredVO.setAddressProvider(0);
        smOrderInsuredVO.setAppStatus("");
        smOrderInsuredVO.setPolicyNo("");
        smOrderInsuredVO.setDownloadURL("");
        smOrderInsuredVO.setIdPeriodStart("");
        smOrderInsuredVO.setIdPeriodEnd("");
        smOrderInsuredVO.setExtendInfoList(Lists.newArrayList());
        smOrderInsuredVO.setBirthday("2021-01-02");
        Mockito.when(orderMapper.getOrderInsuredByInsId(Mockito.anyInt())).thenReturn(smOrderInsuredVO);
        claimServiceConfig.downloadPartClaimFiles(Lists.newArrayList(1), 1, response);
    }


    @Test
    public void testDownloadClaims() {
        MockHttpServletResponse response = new MockHttpServletResponse();
        claimService.downloadClaims(JMockData.mock(SmClaimQuery.class),  response);
    }

    @Test
    public void testGetSmClaimByPage() {
        setUp();
        claimService.getSmClaimByPage(JMockData.mock(SmClaimQuery.class));
    }


    @Override
    public void setUp() {
        try{
            super.setUp();
        } catch(Exception e) {
            e.printStackTrace();
        }
        UserDetailVO.UserRoleVO userRoleVO = JMockData.mock(UserDetailVO.UserRoleVO.class);
        userRoleVO.setRoleCode("R1000");
        userRoleVO.setRoleName("保险|管理员");
        userRoleVO.setUserRoleType(1);
        List<UserDetailVO.UserRoleVO> roleVOList = new ArrayList<>();
        roleVOList.add(userRoleVO);

        UserDetailVO userDetailVO = JMockData.mock(UserDetailVO.class);
        userDetailVO.setRoleList(roleVOList);
        ThreadUserUtil.USER_DETAIL_TL.set(userDetailVO);

        Mockito.when(bmsConfig.getCustomerSensitiveClaimList()).thenReturn("CUSTOMER_SENSITIVE_CLAIM_LIST");

        List<SmClaimVO> claimVOList = new ArrayList<>();
        SmClaimVO claimVO1 = JMockData.mock(SmClaimVO.class);
        claimVO1.setApplicantEmail("<EMAIL>");
        claimVO1.setApplicantCellPhone("15700989084");
        claimVO1.setInsuredCellPhone("15700989084");
        claimVO1.setInsuredEmail("<EMAIL>");
        claimVO1.setRecommendUserMobile("15700989084");
        claimVOList.add(claimVO1);
        Mockito.when(mapper.listSmClaims(Mockito.any())).thenReturn(claimVOList);
        Mockito.when(SpringFactoryUtil.getBean("regex-Executor", AsyncTaskExecutor.class)).thenReturn(new SimpleAsyncTaskExecutor());
    }
}