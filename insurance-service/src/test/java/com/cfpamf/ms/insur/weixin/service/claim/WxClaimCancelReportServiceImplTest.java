package com.cfpamf.ms.insur.weixin.service.claim;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportProgressMapper;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCancelReport;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmClaimServiceImpl;
import com.cfpamf.ms.insur.admin.service.SmOrderRenewBindService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.claim.ClaimCancelReportWorkflow;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.form.claim.ClaimCancelReportForm;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.service.WxCcClaimService;
import com.cfpamf.ms.insur.weixin.service.claim.impl.WxClaimCancelReportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/1/27 9:37
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, RedisUtil.class})
@Slf4j
public class WxClaimCancelReportServiceImplTest extends BaseTest {
    @Mock
    SmClaimMapper claimMapper;
    @Mock
    SmClaimCancelReportMapper claimCancelReportMapper;
    @Mock
    SmClaimCancelReportProgressMapper claimCancelReportProgressMapper;
    @Mock
    private UserService userService;
    @Mock
    ClaimCancelReportWorkflow claimCancelReportWorkflow;
    @Mock
    PermissionUtil permissionUtil;
    @Mock
    WxCcClaimService wxCcClaimService;
    @Mock
    protected RedisUtil<String, String> redisUtil;


    @Mock
    private BmsService bmsService;
    @Mock
    protected SmOrderRenewBindService smOrderRenewBindService;
    /**
     * 理赔service
     */
    @Mock
    private SmClaimServiceImpl claimService;
    @InjectMocks
    WxClaimCancelReportServiceImpl wxClaimCancelReportService;

    @Test
    public void testNoAuth() {
        mockWxSession();
        Mockito.when(permissionUtil.isClaimAdmin(Mockito.any())).thenReturn(false);
        String claimNo = "LP001";
        ClaimCancelReportForm claimCancelReportForm = Mockito.mock(ClaimCancelReportForm.class);
        boolean result = false;
        try {
            wxClaimCancelReportService.apply(claimNo, claimCancelReportForm);
        } catch (Exception e) {
            result = Objects.equals(e.getMessage(), "暂无当前理赔流程权限。");
        }
        Assert.assertTrue(result);
    }

    @Test
    public void testNoAuth02() {
        mockWxSession();
        Mockito.when(permissionUtil.isClaimAdmin(Mockito.any())).thenReturn(true);
        String claimNo = "LP001";
        ClaimCancelReportForm claimCancelReportForm = Mockito.mock(ClaimCancelReportForm.class);
        boolean result = false;
        try {
            wxClaimCancelReportService.apply(claimNo, claimCancelReportForm);
        } catch (Exception e) {
            result = Objects.equals(e.getMessage(), "当前理赔流程有未完成的注销理赔报案申请。");
        }
        Assert.assertTrue(result);
    }

    @Test
    public void testNoAuth03() {
        String claimNo = "LP001";
        ClaimCancelReportForm claimCancelReportForm = Mockito.mock(ClaimCancelReportForm.class);

        mockWxSession();
        Mockito.when(permissionUtil.isClaimAdmin(Mockito.any())).thenReturn(true);
        SmClaimCancelReport smClaimCancelReport = new SmClaimCancelReport();
        Mockito.when(claimCancelReportMapper.getByClaimNo(Mockito.anyString())).thenReturn(Lists.newArrayList());

        boolean result = false;
        try {
            wxClaimCancelReportService.apply(claimNo, claimCancelReportForm);
        } catch (Exception e) {
            log.info("", e);
            result = Objects.equals(e.getMessage(), "当前理赔流程状态已不支持理赔注销报案申请");
        }
        Assert.assertTrue(result);
    }

    @Test
    public void testNoAuth04() {
        String claimNo = "LP001";
        ClaimCancelReportForm claimCancelReportForm = Mockito.mock(ClaimCancelReportForm.class);

        mockWxSession();
        Mockito.when(permissionUtil.isClaimAdmin(Mockito.any())).thenReturn(true);
        SmClaimCancelReport smClaimCancelReport = new SmClaimCancelReport();
        Mockito.when(claimCancelReportMapper.getByClaimNo(Mockito.anyString())).thenReturn(Lists.newArrayList());
        WxClaimListVo claimOrder = new WxClaimListVo();
        claimOrder.setId(1);
        claimOrder.setInsuredId(0);
        claimOrder.setClaimNo("");
        claimOrder.setProductName("");
        claimOrder.setPlanName("");
        claimOrder.setInsuredPersonName("");
        claimOrder.setPolicyNo("");
        claimOrder.setRiskTime(new Date());
        claimOrder.setReportTime(new Date());
        claimOrder.setUpdateTime(new Date());
        claimOrder.setClaimState("stepDataPrepare");
        claimOrder.setClaimResult("");
        claimOrder.setExpireDays(0);
        claimOrder.setLastFollowUp("");
        claimOrder.setApptPersonName("");
        claimOrder.setCustomerAdminName("");
        claimOrder.setCustomerAdminId("");
        claimOrder.setCustomerAdminMobile("");
        Mockito.when(claimMapper.getClaimOrder(claimNo)).thenReturn(claimOrder);
        wxClaimCancelReportService.apply(claimNo, claimCancelReportForm);


    }

    private void mockWxSession() {
        WxSessionVO wxSessionVO = new WxSessionVO();
        String openId = "oK6-1wF85NO0cFxRkFisD1avtDEI";
        String authorization = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI2OTQiLCJlbXBsb3llZUlkIjoiNTE4MyIsImhyVXNlcklkIjoiMTE4ODA5OTY5IiwiYWNjb3VudCI6IjE1MTk3MDE4OTc2IiwidXNlck5hbWUiOiLkvZXnu6rmhI8iLCJqb2JOdW1iZXIiOiJaSE5YMDgwMTgiLCJvcmdJZCI6IjI0MyIsImhyT3JnSWQiOiIzMDkzMTciLCJock9yZ0NvZGUiOiJITlBKIiwiaHJPcmdOYW1lIjoi5bmz5rGfIiwiaHJPcmdUcmVlUGF0aCI6IjkwMDEwNTE1My8yODI3MzIvMTQ3MTYxMS8yODQ1MTcvMTQ0NDU3NS8zMDkzMTciLCJ1c2VyVHlwZSI6IjIiLCJyYW5kb21Db2RlIjoiMjhCMkMzODYtNUVGQi00MzY2LUEzRjEtQzJGODY5NEVGMUVDIn0.UNINiduyZvZZReqoOK073RwwSfYsGDIssZ-NWhU9bV9my4mFa8wCccXlwGKkP_VRe1Lz51MfE5UsfFNIdTkeDA";
        wxSessionVO.setAuthorization(authorization);
        wxSessionVO.setWxOpenId(openId);
        Mockito.when(redisUtil.get(openId)).thenReturn(JSON.toJSONString(wxSessionVO));
    }
}
