package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationVO;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.TimeFactorMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OfflinePayDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.BankVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEndorResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaGroupEndorRevoke;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.PolicyVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.ProductDetailVo;
import com.cfpamf.ms.insur.weixin.service.config.ApplyConfig;
import com.cfpamf.ms.insur.weixin.service.policy.PolicyService;
import com.cfpamf.ms.insur.weixin.service.policy.ServiceConfig;
import com.cfpamf.ms.insur.weixin.zaGroup.ZaMain;
import com.github.jsonzou.jmockdata.JMockData;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class PolicyServiceTest extends BaseTest {

    @Mock
    private PolicyMapper policyMapper;

    @Mock
    protected AuthUserMapper userMapper;

    @Mock
    SmProductVersionMapper productVersionMapper;

    @Mock
    private SmCommissionMapper commissionMapper;

    @Mock
    private SmOrderMapper orderMapper;

    @Mock
    private SmOccupationMapper mapper;

    @Mock
    private RedisUtil redisUtil;

    @Mock
    private TimeFactorMapper timeFactorMapper;

    @InjectMocks
    private PolicyService policyService;

    @InjectMocks
    private ZaOrderServiceAdapter zaOrderServiceAdapter;

    @Before
    public void before()throws Exception {
        super.setUp();

    }

    @Test
    public void policyInfo() {
         String orderId = JMockData.mock(String.class);
         String policyNo = JMockData.mock(String.class);
         String endorNo = JMockData.mock(String.class);
         PolicyVo policy = policyService.policyInfo(orderId,policyNo,endorNo);
         System.err.println(policy);
        Assert.assertTrue(true);
    }

    @Test
    public void banklist() {
        List<BankVo> banks = ApplyConfig.getBanks();
        System.err.println(banks);
        Assert.assertTrue(true);
    }

    @Test
    public void getJobList() {
        String orderId = JMockData.mock(String.class);
        String policyNo = JMockData.mock(String.class);
        List<String> ocps = policyMapper.queryJobList(orderId,policyNo);

        Integer companyId =orderMapper.queryCompanyIdByOrder(orderId);
        List<SmOccupationVO> occupationVos = mapper.listOccupations(companyId, null, ocps, true);

        List<WxTreeVO> wxTreeVos = occupationVos.stream().map(this::mapperToWxTreeVo)
                .sorted(Comparator.comparing(WxTreeVO::getValue))
                .collect(Collectors.toList());
        // 职业第二级类别编码
        List<String> categoryCodes1 = occupationVos.stream()
                .filter(g -> !com.alibaba.druid.util.StringUtils.isEmpty(g.getOccupationGroup()))
                .map(SmOccupationVO::getParentCode)
                .collect(Collectors.toList());
        // 职业第一级类别编码
        List<String> categoryCodes2 = occupationVos.stream()
                .filter(o -> categoryCodes1.stream().anyMatch(j -> Objects.equals(o.getCategoryCode(), j)))
                .map(SmOccupationVO::getParentCode)
                .collect(Collectors.toList());
        //
        List<WxTreeVO> data =  wxTreeVos.stream()
                .filter(o -> categoryCodes2
                        .stream()
                        .anyMatch(c -> Objects.equals(c, o.getValue()))
                        || categoryCodes1
                        .stream()
                        .anyMatch(c -> Objects.equals(c, o.getValue()))
                        || categoryCodes1
                        .stream()
                        .anyMatch(c -> Objects.equals(c, o.getParent())))
                .collect(Collectors.toList());
        System.err.println(JSON.toJSONString(data));
        Assert.assertTrue(true);
    }

    private WxTreeVO mapperToWxTreeVo(SmOccupationVO o) {
        WxTreeVO wtv = new WxTreeVO();
        wtv.setParent(o.getParentCode());
        if (com.alibaba.druid.util.StringUtils.isEmpty(o.getOccupationName())) {
            wtv.setName(o.getCategoryName());
            wtv.setValue(o.getCategoryCode());
            wtv.setType(o.getOccupationGroup());
        } else {
            wtv.setName(o.getOccupationName());
            wtv.setValue(o.getOccupationCode());
            wtv.setType(o.getOccupationGroup());
        }
        wtv.setParent(o.getParentCode());
        return wtv;
    }

    public void underwriter(){
        String revokeFile = "/zhongan/revoke.json";
        String json = ZaMain.readFile(revokeFile);
        ZaGroupEndorRevoke revokeReq = JSON.parseObject(json,ZaGroupEndorRevoke.class);
        ZhongAnApiClient client = ZaMain.mockClient();
        String api = "zhongan.health.group.endorsement.revoke";
        String result = ZaMain.call(client,api,revokeReq);
        System.err.println(result);
        Assert.assertTrue(true);
    }


    public void endorRevoke(){
        String revokeFile = "/zhongan/revoke.json";
        String json = ZaMain.readFile(revokeFile);
        ZaGroupEndorRevoke revokeReq = JSON.parseObject(json,ZaGroupEndorRevoke.class);
        ZhongAnApiClient client = ZaMain.mockClient();
        String api = "zhongan.health.group.endorsement.revoke";
        String result = ZaMain.call(client,api,revokeReq);
        System.err.println(result);
        Assert.assertTrue(true);
    }

    @Test
    public void endorSupply(){
        Endor endor = JMockData.mock(Endor.class);
        endor.setStatus(0);
        endor.setAmount(new BigDecimal(-1.0));
        GroupEndorResponse resp = policyService.endorSupply(EnumChannel.ZA.getCode(),endor);
        Assert.assertTrue(resp.getEndorStatus()==1);
    }

    @Test
    public void endorSupply2(){
        Endor endor = JMockData.mock(Endor.class);
        endor.setStatus(0);
        endor.setAmount(new BigDecimal(1.0));
        GroupEndorResponse resp = policyService.endorSupply(EnumChannel.ZA.getCode(),endor);
        System.err.println(JSON.toJSONString(resp));
        Assert.assertTrue(resp.getEndorStatus()==0);
    }

    public void queryRawPolicy(){
        Mockito.when(policyService.buildQuery()).thenReturn(Mockito.mock(OrderDetailQuery.class));
        Mockito.when(policyService.getSession()).thenReturn(Mockito.mock(WxSessionVO.class));
        Mockito.when(policyService.getWxSession(Mockito.any(String.class))).thenReturn(Mockito.mock(WxSessionVO.class));
        String order = "9001";
        String policyNo = "2001";
        PolicyVo vo=policyService.queryRawPolicy(order,policyNo);
        System.err.println(vo);
    }

    @Test
    public void queryProductInfo(){
        Integer productId = Mockito.anyInt();
        String region = JMockData.mock(String.class);
        ProductDetailVo vo=policyService.queryProductInfo(productId,region);
        System.err.println(vo);
        Assert.assertTrue(vo!=null);
    }


    @Test
    public void fixPeriod2ShortTerm( ) {
         String period = "12月";
         String data = policyService.fixPeriod2ShortTerm(period);
         System.out.println(data);
         Assert.assertEquals("1~12月",data);
    }

    @Test
    public void fixPeriod2ShortTerm2( ) {
        String period = "12232";
        String data = policyService.fixPeriod2ShortTerm(period);
        System.out.println(data);
        Assert.assertEquals(period,data);
    }

    @Test
    public void submitOfflinePay() {
        String channel = EnumChannel.ZA.getCode();
        OfflinePayDTO data = JMockData.mock(OfflinePayDTO.class);
        ChannelOrderService serice = ServiceConfig.getService(channel);
        serice.submitOfflinePay(data);
        Assert.assertTrue(Boolean.TRUE);
    }
}
