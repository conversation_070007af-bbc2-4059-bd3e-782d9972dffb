package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import tk.mybatis.mapper.MapperException;

import java.io.IOException;

/**
 * <AUTHOR> 2022/3/21 13:43
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AopContext.class})
public class WhaleOrderServiceTest extends BaseTest {
    @InjectMocks
    WhaleOrderService whaleOrderService;
    @org.mockito.Mock
    WhaleOrderServiceAdapterImpl adapter;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmXjxhService xjxhService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.order.SmOrderPolicyService policyService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendWhaleMapper whaleMapper;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper riskMapper;

    @Mock
    SmProductService productService;

    @Mock
    SmOrderMapper orderMapper;

    @Mock
    EventBusEngine eventBusEngine;

    @Mock
    SmOrderManageService manageService;

    @Override
    @Before
    public void setUp() throws Exception {
        super.setUp();
        PowerMockito.mockStatic(AopContext.class);
        PowerMockito.when(AopContext.currentProxy()).thenReturn(whaleOrderService);
    }


    @Test
    public void getH5JumpUrl() {
        try {
            whaleOrderService.getH5JumpUrl(6362, ",XPMaf,", ",VGisy,", null);
        } catch (Exception e) {

        }
    }

    @Test
    public void support() {
        try {
            whaleOrderService.support(",YjPlH,");
        } catch (Exception e) {

        }
    }

    @Test
    public void updateOrderPolicyInfo() {
        try {
            whaleOrderService.updateOrderPolicyInfo(",vlpqZ,");
        } catch (Exception e) {

        }
    }

    @Test
    public void submitOrder() {
        try {
            whaleOrderService.submitOrder(",EOEBh,", JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderSubmitRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void handAcceptSuccess() throws IOException {
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        mockHttpServletRequest.setParameter("contractCode", "contractCode");
        mockHttpServletRequest.setParameter("policyOperation", "CREATE");
        try {
            whaleOrderService.handAcceptSuccess(mockHttpServletRequest, new MockHttpServletResponse());
        } catch (MapperException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void handAcceptSuccessErr() throws IOException {
        MockHttpServletRequest mockHttpServletRequest = new MockHttpServletRequest();
        try {
            whaleOrderService.handAcceptSuccess(mockHttpServletRequest, new MockHttpServletResponse());
        } catch (MSBizNormalException e) {
            Assert.assertEquals(ExcptEnum.PARAMS_ERROR.getCode(), e.getErrorCode());

        }

    }

    @Test
    public void transactional() {
        try {
            whaleOrderService.transactional(JMockData.mock(java.util.function.Supplier.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void saveOrderInfoAppStatus() {
    }

    @Test
    public void cvtOrderPolicy() {
        WhaleContract mock = JMockData.mock(WhaleContract.class);
        mock.getProductInfoList().get(0).setMainInsurance(1);
        try {
            whaleOrderService.cvtOrderPolicy(mock, JMockData.mock(com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendWhale.class));
        } catch (MSException e) {
            e.printStackTrace();
        }
    }
}
