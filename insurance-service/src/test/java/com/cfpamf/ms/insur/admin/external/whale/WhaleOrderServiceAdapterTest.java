package com.cfpamf.ms.insur.admin.external.whale;

import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR> 2022/3/21 14:04
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class WhaleOrderServiceAdapterTest extends BaseTest {
    @InjectMocks
    WhaleOrderServiceAdapterImpl whaleOrderServiceAdapter;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.OrderNoGenerator orderNoGenerator;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmXjxhService whaleService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.external.whale.api.WhaleApiService apiService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.service.SmProductService productService;
    @org.mockito.Mock
    com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper riskMapper;

    @Test
    public void planCode() {
        try {
            whaleOrderServiceAdapter.planCode(JMockData.mock(com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void genDynamicUrl() {
        try {
            whaleOrderServiceAdapter.genDynamicUrl(",XiLbk,", ",uUViE,", ",sFBUk,",null);
        } catch (Exception e) {

        }
    }

    @Test
    public void submitChannelOrder() {
        try {
            whaleOrderServiceAdapter.submitChannelOrder(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderSubmitRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void prePayChannelOrder() {
        try {
            whaleOrderServiceAdapter.prePayChannelOrder(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderPrePayRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void queryChannelOrderInfo() {
        try {
            whaleOrderServiceAdapter.queryChannelOrderInfo(JMockData.mock(com.cfpamf.ms.insur.admin.external.OrderQueryRequest.class));
        } catch (Exception e) {

        }
    }

    @Test
    public void getOrderPayedCallbackResp() {
        try {
            whaleOrderServiceAdapter.getOrderPayedCallbackResp(",tIvCF,");
        } catch (Exception e) {

        }
    }

    @Test
    public void getPolicyNo() {
        try {
            whaleOrderServiceAdapter.getPolicyNo(",ASPDC,", ",HGxpp,");
        } catch (Exception e) {

        }
    }
}
