package com.cfpamf.ms.insur.admin.service.product;

import com.beust.jcommander.internal.Maps;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.AmConstants;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPaymentMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.common.model.CompanyPolicyInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.service.OccupationService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.order.ZaOrderService;
import com.cfpamf.ms.insur.base.controller.ApplicationTest;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.InsurPayService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupQuoteResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupUnderwriting;
import com.cfpamf.ms.pay.facade.api.InsurancePayFacade;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import com.cfpamf.ms.pay.facade.constant.PayStatusEnum;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaEPolicyReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.ZaEPolicyResp;
import com.cfpamf.ms.insur.weixin.service.policy.PolicyService;
import com.github.jsonzou.jmockdata.JMockData;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class})
public class ZaOrderServiceAdapterTest extends ApplicationTest {
    @InjectMocks
    private PolicyService policyService;

    @InjectMocks
    private ZaOrderServiceAdapter adapter;

    @InjectMocks
    InsurPayService payService;

    @Mock
    private SmProductService productService;

    @Mock
    private OccupationService occupationService;

    @Mock
    private PayFacadeConfigProperties payConfigProperties;

    @Mock
    private InsurancePayFacade insurPayFacade;

    @Mock
    SmOrderPaymentMapper orderPaymentMapper;

    @Mock
    SmOrderMapper orderMapper;

    @Mock
    ZaApiService apiService;

    @Test
    public void queryEPolicyUrl(){
        String policyNo = "HA1100001435005305";
        ZaEPolicyReq req = new ZaEPolicyReq();
        ZaApiProperties prop = apiService.getZaApiProperties();
        req.setChannelCode(prop.getGroupChannelCode());
        req.setPolicyNo(policyNo);
        req.setEndorsementNo(null);
        ZaEPolicyResp resp = apiService.queryEPolicyUrl(req);
        if(resp!=null){
            System.err.println(resp.getEPolicyURL());
        }
        Assert.assertTrue("Some Error",true);
    }

    public ChannelOrderService getExternalThirdPartyOrderService(String channel) {
        return adapter;
    }

    protected OrderQueryResponse getExternalThirdPartyOrderInfoByOrderId(String orderId) {
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        OrderQueryRequest queryDTO = new OrderQueryRequest();
        queryDTO.setOrderId(orderId);
        queryDTO.setPayId(baseOrderVO.getPayId());
        queryDTO.setAppNo(baseOrderVO.getAppNo());
        queryDTO.setSubmitTime(baseOrderVO.getSubmitTime());
        return  queryChannelOrderInfo(queryDTO);
    }

    protected void adjustExpireOrderResp(String orderId, OrderQueryResponse resp) {
        // 待支付状态、已过期状态定义
        if (resp.getOrderInfo() != null
                && !com.alibaba.druid.util.StringUtils.isEmpty(resp.getOrderInfo().getStartTime())
                && Objects.equals(resp.getOrderInfo().getOrderState(), SmConstants.ORDER_STATUS_TO_PAY)) {
            int effectWaitingDay = orderMapper.countOrderProductEffectWaitingDay(orderId);
            Date todayBuyStartTime = DateUtil.addDay(DateUtil.getBeginOfDay(new Date()), effectWaitingDay);
            Date orderRealStartTime = DateUtil.parseDate(resp.getOrderInfo().getStartTime(), SmConstants.DATE_FORMAT_1);
            if (todayBuyStartTime.compareTo(orderRealStartTime) > 0) {
                resp.getOrderInfo().setOrderState(SmConstants.ORDER_STATUS_EXPIRE);
            }
        }
    }

    public OrderQueryResponse queryChannelOrderInfo(OrderQueryRequest request) {
        List<SmOrderListVO> smOrderListVOS = orderMapper.listOrderInsuredDetailByOrderId(request.getOrderId());

        if (CollectionUtils.isEmpty(smOrderListVOS)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "订单被保人不存在！");
        }
        SmOrderListVO smOrderListVO = smOrderListVOS.get(0);
        // 初始化返回对象
        OrderQueryResponse response = new OrderQueryResponse();
        OrderQueryResponse.OrderInfo orderInfo = new OrderQueryResponse.OrderInfo();
        OrderQueryResponse.PolicyInfo policyInfo = new OrderQueryResponse.PolicyInfo();
        response.setOrderInfo(orderInfo);
        response.setPolicyInfo(policyInfo);
        response.setNoticeCode(AmConstants.API_FANHUA_SUCCESS_0);
        orderInfo.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);
        List<OrderQueryResponse.PolicyInfo> policyInfos = smOrderListVOS.stream().map(tmpVo -> {
            OrderQueryResponse.PolicyInfo tmp = new OrderQueryResponse.PolicyInfo();
            tmp.setInsuredSn(tmpVo.getInsuredIdNumber());
            tmp.setAppStatus(tmpVo.getAppStatus());
            tmp.setDownloadURL(tmpVo.getDownloadURL());
            return tmp;
        }).collect(Collectors.toList());
        response.setPolicyInfos(policyInfos);

        //如果状态已承保 则说明已经被处理过 直接返回
        orderInfo.setOrderState(smOrderListVO.getPayStatus());
        policyInfo.setAppStatus(smOrderListVO.getAppStatus());
        policyInfo.setPolicyNo(smOrderListVO.getPolicyNo());
        policyInfo.setDownloadURL(smOrderListVO.getDownloadURL());
        return response;
    }

    public OrderQueryResponse checkPolicy(String orderId){
        OrderQueryResponse resp = getExternalThirdPartyOrderInfoByOrderId(orderId);
        if (!Objects.equals(resp.getNoticeCode(), AmConstants.API_FANHUA_SUCCESS_0)) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201008);
        }

        adjustExpireOrderResp(orderId, resp);
        return resp;
    }

    @Test
    public void updateOrderPolicyInfo() {
        String orderId = JMockData.mock(String.class);
        OrderQueryResponse resp = checkPolicy(orderId);
        /**
         * 检查出单成功的保单的电子保单地址
         */
        String epolicyUrl = resp.getEPolicyUrl();
        if(Objects.equals(resp.getPolicyInfo().getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)&& StringUtils.isBlank(epolicyUrl)) {
            epolicyUrl = adapter.queryEPolicyUrl(resp.getPolicyInfo().getPolicyNo(),null);
            if(StringUtils.isNotBlank(epolicyUrl)){
                orderMapper.updateEPolicyUrl(orderId,epolicyUrl);
            }
        }

        Map<String, String> res = Maps.newHashMap();
        resp.getPolicyInfos().forEach(policyInfo -> res.put(policyInfo.getInsuredSn(), policyInfo.getAppStatus()));
        System.err.println(res);
        Assert.assertTrue(true);
    }


}
