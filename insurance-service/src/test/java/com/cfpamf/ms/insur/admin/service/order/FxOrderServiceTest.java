package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendFxMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper;
import com.cfpamf.ms.insur.admin.external.fx.FxOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.fx.model.FxPayNotifyReq;
import com.cfpamf.ms.insur.admin.service.BaseTest;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmCancelRefundService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.github.jsonzou.jmockdata.JMockData;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.aop.framework.AopContext;

/**
 * <AUTHOR>
 * @Version 1.0
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRequestUtil.class, AopContext.class})
public class FxOrderServiceTest extends BaseTest {

    @Mock
    FxOrderServiceAdapterImpl adapter;
    @Mock
    SmOrderExtendFxMapper extendFxMapper;

    @Mock
    OrderNoGenerator orderNoGenerator;
    @Mock
    SmProductMapper productMapper;

    @Mock
    SmCancelRefundService cancelRefundService;

    @Mock
    SmOrderRiskDutyMapper orderRiskDutyMapper;

    @InjectMocks
    FxOrderService fxOrderService;

    @Test
    public void testAddGuaranteeInfo() {
        fxOrderService.addGuaranteeInfo(JMockData.mock(FxPayNotifyReq.class, con()), "1");
    }
}