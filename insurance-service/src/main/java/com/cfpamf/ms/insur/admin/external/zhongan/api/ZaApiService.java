package com.cfpamf.ms.insur.admin.external.zhongan.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductExtendZaCacheMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderOutType;
import com.cfpamf.ms.insur.admin.enums.order.OrderBindStatusEnum;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.common.CompanyErrorModel;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhOrderInfo;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaConsts;
import com.cfpamf.ms.insur.admin.external.zhongan.model.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.accept.ZaChargeInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.bank.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.check.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.claim.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.claim.za.kaiping.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupOrderRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaQueryGroupEndorsementReq;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaQueryGroupOrderReq;
import com.cfpamf.ms.insur.admin.external.zhongan.model.test.ZaClause;
import com.cfpamf.ms.insur.admin.external.zhongan.model.test.ZaLiability;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaCreateFamilyQuestionnaireBody;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaFamilyMember;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaFamilySmartUwConclusions;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaSmartBasicResp;
import com.cfpamf.ms.insur.admin.external.zhongan.util.ZaSmartUWEncrypt;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductExtendZaCache;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.base.constant.CacheKeyConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.weixin.constant.za.EnumPayWay;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.item.ChargeInfo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.PaymentReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.*;
import com.cfpamf.ms.insur.weixin.pojo.convertor.ZaConvertor;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupUnderwriting;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.OrgHolder;
import com.cfpamf.ms.pay.facade.constant.PayTypeEnum;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.google.common.collect.Lists;
import com.zhongan.filegateway.common.FileGatewayClient;
import com.zhongan.filegateway.common.FileUploadRequest;
import com.zhongan.filegateway.common.FileUploadResponse;
import com.zhongan.scorpoin.common.ZhongAnApiClient;
import com.zhongan.scorpoin.common.dto.CommonRequest;
import com.zhongan.scorpoin.common.dto.CommonResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.constant.BaseConstants.FMT_DATETIME;

/**
 * <AUTHOR> 2020/10/9 15:27
 */
@Slf4j
@Service
public class ZaApiService {
    static DateTimeFormatter ZA_FMT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    final static String CACHE_KEY = CacheKeyConstants.OTHER_PREFIX + "product:zacode:";

    @Getter
    @Autowired
    protected ZaApiProperties zaApiProperties;

    @Autowired
    protected OrderNoGenerator orderNoGenerator;

    @Qualifier("zaOrderClient")
    @Autowired
    protected ZhongAnApiClient apiClient;
    @Autowired
    SmProductExtendZaCacheMapper cacheMapper;

    @Autowired
    @Qualifier("zaAiCheckClient")
    protected ZhongAnApiClient aiCheckClient;

    @Autowired
    @Qualifier("zaClaimReportClient")
    protected ZhongAnApiClient zaClaimReportClient;

    @Autowired
    @Qualifier("zaKpClaimReportClient")
    protected ZhongAnApiClient zaKpClaimReportClient;

    static Map<String, Integer> ZA_RELATION_SHIP_MAPPER = new HashMap<>();
    static Map<String, String> ZA_RELATION_SHIP_NAME_MAPPER = new HashMap<>();

    static {
        //核保配置
        // 1	本人
        //2	配偶
        //3	子女
        //4	父母
        //2.3亲属关系（注意跟前置核保的关系码表不同，切勿搞混）
        //值	含义
        //1	本人
        //10	配偶
        //11	父母
        //12	子女
        //13	无人员信息
        ZA_RELATION_SHIP_MAPPER.put("1", 1);
        ZA_RELATION_SHIP_MAPPER.put("2", 10);
        ZA_RELATION_SHIP_MAPPER.put("3", 12);
        ZA_RELATION_SHIP_MAPPER.put("4", 11);

        ZA_RELATION_SHIP_NAME_MAPPER.put("1", "本人");
        ZA_RELATION_SHIP_NAME_MAPPER.put("2", "配偶");
        ZA_RELATION_SHIP_NAME_MAPPER.put("3", "子女");
        ZA_RELATION_SHIP_NAME_MAPPER.put("4", "父母");
    }

    /**
     * 核保 多人
     *
     * @return
     */
    public ZaCheckRes check(OrderSubmitRequest request) {

        String serviceName = "zhongan.health.personal.proposal.multiApply";
        ZaCheckReq req = new ZaCheckReq();
        req.setChannelCode(zaApiProperties.getChannelCode());

        FhOrderInfo orderInfo = request.getOrderInfo();
        req.setEffectiveTime(ZA_FMT.format(LocalDateTime.parse(orderInfo.getStartTime(), FMT_DATETIME)));
        req.setExpireTime(ZA_FMT.format(LocalDateTime.parse(orderInfo.getEndTime(), FMT_DATETIME)));
        //如果为1，则会将所有被保人全部校验后返回。但出单时该订单必须是核保全部通过的。
        req.setUwHandleType("1");

        FhProposer proposerInfo = request.getProposerInfo();
        ZaPolicyHolderPerson appl = new ZaPolicyHolderPerson();
        appl.setBirthday(proposerInfo.getBirthday().replaceAll("-", ""));
        appl.setCertNo(proposerInfo.getIdNumber());
        appl.setCertType(proposerInfo.getIdType());
        appl.setGender(proposerInfo.getPersonGender());
        appl.setEmail(proposerInfo.getEmail());
        appl.setPhone(proposerInfo.getCellPhone());
        appl.setName(proposerInfo.getPersonName());

        req.setPolicyHolder(appl);

        //被保人信息
        List<ZaPolicy> policies = request.getInsuredPerson()
                .stream()
                .map(ip -> {
                    TestPremiumProductForm product = ip.getProduct();

                    String planCode = Optional.ofNullable(product).map(TestPremiumProductForm::getPlanCode)
                            .orElseGet(() -> request.getProductInfo().getProductId());
                    ZaPolicy zaPolicy = new ZaPolicy();
                    zaPolicy.setProductCode(getZaProductCode(request, planCode, ip.getDuties()));
                    ZaInsured zaInsured = new ZaInsured();
                    zaInsured.setBirthday(ip.getBirthday().replaceAll("-", ""));
                    zaInsured.setCertNo(ip.getIdNumber());
                    zaInsured.setCertType(ip.getIdType());
                    zaInsured.setJobCode(ip.getOccupationCode());
                    zaInsured.setGender(ip.getPersonGender());
                    zaInsured.setRelationToPH(ip.getRelationship());
                    if (StringUtils.isNotBlank(ip.getIsSecurity())) {
                        zaInsured.setHasSocialInsurance(Objects.equals(ip.getIsSecurity(), "1") ? "Y" : "N");
                    }
                    zaInsured.setName(ip.getPersonName());
                    zaPolicy.setInsured(zaInsured);
                    //保费
                    String premium = Optional.ofNullable(product)
                            // 如果前端传了
                            .map(TestPremiumProductForm::getPremium)
                            //前端没传取全局产品的单价*分数
                            .orElseGet(() -> request.getProductInfo().getProductPrice()
                                    .multiply(new BigDecimal(request.getQty())))
                            .setScale(2, BigDecimal.ROUND_DOWN)
                            .toString();

                    //获取购买份数
                    String qty = Optional.ofNullable(product).map(TestPremiumProductForm::getQty)
                            .orElseGet(request::getQty) + "";
                    zaPolicy.setApplyNum(qty);
                    zaPolicy.setPremium(premium);
                    List<ZaExtendInfo> extendInfos = Lists.newArrayListWithCapacity(3);
                    //、、 * 如果有智能核保
                    if (StringUtils.isNotBlank(ip.getQuestionnaireId())) {
                        //isSmartUW和questionnaireId，其中isSmartUW固定传“true”,questionnaireId
                        extendInfos.add(new ZaExtendInfo("isSmartUW", Boolean.TRUE.toString()));
                        extendInfos.add(new ZaExtendInfo("questionnaireId", ip.getQuestionnaireId()));
                    }

                    if (StringUtils.isNotBlank(ip.getSmoke())) {
                        extendInfos.add(new ZaExtendInfo("smokeType", ip.getSmoke()));
                    }
                    if (!CollectionUtils.isEmpty(extendInfos)) {
                        zaPolicy.setExtendInfos(extendInfos);
                    }
                    //s48 绑卡/自动续保 add by zhangjian 2021-07-26
                    if (Objects.equals(request.getIsAutoRenewal(), "Y")) {
                        zaPolicy.setIsAutoRenewal(request.getIsAutoRenewal());
                        ZaCapitalAccountV2 capitalAccountV2 = new ZaCapitalAccountV2();
                        capitalAccountV2.setProtocolNo(request.getProtocolNo());
                        zaPolicy.setRenewalAuthAccount(capitalAccountV2);
                    }

                    return zaPolicy;
                }).collect(Collectors.toList());
        req.setPolicies(policies);

        BigDecimal allAmount = policies.stream().map(ZaPolicy::getPremium).map(BigDecimal::new).reduce(BigDecimal.ZERO
                , BigDecimal::add);
        request.getOrderInfo().setTotalAmount(allAmount);
        //产品的默认值 去第一个被保人方案
        req.setProductCode(policies.get(0).getProductCode());
        req.setChannelOrderNo(orderNoGenerator.getNextNo(EnumChannel.ZA.getCode()));

        return call(serviceName, Collections.singletonMap("infoJson", req), new TypeReference<ZaCheckRes>() {
        });
    }


    /**
     * 生成众安虚拟计划
     *
     * @param request
     * @return
     */
    public String getZaProductCode(OrderSubmitRequest request, String productSeriesCode, List<TestPremiumDutyForm> duties) {

        if (CollectionUtils.isEmpty(duties)) {
            return request.getProductInfo().getProductId().split("-")[0];
        }

        //根据险种分组
        final Map<String, List<TestPremiumDutyForm>> groups = LambdaUtils.groupBy(duties, TestPremiumDutyForm::getRiskCode);

        //缓存key
        final String allCodeStr = productSeriesCode + duties.stream()
                .map(duty -> duty.getDutyCode() + duty.getAmount()).sorted().collect(Collectors.joining());
        ZaApiService that = (ZaApiService) AopContext.currentProxy();
        final SmProductExtendZaCache cache = that.cache(allCodeStr);
        if (Objects.nonNull(cache)) {
            return cache.getZaProductCode();
        }
        final ZaGenerateProductCodeReq req = new ZaGenerateProductCodeReq();
        //转换成众安的参数
        final List<ZaClause> clauses = groups.entrySet().stream().map(entry -> {
            final ZaClause zaClause = new ZaClause();
            //所有责任
            final List<ZaLiability> liabilities = entry.getValue()
                    .stream().map(duty -> new ZaLiability(duty.getDutyCode(), duty.getAmount().toString())).collect(Collectors.toList());
            zaClause.setClauseCode(entry.getKey());
            zaClause.setLiabilities(liabilities);
            // 条款保额  所有责任保额只和
            zaClause.setClauseAmount(liabilities.stream().map(ZaLiability::getLiabilityAmount)
                    .map(BigDecimal::new)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO).toString());
            return zaClause;
        }).collect(Collectors.toList());
        req.setChannelCode(zaApiProperties.getChannelCode());
        req.setProductSeriesCode(productSeriesCode);
        req.setClauses(clauses);
        final String productCode = call("zhongan.health.personal.virtualproduct.generateProductCode", req, new TypeReference<ZaGenerateProductCodeResp>() {
        }).getProductCode();
        //保存到数据库 forkjoin pool
        CompletableFuture.runAsync(() -> that.saveCache(allCodeStr, productCode));
        return productCode;

    }

    @CacheEvict(value = CACHE_KEY, key = "#dutyJoinKey")
    public void saveCache(String dutyJoinKey, String productKey) {
        final SmProductExtendZaCache cache = new SmProductExtendZaCache();
        cache.setDutyJoin(dutyJoinKey);
        cache.setZaProductCode(productKey);
        cacheMapper.insertOrUpdate(cache);
    }

    @Cacheable(value = CACHE_KEY, key = "#codeJoin")
    public SmProductExtendZaCache cache(String codeJoin) {
        return cacheMapper.selectByJoinCode(codeJoin);
    }

    /**
     * 个险-出单接口
     *
     * @return
     */
    public ZaAcceptRes accept(OrderQueryResponse response, SmOrderListVO orderInfo, QueryOrderVO paymentInfo) {
        ZaAcceptPolicyReq acceptPolicyReq = new ZaAcceptPolicyReq();
        acceptPolicyReq.setChannelCode(zaApiProperties.getChannelCode());
        acceptPolicyReq.setChannelOrderNo(orderInfo.getFhOrderId());
        ZaChargeInfo zaPayment = new ZaChargeInfo();

        zaPayment.setPayTradeNo(paymentInfo.getPayTrxNo());
        zaPayment.setPayTime(ZA_FMT.format(LocalDateUtil.dateToLocaldatetime(paymentInfo.getPayTime())));
        //1	支付宝 2	银行转账 3	其他 4	微信支付
        zaPayment.setPayWay(PayTypeEnum.WXPAY.getTypeCode().equals(paymentInfo.getPayType()) ? "4" : "1");
        zaPayment.setPayMoney(paymentInfo.getOrderAmount());
        if (Objects.equals(orderInfo.getBindStatus(), OrderBindStatusEnum.BIND.getCode())) {
            zaPayment.setBindBizType(orderInfo.getBizType());
        }
        if (!EnumOrderOutType.SEE_FEE.getCode().equals(orderInfo.getOrderOutType())) {
            zaPayment.setIsCashByZADesk(Boolean.FALSE);
            zaPayment.setPayTradeNo(null);
        }
        acceptPolicyReq.setPayment(zaPayment);
        return call("zhongan.health.personal.policy.multiAcceptPolicy",
                Collections.singletonMap("infoJson", acceptPolicyReq), new TypeReference<ZaAcceptRes>() {
                });
    }


    /**
     * @param policyNo
     */
    public ZaQueryEPolicyURLResp queryEPolicyURL(String policyNo) {
        ZaQueryEPolicyURLReq req = new ZaQueryEPolicyURLReq();
        req.setPolicyNo(policyNo);
        req.setChannelCode(zaApiProperties.getChannelCode());
        return call("zhongan.health.policy.queryEPolicyURL",
                Collections.singletonMap("infoJson", req), new TypeReference<ZaQueryEPolicyURLResp>() {
                });
    }

    /**
     * @param groupPolicyNo
     */
    public ZaGroupOrderRes queryGroupOrder(String groupPolicyNo) {
        ZaQueryGroupOrderReq req = new ZaQueryGroupOrderReq();
        req.setHaGroupPolicyNo(groupPolicyNo);
        req.setChannelType(zaApiProperties.getGroupChannelType());
        return call("zhongan.health.group.policy.getGroupPolicyDetailInfo",
                Collections.singletonMap("infoJson", req), new TypeReference<ZaGroupOrderRes>() {
                });
    }

    /**
     * @param endorsementNo
     */
    public ZaGroupEndorsementRes queryGroupEndorsement(String endorsementNo) {
        ZaQueryGroupEndorsementReq req = new ZaQueryGroupEndorsementReq();
        req.setEndorsementNo(endorsementNo);
        req.setChannelType(zaApiProperties.getGroupChannelType());
        return call("zhongan.health.group.cs.getEndorsementDetailInfo",
                Collections.singletonMap("infoJson", req), new TypeReference<ZaGroupEndorsementRes>() {
                });
    }


    public ZaCreateFamilyQuestionnaireBody createFamily(OrderSubmitRequest submitRequest, String orderId) {

        String serviceName = "zhongan.health.smartuw.createFamilyQuestionnaireId";
        ZaCreateFamilyQuestionnaireIdReq familyQuestionnaireIdReq = new ZaCreateFamilyQuestionnaireIdReq();

        familyQuestionnaireIdReq.setAcceptPage(String.format(zaApiProperties.getAiCheckAcceptUrl(), orderId));
        familyQuestionnaireIdReq.setRejectPage(String.format(zaApiProperties.getAiCheckFailUrl(), orderId));
        familyQuestionnaireIdReq.setHealthNoticePage(String.format(zaApiProperties.getAiCheckFailUrl(), orderId));

        familyQuestionnaireIdReq.setChannelType("3");
        familyQuestionnaireIdReq.setSysTimestamp(System.currentTimeMillis() + "");
        familyQuestionnaireIdReq.setChannelCode(zaApiProperties.getAiCheckChannelCode());

        AtomicInteger integer = new AtomicInteger(1);
        List<ZaFamilyMember> memberList = submitRequest.getInsuredPerson()
                .stream()
                .map(ip -> {


                    TestPremiumProductForm product = ip.getProduct();
                    String planCode = Optional.ofNullable(product).map(TestPremiumProductForm::getPlanCode)
                            .orElseGet(() -> submitRequest.getProductInfo().getProductId());
                    String zaProductCode = getZaProductCode(submitRequest, planCode, ip.getDuties());
                    ZaFamilyMember familyMember = new ZaFamilyMember();
                    familyMember.setPackageCode(zaProductCode);
                    familyMember.setSerialNo(integer.getAndIncrement());
                    familyMember.setAge(ip.calcAge() + "");
                    familyMember.setCertificateCode(ip.getIdNumber());
                    familyMember.setCertificateType(ip.getIdType());
                    familyMember.setName(ip.getPersonName());
                    familyMember.setGender(ip.getPersonGender());
                    // 如果翻译失败 取无
                    familyMember.setRelationShipTitleCode(ZA_RELATION_SHIP_MAPPER.getOrDefault(ip.getRelationship(), 13));
                    familyMember.setRelationShipTitleName(ZA_RELATION_SHIP_NAME_MAPPER.getOrDefault(ip.getRelationship(), "无人员信息"));
                    return familyMember;
                }).collect(Collectors.toList());
        familyQuestionnaireIdReq.setFamilyMemberReqList(memberList);
        return call(aiCheckClient,
                serviceName,
                Collections.singletonMap("infoJson", familyQuestionnaireIdReq),
                new TypeReference<ZaSmartBasicResp<ZaCreateFamilyQuestionnaireBody>>() {
                },
                true).getResult();
    }


    public List<ZaFamilySmartUwConclusions> queryQuestionnaireRes(String questionnaireId) {

        ZaGetFamilySmartUwConclusionsReq req = new ZaGetFamilySmartUwConclusionsReq();
        req.setFamilyQuestionnaireId(questionnaireId);
        return call(aiCheckClient,
                ZaConsts.SERVICE_NAME_QUERY_QUESTIONNAIRE,
                Collections.singletonMap("infoJson", req),
                new TypeReference<ZaSmartBasicResp<List<ZaFamilySmartUwConclusions>>>() {
                },
                true).getResult();
    }

    /**
     * 退保 todo
     */
    public void surrender(String orderId) {
        String serviceName = "zhongan.health.personal.policy.surrender";

    }

    /**
     * 查询银行列表
     *
     * @return
     */
    public ZaBankListRes queryBankList() {
        ZaBankBaseReq req = new ZaBankBaseReq<ZaBankListReq>();
        req.setChannelCode(zaApiProperties.getChannelCode());
        req.setBizType(ZaConsts.BIZ_TYPE_BANK_LIST);
        req.setBizKey(System.currentTimeMillis() + "");
        ZaBankListReq bankListReq = new ZaBankListReq();
        bankListReq.setBizType(ZaConsts.BIZ_TYPE_BANK_LIST);
        req.setBizContent(bankListReq);

        return call(
                ZaConsts.SERVICE_NAME_QUERY_BANK_LIST,
                Collections.singletonMap("infoJson", req),
                new TypeReference<ZaBankListRes>() {
                });
    }

    /**
     * 获取协议号
     *
     * @return
     */
    public ZaCardBindRes bindingCard(ZaCardBindReq req) {
        req.setChannelCode(zaApiProperties.getChannelCode());
        req.setProtocolChannel(zaApiProperties.getProtocolChannel());

        return call(
                ZaConsts.SERVICE_NAME_BIND_AUTHENTICATION,
                Collections.singletonMap("infoJson", req),
                new TypeReference<ZaCardBindRes>() {
                });
    }

    /**
     * 验证绑定
     *
     * @return
     */
    public ZaBankBaseRes validateBind(String protocolNo, String bankSms) {
        ZaBankBaseReq req = new ZaBankBaseReq<ZaValidateBindReq>();
        req.setChannelCode(zaApiProperties.getChannelCode());
        ZaValidateBindReq validateBindReq = new ZaValidateBindReq();
        validateBindReq.setProtocolNo(protocolNo);
        validateBindReq.setVerCode(bankSms);
        req.setBizContent(validateBindReq);
        req.setBizType(ZaConsts.BIZ_TYPE_BANK_CARD_BIND);
        req.setBizKey(UUID.randomUUID().toString().replaceAll("-", ""));
        return call(
                ZaConsts.SERVICE_NAME_VALIDATE_BIND,
                Collections.singletonMap("infoJson", req),
                new TypeReference<ZaBankBaseRes>() {
                });
    }


    /**
     * 解密
     *
     * @param content
     * @return
     */
    public String decrypt(String content) {
        try {
            return ZaSmartUWEncrypt.urlDecodeAndDecrypt(zaApiProperties.getAiCheckToken(), content);
        } catch (Exception e) {
            log.warn("解密失败 ", e);
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安解密失败");
        }
    }

    private <T extends CompanyErrorModel> T call(String serviceName, Object body, TypeReference<T> resModel) {
        return call(apiClient, serviceName, body, resModel, true);
    }

    private static <T extends CompanyErrorModel> T call(ZhongAnApiClient client,
                                                        String serviceName,
                                                        Object body,
                                                        TypeReference<T> resModel, boolean autoThrow) {

        long begin = System.currentTimeMillis();
        boolean bizSuccess = true;
        int httpStatus = 200;
        CommonResponse call = null;
        try {
            CommonRequest request = new CommonRequest(serviceName);

            JSONObject jsonObject = (JSONObject) JSONObject.toJSON(body);
            request.setParams(jsonObject);
            log.info("众安req {}:{}", serviceName, jsonObject);
            call = client.call(request);
            log.info("众安resp:{}", JSON.toJSON(call));
            if (StringUtils.isNotBlank(call.getErrorMsg())) {
                throw new MSBizNormalException(ExcptEnum.FH_ERROR_000099.getCode(), call.getErrorMsg());
            }
            T t = JSON.parseObject(call.getBizContent(), resModel);
            if (!t.isSuccess() && autoThrow) {
                bizSuccess = false;
                if (t.isShow()) {
                    throw new MSBizNormalException(ExcptEnum.FH_ERROR_000099.getCode(), "[众安]" + t.errorMsg());
                } else {
                    throw new MSException(ExcptEnum.FH_ERROR_000099.getCode(), "[众安]" + t.errorMsg());
                }

            }
            return t;
        } catch (MSException be) {
            bizSuccess = false;
            throw be;
        } catch (Exception e) {
            log.warn("众安接口调用失败", e);
            bizSuccess = false;
            throw new MSException(ExcptEnum.FH_ERROR_000099.getCode(), "[众安]" + e.getMessage());
        } finally {
            log.info("THIRD-MONITOR: method:{}, url:{}, httpCode:{}, bizSuccess:{}, userId:{}, source:{}, costTime:{}ms,  requestBody:{}, responseBody:{}",
                     "POST", serviceName, httpStatus, bizSuccess, HttpRequestUtil.getUserId(), "self", System.currentTimeMillis() - begin,
                     body, call);
        }
    }


    /***************************************************************/
    /**
     * 团险报价
     *
     * @param zaGroupQuote
     * @return
     */
    public ZaQuoteResp quotePrice4Group(final ZaGroupUnderwritingReq zaGroupQuote) {
        log.info("[众安团险报价请求]-[{}]", JSON.toJSONString(zaGroupQuote));
        ZhongAnApiClient client = initClient();
        String quoteUrl = "zhongan.health.group.proposal.quotePrice";
        ZaQuoteResp resp = call(client, quoteUrl, zaGroupQuote, new TypeReference<ZaQuoteResp>() {
        }, false);
        log.info("[众安团险报价结果]-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        return resp;
    }

    public ZaQuoteResp apply4Group(ZaGroupUnderwritingReq uwr) {
        log.info("[众安团险核保请求]-[{}]", JSON.toJSONString(uwr));
        ZhongAnApiClient client = initClient();
        String quoteUrl = "zhongan.health.group.proposal.apply";
        ZaQuoteResp resp = call(client, quoteUrl, uwr, new TypeReference<ZaQuoteResp>() {
        }, false);
        log.info("[众安团险核保结果]-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        return resp;
    }

    /**
     * 众安查询支付信息
     * Api地址：https://open.zhongan.com/portal/site/apidoc/documentDetail/main?spaceId=2001&directoryId=2004&articleId=16004
     *
     * @param orderId
     * @return
     */
    public ZaPayWrapper checkPay(String orderId) {
        log.info("[众安支付信息检查]-{}", orderId);
        ZhongAnApiClient client = initClient();
        String proxyUrl = "com.zhongan.brave.troops.findTradeElementByOutTradeNo";

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("outTradeNo", orderId);
        paramMap.put("merchantCode", zaApiProperties.getMerchantCode());

        ZaPayWrapper resp = call(client, proxyUrl, paramMap, new TypeReference<ZaPayWrapper>() {
        }, false);
        log.info("[众安支付信息检查]-{}", resp);
        return resp;
    }

    /**
     * 文件上传
     *
     * @param localFile
     * @return
     */
    public FileUploadResponse uploadFile(String localFile) {
        String env = zaApiProperties.getFileGwEnv();

        String url = zaApiProperties.getFileUploadUrl();
        String appKey = zaApiProperties.getAppKey();
        FileGatewayClient client = new FileGatewayClient(env, url);
        FileUploadRequest req = new FileUploadRequest();
        req.setAppKey(appKey);
        req.addFile(new File(localFile));
        FileUploadResponse response = client.call(req);
        log.info("众安文件上传,resp:{}", response);
        return response;
    }


    /**
     * 获取支付Html信息
     *
     * @param req
     */
    public String getPayHtml(PaymentReq req) {
        String json = JSON.toJSONString(req);
        return PaymentIntentUtil.buildV2(zaApiProperties.getPayGateway(),
                JSON.parseObject(json, Map.class),
                zaApiProperties.getPayAppKey());
    }

    /**
     * 获取支付Html信息
     *
     * @param req
     */
    public String getPayForm(PaymentReq req) {
        String json = JSON.toJSONString(req);
        return PaymentIntentUtil.buildFormV2(zaApiProperties.getPayGateway(),
                JSON.parseObject(json, Map.class),
                zaApiProperties.getPayAppKey());
    }

    /**
     * 个险-出单接口
     *
     * @return
     */
    public ZaQuoteResp groupApply(SmOrderListVO orderInfo, QueryOrderVO paymentInfo) {
        ZaGroupInsureReq request = new ZaGroupInsureReq();
        String appNo = orderInfo.getAppNo();
        String orderId = orderInfo.getFhOrderId();
        request.setChannelCode(zaApiProperties.getGroupChannelCode());
        request.setChannelOrderNo(orderId);
        request.setProposalNo(appNo);

        ChargeInfo payment = new ChargeInfo();
        BigDecimal orderAmount = paymentInfo.getOrderAmount();
        payment.setPayMoney(String.valueOf(orderAmount));
        payment.setPayTradeNo(paymentInfo.getSourceOrderId());
        String payTime = LocalDateUtil.format(paymentInfo.getPayTime());
        payment.setPayTime(payTime);
        payment.setPayWay(EnumPayWay.getCode(paymentInfo.getPayType()));
        request.setPayment(payment);

        return groupInsue(request);
    }

    /**
     * 团险出单
     *
     * @param req
     * @return
     */
    public ZaQuoteResp groupInsue(ZaGroupInsureReq req) {
        log.info("众安团险出单-{}", JSON.toJSONString(req));

        ZhongAnApiClient client = initClient();
        String quoteUrl = "zhongan.health.group.policy.acceptPolicy";
        ZaQuoteResp resp = call(client, quoteUrl, req, new TypeReference<ZaQuoteResp>() {
        }, false);
        log.info("众安团险出单结果-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.getMsgInfo());
        }
        return resp;
    }

    /**
     * 批改试算保费
     *
     * @param req
     * @return
     */
    public ZaEndorRespWrapper<ZaEndorCalPremiumResp> endorCalPremium(ZaGroupEndorReq req) {
        log.info("[众安团险批改试算请求报文]-[{}]", JSON.toJSONString(req));
        ZhongAnApiClient client = initClient();
        String proxyUrl = "zhongan.health.group.endorsement.calculatedPremium";
        TypeReference<ZaEndorRespWrapper<ZaEndorCalPremiumResp>> tr = new TypeReference<ZaEndorRespWrapper<ZaEndorCalPremiumResp>>() {
        };
        ZaEndorRespWrapper<ZaEndorCalPremiumResp> resp = call(client, proxyUrl, req, tr, false);
        log.info("[众安团险批改试算结果]-{}", resp);
        return resp;
    }

    /**
     * 批改核保Api
     *
     * @param endor
     * @return
     */
    public ZaEndorRespWrapper<ZaEndorUnderwritingResp> endorUnderwriting(ZaGroupEndorReq endor) {
        log.info("众安团险批改核保-[{}]", JSON.toJSONString(endor));
        ZhongAnApiClient client = initClient();
        String proxyurl = "zhongan.health.group.endorsement.underwriting";
        TypeReference<ZaEndorRespWrapper<ZaEndorUnderwritingResp>> tr = new TypeReference<ZaEndorRespWrapper<ZaEndorUnderwritingResp>>() {
        };
        ZaEndorRespWrapper<ZaEndorUnderwritingResp> resp = call(client, proxyurl, endor, tr, false);
        log.info("[众安团险批改核保结果]-{}", resp);

        return resp;
    }

    /**
     * 批改提交
     *
     * @param endor
     * @return
     */
    public ZaEndorRespWrapper<ZaEndorCommitResp> endorSubmit(ZaGroupEndorCommit endor) {
        log.info("众安团险批改提交请求报文-[{}]", JSON.toJSONString(endor));
        ZhongAnApiClient client = initClient();
        String proxyurl = "zhongan.health.group.endorsement.submit";
        TypeReference<ZaEndorRespWrapper<ZaEndorCommitResp>> tr = new TypeReference<ZaEndorRespWrapper<ZaEndorCommitResp>>() {
        };
        ZaEndorRespWrapper<ZaEndorCommitResp> resp = call(client, proxyurl, endor, tr, false);
        log.info("[众安团险批改提交相应报文]-{}", resp);
        return resp;
    }

    /**
     * 批改生效Api
     *
     * @param endor
     * @return
     */
    public ZaEndorRespWrapper<ZaEndorCommitResp> endorEffective(ZaGroupEndorEffective endor) {
        log.info("众安团险批改生效请求-[{}]", JSON.toJSONString(endor));
        ZhongAnApiClient client = initClient();
        String proxyurl = "zhongan.health.group.endorsement.effective";

        TypeReference<ZaEndorRespWrapper<ZaEndorCommitResp>> tr = new TypeReference<ZaEndorRespWrapper<ZaEndorCommitResp>>() {
        };
        ZaEndorRespWrapper<ZaEndorCommitResp> resp = call(client, proxyurl, endor, tr, false);
        log.info("[众安团险批改生效响应报文]-{}", resp);
        return resp;
    }

    /**
     * 查询电子保单地址
     *
     * @param req
     * @return
     */
    public ZaEPolicyResp queryEPolicyUrl(ZaEPolicyReq req) {
        log.info("众安团险查询电子保单地址-{}", JSON.toJSONString(req));
        ZhongAnApiClient client = initClient();
        String proxyurl = "zhongan.health.policy.queryEPolicyURL";

        TypeReference<ZaEPolicyResp> tr = new TypeReference<ZaEPolicyResp>() {
        };
        Map<String, Object> paramMap = new HashMap(1);
        paramMap.put("infoJson", req);

        ZaEPolicyResp resp = call(client, proxyurl, paramMap, tr, false);
        log.info("[众安团险查询电子保单地址响应]-{}", resp);
        if (resp == null || !resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.getMsgInfo());
        }
        return resp;
    }

    public Boolean revokeEndor(ZaGroupEndorRevoke revoke) {
        log.info("众安团险撤销批改请求-[{}]", JSON.toJSONString(revoke));
        ZhongAnApiClient client = initClient();
        String proxyurl = "zhongan.health.group.endorsement.revoke";

        TypeReference<ZaEndorRespWrapper<ZaEndorRevokeResp>> tr = new TypeReference<ZaEndorRespWrapper<ZaEndorRevokeResp>>() {
        };
        ZaEndorRespWrapper<ZaEndorRevokeResp> resp = call(client, proxyurl, revoke, tr, false);
        log.info("[众安团险撤销批改响应]-{}", resp);
        return resp.isSuccess();
    }

    public ZaInvoiceResp openInvoice(ZaInvoiceReq data) {
        log.info("[众安团险统一开票请求]-[{}]", JSON.toJSONString(data));
        ZhongAnApiClient client = initClientV3();
        String proxyUrl = "zhongan.health.group.endorsement.applyUnifyInvoice";


        TypeReference<ZaEndorRespWrapper<ZaInvoiceResp>> tr = new TypeReference<ZaEndorRespWrapper<ZaInvoiceResp>>() {
        };
        ZaEndorRespWrapper<ZaInvoiceResp> resp = call(client, proxyUrl, data, tr, false);
        log.info("[众安团险统一开票结果]-{}", JSON.toJSONString(resp));
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.getMessage());
        }
        return resp.getResult();
    }


    public ZaOfflinePayResponse submitOfflinePay(ZaOfflinePayRequest data) {
        log.info("众安团险线下支付请求-{}", JSON.toJSONString(data));
        ZhongAnApiClient client = initClient();
        String proxyUrl = "zhongan.health.group.policy.offlinePayment";

        TypeReference<ZaOfflinePayResponse> tr = new TypeReference<ZaOfflinePayResponse>() {
        };

        ZaOfflinePayResponse resp = call(client, proxyUrl, data, tr, false);
        log.info("[众安团险线下支付请求结果]-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.getMsgInfo());
        }
        return resp;
    }


    public ZaOfflinePay4EndorResponse submitOfflinePay4Endor(ZaOfflinePayEndorRequest data) {
        log.info("众安团险线下支付请求(批改)-{}", JSON.toJSONString(data));
        ZhongAnApiClient client = initClient();
        String proxyUrl = "zhongan.health.group.endorsement.saveVoucher";

        TypeReference<ZaOfflinePay4EndorResponse> tr = new TypeReference<ZaOfflinePay4EndorResponse>() {
        };

        ZaOfflinePay4EndorResponse resp = call(client, proxyUrl, data, tr, false);
        log.info("[众安团险线下支付请求结果]-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.getMessage());
        }
        return resp;
    }

    /**
     * 理赔报案 - 报案并上传附件
     *
     * @param reportRequest
     * @return
     */
    public ReportResponse claimReport(ZaRequestWrapper<ReportRequest> reportRequest) {
        log.info("[众安理赔报案接口请求]-[{}]", JSON.toJSONString(reportRequest));
//        ZhongAnApiClient client = initClient();
        String proxyUrl = "zhongan.ha.claim.claimReportAndUploadAttachment";
        TypeReference<ReportResponse> tr = new TypeReference<ReportResponse>() {
        };
        return call(zaClaimReportClient, proxyUrl, reportRequest, tr, true);
    }


    /**
     * 理赔报案 - 创建收件任务
     *
     * @param expressTaskWrapper
     * @return
     */
    public ZaExpressResponse addExpressTask(ZaClaimExpressTaskWrapper expressTaskWrapper) {
        log.info("[众安理赔邮寄接口请求]-[{}]", JSON.toJSONString(expressTaskWrapper));
        String proxyUrl = "zhongan.ha.claim.addExpressTask";
        TypeReference<ZaExpressResponse> tr = new TypeReference<ZaExpressResponse>() {
        };
        return call(zaClaimReportClient, proxyUrl, expressTaskWrapper, tr, true);
    }

    public ZaClaimQueryResponse claimSearch(ZaSearchRequestWrapper searchRequestWrapper) {
        log.info("[众安理赔查询接口请求]-[{}]", JSON.toJSONString(searchRequestWrapper));
        String proxyUrl = "zhongan.ha.claim.search";
        TypeReference<ZaClaimQueryResponse> tr = new TypeReference<ZaClaimQueryResponse>() {
        };
        return call(zaClaimReportClient, proxyUrl, searchRequestWrapper, tr, true);
    }

    public ZaKpResponse kpClaimReport(ZaKpApply reportRequest) {
        log.info("[众安开平理赔报案接口请求]-[{}]", JSON.toJSONString(reportRequest));
        String proxyUrl = "zhongan.open.individual.generic.claim.apply";
        TypeReference<ZaKpResponse> tr = new TypeReference<ZaKpResponse>() {
        };
        return call(zaKpClaimReportClient, proxyUrl, reportRequest, tr, true);
    }

    public ZaKpResponse kpClaimSupplement(ZaFileSupplement fileSupplement) {
        log.info("[众安开平理赔材料补充报案接口请求]-[{}]", JSON.toJSONString(fileSupplement));
        String proxyUrl = "zhongan.open.individual.generic.claim.supplement";
        TypeReference<ZaKpResponse> tr = new TypeReference<ZaKpResponse>() {
        };
        return call(zaKpClaimReportClient, proxyUrl, fileSupplement, tr, true);
    }

    public ZaKpQueryResponse queryZaKpClaimInfo(ZaKpQuery query) {
        log.info("[众安开平查询报案接口请求]-[{}]", JSON.toJSONString(query));
        String proxyUrl = "zhongan.open.individual.generic.claim.query";
        TypeReference<ZaKpQueryResponse> tr = new TypeReference<ZaKpQueryResponse>() {
        };
        return call(zaKpClaimReportClient, proxyUrl, query, tr, true);
    }



    private ZaInvoiceResp mockBean(ZaInvoiceReq data) {
        ZaInvoiceResp resp = new ZaInvoiceResp();
        resp.setGroupPolicyNo(data.getPolicyNo());
        resp.setInvoiceNum("M10001");
        resp.setInvoiceUrl("http://example.com");
        List<String> es = new ArrayList<>();
        for (InvoiceEndorsementItem entry : data.getInvoiceEndorsementInfo()) {
            es.add(entry.getEndorsementNo());
        }
        resp.setEndorsementNo(es);
        return resp;
    }

    private ZhongAnApiClient initClient() {
        String env = zaApiProperties.getEnv();
        String appKey = zaApiProperties.getAppKey();
        String privateKey = zaApiProperties.getPrivateKey();
        String version = zaApiProperties.getGroupVersion();
        return new ZhongAnApiClient(env,
                appKey,
                privateKey,
                version);
    }

    public ZaGroupRenewalApplyRes renewwalApply4Group(ZaGroupUnderwritingReq uwr, GroupUnderwriting req) {
        log.info("[众安团险续保申请请求]-[{}]", JSON.toJSONString(uwr));
        ZhongAnApiClient client = initClient();
        String quoteUrl = "zhongan.health.group.renewPolicy.apply";
        ZaGroupRenewalApplyRes resp = call(client, quoteUrl, uwr, new TypeReference<ZaGroupRenewalApplyRes>() {
        }, false);
        log.info("[众安团险核保结果]-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        return resp;
    }

    public ZaGroupRenewalQuoteResp renewwalUnderwriting(ZaGroupUnderwritingReq uwr, GroupUnderwriting req, ZaGroupRenewalApplyResult applyResult) {
        ZaGroupUnderwritingReq renewwalUwr = new ZaGroupUnderwritingReq();
        log.info("[众安团险续保核保请求]-[{}]", JSON.toJSONString(uwr));
        ZhongAnApiClient client = initClient();
        String quoteUrl = "zhongan.health.group.renewPolicy.underwrting";
        ZaGroupRenewalQuoteResp resp = call(client, quoteUrl, uwr, new TypeReference<ZaGroupRenewalQuoteResp>() {
        }, false);
        log.info("[众安团险续保核保结果]-{}", resp);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        return resp;
    }

    private ZhongAnApiClient initClientV3() {
        String env = zaApiProperties.getEnv();
        String appKey = zaApiProperties.getAppKey();
        String privateKey = zaApiProperties.getPrivateKey();
        String version = zaApiProperties.getInvoiceVersion();
        return new ZhongAnApiClient(env,
                appKey,
                privateKey,
                version);
    }
}
