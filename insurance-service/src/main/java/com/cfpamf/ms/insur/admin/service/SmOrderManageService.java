package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.CentNoUtil;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.ModuleVO;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.annotation.MaskMethod;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.product.ProductLabelEnum;
import com.cfpamf.ms.insur.admin.convertedpremium.dao.ConvertedPremiumMapper;
import com.cfpamf.ms.insur.admin.dao.safepg.InsuranceOrderCommissionShortMapper;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderPolicyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderRiskMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmAddCommissionDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.dao.safes.reconciliation.SmReconciliationPolicyMapper;
import com.cfpamf.ms.insur.admin.enums.*;
import com.cfpamf.ms.insur.admin.enums.commission.CommissionBusinessTypeEnum;
import com.cfpamf.ms.insur.admin.enums.correct.CorrectProject;
import com.cfpamf.ms.insur.admin.enums.order.EnumOrderType;
import com.cfpamf.ms.insur.admin.enums.order.OrderBindStatusEnum;
import com.cfpamf.ms.insur.admin.enums.order.OrderVisitResultEnum;
import com.cfpamf.ms.insur.admin.enums.reconciliation.EnumReconciliationStatusType;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.event.OrderCustomerChangeEvent;
import com.cfpamf.ms.insur.admin.event.OrderImportEvent;
import com.cfpamf.ms.insur.admin.event.SmPolicyStatusCorrectEvent;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionQueryResultDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.UpdateCommissionInfoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.excelorder.ExcelLongTermOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.excelorder.ExcelShortTermOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.OrderBaseDetailDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderInsuredBuyDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.UpdateOrderRecommendDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.reconciliation.ReconciledPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.*;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrderRisk;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmAddCommissionDetail;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmPolicyRegister;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImport;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImportError;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImportExtend;
import com.cfpamf.ms.insur.admin.pojo.query.*;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderInfoVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderMasterVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderNewVO;
import com.cfpamf.ms.insur.admin.renewal.dao.OrderRenewalMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalTermMapper;
import com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal;
import com.cfpamf.ms.insur.admin.renewal.service.RenewalOrderService;
import com.cfpamf.ms.insur.admin.renewal.vo.BaseOrderVo;
import com.cfpamf.ms.insur.admin.service.commission.AddCommissionQueryService;
import com.cfpamf.ms.insur.admin.service.commission.CommissionManagerService;
import com.cfpamf.ms.insur.admin.service.commission.CommissionQueryService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderPolicyService;
import com.cfpamf.ms.insur.admin.service.product.ProductQueryService;
import com.cfpamf.ms.insur.admin.util.TxUtil;
import com.cfpamf.ms.insur.admin.validation.SmOfflineOrderValidation;
import com.cfpamf.ms.insur.admin.validation.ValidationResult;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.AbstractLockService;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.service.InsurPayService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.base.util.excel.ExcelReadUtils;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveDefault;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveUtil;
import com.cfpamf.ms.insur.common.datasource.annotation.DataSourceReadOnly;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxFormFieldOptionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 小额保险订单管理service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmOrderManageService extends AbstractLockService {

    public static int ORDER_LIST_MAX_SIZE = 30000;

    @Autowired
    InsurPayService payService;

    @Autowired
    SmOrderDDDMapper orderDDDMapper;

    @Autowired
    SmOrderApplicantMapper applicantMapper;

    @Autowired
    SmOrderInsuredMapper insuredMapper;

    @Autowired
    SmCancelService cancelService;

    @Autowired
    SmProductFormBuyLimitMapper formBuyLimitMapper;

    /**
     * 保险公司参数查询mapper
     */
    @Autowired
    private SmCmpySettingMapper cmpySettingMapper;

    /**
     * 产品service
     */
    @Autowired
    private SmProductService productService;

    /**
     * 产品 快照 service
     */
    @Autowired
    private SmProductHistoryService productHistoryService;

    /**
     * 订单业务报表
     */
    @Autowired
    private SmReportService reportService;

    /**
     * PermissionQueryUtil
     */
    @Autowired
    private PermissionUtil permissionUtil;

    /**
     * 订单mapper
     */
    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private InsuranceOrderCommissionShortMapper insuranceOrderCommissionShortMapper;
    /**
     * 小额保险系统补单记录Mapper
     */
    @Autowired
    private SmOrderSupmtMapper supmtMapper;

    /**
     * BmsService
     */
    @Autowired
    private BmsService bmsService;

    /**
     * 用户mapper
     */
    @Autowired
    private AuthUserMapper userMapper;

    /**
     * BmsConfig
     */
    @Autowired
    private BmsConfig bmsConfig;

    /**
     * 数据校验
     */
    @Autowired
    SmOfflineOrderValidation validation;

    /**
     * 订单导入
     */
    @Autowired
    SmOrderImportMapper orderImportMapper;

    /**
     * 订单导入错误信息
     */
    @Autowired
    SmOrderImportErrorMapper orderImportErrorMapper;

    /**
     * 单号生成
     */
    @Autowired
    OrderNoGenerator orderNoGenerator;

    /**
     * 事件引擎
     */
    @Lazy
    @Autowired
    EventBusEngine eventBusEngine;

    @Autowired
    ObjectMapper objectMapper;
    @Autowired
    UserPostMapper userPostMapper;

    @Autowired
    SmPolicyRegisterService registerService;
    @Autowired
    SmOrderImportExtendMapper smOrderImportExtendMapper;
    @Autowired
    SmOrderImportService smOrderImportService;
    @Autowired
    SmOrderItemMapper smOrderItemMapper;
    @Autowired
    private SmCarOrderManageService smCarOrderManageService;
    @Autowired
    private AutoOrderPolicyMapper autoOrderPolicyMapper;
    /**
     * 产品提成 service
     */
    @Autowired
    protected SmCommissionMapper cmsMapper;
    /**
     * 字典service
     */
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private SmReconciliationPolicyMapper smReconciliationPolicyMapper;

    @Autowired
    private UserPostService userPostService;
    @Autowired
    private SmCancelRefundService smCancelRefundService;
    /**
     * 任务保费配置的service
     */
    @Autowired
    private SmTaskPremiumConfigService premiumConfigService;

    @Autowired
    private RenewalOrderService renewalOrderService;
    @Autowired
    private SmOrderPolicyMapper smOrderPolicyMapper;
    @Autowired
    private SmOrderCommissionDetailMapper smOrderCommissionDetailMapper;
    @Autowired
    ConvertedPremiumMapper convertedPremiumMapper;
    @Autowired
    SmOrderRenewalTermMapper smOrderRenewalTermMapper;

    @Autowired
    protected SmOrderRiskDutyMapper riskDutyMapper;
    @Autowired
    private CommissionQueryService commissionQueryService;
    @Autowired
    private ProductQueryService productQueryService;

    @Autowired
    AddCommissionQueryService addCommissionQueryService;
    @Autowired
    CommissionManagerService commissionManagerService;
    @Autowired
    private SmAddCommissionDetailMapper smAddCommissionDetailMapper;
    @Autowired
    private AutoOrderRiskMapper autoOrderRiskMapper;
    @Autowired
    private SmOrderPolicyService smOrderPolicyService;

    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private OrderRenewalMapper orderRenewalMapper;


    @DataSourceReadOnly
    public SmOrderSummaryVO getOrderCommissionSummaryV3(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initOrderQuery(query);
        return orderMapper.getOrderCommissionSummaryV3(query);
    }

    /**
     * 短险数据查询
     *
     * @param query
     * @return
     */
    @DataSourceReadOnly
    public PageInfo<SmOrderListVO> getOrderCommissionByPageV3(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initOrderQuery(query);

        List<SmOrderListVO> orderVos = orderMapper.listOrderCommissionV3(query);
        //计算任务保费字段
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderVos)) {
            caculateTaskPremium(orderVos);
            //设置加佣比例
            setOrderListCommissionProportion(orderVos);

            initReconciliationResult(orderVos);
        }

        handleCode(orderVos);

        setEndorsementNo(orderVos);

        // 对客户敏感数据进行脱敏
        if (query.getCommission() == null) {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderList());
        } else {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderCms());
        }
        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            if(query.getForbidCountSwitch()){
                summaryPage.setTotal(0);
                summaryPage.setHasNextPage(true);
            }else{
                summaryPage.setTotal(orderMapper.countOrderCommissionV3(query));
                summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
            }
        }
        return summaryPage;

    }

    @DataSourceReadOnly
    public Integer countOrderCommission(SmOrderQuery query){
        if (query == null) {
            return null;
        }
        initOrderQuery(query);
        return query.isQueryPage() ? orderMapper.countOrderCommissionV3(query) : 0;
    }

    /**
     * 码值处理
     *
     * @param orderVos
     */
    private void handleCode(List<SmOrderListVO> orderVos) {
        CmpySettingQuery settingQuery = new CmpySettingQuery();
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<SmCompanySettingVO> sexSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP);
        List<SmCompanySettingVO> relationshipSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        List<String> mobiles = orderVos.stream().filter(o -> o.getRecommendUserId() != null && o.getEntryDate() != null).map(SmOrderListVO::getRecommendUserMobile)
                .distinct().collect(Collectors.toList());

        Map<String, String> mapByType = dictionaryService.getMapByType(EnumDictionaryType.PRODUCT_GROUP.getCode());
        orderVos.forEach(o -> {
            Optional<SmCompanySettingVO> optional = sexSettings.stream()
                    .filter(v -> o.getCompanyId() != null && (v.getCompanyId() == o.getCompanyId())
                            && Objects.equals(v.getOptionCode(), o.getApplicantPersonGender())
                            && Objects.equals(v.getChannel(), o.getChannel())
                    ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setApplicantPersonGender(smCompanySettingVO.getOptionName()));
            optional = sexSettings.stream().filter(v -> o.getCompanyId() != null && v.getCompanyId() == o.getCompanyId()
                    && Objects.equals(v.getOptionCode(), o.getInsuredPersonGender())
                    && Objects.equals(v.getChannel(), o.getChannel())
            ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setInsuredPersonGender(smCompanySettingVO.getOptionName()));
            optional = relationshipSettings.stream().filter(v -> o.getCompanyId() != null && v.getCompanyId() == o.getCompanyId()
                    && Objects.equals(v.getOptionCode(), o.getInsuredRelationship())
                    && Objects.equals(v.getChannel(), o.getChannel())
            ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setInsuredRelationship(smCompanySettingVO.getOptionName()));

            if (org.apache.commons.lang3.StringUtils.isNotBlank(o.getProductType())) {
                o.setProductTypeName(mapByType.get(o.getProductType()));
            }
        });
    }

    private void caculateTaskPremium(List<SmOrderListVO> orderVos) {
        List<Integer> productIds = orderVos.stream().map(SmOrderListVO::getProductId).distinct().collect(Collectors.toList());
        List<SmTaskPremiumConfig> taskPremiumConfigList = premiumConfigService.queryTaskPremiumConfigByProductIds(productIds);
        Map<Integer, List<SmTaskPremiumConfig>> productIdPremiumConfigMap = taskPremiumConfigList.stream()
                .collect(Collectors.groupingBy(SmTaskPremiumConfig::getProductId));

        orderVos.forEach(item -> {
            //承保时, 根据任务保费支付时间配置计算
            String appStatus = item.getAppStatus();
            String config = null;
            if (item.getProductId() != null && item.getPaymentTime() != null) {
                config = Optional.ofNullable(productIdPremiumConfigMap.get(item.getProductId()))
                        .map(configs -> getPremiumConfig(configs, LocalDateTime.ofInstant(item.getPaymentTime().toInstant(), ZoneId.systemDefault())))
                        .map(SmTaskPremiumConfig::getPremiumStandard).orElse(null);
            }
            if (SmConstants.POLICY_STATUS_SUCCESS.equals(appStatus)) {
                if ("01".equals(config)) {
                    //折算
                    item.setTaskPremium(item.getConvertedAmount());
                } else {
                    item.setTaskPremium(item.getTotalAmount());
                }
            }
            //退保成功 根据承保时的标准来扣减任务保费,标准保费是订单金额
            if (SmConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(appStatus)) {
                if ("01".equals(config)) {
                    //折算
                    item.setTaskPremium(item.getConvertedAmount().abs().negate());
                } else {
                    item.setTaskPremium(item.getTotalAmount().abs().negate());
                }
            }
        });
    }


    public BigDecimal getTaskBigDecimal(String appStatus, Date paymentTime, Integer productId, BigDecimal convertedAmount, BigDecimal totalAmount) {
        String config = null;
        BigDecimal taskPremium = null;
        if (productId != null && paymentTime != null) {
            config = premiumConfigService.queryPremiumTypeByOrderTime(productId, LocalDateTime.ofInstant(paymentTime.toInstant(), ZoneId.systemDefault()));
        }
        if (SmConstants.POLICY_STATUS_SUCCESS.equals(appStatus)) {
            if ("01".equals(config)) {
                //折算
                taskPremium = convertedAmount;
            } else {
                taskPremium = totalAmount;
            }
        }
        //退保成功 根据承保时的标准来扣减任务保费,标准保费是订单金额
        if (SmConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(appStatus)) {
            if ("01".equals(config)) {
                //折算
                taskPremium = convertedAmount.abs().negate();
            } else {
                taskPremium = totalAmount.abs().negate();
            }
        }
        return taskPremium;
    }


    /**
     * 下载订单信息
     *
     * @param query
     * @param response
     */
    @DataSourceReadOnly
    public void downloadOrderCommissionV3(SmOrderQuery query, HttpServletResponse response) {
        log.info("download policies query={}", JSON.toJSONString(query));
        query.setSize(5000);
        UserDetailVO contextUser = bmsService.getContextUserDetail();
        Class<?> clazz;
        Set<String> excludeFileds;
        String fileName;

        fileName = "小额保险提成明细";
        clazz = SmCommissionVO.class;
        if (permissionUtil.isRegionRole(contextUser) || permissionUtil.isBranchRole(contextUser)) {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmCommissionVO.NormalRoleCommission.class);
        } else {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmCommissionVO.Commission.class);
        }
        try (OutputStream os = response.getOutputStream()) {
            int nextPage = 1;
            query.setPage(nextPage);
            PageInfo<SmOrderListVO> pageInfo = getOrderCommissionByPageV3(query);
            if (pageInfo.getTotal() > BaseConstants.EXCEL_ROW_LIMIT) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil download = ExcelBuilderUtil.newInstance()
                    .createSheet("保单列表")
                    .buildSheetHead(clazz, excludeFileds);

            Map<String, List<DictionaryVO>> dictionaryVos = dictionaryMapper.listDictionarys("channel", null, true)
                    .stream().collect(Collectors.groupingBy(DictionaryVO::getCode));

            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = getOrderCommissionByPageV3(query);
                nextPage++;
                List<SmOrderListVO> orderVos = pageInfo.getList();
                //设置产品名称 = 产品名称+计划名称
                orderVos.forEach(smOrderListVO -> smOrderListVO.setProductName(smOrderListVO.getProductName() + smOrderListVO.getPlanName()));
                if (PolicyCodeUtil.isLoadSuccess()) {
                    orderVos.forEach(o -> {
                        o.setPayStatusName(PolicyCodeUtil.getSmPayStatusTypeName(o.getPayStatus()));
                        o.setAppStatusName(PolicyCodeUtil.getSmPolicyStatusName(o.getAppStatus()));
                        o.setChannelName(CollectionUtils.isEmpty(dictionaryVos.get(o.getChannel())) ? "" : dictionaryVos.get(o.getChannel()).get(0).getName());
                    });
                }
                log.info("download policies size={}", orderVos.size());
                download.addSheetData(orderVos);
            }
            download.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }


    /**
     * 验证分页参数
     * 1、列表不能是查询所有
     * 2、每页大小不能超过5000，超过则重新设置为5000
     *
     * @param pageQuery
     */
    private void validatePageParams(DataPermissionPageQuery pageQuery) {
        pageQuery.setAll(false);
        if (pageQuery.getSize() == null) {
            pageQuery.setSize(20);
        }
        if (pageQuery.getPage() == null) {
            pageQuery.setPage(1);
        }
        if (pageQuery.getSize() != null && pageQuery.getSize() > ORDER_LIST_MAX_SIZE) {
            pageQuery.setSize(ORDER_LIST_MAX_SIZE);
        }
    }

    private void initOrderV3BaseQuery(OrderV3BaseQuery query) {
        //验证分页参数
        validatePageParams(query);

        permissionUtil.buildDataPermissionPageQuery(query);
        query.setCreateDateStart(CommonUtil.getStartTimeOfDay(query.getCreateDateStart()));
        query.setCreateDateEnd(CommonUtil.getEndTimeOfDay(query.getCreateDateEnd()));

        //处理 投保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getApplicantdName())) {
            //手机号码
            if (isPhone(query.getApplicantdName())) {
                query.setApplicantdType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getApplicantdName())) {
                query.setApplicantdType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setApplicantdType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setApplicantdName(null);
        }
        //处理被保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getInsuredName())) {
            //手机号码
            if (isPhone(query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setInsuredType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setInsuredName(null);
        }
        if (StringUtils.isEmpty(query.getChannel())) {
            query.setChannel(null);
        }
        if (StringUtils.isEmpty(query.getProductAttrCode())) {
            query.setProductAttrCode(null);
        }

        if (Objects.isNull(query.getOrderType())) {
            query.setOrderType(EnumOrderType.NORMAL.getCode());
        }
    }

    private void initSmOrderV3Query(SmOrderV3Query query) {
        initOrderV3BaseQuery(query);
        query.setPolicyToDateStart(CommonUtil.getStartTimeOfDay(query.getPolicyToDateStart()));
        query.setPolicyToDateEnd(CommonUtil.getEndTimeOfDay(query.getPolicyToDateEnd()));

        query.setReturnVisitTimeStart(CommonUtil.getStartTimeOfDay(query.getReturnVisitTimeStart()));
        query.setReturnVisitTimeEnd(CommonUtil.getEndTimeOfDay(query.getReturnVisitTimeEnd()));

    }

    @DataSourceReadOnly
    public Integer getOrderCntByPageV3(SmOrderV3Query query){
        if (query == null) {
            return null;
        }
        initSmOrderV3Query(query);

        return query.isQueryPage() ? orderMapper.countOrdersV3(query) : 0;
    }

    @DataSourceReadOnly
    public PageInfo<SmOrderV3ListVO> getOrderByPageV3(SmOrderV3Query query) {
        if (query == null) {
            return null;
        }
        initSmOrderV3Query(query);

        // 订单明细
        List<SmOrderV3ListVO> orderVos = orderMapper.listOrdersV3(query);
        //判断是否续保
        doOrderV3ListRenewalStatus(orderVos);
        //计算新的佣金
        //calculateCommission(orderVos);
        //设置加佣比例

        setOrderV3ListCommissionProportion(orderVos);

        //码值转换
        codeToName(orderVos);

        doCarOrderParam(orderVos);

        // 对客户敏感数据进行脱敏
        permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getOrderSensitiveList());

        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderV3ListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderV3ListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            if(query.getForbidCountSwitch()){
                summaryPage.setTotal(0);
                summaryPage.setHasNextPage(true);
            }else{
                summaryPage.setTotal(orderMapper.countOrdersV3(query));
                summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
            }
        }
        return summaryPage;
    }

    private void doOrderV3ListRenewalStatus(List<SmOrderV3ListVO> orderVos){
        List<String> orderIdList = orderVos.stream()
                .map(SmOrderV3ListVO::getFhOrderId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return;
        }
        List<OrderRenewal> list = orderRenewalMapper.listRenewedByNewOrderIdList(orderIdList);
        /*if(CollectionUtils.isEmpty(list)){
            return ;
        }*/
        Map<String ,OrderRenewal> map = list.stream().collect(Collectors.toMap(OrderRenewal::getNewOrderId, e -> e, (e1, e2) -> e1));
        for(SmOrderV3ListVO vo:orderVos){
            if(map.containsKey(vo.getFhOrderId())){
                vo.setRenewed("是");
            }else{
                vo.setRenewed("否");
            }
        }
    }


    /**
     * 设置订单集合的加佣比例
     *
     * @param orderVos
     */
    private void setOrderV3ListCommissionProportion(List<SmOrderV3ListVO> orderVos) {
        List<String> orderIdList = orderVos.stream()
                .map(SmOrderV3ListVO::getFhOrderId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return;
        }
        Map<String, BigDecimal> orderAddCommissionAmountMap = addCommissionQueryService.getLongInsuredPersonAddCommissionMap(orderIdList);
        Map<String, BigDecimal> orderAddCommissionProportionMap = addCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(orderIdList);
        List<String> insuredIdNumbers = orderVos.stream()
                .map(SmOrderV3ListVO::getInsuredIdNumber)
                .distinct()
                .collect(Collectors.toList());
        List<SmAddCommissionDetail> commissionDetails = addCommissionQueryService.getOrderAddCommissionListByIdNumbers(insuredIdNumbers);

        orderVos.forEach(smOrderListVO -> {
            BigDecimal addCommissionAmount = orderAddCommissionAmountMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(smOrderListVO.getFhOrderId(), smOrderListVO.getPolicyNo(), smOrderListVO.getInsuredIdNumber(), smOrderListVO.getTermNum() + ""), BigDecimal.ZERO);
            //退保的情况下加佣金额要为负数
            if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                BigDecimal proportion = orderAddCommissionProportionMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(smOrderListVO.getFhOrderId(), smOrderListVO.getPolicyNo(), smOrderListVO.getInsuredIdNumber(), smOrderListVO.getTermNum() + ""), BigDecimal.ZERO);
                if (proportion != null && proportion.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal totalAmount = Objects.isNull(smOrderListVO.getOriginalAmount()) ? smOrderListVO.getTotalAmount() : smOrderListVO.getOriginalAmount();
                    addCommissionAmount = totalAmount.multiply(proportion).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("-1"));
                } else {
                    addCommissionAmount = addCommissionAmount.multiply(new BigDecimal("-1"));
                }
            } else if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) < 0 && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                addCommissionAmount = addCommissionAmount.abs();
            } else if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) == 0
                    && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                //团险批减单按实际加佣比例显示
                if (smOrderListVO.getDetailCreateTime() != null) {
                    List<SmAddCommissionDetail> details = commissionDetails.stream().filter(x -> (x.getCreateTime().compareTo(LocalDateTime.ofInstant(smOrderListVO.getDetailCreateTime().toInstant(), ZoneId.systemDefault())) < 0
                                    && x.getOrderId().split("_")[0].equals(smOrderListVO.getFhOrderId().split("_")[0])
                                    && x.getInsuredIdNumber().equals(smOrderListVO.getInsuredIdNumber())))
                            .collect(Collectors.toList())
                            .stream()
                            .sorted(Comparator.comparing(SmAddCommissionDetail::getCreateTime))
                            .limit(1).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(details)) {
                        addCommissionAmount = smOrderListVO.getTotalAmount().multiply(details.get(0).getProportion()).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP);
                    }
                }
            }

            smOrderListVO.setAddCommissionAmount(addCommissionAmount);
        });
    }

//    public static final Date SPLI_DATE = DateUtil.parseDate("2021-01-01 00:00:00");
//    private void calculateCommission(List<SmOrderV3ListVO> orderVos) {
//        if (CollectionUtils.isEmpty(orderVos)) {
//            return;
//        }
//        List<String> orderIdList = orderVos.stream().filter(order -> SPLI_DATE.compareTo(order.getCreateTime()) < 0)
//                .map(SmOrderV3ListVO::getFhOrderId)
//                .collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(orderIdList)) {
//            return;
//        }
//        //查询新版佣金，是按照被保人维度汇总的
//        List<SmCommissionDetailPO> commissionDetailList = smOrderCommissionDetailMapper.getByOrderIds(orderIdList);
//        Map<String, BigDecimal> orderCommissionMap = Optional.ofNullable(commissionDetailList)
//                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
//                .orElseGet(ArrayList::new)
//                .stream()
//                .collect(
//                        Collectors.toMap(
//                                item -> String.format("%s-%s", item.getOrderId(), item.getInsuredIdNumber()),
//                                commissionDetail -> Optional.ofNullable(commissionDetail.getPaymentAmount()).orElse(BigDecimal.ZERO),
//                                BigDecimal::add
//                        )
//                );
//
//        List<SmAddCommissionDetail> addCommissionDetailList = smAddCommissionDetailMapper.getByOrderIdList(orderIdList);
//        Map<String, BigDecimal> orderAddCommissionMap = Optional.ofNullable(addCommissionDetailList)
//                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
//                .orElseGet(ArrayList::new)
//                .stream()
//                .collect(
//                        Collectors.toMap(
//                                item -> String.format("%s-%s", item.getOrderId(), item.getInsuredIdNumber()),
//                                commissionDetail -> Optional.ofNullable(commissionDetail.getAddCommissionAmount()).orElse(BigDecimal.ZERO),
//                                BigDecimal::add
//                        )
//                );
//        //查新版本佣金和加佣
//        Map<String, BigDecimal> commissionMap = Stream.concat(orderCommissionMap.keySet().stream(), orderAddCommissionMap.keySet().stream().sequential())
//                .distinct().collect(
//                        Collectors.toMap(
//                                Function.identity()
//                                , orderId -> Optional.ofNullable(orderCommissionMap.get(orderId)).orElse(BigDecimal.ZERO)
//                                        .add(Optional.ofNullable(orderAddCommissionMap.get(orderId)).orElse(BigDecimal.ZERO))
//                        )
//                );
//
//        orderVos.forEach(item -> {
//            if (commissionMap.containsKey(String.format("%s-%s", item.getFhOrderId(), item.getInsuredIdNumber()))) {
//                item.setPaymenyCommission(
//                        Optional.ofNullable(orderCommissionMap.get(String.format("%s-%s", item.getFhOrderId(), item.getInsuredIdNumber()))).orElse(BigDecimal.ZERO)
//                                                  .add(Optional.ofNullable(orderAddCommissionMap.get(String.format("%s-%s", item.getFhOrderId(), item.getInsuredIdNumber())))
//                                                               .orElse(BigDecimal.ZERO))
//                );
////                item.setConvertedAmount();
////                item.setCalNewCommissionType(true);
//            }
//        });
//    }

    /**
     * 折算保费处理
     *
     * @param orderVos
     */
    private void convertedAmountHandler(List<SmOrderV3ListVO> orderVos) {
        Map<String, BigDecimal> personCancelOrderMap = Maps.newHashMap();
        List<String> cancelOrderIdList = orderVos.stream()
                .filter(smOrderListVO -> Objects.equals(smOrderListVO.getProductAttrCode(), SmConstants.PRODUCT_ATTR_PERSON) && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS))
                .map(SmOrderV3ListVO::getFhOrderId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(cancelOrderIdList)) {
            List<SmOrderListVO> orderCommissionByOderIdList = orderMapper.findOrderCommissionByOderIdList(cancelOrderIdList);
            personCancelOrderMap = orderCommissionByOderIdList.stream()
                    .collect(Collectors.toMap(SmOrderListVO::getFhOrderId, SmOrderListVO::getConvertedAmount, (e1, e2) -> e2));
        }
        for (SmOrderV3ListVO orderVo : orderVos) {
            BigDecimal convertedProportion = orderVo.getConvertedProportion();
            String productAttrCode = orderVo.getProductAttrCode();
            BigDecimal totalAmount = orderVo.getTotalAmount();
            BigDecimal convertedAmount = totalAmount;
            if (Objects.nonNull(convertedProportion)) {
                //折算比例不为空
                convertedAmount = totalAmount.multiply(convertedProportion).divide(BigDecimal.valueOf(100));
            }
            String appStatus = orderVo.getAppStatus();
            if (!Objects.equals(appStatus, SmConstants.POLICY_STATUS_SUCCESS) && !Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                //如果不是承保成功和退保成功折算保费为0
                convertedAmount = BigDecimal.ZERO;
            }
            if (Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_GROUP)) {
                    //如果是团险退保成功为负数
                    convertedAmount = convertedAmount.abs().negate();
                }
                if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_PERSON)) {
                    //如果是个险退保成功
                    convertedAmount = personCancelOrderMap.getOrDefault(orderVo.getFhOrderId(), convertedAmount);
                }
            }
            orderVo.setConvertedAmount(convertedAmount);
        }
    }

    private void initAutoOrderV3Query(AutoOrderV3Query query) {
        initOrderV3BaseQuery(query);
        //处理车主查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getOwnerPersonName())) {
            //手机号码
            if (isPhone(query.getOwnerPersonName())) {
                query.setApplicantdType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getOwnerPersonName())) {
                query.setApplicantdType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setApplicantdType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setOwnerPersonName(null);
        }
        //处理支付
        query.setPaymentDateStart(CommonUtil.getStartTimeOfDay(query.getPaymentDateStart()));
        query.setPaymentDateEnd(CommonUtil.getEndTimeOfDay(query.getPaymentDateEnd()));
    }

    public PageInfo<SmOrderV3ListVO> getCarOrderByPageV3(AutoOrderV3Query query) {
        initAutoOrderV3Query(query);
        List<SmOrderV3ListVO> orderVos = orderMapper.listAutoOrdersV3(query);
        if (!CollectionUtils.isEmpty(orderVos)) {
            //对折算保费进行特殊处理
            convertedAmountHandler(orderVos);
            //车险保单下载地址处理
            getMultiPolicyUrl_V3(orderVos);
        }

        //码值转换
        codeToName(orderVos);

        doCarOrderParam(orderVos);
        doOrderV3ListRenewalStatus(orderVos);

        // 对客户敏感数据进行脱敏
        permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getOrderSensitiveList());

        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderV3ListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderV3ListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            summaryPage.setTotal(orderMapper.countAutoOrdersV3(query));
            summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
        }
        return summaryPage;
    }

    /**
     * 车险订单特殊处理
     *
     * @param orderVos
     */
    private void doCarOrderParam(List<SmOrderV3ListVO> orderVos) {
        if (CollectionUtils.isEmpty(orderVos)) {
            return;
        }
        List<String> policyNos = orderVos.stream().map(SmOrderV3ListVO::getPolicyNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policyNos)) {
            return;
        }
        List<AutoOrderPolicy> policyList = autoOrderPolicyMapper.listAutoOrderPolicyBySubPolicyNos(policyNos);
        if (CollectionUtils.isEmpty(policyList)) {
            return;
        }
        policyList.forEach(p -> {
            orderVos.forEach(vo -> {
                if (Objects.equals(vo.getPolicyNo(), p.getSubPolicyNo())) {
                    vo.setBusinessScore(p.getBusinessScore());
                }
            });
        });
    }

    private void codeToName(List<SmOrderV3ListVO> orderVos) {
        Map<String, List<SmCompanySettingVO>> settingMap = getOrderQueryV3CompanySettingMap();
        Map<String, String> categoryMap = dictionaryService.getDictionarysByPage(SmConstants.DICTIONARY_PRODUCT_GROUP, null, null, false)
                .getList().stream()
                .collect(toMap(d -> d.getCode(), DictionaryVO::getName));
        Map<String, String> channelMap = dictionaryService.getDictionarysByPage(DictionaryVO.TYPE_CHANNEL, null, null, true)
                .getList().stream()
                .collect(toMap(d -> d.getCode(), DictionaryVO::getName));
        orderVos.forEach(o -> {
            o.setInsuredRelationship(getCompanySettingValue(settingMap, SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP, o.getCompanyId(), o.getChannel(), o.getInsuredRelationship()));
            o.setApplicantPersonGender(getCompanySettingValue(settingMap, SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX, o.getCompanyId(), o.getChannel(), o.getApplicantPersonGender()));
            o.setInsuredPersonGender(getCompanySettingValue(settingMap, SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX, o.getCompanyId(), o.getChannel(), o.getInsuredPersonGender()));
            o.setProductTypeName(categoryMap.get(o.getProductType()) != null ? categoryMap.get(o.getProductType()) : o.getProductType());
            o.setReturnVisitStatusName(OrderVisitResultEnum.dict(o.getReturnVisitStatus()));
            o.setSubChannel(EnumOrderSubChannel.dict(o.getSubChannel()));
            o.setChannel(channelMap.get(o.getChannel()) != null ? channelMap.get(o.getChannel()) : o.getChannel());
            //s48 绑卡/自动续保
            o.setBindStatusName(OrderBindStatusEnum.dict(o.getBindStatus()));
            o.setSelfInsuredName(Objects.equals(o.getSelfInsured(),SmConstants.LABEL_Y)?"是":"否");
        });
    }


    /**
     * 字典值转换，如果没有配置枚举的
     *
     * @param map
     * @param key
     * @param companyId
     * @param channel
     * @param value
     * @return
     */
    private String getCompanySettingValue(Map<String, List<SmCompanySettingVO>> map, String key, Integer companyId, String channel, String value) {
        List<SmCompanySettingVO> list = map.get(key);
        if (CollectionUtils.isEmpty(list)) {
            return value;
        }
        Optional<SmCompanySettingVO> optional = list.stream().filter(v -> companyId != null && Objects.equals(v.getCompanyId(), companyId)
                && Objects.equals(v.getOptionCode(), value)
                && Objects.equals(v.getChannel(), channel)).findFirst();
        if (optional.isPresent()) {
            return optional.get().getOptionName();
        } else {
            return value;
        }
    }


    private Map<String, List<SmCompanySettingVO>> getOrderQueryV3CompanySettingMap() {
        Map<String, List<SmCompanySettingVO>> map = new HashMap<>();
        CmpySettingQuery settingQuery = new CmpySettingQuery();
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<SmCompanySettingVO> sexSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        map.put(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX, sexSettings);

        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP);
        List<SmCompanySettingVO> relationshipSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        map.put(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP, relationshipSettings);


        settingQuery.setFieldCode(SmConstants.DICTIONARY_PRODUCT_TYPE);
        List<SmCompanySettingVO> productTypeSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        map.put(SmConstants.DICTIONARY_PRODUCT_TYPE, productTypeSettings);
        return map;
    }

    private void getMultiPolicyUrl_V3(List<SmOrderV3ListVO> orderVos) {
        List<String> orderIdList = orderVos.stream().map(SmOrderV3ListVO::getFhOrderId).collect(Collectors.toList());
        List<AutoOrderPolicy> autoOrderPolicyList = autoOrderPolicyMapper.listAutoOrderPolicyByOrderNo(orderIdList);
        if (!CollectionUtils.isEmpty(autoOrderPolicyList)) {
            Map<String, String> downloadUrlMap = new HashMap<String, String>();
            autoOrderPolicyList.stream().forEach(policy -> {
                String url = policy.getPolicyUrl();
                String existUrl = downloadUrlMap.get(policy.getOrderNo());
                if (existUrl != null) {
                    downloadUrlMap.put(policy.getOrderNo(), existUrl.concat("|").concat(policy.getPolicyUrl()));
                } else {
                    downloadUrlMap.put(policy.getOrderNo(), policy.getPolicyUrl());
                }
            });

            orderVos.stream().forEach(order -> {
                if (downloadUrlMap.get(order.getFhOrderId()) != null) {
                    order.setDownloadURL(downloadUrlMap.get(order.getFhOrderId()));
                }
            });
        }
    }

    /**
     * 下载订单信息
     *
     * @param query
     * @param response
     */
    @DataSourceReadOnly
    public void downloadOrdersV3(SmOrderV3Query query, HttpServletResponse response) {
        log.info("download policies query={}", JSON.toJSONString(query));
        query.setSize(5000);
        Class<SmOrderV3ListVO> clazz = SmOrderV3ListVO.class;
        Set<String> excludeFileds;
        if (!Objects.equals(query.getTabType(), "OTHER")) {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmOrderV3ListVO.NoOther.class);
        } else {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmOrderV3ListVO.Other.class);
        }
        filterDownLoadAuthParam(excludeFileds);
        String fileName = "保险订单明细";

        try (OutputStream os = response.getOutputStream()) {

            int nextPage = 1;
            query.setPage(nextPage);
            PageInfo<SmOrderV3ListVO> pageInfo = getOrderByPageV3(query);
            if (pageInfo.getTotal() > BaseConstants.EXCEL_ROW_LIMIT) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil download = ExcelBuilderUtil.newInstance()
                    .createSheet("保单列表")
                    .buildSheetHead(clazz, excludeFileds);

            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = getOrderByPageV3(query);
                nextPage++;
                List<SmOrderV3ListVO> orderVos = pageInfo.getList();
                if (PolicyCodeUtil.isLoadSuccess()) {
                    orderVos.forEach(o -> {
                        o.setPayStatusName(PolicyCodeUtil.getSmPayStatusTypeName(o.getPayStatus()));
                        o.setAppStatusName(PolicyCodeUtil.getSmPolicyStatusName(o.getAppStatus()));
                    });
                }
                log.info("download policies size={}", orderVos.size());
                download.addSheetData(orderVos);
            }
            download.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    private void filterDownLoadAuthParam(Set<String> excludeFileds) {
        List<ModuleVO> modules = ThreadUserUtil.USER_MODULES_TL.get();

        if (modules.stream().noneMatch(m -> Objects.equals(m.getModuleCode(), bmsConfig.getOrderPaymentAmount()))) {
            excludeFileds.add("paymenyCommission");
        }

    }

    /**
     * 下载车险订单信息
     *
     * @param query
     * @param response
     */
    @DataSourceReadOnly
    public void downloadCarOrdersV3(AutoOrderV3Query query, HttpServletResponse response) {
        log.info("download policies query={}", JSON.toJSONString(query));
        query.setSize(5000);
//        Set<String> excludeFileds = new HashSet<>();
//        filterDownLoadAuthParam(excludeFileds);
//        excludeFileds.add("insuredAmount");
//        excludeFileds.add("payingType");
//        excludeFileds.add("payingYears");
//        excludeFileds.add("validPeriod");
//        excludeFileds.add("returnVisitStatusName");
//        excludeFileds.add("returnVisitTime");
//        excludeFileds.add("downloadURL");
//        excludeFileds.add("bindStatusName");

        Class<SmOrderV3ListVO> clazz = SmOrderV3ListVO.class;
        Set<String> excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmOrderV3ListVO.CarOrder.class);
        String fileName = "车险订单明细";

        try (OutputStream os = response.getOutputStream()) {

            int nextPage = 1;
            query.setPage(nextPage);
            PageInfo<SmOrderV3ListVO> pageInfo = getCarOrderByPageV3(query);
            if (pageInfo.getTotal() > BaseConstants.EXCEL_ROW_LIMIT) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil download = ExcelBuilderUtil.newInstance()
                    .createSheet("保单列表")
                    .buildSheetHead(clazz, excludeFileds);

            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = getCarOrderByPageV3(query);
                nextPage++;
                List<SmOrderV3ListVO> orderVos = pageInfo.getList();
                Set<String> orderNos = orderVos.stream().map(SmOrderV3ListVO::getFhOrderId).collect(Collectors.toSet());
                List<AutoOrderRisk> orderRiskList = autoOrderRiskMapper.listAutoOrderRiskByOrderNos(orderNos);

                if (PolicyCodeUtil.isLoadSuccess()) {
                    orderVos.forEach(o -> {
                        if (!CollectionUtils.isEmpty(orderRiskList)) {
                            List<AutoOrderRisk> list = orderRiskList.stream().filter(r -> Objects.equals(o.getFhOrderId(), r.getOrderNo())).collect(Collectors.toList());
                            //增加车险保额信息
                            o.setCarAmountStr(toCarAmountStr(list));
                        }
                        o.setPayStatusName(PolicyCodeUtil.getSmPayStatusTypeName(o.getPayStatus()));
                        o.setAppStatusName(PolicyCodeUtil.getSmPolicyStatusName(o.getAppStatus()));
                    });
                }
                log.info("download policies size={}", orderVos.size());
                download.addSheetData(orderVos);
            }
            download.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    private String toCarAmountStr(List<AutoOrderRisk> risks) {
        if (CollectionUtils.isEmpty(risks)) {
            return null;
        }
        StringBuilder str = new StringBuilder("");
        for (AutoOrderRisk k : risks) {
            str.append(k.getRiskName()).append(k.getAmount()).append(",");
        }
        if (str.length() > 0) {
            str.deleteCharAt(str.length() - 1);
        }
        return str.toString();
    }


    /**
     * @param query
     * @return
     */
    public SmOrderSummaryVO getOrderSummaryV3(SmOrderV3Query query) {
        if (query == null) {
            return null;
        }
        initSmOrderV3Query(query);
        Optional<SmOrderSummaryVO> summaryV4op = Optional.ofNullable(orderMapper.getOrderSummaryV4(query));
        Optional<SmOrderSummaryVO> summaryV3op = Optional.ofNullable(orderMapper.getOrderSummaryV3(query));


        SmOrderSummaryVO result = new SmOrderSummaryVO();
        result.setPaymentAmount(summaryV3op.map(SmOrderSummaryVO::getPaymentAmount).orElse(BigDecimal.ZERO).add(summaryV4op.map(SmOrderSummaryVO::getPaymentAmount).orElse(BigDecimal.ZERO)));
        result.setSettlementAmount(summaryV3op.map(SmOrderSummaryVO::getSettlementAmount).orElse(BigDecimal.ZERO).add(summaryV4op.map(SmOrderSummaryVO::getSettlementAmount).orElse(BigDecimal.ZERO)));
        result.setAddCommissionAmount(summaryV3op.map(SmOrderSummaryVO::getAddCommissionAmount).orElse(BigDecimal.ZERO).add(summaryV4op.map(SmOrderSummaryVO::getAddCommissionAmount).orElse(BigDecimal.ZERO)));
        result.setConvertedAmount(summaryV3op.map(SmOrderSummaryVO::getConvertedAmount).orElse(BigDecimal.ZERO).add(summaryV4op.map(SmOrderSummaryVO::getConvertedAmount).orElse(BigDecimal.ZERO)));
        result.setOrderAmount(summaryV3op.map(SmOrderSummaryVO::getOrderAmount).orElse(BigDecimal.ZERO).add(summaryV4op.map(SmOrderSummaryVO::getOrderAmount).orElse(BigDecimal.ZERO)));
        result.setDistributionAmount(summaryV3op.map(SmOrderSummaryVO::getDistributionAmount).orElse(BigDecimal.ZERO).add(summaryV4op.map(SmOrderSummaryVO::getDistributionAmount).orElse(BigDecimal.ZERO)));
        result.setTotalQty(summaryV3op.map(SmOrderSummaryVO::getTotalQty).orElse(0) + summaryV4op.map(SmOrderSummaryVO::getTotalQty).orElse(0));

        return result;
    }

    /**
     * @param query
     * @return
     */
    public SmOrderSummaryVO getAutoOrderSummaryV3(AutoOrderV3Query query) {
        if (query == null) {
            return null;
        }
        initAutoOrderV3Query(query);
        return orderMapper.getAutoOrderSummaryV3(query);
    }


    /********************************/
    public void initOrderQuery(SmOrderQuery query) {
        //验证分页参数
        validatePageParams(query);

        permissionUtil.buildDataPermissionPageQuery(query);
        query.setCreateDateStart(CommonUtil.getStartTimeOfDay(query.getCreateDateStart()));
        query.setCreateDateEnd(CommonUtil.getEndTimeOfDay(query.getCreateDateEnd()));
        query.setPolicyFromDateStart(CommonUtil.getStartTimeOfDay(query.getPolicyFromDateStart()));
        query.setPolicyFromDateEnd(CommonUtil.getEndTimeOfDay(query.getPolicyFromDateEnd()));
        query.setPolicyToDateStart(CommonUtil.getStartTimeOfDay(query.getPolicyToDateStart()));
        query.setPolicyToDateEnd(CommonUtil.getEndTimeOfDay(query.getPolicyToDateEnd()));
        query.setPaymentDateStart(CommonUtil.getStartTimeOfDay(query.getPaymentDateStart()));
        query.setPaymentDateEnd(CommonUtil.getEndTimeOfDay(query.getPaymentDateEnd()));
        query.setAccountDateStart(CommonUtil.getStartTimeOfDay(query.getAccountDateStart()));
        query.setAccountDateEnd(CommonUtil.getEndTimeOfDay(query.getAccountDateEnd()));

        //处理 投保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getApplicantdName())) {
            //手机号码
            if (isPhone(query.getApplicantdName())) {
                query.setApplicantdType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getApplicantdName())) {
                query.setApplicantdType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setApplicantdType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setApplicantdName(null);
        }
        //处理被保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getInsuredName())) {
            //手机号码
            if (isPhone(query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setInsuredType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setInsuredName(null);
        }
        if (StringUtils.isEmpty(query.getChannel())) {
            query.setChannel(null);
        }
        if (StringUtils.isEmpty(query.getProductAttrCode())) {
            query.setProductAttrCode(null);
        }
        if (Objects.isNull(query.getOrderType())) {
            query.setOrderType(EnumOrderType.NORMAL.getCode());
        }

    }

    public void initPgOrderQuery(SmOrderQuery query) {
        //验证分页参数
        validatePageParams(query);

        permissionUtil.buildDataPermissionPageQuery(query);
        query.setCreateDateStart(CommonUtil.getStartTimeOfDay(query.getCreateDateStart()));
        query.setCreateDateEnd(CommonUtil.getEndTimeOfDay(query.getCreateDateEnd()));
        query.setPolicyFromDateStart(CommonUtil.getStartTimeOfDay(query.getPolicyFromDateStart()));
        query.setPolicyFromDateEnd(CommonUtil.getEndTimeOfDay(query.getPolicyFromDateEnd()));
        query.setPolicyToDateStart(CommonUtil.getStartTimeOfDay(query.getPolicyToDateStart()));
        query.setPolicyToDateEnd(CommonUtil.getEndTimeOfDay(query.getPolicyToDateEnd()));
        query.setPaymentDateStart(CommonUtil.getStartTimeOfDay(query.getPaymentDateStart()));
        query.setPaymentDateEnd(CommonUtil.getEndTimeOfDay(query.getPaymentDateEnd()));
        query.setAccountDateStart(CommonUtil.getStartTimeOfDay(query.getAccountDateStart()));
        query.setAccountDateEnd(CommonUtil.getEndTimeOfDay(query.getAccountDateEnd()));

        //处理 投保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getApplicantdName())) {
            //手机号码
            if (isPhone(query.getApplicantdName())) {
                query.setApplicantdType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getApplicantdName())) {
                query.setApplicantdType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setApplicantdType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setApplicantdName(null);
        }
        //处理被保人查询条件
        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getInsuredName())) {
            //手机号码
            if (isPhone(query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.CELLPHONE.getCode());
            }
            //证件号码
            else if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                query.setInsuredType(EnumKeywordSearchType.ID_CARD.getCode());
            } else {
                query.setInsuredType(EnumKeywordSearchType.NAME.getCode());
            }
        } else {
            query.setInsuredName(null);
        }
        if (StringUtils.isEmpty(query.getChannel())) {
            query.setChannel(null);
        }
        if (StringUtils.isEmpty(query.getProductAttrCode())) {
            query.setProductAttrCode(null);
        }
    }
    private boolean isPhone(String str) {
        return str.length() == 11 && Pattern.matches("[0-9]*", str);
    }

    /**
     * 查询小额保险订单分页信息 包括对账信息
     *
     * @param query
     * @return
     */
    public PageInfo<SmOrderListVO> getOrderIncludeReconciliationByPage(SmOrderQuery query) {
        PageInfo<SmOrderListVO> pageInfo = getOrderByPage(query);
        List<SmOrderListVO> smOrderListVOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(smOrderListVOList)) {
            return pageInfo;
        }
        //获取保单集合
        List<String> policyNoList = smOrderListVOList.stream()
                .map(this::getNoLinePolicyNo)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        //获取保单最新的表单信息
        Map<String, ReconciledPolicyDTO> smReconciliationNewestPolicy = smReconciliationPolicyMapper.getSmReconciliationNewestPolicy(null, null, null, policyNoList);

        //设置对账结果
        smOrderListVOList.forEach(smOrderListVO -> {
            String policyKey = smReconciliationPolicyMapper.getPolicyKey(smOrderListVO.getPolicyNo(), smOrderListVO.getAppStatus());
            ReconciledPolicyDTO reconciledPolicyDTO = smReconciliationNewestPolicy.get(policyKey);
            EnumReconciliationStatusType reconciliationResult = getReconciliationResult(reconciledPolicyDTO);
            if (Objects.nonNull(reconciliationResult)) {
                smOrderListVO.setReconciliationResult(reconciliationResult.getDescribe());
            }
        });
        return pageInfo;
    }


    public Integer getOrderCommissionCntByPage(SmOrderQuery query){
        return getOrderCnt(query);
    }


    /**
     * 获取保司对账结果
     * <p>
     * 、对账结果字段
     * （1）未对账：没有启动对账
     * （2）已对账：对账后我司与保司一致
     * （3）已平账：已进行平账处理
     * （4）多账：对账后，我司有的保单号，保司对账清单中无；
     * （短账：列表中体现不出，保单号保司有我司无的情况；如果补单后进行了平账，显示状态为已平账；）
     * （5）金额不一致，长款
     * （6）金额不一致，短款
     * （7）投保人不一致
     * （8）保单状态不一致
     * 注意（4）（5）（6）（7）（8）这些情况，进行平账操作后，状态会更新成为“已平账”；
     *
     * @param reconciledPolicyDTO
     * @return
     */
    public EnumReconciliationStatusType getReconciliationResult(ReconciledPolicyDTO reconciledPolicyDTO) {
        //未对账
        if (Objects.isNull(reconciledPolicyDTO)) {
            return EnumReconciliationStatusType.Unreconciled;
        }
        //已对账
        Integer reconciliationStatus = reconciledPolicyDTO.getReconciliationStatus();
        Integer balanceAccountFlag = reconciledPolicyDTO.getBalanceAccountFlag();
        //（4）（5）（6）（7）（8）这些情况，进行平账操作后，状态会更新成为“已平账”
        if (Objects.equals(balanceAccountFlag, 1)) {
            return EnumReconciliationStatusType.BalanceAccount;
        }
        return EnumReconciliationStatusType.getByCode(reconciliationStatus);
    }

    /**
     * 获取没有下划线的保单号
     *
     * @param smOrderListVO
     * @return
     */
    private String getNoLinePolicyNo(SmOrderListVO smOrderListVO) {
        String policyNo = smOrderListVO.getPolicyNo();
        if (org.apache.commons.lang3.StringUtils.isBlank(policyNo)) {
            return policyNo;
        }
        int index = policyNo.indexOf("_");
        if (index > 0) {
            return policyNo.substring(0, index);
        } else {
            return policyNo;
        }
    }

    /**
     * 查询小额保险订单分页信息
     *
     * @param query
     * @return
     */
    @DataSourceReadOnly
    public PageInfo<SmOrderListVO> getOrderByPage(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initOrderQuery(query);
        List<SmOrderListVO> orderVos = null;
        if (query.getCommission() == null) {
            orderVos = orderMapper.listOrders(query);
            if (!CollectionUtils.isEmpty(orderVos)) {
                Map<String, BigDecimal> personCancelOrderMap = Maps.newHashMap();
                List<String> cancelOrderIdList = orderVos.stream()
                        .filter(smOrderListVO -> Objects.equals(smOrderListVO.getProductAttrCode(), SmConstants.PRODUCT_ATTR_PERSON) && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS))
                        .map(SmOrderListVO::getFhOrderId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(cancelOrderIdList)) {
                    List<SmOrderListVO> orderCommissionByOderIdList = orderMapper.findOrderCommissionByOderIdList(cancelOrderIdList);
                    personCancelOrderMap = orderCommissionByOderIdList.stream()
                            .collect(Collectors.toMap(SmOrderListVO::getFhOrderId, SmOrderListVO::getConvertedAmount, (e1, e2) -> e2));
                }

                //车险多子保单下载地址
                getMultiPolicyUrl(orderVos);

                //对折算保费进行特殊处理
                for (SmOrderListVO orderVo : orderVos) {
                    BigDecimal convertedProportion = orderVo.getConvertedProportion();
                    String productAttrCode = orderVo.getProductAttrCode();
                    BigDecimal totalAmount = orderVo.getTotalAmount();
                    BigDecimal convertedAmount = totalAmount;
                    if (Objects.nonNull(convertedProportion)) {
                        //折算比例不为空
                        convertedAmount = totalAmount.multiply(convertedProportion).divide(BigDecimal.valueOf(100));
                    }
                    String appStatus = orderVo.getAppStatus();
                    if (!Objects.equals(appStatus, SmConstants.POLICY_STATUS_SUCCESS) && !Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                        //如果不是承保成功和退保成功折算保费为0
                        convertedAmount = BigDecimal.ZERO;
                    }
                    if (Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                        if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_GROUP)) {
                            //如果是团险退保成功为负数
                            convertedAmount = convertedAmount.abs().negate();
                        }
                        if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_PERSON)) {
                            //如果是个险退保成功
                            convertedAmount = personCancelOrderMap.getOrDefault(orderVo.getFhOrderId(), convertedAmount);
                        }
                    }
                    orderVo.setConvertedAmount(convertedAmount);
                }
            }

        } else {
            orderVos = orderMapper.listOrderCommission(query);
            //计算任务保费字段
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderVos)) {
                caculateTaskPremium(orderVos);
                //设置加佣比例
                setOrderListCommissionProportion(orderVos);
            }
        }

        doXjChannelCommission(orderVos);

        CmpySettingQuery settingQuery = new CmpySettingQuery();
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<SmCompanySettingVO> sexSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP);
        List<SmCompanySettingVO> relationshipSettings = cmpySettingMapper.listCompanySettings(settingQuery);

        orderVos.forEach(o -> {
            Optional<SmCompanySettingVO> optional = sexSettings.stream()
                    .filter(v -> o.getCompanyId() != null && (v.getCompanyId() == o.getCompanyId())
                            && Objects.equals(v.getOptionCode(), o.getApplicantPersonGender())
                            && Objects.equals(v.getChannel(), o.getChannel())
                    ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setApplicantPersonGender(smCompanySettingVO.getOptionName()));
            optional = sexSettings.stream().filter(v -> o.getCompanyId() != null && v.getCompanyId() == o.getCompanyId()
                    && Objects.equals(v.getOptionCode(), o.getInsuredPersonGender())
                    && Objects.equals(v.getChannel(), o.getChannel())
            ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setInsuredPersonGender(smCompanySettingVO.getOptionName()));
            optional = relationshipSettings.stream().filter(v -> o.getCompanyId() != null && v.getCompanyId() == o.getCompanyId()
                    && Objects.equals(v.getOptionCode(), o.getInsuredRelationship())
                    && Objects.equals(v.getChannel(), o.getChannel())
            ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setInsuredRelationship(smCompanySettingVO.getOptionName()));
            o.setSelfInsuredName(Objects.equals(o.getSelfInsured(),SmConstants.LABEL_Y)?"是":"否");

        });

        // 对客户敏感数据进行脱敏
        if (query.getCommission() == null) {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderList());
        } else {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderCms());
        }

        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            // 如果禁止统计总数 则不执行count查询
            if(query.getForbidCountSwitch()){
                summaryPage.setTotal(0);
                summaryPage.setHasNextPage(true);
            }else{
                summaryPage.setTotal(query.getCommission() == null ? orderMapper.countOrders(query) : orderMapper.countOrderCommission(query));
                summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
            }
        }
        return summaryPage;
    }

    @DataSourceReadOnly
    public Integer getOrderCnt(SmOrderQuery query){
        if (null == query){
            return 0;
        }
        // 需要query中指定重要查询条件
        initOrderQuery(query);
        return query.getCommission() == null ? orderMapper.countOrders(query) : orderMapper.countOrderCommission(query);
    }

    private void setEndorsementNo(List<SmOrderListVO> orderVos) {
        if (CollectionUtils.isEmpty(orderVos)) {
            return;
        }
        List<String> policyNoList = orderVos.stream().map(SmOrderListVO::getPolicyNo).collect(Collectors.toList());
        List<SmOrderItem> orderItems = smOrderItemMapper.listByPolicyList(policyNoList);

        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        Map<String, SmOrderItem> policyMap = orderItems.stream().collect(toMap(SmOrderItem::getPolicyNo, Function.identity(), (x, y) -> y));

        orderVos.forEach(
                item -> {
                    if (Objects.nonNull(item.getPolicyNo()) && item.getPolicyNo().contains("_")) {
                        item.setEndorsementNo(item.getPolicyNo());
                        String originPolicyNo = Optional.ofNullable(policyMap.get(item.getPolicyNo())).map(SmOrderItem::getThPolicyNo).orElse(item.getPolicyNo());
                        item.setPolicyNo(
                                StringUtils.isEmpty(originPolicyNo) ? item.getPolicyNo() : originPolicyNo
                        );
                    }
                }
        );
    }

    /**
     * 获取小鲸渠道的新的佣金
     *
     * @param orderVos
     */
    private void doXjChannelCommission(List<SmOrderListVO> orderVos) {
        List<Integer> productIds = productQueryService.listProductIdByLabelTypeValue(ProductLabelEnum.LABEL_CALC_NEW_COMMISSION.getCode(), SmConstants.LABEL_Y);
        List<SmOrderListVO> vos = orderVos.stream().filter(order -> productIds.contains(order.getProductId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vos)) {
            return;
        }
        doNewCommission(vos, getNewCommission(vos));
    }

    /**
     * 获取小鲸渠道的新的佣金
     *
     * @param orderVos
     * @return
     */
    private List<CommissionQueryResultDTO> getNewCommission(List<SmOrderListVO> orderVos) {

        List<String> fhOrderIdList = orderVos.stream().map(SmOrderListVO::getFhOrderId).distinct().collect(Collectors.toList());
        return commissionQueryService.getCommissionByOrderId(fhOrderIdList);
    }

    private void doNewCommission(List<SmOrderListVO> orderVos, List<CommissionQueryResultDTO> newCommissionList) {
        if (CollectionUtils.isEmpty(newCommissionList)) {
            return;
        }
        //老的提成列表只有普通佣金
        orderVos.forEach(item -> {
            Optional<CommissionQueryResultDTO> opt = newCommissionList.stream().filter(n -> Objects.equals(n.getOrderId(), item.getFhOrderId())
                    && Objects.equals(n.getInsuredIdNumber(), item.getInsuredIdNumber())
                    && Objects.equals(n.getPolicyNo(), item.getPolicyNo())
                    && Objects.equals(n.getPolicyStatus(), item.getAppStatus())
                    && Objects.equals(n.getBusinessType(), CommissionBusinessTypeEnum.COMMON.getCode())
                    && Objects.equals(n.getTermNum(), Integer.valueOf(1))).findFirst();
            if (opt.isPresent()) {
                CommissionQueryResultDTO dto = opt.get();
                item.setPaymentProportion(dto.getPaymentRate());
                item.setPaymenyCommission(dto.getPaymentAmount());
                item.setSettlementProportion(dto.getSettlementRate());
                item.setSettlementCommission(dto.getSettlementAmount());
                item.setConvertedProportion(dto.getConversionRate());
                item.setConvertedAmount(dto.getConversionAmount());
            }
        });


    }


    private SmTaskPremiumConfig getPremiumConfig(List<SmTaskPremiumConfig> configs, LocalDateTime localDateTime) {
        return configs.stream()
                .filter(i -> localDateTime.compareTo(i.getEndTime()) < 1 && localDateTime.compareTo(i.getStartTime()) > -1)
                .findAny().orElse(null);
    }

    /**
     * 设置订单集合的加佣比例
     *
     * @param orderVos
     */
    private void setOrderListCommissionProportion(List<SmOrderListVO> orderVos) {
        List<String> orderIdList = orderVos.stream()
                .map(SmOrderListVO::getFhOrderId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, BigDecimal> orderAddCommissionAmountMap = addCommissionQueryService.getInsuredPersonAddCommissionMap(orderIdList);
        Map<String, BigDecimal> orderAddCommissionProportionMap = addCommissionQueryService.getInsuredPersonAddCommissionProportionMap(orderIdList);
        List<String> insuredIdNumbers = orderVos.stream()
                .map(SmOrderListVO::getInsuredIdNumber)
                .distinct()
                .collect(Collectors.toList());
        List<SmAddCommissionDetail> commissionDetails = addCommissionQueryService.getOrderAddCommissionListByIdNumbers(insuredIdNumbers);


        orderVos.forEach(smOrderListVO -> {
            BigDecimal addCommissionAmount = orderAddCommissionAmountMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(smOrderListVO.getFhOrderId(), smOrderListVO.getPolicyNo(), smOrderListVO.getInsuredIdNumber()), BigDecimal.ZERO);
            //退保的情况下加佣金额要为负数
            if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                BigDecimal proportion = orderAddCommissionProportionMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(smOrderListVO.getFhOrderId(), smOrderListVO.getPolicyNo(), smOrderListVO.getInsuredIdNumber()), BigDecimal.ZERO);
                if (proportion != null && proportion.compareTo(BigDecimal.ZERO) > 0) {
                    addCommissionAmount = smOrderListVO.getTotalAmount().multiply(proportion).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP);
                } else {
                    addCommissionAmount = addCommissionAmount.multiply(new BigDecimal("-1"));
                }
            } else if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) < 0 && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                addCommissionAmount = addCommissionAmount.abs();
            } else if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) == 0
                    && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                //团险批减单按实际加佣比例显示
                if (smOrderListVO.getDetailCreateTime() != null) {
                    List<SmAddCommissionDetail> details = commissionDetails.stream().filter(x -> (x.getCreateTime().compareTo(LocalDateTime.ofInstant(smOrderListVO.getDetailCreateTime().toInstant(), ZoneId.systemDefault())) < 0
                                    && x.getOrderId().split("_")[0].equals(smOrderListVO.getFhOrderId().split("_")[0])
                                    && x.getInsuredIdNumber().equals(smOrderListVO.getInsuredIdNumber())))
                            .collect(Collectors.toList())
                            .stream()
                            .sorted(Comparator.comparing(SmAddCommissionDetail::getCreateTime))
                            .limit(1).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(details)) {
                        addCommissionAmount = smOrderListVO.getTotalAmount().multiply(details.get(0).getProportion()).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP);
                    }
                }
            }

            smOrderListVO.setAddCommissionAmount(addCommissionAmount);
        });
    }

    private void getMultiPolicyUrl(List<SmOrderListVO> orderVos) {
        List<String> orderIdList = orderVos.stream().map(SmOrderListVO::getFhOrderId).collect(Collectors.toList());
        List<AutoOrderPolicy> autoOrderPolicyList = autoOrderPolicyMapper.listAutoOrderPolicyByOrderNo(orderIdList);
        if (!CollectionUtils.isEmpty(autoOrderPolicyList)) {
            Map<String, String> downloadUrlMap = new HashMap<String, String>();
            autoOrderPolicyList.stream().forEach(policy -> {
                String url = policy.getPolicyUrl();
                String existUrl = downloadUrlMap.get(policy.getOrderNo());
                if (existUrl != null) {
                    downloadUrlMap.put(policy.getOrderNo(), existUrl.concat("|").concat(policy.getPolicyUrl()));
                } else {
                    downloadUrlMap.put(policy.getOrderNo(), policy.getPolicyUrl());
                }
            });

            orderVos.stream().forEach(order -> {
                if (downloadUrlMap.get(order.getFhOrderId()) != null) {
                    order.setDownloadURL(downloadUrlMap.get(order.getFhOrderId()));
                }
            });
        }
    }

    /**
     * 查询车险订单分页信息
     *
     * @param query
     * @return
     */
    @DataSourceReadOnly
    public PageInfo<SmOrderListVO> getCarOrderByPage(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initOrderQuery(query);
        List<SmOrderListVO> orderVos = null;
        if (query.getCommission() == null) {
            orderVos = orderMapper.listOrders(query);
            if (!CollectionUtils.isEmpty(orderVos)) {
                Map<String, BigDecimal> personCancelOrderMap = Maps.newHashMap();
                List<String> cancelOrderIdList = orderVos.stream()
                        .filter(smOrderListVO -> Objects.equals(smOrderListVO.getProductAttrCode(), SmConstants.PRODUCT_ATTR_PERSON) && Objects.equals(smOrderListVO.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS))
                        .map(SmOrderListVO::getFhOrderId)
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(cancelOrderIdList)) {
                    List<SmOrderListVO> orderCommissionByOderIdList = orderMapper.findOrderCommissionByOderIdList(cancelOrderIdList);
                    personCancelOrderMap = orderCommissionByOderIdList.stream()
                            .collect(Collectors.toMap(SmOrderListVO::getFhOrderId, SmOrderListVO::getConvertedAmount, (e1, e2) -> e2));
                }

                //车险多子保单下载地址
                getMultiPolicyUrl(orderVos);

                //对折算保费进行特殊处理
                for (SmOrderListVO orderVo : orderVos) {
                    BigDecimal convertedProportion = orderVo.getConvertedProportion();
                    String productAttrCode = orderVo.getProductAttrCode();
                    BigDecimal totalAmount = orderVo.getTotalAmount();
                    BigDecimal convertedAmount = totalAmount;
                    if (Objects.nonNull(convertedProportion)) {
                        //折算比例不为空
                        convertedAmount = totalAmount.multiply(convertedProportion).divide(BigDecimal.valueOf(100));
                    }
                    String appStatus = orderVo.getAppStatus();
                    if (!Objects.equals(appStatus, SmConstants.POLICY_STATUS_SUCCESS) && !Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                        //如果不是承保成功和退保成功折算保费为0
                        convertedAmount = BigDecimal.ZERO;
                    }
                    if (Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                        if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_GROUP)) {
                            //如果是团险退保成功为负数
                            convertedAmount = convertedAmount.abs().negate();
                        }
                        if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_PERSON)) {
                            //如果是个险退保成功
                            convertedAmount = personCancelOrderMap.getOrDefault(orderVo.getFhOrderId(), convertedAmount);
                        }
                    }
                    orderVo.setConvertedAmount(convertedAmount);
                }
            }

        } else {
            orderVos = orderMapper.listOrderCommission(query);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderVos)) {
                setOrderListCommissionProportion(orderVos);
            }
        }
        CmpySettingQuery settingQuery = new CmpySettingQuery();
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<SmCompanySettingVO> sexSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP);
        List<SmCompanySettingVO> relationshipSettings = cmpySettingMapper.listCompanySettings(settingQuery);
        List<String> mobiles = orderVos.stream().filter(o -> o.getRecommendUserId() != null && o.getEntryDate() != null).map(SmOrderListVO::getRecommendUserMobile)
                .distinct().collect(Collectors.toList());

        orderVos.forEach(o -> {
            Optional<SmCompanySettingVO> optional = sexSettings.stream()
                    .filter(v -> o.getCompanyId() != null && (v.getCompanyId() == o.getCompanyId())
                            && Objects.equals(v.getOptionCode(), o.getApplicantPersonGender())
                            && Objects.equals(v.getChannel(), o.getChannel())
                    ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setApplicantPersonGender(smCompanySettingVO.getOptionName()));
            optional = sexSettings.stream().filter(v -> o.getCompanyId() != null && v.getCompanyId() == o.getCompanyId()
                    && Objects.equals(v.getOptionCode(), o.getInsuredPersonGender())
                    && Objects.equals(v.getChannel(), o.getChannel())
            ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setInsuredPersonGender(smCompanySettingVO.getOptionName()));
            optional = relationshipSettings.stream().filter(v -> o.getCompanyId() != null && v.getCompanyId() == o.getCompanyId()
                    && Objects.equals(v.getOptionCode(), o.getInsuredRelationship())
                    && Objects.equals(v.getChannel(), o.getChannel())
            ).findFirst();
            optional.ifPresent(smCompanySettingVO -> o.setInsuredRelationship(smCompanySettingVO.getOptionName()));

        });

        // 对客户敏感数据进行脱敏
        if (query.getCommission() == null) {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderList());
        } else {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderCms());
        }

        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            summaryPage.setTotal(query.getCommission() == null ? orderMapper.countOrders(query) : orderMapper.countOrderCommission(query));
            summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
        }
        return summaryPage;
    }


    @MaskMethod
    public PageInfo<SmOrderNewVO> getOrderByPageV2(SmOrderQuery query) {

        if (query == null) {
            return null;
        }
        initOrderQuery(query);

        PageInfo<String> pages = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> orderMapper.listFhOrderIdOrders(query));

        if (CollectionUtils.isEmpty(pages.getList())) {
            List<SmOrderNewVO> smOrderNewVOS = orderMapper.listOrdersV2(pages.getList());
            smOrderNewVOS.forEach(sm -> sm.setPayStatusName(EnumOrderPayStatus.getDesc(sm.getPayStatus())));
            PageInfo<SmOrderNewVO> objectPageInfo = new PageInfo<>();
            BeanUtils.copyProperties(pages, objectPageInfo);
            objectPageInfo.setList(smOrderNewVOS);
            return objectPageInfo;
        }
        return new PageInfo<>();
    }

    /**
     * 查询需要同步订单信息
     *
     * @param payStatus
     * @param appStatus
     * @param startDate
     * @param endDate
     * @return
     */
    public List<String> getJobChangeOrderIds(String payStatus, String appStatus, Date startDate, Date endDate, String channel) {
        return orderMapper.listJobChangeOrderIds(payStatus, appStatus, startDate, endDate, channel);
    }

    /**
     * 查询需要同步订单信息(保单 被保人保单信息)
     *
     * @param payStatus
     * @param appStatus
     * @param startDate
     * @param endDate
     * @return
     */
    public List<SmOrderListVO> getChangOrderList(String payStatus, String appStatus, Date startDate, Date endDate, String channel) {
        return orderMapper.listJobChangeOrders(payStatus, appStatus, startDate, endDate, channel);
    }

    /**
     * 查询订单短信发送信息
     *
     * @param fhOrderId
     * @return
     */
    public List<SmOrderSmsNotifyVO> getOrderSmsNotifyList(String fhOrderId) {
        return orderMapper.getOrderSmsNotifyByOrderId(fhOrderId);
    }

    /**
     * 查询订单短信发送信息
     *
     * @param fhOrderId
     * @return
     */
    public List<SmOrderSmsNotifyVO> getGroupOrderNotifyMessage(String fhOrderId) {
        return orderMapper.getGroupOrderNotifyMessage(fhOrderId);
    }

    /**
     * 查询补单记录
     *
     * @return
     */
    public PageInfo<SmOrderSupmtRecordVO> getSupplementOrder(OrderSupmtQuery query) {
        if (query != null) {
            PageHelper.startPage(query.getPage(), query.getSize());
            query.setCreateDateStart(CommonUtil.getStartTimeOfDay(query.getCreateDateStart()));
            query.setCreateDateEnd(CommonUtil.getEndTimeOfDay(query.getCreateDateEnd()));
            query.setSpmDateStart(CommonUtil.getStartTimeOfDay(query.getSpmDateStart()));
            query.setSpmDateEnd(CommonUtil.getEndTimeOfDay(query.getSpmDateEnd()));
        }
        return new PageInfo<>(supmtMapper.listSmOrderSupmtRecords(query));
    }

    /**
     * 查询订单批改记录列表
     *
     * @return
     */
    public PageInfo<SmOrderCorrectRecordVO> getOrderCorrectRecords(SmOrderCorrectQuery query) {
        if (query != null) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        return new PageInfo<>(orderMapper.listOrderCorrectRecords(query));
    }

    /**
     * 订单汇总信息缓存优化
     *
     * @param query
     * @return
     */
    public SmOrderSummaryVO getOrderSummary(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initOrderQuery(query);
        return query.getCommission() == null ? orderMapper.getOrderSummary(query) : orderMapper.getOrderCommissionSummary(query);
    }

    @Autowired
    ProductSpecialRuleProperties productSpecialRuleProperties;

    /**
     * 检验订单所有被保人是否查过产品最大购买份数限制
     *
     * @param orderId
     */
    public void checkUnpayOrderInsuredProductBuyLimit(String orderId) {
        List<SmBaseOrderInsuredVO> baseOrderInsureds = orderMapper.listBaseOrderInsuredByOrderId(orderId);
        if (CollectionUtils.isEmpty(baseOrderInsureds)) {
            return;
        }
        int qty = baseOrderInsureds.get(0).getQty();
        int productId = baseOrderInsureds.get(0).getProductId();
        //验证产品是否在线状态
        //productService.validProductState(productId);

        SmProductDetailVO product = productService.getProductById(productId);
        Date startTime = baseOrderInsureds.get(0).getStartTime();
        log.info("开始校验保单重复投保:{},{}",orderId,startTime);
        if (Objects.equals(product.getProductAttrCode(), "group")) {
            List<String> idNumbers = baseOrderInsureds.stream().map(SmBaseOrderInsuredVO::getInsuredIdNumber).collect(Collectors.toList());
            checkBuyLimit(productId, qty, startTime, idNumbers);
            return;
        }
        checkUnpayOrderInsuredProductBuyLimit(product, baseOrderInsureds);
    }

    public void checkUnpayOrderInsuredProductBuyLimit(SmProductDetailVO product, List<SmBaseOrderInsuredVO> baseOrderInsureds) {
        int productId = product.getId();
        int productLimit = product.getBuyLimit();

        String type = productSpecialRuleProperties.queryProductTypeHandlerById(productId);
        List<Integer> sameProduct = productSpecialRuleProperties.queryProductCategoryById(productId);

        baseOrderInsureds.forEach(ins -> {
            int holdPolicyQty = productSpecialRuleProperties.routerToHandler(type)
                    .countProductsPolicyQty(
                            sameProduct, ins.getInsuredIdNumber(), null, DateUtil.format(ins.getStartTime(), DateUtil.CN_LONG_FORMAT)
                    );
            if (ins.getQty() + holdPolicyQty > productLimit) {
                throw new BizException(ExcptEnum.OVER_PRODUCT_LIMIT.getCode(), "被保人[" + ins.getPersonName() + "]投保该产品已达上限，保存失败");
            }
        });
    }

    /**
     * 购买限制
     *
     * @param productId
     * @param qty
     * @param startTime
     * @param idNumbers
     */
    public void checkBuyLimit(Integer productId, int qty, Date startTime, List<String> idNumbers) {
        log.info("开始校验被保人是否重复投保:{},{},{},{}",productId,qty,startTime,idNumbers);
        if (CollectionUtils.isEmpty(idNumbers)) {
            log.warn("被保人身份证列表:{}", productId);
            return;
        }
        if (startTime == null) {
            log.warn("生效期为空:{}", productId);
            return;
        }
        List<Integer> sameProduct = productSpecialRuleProperties.queryProductCategoryById(productId);
        if (CollectionUtils.isEmpty(sameProduct)) {
            log.info("未配置重复投保校验相关信息:{}",productId);
            return;
        }
        SmProductDetailVO product = productService.getProductById(productId);
        if (product == null) {
            log.warn("产品信息不存在：{}", productId);
            return;
        }
        String productAttrCode = product.getProductAttrCode();
        boolean ignoreFlag = checkSwitch(productAttrCode);
        if (ignoreFlag) {
            log.warn("投保校验已跳过：{}", productId);
            return;
        }
        int productLimit = product.getBuyLimit();

        String type = productSpecialRuleProperties.queryProductTypeHandlerById(productId);

        productSpecialRuleProperties.routerToHandler(type).batchCheck(sameProduct, idNumbers, startTime, productLimit, qty);
    }

    @Autowired
    private RedisUtil<String, Object> redisUtil;

    private boolean checkSwitch(String productAttrCode) {
        String key = "apply:check:ignore:" + productAttrCode;
        Object ignoreFlag = redisUtil.get(key);
        return Objects.equals("1", ignoreFlag);
    }

    public void openCheck(String productAttrCode) {
        String key = "apply:check:ignore:" + productAttrCode;
        redisUtil.remove(key);
    }

    public void closeCheck(String productAttrCode) {
        String key = "apply:check:ignore:" + productAttrCode;
        redisUtil.set(key, "1");
    }

    /**
     * 下载订单信息
     *
     * @param query
     * @param response
     */
    @DataSourceReadOnly
    public void downloadOrders(SmOrderQuery query, HttpServletResponse response) {
        log.info("download policies query={}", JSON.toJSONString(query));
        query.setSize(5000);
        if (query.getCommission() != null && query.getCommission()) {
            query.setSize(30000);
        }
        boolean isCommission = query.getCommission() != null && query.getCommission();
        UserDetailVO contextUser = bmsService.getContextUserDetail();
        Class<?> clazz;
        Set<String> excludeFileds;
        String fileName;
        if (isCommission) {
            fileName = "小额保险提成明细";
            clazz = SmCommissionVO.class;
            if (permissionUtil.isRegionRole(contextUser) || permissionUtil.isBranchRole(contextUser)) {
                excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmCommissionVO.NormalRoleCommission.class);
            } else {
                excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmCommissionVO.Commission.class);
            }
        } else {
            fileName = "小额保险订单明细";
            clazz = SmOrderListVO.class;
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmOrderListVO.NonCommission.class);
        }
        try (OutputStream os = response.getOutputStream()) {

            int nextPage = 1;
            query.setPage(nextPage);
            PageInfo<SmOrderListVO> pageInfo = getOrderByPage(query);
            if (pageInfo.getTotal() > BaseConstants.EXCEL_ROW_LIMIT) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil download = ExcelBuilderUtil.newInstance()
                    .createSheet("保单列表")
                    .buildSheetHead(clazz, excludeFileds);

            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = getOrderByPage(query);
                nextPage++;
                List<SmOrderListVO> orderVos = pageInfo.getList();
                //设置产品名称 = 产品名称+计划名称
                orderVos.forEach(smOrderListVO -> smOrderListVO.setProductName(smOrderListVO.getProductName() + smOrderListVO.getPlanName()));
                if (PolicyCodeUtil.isLoadSuccess()) {
                    orderVos.forEach(o -> {
                        o.setPayStatusName(PolicyCodeUtil.getSmPayStatusTypeName(o.getPayStatus()));
                        o.setAppStatusName(PolicyCodeUtil.getSmPolicyStatusName(o.getAppStatus()));
                    });
                }
                log.info("download policies size={}", orderVos.size());
                download.addSheetData(orderVos);
            }
            download.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    /**
     * 把泛华订单同步到系统
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSupplementOrder(SmOrderSupmtDTO dto) {

        String userId = HttpRequestUtil.getUserId();
        lock(userId, 30, "上一次补单未处理完，请稍后重试！");
        try {
            insertSupplementOrder(dto, HttpRequestUtil.getUserId());
        } finally {
            unlock(userId);
        }

    }

    /**
     * 把泛华订单同步到系统
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSupplementOrder(SmOrderSupmtDTO dto, String userId) {
        List<SmOrderListVO> orders = orderMapper.listOrderInsuredDetailByOrderId(dto.getFhOrderId());
        if (!orders.isEmpty()) {
            throw new BizException(ExcptEnum.ORDER_ERROR_801007);
        }
        SmCreateOrderSubmitRequest createDto = OrderConvertor.mapperSumptCreateOrderDTO(dto);
        if (!Objects.equals(createDto.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
            throw new BizException(ExcptEnum.SMUPT_ORDER_ERROR_UNPAY);
        }
        orderMapper.insertOrder(createDto);
        orderMapper.insertOrderApplicant(createDto);
        orderMapper.insertOrderInsured(createDto);
        orderMapper.updateOrderPaymentTime(dto.getFhOrderId());
        SmOrderSupmtRecordDTO recordDTO = new SmOrderSupmtRecordDTO();
        recordDTO.setFhOrderId(dto.getFhOrderId());
        recordDTO.setResult("补单成功");
        recordDTO.setCreateBy(userId);
        supmtMapper.insertSmOrderSupmtRecords(recordDTO);
    }

    private void checkVisitStateValue(SmOrderCorrectDTO dto, SmOrderPolicy smOrderPolicy) {
        String oldValue = dto.getOldValue();
        String newValue = dto.getNewValue();
        if (org.apache.commons.lang3.StringUtils.isBlank(oldValue)) {
            //回访前-> 空值 回访后-> 回访成功 回访失败
            if (!Objects.equals(newValue, OrderVisitResultEnum.SUCCESS.getCode()) && !Objects.equals(newValue, OrderVisitResultEnum.FAILED.getCode())) {
                throw new MSBizNormalException("", "变更前后信息不合法！");
            }
            return;
        }
        if (Objects.equals(oldValue, OrderVisitResultEnum.UNDO.getCode())) {
            //回访前-> 未回访  回访后-> 回访成功 回访失败
            if (!Objects.equals(newValue, OrderVisitResultEnum.SUCCESS.getCode()) && !Objects.equals(newValue, OrderVisitResultEnum.FAILED.getCode())) {
                throw new MSBizNormalException("", "变更前后信息不合法！");
            }
            //判断变更前信息是否一致
            String visitStatus = smOrderPolicy.getVisitStatus();
            if (!Objects.equals(visitStatus, oldValue)) {
                throw new MSBizNormalException("", "变更前信息不一致！");
            }
            return;
        }
        if (Objects.equals(oldValue, OrderVisitResultEnum.FAILED.getCode())) {
            //回访前-> 回访失败  回访后-> 回访成功
            if (!Objects.equals(newValue, OrderVisitResultEnum.SUCCESS.getCode())) {
                throw new MSBizNormalException("", "变更前后信息不合法！");
            }
            //判断变更前信息是否一致
            String visitStatus = smOrderPolicy.getVisitStatus();
            if (!OrderVisitResultEnum.getFailCodeList().contains(visitStatus)) {
                throw new MSBizNormalException("", "变更前信息不一致！");
            }
            //变更为回访成功时，需要填写回访时间
            if (Objects.isNull(dto.getVisitTime())) {
                throw new MSBizNormalException("", "变更为回访成功时，回访时间不能为空！");
            }
        }

    }

    public void correctReturnVisitStatus(SmOrderCorrectDTO dto, SmOrderToCorrectVO orderToCorrectByPolicyNo) {
        String policyNo = dto.getPolicyNo();
        String newValue = dto.getNewValue();
        SmOrderToCorrectVO correctVO = orderMapper.getOrderToCorrectByPolicyNo(policyNo, null, null, null);
        //回访状态，未回访undo,回访成功 success,首访不成功 firstFailed,再访不成功 failedAgain
        SmOrderPolicy smOrderPolicy = smOrderPolicyMapper.getOrderPolicyByPolicyNo(policyNo, correctVO.getChannel());
        if (Objects.isNull(smOrderPolicy)) {
            throw new MSBizNormalException("", "回访记录不存在");
        }
        //校验变更前信息选项值是否正确
        checkVisitStateValue(dto, smOrderPolicy);
        //信息批改
        smOrderPolicy.setVisitStatus(newValue);
        if (Objects.equals(newValue, OrderVisitResultEnum.SUCCESS.getCode())) {
            smOrderPolicy.setVisitTime(dto.getVisitTime());
        }
        smOrderPolicyMapper.updateByPrimaryKey(smOrderPolicy);

        SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, orderToCorrectByPolicyNo);
        orderMapper.insertOrderCorrect(cDto);
    }

    /**
     * 订单批改接口
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void correctOrder(SmOrderCorrectDTO dto) {
        String fieldCode = dto.getFieldCode();
        if (Objects.equals(fieldCode, CorrectProject.RENEWAL_STATUS.getCode())) {
            //如果续保状态的批改
            SmOrderToCorrectVO orderToCorrectByPolicyNo = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(), null, null, null);
            if (Objects.isNull(orderToCorrectByPolicyNo)) {
                throw new MSBizNormalException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：保单号不存在");
            }
            //保单续保状态批改
            OrderRenewal orderRenewal = renewalOrderService.correctOrderRenewalStatus(dto);
            SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, orderRenewal.getOldOrderId(), orderRenewal.getIdNumber());
            orderMapper.insertOrderCorrect(cDto);
            return;
        }
        if (Objects.equals(fieldCode, CorrectProject.RETURN_VISIT_STATUS.getCode())) {
            SmOrderToCorrectVO orderToCorrectByPolicyNo = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(), null, null, null);
            if (Objects.isNull(orderToCorrectByPolicyNo)) {
                throw new MSBizNormalException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：保单号不存在");
            }
            //如果回访状态的批改
            correctReturnVisitStatus(dto, orderToCorrectByPolicyNo);
            return;

        }
        if (Objects.equals(fieldCode, CorrectProject.CANCEL_AMOUNT.getCode())) {
            //如果退保金额的批改
            SmOrderToCorrectVO orderToCorrectByPolicyNo = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(), null, null, null);
            if (Objects.isNull(orderToCorrectByPolicyNo)) {
                throw new MSBizNormalException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：保单号不存在");
            }
            if (!Objects.equals(orderToCorrectByPolicyNo.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                throw new MSBizNormalException("", "当前保单状态不是退保成功，不能修改");
            }

            if (Objects.isNull(orderToCorrectByPolicyNo.getSurrenderTime())
                    || !DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT).equals(orderToCorrectByPolicyNo.getSurrenderTime().format(DateTimeFormatter.ofPattern(DateUtil.CN_YEAR_MONTH_FORMAT)))) {
                throw new BizException(ExcptEnum.CHANGE_CANCEL_AMT_CROSS_MONTH.getCode(), ExcptEnum.CHANGE_CANCEL_AMT_CROSS_MONTH.getMsg());
            }
            //correctCancelAmount(dto, orderToCorrectByPolicyNo);
            correctNewCancelAmount(dto, orderToCorrectByPolicyNo);
            return;
        }
        SmOrderToCorrectVO toCorrectVO = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(),
                fieldCode, dto.getOldValue(), dto.getIdNumber());
        if (toCorrectVO == null) {
            toCorrectVO = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(), null, null, null);
            if (toCorrectVO == null) {
                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：保单号不存在，请重新输入");
            } else {
                //非推荐人修改，抛异常
                if (!Objects.equals(fieldCode, CorrectProject.RECOMMEND_NAME.getCode())) {
                    throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：变更前信息填写有误，请核对后重新输入");
                }
            }
        }
        String fhOrderId = toCorrectVO.getFhOrderId();
        dto.setFhOrderId(fhOrderId);

        if (Objects.equals(fieldCode, CorrectProject.APP_NAME.getCode())) {
            correctApplicantName(dto);
        } else if (Objects.equals(fieldCode, CorrectProject.APP_MOBILE.getCode())) {
            if (dto.getNewValue() == null || !CommonUtil.isMobile(dto.getNewValue())) {
                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：变更后信息填写有误，请核对后重新输入");
            }
            orderMapper.updateOrderErrorAppInfo(dto);
            orderMapper.updateOrderErrorCommissionInfo(dto);
        } else if (Objects.equals(fieldCode, CorrectProject.APP_ID_NUMBER.getCode())) {
            fillSexAndBirthday(toCorrectVO.getCompanyId(), dto);
            correctApplicantIdCard(dto);
        } else if (Objects.equals(fieldCode, CorrectProject.INS_NAME.getCode())) {
            correctInsuredName(dto);
//            orderMapper.updateOrderErrorInsInfo(dto);
//            orderMapper.updateOrderErrorCommissionInfo(dto);
        } else if (Objects.equals(fieldCode, CorrectProject.INS_MOBILE.getCode())) {
            if (dto.getNewValue() == null || !CommonUtil.isMobile(dto.getNewValue())) {
                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：变更后信息填写有误，请核对后重新输入");
            }
            orderMapper.updateOrderErrorInsInfo(dto);
            orderMapper.updateOrderErrorCommissionInfo(dto);
        } else if (Objects.equals(fieldCode, CorrectProject.INS_ID_NUMBER.getCode())) {
            fillSexAndBirthday(toCorrectVO.getCompanyId(), dto);
            //add by zhangjian 2022-06-20
            //需要验证新的身份证号是否已经存在，不然会导致存在同一个保单号下有多个相同的身份证号的可能
            correctInsuredIdCard(dto);
//            int ret = orderMapper.updateOrderErrorInsInfo(dto);
//            if (ret == 0) {
//                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：记录无变更，请核对后重新输入");
//            }
//            orderMapper.updateOrderErrorCommissionInfo(dto);
//            //删除批改被保人后的原被保人加佣信息
//            smAddCommissionDetailMapper.deleteAddCommissionInsuredIdNumber(dto.getFhOrderId(), dto.getPolicyNo(), dto.getOldValue());
//            //需要更新新的佣金表数据
//            updateNewCommissionInfo(dto);
//            //修改sm_order_item表中身份证信息
//            smOrderItemMapper.updateIdNumber(dto.getFhOrderId(), dto.getOldValue(), dto.getNewValue());
        } else if (Objects.equals(fieldCode, CorrectProject.OCCUPATION_CODE.getCode())) {
            orderMapper.updateOrderErrorInsInfo(dto);
        } else if (Objects.equals(fieldCode, CorrectProject.RECOMMEND_NAME.getCode())) {
            correctRecommend(dto, toCorrectVO);
            //2020-07-08 add by zhangjian 统一工号
            UserPost userPost = userPostMapper.selectUserPostByJobCode(dto.getNewRecommendJobCode());
            if (userPost == null) {
                throw new BizException(ExcptEnum.JOB_CODE_NOT_EXIST);
            } else if (userPost.getEmployeeStatus() == 8 || userPost.getServiceStatus() == 1 || userPost.getStopDate().before(new Date())) {
                throw new BizException(ExcptEnum.JOB_CODE_SERVICE_TIME_IS_CLOSE);
            }
            dto.setRecommendPostName(userPost.getPostName());
            dto.setNewRecommendMainJobNumber(userPost.getMainJobNumber());
            dto.setNewRecommendOrgCode(userPost.getOrgCode());
            //Date accountTime = orderMapper.getOrderCommissionAccountTime(fhOrderId, toCorrectVO.getInsuredIdNumber());
            orderMapper.updateOrderErrorRecommendInfo(dto);
            orderMapper.updateOrderErrorCommissionInfo(dto);
            //更新新的佣金表
            updateNewCommissionInfo(dto);
            // 报表跑批重跑时间需要小于今天
//            if (accountTime != null && accountTime.compareTo(DateUtil.getBeginOfDay(new Date())) < 0) {
//                reportService.reBatchReport(accountTime);
//            }
        } else if (CorrectProject.onlyCurrentMonth(fieldCode)) {
            correctMonthField(dto, toCorrectVO);
        }
        SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, toCorrectVO);
        orderMapper.insertOrderCorrect(cDto);
    }

    /**
     * 农保-批改流程
     * @param dto
     */
    public void correct(SmOrderCorrectDTO dto){
        if(Objects.equals(dto.getFieldCode(), CorrectProject.INS_ID_NUMBER.getCode())){
            correctInsuredIdCard(dto);
            return;
        }
        if(Objects.equals(dto.getFieldCode(), CorrectProject.INS_NAME.getCode())){
            correctInsuredName(dto);
            return;
        }
        if(Objects.equals(dto.getFieldCode(), CorrectProject.APP_NAME.getCode())){
            correctApplicantName(dto);
            return;
        }
        if(Objects.equals(dto.getFieldCode(), CorrectProject.APP_ID_NUMBER.getCode())){
            correctApplicantIdCard(dto);
            return;
        }
        if(Objects.equals(dto.getFieldCode(), CorrectProject.INS_RELATION.getCode())){
            correctInsuredRelation(dto);
            return;
        }
        log.error("批改类型暂不支持:{}",JSON.toJSONString(dto));
    }
    public void correctApplicantIdCard(SmOrderCorrectDTO dto){
        if(!Objects.equals(dto.getFieldCode(), CorrectProject.APP_ID_NUMBER.getCode())){
            log.warn("只处理投保人证件号变更事件");
            return;
        }
        log.info("开始变更投保人证件号:{}",dto);
        int r1 = orderMapper.updateOrderErrorAppInfo(dto);
        int r2 = orderMapper.updateOrderErrorCommissionInfo(dto);
        log.info("投保人证件号变更完成:{},{},{}",r1,r2);
    }

    public void correctApplicantName(SmOrderCorrectDTO dto){
        if(!Objects.equals(dto.getFieldCode(), CorrectProject.APP_NAME.getCode())){
            log.warn("只处理投保人名字变更事件");
            return;
        }
        log.info("开始变更投保人名字:{}",dto);
        int r1 = orderMapper.updateOrderErrorAppInfo(dto);
        int r2 = orderMapper.updateOrderErrorCommissionInfo(dto);
        log.info("投保人名字变更完成:{}",r1,r2);
    }
    /**
     * 被保人证件号变更
     * @param dto
     */
    /**
     * 被保人与投保人的关系
     * @param dto
     */
    public void correctInsuredRelation(SmOrderCorrectDTO dto){
        if(!Objects.equals(dto.getFieldCode(), CorrectProject.INS_RELATION.getCode())){
            log.warn("只处理变更投被保人关系");
            return;
        }
        log.info("开始变更投被保人关系:{}",dto);
        int ret = orderMapper.updateOrderErrorInsInfo(dto);
        log.info("开始变更投被保人关系:{}",ret);
    }


    /**
     * 被保人证件号变更
     * @param dto
     */
    public void correctInsuredIdCard(SmOrderCorrectDTO dto){
        if(!Objects.equals(dto.getFieldCode(), CorrectProject.INS_ID_NUMBER.getCode())){
            log.warn("只处理被保人证件号变更事件");
            return;
        }
        log.info("开始变更被保人证件号:{}",dto);
        int ret = orderMapper.updateOrderErrorInsInfo(dto);
        if (ret == 0) {
            log.warn("数据记录无变更");
            return;
        }
        int r1 = orderMapper.updateOrderErrorCommissionInfo(dto);
        //删除批改被保人后的原被保人加佣信息
        int r2 =smAddCommissionDetailMapper.deleteAddCommissionInsuredIdNumber(dto.getFhOrderId(), dto.getPolicyNo(), dto.getOldValue());
        //新佣金规则处理
        //需要更新新的佣金表数据
        updateNewCommissionInfo(dto);
        //修改sm_order_item表中身份证信息
        int r3=smOrderItemMapper.updateIdNumber(dto.getFhOrderId(), dto.getOldValue(), dto.getNewValue());
        log.info("被保人证件号变更完成:{},{},{}",r1,r2,r3);
    }

    /**
     * 被保人名字变更
     * @param dto
     */
    public void correctInsuredName(SmOrderCorrectDTO dto){
        if(!Objects.equals(dto.getFieldCode(), CorrectProject.INS_NAME.getCode())){
            log.warn("只处理被保人名字变更事件");
            return;
        }
        log.info("开始变更被保人名字:{}",dto);
        int r1 = orderMapper.updateOrderErrorInsInfo(dto);
        int r2 = orderMapper.updateOrderErrorCommissionInfo(dto);
        log.info("被保人名字变更完成:{}",r1,r2);
    }

    /**
     * 推荐人变更流程校验
     * 2024-11-06 请使用V1版本
     * @param dto
     * @param toCorrectVO
     */
    @Deprecated
    public void correctRecommend(SmOrderCorrectDTO dto, SmOrderToCorrectVO toCorrectVO) {
        //修改推荐人，变更前信息如果选择无则实际推荐人为空或空串,或者选择的推荐人与实际推荐人不相符，则抛出异常
        //if (Objects.equals(dto.getOldValue(), "无") || !Objects.equals(toCorrectVO.getRecommendId(), dto.getOldValue())) {
        //if (!Objects.equals(dto.getOldValue(), "无") && !Objects.equals(toCorrectVO.getRecommendId(), dto.getOldValue())) {
        WxUserVO oldUser = null;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(toCorrectVO.getRecommendId())) {
            oldUser = userMapper.getUserByUserId(toCorrectVO.getRecommendId());
        }
        //如果选择无，则判断订单记录的推荐人是否在用户表中存在，存在则需要抛出异常，选择非无，则判断订单记录的推荐人与输入的信息是否一致
        if ((Objects.equals(dto.getOldValue(), "无") && oldUser != null)
                || (!Objects.equals(dto.getOldValue(), "无") && !Objects.equals(toCorrectVO.getRecommendId(), dto.getOldValue()))) {
            throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：变更前信息填写有误，请核对后重新输入");
        }
        if (userMapper.getUserByUserId(dto.getNewValue()) == null) {
            throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：推荐人工号错误");
        }
        //modify by zhangjian 2022-03-14 推荐人从无批改为员工A，记账时间改为批改成功的时间；推荐人从员工A批改为员工B，记账时间不变；
        if (Objects.equals(toCorrectVO.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                && (org.apache.commons.lang3.StringUtils.isBlank(toCorrectVO.getRecommendId()) || oldUser == null)) {
            dto.setPaymentTime(new Date());
        }

        //推荐人从员工A批改为员工B, 不能跨月修改数据；
        if (org.apache.commons.lang3.StringUtils.isNotBlank(toCorrectVO.getRecommendId()) && Objects.nonNull(oldUser)) {
            if (Objects.isNull(toCorrectVO.getPaymentTime()) || !DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT).equals(DateUtil.format(toCorrectVO.getPaymentTime(), DateUtil.CN_YEAR_MONTH_FORMAT))) {
                throw new BizException(ExcptEnum.POLICY_CHNAGE_CROSS_MONTH.getCode(), ExcptEnum.POLICY_CHNAGE_CROSS_MONTH.getMsg());
            }
        }

    }

    /**
     * 农保变更规则：只要小鲸系统校验通过以后，农保则允许变更
     * @param dto 变更规则
     * @param toCorrectVO
     */
    public void correctRecommendV1(SmOrderCorrectDTO dto, SmOrderToCorrectVO toCorrectVO) {
        //修改推荐人，变更前信息如果选择无则实际推荐人为空或空串,或者选择的推荐人与实际推荐人不相符，则抛出异常
        WxUserVO oldUser = null;
        if (StringUtils.isNotBlank(toCorrectVO.getRecommendId())) {
            oldUser = userMapper.getUserByUserId(toCorrectVO.getRecommendId());
        }
        //如果选择无，则判断订单记录的推荐人是否在用户表中存在，存在则需要抛出异常，选择非无，则判断订单记录的推荐人与输入的信息是否一致
        if (Objects.equals(toCorrectVO.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS) && (StringUtils.isBlank(toCorrectVO.getRecommendId()) || oldUser == null)) {
            dto.setPaymentTime(new Date());
        }
    }


    private void correctNewCancelAmount(SmOrderCorrectDTO dto, SmOrderToCorrectVO order) {
        try {
            BigDecimal cancelAmount = null;
            if (dto.getNewValue() != null) {
                cancelAmount = BigDecimal.valueOf(Float.parseFloat(dto.getNewValue())).abs();
            }
            //修改sm_order_policy金额
            SmOrderPolicy beforeUpdate = smOrderPolicyService.updatePolicyCancelAmount(order.getFhOrderId(), order.getPolicyNo(), order.getChannel(), cancelAmount, order.getSurrenderTime(), Boolean.FALSE);
            //记录批改记录
            SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, order);
            //退保历史金额以表里实际金额为准
            if (Objects.nonNull(beforeUpdate) && beforeUpdate.getCancelAmount() != null) {
                cDto.setOldValue(beforeUpdate.getCancelAmount().toString());
            } else {
                cDto.setOldValue(null);
            }
            orderMapper.insertOrderCorrect(cDto);

            //佣金计算触发
            eventBusEngine.publish(new OrderCommissionChangeEvent(order.getFhOrderId(), Boolean.FALSE, Boolean.TRUE));

        } catch (NumberFormatException e) {
            log.error("变更后信息转换为退保金额失败", e);
            throw new MSBizNormalException("", "变更后信息需要为数值金额。");
        }
    }

    /**
     * 当前只需要被保人身份证信息，和推荐人信息
     */
    public void updateNewCommissionInfo(SmOrderCorrectDTO dto) {

        UpdateCommissionInfoDTO updateDTO = new UpdateCommissionInfoDTO();
        updateDTO.setOrderId(dto.getFhOrderId());
        updateDTO.setPolicyNo(dto.getPolicyNo());
        updateDTO.setOldValue(dto.getOldValue());
        updateDTO.setNewValue(dto.getNewValue());
        updateDTO.setPaymentTime(dto.getPaymentTime());
        if (Objects.equals(dto.getFieldCode(), CorrectProject.RECOMMEND_NAME.getCode())) {
            if (Objects.equals(dto.getOldValue(), "无")) {
                updateDTO.setOldValue(null);
            }
            updateDTO.setFieldCode(UpdateCommissionInfoDTO.RECOMMEND_NAME);
        } else if (Objects.equals(dto.getFieldCode(), CorrectProject.INS_ID_NUMBER.getCode())) {

            updateDTO.setFieldCode(UpdateCommissionInfoDTO.INS_ID_NUMBER);
        }

        commissionManagerService.updateCommissionInfo(updateDTO);
    }

    /**
     * 批改订单金额
     *
     * @param dto
     * @param orderToCorrectByPolicyNo
     */
    @Deprecated
    private void correctCancelAmount(SmOrderCorrectDTO dto, SmOrderToCorrectVO orderToCorrectByPolicyNo) {
        //如果退保金额的批改  修改提成表明细  修改sm_order_policy
        try {
            //修改提成明细表sm_order_commission退保记录订单金额
            BigDecimal cancelAmount = BigDecimal.valueOf(Float.parseFloat(dto.getNewValue())).abs();
            orderMapper.updateOrderCommissionCancelAmount(dto.getPolicyNo(), cancelAmount.negate(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            //修改sm_commission_detail退保记录订单金额
            smOrderCommissionDetailMapper.updateOrderCommissionCancelAmount(dto.getPolicyNo(), cancelAmount, SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            //修改sm_order_converted_premium的折算保费
            convertedPremiumMapper.updateOrderAmount(dto.getPolicyNo(), cancelAmount, SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            //修改sm_order_policy金额
            SmOrderPolicy smOrderPolicy = smOrderPolicyMapper.getOrderPolicyByPolicyNo(dto.getPolicyNo(), orderToCorrectByPolicyNo.getChannel());
            if (Objects.nonNull(smOrderPolicy)) {
                smOrderPolicy.setCancelAmount(cancelAmount);
                smOrderPolicyMapper.updateByPrimaryKey(smOrderPolicy);
            }


            SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, orderToCorrectByPolicyNo);
            orderMapper.insertOrderCorrect(cDto);
        } catch (NumberFormatException e) {
            log.error("变更后信息转换为退保金额失败", e);
            throw new MSBizNormalException("", "变更后信息需要为数值金额。");
        }
    }

    /**
     * 批改只能修改当月数据的字段
     *
     * @param correctDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void correctMonthField(SmOrderCorrectDTO correctDTO, SmOrderToCorrectVO vo) {

        if (!validDate(vo.getCreateTime().toLocalDate())) {
            throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(),
                    ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：该保单已进入佣金结算环节，不可修改。如需修改请联系数仓及技术人员处理");
        }
        //订单金额
        if (Objects.equals(correctDTO.getFieldCode(), CorrectProject.SUBMIT_TIME.getCode())) {

            SmCommissionSettingVO settingVo = cmsMapper.getCommissionSettingByPlanId(
                    vo.getPlanId(), DateUtil.parseDate(correctDTO.getNewValue(), DateUtil.CN_LONG_FORMAT));
            orderMapper.updateOrderErrorInfo(correctDTO);
            if (Objects.nonNull(settingVo)) {
                orderMapper.updateOrderCommissionOnly(correctDTO.getFhOrderId(), settingVo.getId() + "");
                orderMapper.deleteOrderCommission(vo.getFhOrderId());
                eventBusEngine.post(new OrderCommissionChangeEvent(vo.getFhOrderId()));
            } else {
                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(),
                        ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：该时间段未配置提成，请先配置提成再进行批改操作");
            }

        }
    }

    private boolean validDate(LocalDate orderDate) {

        YearMonth month = YearMonth.now();
        LocalDate monthStart = month.atDay(1);
        LocalDate monthEnd = month.atDay(month.lengthOfMonth());
        //今天是月初 修改时间可以修改上个月的数据
        if (monthStart.compareTo(LocalDate.now()) == 0) {
            monthStart = monthStart.minusMonths(1L);
        }

        return orderDate.compareTo(monthStart) >= 0 && orderDate.compareTo(monthEnd) <= 0;
    }

    private void modifyCustomerAdmin() {
        //todo
    }

    /**
     * 订单批改接口(批改成退保)
     *
     * @param dto
     */
//    @Transactional(rollbackFor = Exception.class)
    public void correctOrderAppStatus(@Valid SmOrderCorrectCancelDTO dto) {
        if (!Objects.equals(dto.getNewValue(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "目前只能批改成退保成功");
        }

        SmOrderToCorrectVO toCorrectVO = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(), dto.getFieldCode(), dto.getOldValue(), null);

        if (toCorrectVO == null) {
            toCorrectVO = orderMapper.getOrderToCorrectByPolicyNo(dto.getPolicyNo(), null, null, null);
            if (toCorrectVO == null) {
                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：保单号不存在，请重新输入");
            } else {
                throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：变更前信息填写有误，请核对后重新输入");
            }
        }

//        如果是雇主责任险或者是团险的话，将禁止他的团险整单退改功能
        if (Objects.equals(toCorrectVO.getProductAttrCode(), EnumProductAttr.GROUP.getCode()) || Objects.equals(toCorrectVO.getProductAttrCode(), EnumProductAttr.EMPLOYER.getCode())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "团险整单退改功能已关闭！");
        }


        if (Objects.equals(EnumChannel.FH.getCode(), toCorrectVO.getChannel())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "泛华渠道不允许做退保批改，请使用补单功能");
        }

        if (Boolean.TRUE.equals(dto.getIsPart()) && CollectionUtils.isEmpty(dto.getInsuredIdNumbers())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "部分退保被保人不能为空！");
        }
        if (Objects.equals(toCorrectVO.getProductAttrCode(), SmConstants.PRODUCT_ATTR_EMPLOYER)
                || Objects.equals(toCorrectVO.getProductAttrCode(), SmConstants.PRODUCT_ATTR_GROUP)) {
            correctGroupOrderAppStatus(toCorrectVO, dto);
        } else {
            orderMapper.updateOrderSurrenderTimeByPolicyNo(dto.getPolicyNo(), dto.getInsuredIdNumbers());
            smOrderItemMapper.updateOrderItemSurrenderTimeByPolicyNo(dto.getPolicyNo(), dto.getInsuredIdNumbers());
            SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, toCorrectVO);
            orderMapper.insertOrderCorrect(cDto);
            //重新计算提层
            eventBusEngine.publish(new OrderCommissionChangeEvent(toCorrectVO.getFhOrderId()));
            eventBusEngine.publish(new SmPolicyStatusCorrectEvent(toCorrectVO.getFhOrderId(), dto.getPolicyNo(),
                    dto.getNewValue(), dto.getInsuredIdNumbers()));
        }


    }

    private void correctGroupOrderAppStatus(SmOrderToCorrectVO toCorrectVO, SmOrderCorrectCancelDTO dto) {
        if (Boolean.TRUE.equals(dto.getIsPart())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "团险/雇主责任险不支持部分退保！");
        }
        Set<String> fhOrderIdSet = smCancelRefundService.refundGroupCancel(toCorrectVO.getPolicyNo());
        smCancelRefundService.batchPushCommissionChangeEvent(fhOrderIdSet);
    }

    /**
     * SmOrderCorrectDTO fillSexAndBirthday
     *
     * @param companyId
     * @param dto
     */
    private void fillSexAndBirthday(Integer companyId, SmOrderCorrectDTO dto) {
        if (dto.getNewValue() == null || !CommonUtil.isIdCardNo(dto.getNewValue())) {
            throw new BizException(ExcptEnum.POLICY_NOT_EXIST.getCode(), ExcptEnum.POLICY_NOT_EXIST.getMsg() + "：变更后信息填写有误，请核对后重新输入");
        }
        CmpySettingQuery query = new CmpySettingQuery();
        query.setCompanyId(companyId.toString());
        query.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<WxFormFieldOptionVO> settings = cmpySettingMapper.listWxCompanySettings(query);
        Map<String, String> sexMap = new HashMap<>();
        settings.forEach(st -> sexMap.put(st.getOptionName(), st.getOptionCode()));
        String sex = sexMap.get(CommonUtil.getSex(dto.getNewValue()));
        String birthday = CentNoUtil.getBirthDay(dto.getNewValue());
        dto.setSexCode(sex);
        dto.setBirthday(birthday);
    }

    public void importCorrectOrderRecommender(SmOrderImportDTO importDTO, String operator) throws Exception {
        if (!Objects.equals(operator, "CNBJ0409")) {
            return;
        }
        String key = ":import:" + operator;
        super.lock(key, 30, "上一次导入未处理完，请稍后重试！");
        try {
            List<SmCorrectOrderExcelDTO> dtos = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(importDTO.getFileUrl()), SmCorrectOrderExcelDTO.class
                            , 0, false);
            if (dtos.size() > 100) {
                throw new MSBizNormalException("10001", "一次性只能批量操作100条记录");
            }
            Set<Date> accountTimeSet = new HashSet<>();
            for (SmCorrectOrderExcelDTO excelDTO : dtos) {
                SmOrderToCorrectVO toCorrectVO = orderMapper.getOrderToCorrectByPolicyNo(excelDTO.getPolicyNo(), null, null, null);
                if (toCorrectVO == null) {
                    log.warn("保单{}信息不存在", excelDTO.getPolicyNo());
                    continue;
                }
                SmOrderCorrectDTO dto = new SmOrderCorrectDTO();
                dto.setFieldCode(CorrectProject.RECOMMEND_NAME.getCode());
                dto.setFhOrderId(toCorrectVO.getFhOrderId());
                WxUserVO userVO = userMapper.getUserByUserId(excelDTO.getRecommendId());
                if (userVO == null) {
                    log.warn("{}推荐人工号错误", excelDTO.getRecommendId());
                    continue;
                }
                UserPost userPost = userPostMapper.selectUserPostByJobCode(userVO.getJobCode());
                if (userPost == null) {
                    log.warn("{}岗位信息不存在", userVO.getJobCode());
                    continue;
                }
                dto.setNewValue(excelDTO.getRecommendId());
                dto.setNewRecommendJobCode(userPost.getJobCode());
                dto.setRecommendPostName(userPost.getPostName());
                dto.setNewRecommendMainJobNumber(userPost.getMainJobNumber());
                dto.setNewRecommendOrgCode(userPost.getOrgCode());
                log.info("订单ID={},身份证id={}", dto.getFhOrderId(), toCorrectVO.getInsuredIdNumber());
                Date accountTime = orderMapper.getOrderCommissionAccountTime(dto.getFhOrderId(), toCorrectVO.getInsuredIdNumber());
                orderMapper.updateOrderErrorRecommendInfoV2(dto);
                orderMapper.updateOrderErrorCommissionRecommendInfoV2(dto);

                // 报表跑批重跑时间需要小于今天
                if (accountTime != null && accountTime.compareTo(DateUtil.getBeginOfDay(new Date())) < 0) {
                    accountTimeSet.add(DateUtil.getBeginOfDay(accountTime));
                    //reportService.reBatchReport(accountTime);
                }
            }
            for (Date day : accountTimeSet) {
                reportService.reBatchReport(day);
            }
        } finally {
            unlock(key);
        }
    }
    @Autowired
    TransactionTemplate txTemplate;

    /**
     * 导入的订单
     *
     * @param data
     */
    public SmOrderImportConfirmDTO importGroupOrder(SmOrderImportDTO data, String operator) throws Exception {

        String key = ":import:" + operator;
        super.lock(key, 30, "上一次导入未处理完，请稍后重试！");

        SmOrderImportConfirmDTO<ConfirmData> confirmVo = new SmOrderImportConfirmDTO<>();

        try {
            List<SmOrderExcelDTO> excelDataList = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(data.getFileUrl()), SmOrderExcelDTO.class
                            , 9, false);

            //错误信息要还原 把导入 列表复制一份
            List<SmOrderExcelDTO> excelClones = objectMapper.convertValue(excelDataList, new TypeReference<List<SmOrderExcelDTO>>() {
            });

            TxUtil.getInstance(txTemplate).doTransactionWithoutResult(
                    () -> {

                        //复制错误数据 并赋值错误信息

                        List<ValidationResult<SmOrderExcelDTO>> validationResults = validation.validAndMapper4Group(data, excelDataList);

                        List<SmOrderExcelDTO> errorDataList = new ArrayList<>();
                        for (int i = 0; i < validationResults.size(); i++) {
                            ValidationResult<SmOrderExcelDTO> smOrderExcelDTOValidationResult = validationResults.get(i);
                            if (!smOrderExcelDTOValidationResult.isSuccess()) {
                                SmOrderExcelDTO error = excelClones.get(i);
                                error.setErrorMsg(smOrderExcelDTOValidationResult.getMessage());
                                errorDataList.add(error);
                            }
                        }

                        Map<Boolean, List<ValidationResult<SmOrderExcelDTO>>> validMap
                                = validationResults.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));

                        List<SmOrderExcelDTO> successData = validMap.getOrDefault(Boolean.TRUE, Collections.emptyList())
                                .stream().map(ValidationResult::getSource)
                                .collect(Collectors.toList());

                        //爱云保渠道，要验证与上报金额不一致的需给用户提示
                        List<SmOrderExcelDTO> confirmDatas = needConfirmDatas(data, successData, confirmVo);
                        successData.removeAll(confirmDatas);

                        //添加导入记录
                        int success = excelDataList.size() - errorDataList.size();
                        SmOrderImport res = null;
                        try{
                            res = smOrderImportService.addImportRecord(data, success, errorDataList.size(), confirmDatas, "", "other", operator);
                        } catch(JsonProcessingException ignored) {
                            throw new BizException("", "导入记录生成失败");
                        }
                        //失败记录处理
                        List<ValidationResult<SmOrderExcelDTO>> errors = validMap.getOrDefault(Boolean.FALSE, Collections.emptyList());
                        //有校验出错的数据
                        if (!CollectionUtils.isEmpty(errorDataList)) {
                            try{
                                smOrderImportService.saveErrorRow(res, errorDataList, errors);
                            } catch(IOException e) {
                                throw new BizException("", "导入失败记录生成失败");
                            }
                        }

                        BeanUtils.copyProperties(res, confirmVo);

                        //处理订单信息
                        List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = OrderConvertor.mapperExcel2Create(data.getChannel(), successData, operator);

                        saveOrderInfo(smCreateOrderSubmitRequests, res);
                    }
            );


            return confirmVo;
        } finally {
            unlock(key);
        }
    }


    /**
     * 导入长期个险订单
     * [不考虑批增]
     * 退保场景：考虑的不够完善，很多规则未作校验，譬如单独退主险，只保附加险等等
     *
     * @param req
     * @param operator
     * @return
     * @throws Exception
     */
    public SmOrderImportConfirmDTO importLongTermPersonalIns(SmOrderImportDTO req, String operator) throws Exception {

        String key = ":import:" + operator;
        super.lock(key, 30, "上一次导入未处理完，请稍后重试！");

        SmOrderImportConfirmDTO<ConfirmData> importDTO = new SmOrderImportConfirmDTO<>();
        try {
            List<ExcelLongTermOrderDTO> data = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(req.getFileUrl()), ExcelLongTermOrderDTO.class
                            , 10, false);

            List<ExcelLongTermOrderDTO> errorDatas = new ArrayList<>();

            //错误信息要还原 把导入 列表复制一份
            List<ExcelLongTermOrderDTO> rawData = objectMapper.convertValue(data, new TypeReference<List<ExcelLongTermOrderDTO>>() {
            });
            /**
             * 长险逻辑校验
             */
            List<ValidationResult<ExcelLongTermOrderDTO>> validationResults = validation.valid4LongTermPersonal(req, data);
            for (int i = 0; i < validationResults.size(); i++) {
                ValidationResult<ExcelLongTermOrderDTO> entry = validationResults.get(i);
                if (!entry.isSuccess()) {
                    ExcelLongTermOrderDTO err = rawData.get(i);
                    err.setErrorMsg(entry.getMessage());
                    errorDatas.add(err);
                }
            }

            Map<Boolean, List<ValidationResult<ExcelLongTermOrderDTO>>> validMap = validationResults.stream()
                    .collect(Collectors.partitioningBy(ValidationResult::isSuccess));

            List<ExcelLongTermOrderDTO> successData = validMap.getOrDefault(Boolean.TRUE, Collections.emptyList())
                    .stream()
                    .map(ValidationResult::getSource)
                    .collect(Collectors.toList());

            //添加导入记录
            SmOrderImport res = smOrderImportService.addImportRecord(req, data.size() - errorDatas.size(), errorDatas.size(), operator);
            //失败记录处理
            List<ValidationResult<ExcelLongTermOrderDTO>> errors = validMap.getOrDefault(Boolean.FALSE, Collections.emptyList());
            //有校验出错的数据
            if (!CollectionUtils.isEmpty(errorDatas)) {
                List<SmOrderImportError> errorLists = errors.stream()
                        .map(val -> {
                            SmOrderImportError error = new SmOrderImportError();
                            error.setErrorCol("");
                            error.setErrorRow(val.getSource().getNo());
                            error.setErrorMsg(val.getMessage());
                            error.setImportId(res.getId());
                            return error;
                        }).collect(Collectors.toList());
                smOrderImportService.addImportErrorRecord(res, errorLists, errorDatas);
            }
            BeanUtils.copyProperties(res, importDTO);

            List<SmCreateOrderSubmitRequest> dbEntitys = OrderConvertor.convertEntity4LongTerm(req.getChannel(), successData, operator);
            if (!CollectionUtils.isEmpty(dbEntitys)) {
                txServiceManager.excute(() -> addEntity4Personal(dbEntitys, res));
            }
            return importDTO;
        } finally {
            unlock(key);
        }
    }

    @Autowired
    private TxServiceManager txServiceManager;

    /**
     * 导入短期个险订单
     * 多人投保：不支持加人，支持单人退保
     * 注意：退保时每一个退保单时一个订单，不能在[同批次保单号]中聚合,否则会出现拆单错误
     *
     * @param req
     * @param operator
     * @return
     * @throws Exception
     */
    public SmOrderImportConfirmDTO importShortTermPersonalIns(SmOrderImportDTO req, String operator) throws Exception {

        String key = ":import:" + operator;
        super.lock(key, 30, "上一次导入未处理完，请稍后重试！");

        SmOrderImportConfirmDTO<ConfirmData> importDTO = new SmOrderImportConfirmDTO<>();

        try {
            List<ExcelShortTermOrderDTO> data = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(req.getFileUrl()), ExcelShortTermOrderDTO.class
                            , 10, false);

            List<ExcelShortTermOrderDTO> errorDatas = new ArrayList<>();

            //错误信息要还原 把导入 列表复制一份
            List<ExcelShortTermOrderDTO> rawData = objectMapper.convertValue(data, new TypeReference<List<ExcelShortTermOrderDTO>>() {
            });

            List<ValidationResult<ExcelShortTermOrderDTO>> validationResults = validation.valid4ShortTermPersonal(req, data);
            for (int i = 0; i < validationResults.size(); i++) {
                ValidationResult<ExcelShortTermOrderDTO> entry = validationResults.get(i);
                if (!entry.isSuccess()) {
                    ExcelShortTermOrderDTO err = rawData.get(i);
                    err.setErrorMsg(entry.getMessage());
                    errorDatas.add(err);
                }
            }

            Map<Boolean, List<ValidationResult<ExcelShortTermOrderDTO>>> validMap = validationResults.stream()
                    .collect(Collectors.partitioningBy(ValidationResult::isSuccess));

            List<ExcelShortTermOrderDTO> successData = validMap.getOrDefault(Boolean.TRUE, Collections.emptyList())
                    .stream()
                    .map(ValidationResult::getSource)
                    .collect(Collectors.toList());


            //添加导入记录
            SmOrderImport res = smOrderImportService.addImportRecord(req, data.size() - errorDatas.size(), errorDatas.size(), operator);
            //失败记录处理
            List<ValidationResult<ExcelShortTermOrderDTO>> errors = validMap.getOrDefault(Boolean.FALSE, Collections.emptyList());
            //有校验出错的数据
            if (!CollectionUtils.isEmpty(errorDatas)) {
                List<SmOrderImportError> errorLists = errors.stream()
                        .map(val -> {
                            SmOrderImportError error = new SmOrderImportError();
                            error.setErrorCol("");
                            error.setErrorRow(val.getSource().getNo());
                            error.setErrorMsg(val.getMessage());
                            error.setImportId(res.getId());
                            return error;
                        }).collect(Collectors.toList());
                smOrderImportService.addImportErrorRecord(res, errorLists, errorDatas);
            }
            BeanUtils.copyProperties(res, importDTO);

            List<SmCreateOrderSubmitRequest> dbEntitys = OrderConvertor.convertEntity4ShortTerm(req.getChannel(), successData, operator);
            if (!CollectionUtils.isEmpty(dbEntitys)) {
                txServiceManager.excute(() -> addEntity4Personal(dbEntitys, res));
            }
            return importDTO;
        } finally {
            unlock(key);
        }
    }

    /**
     * 个险入库.
     * 退保数据需要更新状态
     *
     * @param dbEntitys
     * @param res
     */
    public void addEntity4Personal(List<SmCreateOrderSubmitRequest> dbEntitys, SmOrderImport res) {
        dbEntitys.forEach(order -> order.setAppNo(res.getId() + ""));

        Map<Boolean, List<SmCreateOrderSubmitRequest>> entityMap = dbEntitys.stream().collect(Collectors.partitioningBy(SmCreateOrderSubmitRequest::isOrderDbExists));
        List<SmCreateOrderSubmitRequest> newOrderList = entityMap.get(Boolean.FALSE);
        List<SmCreateOrderSubmitRequest> correctOrderList = entityMap.get(Boolean.TRUE);

        addNewPolicy(newOrderList);
        addCorrectPolicy(correctOrderList);
        addPolicyRiskDuty(dbEntitys);

        if (ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(res.getChannel())) {
            updateIybState(dbEntitys);
        }
        List<String> addOrderIdList = dbEntitys.stream()
                .map(SmCreateOrderSubmitRequest::getFhOrderId)
                .collect(Collectors.toList());

        orderMapper.updateOrderUnit(addOrderIdList);
        eventBusEngine.publish(new OrderImportEvent(dbEntitys));
    }

    public int addPolicyRiskDuty(List<SmCreateOrderSubmitRequest> orderRequestList) {
        if (CollectionUtils.isEmpty(orderRequestList)) {
            return 0;
        }
        log.info("开始保存保单险种和责任内容");
        List<SmOrderRiskDuty> duties = orderRequestList.stream()
                .filter(s -> !CollectionUtils.isEmpty(s.getDuties()))
                .map(SmCreateOrderSubmitRequest::getDuties)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(duties)) {
            riskDutyMapper.insertList(duties);
        }

        List<SmOrderPolicy> policys = orderRequestList.stream()
                .filter(s -> s.getOrderPolicy() != null)
                .map(SmCreateOrderSubmitRequest::getOrderPolicy)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(policys)) {
            smOrderPolicyMapper.insertList(policys);
        }
        int success = orderRequestList.size();
        log.info("保单险种和责任内容更新完成：{}", success);
        return success;
    }

    /**
     * 新契约数据
     *
     * @return
     */
    public int addNewPolicy(List<SmCreateOrderSubmitRequest> orderRequestList) {
        if (CollectionUtils.isEmpty(orderRequestList)) {
            return 0;
        }
        log.info("开始保存新保单信息");
        orderMapper.insertOrderBatch(orderRequestList);
        orderMapper.insertOrderApplicantBatch(orderRequestList);
        orderMapper.insertOrderInsuredBatch(orderRequestList);

        List<SmCreateOrderSubmitRequest> orderItemList = orderRequestList.stream()
                .filter(order -> !CollectionUtils.isEmpty(order.getOrderItemList()))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(orderItemList)) {
            smOrderItemMapper.insertOrderItemBatch(orderItemList);
        }
        int success = orderRequestList.size();
        log.info("新保单数据入库完成：{}", success);
        return success;
    }

    /**
     * 批改数据
     *
     * @return
     */
    public int addCorrectPolicy(List<SmCreateOrderSubmitRequest> orderRequestList) {
        if (CollectionUtils.isEmpty(orderRequestList)) {
            return 0;
        }
        log.info("开始保存批改保单数据");
        List<FhInsuredPerson> addInsuredList = new ArrayList<>();
        List<FhInsuredPerson> updateInsuredList = new ArrayList<>();

        List<SmOrderItem> addItemList = new ArrayList<>();
        List<SmOrderItem> updateItemList = new ArrayList<>();
        for (SmCreateOrderSubmitRequest request : orderRequestList) {
            List<FhInsuredPerson> insuredList = request.getInsuredPerson();
            List<SmOrderItem> orderItemList = request.getOrderItemList();

            Map<Boolean, List<FhInsuredPerson>> insuredMap = LambdaUtils.groupBy(insuredList, FhInsuredPerson::isMemberExist);
            List<FhInsuredPerson> tempUpdateInsuredList = insuredMap.get(Boolean.TRUE);
            List<FhInsuredPerson> tempAddInsuredList = insuredMap.get(Boolean.FALSE);
            if (tempUpdateInsuredList != null) {
                updateInsuredList.addAll(tempUpdateInsuredList);
            }
            if (tempAddInsuredList != null) {
                addInsuredList.addAll(tempAddInsuredList);
            }

            Map<Boolean, List<SmOrderItem>> itemMap = LambdaUtils.groupBy(orderItemList, SmOrderItem::isMemberExist);
            List<SmOrderItem> tempUpdateItemList = itemMap.get(Boolean.TRUE);
            List<SmOrderItem> tempAddItemList = itemMap.get(Boolean.FALSE);
            if (tempUpdateItemList != null) {
                updateItemList.addAll(tempUpdateItemList);
            }
            if (tempAddItemList != null) {
                addItemList.addAll(tempAddItemList);
            }
        }

        if (!CollectionUtils.isEmpty(addInsuredList)) {
            orderMapper.insertOrderInsuredList(addInsuredList);
        }

        if (!CollectionUtils.isEmpty(addItemList)) {
            smOrderItemMapper.insertOrderItemEntityList(addItemList);
        }

        if (!CollectionUtils.isEmpty(updateInsuredList)) {
            orderMapper.updateSmOrderInsuredBatch(updateInsuredList);
        }

        if (!CollectionUtils.isEmpty(updateItemList)) {
            smOrderItemMapper.updateOrderItemBatch(updateItemList);
        }

        int success = orderRequestList.size();
        log.info("批改保单数据更新完成：{}", success);
        return success;
    }

    /**
     * 导入单入库
     *
     * @param orderMapperEntity
     * @param res
     */
    public void saveOrderInfo(List<SmCreateOrderSubmitRequest> orderMapperEntity, SmOrderImport res) {
        if (CollectionUtils.isEmpty(orderMapperEntity)) {
            log.warn("订单信息为空");
            return;
        }
        orderMapperEntity.forEach(ac -> ac.setAppNo(res.getId() + ""));

        /**
         * 如果订单已存在 则订单数据以及投保人数据不再重复插入
         */
        List<SmCreateOrderSubmitRequest> newOrderList = orderMapperEntity.stream()
                .filter(order -> !order.isOrderDbExists())
                .collect(Collectors.toList());

        List<SmCreateOrderSubmitRequest> orderItemList = orderMapperEntity.stream()
                .filter(s -> !CollectionUtils.isEmpty(s.getOrderItemList()))
                .collect(Collectors.toList());

        List<String> addOrderIdList = orderMapperEntity.stream()
                .map(SmCreateOrderSubmitRequest::getFhOrderId)
                .collect(Collectors.toList());

        txServiceManager.excute(() -> {
            int a = 0;
            int b = 0;
            int c = 0;
            if (!CollectionUtils.isEmpty(newOrderList)) {
                a = orderMapper.insertOrderBatch(newOrderList);
                b = orderMapper.insertOrderApplicantBatch(newOrderList);
            }
            if (!CollectionUtils.isEmpty(orderItemList)) {
                smOrderItemMapper.insertOrderItemBatch(orderItemList);
            }
            int d = orderMapper.insertOrderInsuredBatch(orderMapperEntity);
            int e = orderMapper.updateOrderUnit(addOrderIdList);
            log.info("保单数据导入完成:{},{},{},{},{}", a, b, c, d, e);
        });
        //异步执行提层 客户提取等操作
        eventBusEngine.publish(new OrderImportEvent(orderMapperEntity));
        //I云宝 修改上传记录
        if (ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(res.getChannel())) {
            updateIybState(orderMapperEntity);
        }

    }

    public void importConfirm(Integer importId, String operator) throws Exception {
        SmOrderImport smOrderImport = orderImportMapper.selectByPrimaryKey(importId);
        if (smOrderImport != null && smOrderImport.getConfirmCnt() > 0) {
            SmOrderImportExtend extend = smOrderImportExtendMapper.selectByPrimaryKey(importId);

            if (Objects.equals(extend.getType(), "other")) {
                List<SmOrderExcelDTO> excelDTOList = objectMapper.readValue(extend.getConfirmContent(),
                        new TypeReference<List<SmOrderExcelDTO>>() {
                        });

                List<SmCreateOrderSubmitRequest> smCreateOrderSubmitRequests = OrderConvertor.mapperExcel2Create(smOrderImport.getChannel(), excelDTOList, operator);
                saveOrderInfo(smCreateOrderSubmitRequests, smOrderImport);
            } else if (Objects.equals(extend.getType(), "car")) {

                List<SmCarOrderExcelDTO> carOrderExcelDTOList = objectMapper.readValue(extend.getConfirmContent(),
                        new TypeReference<List<SmCarOrderExcelDTO>>() {
                        });
                List<CarConfirmData> carConfirmDataList = objectMapper.readValue(extend.getDifContent(), new TypeReference<List<CarConfirmData>>() {
                });

                smCarOrderManageService.importConfirm(smOrderImport, carOrderExcelDTOList, carConfirmDataList);
            }
        }
    }

    /**
     * 众安I云宝上传金额校验
     */
    private List<SmOrderExcelDTO> needConfirmDatas(SmOrderImportDTO dto,
                                                   List<SmOrderExcelDTO> success, SmOrderImportConfirmDTO importDTO) {
        if (!ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(dto.getChannel())) {
            return Collections.emptyList();
        }

        List<String> policyNos = success.stream().map(SmOrderExcelDTO::getPolicyNo)
                .distinct().collect(Collectors.toList());

        Map<String, SmPolicyRegister> mapByPolicyNos = registerService.getMapByPolicyNos(policyNos);
        List<SmOrderExcelDTO> needConfirmDatas = new ArrayList<>();
        List<ConfirmData> confirmDataList = new ArrayList<>();
        for (SmOrderExcelDTO result : success) {
            if (OrderImportEndorsementTypeEnum.NEW.isMe(result.getEndorsementType())) {
                SmPolicyRegister register = mapByPolicyNos.get(result.getPolicyNo());
                if (register.getPremium().compareTo(result.getAmount()) != 0) {

                    needConfirmDatas.add(result);

                    ConfirmData confirmData = new ConfirmData();
                    //已经存在跳出当前循环
                    if (confirmDataList.stream().anyMatch(o -> Objects.equals(o.getPolicyNo(), result.getPolicyNo()))) {
                        continue;
                    }
                    confirmData.setPolicyNo(result.getPolicyNo());
                    confirmData.setAmount(result.getAmount());
                    confirmData.setPreminum(register.getPremium());
                    confirmDataList.add(confirmData);
                }
            }
        }
        importDTO.setConfirmDataList(confirmDataList);
        return needConfirmDatas;
    }

    /**
     * 众安I云宝上传金额校验
     */
//    private List<SmOrderExcelDTO> needConfirmDatas(SmOrderImportDTO dto, List<SmOrderExcelDTO> success, SmOrderImportConfirmDTO importDTO) {
//        if (!ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(dto.getChannel())) {
//            return Collections.emptyList();
//        }
//
//        List<String> policyNos = success.stream().map(SmOrderExcelDTO::getPolicyNo)
//                .distinct().collect(Collectors.toList());
//
//        Map<String, SmPolicyRegister> mapByPolicyNos = registerService.getMapByPolicyNos(policyNos);
//        List<SmOrderExcelDTO> needConfirmDatas = new ArrayList<>();
//        List<ConfirmData> confirmDataList = new ArrayList<>();
//        for (SmOrderExcelDTO result : success) {
//            if (OrderImportEndorsementTypeEnum.NEW.isMe(result.getEndorsementType())) {
//                SmPolicyRegister register = mapByPolicyNos.get(result.getPolicyNo());
//                if (register.getPremium().compareTo(result.getAmount()) != 0) {
//
//                    needConfirmDatas.add(result);
//
//                    ConfirmData confirmData = new ConfirmData();
//                    //已经存在跳出当前循环
//                    if (confirmDataList.stream().anyMatch(o -> Objects.equals(o.getPolicyNo(), result.getPolicyNo()))) {
//                        continue;
//                    }
//                    confirmData.setPolicyNo(result.getPolicyNo());
//                    confirmData.setAmount(result.getAmount());
//                    confirmData.setPreminum(register.getPremium());
//                    confirmDataList.add(confirmData);
//                }
//            }
//        }
//        importDTO.setConfirmDataList(confirmDataList);
//        return needConfirmDatas;
//
//
//    }
    public PageInfo<SmOrderImport> importList(SmOrderImportQuery query) {

        if (Objects.nonNull(query.getEndTime())) {
            LocalDate endTime = query.getEndTime().plusDays(1L);
            query.setEndTime(endTime);
        }

        PageInfo<SmOrderImport> res = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> orderImportMapper.queryImportList(query.getType(), query.getStartTime(), query.getEndTime()));

        List<String> jobNumbers = res.getList()
                .stream()
                .map(SmOrderImport::getCreateBy)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(jobNumbers)) {
            Map<String, AuthUserNameVO> dictMap = LambdaUtils.toMap(userMapper.listUserNameByUserId(jobNumbers), AuthUserNameVO::getUserId);
            res.getList().forEach(ip -> {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(ip.getCreateBy())) {
                    ip.setCreateBy(dictMap.getOrDefault(ip.getCreateBy(), new AuthUserNameVO()).getUserName());
                }
            });
        }
        return res;

    }

    /**
     * 找出所有有效的被保人
     *
     * @param policyNo
     * @return
     */
    public List<SmPolicyInsured> listInsuredByPolicyNo(String policyNo) {
        String fhOrderId = orderMapper.getFhOrderIdByPolicyNo(policyNo);
        String productAttrCode = orderMapper.getProductAttrCode(fhOrderId);
        if (Objects.equals(productAttrCode, EnumProductAttr.GROUP.getCode()) || Objects.equals(productAttrCode, EnumProductAttr.EMPLOYER.getCode())) {
//            1. 如果是团险则查询出所有被保人信息包括已经批改过的订单
            List<SmPolicyInsured> smPolicyInsureds = orderMapper.listOrderInsuredByPolicyNo(policyNo);
            Map<String, SmPolicyInsured> map = new HashMap<>();
            smPolicyInsureds.forEach(x -> {
//            2. 首先将所有状态为1的订单加入map中
                if (Objects.equals(x.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                    map.put(x.getIdNumber().toLowerCase(), x);
                }
//            3. 最后如果状态已退保的话就删掉该订单
                if (map.containsKey(x.getIdNumber().toLowerCase()) && Objects.equals(x.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    map.remove(x.getIdNumber().toLowerCase());
                }
            });
//        4. 最终返回一个包含去重后有效数据的列表。
            List<SmPolicyInsured> smValidPolicyInsureds = new ArrayList<>(map.values());
            return smValidPolicyInsureds;
        } else {
//            5. 若不是团险则获取个险订单的被保险人列表
            List<SmPolicyInsured> listIndividualOrderInsured = orderMapper.listIndividualOrderInsuredByPolicyNo(policyNo);
            Map<String, SmPolicyInsured> map = new HashMap<>();
            listIndividualOrderInsured.forEach(x -> {
//               首先将所有状态为1的订单加入map中
                if (Objects.equals(x.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                    map.put(x.getIdNumber().toLowerCase(), x);
                }
//               最后如果状态已退保的话就删掉该订单
                if (map.containsKey(x.getIdNumber().toLowerCase()) && Objects.equals(x.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    map.remove(x.getIdNumber().toLowerCase());
                }
            });
            List<SmPolicyInsured> smPolicyInsureds = new ArrayList<>(map.values());
            return smPolicyInsureds;
        }
    }

    /**
     * 补单成功推送事件 【佣金计算 客户信息提取】
     *
     * @param fhOrderId
     */
    public void pushSupplementSuccess(String fhOrderId) {
        eventBusEngine.publish(new OrderCommissionChangeEvent(fhOrderId));
        eventBusEngine.publish(new OrderCustomerChangeEvent(fhOrderId));
    }

    /**
     * 查询订单详情ddd
     *
     * @param orderId
     * @return
     */
    public SmOrderDetailVO getOrderDetail(String orderId) {
//
        SmOrder order = orderDDDMapper.selectByOrderId(orderId);
        List<SmOrderInsured> smOrderInsureds = insuredMapper.selectByOrderId(orderId);
        SmOrderApplicant smOrderApplicant = applicantMapper.selectByOrderId(orderId);

        SmOrderDetailVO result = new SmOrderDetailVO();
        SmOrderInfoVO orderInfo = new SmOrderInfoVO();
        BeanUtils.copyProperties(order, orderInfo);
        orderInfo.setOrderUpdateTime(order.getUpdateTime());
        SmProductDetailVO product = productService.getProductById(order.getProductId());
        orderInfo.setProductName(product.getProductName());
        orderInfo.setProductAttrCode(product.getProductAttrCode());
        return result;
    }

    /**
     * 查询支付信息
     *
     * @param orderId
     * @return
     */
    public QueryOrderVO getPaymentInfo(String orderId) {
        return payService.queryOrderInfo(orderId);
    }

    /**
     * 查询业务归属
     *
     * @param orderId
     * @return
     */
    public SmOrderMasterVO getOrderMaster(String orderId) {

        SmOrder smOrder = orderDDDMapper.selectByOrderId(orderId);
        String recommendId = smOrder.getRecommendId();
        String customerAdminId = smOrder.getCustomerAdminId();
        SmOrderMasterVO res = new SmOrderMasterVO();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(recommendId)) {
            res.setReferrer(userMapper.getUserByUserId(recommendId));
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(customerAdminId)) {
            res.setCusterAdmin(userMapper.getUserByUserId(customerAdminId));
        }

        return res;
    }

    public List<SmCancel> getCancel(String orderId) {
        return cancelService.listSimpleByFhOrderId(orderId);
    }

    public List<SmProductCoverageAmountVO> getProductCoverage(String orderId) {
        SmOrder smOrder = orderDDDMapper.selectByOrderId(orderId);
        int productVersion = productHistoryService.getProductVersion(smOrder.getProductId(), smOrder.getSubmitTime());

        return productHistoryService.getProductCoverageAmountListByPlan(smOrder.getPlanId(), productVersion);
    }

    /**
     * 是否超过限购
     *
     * @param idNumbers
     * @param productId
     * @return
     */
    public boolean checkSubmitOrderInsuredProductBuyLimit(List<SmOrderInsuredBuyDTO> idNumbers,
                                                          int productId) {

        SmProductDetailVO productById = productService.getProductById(productId);
        Integer productBuyLimit = productById.getBuyLimit();
        SmProductFormBuyLimit query = new SmProductFormBuyLimit();

        query.setProductId(productId);
        List<SmProductFormBuyLimit> formBuyLimits = formBuyLimitMapper.selectEnabled(query);

        for (int i = 0; i < idNumbers.size(); i++) {

            SmOrderInsuredBuyDTO dto = idNumbers.get(i);
            if (!CollectionUtils.isEmpty(formBuyLimits)) {//如果当前产品有特殊职业配置
                int occLimit = formBuyLimits.stream().map(SmProductFormBuyLimit::getLimitCode)
                        .mapToInt(Integer::parseInt).min().orElse(productBuyLimit);

                List<String> os = formBuyLimits.stream().map(SmProductFormBuyLimit::getFiledValue).collect(Collectors.toList());
                int occHasPolicyQty = orderMapper.countProductPolicyQty(productId,
                        dto.getIdNumber(), os);
                if (occHasPolicyQty >= occLimit) {
                    return false;
                }
            }
            Optional<SmProductFormBuyLimit> specialOcc = findSpecialOcc(dto.getOccupationCode(), formBuyLimits);

            //如果当前职业单独配置了限购分数则用配置的限购分数 否则使用计划设置
            int condBuyLimit = specialOcc.map(SmProductFormBuyLimit::getLimitCode)
                    .map(Integer::parseInt)
                    .orElse(productBuyLimit);

            int hasPolicyQty = orderMapper.countProductPolicyQty(productId, dto.getIdNumber(), null);
            if (dto.getQty() + hasPolicyQty > condBuyLimit) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取本地数据
     */
    public SmBaseOrderVO getLocalBaseOrderInfo(String orderId) {
        SmBaseOrderVO baseOrderInfoByOrderId = orderMapper.getBaseOrderInfoByOrderId(orderId);
        List<SmOrderSmsNotifyVO> insureds = orderMapper.getOrderInsuredByOrderId(orderId);
        if (!CollectionUtils.isEmpty(insureds)) {
            baseOrderInfoByOrderId.setInsuredId(insureds.iterator().next().getInsuredId());
        }
        return baseOrderInfoByOrderId;
    }

    /**
     * 是否特殊职业
     */
    private Optional<SmProductFormBuyLimit> findSpecialOcc(String occCode, List<SmProductFormBuyLimit> formBuyLimits) {

        return formBuyLimits.stream()
                .filter(pre -> Objects.equals(pre.getFiledValue(), occCode))
                .findFirst();
    }

    /**
     * 修改众安i云宝的 上报记录
     *
     * @param reqs
     */
    private void updateIybState(List<SmCreateOrderSubmitRequest> reqs) {
        List<SmCreateOrderSubmitRequest> newList = reqs.stream().filter(request -> Objects.equals(OrderImportEndorsementTypeEnum.NEW.getCode(), request.getEndorsementType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(newList)) {
            for (SmCreateOrderSubmitRequest re : newList) {
                registerService.updateStateByPolicyNo(re.getInsuredPerson().get(0).getPolicyNo(), re.getOrderInfo().getTotalAmount());
            }
        }

        List<FhInsuredPerson> peoples = LambdaUtils.one2More(reqs, SmCreateOrderSubmitRequest::getInsuredPerson);
        //保单列表
        List<String> policies = peoples.stream().map(FhInsuredPerson::getPolicyNo).distinct()
                .collect(Collectors.toList());

        registerService.updateStateByPolicyNos(policies);
    }

    /************ s51 根据订单id，原管护信贷员工号 变更 管护客户经理 *************/
    @Transactional(rollbackFor = Exception.class)
    public int updateOrderRecommendByOrderId(String fhOrderId, String oldRecommendId, String newRecommendId){
        return updateOrderRecommendByOrderId(fhOrderId, oldRecommendId, newRecommendId, false);
    }

    /************ s51 根据订单id，原管护信贷员工号 变更 管护客户经理 *************/
    @Transactional(rollbackFor = Exception.class)
    public int updateOrderRecommendByOrderId(String fhOrderId, String oldRecommendId, String newRecommendId,boolean isWhaleUp) {
        /*if (org.apache.commons.lang.StringUtils.isBlank(oldRecommendId)) {
            return 0;
        }*/
        if (StringUtils.isBlank(newRecommendId)) {
            return 0;
        }
        List<OrderBaseDetailDTO> orderBaseDetailDTOList = orderMapper.listOrderBaseDetailByOrderId(fhOrderId);
        if (CollectionUtils.isEmpty(orderBaseDetailDTOList)) {
            return 0;
        }
        OrderBaseDetailDTO dto = orderBaseDetailDTOList.get(0);
        if (!StringUtils.isEmpty(dto.getRecommendId()) && !Objects.equals(oldRecommendId, dto.getRecommendId())) {
            log.info("原客户经理工号{}与数据库{}中不一致", oldRecommendId, dto.getRecommendId());
            return 0;
        }

        //推荐人从员工A(存在)批改为员工B, 不能跨月修改数据；
        if (org.apache.commons.lang3.StringUtils.isNotBlank(oldRecommendId)) {
            if (Objects.isNull(dto.getPaymentTime()) || !DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT).equals(dto.getPaymentTime().format(DateTimeFormatter.ofPattern(DateUtil.CN_YEAR_MONTH_FORMAT)))) {
                log.warn(ExcptEnum.POLICY_CHNAGE_CROSS_MONTH.getMsg() + ",推荐时间为:{}", dto.getPaymentTime());
                return 0;
            }
        }
        List<UserPost> userPosts=null;
        if(isWhaleUp){
            userPosts = userPostService.listAllUserPostByJobNumber(newRecommendId);
        }else {
            userPosts = userPostService.listUserPostByJobNumber(newRecommendId);
        }
        if (CollectionUtils.isEmpty(userPosts)) {
            log.info("新推荐人的职位信息不存在{}", newRecommendId);
            return 0;
        }
        //获取主职
        Optional<UserPost> mainPostOpt = userPosts.stream().filter(p -> Objects.equals(p.getServiceType(), 0)).findFirst();
        if (!mainPostOpt.isPresent()) {
            throw new MSBizNormalException("", "新推荐人的主职信息不存在");
        }
        UserPost mainPost = mainPostOpt.get();
        UpdateOrderRecommendDTO orderRecommendDTO = new UpdateOrderRecommendDTO();
        orderRecommendDTO.setFhOrderId(fhOrderId);
        orderRecommendDTO.setOldRecommendId(dto.getRecommendId());
        orderRecommendDTO.setOldRecommendJobCode(dto.getRecommendJobCode());
        orderRecommendDTO.setOldRecommendMainJobNumber(dto.getRecommendMainJobNumber());
        orderRecommendDTO.setOldRecommendOrgCode(dto.getRecommendOrgCode());
        orderRecommendDTO.setNewRecommendId(newRecommendId);
        orderRecommendDTO.setNewRecommendJobCode(mainPost.getJobCode());
        orderRecommendDTO.setNewRecommendMainJobNumber(mainPost.getMainJobNumber());
        orderRecommendDTO.setNewRecommendOrgCode(mainPost.getOrgCode());
        orderRecommendDTO.setNewRecommendAdminName(mainPost.getUserAdminName());
        orderRecommendDTO.setNewRecommendPostName(mainPost.getPostName());
        orderRecommendDTO.setNewRecommendMasterName(mainPost.getUserMasterName());
        //佣金有可能没有计算
        orderMapper.updateOrderCommissionRecommendIdByOrderId(orderRecommendDTO);
        int ret = orderMapper.updateOrderRecommendIdByOrderId(orderRecommendDTO);

        //更新新的佣金表
        SmOrderCorrectDTO correctDTO = new SmOrderCorrectDTO();
        correctDTO.setFhOrderId(dto.getFhOrderId());
        correctDTO.setPolicyNo(dto.getPolicyNo());
        correctDTO.setOldValue(oldRecommendId);
        correctDTO.setNewValue(newRecommendId);
        correctDTO.setFieldCode(CorrectProject.RECOMMEND_NAME.getCode());
        if (StringUtils.isBlank(oldRecommendId)) {
            correctDTO.setPaymentTime(new Date());
        }
        updateNewCommissionInfo(correctDTO);
        SmSaveOrderCorrectDTO correctVo = new SmSaveOrderCorrectDTO();
        BeanUtils.copyProperties(correctDTO,correctVo);
        orderMapper.insertOrderCorrect(correctVo);
        return ret;
    }

    /**
     * 变更保单管护人
     * @param fhOrderId 订单Id
     * @param oldCustomerManagerId
     * @param newCustomerManagerId
     * @param fromWhale 是否来源于小鲸系统
     * @return
     */
    public int updateOrderCustomerManagerByOrderId(String fhOrderId, String oldCustomerManagerId, String newCustomerManagerId,boolean fromWhale) {

        if (StringUtils.isBlank(newCustomerManagerId)) {
            return 0;
        }
        List<OrderBaseDetailDTO> orderBaseDetailDTOList = orderMapper.listOrderBaseDetailByOrderId(fhOrderId);
        if (CollectionUtils.isEmpty(orderBaseDetailDTOList)) {
            return 0;
        }
        OrderBaseDetailDTO dto = orderBaseDetailDTOList.get(0);
        if (!Objects.equals(oldCustomerManagerId, dto.getCustomerAdminId())) {
            log.info("原客户经理工号{}与数据库{}中不一致", oldCustomerManagerId, dto.getCustomerAdminId());
            return 0;
        }

        List<UserPost> userPosts = userPostService.listAllUserPostByJobNumber(newCustomerManagerId);
        if (CollectionUtils.isEmpty(userPosts)) {
            log.info("新客户经理人的职位信息不存在{}", oldCustomerManagerId);
            return 0;
        }
        //获取主职
        Optional<UserPost> mainPostOpt = userPosts.stream().filter(p -> Objects.equals(p.getServiceType(), 0)).findFirst();
        if (!mainPostOpt.isPresent()) {
            log.warn("新客户经理人的职位信息不存在{}", oldCustomerManagerId);
            return 0;
        }
        UserPost mainPost = mainPostOpt.get();
        UpdateOrderRecommendDTO orderRecommendDTO = new UpdateOrderRecommendDTO();
        orderRecommendDTO.setFhOrderId(fhOrderId);
        orderRecommendDTO.setOldRecommendId(dto.getRecommendId());
        orderRecommendDTO.setOldRecommendJobCode(dto.getRecommendJobCode());
        orderRecommendDTO.setOldRecommendMainJobNumber(dto.getRecommendMainJobNumber());
        orderRecommendDTO.setOldRecommendOrgCode(dto.getRecommendOrgCode());
        orderRecommendDTO.setNewRecommendId(newCustomerManagerId);
        orderRecommendDTO.setNewRecommendJobCode(mainPost.getJobCode());
        orderRecommendDTO.setNewRecommendMainJobNumber(mainPost.getMainJobNumber());
        orderRecommendDTO.setNewRecommendOrgCode(mainPost.getOrgCode());
        orderRecommendDTO.setNewRecommendAdminName(mainPost.getUserAdminName());
        orderRecommendDTO.setNewRecommendPostName(mainPost.getPostName());
        orderRecommendDTO.setNewRecommendMasterName(mainPost.getUserMasterName());

        SmSaveOrderCorrectDTO correctDTO = new SmSaveOrderCorrectDTO();
        correctDTO.setFhOrderId(dto.getFhOrderId());
        correctDTO.setPolicyNo(dto.getPolicyNo());
        correctDTO.setOldValue(oldCustomerManagerId);
        correctDTO.setNewValue(newCustomerManagerId);
        correctDTO.setFieldCode(CorrectProject.CUSTOMER_ADMIN_INFO.getCode());
        int ret = txServiceManager.excute(()->{
            orderMapper.insertOrderCorrect(correctDTO);
            return orderMapper.updateOrderCustomerAdminIdByOrderId(orderRecommendDTO);
        });
        return ret;
    }

    /**
     * s64 变更管护客户经理后，长期险续期单以月底最后一天的管护客户经理来计算当月业绩和佣金归属
     **/
    @Transactional(rollbackFor = Exception.class)
    public void changeRenewalCommissionUserId(String fhOrderId, Integer termNum, String newCustomerAdminId) {
        smOrderRenewalTermMapper.updateCustomerAdminIdByOrderIdAndTermNum(fhOrderId, termNum, newCustomerAdminId);
        commissionManagerService.updateCommissionUserId(fhOrderId, termNum, newCustomerAdminId);

    }


    public PageInfo<SmOrderListVO> getOrderAddCommissionByPage(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initOrderQuery(query);
        List<SmOrderListVO> orderVos = orderMapper.listOrderAddCommission(query);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderVos)) {
            caculateTaskPremium(orderVos);
        }
        handleCode(orderVos);
        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderListVO.class, FieldRemoveDefault.class);
        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            if(query.getForbidCountSwitch()){
                summaryPage.setTotal(0);
                summaryPage.setHasNextPage(true);
            }else{
                summaryPage.setTotal(orderMapper.countOrderCommissionV3(query));
                summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
            }
        }
        return summaryPage;
    }

    @DataSourceReadOnly
    public void downloadAddCommission(SmOrderQuery query, HttpServletResponse response) {
        log.info("download policies query={}", JSON.toJSONString(query));
        query.setSize(5000);
        UserDetailVO contextUser = bmsService.getContextUserDetail();
        Class<?> clazz;
        Set<String> excludeFileds;
        String fileName;

        fileName = "小额保险提成明细";
        clazz = SmCommissionVO.class;
        if (permissionUtil.isRegionRole(contextUser) || permissionUtil.isBranchRole(contextUser)) {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmCommissionVO.NormalRoleCommission.class);
        } else {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmCommissionVO.Commission.class);
        }
        try (OutputStream os = response.getOutputStream()) {
            int nextPage = 1;
            query.setPage(nextPage);
            PageInfo<SmOrderListVO> pageInfo = getOrderByPage(query);
            if (pageInfo.getTotal() > BaseConstants.EXCEL_ROW_LIMIT) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil download = ExcelBuilderUtil.newInstance()
                    .createSheet("保单列表")
                    .buildSheetHead(clazz, excludeFileds);

            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = getOrderCommissionByPageV3(query);
                nextPage++;
                List<SmOrderListVO> orderVos = pageInfo.getList();
                //设置产品名称 = 产品名称+计划名称
                orderVos.forEach(smOrderListVO -> smOrderListVO.setProductName(smOrderListVO.getProductName() + smOrderListVO.getPlanName()));
                if (PolicyCodeUtil.isLoadSuccess()) {
                    orderVos.forEach(o -> {
                        o.setPayStatusName(PolicyCodeUtil.getSmPayStatusTypeName(o.getPayStatus()));
                        o.setAppStatusName(PolicyCodeUtil.getSmPolicyStatusName(o.getAppStatus()));
                    });
                }
                log.info("download policies size={}", orderVos.size());
                download.addSheetData(orderVos);
            }
            download.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }

    }

    /**
     * 设置对账结果
     *
     * @param smOrderListVOList 订单集合
     */
    public void initReconciliationResult(List<SmOrderListVO> smOrderListVOList) {
        //获取保单集合
        List<String> policyNoList = smOrderListVOList.stream()
                .map(this::getNoLinePolicyNo)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        //获取保单最新的表单信息
        Map<String, ReconciledPolicyDTO> smReconciliationNewestPolicy = smReconciliationPolicyMapper.getSmReconciliationNewestPolicy(null, null, null, policyNoList);

        //设置对账结果
        smOrderListVOList.forEach(smOrderListVO -> {
            String policyKey = smReconciliationPolicyMapper.getPolicyKey(this.getNoLinePolicyNo(smOrderListVO), smOrderListVO.getAppStatus());
            ReconciledPolicyDTO reconciledPolicyDTO = smReconciliationNewestPolicy.get(policyKey);
            EnumReconciliationStatusType reconciliationResult = getReconciliationResult(reconciledPolicyDTO);
            if (Objects.nonNull(reconciliationResult)) {
                smOrderListVO.setReconciliationResult(reconciliationResult.getDescribe());
            }
        });
    }

    public PageInfo<SmOrderListVO> getOrderCommissionPgShortByPageV3(SmOrderQuery query) {
        if (query == null) {
            return null;
        }
        initPgOrderQuery(query);

        List<SmOrderListVO> orderVos = insuranceOrderCommissionShortMapper.listOrderCommissionV3(query);
        //计算任务保费字段
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderVos)) {
            caculateTaskPremium(orderVos);
            //设置加佣比例
            setOrderListCommissionProportion(orderVos);

            initReconciliationResult(orderVos);
        }

        handleCode(orderVos);

        setEndorsementNo(orderVos);

        // 对客户敏感数据进行脱敏
        if (query.getCommission() == null) {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderList());
        } else {
            permissionUtil.maskCustomerSensitiveFields(orderVos, bmsConfig.getCustomerSensitiveOrderCms());
        }
        // 对敏感信息作置空处理
        FieldRemoveUtil.tranListField2Null(orderVos, SmOrderListVO.class, FieldRemoveDefault.class);

        PageInfo<SmOrderListVO> summaryPage = new PageInfo<>(orderVos);
        if (query.isQueryPage()) {
            summaryPage.setPageNum(query.getPage());
            summaryPage.setSize(query.getSize());
            summaryPage.setTotal(insuranceOrderCommissionShortMapper.countOrderCommissionV3(query));
            summaryPage.setHasNextPage(query.getPage() * query.getSize() < summaryPage.getTotal());
        }
        return summaryPage;
    }

    public void downloadPgOrderCommissionV3(SmOrderQuery query, HttpServletResponse response) {
        log.info("download policies query={}", JSON.toJSONString(query));
        query.setSize(5000);
        UserDetailVO contextUser = bmsService.getContextUserDetail();
        Class<?> clazz;
        Set<String> excludeFileds;
        String fileName;

        fileName = "小额保险提成明细";
        clazz = SmPgCommissionVO.class;
        if (permissionUtil.isRegionRole(contextUser) || permissionUtil.isBranchRole(contextUser)) {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmPgCommissionVO.NormalRoleCommission.class);
        } else {
            excludeFileds = FieldRemoveUtil.extractRemoveFieldName(clazz, SmPgCommissionVO.Commission.class);
        }
        try (OutputStream os = response.getOutputStream()) {
            int nextPage = 1;
            query.setPage(nextPage);
            PageInfo<SmOrderListVO> pageInfo = getOrderCommissionPgShortByPageV3(query);
            if (pageInfo.getTotal() > BaseConstants.EXCEL_ROW_LIMIT) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil download = ExcelBuilderUtil.newInstance()
                    .createSheet("保单列表")
                    .buildSheetHead(clazz, excludeFileds);

            Map<String, List<DictionaryVO>> dictionaryVos = dictionaryMapper.listDictionarys("channel", null, true)
                    .stream().collect(Collectors.groupingBy(DictionaryVO::getCode));

            long maxPage = pageInfo.getTotal() / query.getSize() + 1;
            while (nextPage <= maxPage) {
                query.setPage(nextPage);
                query.setQueryPage(false);
                pageInfo = getOrderCommissionPgShortByPageV3(query);
                nextPage++;
                List<SmOrderListVO> orderVos = pageInfo.getList();
                //设置产品名称 = 产品名称+计划名称
                orderVos.forEach(smOrderListVO -> smOrderListVO.setProductName(smOrderListVO.getProductName() + smOrderListVO.getPlanName()));
                if (PolicyCodeUtil.isLoadSuccess()) {
                    orderVos.forEach(o -> {
                        o.setPayStatusName(PolicyCodeUtil.getSmPayStatusTypeName(o.getPayStatus()));
                        o.setAppStatusName(PolicyCodeUtil.getSmPolicyStatusName(o.getAppStatus()));
                        o.setChannelName(CollectionUtils.isEmpty(dictionaryVos.get(o.getChannel())) ? "" : dictionaryVos.get(o.getChannel()).get(0).getName());
                    });
                }
                log.info("download policies size={}", orderVos.size());
                download.addSheetData(orderVos);
            }
            download.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    @DataSourceReadOnly
    public SmOrderSummaryVO getOrderPgCommissionSummaryV3(SmOrderQuery query) {
            if (query == null) {
                return null;
            }
            initPgOrderQuery(query);
            return insuranceOrderCommissionShortMapper.getOrderCommissionSummaryV3(query);
    }

    /**
     * 根据订单号删除订单
     * @param orderId 订单号
     */
    public int removeOrder(String orderId) {
        return txServiceManager.excute(()->{
            int r1 = orderDDDMapper.logicDelete(orderId);
            int r2 =insuredMapper.logicDelete(orderId);
            int r3 =smOrderItemMapper.logicDeleteByOrderId(orderId);
            int r4 =smOrderPolicyMapper.logicDelete(orderId);
            int r5 =riskDutyMapper.logicDelete(orderId);
            log.info("订单逻辑删除完成:{},{},{},{},{}",r1,r2,r3,r4,r5);
            return r1;
        });
    }
}
