package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.config.OrderConfigRule;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderPolicyMapper;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.external.AutoOrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderSupmtDTO;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrder;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCommissionSettingVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSmsNotifyVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.order.FhOrderService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BadException;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.InsurPayService;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.pay.facade.constant.PayStatusEnum;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 小额保险订单service
 * 此接口对所有第三方接口做了适配
 * 所有订单接口都订单用此接口下单  更新订单信息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Deprecated
public class SmOrderCoreService {

    /**
     * 产品service
     */
    @Autowired
    private SmProductService productService;

    /**
     * 产品提成 service
     */
    @Autowired
    private SmCommissionMapper cmsMapper;

    /**
     * 订单mapper
     */
    @Autowired
    private SmOrderMapper orderMapper;

    /**
     * 代理类
     */
    @Autowired
    private SmOrderServiceWrapper wrapper;


    /**
     * 小额保险订单接口
     */
    @Autowired
    private SmOrderManageService orderManageService;


    /**
     * 泛华订单接口
     */
    @Autowired
    private FhOrderService fhOrderService;


    @Autowired
    InsurPayService insurPayService;
    @Autowired
    private PermissionUtil permissionUtil;
    @Autowired
    private BmsConfig bmsConfig;

    @Autowired
    private EndorMapper paymentMapper;

    @Autowired
    private AutoOrderMapper autoOrderMapper;

    @Autowired
    private AutoOrderPolicyMapper autoOrderPolicyMapper;

    /**
     * 修改非见费订单状态
     * 同步支付系统支付状态，
     * 如果支付成功则调用第三方接口出单
     *
     * @param orderId
     */
    public boolean updateNonSeeFeeUnPayOrderPayInfo(String orderId) {
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        // 订单已支付不再处理
        if (Objects.equals(baseOrderVO.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
            return true;
        }
        QueryOrderVO queryOrderVO = insurPayService.queryOrderInfo(orderId);
        // 第三方支付不成功
        if (queryOrderVO.getPayStatus() != PayStatusEnum.PAY_SUCCESS.getStatusCode()) {
            return false;
        }
        // 比较订单金额和支付金额
        if (!Objects.equals(baseOrderVO.getTotalAmount(), queryOrderVO.getOrderAmount())) {
            throw new BadException(ExcptEnum.ORDER_PAY_BACK_ERROR.getCode(), "订单支付金额与产品金额不一致");
        }
//        // 保存支付Id
        orderMapper.updateOrderPaymentId(orderId, queryOrderVO.getPayTrxNo(), null);
        return true;
    }

    /**
     * 泛华保险支付回调
     *
     * @param orderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void handFhOrderPayedCallback(String orderId, String recommendId, String queryString) {

        SmBaseOrderVO baseOrder = orderMapper.getBaseOrderInfoByOrderId(orderId);
        log.info("泛华异步回调 {}->{}", orderId, recommendId);
        //如果订单信息为空 做补单操作 针对泛华H5对接的方式
        if (baseOrder == null) {
            // 保存回调记录
            fhOrderService.saveCallback(orderId, recommendId, queryString);
            SmOrderSupmtDTO dto = new SmOrderSupmtDTO();
            dto.setFhOrderId(orderId);
            dto.setRecommendId(recommendId);
            orderManageService.insertSupplementOrder(dto, null);
            updateOrderPolicyInfo(dto.getFhOrderId());

            // 将回调记录修改成已处理
            fhOrderService.updateState2Success(orderId);
        } else {
            OrderQueryResponse orderQueryResp = getExternalThirdPartyOrderInfoByOrderId(orderId);
            if (!Objects.equals(orderQueryResp.getOrderInfo().getOrderState(), SmConstants.ORDER_STATUS_PAYED)) {
                log.error("泛华支付回调获取支付状态不是已支付order={}", JSON.toJSONString(orderQueryResp));
                return;
            }
            orderMapper.updateOrderPayStatus(orderId, SmConstants.ORDER_STATUS_PAYED);
            orderMapper.updateOrderAppStatus(orderId, SmConstants.POLICY_STATUS_PROCESS, null);
            updateOrderPaymentTimeAndCommission(orderId);
            // 发布订单提成变更事件
            SpringFactoryUtil.getBean(EventBusEngine.class).publish(new OrderCommissionChangeEvent(orderId));
        }
    }

    /**
     * 更新订单支付佣金和支付时间，
     * 抽取用户，计算佣金明细，代理人佣金明细
     * =========================================
     * 注意 一定要更新订单状态支付成功才能调用此方法
     *
     * @param orderId
     */
    public void updateOrderPaymentTimeAndCommission(String orderId) {
        // 设置提成
        SmBaseOrderVO baseOrder = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (!Objects.equals(baseOrder.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
            log.error("订单= {} 未支付成功不能计算提成。", orderId);
            return;
        }
        SmCommissionSettingVO settingVo = cmsMapper.getCommissionSettingByPlanId(baseOrder.getPlanId(), baseOrder.getCreateTime());
        String commissionId = null;
        if (settingVo != null) {
            commissionId = String.valueOf(settingVo.getId());
        } else {
            log.warn("订单= {} 没有正确的提成配置。", orderId);
        }
        orderMapper.updateOrderCommission(orderId, commissionId);
        orderMapper.updateOrderPaymentTime(orderId);
    }

    /**
     * 与泛华同步更新存小额保险保单信息
     *
     * @param orderId
     * @return 更新保单后身份证保单状态map insIdNumber->appStatus
     */
    public Map<String, String> updateOrderPolicyInfo(String orderId) {
        return wrapper.updateOrderPolicyInfo(orderId);
    }

    /**
     * 更新订单成已过期状态
     *
     * @param orderId
     * @return
     */
    public void updateExpireOrderInfo(String orderId) {
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (baseOrderVO == null) {
            log.warn("订单不存在:{}", orderId);
            return;
        }
        String channel = baseOrderVO.getChannel();
        if (OrderConfigRule.skipPayExpire(channel)) {
            log.warn("{}-该渠道的单跳过支付过期逻辑~");
            return;
        }

        /**
         * 主要将已核保但是未支付的单的状态置为过期状态；
         */
        if (Objects.equals(baseOrderVO.getPayStatus(), SmConstants.ORDER_STATUS_TO_PAY)) {
            int productId = baseOrderVO.getProductId();

            SmProductDetailVO productDetail = productService.getProductById(productId);

            int effectWaitingDay = productDetail.getEffectWaitingDayMin();
            Date todayBuyStartTime = DateUtil.addDay(DateUtil.getBeginOfDay(new Date()), effectWaitingDay);

            if (todayBuyStartTime.compareTo(baseOrderVO.getStartTime()) > 0) {
                orderMapper.updateOrderPayStatus(orderId, SmConstants.ORDER_STATUS_EXPIRE);
            }
        }
    }


    /**
     * 获取本地数据
     */
    public SmBaseOrderVO getLocalBaseOrderInfo(String orderId) {
        SmBaseOrderVO baseOrderInfoByOrderId = orderMapper.getBaseOrderInfoByOrderId(orderId);
        List<SmOrderSmsNotifyVO> insureds = orderMapper.getOrderInsuredByOrderId(orderId);
        if (!CollectionUtils.isEmpty(insureds)) {
            baseOrderInfoByOrderId.setInsuredId(insureds.iterator().next().getInsuredId());
        }
        return baseOrderInfoByOrderId;
    }

    /**
     * 查询订单信息
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getOrderInfo(String orderId) {

        return wrapper.getOrderInfo(orderId);
    }

    /**
     * 查询订单信息
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getOrderInfo4Local(String orderId) {
        return wrapper.getOrderInfoForLocal(orderId);
    }

    /**
     * 查询订单信息V3版本
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getOrderInfo4Local_V3(String orderId, Integer insuredSn) {
        OrderQueryResponse response = OrderConvertor.mapperOrderQuery4LocalV3(orderId, Boolean.TRUE, insuredSn);

        // 对客户敏感数据进行脱敏
        return permissionUtil.maskCustomerSensitiveFields2(response, bmsConfig.getOrderSensitiveDetail());

    }

    /**
     * 查询车险订单信息V3
     *
     * @param orderId
     * @return
     */
    public AutoOrderQueryResponse getAutoOrderInfo4Local_V3(String orderId) {
        AutoOrderQueryResponse response = OrderConvertor.mapperAutoOrderQuery4LocalV3(orderId);
        AutoOrder autoOrderByOrderNo = autoOrderMapper.getAutoOrderByOrderNo(orderId);
        if (Objects.nonNull(autoOrderByOrderNo)) {
            response.getOrderInfo().setTotalAmount(autoOrderByOrderNo.getTotalAmount().toString());
            response.getOrderInfo().setJointIssuingFlag(autoOrderByOrderNo.getJointIssuingFlag());
            response.getOrderInfo().setInsurancePlace(autoOrderByOrderNo.getInsurancePlace());
            response.getOrderInfo().setBingDrivingFlag(autoOrderByOrderNo.getBingDrivingFlag());
            response.getOrderInfo().setContinuePurchaseFlag(autoOrderByOrderNo.getContinuePurchaseFlag());
        }

        // 对客户敏感数据进行脱敏
        return permissionUtil.maskCustomerSensitiveFields2(response, bmsConfig.getOrderSensitiveDetail());

    }

    /**
     * 获取订单支付URL
     *
     * @param orderId
     * @param type    0=正常投保下单,1=批改
     * @return
     */
    public String getOrderPayUrl(String orderId, Integer type) {
        return wrapper.getOrderPayUrl(orderId, type);
    }

    /**
     * 获取第三方订单详情信息
     *
     * @return
     */
    private OrderQueryResponse getExternalThirdPartyOrderInfoByOrderId(String orderId) {
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        OrderQueryRequest queryDTO = new OrderQueryRequest();
        queryDTO.setOrderId(orderId);
        queryDTO.setPayId(baseOrderVO.getPayId());
        queryDTO.setAppNo(baseOrderVO.getAppNo());
        queryDTO.setSubmitTime(baseOrderVO.getSubmitTime());
        String channel = orderMapper.getBaseOrderInfoByOrderId(orderId).getChannel();
        return getExternalThirdPartyOrderService(channel).queryChannelOrderInfo(queryDTO);
    }

    /**
     * 获取渠道订单service
     *
     * @param channel
     * @return
     */
    public ChannelOrderService getExternalThirdPartyOrderService(String channel) {
        try {
            return SpringFactoryUtil.getBean(channel, ChannelOrderService.class);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.PRODUCT_CHANNEL_ERROR, e);
        }
    }
}
