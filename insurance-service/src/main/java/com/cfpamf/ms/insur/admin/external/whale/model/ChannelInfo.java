package com.cfpamf.ms.insur.admin.external.whale.model;


import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class ChannelInfo {

    String branchName;

    String channelCode;

    String channelName;

    /**
     * 推荐人工号(代理人)
     */
    String policyReferrerBusinessCode;

    /**
     * 推荐人姓名(代理人)
     */
    String policyReferrerName;

    String referrerName;


    /**
     * referrerType	渠道推荐人类型 0:推荐人 1:代理人
     */
    Integer referrerType;

    @ApiModelProperty("保单管护人工号")
    String referrerWno;

    /**
     * 三方平台编码
     */
    String thirdPartyPlatformCode;

    /**
     * 三方客户编码
     */
    String thirdPartyCustomerCode;

    /**
     * 小鲸站长身份证
     */
    String propagandistIdCard;

    String propagandistName;

    @ApiModelProperty("初始推荐人工号")
    String customerManagerChannelCode;

    @ApiModelProperty("初始推荐人机构")
    String customerManagerChannelOrgCode;

}
