package com.cfpamf.ms.insur.weixin.pojo.dto.order;

import com.cfpamf.ms.insur.weixin.constant.EnumChonghoPay;
import lombok.Data;

@Data
public class FastOrderDTO {

    private String orderId;

    private String endorId;

    private String proposalNo;

    private String policyNo;

    private String endorsementNo;

    private String payType;

    private String payStatus;

    public boolean offlinepay() {
        return EnumChonghoPay.offline.name().equals(payType);
    }
}
