package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.order.EnumGroupCorrectType;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmCancelRefundMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmCancelRefundNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.enums.order.InvoiceTypeEnum;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.event.OrderImportEvent;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.CommonTools;
import com.cfpamf.ms.insur.admin.external.tk.TkApiProperties;
import com.cfpamf.ms.insur.admin.external.tk.TkConsts;
import com.cfpamf.ms.insur.admin.external.tk.TkOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.tk.TkPayOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.tk.model.*;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.*;
import com.cfpamf.ms.insur.admin.external.tk.util.TkEmpV2Util;
import com.cfpamf.ms.insur.admin.external.tk.util.TkMd5Encrypt;
import com.cfpamf.ms.insur.admin.external.tk.util.TkSignUtil;
import com.cfpamf.ms.insur.admin.external.tk.util.URLParser;
import com.cfpamf.ms.insur.admin.external.zhongan.model.enums.ZaGroupPolicyStatusEnum;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupInsuredInfo;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.order.*;
import com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigMapper;
import com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfig;
import com.cfpamf.ms.insur.admin.service.DictionaryService;
import com.cfpamf.ms.insur.admin.service.SmCancelRefundService;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupNotifyService;
import com.cfpamf.ms.insur.admin.service.order.group.GroupRuleAdaptor;
import com.cfpamf.ms.insur.admin.service.order.group.TkEmployerQueryService;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.common.enums.EnumMonitor;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.common.service.monitor.MonitorHelper;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyInvoice;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import com.cfpamf.ms.pay.facade.constant.PayStatusEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 众安保险
 *
 * <AUTHOR> 2020/3/19 09:30
 */
@Service
@Slf4j
public class TkOrderService extends SmOrderServiceZhnxPayAdapter {

    static String NOT_ENDORSEMENT = "1";

    static int INTERCEPT_LENGTH = 5;

    @Autowired
    TkOrderServiceAdapter adapter;

    @Autowired
    TkApiProperties properties;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    SmCancelRefundService refundService;

    @Autowired
    SmCancelRefundNotifyMapper notifyMapper;

    @Autowired
    SmCancelRefundMapper refundMapper;

    @Autowired
    TkApiProperties tkApiProperties;

    @Autowired
    protected SmOrderGroupNotifyService smOrderGroupNotifyService;

    @Autowired
    private TkPayOrderServiceAdapter orderServiceAdapter;

    @Autowired
    DictionaryService dictionaryService;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private SmOrderInsuredMapper smOrderInsuredMapper;

    @Autowired
    private RenewalConfigMapper renewalConfigMapper;

    @Autowired
    private TxServiceManager txServiceManager;

    @Autowired
    private TkEmployerQueryService tkOrderQueryService;

    /**
     * 需要退费的失效类型
     * 15：保单注销,保司线下操作批改,需退费
     * 16：退保
     * 18：终止保险合同
     * 70：保单退保后,进行的协议补差退费
     * 71：保单失效，分期保单,超过宽限期未缴费
     *
     * @方亚：保单失效状态是否需要处理
     */
    static Set<String> POLICY_AMT_TYPE = Sets.newHashSet("15", "16", "18");

    @Override
    public boolean support(String channel) {
        return Objects.equals(channel, EnumChannel.TK.getCode());
    }

    @Override
    protected String channel() {
        return EnumChannel.TK.getCode();
    }

    @Override
    protected ChannelOrderService orderService() {
        return adapter;
    }

    public Object policyLose(HttpServletRequest request, HttpServletResponse response) throws IOException {
        byte[] bys = IOUtils.toByteArray(request.getInputStream());
        String message = new String(bys, StandardCharsets.UTF_8);

        log.info("泰康保单状态变更通知参数为: {}", message);

        Map<String, Object> map = objectMapper.readValue(bys, Map.class);

        Object applyContent = map.get("apply_content");
        if (Objects.isNull(applyContent)) {
            return TkRespWrapper.error("保单失效详情为空");
        }

        TkReqWrapper<TkPolicyLose> o = objectMapper.readValue(bys, new TypeReference<TkReqWrapper<TkPolicyLose>>() {
        });
        boolean check = TkMd5Encrypt.check(properties.getNotifyToken(), applyContent, o.getSign());
        if (!check) {
            return TkRespWrapper.error("签名校验错误");
        }

        TkPolicyLose policyEvent = o.getApplyContent();
        String orderId = null;
        if (!Objects.isNull(policyEvent)) {
            orderId = policyEvent.getPolicyNo();
        }
        super.saveNotifyLog(orderId, EnumChannel.TK.getCode(), message);

        return policyLose(o);
    }

    /**
     * 保单失效
     */
    public TkRespWrapper policyLose(TkReqWrapper<TkPolicyLose> lose) {
        TkPolicyLose applyContent = lose.getApplyContent();
        String policyNo = applyContent.getPolicyNo();
        String endPolicyType = applyContent.getEndPolicyType();
        String endorsementNo = applyContent.getEndorsementNo();

        if (!POLICY_AMT_TYPE.contains(endPolicyType)) {
            log.warn("泰康保单状态变更消息,未知状态,请开发人员跟保司核实:{},{},{}", endPolicyType, policyNo, endorsementNo);

            String format = "泰康保单状态变更消息,未知状态,请开发人员跟保司核实:{0},{1},{2}";
            monitorService.addMonitor(EnumMonitor.SURRENDER, format, endPolicyType, policyNo, endorsementNo);
            return TkRespWrapper.error("系统无法识别该保单失效类型:" + applyContent.getEndPolicyType());
        }

        List<TkInstallmentsDetail> installmentsDetail = applyContent.getInstallmentsDetail();
        if (CollectionUtils.isEmpty(installmentsDetail) || StringUtils.isBlank(installmentsDetail.iterator().next().getRefundAmount())) {
            return TkRespWrapper.error("保单失效类型为" + applyContent.getEndPolicyType() + "，需要退费金额为空！");
        }

        SmOrderInsured query = new SmOrderInsured();
        query.setPolicyNo(policyNo);
        List<SmOrderInsured> smOrderInsuredList = insuredMapper.select(query);

        if (CollectionUtils.isEmpty(smOrderInsuredList)) {
            return TkRespWrapper.error(applyContent.getPolicyNo() + "保单号不存在");
        }

        SmOrderInsured smOrderInsured = smOrderInsuredList.get(0);
        try {
            List<SmOrderItem> itemList = orderItemMapper.selectByOrderId(smOrderInsured.getFhOrderId());
            /**
             * 2023-07-20：按方亚提供的需求：
             * 团险&雇主险：旧的泰康退保接口，团险整单退保暂停对接，系统告警提醒业务走线下处理
             * 个险：暂时全部对接为退保，新增退保类型：18
             */
            if (CollectionUtils.isEmpty(itemList)) {
                doPersonPolicyLose(applyContent, smOrderInsured);
            } else {
                //泰康团险/雇责险/家庭险退保处理
                //doGroupPolicyLose(applyContent, smOrderInsured);
                log.warn("有雇主险整单退保，目前已经关闭线上对接的入口，请联系业务人员在管理系统操作保全");

                String format = "有雇主险整单退保，目前已经关闭线上对接的入口，请联系业务人员在管理系统操作保全:{0},{1}";
                monitorService.addMonitor(EnumMonitor.SURRENDER, format, policyNo, endPolicyType);
            }
            return TkRespWrapper.success();
        } catch (Exception e) {
            log.error("泰康退保流程异常", e);
            String format = "泰康退保异常:{0},{1}";
            monitorService.addMonitor(EnumMonitor.SURRENDER, format, policyNo, endPolicyType);
            return TkRespWrapper.error(e.getMessage());
        }
    }

    private void doPersonPolicyLose(TkPolicyLose applyContent, SmOrderInsured smOrderInsured) {
        SmCancelRefund refund = cvtRefundModel(applyContent, smOrderInsured);
        log.info("申请退费{}", refund);
        refundService.refund(refund);
        //重算提层
        busEngine.publish(new OrderCommissionChangeEvent(smOrderInsured.getFhOrderId()));
    }

    /**
     * 已弃用
     * 雇主险&团险：暂不支持整单退保
     *
     * @param applyContent
     * @param smOrderInsured
     */
    @Deprecated
    private void doGroupPolicyLose(TkPolicyLose applyContent, SmOrderInsured smOrderInsured) {
        log.info("雇主责任险{}申请退费", smOrderInsured.getPolicyNo());
        SmCancelRefund refund = cvtRefundModel(applyContent, smOrderInsured);
        Set<String> fhOrderIdSet = refundService.refundGroupCancel(smOrderInsured.getPolicyNo());
        //退保保费
        smOrderPolicyService.updatePolicyCancelAmount(refund.getFhOrderId(), refund.getPolicyNo(), refund.getChannel(), refund.getRefundAmount(), LocalDateTime.now(), Boolean.FALSE);

        refundService.batchPushCommissionChangeEvent(fhOrderIdSet);
    }


    /**
     * 退费通知
     *
     * @param refundId
     */
    public void refundNotify(Integer refundId) {

        SmCancelRefundNotify notify = notifyMapper.selectByRefundId(refundId);

        if (Objects.isNull(notify)) {

            SmCancelRefund cancelRefund = refundMapper.selectByPrimaryKey(refundId);
            notify = new SmCancelRefundNotify();
            notify.setVersion(0);
            notify.setRefundId(refundId);
            notify.setFhOrderId(cancelRefund.getFhOrderId());
            notify.setStatus(Boolean.FALSE);
            notifyMapper.insertUseGeneratedKeys(notify);
        }
        if (Boolean.FALSE.equals(notify.getStatus()) && notify.getVersion() < 4) {
            notify.setVersion(notify.getVersion() + 1);
            try {
                adapter.refundNotify(refundId);
                notify.setStatus(Boolean.TRUE);
                notifyMapper.updateByPrimaryKeySelective(notify);
            } catch (Exception e) {
                log.warn("退费通知失败", e);
                notify.setRemark(e.getMessage());

                notifyMapper.updateByPrimaryKeySelective(notify);
            }
        }

    }

    public void jumpOnlineService(String userId, Integer productId, HttpServletResponse response) throws IOException {

        List<SmPlanVO> productPlans = productService.getProductPlans(productId);
        if (CollectionUtils.isEmpty(productPlans)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "产品未配置计划");
        }
        SmPlanVO next = productPlans.iterator().next();
        //计划编码里面的第一个就是泰康的产品编码
        String fhProductId = next.getFhProductId().split("-")[0];
        adapter.onlineServiceRes(userId, fhProductId, response);
    }


    private SmCancelRefund cvtRefundModel(TkPolicyLose lose, SmOrderInsured insured) {


        SmCancelRefund refund = new SmCancelRefund();
        refund.setSysAcceptTime(LocalDateTime.now());
//        refund.setIdNumber(lose.getIdNo());
        refund.setIdNumber(insured.getIdNumber());
        refund.setRefundStatus(PayStatusEnum.PAY_SUCCESS.getStatusCode() + "");
        refund.setFhOrderId(insured.getFhOrderId());
        refund.setPolicyNo(lose.getPolicyNo());
        refund.setRetEndTime(lose.getRetEndTime());
        if (insured.getFhOrderId().contains(EnumChannel.TK_PAY.getCode().toUpperCase())) {
            refund.setChannel(EnumChannel.TK_PAY.getCode());
            refund.setEnabledFlag(1);
        } else {
            SmBaseOrderVO baseOrderInfoByOrderId = orderMapper.getBaseOrderInfoByOrderId(insured.getFhOrderId());
            List<DictionaryVO> tkRefund = dictionaryService.listByType("tkRefund");
            refund.setChannel(EnumChannel.TK.getCode());
            //如果不是配置的需要退费的产品就不显示
            if (tkRefund.stream().noneMatch(r -> Objects.equals(r.getCode(), baseOrderInfoByOrderId.getProductId() + ""))) {
                refund.setEnabledFlag(1);
            }
        }
        refund.setRetTime(lose.getRetTime());
        refund.setRefundType(1);
        refund.setRefundAmount(new BigDecimal(lose.getRetMoney()).divide(new BigDecimal("100"),
                2, RoundingMode.HALF_DOWN));
        refund.setPremium(new BigDecimal(lose.getPremium()));
        refund.setRemark(JSON.toJSONString(lose, SerializerFeature.MapSortField));
        return refund;
    }

    @Override
    public void handAICheckAccept(String orderId, String questionnaireId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        orderMapper.updateOrderPayStatus(orderId, SmConstants.ORDER_STATUS_CHECKED);
        String aiCheckFrontUrl = tkApiProperties.getAiCheckFrontUrl();
        response.sendRedirect(aiCheckFrontUrl + "?channel=" + channel() + "&questionnaireId=" + questionnaireId + "&orderId=" + orderId);
    }

    @Override
    public List<AICheckQueryResponse> aiCheckQuery(String orderId, String questionnaireId) {
        AICheckQueryRequest request = new AICheckQueryRequest();
        request.setOrderId(orderId);
        request.setQuestionnaireId(questionnaireId);
        return adapter.aiCheckQuery(request);
    }


    /************** begin add by zhangjian 2021-06-15 雇主责任险********************/
    @Override
    public void handChannelCallBackEmployerAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        TkReqBox<TkEmpOrderReq> reqBox = (TkReqBox<TkEmpOrderReq>) object;
        if (reqBox == null) {
            return;
        }
        String requestId = reqBox.getRequestId();
        String lockKey = "tk:employerCallback:" + requestId;
        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }
            if (!checkEmployerSign(request)) {
                log.info("泰康雇主责任险验签失败");
                response.getWriter().write(JSONObject.toJSONString(failResp(reqBox.getRequestId(), "泰康雇主责任险验签失败")));
                return;
            }

            if (validateExist(reqBox)) {
                response.getWriter().write(JSONObject.toJSONString(successResp(reqBox.getRequestId())));
                return;
            }
            String policyNo = reqBox.getRequestData().getPolicyNo();
            String body = JSONObject.toJSONString(object);
            //添加回调记录(状态为处理中的记录)
            SmOrderGroupNotify notify = smOrderGroupNotifyService.addOrderGroupNotifyRecord(requestId,
                    GroupNotify.GroupType.EMPLOYER.getCode(),
                    EnumChannel.TK.getCode(),
                    policyNo,
                    null,
                    GroupNotify.StatusEnum.DOING.getCode(),
                    "1",
                    0L,
                    body);

            //如果添加记录为未处理状态，则后续的业务处理需要屏蔽，否则会与定时任务存在并发的可能
            //新契约
            if (Objects.equals(reqBox.getRequestData().getIsOutEdor(), NOT_ENDORSEMENT)) {
                SmCreateOrderSubmitRequest submitRequest = adapter.cvtByEmployerOrderNotify(reqBox.getRequestData());
                String productId = submitRequest.getProductInfo().getProductId();
                SmPlanVO planVO = productService.getPlanByFhProductId(productId);
                if (Objects.nonNull(planVO)) {
                    submitRequest.setProductId(planVO.getProductId() + "");
                    submitRequest.setPlanId(planVO.getId());
                }
                saveEmpOrderInfo(submitRequest, null, planVO);
            } else {
                //批单处理 todo
                throw new BizException(ExcptEnum.DATA_ERROR_801304.getCode(), "暂不支持批改流程逻辑");

            }
            smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DONE.getCode());

            response.getWriter().write(JSONObject.toJSONString(successResp(reqBox.getRequestId())));
        } catch (BizException e) {
            log.warn("泰康雇主险保单处理失败", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_APPLY, "泰康雇主责任险批改失败:" + requestId);
            response.getWriter().write(JSONObject.toJSONString(failResp(reqBox.getRequestId(), e.getMessage())));
        } catch (Exception e) {
            log.warn("泰康雇主险保单处理失败", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_APPLY, "泰康雇主责任险批改失败:" + requestId);
            response.getWriter().write(JSONObject.toJSONString(failResp(reqBox.getRequestId(), "系统异常")));
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }

    }

    public boolean validateExist(TkReqBox<TkEmpOrderReq> reqBox) {
        String endorsementNo = Objects.equals(reqBox.getRequestData().getIsOutEdor(), NOT_ENDORSEMENT) ? "" : "";
        return doValidateExist(reqBox.getRequestId(), reqBox.getRequestData().getPolicyNo(), endorsementNo);
    }


    public boolean doValidateExist(String requestId, String policyNo, String endorsementNo) {
        //验证请求流水号
        if (existRequestId(requestId)) {
            log.info("泰康雇主责任险请求流水号{}已存在", requestId);
            return true;
        }
        //验证保单号和批单号，当前接口还未存在批单号字段,存在后运算符：后改取批单号
        //String endorsementNo = Objects.equals(reqBox.getRequestData().getIsOutEdor(),NOT_ENDORSEMENT)?"":"";
        if (existPolicyNoAndEndorsementNo(policyNo, endorsementNo)) {
            log.info("泰康雇主责任险保单号{}与批单号{}已存在", policyNo, endorsementNo);
            return true;
        }
        return false;
    }

    /**
     * 验证requestId是否已经存在
     *
     * @param requestId
     * @return
     */
    private boolean existRequestId(String requestId) {
        log.info("requestId={}", requestId);
        return smOrderGroupNotifyService.getByRequestId(requestId, EnumChannel.TK.getCode()) != null;
    }

    /**
     * 验证保单号和批单是否已经存在
     *
     * @param policyNo
     * @param endorsementNo
     * @return
     */
    private boolean existPolicyNoAndEndorsementNo(String policyNo, String endorsementNo) {
        log.info("policyNo={}", policyNo);
        return smOrderGroupNotifyService.getOrderGroupNotifyByPolicyNo(policyNo, endorsementNo) != null;
    }

    private boolean checkEmployerSign(HttpServletRequest request) {
        try {
            String nonce = request.getParameter("nonce");
            String timestamp = request.getParameter("timestamp");
            String signature = request.getParameter("signature");
            log.info("nonce={}/timestamp={}/signature={}", nonce, timestamp, signature);

            return TkSignUtil.checkSignature(tkApiProperties.getAcceptEmployerSignKey(), signature, timestamp, nonce);
        } catch (Exception e) {
            log.error("泰康回调请求签名失败", e);
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "验签异常");
        }
    }

    private TkRespBox successResp(String requestId) {
        TkRespBox respBox = new TkRespBox();
        respBox.setRequestId(requestId);
        respBox.setResponseCode(TkConsts.CODE_SUCCESS);
        respBox.setResponseMsg("成功");
        respBox.setResponseTime(TkConsts.FMT_DATETIME.format(LocalDateTime.now()));
        return respBox;
    }

    private TkRespBox failResp(String requestId, String msg) {
        TkRespBox respBox = new TkRespBox();
        respBox.setRequestId(requestId);
        respBox.setResponseCode(TkConsts.CODE_FAIL);
        respBox.setResponseMsg(msg);
        respBox.setResponseTime(TkConsts.FMT_DATETIME.format(LocalDateTime.now()));
        return respBox;
    }

    public void saveEmpOrderInfo(OrderSubmitRequest dto, OrderSubmitResponse smOrderResp, SmPlanVO planVo) {

        SmCreateOrderSubmitRequest createDto = OrderConvertor.mapperSmCreateOrderSubmitRequest(dto, smOrderResp, planVo);

        createDto.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        createDto.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        createDto.setAppNo(dto.getAppNo());
        createDto.getInsuredPerson().forEach(ip -> ip.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS));

        txServiceManager.excute(()->{
            orderMapper.insertOrderApplicant(createDto);
            orderMapper.insertOrderInsured(createDto);
            orderItemMapper.insertList(createDto.getOrderItemList());
            if (createDto.getPropertyInfo() != null) {
                orderMapper.insertOrderPropertyInfo(createDto);
            }
            orderMapper.insertOrder(createDto);
            updateOrderPaymentTimeAndCommission(createDto.getFhOrderId());
        });
        log.info("订单数据更新完成");
        //异步执行提成 客户提取等操作
        List<SmCreateOrderSubmitRequest> submitRequestList = new ArrayList<>();
        submitRequestList.add(createDto);
        busEngine.publish(new OrderImportEvent(submitRequestList, Boolean.TRUE));

    }

    @Override
    public void handSyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException {
        response.sendRedirect(properties.getEmployerPayRedirectUrl());
    }

    /**
     * 主职工号  产品id
     *
     * @param userId
     * @param productId
     */
    public String genTkEmployerProduct(String userId, Integer productId, boolean oldProduct) {

        log.info("{}跳转到泰康雇主责任险产品页面{}", userId, productId);
        String h5Url = productService.getProductById(productId).getH5Url();
        return adapter.genAgentEmployerUrl(h5Url, userId);

    }

    /*** begin s52雇主责任险批改回传 ****/
    @Override
    public void handChannelCallBackEmployerEndorAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        TkReqBox<TkEmpEndorOrderReq> reqBox = (TkReqBox<TkEmpEndorOrderReq>) object;
        if (reqBox == null) {
            log.error("泰康雇主责任险批改V1报文为空");
            return;
        }
        TkEmpEndorOrderReq data = reqBox.getRequestData();
        if (data == null) {
            log.error("泰康雇主责任险批改V1报文为空");
            return;
        }
        String policyNo = data.getPolicyNo();
        String endorsementNo = data.getEndorNo();
        //必须为保单为redis的key，因为存在一个保单多个批单同时发送过来的情况
        String lockKey = "tk:employerEndorCallback:" + policyNo;
        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }
            if (!checkEmployerSign(request)) {
                log.info("泰康雇主责任险批改验签失败");
                response.getWriter().write(JSONObject.toJSONString(failResp(reqBox.getRequestId(), "泰康雇主责任险批改验签失败")));
                return;
            }

            if (validateEndorExist(reqBox)) {
                response.getWriter().write(JSONObject.toJSONString(successResp(reqBox.getRequestId())));
                return;
            }
            String requestId = reqBox.getRequestId();
            Long correctTimestamp = 0L;
            String body = JSONObject.toJSONString(object);
            LocalDateTime correctDate = data.getEndorDate();
            if(correctDate!=null){
                correctTimestamp = correctDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            }

            //添加回调记录(状态为处理中的记录)
            SmOrderGroupNotify notify = smOrderGroupNotifyService.addOrderGroupNotifyRecord(requestId,
                    GroupNotify.GroupType.EMPLOYER.getCode(),
                    EnumChannel.TK.getCode(),
                    policyNo,
                    endorsementNo,
                    GroupNotify.StatusEnum.DOING.getCode(),
                    "1",
                    correctTimestamp,
                    body);

            doEndor(policyNo, notify, reqBox);

            response.getWriter().write(JSONObject.toJSONString(successResp(reqBox.getRequestId())));
        } catch (BizException e) {
            log.warn("泰康雇主责任险-批改失败:{}", policyNo, e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_CORRECT, "泰康雇主责任险-批改失败:{}", policyNo);
            response.getWriter().write(JSONObject.toJSONString(failResp(reqBox.getRequestId(), e.getMessage())));
        } catch (Exception e) {
            log.warn("泰康雇主责任险-批改失败 ", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_CORRECT, "泰康雇主责任险-批改失败:{}", policyNo);
            response.getWriter().write(JSONObject.toJSONString(failResp(reqBox.getRequestId(), "系统异常")));
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }
    }

    private void doEndor(String policyNo, SmOrderGroupNotify notify, TkReqBox<TkEmpEndorOrderReq> reqBox) {
        SmPolicyInsured policyInsured = orderMapper.listOrderInsuredByPolicyNo(policyNo).get(0);
        SmBaseOrderVO smOrder = orderMapper.getBaseOrderInfoByOrderId(policyInsured.getFhOrderId());
        SmPlanVO planVO = productService.getPlanById(smOrder.getPlanId());
        SmOrderApplicant applicant = orderMapper.selectOrderApplicantByOrderId(policyInsured.getFhOrderId());
        List<SmCreateOrderSubmitRequest> submitRequestList = adapter.cvtByEmployerEndorsementNotify(policyNo, reqBox.getRequestData(), smOrder, applicant, planVO);
        batchSaveOrderInfoGroup(submitRequestList);


        smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DONE.getCode());
    }

    public boolean validateEndorExist(TkReqBox<TkEmpEndorOrderReq> reqBox) {
        return doValidateExist(reqBox.getRequestId(), reqBox.getRequestData().getPolicyNo(), reqBox.getRequestData().getEndorNo());
    }

    /*** end s52雇主责任险批改回传 ****/
    /************** end add by zhangjian 2021-06-15 雇主责任险********************/

    /*****s63 add by zhangjian 2022-06-28 begin****/


    @Override
    public void handChannelCallBackV2EmployerAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        TkEmpV2Req reqBox = (TkEmpV2Req) object;
        if (reqBox == null) {
            return;
        }
        String requestId = reqBox.getHead().getRequest_id();
        String lockKey = "tk:v2:employerCallback:" + requestId;

        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }
            log.info("reqBox={}", reqBox);
            if (!checkV2EmployerSign(reqBox)) {
                log.error("泰康雇主责任险V2接口验签失败，请开发人员跟保司沟通");
                response.getWriter().write(JSONObject.toJSONString(failV2Resp(reqBox, "验签失败")));
                return;
            }

            String bodyJson = decryptV2Body(reqBox);
            TkEmpV2Order order = objectMapper.readValue(bodyJson, new TypeReference<TkEmpV2Order>() {
            });
            if (validateOrderV2Exist(requestId, order)) {
                log.warn("该保单数据已同步，重复请求:{}",requestId);
                response.getWriter().write(JSONObject.toJSONString(successV2Resp(reqBox)));
                return;
            }

            //添加回调记录(状态为处理中的记录)
            SmOrderGroupNotify notify = smOrderGroupNotifyService.addOrderGroupNotifyRecordV2(requestId,
                    GroupNotify.GroupType.EMPLOYER.getCode(),
                    EnumChannel.TK.getCode(),
                    order);

            SmCreateOrderSubmitRequest submitRequest = adapter.cvtByV2EmployerOrderNotify(order);
            String productId = submitRequest.getProductInfo().getProductId();
            SmPlanVO planVO = productService.getPlanByFhProductId(productId);
            if (Objects.nonNull(planVO)) {
                submitRequest.setProductId(planVO.getProductId() + "");
                submitRequest.setPlanId(planVO.getId());
            }
            saveEmpOrderInfo(submitRequest, null, planVO);

            smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DONE.getCode());

            response.getWriter().write(JSONObject.toJSONString(successV2Resp(reqBox)));
        } catch (BizException e) {
            log.error("泰康雇主责任保单处理失败", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_APPLY, "泰康雇主责任险批改失败:" + requestId);
            response.getWriter().write(JSONObject.toJSONString(failV2Resp(reqBox, e.getMessage())));
        } catch (Exception e) {
            log.error("泰康雇主责任保单处理失败", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_APPLY, "泰康雇主责任险批改失败:" + requestId);
            response.getWriter().write(JSONObject.toJSONString(failV2Resp(reqBox, "系统异常")));
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }
    }

    private String decryptV2Body(TkEmpV2Req reqBox) {
        return TkEmpV2Util.decryptAES(reqBox.getBody(), tkApiProperties.getEmployerV2AesKey());
    }

    @Override
    public void handChannelCallBackV2EmployerTicketAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            TkEmpTicket ticket = (TkEmpTicket) object;
            PolicyInvoice policyInvoice = new PolicyInvoice();

            if (StringUtils.isNotBlank(ticket.getBusinessNo()) && ticket.getBusinessNo().length() > INTERCEPT_LENGTH) {
                //保司规则：去掉后5位数字提取出剩下的部分就是保单号
                String policyNo = ticket.getBusinessNo().substring(0, ticket.getBusinessNo().length() - INTERCEPT_LENGTH);
                policyInvoice.setPolicyNo(policyNo);
            } else {
                response.getWriter().write(JSONObject.toJSONString(ticketV2Resp(TkEmpTicketResp.FAIL_CODE, "BusinessNo字段违反约定规则" + ticket.getBusinessNo())));
                return;
            }
            policyInvoice.setInvoiceStatus(ticket.getInvoiceStatus());
            policyInvoice.setInvoiceCode(ticket.getInvoiceCode());
            policyInvoice.setInvoiceNo(ticket.getInvoiceNumber());
            policyInvoice.setChannel(EnumChannel.TK.getCode());
            policyInvoice.setInvoiceDate(ticket.getInvoiceCreateDate());
            policyInvoice.setMailingTime(ticket.getTraceDate());
            policyInvoice.setWaybillNo(ticket.getWayBillNumber());
            policyInvoice.setErrorMessage(ticket.getMsg());
            policyInvoice.setStatus(1);
            policyInvoice.setCreateTime(DateUtil.getNow());
            if (Objects.equals(ticket.getInvoiceType(), "1")) {
                policyInvoice.setInvoiceType(InvoiceTypeEnum.PAPER_TICKET.getCode());
            } else if (Objects.equals(ticket.getInvoiceType(), "0")) {
                policyInvoice.setInvoiceType(InvoiceTypeEnum.ELECTRONIC_TICKET.getCode());
            } else if (Objects.equals(ticket.getInvoiceType(), "3")) {
                policyInvoice.setInvoiceType(InvoiceTypeEnum.REMOVE.getCode());
            }
            policyInvoice.setInvoiceInfo(objectMapper.writeValueAsString(ticket));
            orderCoreService.addPolicyInvoice(policyInvoice);

            response.getWriter().write(JSONObject.toJSONString(ticketV2Resp(TkEmpTicketResp.SUCCESS_CODE, "回调成功执行!")));
        } catch (Exception e) {
            response.getWriter().write(JSONObject.toJSONString(ticketV2Resp(TkEmpTicketResp.FAIL_CODE, e.getMessage())));
        }

    }

    private TkEmpTicketResp ticketV2Resp(String code, String message) {
        TkEmpTicketResp respBox = new TkEmpTicketResp();
        respBox.setCode(code);
        respBox.setMessage(message);
        return respBox;
    }

    @Override
    public void handChannelCallBackV2EmployerEndorAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        TkEmpV2Req reqBox = (TkEmpV2Req) object;
        if (reqBox == null) {
            log.error("泰康雇主责任险批改参数为空");
            return;
        }
        TkEmpV2Head head = reqBox.getHead();
        if (head == null) {
            log.error("泰康雇主责任险批改参数Head为空");
            return;
        }
        String requestId = head.getRequest_id();
        String lockKey = "tk:v2:employerCallback:" + requestId;
        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }
            if (!checkV2EmployerSign(reqBox)) {
                log.info("泰康雇主责任险批改新接口验签失败");
                response.getWriter().write(JSONObject.toJSONString(failV2Resp(reqBox, "验签失败")));
                return;
            }
            String bodyJson = decryptV2Body(reqBox);
            TkEmpV2EndorOrder endorOrder = JSONObject.parseObject(bodyJson, TkEmpV2EndorOrder.class);

            employerCorrectV2(requestId, endorOrder);

            response.getWriter().write(JSONObject.toJSONString(successV2Resp(reqBox)));
        } catch (BizException e) {
            log.warn("雇主责任险保单批改失败原因：{}", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_CORRECT, "泰康雇主责任险批改失败:" + requestId);
            response.getWriter().write(JSONObject.toJSONString(failV2Resp(reqBox, e.getMessage())));
        } catch (Exception e) {
            log.warn("雇主责任险保单批改系统异常：{}", e);
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_CORRECT, "泰康雇主责任险批改失败:" + requestId);
            response.getWriter().write(JSONObject.toJSONString(failV2Resp(reqBox, "系统异常")));
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }

    }

    /**
     * 重新补偿保单流程
     * 已经找保司重推了错误的批单数据，新的数据记录在[temp_sm_order_group_notify] 临时表中
     * 从临时表读取正确的记录...
     *
     * @param policyNo
     */
    public void retryApiFlow(String policyNo, String endorsementNo,int version) throws IOException {
        List<SmOrderGroupNotify> notifyList = smOrderGroupNotifyService.listByPolicyNoAndStatus(policyNo, endorsementNo,-1);
        if (CollectionUtils.isEmpty(notifyList)) {
            log.info("该保单的数据均已处理成功，无需处理:{}", policyNo);
            return;
        }
        for (SmOrderGroupNotify smOrderGroupNotify : notifyList) {
            retryApiFlow(smOrderGroupNotify,version);
        }
    }

    public void retryApiFlow(SmOrderGroupNotify notify,int version) throws IOException {

        String policyNo = notify.getGroupPolicyNo();
        String endorsementNo = notify.getEndorsementNo();

        if (StringUtils.isBlank(endorsementNo)) {
            log.warn("暂不支持补偿新契约流程:{}", policyNo);
            return;
        }


        TempSmOrderGroupNotify fixMessage = smOrderGroupNotifyService.queryTempNotifyContent(policyNo, endorsementNo);

        String content = null;
        if (fixMessage != null) {
            content = fixMessage.getNotifyContent();
         }else{
            log.warn("回传报文数据为空:{},{}", policyNo, endorsementNo);
            SmOrderGroupNotifyMsg message = smOrderGroupNotifyService.getNotifyMsg(notify.getId());
            if(message!=null){
                content = message.getNotifyContent();
            }
        }
        if(StringUtils.isBlank(content)){
            log.warn("回传报文数据为空:{},{}", policyNo, endorsementNo);
            return;
        }

        if (Objects.equals(notify.getInterfaceVersion(), 2)) {
            log.info("泰康雇主责任险-批改流程补偿v2:{},{}", policyNo, endorsementNo);
            TkEmpV2CancelDetail cancelDetail = objectMapper.readValue(content, new TypeReference<TkEmpV2CancelDetail>() {
            });

            doV2Endor(cancelDetail.getPolicyNo(), notify, cancelDetail, version);
        } else {
            log.info("暂不支持V1版本批改流程:{},{}", policyNo, endorsementNo);
        }

    }

    public void manualDoV2(String policyNo) {
        SmOrderGroupNotify notify = smOrderGroupNotifyService.getOrderGroupNotifyByPolicyNo(policyNo, "");
        if (notify == null) {
            return;
        }
        if (notify.getStatus() == 2) {
            return;
        }
        SmOrderGroupNotifyMsg notifyMsg = smOrderGroupNotifyService.getNotifyMsg(notify.getId());
        String lockKey = "tk:v2:employerCallback:" + policyNo;

        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }

            TkEmpV2Order order = objectMapper.readValue(notifyMsg.getNotifyContent(), new TypeReference<TkEmpV2Order>() {
            });
            //添加回调记录(状态为处理中的记录)
            SmCreateOrderSubmitRequest submitRequest = adapter.cvtByV2EmployerOrderNotify(order);
            String productId = submitRequest.getProductInfo().getProductId();
            SmPlanVO planVO = productService.getPlanByFhProductId(productId);
            if (Objects.nonNull(planVO)) {
                submitRequest.setProductId(planVO.getProductId() + "");
                submitRequest.setPlanId(planVO.getId());
            }
            saveEmpOrderInfo(submitRequest, null, planVO);

            smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DONE.getCode());


        } catch (BizException e) {
            //e.printStackTrace();
            log.warn("雇主责任险保单新契约失败原因：{}", e);

        } catch (Exception e) {
            //e.printStackTrace();
            log.warn("雇主责任险保单新契约系统异常：{}", e);

        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }
    }


    public void manualDoEndor(String policyNo, String endorNo) throws IOException {
        SmOrderGroupNotify notify = smOrderGroupNotifyService.getOrderGroupNotifyByPolicyNo(policyNo, endorNo);
        if (notify == null) {
            log.warn("该批单保司推送的日志不存在");
            return;
        }
        if (notify.getStatus() == 2) {
            log.warn("该批单已处理成功,请误重复调用改流程");
            return;
        }

        SmOrderGroupNotifyMsg notifyMsg = smOrderGroupNotifyService.getNotifyMsg(notify.getId());

        String content = notifyMsg.getNotifyContent();
        if (Objects.equals(notify.getInterfaceVersion(), 2)) {
            log.info("泰康雇主责任险-批改流程补偿v2:{},{}", policyNo, endorNo);
            TkEmpV2CancelDetail cancelDetail = objectMapper.readValue(content, new TypeReference<TkEmpV2CancelDetail>() {
            });
            doV2Endor(cancelDetail.getPolicyNo(), notify, cancelDetail,1);
            return;
        }

        log.info("泰康雇主责任险-批改流程补偿v1:{},{}", policyNo, endorNo);
        TkReqBox<TkEmpEndorOrderReq> reqBox = objectMapper.readValue(content, new TypeReference<TkReqBox<TkEmpEndorOrderReq>>() {
        });
        doEndor(policyNo, notify, reqBox);
    }


    /**
     * 手工处理临时表的批改信息
     *
     * @param policyNo
     * @param endorNo
     * @param tempId
     * @throws IOException
     */
//    public void manualDoV2TempEndor(String policyNo, String endorNo, Integer tempId) throws IOException {
//        SmOrderGroupNotify notif = smOrderGroupNotifyService.getOrderGroupNotifyByPolicyNo(policyNo, endorNo);
//        if (notif == null) {
//            log.warn("原始保单{}/{}同步信息不存在", policyNo, endorNo);
//            return;
//        }
//        if (notif.getStatus() == 2) {
//            log.warn("原始保单{}/{}同步信息已处理，不能重复处理", policyNo, endorNo);
//            return;
//        }
//        TempSmOrderGroupNotify tempNotify = smOrderGroupNotifyService.getTempOrderGroupNotifyByPolicyNo(tempId);
//        if (tempNotify == null) {
//            log.warn("临时表中找不到保单{}的批改号为{}的批改记录{}", policyNo, endorNo, tempId);
//            return;
//        }
//        if (!Objects.equals(tempNotify.getGroupPolicyNo(), policyNo)) {
//            log.warn("临时表中{}记录的保单号{}与传入的保单号{}不一致", tempId, tempNotify.getGroupPolicyNo(), policyNo);
//            return;
//        }
//        if (!Objects.equals(tempNotify.getEndorsementNo(), endorNo)) {
//            log.warn("临时表中{}记录的批单号{}与传入的批单号{}不一致", tempId, tempNotify.getEndorsementNo(), endorNo);
//            return;
//        }
//        TkEmpV2CancelDetail cancelDetail = objectMapper.readValue(tempNotify.getNotifyContent(), new TypeReference<TkEmpV2CancelDetail>() {
//        });
//
//        String lockKey = "tk:v2:employerCallback:" + policyNo;
//        try {
//            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
//            if (!lockSuccess) {
//                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
//            }
//
//            SmPolicyInsured policyInsured = orderMapper.listOrderInsuredByPolicyNo(policyNo).get(0);
//            String orderId = policyInsured.getFhOrderId();
//
//            SmBaseOrderVO smOrder = orderMapper.getBaseOrderInfoByOrderId(orderId);
//            SmPlanVO planVo = productService.getPlanByOrderId(orderId);
//
//            //批改标的（包括更新雇员信息）
//            if (cancelDetail.getEndorType() != null && Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_REPLACE)) {
//                log.info("泰康产品-线下人员替换：{}", cancelDetail);
//                ZaGroupEndorsementRes endorsementRes = employerReplaceMember(policyNo, smOrder, cancelDetail);
//
//                GroupRuleAdaptor.addSubtractCheck(endorsementRes.getResult());
//                List<SmCreateOrderSubmitRequest> correctOrderList = orderServiceAdapter.buildReplaceMemberMessage(policyNo, endorsementRes, planVo);
//                super.batchSaveOrderInfoGroup(correctOrderList);
//            } else if (cancelDetail.getEndorType() != null && Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_REPLACE_ONLINE)) {
//                log.info("泰康产品-线上人员替换：{}", cancelDetail);
//                ZaGroupEndorsementRes endorsementRes = employerOnlineReplaceMember(policyNo, smOrder, cancelDetail);
//
//                GroupRuleAdaptor.addSubtractCheck(endorsementRes.getResult());
//                List<SmCreateOrderSubmitRequest> correctOrderList = orderServiceAdapter.buildReplaceMemberMessage(policyNo, endorsementRes, planVo);
//                super.batchSaveOrderInfoGroup(correctOrderList);
//
//            } else if (cancelDetail.getEndorType() != null && (Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_INCREASE)
//                    || Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_DECREASE)
//                    || Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_CANCEL))) {
//
//
//                GroupRuleAdaptor.addSubtractCheck(cancelDetail);
//                SmPlanVO planVO = productService.getPlanById(smOrder.getPlanId());
//                SmOrderApplicant applicant = orderMapper.selectOrderApplicantByOrderId(policyInsured.getFhOrderId());
//                List<SmCreateOrderSubmitRequest> submitRequestList = adapter.cvtByV2EmployerEndorsementNotify(policyNo, cancelDetail, smOrder, applicant, planVO);
//                batchSaveOrderInfoGroup(submitRequestList);
//            }
//            smOrderGroupNotifyService.updateOrderGroupStatus(notif.getId(), notif.getStatus(), GroupNotify.StatusEnum.DONE.getCode());
//            smOrderGroupNotifyService.updateTempOrderGroupStatus(tempNotify.getId(), tempNotify.getStatus(), GroupNotify.StatusEnum.DONE.getCode());
//        } catch (Exception e) {
//            log.warn("泰康团险新批改接口--交易流水号为{}中中保单号/批单号为{}/{}的批改信息处理存在异常", tempNotify.getRequestId(), policyNo, cancelDetail.getEndorNo(), e);
//
//        } finally {
//            tokenService.unlockBusinessToken(lockKey);
//        }
//
//    }

    /**
     * 泰康雇主险批改流程
     *
     * @param requestId
     * @param endorOrder
     */
    public void employerCorrectV2(String requestId, TkEmpV2EndorOrder endorOrder) {
        List<TkEmpV2CancelDetail> cancelDetailList = endorOrder.getCancelDetail();

        List<SmOrderGroupNotify> notifyList = smOrderGroupNotifyService.listOrderGroupNotifyByRequestId(requestId);

        for (TkEmpV2CancelDetail cancelDetail : cancelDetailList) {
            Optional<SmOrderGroupNotify> opt = notifyList.stream()
                    .filter(n -> Objects.equals(n.getGroupPolicyNo(), cancelDetail.getPolicyNo())
                            && Objects.equals(n.getEndorsementNo(), cancelDetail.getEndorNo()))
                    .findFirst();
            if (opt.isPresent()) {
                addTempOrderGroupNotify(requestId, cancelDetail);
                continue;
            }
            if (existPolicyNoAndEndorsementNo(cancelDetail.getPolicyNo(), cancelDetail.getEndorNo())) {
                addTempOrderGroupNotify(requestId, cancelDetail);
                continue;
            }
            SmOrderGroupNotify notify = smOrderGroupNotifyService.addOrderGroupNotifyRecordV2(
                    requestId,
                    GroupNotify.GroupType.EMPLOYER.getCode(),
                    EnumChannel.TK.getCode(),
                    cancelDetail);
            doV2Endor(cancelDetail.getPolicyNo(), notify, cancelDetail,1);
        }
    }

    private void addTempOrderGroupNotify(String requestId, TkEmpV2CancelDetail cancelDetail) {
        try {
            log.info("泰康雇主责任险保单号{}与批单号{}已存在", cancelDetail.getPolicyNo(), cancelDetail.getEndorNo());
            log.info("入临时表保单号{}与批单号{}", cancelDetail.getPolicyNo(), cancelDetail.getEndorNo());
            smOrderGroupNotifyService.addTempOrderGroupNotifyRecordByVersion(requestId, GroupNotify.GroupType.EMPLOYER.getCode(), EnumChannel.TK.getCode(),
                    cancelDetail.getPolicyNo(), cancelDetail.getEndorNo(), GroupNotify.StatusEnum.DOING.getCode(), "1", JSONObject.toJSONString(cancelDetail), 2);
        } catch (Exception e) {
            log.warn("添加临时表失败", e);
        }
    }


    public boolean validateOrderV2Exist(String requestId, TkEmpV2Order order) {
        return doValidateExist(requestId, order.getPolicy().getPolicyNo(), "");

    }

    private boolean checkV2EmployerSign(TkEmpV2Req req) {
        return TkEmpV2Util.checkSign(req, tkApiProperties.getEmployerV2Md5Key());
    }

    @Autowired
    private BusinessMonitorService monitorService;

    /**
     * 当前只能有一个保单号进行处理
     * PC008：泰康雇主责任险的[人员替换流程]优化
     * 雇主责任险的数据比较特殊：
     * 批改时有可能批改的生效时间比批改时间还早，因为保司处理数据时会先预存数据，然后依次处理
     * 考虑到佣金计算：[替换人员]的paymentTime 和surrenderTime都取当前操作的时间（@张健）
     *
     * @param policyNo
     * @param notify
     * @param cancelDetail
     */
    private void doV2Endor(String policyNo, SmOrderGroupNotify notify, TkEmpV2CancelDetail cancelDetail,int version) {
        String lockKey = "tk:v2:employerCallback:" + policyNo;
        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }

            SmPolicyInsured policyInsured = orderMapper.listOrderInsuredByPolicyNo(policyNo).get(0);
            String orderId = policyInsured.getFhOrderId();

            SmBaseOrderVO smOrder = orderMapper.getBaseOrderInfoByOrderId(orderId);
            SmPlanVO planVo = productService.getPlanByOrderId(orderId);

            if (cancelDetail.getEndorType() != null) {

            }
            //批改标的（包括更新雇员信息）
            if (cancelDetail.getEndorType() != null && Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_REPLACE)) {
                log.info("泰康产品-线下人员替换：{}", cancelDetail);
                ZaGroupEndorsementRes endorsementRes = employerReplaceMember(policyNo, smOrder, cancelDetail);
                if(CollectionUtils.isEmpty(endorsementRes.getResult().getInsuredList())){
                    log.info("泰康产品-线下人员替换，被保人身份证未发生变化，不受理该批单：{}", cancelDetail.getEndorNo());
                    smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.JUMP.getCode());
                    return;
                }
                GroupRuleAdaptor.addSubtractCheck4TkEmployer(endorsementRes.getResult());

                List<SmCreateOrderSubmitRequest> correctOrderList = orderServiceAdapter.buildReplaceMemberMessage(policyNo, endorsementRes, planVo);
                super.batchSaveOrderInfoGroup(correctOrderList);

            } else if (cancelDetail.getEndorType() != null && Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_REPLACE_ONLINE)) {
                log.info("泰康产品-线上人员替换：{}", cancelDetail);
                ZaGroupEndorsementRes endorsementRes = null;
                if(Objects.equals(version,2)){
                    //如果是补偿流程，采用新版本的数据组装方案，该方案不成熟，不敢轻易使用，只有开发人员确认数据后可切换成改方案
                    endorsementRes = employerOnlineReplaceMemberV2(policyNo, smOrder, cancelDetail);
                }
                else  if(Objects.equals(version,3)){
                    //如果是补偿流程，采用新版本的数据组装方案，该方案不成熟，不敢轻易使用，只有开发人员确认数据后可切换成改方案
                    endorsementRes = employerOnlineReplaceMemberV3(policyNo, smOrder, cancelDetail);
                }else{
                    //默认主流程
                    endorsementRes = employerOnlineReplaceMember(policyNo, smOrder, cancelDetail);
                }
                GroupRuleAdaptor.addSubtractCheck4TkEmployer(endorsementRes.getResult());

                List<SmCreateOrderSubmitRequest> correctOrderList = orderServiceAdapter.buildReplaceMemberMessage(policyNo, endorsementRes, planVo);
                super.batchSaveOrderInfoGroup(correctOrderList);

            } else if (cancelDetail.getEndorType() != null &&
                    (Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_INCREASE)
                            || Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_DECREASE)
                            || Objects.equals(cancelDetail.getEndorType(), TkConsts.V2_ENDOR_TYPE_CANCEL))) {

                GroupRuleAdaptor.addSubtractCheck(cancelDetail);

                SmPlanVO planVO = productService.getPlanById(smOrder.getPlanId());
                SmOrderApplicant applicant = orderMapper.selectOrderApplicantByOrderId(policyInsured.getFhOrderId());
                List<SmCreateOrderSubmitRequest> submitRequestList = adapter.cvtByV2EmployerEndorsementNotify(policyNo, cancelDetail, smOrder, applicant, planVO);
                batchSaveOrderInfoGroup(submitRequestList);
            }
            smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DONE.getCode());

        } catch (Exception e) {
            smOrderGroupNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.ERROR.getCode());
            log.warn("泰康团险新批改接口--交易流水号为{}中中保单号/批单号为{}/{}的批改信息处理存在异常", notify.getRequestId(), policyNo, cancelDetail.getEndorNo(), e);
            monitorService.addMonitor(EnumMonitor.TK_EMPLOYER_CORRECT, "泰康雇主责任险批改失败:" + policyNo);
            throw e;
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }
    }

    /**
     * 雇主责任险-在线替换人员
     * V2版本-针对保司修复的一批数据，他们的数据没办法还原成快照版本，所以此处兼容替换人员的保费为0的情况
     *
     * @param policyNo
     * @param order
     * @param employerCorrectMsg
     * @return
     */
    public ZaGroupEndorsementRes employerOnlineReplaceMember(String policyNo,
                                                             SmBaseOrderVO order,
                                                             TkEmpV2CancelDetail employerCorrectMsg) {
        List<TkEmpV2Employee> employeeList = employerCorrectMsg.getEmployeeList();


        Map<String,TkEmpV2Employee> employeeMap = new HashMap<>();
        List<TkEmpV2Employee> replacedList = new ArrayList<>();

        //1. 数据分类
        for(TkEmpV2Employee entry:employeeList){
            String key = entry.getGroup()+"-" +entry.getItemEndorInd();

            employeeMap.put(key,entry);
            if(Objects.equals(entry.getItemEndorInd(),TkConsts.ITEM_ENDOR_IND_D)){
                replacedList.add(entry);
            }
        }

        //2. 查询快照数据
        List<String> branchIdList = replacedList.stream().map(TkEmpV2Employee::getItemCode).collect(Collectors.toList());
        Long correctTimestamp = employerCorrectMsg.getEndorDate();
        Map<String, FastOrderInsuredPo> insuredMap = tkOrderQueryService.queryInsured4Correct(policyNo,correctTimestamp,branchIdList);

        ZaGroupEndorsementRes endorsementBody = new ZaGroupEndorsementRes();
        endorsementBody.setSubChannel(order.getSubChannel());
        endorsementBody.setCode("0");
        endorsementBody.setResult(null);

        ZaGroupEndorsementInfo correctMessage = new ZaGroupEndorsementInfo();
        correctMessage.setPolicyNo(policyNo);
        correctMessage.setEndorsementNo(employerCorrectMsg.getEndorNo());
        correctMessage.setEndorsementType(EnumGroupCorrectType.CHANGE_MEMBER_MESSAGE.getCode());
        correctMessage.setCorrectTimestamp(employerCorrectMsg.getEndorDate());

        Long validateTimeLong = employerCorrectMsg.getValidDate();
        if (validateTimeLong != null) {
            Date validateTime = new Date(validateTimeLong);
            String validateTimeStr = DateUtils.format(validateTime, DateUtils.FORMAT_TIME);
            correctMessage.setValidateTime(validateTimeStr);
        }
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
        String now = LocalDateUtil.commonFormat();
        //遍历被替换人列表
        for (TkEmpV2Employee oldEmp : replacedList) {
            String branchId = oldEmp.getItemCode();
            String groupId = oldEmp.getGroup();
            String key = groupId + "-" + TkConsts.ITEM_ENDOR_IND_A;

            FastOrderInsuredPo orderInsuredPo = insuredMap.get(branchId);

            if (orderInsuredPo == null) {
                log.warn("被替换人的分单信息不存在，请开发人员确认数据正确性.{}", oldEmp);
                throw new MSBizNormalException("-1", "数据处理失败，分单号不存在：" + branchId);
            }

            if (!Objects.equals(oldEmp.getIdentifyNumber(), orderInsuredPo.getIdNumber())) {
                log.warn("被替换人的身份证信息与保司传入的身份证信息不匹配，请开发人员确认数据正确性：{},数据库{}/保司{}", branchId, orderInsuredPo.getIdNumber(), oldEmp.getIdentifyNumber());
                throw new MSBizNormalException("-1", "被替换人的身份证信息与保司传入的身份证信息不匹配，数据库" + orderInsuredPo.getIdNumber() + "/保司" + oldEmp.getIdentifyNumber());
            }

            TkEmpV2Employee newEmp = employeeMap.get(key);
            if(newEmp == null){
                log.warn("替换后的人员信息不存在，请开发人员确认数据正确性：{},{}", branchId,employerCorrectMsg.getEndorNo());
                throw new MSBizNormalException("-1", "替换人员信息不存在" + branchId);
            }
            List<ZaGroupInsuredInfo> correctInsuredList = convertOnlineReplaceInsured(now, oldEmp, newEmp, orderInsuredPo);
            insuredList.addAll(correctInsuredList);
        }

        correctMessage.setInsuredList(insuredList);
        endorsementBody.setResult(correctMessage);
        return endorsementBody;
    }
    private List<ZaGroupInsuredInfo> convertOnlineReplaceInsured(String surrenderTime, TkEmpV2Employee oldEmp, TkEmpV2Employee newEmp, FastOrderInsuredPo orderInsuredPo) {
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
        //1.修正保费，保司的数据可能已经错乱了，替换的新成员的保费为0,此处做一个修正，取大于0的保费即可，替换人员的保费肯定是前后一致...
        String oldManPremiumStr = oldEmp.getPremium();
        BigDecimal oldManPremium = new BigDecimal(oldManPremiumStr);
        oldManPremium = oldManPremium.abs();

        String newManPremiumStr = newEmp.getPremium();
        BigDecimal newManPremium = new BigDecimal(newManPremiumStr);
        newManPremium = newManPremium.abs();

        if (newManPremium.compareTo(BigDecimal.ZERO) == 0) {
            newManPremium = oldManPremium;
        }

        if (oldManPremium.compareTo(newManPremium) != 0) {
            log.error("被替换人的保费与替换人员的保费不一致，请开发人员确认数据正确性：{},被替换人的保费({})/替换人的保费({})", oldEmp.getItemCode(), oldManPremium, newManPremium);
            throw new MSBizNormalException("-1", "被替换人的保费与替换人员的保费不一致," + oldEmp.getItemCode() + ",被替换人的保费(" + oldManPremium + ")/替换人的保费(" + newManPremium + ")");
        }

        ZaGroupInsuredInfo oldMan = new ZaGroupInsuredInfo();
        oldMan.setOrderId(orderInsuredPo.getFhOrderId());
        oldMan.setBranchId(oldEmp.getItemCode());
        oldMan.setActiveBranchId(oldEmp.getGroup());
        oldMan.setCertNo(orderInsuredPo.getIdNumber());
        oldMan.setBirthday(orderInsuredPo.getBirthday());
        oldMan.setName(orderInsuredPo.getPersonName());
        oldMan.setTotalPremium(oldManPremium);
        oldMan.setOccupationGroup(orderInsuredPo.getOccupationGroup());
        oldMan.setOccupationCode(orderInsuredPo.getOccupationCode());
        oldMan.setCertType(orderInsuredPo.getIdType());
        oldMan.setIsSecurity(orderInsuredPo.getIsSecurity());
        oldMan.setMobile(orderInsuredPo.getCellPhone());
        oldMan.setGender(orderInsuredPo.getPersonGender());

        oldMan.setTerminateTime(surrenderTime);
        oldMan.setChongHoRelationship(orderInsuredPo.getRelationship());
        oldMan.setEmail(orderInsuredPo.getEmail());
        oldMan.setEpolicyUrl(orderInsuredPo.getDownloadURL());
        oldMan.setPlanCode(orderInsuredPo.getPlanCode());
        oldMan.setPlanName(orderInsuredPo.getPlanName());
        oldMan.setHandleType("4");
        oldMan.setInsuredTime(orderInsuredPo.getInsuredTime());
        oldMan.setIndividualStatus(ZaGroupPolicyStatusEnum.TERMINATED.getCode());

        ZaGroupInsuredInfo newMan = new ZaGroupInsuredInfo();
        BeanUtils.copyProperties(oldMan, newMan);
        newMan.setTotalPremium(newManPremium);
        newMan.setBranchId(newEmp.getItemCode());
        newMan.setActiveBranchId(newEmp.getGroup());
        newMan.setTerminateTime(null);
        newMan.setIndividualStatus(ZaGroupPolicyStatusEnum.INFORCE.getCode());
        newMan.setName(newEmp.getEmployeeName());
        newMan.setCertType(newEmp.getIdentifyType());
        newMan.setCertNo(newEmp.getIdentifyNumber());
        newMan.setBirthday(CommonTools.getBirthday(newEmp.getIdentifyNumber()));
        newMan.setOccupationCode(newEmp.getProfessionCode());
        newMan.setOccupationGroup(newEmp.getOccupationLevel());
        newMan.setGender(newEmp.getSex());

        insuredList.add(oldMan);
        insuredList.add(newMan);
        return insuredList;
    }

    private List<ZaGroupInsuredInfo> convertOnlineReplaceInsuredV2(String surrenderTime, TkEmpV2Employee oldEmp, TkEmpV2Employee newEmp, FastOrderInsuredPo orderInsuredPo) {
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
        //1.修正保费，保司的数据可能已经错乱了，替换的新成员的保费为0,此处做一个修正，取大于0的保费即可，替换人员的保费肯定是前后一致...
        String oldManPremiumStr = oldEmp.getPremium();
        BigDecimal oldManPremium = new BigDecimal(oldManPremiumStr);
        oldManPremium = oldManPremium.abs();

        BigDecimal newManPremium = oldManPremium;

        ZaGroupInsuredInfo oldMan = new ZaGroupInsuredInfo();
        oldMan.setOrderId(orderInsuredPo.getFhOrderId());
        oldMan.setBranchId(oldEmp.getItemCode());
        oldMan.setActiveBranchId(oldEmp.getGroup());
        oldMan.setCertNo(orderInsuredPo.getIdNumber());
        oldMan.setBirthday(orderInsuredPo.getBirthday());
        oldMan.setName(orderInsuredPo.getPersonName());
        oldMan.setTotalPremium(oldManPremium);
        oldMan.setOccupationGroup(orderInsuredPo.getOccupationGroup());
        oldMan.setOccupationCode(orderInsuredPo.getOccupationCode());
        oldMan.setCertType(orderInsuredPo.getIdType());
        oldMan.setIsSecurity(orderInsuredPo.getIsSecurity());
        oldMan.setMobile(orderInsuredPo.getCellPhone());
        oldMan.setGender(orderInsuredPo.getPersonGender());

        oldMan.setTerminateTime(surrenderTime);
        oldMan.setChongHoRelationship(orderInsuredPo.getRelationship());
        oldMan.setEmail(orderInsuredPo.getEmail());
        oldMan.setEpolicyUrl(orderInsuredPo.getDownloadURL());
        oldMan.setPlanCode(orderInsuredPo.getPlanCode());
        oldMan.setPlanName(orderInsuredPo.getPlanName());
        oldMan.setHandleType("4");
        oldMan.setInsuredTime(orderInsuredPo.getInsuredTime());
        oldMan.setIndividualStatus(ZaGroupPolicyStatusEnum.TERMINATED.getCode());

        ZaGroupInsuredInfo newMan = new ZaGroupInsuredInfo();
        BeanUtils.copyProperties(oldMan, newMan);
        newMan.setTotalPremium(newManPremium);
        newMan.setBranchId(newEmp.getItemCode());
        newMan.setActiveBranchId(newEmp.getGroup());
        newMan.setTerminateTime(null);
        newMan.setIndividualStatus(ZaGroupPolicyStatusEnum.INFORCE.getCode());
        newMan.setName(newEmp.getEmployeeName());
        newMan.setCertType(newEmp.getIdentifyType());
        newMan.setCertNo(newEmp.getIdentifyNumber());
        newMan.setBirthday(CommonTools.getBirthday(newEmp.getIdentifyNumber()));
        newMan.setOccupationCode(newEmp.getProfessionCode());
        newMan.setOccupationGroup(newEmp.getOccupationLevel());
        newMan.setGender(newEmp.getSex());

        insuredList.add(oldMan);
        insuredList.add(newMan);
        return insuredList;
    }


    /**
     * 雇主责任险-在线替换人员
     * V2版本-针对保司修复的一批数据，他们的数据没办法还原成快照版本，所以此处兼容替换人员的保费为0的情况
     * 改良版...
     * @param policyNo
     * @param order
     * @param employerCorrectMsg
     * @return
     */
    public ZaGroupEndorsementRes employerOnlineReplaceMemberV2(String policyNo,
                                                             SmBaseOrderVO order,
                                                             TkEmpV2CancelDetail employerCorrectMsg) {
        List<TkEmpV2Employee> employeeList = employerCorrectMsg.getEmployeeList();


        List<TkEmpV2Employee> oldEmployeeList = new ArrayList<>();
        List<TkEmpV2Employee> newEmployeeList = new ArrayList<>();

        //1. 数据分类
        for(TkEmpV2Employee entry:employeeList){
            if(Objects.equals(entry.getItemEndorInd(),TkConsts.ITEM_ENDOR_IND_D)){
                oldEmployeeList.add(entry);
            }
            else if(Objects.equals(entry.getItemEndorInd(),TkConsts.ITEM_ENDOR_IND_A)){
                newEmployeeList.add(entry);
            }
        }
        if(oldEmployeeList.size()!=newEmployeeList.size()){
            log.warn("替换前后的数量不一致.{},{}", oldEmployeeList.size(),newEmployeeList.size());
            throw new MSBizNormalException("-1", "替换前后的数量不一致：");
        }
        Collections.sort(oldEmployeeList,(a,b)->a.getPremium().compareTo(b.getPremium()));
        Collections.sort(newEmployeeList,(a,b)->b.getPremium().compareTo(a.getPremium()));

        //2. 查询快照数据
        List<String> branchIdList = oldEmployeeList.stream().map(TkEmpV2Employee::getItemCode).collect(Collectors.toList());
        Long correctTimestamp = employerCorrectMsg.getEndorDate();
        Map<String, FastOrderInsuredPo> insuredMap = tkOrderQueryService.queryInsured4Correct(policyNo,correctTimestamp,branchIdList);

        ZaGroupEndorsementRes endorsementBody = new ZaGroupEndorsementRes();
        endorsementBody.setSubChannel(order.getSubChannel());
        endorsementBody.setCode("0");
        endorsementBody.setResult(null);

        ZaGroupEndorsementInfo correctMessage = new ZaGroupEndorsementInfo();
        correctMessage.setPolicyNo(policyNo);
        correctMessage.setEndorsementNo(employerCorrectMsg.getEndorNo());
        correctMessage.setEndorsementType(EnumGroupCorrectType.CHANGE_MEMBER_MESSAGE.getCode());
        correctMessage.setCorrectTimestamp(employerCorrectMsg.getEndorDate());

        Long validateTimeLong = employerCorrectMsg.getValidDate();
        if (validateTimeLong != null) {
            Date validateTime = new Date(validateTimeLong);
            String validateTimeStr = DateUtils.format(validateTime, DateUtils.FORMAT_TIME);
            correctMessage.setValidateTime(validateTimeStr);
        }
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
        String now = LocalDateUtil.commonFormat();
        //2.遍历被替换人列表
        for (int i=0;i<oldEmployeeList.size();i++) {
            TkEmpV2Employee oldEmployee = oldEmployeeList.get(i);
            TkEmpV2Employee newEmployee = newEmployeeList.get(i);

            String branchId = oldEmployee.getItemCode();

            FastOrderInsuredPo orderInsuredPo = insuredMap.get(branchId);

            if (orderInsuredPo == null) {
                log.warn("被替换人的分单信息不存在，请开发人员确认数据正确性.{}", oldEmployee);
                throw new MSBizNormalException("-1", "数据处理失败，分单号不存在：" + branchId);
            }

            if (!Objects.equals(oldEmployee.getIdentifyNumber(), orderInsuredPo.getIdNumber())) {
                log.warn("被替换人的身份证信息与保司传入的身份证信息不匹配，请开发人员确认数据正确性：{},数据库{}/保司{}", branchId, orderInsuredPo.getIdNumber(), oldEmployee.getIdentifyNumber());
                throw new MSBizNormalException("-1", "被替换人的身份证信息与保司传入的身份证信息不匹配，数据库" + orderInsuredPo.getIdNumber() + "/保司" + oldEmployee.getIdentifyNumber());
            }

            List<ZaGroupInsuredInfo> correctInsuredList = convertOnlineReplaceInsuredV2(now, oldEmployee, newEmployee, orderInsuredPo);
            insuredList.addAll(correctInsuredList);
        }

        correctMessage.setInsuredList(insuredList);
        endorsementBody.setResult(correctMessage);
        return endorsementBody;
    }

    /**
     * 雇主责任险-在线替换人员
     * V2版本-针对保司修复的一批数据，他们的数据没办法还原成快照版本，所以此处兼容替换人员的保费为0的情况
     * 改良改良版...
     * @param policyNo
     * @param order
     * @param employerCorrectMsg
     * @return
     */
    public ZaGroupEndorsementRes employerOnlineReplaceMemberV3(String policyNo,
                                                               SmBaseOrderVO order,
                                                               TkEmpV2CancelDetail employerCorrectMsg) {
        List<TkEmpV2Employee> employeeList = employerCorrectMsg.getEmployeeList();


        List<TkEmpV2Employee> oldEmployeeList = new ArrayList<>();
        List<TkEmpV2Employee> newEmployeeList = new ArrayList<>();

        //1. 数据分类
        for(TkEmpV2Employee entry:employeeList){
            BigDecimal premium = new BigDecimal(entry.getPremium());
            if(premium.compareTo(BigDecimal.ZERO)<0){
                oldEmployeeList.add(entry);
            } else {
                newEmployeeList.add(entry);
            }
        }

        if(oldEmployeeList.size()!=newEmployeeList.size()){
            log.warn("替换前后的数量不一致.{},{}", oldEmployeeList.size(),newEmployeeList.size());
            throw new MSBizNormalException("-1", "替换前后的数量不一致：");
        }
        Collections.sort(oldEmployeeList,(a,b)->a.getPremium().compareTo(b.getPremium()));
        Collections.sort(newEmployeeList,(a,b)->b.getPremium().compareTo(a.getPremium()));

        //2. 查询快照数据
        List<String> branchIdList = oldEmployeeList.stream().map(TkEmpV2Employee::getItemCode).collect(Collectors.toList());
        Long correctTimestamp = employerCorrectMsg.getEndorDate();
        Map<String, FastOrderInsuredPo> insuredMap = tkOrderQueryService.queryInsured4Correct(policyNo,correctTimestamp,branchIdList);

        ZaGroupEndorsementRes endorsementBody = new ZaGroupEndorsementRes();
        endorsementBody.setSubChannel(order.getSubChannel());
        endorsementBody.setCode("0");
        endorsementBody.setResult(null);

        ZaGroupEndorsementInfo correctMessage = new ZaGroupEndorsementInfo();
        correctMessage.setPolicyNo(policyNo);
        correctMessage.setEndorsementNo(employerCorrectMsg.getEndorNo());
        correctMessage.setEndorsementType(EnumGroupCorrectType.CHANGE_MEMBER_MESSAGE.getCode());
        correctMessage.setCorrectTimestamp(employerCorrectMsg.getEndorDate());

        Long validateTimeLong = employerCorrectMsg.getValidDate();
        if (validateTimeLong != null) {
            Date validateTime = new Date(validateTimeLong);
            String validateTimeStr = DateUtils.format(validateTime, DateUtils.FORMAT_TIME);
            correctMessage.setValidateTime(validateTimeStr);
        }
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
        String now = LocalDateUtil.commonFormat();
        //2.遍历被替换人列表
        for (int i=0;i<oldEmployeeList.size();i++) {
            TkEmpV2Employee oldEmployee = oldEmployeeList.get(i);
            TkEmpV2Employee newEmployee = newEmployeeList.get(i);

            String branchId = oldEmployee.getItemCode();

            FastOrderInsuredPo orderInsuredPo = insuredMap.get(branchId);

            if (orderInsuredPo == null) {
                log.warn("被替换人的分单信息不存在，请开发人员确认数据正确性.{}", oldEmployee);
                throw new MSBizNormalException("-1", "数据处理失败，分单号不存在：" + branchId);
            }

            if (!Objects.equals(oldEmployee.getIdentifyNumber(), orderInsuredPo.getIdNumber())) {
                log.warn("被替换人的身份证信息与保司传入的身份证信息不匹配，请开发人员确认数据正确性：{},数据库{}/保司{}", branchId, orderInsuredPo.getIdNumber(), oldEmployee.getIdentifyNumber());
                throw new MSBizNormalException("-1", "被替换人的身份证信息与保司传入的身份证信息不匹配，数据库" + orderInsuredPo.getIdNumber() + "/保司" + oldEmployee.getIdentifyNumber());
            }

            List<ZaGroupInsuredInfo> correctInsuredList = convertOnlineReplaceInsuredV2(now, oldEmployee, newEmployee, orderInsuredPo);
            insuredList.addAll(correctInsuredList);
        }

        correctMessage.setInsuredList(insuredList);
        endorsementBody.setResult(correctMessage);
        return endorsementBody;
    }






//    private List<ZaGroupInsuredInfo> convertOnlineReplaceInsured(String surrenderTime, TkEmpV2Employee oldEmp, TkEmpV2Employee newEmp, FastOrderInsuredPo orderInsuredPo) {
//        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
//        //保司传的被替换人的保费是负数，需要变成整数
//        BigDecimal oldPremium = new BigDecimal(oldEmp.getPremium()).multiply(new BigDecimal("-1")).setScale(2, BigDecimal.ROUND_HALF_UP);
//        BigDecimal newPremium = new BigDecimal(newEmp.getPremium());
//        if (oldPremium.compareTo(newPremium) != 0) {
//            log.error("被替换人的保费与替换人员的保费不一致，请开发人员确认数据正确性：{},被替换人的保费({})/替换人的保费({})", oldEmp.getItemCode(), oldPremium, newPremium);
//            throw new MSBizNormalException("-1", "被替换人的保费与替换人员的保费不一致," + oldEmp.getItemCode() + ",被替换人的保费(" + oldPremium + ")/替换人的保费(" + newPremium + ")");
//        }
//
//        ZaGroupInsuredInfo oldMan = new ZaGroupInsuredInfo();
//        oldMan.setOrderId(orderInsuredPo.getFhOrderId());
//        oldMan.setBranchId(oldEmp.getItemCode());
//        oldMan.setActiveBranchId(oldEmp.getGroup());
//        oldMan.setCertNo(orderInsuredPo.getIdNumber());
//        oldMan.setBirthday(orderInsuredPo.getBirthday());
//        oldMan.setName(orderInsuredPo.getPersonName());
//        oldMan.setTotalPremium(oldPremium);
//        oldMan.setOccupationGroup(orderInsuredPo.getOccupationGroup());
//        oldMan.setOccupationCode(orderInsuredPo.getOccupationCode());
//        oldMan.setCertType(orderInsuredPo.getIdType());
//        oldMan.setIsSecurity(orderInsuredPo.getIsSecurity());
//        oldMan.setMobile(orderInsuredPo.getCellPhone());
//        oldMan.setGender(orderInsuredPo.getPersonGender());
//
//        oldMan.setTerminateTime(surrenderTime);
//        oldMan.setChongHoRelationship(orderInsuredPo.getRelationship());
//        oldMan.setEmail(orderInsuredPo.getEmail());
//        oldMan.setEpolicyUrl(orderInsuredPo.getDownloadURL());
//        oldMan.setPlanCode(orderInsuredPo.getPlanCode());
//        oldMan.setPlanName(orderInsuredPo.getPlanName());
//        oldMan.setHandleType("4");
//        oldMan.setInsuredTime(orderInsuredPo.getInsuredTime());
//        oldMan.setIndividualStatus(ZaGroupPolicyStatusEnum.TERMINATED.getCode());
//
//        ZaGroupInsuredInfo newMan = new ZaGroupInsuredInfo();
//        BeanUtils.copyProperties(oldMan, newMan);
//        newMan.setTotalPremium(new BigDecimal(newEmp.getPremium()));
//        newMan.setBranchId(newEmp.getItemCode());
//        newMan.setActiveBranchId(newEmp.getGroup());
//        newMan.setTerminateTime(null);
//        newMan.setIndividualStatus(ZaGroupPolicyStatusEnum.INFORCE.getCode());
//        newMan.setName(newEmp.getEmployeeName());
//        newMan.setCertType(newEmp.getIdentifyType());
//        newMan.setCertNo(newEmp.getIdentifyNumber());
//        newMan.setBirthday(CommonTools.getBirthday(newEmp.getIdentifyNumber()));
//        newMan.setOccupationCode(newEmp.getProfessionCode());
//        newMan.setOccupationGroup(newEmp.getOccupationLevel());
//        newMan.setGender(newEmp.getSex());
//
//        insuredList.add(oldMan);
//        insuredList.add(newMan);
//        return insuredList;
//    }


    /**
     * 雇主责任险-替换人员流程
     *
     * @param policyNo
     * @param order
     * @param employerCorrectMsg
     */
    public ZaGroupEndorsementRes employerReplaceMember(String policyNo,
                                                       SmBaseOrderVO order,
                                                       TkEmpV2CancelDetail employerCorrectMsg) {

        List<String> branchIdList = employerCorrectMsg.getEmployeeList().stream().map(TkEmpV2Employee::getItemCode).collect(Collectors.toList());
        Long correctTimestamp = employerCorrectMsg.getEndorDate();
        Map<String,FastOrderInsuredPo> insuredMap = tkOrderQueryService.queryInsured4Correct(policyNo,correctTimestamp,branchIdList);

        ZaGroupEndorsementRes endorsementBody = new ZaGroupEndorsementRes();
        endorsementBody.setSubChannel(order.getSubChannel());
        endorsementBody.setCode("0");
        endorsementBody.setResult(null);

        ZaGroupEndorsementInfo correctMessage = new ZaGroupEndorsementInfo();
        correctMessage.setPolicyNo(policyNo);
        correctMessage.setEndorsementNo(employerCorrectMsg.getEndorNo());
        correctMessage.setEndorsementType(EnumGroupCorrectType.CHANGE_MEMBER_MESSAGE.getCode());
        correctMessage.setCorrectTimestamp(employerCorrectMsg.getEndorDate());

        Long validateTimeLong = employerCorrectMsg.getValidDate();
        if (validateTimeLong != null) {
            Date validateTime = new Date(validateTimeLong);
            String validateTimeStr = DateUtils.format(validateTime, DateUtils.FORMAT_TIME);
            correctMessage.setValidateTime(validateTimeStr);
        }
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();
        List<TkEmpV2Employee> employeeList = employerCorrectMsg.getEmployeeList();
        String now = LocalDateUtil.commonFormat();
        for (TkEmpV2Employee employee : employeeList) {
            String branchId = employee.getItemCode();
            FastOrderInsuredPo orderInsuredPo = insuredMap.get(branchId);

            if (orderInsuredPo == null) {
                log.warn("被替换人的分单信息不存在，请开发人员确认数据正确性.{}", employee);
                throw new MSBizNormalException("-1", "数据处理失败，分单号不存在：" + branchId);
            }

            if (Objects.equals(employee.getIdentifyNumber(), orderInsuredPo.getIdNumber())) {
                log.warn("被保人的身份证信息未发生变更，系统不支持该类型的批改：{},{}", branchId, orderInsuredPo.getIdNumber());
                continue;
            }
            List<ZaGroupInsuredInfo> correctInsuredList = convertInsured(now, employee, orderInsuredPo);
            insuredList.addAll(correctInsuredList);
        }

        correctMessage.setInsuredList(insuredList);
        endorsementBody.setResult(correctMessage);
        return endorsementBody;
    }

    /**
     * 通用被保人
     *
     * @param surrenderTime
     * @param employee
     * @param orderInsuredPo
     * @return
     */
    private List<ZaGroupInsuredInfo> convertInsured(String surrenderTime, TkEmpV2Employee employee, FastOrderInsuredPo orderInsuredPo) {
        List<ZaGroupInsuredInfo> insuredList = new ArrayList<>();

        ZaGroupInsuredInfo oldMan = new ZaGroupInsuredInfo();
        oldMan.setOrderId(orderInsuredPo.getFhOrderId());
        oldMan.setBranchId(orderInsuredPo.getBranchId());
        oldMan.setActiveBranchId(orderInsuredPo.getActiveBranchId());
        oldMan.setCertNo(orderInsuredPo.getIdNumber());
        oldMan.setBirthday(orderInsuredPo.getBirthday());
        oldMan.setName(orderInsuredPo.getPersonName());
        oldMan.setTotalPremium(orderInsuredPo.getPremium());
        oldMan.setOccupationGroup(orderInsuredPo.getOccupationGroup());
        oldMan.setOccupationCode(orderInsuredPo.getOccupationCode());
        oldMan.setCertType(orderInsuredPo.getIdType());
        oldMan.setIsSecurity(orderInsuredPo.getIsSecurity());
        oldMan.setMobile(orderInsuredPo.getCellPhone());
        oldMan.setGender(orderInsuredPo.getPersonGender());

        oldMan.setTerminateTime(surrenderTime);
        oldMan.setChongHoRelationship(orderInsuredPo.getRelationship());
        oldMan.setEmail(orderInsuredPo.getEmail());
        oldMan.setEpolicyUrl(orderInsuredPo.getDownloadURL());
        oldMan.setPlanCode(orderInsuredPo.getPlanCode());
        oldMan.setPlanName(orderInsuredPo.getPlanName());
        oldMan.setHandleType("4");
        oldMan.setInsuredTime(orderInsuredPo.getInsuredTime());
        oldMan.setIndividualStatus(ZaGroupPolicyStatusEnum.TERMINATED.getCode());

        ZaGroupInsuredInfo newMan = new ZaGroupInsuredInfo();
        BeanUtils.copyProperties(oldMan, newMan);
        newMan.setTerminateTime(null);
        newMan.setIndividualStatus(ZaGroupPolicyStatusEnum.INFORCE.getCode());
        newMan.setName(employee.getEmployeeName());
        newMan.setCertType(employee.getIdentifyType());
        newMan.setCertNo(employee.getIdentifyNumber());
        newMan.setBirthday(CommonTools.getBirthday(employee.getIdentifyNumber()));
        newMan.setOccupationCode(employee.getProfessionCode());
        newMan.setOccupationGroup(employee.getOccupationLevel());
        newMan.setGender(employee.getSex());

        insuredList.add(oldMan);
        insuredList.add(newMan);
        return insuredList;
    }


    private TkEmpV2Resp failV2Resp(TkEmpV2Req req, String msg) {
        TkEmpV2Resp respBox = new TkEmpV2Resp();
        respBox.setHead(req.getHead());
        TkEmpV2RespBody body = new TkEmpV2RespBody();
        body.setCode(TkEmpV2RespBody.CODE_FAIL);
        body.setMsg(msg);
        respBox.setBody(body);
        respBox.setSign("");
        //return TkEmpV2Util.createSign(respBox,tkApiProperties.getEmployerV2Md5Key());
        return respBox;
    }

    private TkEmpV2Resp successV2Resp(TkEmpV2Req req) {
        TkEmpV2Resp respBox = new TkEmpV2Resp();
        respBox.setHead(req.getHead());
        TkEmpV2RespBody body = new TkEmpV2RespBody();
        body.setCode(TkEmpV2RespBody.CODE_SUCCESS);
        body.setMsg("success!");
        respBox.setBody(body);
        respBox.setSign("");
        //return TkEmpV2Util.createSign(respBox,tkApiProperties.getEmployerV2Md5Key());
        return respBox;
    }


    /***** add by zhangjian 2022-06-28 end****/

    /**
     * 泰康百万医疗H5续保产品回调处理
     *
     * @param request
     * @param response
     * @throws IOException
     */
    @Override
    public void handRenewalCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setContentType("application/json;charset=utf-8");
        String msg = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8.toString());
        log.info("泰康百万医疗H5续保产品回调处理msg= [{}]", msg);
        String contentStr = null;
        try {
            contentStr = adapter.h5CallBackDecrypt(msg, tkApiProperties.getMedicalRenewalAesKey());
        } catch (Exception e) {
            log.warn("h5CallBackDecrypt error", e);
            response.getWriter().write(getH5MedicalCallBackFailResponse());
            return;
        }
        log.info("泰康百万医疗H5续保产品回调处理解密后数据contentStr= {}", contentStr);
        if (StringUtils.isBlank(contentStr)) {
            log.warn("泰康续保保单回调报文为空");
            response.getWriter().write(getH5MedicalCallBackFailResponse());
            return;
        }
        JSONObject req = JSONObject.parseObject(contentStr);
        String body = req.getString("requestData");
        log.info("taikang===========body= {}", body);
        TkMedicalPolicyCallBackReq policyInfo = jsonMapper.readValue(body, TkMedicalPolicyCallBackReq.class);
        String lockKey = "tk_medical_renewal_order:callback:" + policyInfo.getPolicyNo();

        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
            }


            ChOrderPersonalNotify notify = chOrderPersonalNotifyService.getRenewalNotifyByPolicyNo(policyInfo.getPolicyNo(), "");
            if (Objects.nonNull(notify)) {
                log.warn("泰康百万医疗续保保单回调报文接收成功,报文处理状态为{}", notify.getStatus());
                throw new MSBizNormalException("", "泰康百万医疗续保保单回调报文接收成功,报文处理状态为" + notify.getStatus());
            }
            //log.info(jsonMapper.writeValueAsString(bindInfo));
            //保存回调信息
            notify = this.createRenewalNotify(policyInfo.getPolicyNo(), GroupNotify.TypeEnum.RENEWAL.getCode());
            super.chOrderPersonalNotifyService.insertOrderPersonalNotifyContent(notify, msg);
            //处理续保保单信息

            doRenewPolicyInfo(policyInfo, notify);
            //返回泰康成功响应
            response.getWriter().write(getH5MedicalCallBackSuccessResponse());

        } catch (Exception e) {
            log.warn("泰康百万医疗续保保单回调处理失败", e);
            response.getWriter().write(getH5MedicalCallBackFailResponse());
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }
    }

    private String getH5MedicalCallBackSuccessResponse() {
        return getH5MedicalCallBackResponse("1");
    }

    private String getH5MedicalCallBackFailResponse() {
        return getH5MedicalCallBackResponse("500");
    }

    private String getH5MedicalCallBackResponse(String code) {
        return new JSONObject().fluentPut("code", code).fluentPut("data", "").fluentPut("message", "").fluentPut("responseTime", "").toString();
    }

    private void doRenewPolicyInfo(TkMedicalPolicyCallBackReq policyInfo, ChOrderPersonalNotify notify) {
        //保单号已存在，则直接变更已处理
        if (orderCoreService.isExistPolicyNo(policyInfo.getPolicyNo())) {
            //变更通知信息已处理
            chOrderPersonalNotifyService.updateOrderGroupStatus(notify.getId(), null, GroupNotify.StatusEnum.DOING.getCode(), GroupNotify.StatusEnum.DONE.getCode());
            return;
        }
        TkMedicalPolicyCallBackParameter tkMedicalPolicyCallBackParameter = policyInfo.getParameterMap();
        URLParser urlParser = null;
        try {
            urlParser = URLParser.fromURL(tkMedicalPolicyCallBackParameter.getProposalUrl()).compile();
        } catch (UnsupportedEncodingException e) {
            log.error("解析泰康自定义参数异常", e);
            throw new MSBizNormalException(ExcptEnum.TK_MEDICAL_H5_ERROR.getCode(), "解析泰康自定义参数异常");
        }
        //保存续保保单信息
        SmPlanVO planVO = productService.getPlanByFhProductId(tkMedicalPolicyCallBackParameter.getRubikCode());
        //从H5请求URL中提取原保单号
        String originalPolicyNo = urlParser.getParameter(TkConsts.TK_MEDICAL_H5_MY_PONO);
        if (StringUtils.isBlank(originalPolicyNo)) {
            log.error("解析泰康自定义参数异常:没有原保单号");
            throw new MSBizNormalException(ExcptEnum.TK_MEDICAL_H5_ERROR.getCode(), "解析泰康自定义参数异常:没有原保单号");
        }
        //根据保单号获取已支付的原保单记录
        SmBaseOrderVO originalBaseOrderVO = adapter.getSmBaseOrderVO(originalPolicyNo);
        if (originalBaseOrderVO == null) {
            log.error("泰康百万医疗H5续保回调:未查到原保单订单记录 originalPolicyNo= {}", originalPolicyNo);
            throw new MSBizNormalException(ExcptEnum.TK_MEDICAL_H5_ERROR.getCode(), "泰康百万医疗H5续保回调:未查到原保单订单记录");
        }
        SmCreateOrderSubmitRequest submitRequest = adapter.cvtRenewalOrder(policyInfo, originalBaseOrderVO, planVO);
        if (checkIsNewPolicy(policyInfo, originalBaseOrderVO)) {
            //保单号,证件号码,证件类型与新保单的信息匹配失败则算新保单
            orderCoreService.saveOrderInfoNoRenewal(submitRequest, null, planVO);
        } else {
            //保单号,证件号码,证件类型与新保单的信息匹配成功则算续保保单
            orderCoreService.saveRenewalOrderInfo(submitRequest, null, planVO);
        }
        orderCoreService.pushSupplementSuccess(submitRequest.getFhOrderId());
        //变更通知信息已处理
        chOrderPersonalNotifyService.updateOrderGroupStatus(notify.getId(), null, GroupNotify.StatusEnum.DOING.getCode(), GroupNotify.StatusEnum.DONE.getCode());

    }

    private ChOrderPersonalNotify createRenewalNotify(String policyNo, Integer type) {
        ChOrderPersonalNotify notify = new ChOrderPersonalNotify();
        notify.setPolicyNo(policyNo);
        notify.setEndorsementNo("");
        notify.setChannel(EnumChannel.TK_PAY.getCode());
        notify.setStatus(GroupNotify.StatusEnum.DOING.getCode());
        //notify.setType(GroupNotify.TypeEnum.RENEWAL.getCode());
        notify.setType(type);
        return notify;
    }


    /**
     * 根据保单号和被保人身份证号码,证件类型判断是一致判断是否是新单
     *
     * @param policyInfo
     * @param originalBaseOrderVO
     * @return
     */
    private boolean checkIsNewPolicy(TkMedicalPolicyCallBackReq policyInfo, SmBaseOrderVO originalBaseOrderVO) {
        List<SmOrderInsured> smOrderInsureds = smOrderInsuredMapper.selectByPolicyNoAndIdNumber(originalBaseOrderVO.getPolicyNo(), policyInfo.getIssueCredentialNo());
        if (CollectionUtils.isEmpty(smOrderInsureds)) {
            return true;
        } else {
            if (smOrderInsureds.size() > 1) {
                log.warn("获取泰康续同一个身份证和保单号对应多条记录 originalPolicyNo= [{}], idNumber= [{}]", originalBaseOrderVO.getPolicyNo(), policyInfo.getIssueCredentialNo());
            }
            SmOrderInsured smOrderInsured = smOrderInsureds.get(0);
            if (!smOrderInsured.getPersonName().equals(policyInfo.getIssueName())) {
                //新保单被保人姓名与原保单姓名不一致则视为新保单
                return true;
            }
            if (SmConstants.POLICY_STATUS_SUCCESS.equals(smOrderInsured.getAppStatus())) {
                return checkNewPolicyIsInRenewConfig(policyInfo, originalBaseOrderVO);
            } else if (SmConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(smOrderInsured.getAppStatus())) {
                //如果新单是退保状态,那么校验新保单的起保时间与旧保单的结束时间是否重叠或者衔接上,
                return checkPolicyTimeIsOverlay(policyInfo, originalBaseOrderVO);
            } else {
                log.warn("smOrderInsured appStatus为非1和4,按照新保单来处理 smOrderInsured= {}", JSON.toJSON(smOrderInsured));
                return true;
            }
        }
    }

    /**
     * 如果新单是退保状态,那么校验新保单的起保时间与旧保单的结束时间是否重叠或者衔接上,
     *
     * @param policyInfo
     * @param originalBaseOrderVO
     * @return
     */
    private boolean checkPolicyTimeIsOverlay(TkMedicalPolicyCallBackReq policyInfo, SmBaseOrderVO originalBaseOrderVO) {
        Date startDate = LocalDateUtil.stringToUdate(policyInfo.getEffectiveTime());
        //新报起保时间必须小于等于原保单实效+1天,即原保单保障时间要么连续要么重合
        //如新续保单生效时间为2021-04-29 00:00:00 原保退保时间
        // 2021-04-29 12:00:00 或者大于这个时间的 算续保
        // 2021-04-28 12:00:00 算续保
        // 2021-04-27 12:00:00 算新保
        startDate = LocalDateUtil.clearDateTime(startDate);
        SmCancelRefund smCancelRefund = refundMapper.selectByOrderIdAndIdNumber(originalBaseOrderVO.getFhOrderId(), policyInfo.getIssueCredentialNo());
        if (smCancelRefund == null) {
            log.error("该续保用户查不到退保记录 policyInfo= {},originalBaseOrderVO= {} ", JSON.toJSON(policyInfo), JSON.toJSON(originalBaseOrderVO));
            return true;
        }
//        Date retDate = DateUtil.parseDate("2021-04-27 23:29:22");
        Date retDate = DateUtil.parseDate(smCancelRefund.getRetTime());
        retDate = LocalDateUtil.addDay(retDate, 1);
        retDate = LocalDateUtil.clearDateTime(retDate);
        return (retDate.compareTo(startDate) < 0);
    }

    public boolean checkNewPolicyIsInRenewConfig(TkMedicalPolicyCallBackReq policyInfo, SmBaseOrderVO originalBaseOrderVO) {
        RenewalConfig renewalConfig = renewalConfigMapper.getActiveRenewalConfigByPlanId(originalBaseOrderVO.getPlanId());
        if (renewalConfig == null) {
            return true;
        }
        Date now = new Date();
        Date endDate = originalBaseOrderVO.getEndTime();
        long rangeDate = LocalDateUtil.daysBetween(endDate, now);
        int beforeDayConfig = 0;
        int afterDayConfig = 0;
        if (renewalConfig.getBeforeExpirationDay() != null) {
            beforeDayConfig = renewalConfig.getBeforeExpirationDay();
        }
        if (renewalConfig.getAfterExpirationDay() != null) {
            afterDayConfig = renewalConfig.getAfterExpirationDay();
        }
        //判断上一个保单的截止时间是否在续保配置范围内
        if (rangeDate >= beforeDayConfig && rangeDate <= afterDayConfig) {
            return false;
        } else {
            return true;
        }
    }

}
