package com.cfpamf.ms.insur.admin.service.correct;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.CentNoUtil;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.order.OrderConstants;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmAddCommissionDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionItemMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.enums.correct.CorrectProject;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmSaveOrderCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.correct.BatchCorrectResult;
import com.cfpamf.ms.insur.admin.pojo.dto.correct.ExcelCorrectOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.po.order.correct.CorrectOrderPo;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImport;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.SmOrderImportError;
import com.cfpamf.ms.insur.admin.pojo.query.CmpySettingQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderToCorrectVO;
import com.cfpamf.ms.insur.admin.service.SmOrderImportService;
import com.cfpamf.ms.insur.admin.validation.ValidationResult;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.DownloadUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.excel.ExcelReadUtils;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxFormFieldOptionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 订单批改功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderCorrectService {

    private final Pattern p = Pattern.compile("(\\S+)-(\\S+)");
    @Autowired
    private Validator validator;

    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private SmOrderItemMapper orderItemMapper;

    @Autowired
    private SmOrderCommissionDetailMapper orderCommissionDetailMapper;

    @Autowired
    private SmOrderCommissionItemMapper orderCommissionItemMapper;

    @Autowired
    private SmAddCommissionDetailMapper addCommissionDetailMapper;

    @Autowired
    private SmCmpySettingMapper settingMapper;

    @Autowired
    private AuthUserMapper userMapper;

    @Autowired
    private UserPostMapper userPostMapper;

    @Autowired
    SmOrderImportMapper orderImportMapper;

    @Autowired
    SmOrderImportErrorMapper orderImportErrorMapper;

    @Autowired
    private SmOrderImportService orderImportService;

    @Autowired
    private TxServiceManager txService;

    /**
     * 批量批改数据
     *
     * @param userId
     * @param file
     * @return
     */
    public BatchCorrectResult batchCorrect(String userId, String file) {

        BatchCorrectResult result = new BatchCorrectResult();
        result.setCreateBy(userId);
        result.setCreateTime(LocalDateTime.now());
        result.setFile(file);

        List<ExcelCorrectOrderDTO> data = null;
        try {
            data = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(file), ExcelCorrectOrderDTO.class
                            , 10, false);
        } catch (Exception e) {
            log.error("文件解析异常:{}", file, e);
            throw new MSBizNormalException("-1", "文件解析异常，请仔细核对数据或者检查导入文件模版");
        }
        if (CollectionUtils.isEmpty(data)) {
            throw new MSBizNormalException("-1", "文件解析数据为空，请仔细核对数据或者检查导入文件模版");
        }
        List<ValidationResult<ExcelCorrectOrderDTO>> validList = wrapVo(data);
        validList.stream().forEach(valid -> {
            ExcelCorrectOrderDTO order = valid.getSource();
            String code = order.getProjectCode();
            code = CorrectProject.convert2Code(code);
            order.setProjectCode(code);
        });

        batchCorrect(validList);
        List<ValidationResult<ExcelCorrectOrderDTO>> errorPart = validList.stream()
                .filter(entry -> {
                    return !entry.isSuccess();
                })
                .collect(Collectors.toList());
        int total = validList.size();

        int fail = errorPart.size();
        int success = total - fail;

        SmOrderImport res = orderImportService.addImportRecord(file, 1, success, fail, userId);
        String errorFileUrl = saveErrorPart(res.getId(), errorPart);
        result.setSuccess(success);
        result.setError(fail);
        result.setErrorFile(errorFileUrl);

        return result;
    }


    public String saveErrorPart(Integer id, List<ValidationResult<ExcelCorrectOrderDTO>> errDataList) {
        if (CollectionUtils.isEmpty(errDataList)) {
            return null;
        }
        List<ExcelCorrectOrderDTO> excelVoList = errDataList.stream()
                .map(valid -> {
                    ExcelCorrectOrderDTO order = valid.getImage();
                    order.setErrorMsg(valid.getMessage());
                    return order;
                })
                .collect(Collectors.toList());

        String errorUrl = null;
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream(8096);
            ExcelReadUtils.write(os, excelVoList);
            errorUrl = AliYunOssUtil.uploadByBytes(os.toByteArray(), AliYunOssUtil.genImportErrorOssPrefix()
                    + "/error" + id + ".xlsx");
        } catch (Exception e) {
            log.error("文件创建异常", e);
        }

        List<SmOrderImportError> errorLists = errDataList.stream()
                .map(entry -> {
                    SmOrderImportError error = new SmOrderImportError();
                    error.setImportId(id);
                    error.setErrorCol("");
                    error.setErrorRow(entry.getSource().getId());
                    error.setErrorMsg(entry.getMessage());
                    return error;
                }).collect(Collectors.toList());
        orderImportErrorMapper.insertList(errorLists);

        SmOrderImport update = new SmOrderImport();
        update.setId(id);
        update.setErrorUrl(errorUrl);
        orderImportMapper.updateByPrimaryKeySelective(update);
        return errorUrl;
    }

    private List<ValidationResult<ExcelCorrectOrderDTO>> wrapVo(List<ExcelCorrectOrderDTO> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.EMPTY_LIST;
        }
        return data.stream()
                .map(valid -> {
                    return ValidationResult.build(valid, ExcelCorrectOrderDTO.class);
                })
                .collect(Collectors.toList());
    }

    /**
     * 校验数据是否正常
     *
     * @param validList
     */
    private void batchCorrect(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        baseValid(validList);
        repeatabilityValid(validList);
        List<ValidationResult<ExcelCorrectOrderDTO>> successPart = validList.stream().filter(ValidationResult::isSuccess).collect(Collectors.toList());
        Map<String, List<ValidationResult<ExcelCorrectOrderDTO>>> validMap = LambdaUtils.groupBy(successPart, valid -> {
            return valid.getSource().getProjectCode();
        });
        for (Map.Entry<String, List<ValidationResult<ExcelCorrectOrderDTO>>> entry : validMap.entrySet()) {
            String projectCode = entry.getKey();
            List<ValidationResult<ExcelCorrectOrderDTO>> data = entry.getValue();
            if (Objects.equals(projectCode, CorrectProject.APP_NAME.getCode())) {
                batchCorrectByAppName(data);
                continue;
            }
            if (Objects.equals(projectCode, CorrectProject.APP_MOBILE.getCode())) {
                batchCorrectByAppMobile(data);
                continue;
            }
            if (Objects.equals(projectCode, CorrectProject.APP_ID_NUMBER.getCode())) {
                batchCorrectByAppIdNumber(data);
                continue;
            }
            if (Objects.equals(projectCode, CorrectProject.INS_NAME.getCode())) {
                batchCorrectByInsuredName(data);
                continue;
            }
            if (Objects.equals(projectCode, CorrectProject.INS_MOBILE.getCode())) {
                batchCorrectByInsuredMobile(data);
                continue;
            }
            if (Objects.equals(projectCode, CorrectProject.INS_ID_NUMBER.getCode())) {
                batchCorrectByInsuredIdCard(data);
                continue;
            }
            if (Objects.equals(projectCode, CorrectProject.RECOMMEND_NAME.getCode())) {
                batchCorrectByRecommendName(data);
                continue;
            }
            data.stream().forEach(valid -> {
                valid.addMessage("该批改类型暂不支持批量批改");
            });
        }
    }

    private void baseValid(List<ValidationResult<ExcelCorrectOrderDTO>> orderWrapperList) {
        orderWrapperList.stream().forEach(this::baseValid);
    }

    private void baseValid(ValidationResult<ExcelCorrectOrderDTO> orderWrapper) {
        ExcelCorrectOrderDTO order = orderWrapper.getSource();
        Set<ConstraintViolation<ExcelCorrectOrderDTO>> validate = validator.validate(order, Default.class);
        if (!CollectionUtils.isEmpty(validate)) {
            validate.stream()
                    .forEach(data -> {
                        orderWrapper.addMessage(data.getMessage());
                    });
        }
    }

    /**
     * 数据重复性校验
     *
     * @param validList
     */
    private void repeatabilityValid(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        Map<String, ValidationResult<ExcelCorrectOrderDTO>> validMap = new HashMap<>(16);
        for (ValidationResult<ExcelCorrectOrderDTO> valid : validList) {
            ExcelCorrectOrderDTO order = valid.getSource();
            String key = mutexKey(order);
            ValidationResult<ExcelCorrectOrderDTO> history = validMap.get(key);
            if (history != null) {
                valid.addMessage("数据重复");
                history.addMessage("数据重复");
                continue;
            }
            validMap.put(key, valid);
        }
    }

    private String mutexKey(ExcelCorrectOrderDTO order) {
        return order.getPolicyNo() + "-" + order.getProjectCode() + "-" + order.getOldValue();
    }

    /**
     * 批量批改投保人名字
     *
     * @param data
     */
    private void batchCorrectByAppName(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByAppName(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderAppInfo(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    /**
     * 批量批改投保人手机号
     *
     * @param data
     */
    private void batchCorrectByAppMobile(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByAppMobile(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderAppInfo(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    /**
     * 批量批改投保人证件号
     *
     * @param data
     */
    private void batchCorrectByAppIdNumber(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByAppIdNumber(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderAppInfo(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    /**
     * 批量批改被保人证件号
     *
     * @param data
     */
    private void batchCorrectByInsuredName(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByInsuredName(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderInsuredInfo(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    /**
     * 批量批改被保人手机号
     *
     * @param data
     */
    private void batchCorrectByInsuredMobile(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByInsuredMobile(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderInsuredInfo(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    /**
     * 批量批改被保人证件号
     *
     * @param data
     */
    private void batchCorrectByInsuredIdCard(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByInsuredIdCard(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderInsuredInfo(correctEntityList);
            orderItemMapper.batchCorrectIdNumber(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
            addCommissionDetailMapper.batchCorrectIdNumber(correctEntityList);
            orderCommissionItemMapper.batchCorrectIdNumber(correctEntityList);
            orderCommissionDetailMapper.batchCorrectIdNumber(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    /**
     * 批量批改被保人证件号
     *
     * @param data
     */
    private void batchCorrectByRecommendName(List<ValidationResult<ExcelCorrectOrderDTO>> data) {
        List<CorrectOrderPo> correctEntityList = validCorrectByRecommendName(data);
        if (CollectionUtils.isEmpty(correctEntityList)) {
            return;
        }
        txService.excute(() -> {
            orderMapper.batchUpdateOrderRecommendInfo(correctEntityList);
            orderMapper.batchUpdateCommissionInfo(correctEntityList);
            orderCommissionItemMapper.batchCorrectRecommendMan(correctEntityList);
            orderCommissionDetailMapper.batchCorrectRecommendMan(correctEntityList);
        });
        recordCorrectMessage(correctEntityList);
    }

    private void recordCorrectMessage(List<CorrectOrderPo> orderList) {
        List<SmSaveOrderCorrectDTO> correctList = new ArrayList<>();
        orderList.stream().forEach(entity -> {
            SmSaveOrderCorrectDTO correct = OrderConvertor.buildSmSaveOrderCorrectDTO(entity);
            correctList.add(correct);
        });
        if (!CollectionUtils.isEmpty(correctList)) {
            orderMapper.batchInsertCorrectOrder(correctList);
        }
    }


    /**
     * 投保人名字变更-数据校验
     *
     * @param validList
     * @return
     */
    private List<CorrectOrderPo> validCorrectByAppName(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<SmOrderToCorrectVO> correctOrderList = queryCorrectOrderEntity(null, validList);
        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getAppPersonName();
        });

        Map<String, SmOrderToCorrectVO> policyMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });

        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();
            if (!policyMap.containsKey(order.getPolicyNo())) {
                data.addMessage("当前保单号不存在");
                continue;
            }

            String key = order.getPolicyNo() + "-" + order.getOldValue();
            SmOrderToCorrectVO entity = correctOrderMap.get(key);

            if (entity == null) {
                data.addMessage("变更前信息填写有误");
            }
            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(entity, order);
                po.setOrderId(entity.getFhOrderId());
                entityList.add(po);
            }
        }
        return entityList;
    }

    private CorrectOrderPo buildCorrectEntity(SmOrderToCorrectVO entity, ExcelCorrectOrderDTO order) {
        CorrectOrderPo po = new CorrectOrderPo();
        po.setPolicyNo(order.getPolicyNo());
        po.setOldValue(order.getOldValue());
        po.setNewValue(order.getNewValue());
        po.setProjectCode(order.getProjectCode());
        if (entity != null) {
            po.setInsuredIdCard(entity.getInsuredIdNumber());
        }
        return po;
    }

    /**
     * 校验批改数据-批改投保人电话
     *
     * @param validList
     */
    private List<CorrectOrderPo> validCorrectByAppMobile(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<SmOrderToCorrectVO> correctOrderList = queryCorrectOrderEntity(null, validList);
        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getAppCellPhone();
        });
        Map<String, SmOrderToCorrectVO> policyMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });
        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();

            if (!policyMap.containsKey(order.getPolicyNo())) {
                data.addMessage("当前保单号不存在");
                continue;
            }

            String mobile = order.getNewValue();
            String key = order.getPolicyNo() + "-" + order.getOldValue();

            SmOrderToCorrectVO vo = correctOrderMap.get(key);
            if (vo == null) {
                data.addMessage("变更前信息填写有误");
            }

            if (!CommonUtil.isMobile(mobile)) {
                data.addMessage("变更后的数据格式不正确");
            }

            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(vo, order);
                po.setOrderId(vo.getFhOrderId());
                entityList.add(po);
            }
        }
        return entityList;
    }

    private List<CorrectOrderPo> validCorrectByAppIdNumber(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<String> policyNoList = new ArrayList<>(validList.size());
        List<String> beforeMessage = new ArrayList<>();
        validList.stream()
                .forEach(valid -> {
                    ExcelCorrectOrderDTO order = valid.getSource();
                    policyNoList.add(order.getPolicyNo());
                    beforeMessage.add(order.getOldValue());
                });
        List<SmOrderToCorrectVO> correctOrderList = orderMapper.batchQueryCorrectOrder(policyNoList, beforeMessage, null);

        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getAppIdNumber();
        });

        Map<String, SmOrderToCorrectVO> policyMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });

        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();
            if (!policyMap.containsKey(order.getPolicyNo())) {
                data.addMessage("当前保单号不存在");
                continue;
            }

            String key = order.getPolicyNo() + "-" + order.getOldValue();
            SmOrderToCorrectVO vo = correctOrderMap.get(key);
            if (vo == null) {
                data.addMessage("变更前信息填写有误");
            }

            if (!CommonUtil.isIdCardNo(order.getNewValue())) {
                data.addMessage("变更后的数据格式不正确");
            }

            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(vo,order);
                po.setOrderId(vo.getFhOrderId());
                setPeopleField(vo.getCompanyId(), order.getNewValue(), po);
                entityList.add(po);
            }
        }
        return entityList;
    }

    private List<CorrectOrderPo> validCorrectByInsuredName(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<String> policyNoList = new ArrayList<>(validList.size());
        List<String> beforeMessage = new ArrayList<>();
        validList.stream()
                .forEach(valid -> {
                    ExcelCorrectOrderDTO order = valid.getSource();
                    policyNoList.add(order.getPolicyNo());
                    beforeMessage.add(order.getOldValue());
                });
        List<SmOrderToCorrectVO> correctOrderList = orderMapper.batchQueryCorrectOrder(policyNoList, beforeMessage, null);

        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getInsuredPersonName();
        });

        Map<String, SmOrderToCorrectVO> policyMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });

        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();
            if (!policyMap.containsKey(order.getPolicyNo())) {
                data.addMessage("当前保单号不存在");
                continue;
            }
            String key = order.getPolicyNo() + "-" + order.getOldValue();
            SmOrderToCorrectVO vo = correctOrderMap.get(key);
            if (vo == null) {
                data.addMessage("变更前信息填写有误");
            }
            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(vo, order);
                po.setOrderId(vo.getFhOrderId());
                entityList.add(po);
            }
        }
        return entityList;
    }

    /**
     * 被保人手机号验证
     *
     * @param validList
     * @return
     */
    private List<CorrectOrderPo> validCorrectByInsuredMobile(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<String> policyNoList = new ArrayList<>(validList.size());
        List<String> beforeMessage = new ArrayList<>();
        validList.stream()
                .forEach(valid -> {
                    ExcelCorrectOrderDTO order = valid.getSource();
                    policyNoList.add(order.getPolicyNo());
                    beforeMessage.add(order.getOldValue());
                });
        List<SmOrderToCorrectVO> correctOrderList = orderMapper.batchQueryCorrectOrder(policyNoList, beforeMessage, null);

        Map<String, SmOrderToCorrectVO> policyMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });
        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getInsuredCellPhone();
        });

        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();
            if (!policyMap.containsKey(order.getPolicyNo())) {
                data.addMessage("当前保单号不存在");
                continue;
            }

            String key = order.getPolicyNo() + "-" + order.getOldValue();
            SmOrderToCorrectVO vo = correctOrderMap.get(key);
            if (vo == null) {
                data.addMessage("变更前信息填写有误");
            }
            if (!CommonUtil.isMobile(order.getNewValue())) {
                data.addMessage("变更后的数据格式不正确");
            }
            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(vo, order);
                po.setOrderId(vo.getFhOrderId());
                entityList.add(po);
            }
        }
        return entityList;
    }

    /**
     * 被保人证件号验证
     *
     * @param validList
     * @return
     */
    private List<CorrectOrderPo> validCorrectByInsuredIdCard(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<String> policyNoList = new ArrayList<>(validList.size());
        List<String> beforeMessage = new ArrayList<>();
        List<String> afterMessage = new ArrayList<>();
        validList.stream()
                .forEach(valid -> {
                    ExcelCorrectOrderDTO order = valid.getSource();
                    policyNoList.add(order.getPolicyNo());
                    beforeMessage.add(order.getOldValue());
                    afterMessage.add(order.getNewValue());
                });
        List<SmOrderToCorrectVO> correctOrderList = orderMapper.batchQueryCorrectOrder(policyNoList, beforeMessage, null);
        List<SmOrderToCorrectVO> mutexOrderList = orderMapper.batchQueryCorrectOrder(policyNoList, afterMessage, CorrectProject.INS_ID_NUMBER.getCode());


        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getInsuredIdNumber();
        });
        Map<String, SmOrderToCorrectVO> policyMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });
        Map<String, SmOrderToCorrectVO> mutexOrderMap = LambdaUtils.safeToMap(mutexOrderList, order -> {
            return order.getPolicyNo() + "-" + order.getInsuredIdNumber();
        });

        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();
            if (!policyMap.containsKey(order.getPolicyNo())) {
                data.addMessage("当前保单号不存在");
                continue;
            }
            String key = order.getPolicyNo() + "-" + order.getOldValue();
            SmOrderToCorrectVO vo = correctOrderMap.get(key);
            if (vo == null) {
                data.addMessage("变更前信息填写有误");
            }
            if (mutexOrderMap.containsKey(key)) {
                data.addMessage("变更后信息填写有误");
            }
            if (!CommonUtil.isIdCardNo(order.getNewValue())) {
                data.addMessage("变更后的数据格式不正确");
            }
            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(vo, order);
                setPeopleField(vo.getCompanyId(), vo.getInsuredIdNumber(), po);
                po.setOrderId(vo.getFhOrderId());
                entityList.add(po);
            }
        }
        return entityList;
    }

    /**
     * 批量批改推荐人-数据校验
     *
     * @param validList
     */
    private List<CorrectOrderPo> validCorrectByRecommendName(List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<String> policyNoList = new ArrayList<>(validList.size());
        List<String> beforeMessage = new ArrayList<>();
        List<String> afterMessage = new ArrayList<>();

        validList.stream()
                .forEach(valid -> {
                    ExcelCorrectOrderDTO order = valid.getSource();
                    policyNoList.add(order.getPolicyNo());
                    if (!Objects.equals(OrderConstants.NULL, order.getOldValue())) {
                        String userId = catRecommendId(order.getOldValue());
                        beforeMessage.add(userId);
                    }
                    if (!Objects.equals(OrderConstants.NULL, order.getNewValue())) {
                        String userId = catRecommendId(order.getNewValue());
                        afterMessage.add(userId);
                    }
                });
        List<SmOrderToCorrectVO> correctOrderList = orderMapper.batchQueryCorrectOrder(policyNoList, null, CorrectProject.RECOMMEND_NAME.getCode());

        List<String> recommendIdList = new ArrayList<>();
        recommendIdList.addAll(beforeMessage);
        recommendIdList.addAll(afterMessage);

        List<WxUserVO> userList = userMapper.queryUserList(recommendIdList);

        List<UserPost> userPostList = userPostMapper.queryByUserIdList(recommendIdList);

        Map<String, WxUserVO> userMap = LambdaUtils.safeToMap(userList, WxUserVO::getUserId);
        Map<String, List<UserPost>> userPostMap = LambdaUtils.groupBy(userPostList, UserPost::getJobNumber);

        Map<String, SmOrderToCorrectVO> correctOrderMap = LambdaUtils.safeToMap(correctOrderList, order -> {
            return order.getPolicyNo();
        });

        List<CorrectOrderPo> entityList = new ArrayList<>();
        for (ValidationResult<ExcelCorrectOrderDTO> data : validList) {
            ExcelCorrectOrderDTO order = data.getSource();
            String key = order.getPolicyNo();
            SmOrderToCorrectVO vo = correctOrderMap.get(key);
            if (vo == null) {
                data.addMessage("变更前信息填写有误");
                continue;
            }
            String oldValue = order.getOldValue();
            String newValue = order.getNewValue();
            String recommendId = catRecommendId(oldValue);
            String recommendName = catRecommendName(oldValue);

            String newRecommendId = catRecommendId(newValue);
            String newRecommendName = catRecommendName(newValue);

            WxUserVO oldUser = userMap.get(recommendId);

            if (Objects.equals(OrderConstants.NULL, oldValue)) {
                if (StringUtils.isNotBlank(vo.getRecommendId())) {
                    data.addMessage("变更前信息填写有误");
                }
            } else {
                if (!Objects.equals(recommendId, vo.getRecommendId())) {
                    data.addMessage("变更前信息填写有误");
                }
                if (oldUser != null && !Objects.equals(oldUser.getUserName(), recommendName)) {
                    data.addMessage("变更前信息填写有误");
                }
            }
            WxUserVO newUser = userMap.get(newRecommendId);
            if (newUser == null || !Objects.equals(newUser.getUserName(), newRecommendName)) {
                data.addMessage("变更后员工不存在");
            }
            if (newUser != null && Objects.equals(newUser.getStatus(), "8")) {
                data.addMessage("变更后员工不存在");
            }

            Date paymentTime = vo.getPaymentTime();
            if (Objects.equals(vo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                    && Objects.equals(OrderConstants.NULL, oldValue)) {
                paymentTime = new Date();
            }
            if (StringUtils.isNotBlank(vo.getRecommendId()) && Objects.nonNull(oldUser)) {
                String month1 = DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT);
                String month2 = DateUtil.format(vo.getPaymentTime(), DateUtil.CN_YEAR_MONTH_FORMAT);
                if (Objects.isNull(vo.getPaymentTime()) || !Objects.equals(month1, month2)) {
                    data.addMessage("已存在推荐人的保单，不容许跨月修改推荐人");
                }
            }

            UserPost currentPost = null;

            List<UserPost> postList = userPostMap.get(newRecommendId);
            if (postList != null) {
                if (postList.size() > 1 && StringUtils.isBlank(order.getOrgName())) {
                    data.addMessage("分支信息未填写");
                } else {
                    currentPost = pickOne(order.getOrgName(), postList);
                    if (Objects.isNull(currentPost)) {
                        data.addMessage("分支与人员不匹配");
                    }
                }
            }
            if (currentPost == null) {
                data.addMessage("变更后的推荐人岗位信息不存在");
            }
            if (data.isSuccess()) {
                CorrectOrderPo po = buildCorrectEntity(vo, order);
                po.setOrderId(vo.getFhOrderId());
                po.setOldValue(catRecommendId(order.getOldValue()));
                po.setNewValue(catRecommendId(order.getNewValue()));

                po.setPaymentTime(paymentTime);
                po.setRecommendPostName(currentPost.getPostName());
                po.setRecommendMainJobNumber(currentPost.getMainJobNumber());
                po.setRecommendOrgCode(currentPost.getOrgCode());

                entityList.add(po);
            }
        }
        return entityList;
    }

    private UserPost pickOne(String orgName, List<UserPost> postList) {
        if (CollectionUtils.isEmpty(postList)) {
            return null;
        }
        if (postList.size() == 1) {
            return postList.get(0);
        }
        for (UserPost entry : postList) {
            if (Objects.equals(orgName, entry.getOrgName())) {
                return entry;
            }
        }
        return null;
    }


    private void setPeopleField(Integer companyId, String idCard, CorrectOrderPo po) {
        CmpySettingQuery query = new CmpySettingQuery();
        query.setCompanyId(companyId.toString());
        query.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<WxFormFieldOptionVO> settings = settingMapper.listWxCompanySettings(query);
        Map<String, String> sexMap = new HashMap<>(8);

        settings.forEach(st -> sexMap.put(st.getOptionName(), st.getOptionCode()));
        String code = CommonUtil.getSex(idCard);
        String sex = sexMap.get(code);
        String birthday = CentNoUtil.getBirthDay(idCard);

        po.setSex(sex);
        po.setBirthday(birthday);
    }

    private List<SmOrderToCorrectVO> queryCorrectOrderEntity(String projectCode, List<ValidationResult<ExcelCorrectOrderDTO>> validList) {
        List<String> policyNoList = new ArrayList<>(validList.size());
        List<String> beforeMessage = new ArrayList<>();
        validList.stream()
                .forEach(valid -> {
                    ExcelCorrectOrderDTO order = valid.getSource();
                    policyNoList.add(order.getPolicyNo());
                    beforeMessage.add(order.getOldValue());
                });
        return orderMapper.batchQueryCorrectOrder(policyNoList, beforeMessage, projectCode);
    }

    /**
     * @param recommendId
     * @return
     */
    private String catRecommendId(String recommendId) {
        if (StringUtils.isBlank(recommendId)) {
            return null;
        }
        Matcher matcher = p.matcher(recommendId);
        while (matcher.find()) {
            return matcher.group(2);
        }
        return recommendId;
    }

    private String catRecommendName(String recommendId) {
        if (StringUtils.isBlank(recommendId)) {
            return null;
        }

        Matcher matcher = p.matcher(recommendId);
        while (matcher.find()) {
            return matcher.group(1);
        }
        return recommendId;
    }
}
