package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.product.ProductConfig;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductAttrHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductAttrMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmProductConfirmHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.sys.SysDutyConfigMapper;
import com.cfpamf.ms.insur.admin.enums.aicheck.AiCheckWayEnum;
import com.cfpamf.ms.insur.admin.enums.product.EnumPremiumFlow;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.product.*;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductNotify;
import com.cfpamf.ms.insur.admin.pojo.po.aicheck.AkProductQuestionnaireHis;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductAttrHistory;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductConfirmHistory;
import com.cfpamf.ms.insur.admin.pojo.query.SmPlanFactorQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmPlanPageQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.product.*;
import com.cfpamf.ms.insur.admin.pojo.vo.product.basic.ProductAttrVo;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireHisService;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireService;
import com.cfpamf.ms.insur.admin.service.product.ProductQueryService;
import com.cfpamf.ms.insur.admin.service.product.config.AttributeProcessorFactory;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductAttrDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import groovy.lang.Lazy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * 小额保险产品service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SmProductHistoryService {

    /**
     * 小额保险产品mapper
     */
    @Autowired
    private SmProductMapper mapper;


    @Resource
    private SmProductAttrHistoryMapper attrHistoryMapper;

    @Autowired
    SmProductAttrMapper attrMapper;

    @Autowired
    SmProductVersionMapper productVersionMapper;

    @Autowired
    private SmProductHistoryMapper historyMapper;

    @Autowired
    private SmPlanSaleOrgMapper planSaleOrgMapper;

    @Autowired
    private SysDutyConfigMapper dutyConfigMapper;
    /**
     * 字典service
     */
    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private ProductQueryService productQueryService;

    /**
     * 职业mapper
     */
    @Autowired
    private SmOccupationMapper occupationMapper;

    /**
     * 提成mapper
     */
    @Autowired
    private SmCommissionMapper commissionMapper;

    /**
     * 客户告知书Mapper
     */
    @Autowired
    private SmProductNotifyMapper notifyMapper;

    /**
     * RedisUtil
     */
    @Autowired
    private RedisUtil<String, String> redisUtil;

    /**
     * 自主确认条款
     */
    @Autowired
    private SmProductConfirmHistoryMapper confirmHistoryMapper;

    @Lazy
    @Autowired
    private AkProductQuestionnaireHisService akProductQuestionnaireHisService;

    @Lazy
    @Autowired
    private AkProductQuestionnaireService akProductQuestionnaireService;

    @Autowired
    private AttributeProcessorFactory attributeProcessorFactory;

    /**
     * 查询产品详情基本信息(历史快照中查询)
     * 此接口谨慎使用 productCategoryName ，只映射了一个类型名称
     *
     * @param id
     * @return
     */
    public SmProductNotificationDTO getProductHealthNotification(int id, Integer version) {
        SmProductDetailVO detailVO = getProductById(id, version);
        SmProductNotificationDTO dto = new SmProductNotificationDTO();
        dto.setProductId(detailVO.getId());
        dto.setAiCheck(detailVO.getAiCheck());
        dto.setHealthNotification(detailVO.getHealthNotification());
        dto.setAiCheckWay(detailVO.getAiCheckWay());
        version = defaultIfAbsent(detailVO.getId(), version);
        if (Objects.equals(detailVO.getAiCheckWay(), AiCheckWayEnum.QUESTION.getCode())) {
            AkProductQuestionnaireHis questionnaire = akProductQuestionnaireHisService.getAkProductQuestionaireHisPOByProductId(detailVO.getId(), version);
            if (questionnaire != null) {
                dto.setFileName(questionnaire.getFileName());
                dto.setFileUrl(questionnaire.getFileUrl());
            }
            List<QuestionDTO> questions = akProductQuestionnaireService.queryQuestions(id, version);
            dto.setQuestions(questions);
        }

        return dto;
    }

    /**
     * 查询产品详情基本信息(历史快照中查询)
     * 此接口谨慎使用 productCategoryName ，只映射了一个类型名称
     *
     * @param id
     * @return
     */
    public SmProductDetailVO getProductById(int id, Integer version) {
        int queryVersion = defaultIfAbsent(id, version);
        SmProductDetailVO detailVO = historyMapper.getProductById(id, queryVersion);
        if (Objects.nonNull(detailVO)) {
            detailVO.init();
            detailVO.setId(id);
            SmProductAttrHistory attrQuery = new SmProductAttrHistory();
            attrQuery.setProductId(id);
            attrQuery.setVersion(queryVersion);
            List<SmProductAttrHistory> select = attrHistoryMapper.select(attrQuery);
            Map<String, String> attrMap = LambdaUtils.safeToMap(select, SmProductAttrHistory::getAttrCode, SmProductAttrHistory::getAttrVal);
            detailVO.setAttrs(attrMap);
        }
        // 根据id获取标签信息
        detailVO.setProductLabelDTO(productQueryService.getProductLabels(id));

        return detailVO;
    }

    /**
     * 查询产品保障项目
     *
     * @param productId
     * @return
     */
    public List<SmProductCoverageVO> getProductCoverageList(int productId, Integer version) {
        return historyMapper.listProductCoverages(productId, defaultIfAbsent(productId, version));
    }

    /**
     * 获取产品计划的保障责任表
     *
     * <AUTHOR>
     */
    public List<DutyTableVO> getPlanDutyTable(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        List<SmProductCoverageVO> data = historyMapper.listProductCoverages(productId, version);
        List<SmPlanVO> plans = getProductPlans(productId, version);
        List<DutyTableVO> result = new ArrayList<>();
        plans.forEach(plan -> {
            DutyTableVO table = new DutyTableVO();
            BeanUtils.copyProperties(plan, table);
            table.setCoverages(data.stream().filter(cv -> Objects.equals(cv.getPlanId(), plan.getPlanId())).collect(toList()));
            result.add(table);
        });
        return result;
    }

    /**
     * 查询产品计划
     *
     * @param productId
     * @return
     */
    public List<SmPlanVO> getProductPlans(int productId, Integer version) {
        List<SmPlanVO> plan = getSmPlanVOList(productId, version);
        plan.forEach(i -> {
            if (org.apache.commons.lang.StringUtils.isBlank(i.getMinPremium())) {
                if (i.getMinPrice() != null) {
                    i.setMinPremium(String.valueOf(i.getMinPrice()));
                }
            }

        });
        return plan;
    }

    /**
     * 获取产品和版本对应的产品计划
     *
     * @param productId
     * @param version
     * @return
     */
    public List<SmPlanVO> getSmPlanVOList(int productId, Integer version) {
        return historyMapper.listProductPlansById(String.valueOf(productId), true,
                defaultIfAbsent(productId, version));
    }

    /**
     * 查询产品计划价格费率表
     *
     * @param id
     * @return
     */
    public List<SmPlanFactorPriceDT> getPlanFactorPrice(int id, Integer version) {
        int realVersion = defaultIfAbsent(id, version);
        List<SmPlanFactorPriceDT> oldPriceVos = historyMapper.listPlanFactorPrices(id, null, realVersion);

        List<SmPlanVO> plans = getProductPlans(id, realVersion);
        if (plans.isEmpty()) {
            return Collections.emptyList();
        }
        SmPriceFactorOptionalsVO vo = getProductPriceFactorOptionals(id, realVersion);

        List<SmPlanFactorPriceDT> priceVos = new ArrayList<>();
        List<SmPlanFactorPriceDT> iterationVos = new ArrayList<>();
        priceVos.add(new SmPlanFactorPriceDT());

        List<SmFactorOptionalDTO> occuplCategorys = vo.getOccuplCategory();
        if (CommonUtil.isCollectionNotEmpty(occuplCategorys)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = occuplCategorys.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = occuplCategorys.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setOccuplCategoryName(optionalDto.getOptionalName());
                    copy.setOccuplCategoryOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> vehicleSeatNumbers = vo.getVehicleSeatNumber();
        if (CommonUtil.isCollectionNotEmpty(vehicleSeatNumbers)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = vehicleSeatNumbers.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = vehicleSeatNumbers.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setVehicleSeatNumberName(optionalDto.getOptionalName());
                    copy.setVehicleSeatNumberOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> socialSecuritys = vo.getSocialSecurity();
        if (CommonUtil.isCollectionNotEmpty(socialSecuritys)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = socialSecuritys.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = socialSecuritys.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSocialSecurityName(optionalDto.getOptionalName());
                    copy.setSocialSecurityOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> sexs = vo.getSex();
        if (CommonUtil.isCollectionNotEmpty(sexs)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = sexs.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = sexs.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSexName(optionalDto.getOptionalName());
                    copy.setSexOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> underWritingAges = vo.getUnderWritingAge();
        if (CommonUtil.isCollectionNotEmpty(underWritingAges)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = underWritingAges.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = underWritingAges.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setUnderWritingAgeName(optionalDto.getOptionalName());
                    copy.setUnderWritingAgeOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> validPeriods = vo.getValidPeriod();
        if (CommonUtil.isCollectionNotEmpty(validPeriods)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = validPeriods.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = validPeriods.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setValidPeriodName(optionalDto.getOptionalName());
                    copy.setValidPeriodOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> protonHeavyIonMedicines = vo.getProtonHeavyIonMedicine();
        if (CommonUtil.isCollectionNotEmpty(protonHeavyIonMedicines)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = protonHeavyIonMedicines.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = protonHeavyIonMedicines.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setProtonHeavyIonMedicineName(optionalDto.getOptionalName());
                    copy.setProtonHeavyIonMedicineOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> specifiedDiseaseSpecificCares = vo.getSpecifiedDiseaseSpecificCare();
        if (CommonUtil.isCollectionNotEmpty(specifiedDiseaseSpecificCares)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = specifiedDiseaseSpecificCares.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = specifiedDiseaseSpecificCares.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSpecifiedDiseaseSpecificCareName(optionalDto.getOptionalName());
                    copy.setSpecifiedDiseaseSpecificCareOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> japanMedicalTreatments = vo.getJapanMedicalTreatment();
        if (CommonUtil.isCollectionNotEmpty(japanMedicalTreatments)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = japanMedicalTreatments.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = japanMedicalTreatments.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setJapanMedicalTreatmentName(optionalDto.getOptionalName());
                    copy.setJapanMedicalTreatmentOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        //202010114 添加是否吸烟选项
        List<SmFactorOptionalDTO> smoke = vo.getSmoke();
        if (CommonUtil.isCollectionNotEmpty(smoke)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = smoke.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = smoke.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSmokeOptionalName(optionalDto.getOptionalName());
                    copy.setSmokeOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }
        iterationVos.clear();
        iterationVos.addAll(priceVos);
        priceVos.clear();
        for (int i = 0, len = plans.size(); i < len; i++) {
            SmPlanVO planVo = plans.get(i);
            for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                copy.setPlanId(planVo.getId());
                copy.setPlanName(planVo.getPlanName());
                copy.setProductName(planVo.getProductName());
                copy.setProductId(planVo.getProductId());
                copy.setPrice("0.00");
                copy.setAvailable(Boolean.TRUE);
                priceVos.add(copy);
            }
        }

        oldPriceVos.stream().forEach(op -> {
            Optional<SmPlanFactorPriceDT> optional = priceVos.stream().filter(op::isSameGroup).findFirst();
            if (optional.isPresent()) {
                optional.get().setPrice(op.getPrice());
                optional.get().setAvailable(op.getAvailable());
            }
        });
        return priceVos;
    }

    /**
     * 查询产品保险条款
     *
     * @param productId
     * @return
     */
    public SmProductClauseMasterVO getProductClause(int productId, Integer version) {

        int realVersion = defaultIfAbsent(productId, version);
        String productClauseContent = historyMapper.getProductClauseContent(productId, realVersion);
        SmProductClauseMasterVO vo = new SmProductClauseMasterVO();
        vo.setClauses(historyMapper.listProductClausesByProductId(productId, realVersion));
        vo.setContent(productClauseContent);
        return vo;
    }

    /**
     * 获取产品修改版本
     *
     * @param productId
     * @return
     */
    public int getProductUpdateVersion(int productId) {
        return productVersionMapper.getMaxVersion(productId) + 1;
    }


    public List<SmProductCoverageAmountVO> getProductCoverageAmountListByPlan(int planId, Integer version) {

        return historyMapper.listProductCoverageAmountsByPlanId(planId, version);
    }

    /**
     * 按产品计划组装责任列表
     *
     * @param productId
     * @return
     */
    public List<PremiumTableVo> getCoverageByPlan(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        List<SmPlanVO> plans = historyMapper.listProductPlansById(String.valueOf(productId), true,
                defaultIfAbsent(productId, version));
        List<SmProductCoverageAmountVO> amounts = historyMapper.listProductCoverageAmounts(productId, version);

        List<PremiumTableVo> datas = convertBean(productId, historyMapper.listProductCoverages(productId, version), plans);
        datas.forEach(p -> {
            p.getCoverages().forEach(c -> {
                List<SmProductCoverageAmountVO> coverageAmounts = new ArrayList<>();
                for (SmProductCoverageAmountVO av : amounts) {
                    if (Objects.equals(av.getSpcId(), c.getSpcId())) {
                        coverageAmounts.add(av.copyOne());
                    }
                }
                c.setCoverageAmounts(coverageAmounts);
            });
        });
        return datas;
    }

    public List<PremiumTableVo> convertBean(Integer productId, List<SmProductCoverageVO> cvs, List<SmPlanVO> plans) {
        List<PremiumTableVo> result = new ArrayList<>();
        plans.forEach(i -> {
            PremiumTableVo vo = new PremiumTableVo();
            vo.setPlanId(i.getPlanId());
            vo.setPlanName(i.getPlanName());
            vo.setProductId(productId);
            cvs.forEach(j -> {
                vo.addOne(createPremiumItem(productId, j));
            });
            result.add(vo);
        });
        return result;
    }

    /**
     * 查询产品保障项目金额
     *
     * @param productId
     * @return
     */
    public List<SmProductCoverageAmountItemVO> getProductCoverageAmountList(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        List<SmProductCoverageAmountVO> amounts = historyMapper.listProductCoverageAmounts(productId, version);

        SmProductDetailVO productDetailVO = historyMapper.getProductById(productId, version);
        List<SmProductCoverageAmountItemVO> items = new ArrayList<>();
        if (ProductConfig.groupTemplate(productDetailVO.getProductAttrCode())) {
            /**
             * 团险配置流程
             */
            historyMapper.listProductCoverages(productId, version)
                    .forEach(cv -> {
                        SmProductCoverageAmountItemVO item = newSmProductCoverageAmountItem(productId, cv);
                        item.setCoverageAmounts(amounts.stream().filter(am -> Objects.equals(am.getSpcId(), cv.getSpcId())).collect(toList()));
                        items.add(item);
                    });
        } else {
            /**
             * 个险配置流程
             */
            List<SmPlanVO> plans = historyMapper.listProductPlansById(String.valueOf(productId), true, version);
            historyMapper.listProductCoverages(productId, version)
                    .forEach(cv -> {
                        SmProductCoverageAmountItemVO item = newSmProductCoverageAmountItem(productId, cv);
                        List<SmProductCoverageAmountVO> cpAmounts = new ArrayList<>();
                        plans.forEach(p -> {
                            Optional<SmProductCoverageAmountVO> optional = amounts.stream().filter(am -> Objects.equals(am.getSpcId(), cv.getSpcId()) && Objects.equals(am.getPlanId(), p.getId()))
                                    .findFirst();
                            SmProductCoverageAmountVO cpAmount;
                            if (optional.isPresent()) {
                                cpAmount = optional.get();
                            } else {
                                cpAmount = new SmProductCoverageAmountVO();
                                cpAmount.setProductId(productId);
                                cpAmount.setPlanId(p.getId());
                                cpAmount.setPlanName(p.getPlanName());
                                cpAmount.setSpcId(cv.getSpcId());
                                cpAmount.setCvgType(cv.getCvgType());
                                cpAmount.setCvgItemName(cv.getCvgItemName());
                            }
                            cpAmount.setPlanName(p.getPlanName());
                            cpAmounts.add(cpAmount);
                            item.setCoverageAmounts(cpAmounts);
                        });
                        items.add(item);
                    });
        }
        return items;
    }

    /**
     * 获取产品计划的费率表
     *
     * @param productId
     * @param version
     * @return
     * <AUTHOR>
     */
    public List<CoverageTableVO> getCoverageTable(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        SmProductDetailVO productDetailVO = historyMapper.getProductById(productId, version);
        if (ProductConfig.groupTemplate(productDetailVO.getProductAttrCode())) {
            /**
             * 团险配置流程
             */
            return getCoverageTable4Group(productId, version);
        } else {
            throw new RuntimeException("功能暂未实现");
        }

    }

    /**
     * 针对团险-查询产品责任限额表
     *
     * @param productId
     * @return
     */
    public List<CoverageTableVO> getCoverageTable4Group(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        List<SmProductCoverageAmountVO> amounts = historyMapper.listProductCoverageAmounts(productId, version);
        List<CoverageTableVO> result = new ArrayList<>();
        TreeMap<Integer, List<SmProductCoverageVO>> coverageMap = new TreeMap<>();
        historyMapper.listProductCoverages(productId, version)
                .forEach(cv -> {
                    if (coverageMap.containsKey(cv.getPlanId())) {
                        coverageMap.get(cv.getPlanId()).add(cv);
                    } else {
                        List<SmProductCoverageVO> vos = new ArrayList<>();
                        vos.add(cv);
                        coverageMap.put(cv.getPlanId(), vos);
                    }
                });
        for (Map.Entry<Integer, List<SmProductCoverageVO>> entry : coverageMap.entrySet()) {
            CoverageTableVO tab = new CoverageTableVO();
            tab.setPlanId(entry.getKey());
            tab.setProductId(productId);
            List<CoverageItemVo> items = new ArrayList<>();
            if (entry.getValue().size() > 0) {
                SmProductCoverageVO vo = entry.getValue().get(0);
                if (vo != null) {
                    tab.setPlanName(vo.getPlanName());
                }
                entry.getValue().forEach(i -> {
                    CoverageItemVo item = buildCoverageItem(productId, i);
                    List<CoverageAmountVo> cavs = new ArrayList<>();
                    for (SmProductCoverageAmountVO ite : amounts) {
                        if (Objects.equals(ite.getSpcId(), i.getSpcId())) {
                            CoverageAmountVo cav = new CoverageAmountVo();
                            cav.setCvgAmount(ite.getCvgAmount());
                            cav.setCvgNotice(ite.getCvgNotice());
                            cav.setSpcId(ite.getSpcId());
                            cav.setPlanId(i.getPlanId());
                            cav.setSpcaId(ite.getSpcaId());
                            cav.setProductId(i.getProductId());
                            cavs.add(cav);
                        }
                    }
                    item.setCoverageAmounts(cavs);
                    items.add(item);
                });
                tab.setCoverages(items);
                result.add(tab);
            }
        }
        return result;
    }

    /**
     * newSmProductCoverageAmountItem
     *
     * @param productId
     * @param cv
     * @return
     */
    private CoverageItemVo buildCoverageItem(int productId, SmProductCoverageVO cv) {
        CoverageItemVo item = new CoverageItemVo();
        item.setCoverageAmounts(new ArrayList<>());
        item.setProductId(productId);
        item.setCvgItemName(cv.getCvgItemName());
        item.setCvgNameDetail(cv.getCvgNameDetail());
        item.setCvgType(cv.getCvgType());
        item.setCvgRespType(cv.getCvgRespType());
        item.setSpcId(cv.getSpcId());
        item.setPlanId(cv.getPlanId());
        item.setPlanName(cv.getPlanName());
        return item;
    }

    /**
     * 查询产品保障项目保费
     *
     * @param productId
     * @return
     */
    public List<SmProductCoveragePremiumItemVO> getProductCoveragePremiumList(int productId, Integer version) {

        version = defaultIfAbsent(productId, version);
        List<SmProductCoveragePremiumDTO> premiums = historyMapper.listProductCoveragePremiums(productId, version);
        SmProductDetailVO productDetail = historyMapper.getProductById(productId, version);
        String ocpn = productDetail.getGlOcpnGroup();
        if (StringUtils.isEmpty(ocpn)) {
            throw new BizException(ExcptEnum.PRODUCT_OCPN_ERROR_501111);
        }
        String[] ocpns = ocpn.split(",");
        if (ocpns.length == 0) {
            throw new BizException(ExcptEnum.PRODUCT_OCPN_ERROR_501111);
        }
        String[] sortedOcpns = Arrays.stream(ocpns).sorted().toArray(String[]::new);
        List<SmProductCoveragePremiumItemVO> coverageItems = new ArrayList<>();
        getProductCoverageAmountList(productId, version)
                .forEach(ci -> {
                    SmProductCoveragePremiumItemVO item = new SmProductCoveragePremiumItemVO();
                    item.setCvgItemName(ci.getCvgItemName());
                    item.setProductId(ci.getProductId());
                    item.setSpcId(ci.getSpcId());
                    item.setCvgType(ci.getCvgType());
                    List<SmProductCoveragePremiumItemVO.CoverageAmount> coverageAmounts = new ArrayList<>();
                    ci.getCoverageAmounts().forEach(ca -> {
                        SmProductCoveragePremiumItemVO.CoverageAmount cav = new SmProductCoveragePremiumItemVO.CoverageAmount();
                        cav.setCvgAmount(ca.getCvgAmount());
                        cav.setCvgNotice(ca.getCvgNotice());
                        cav.setSpcaId(ca.getSpcaId());
                        cav.setPremiums(new ArrayList<>());
                        for (String gp : sortedOcpns) {
                            Optional<SmProductCoveragePremiumDTO> optional = premiums.stream().filter(p -> Objects.equals(p.getSpcaId(), ca.getSpcaId()) && Objects.equals(gp, p.getOccupationGroup())).findFirst();
                            if (optional.isPresent()) {
                                cav.getPremiums().add(optional.get());
                            } else {
                                SmProductCoveragePremiumDTO premium = new SmProductCoveragePremiumDTO();
                                premium.setProductId(ca.getProductId());
                                premium.setSpcaId(ca.getSpcaId());
                                premium.setSpcId(ca.getSpcId());
                                premium.setOccupationGroup(gp);
                                premium.setCvgItemName(ca.getCvgItemName());
                                premium.setCvgAmount(ca.getCvgAmount());
                                premium.setCvgNotice(ca.getCvgNotice());
                                cav.getPremiums().add(premium);
                            }
                        }
                        coverageAmounts.add(cav);
                    });
                    item.setCoverageAmounts(coverageAmounts);
                    coverageItems.add(item);
                });
        return coverageItems;
    }

    /**
     * 查询产品保障项目保费
     *
     * @param productId
     * @return
     * <AUTHOR>
     */
    public PremiumPage getPremiumPage(int productId, Integer version) {
        PremiumPage page = new PremiumPage();
        page.setProductId(productId);
        page.setPremiumFiles(getPremiumFile(productId));
        page.setPremiumTable(getPremiumTable(productId, version));
        return page;
    }

    /**
     * 获取产品费率表展示文件
     *
     * @param productId
     * @return
     */
    private List<PremiumFile> getPremiumFile(int productId) {
        List<SmProductExtend> extendList = mapper.getProductExtendByType(productId, 1);
        List<PremiumFile> data = new ArrayList<>();
        if (extendList != null) {
            extendList.forEach(i -> {
                PremiumFile file = new PremiumFile();
                file.setId(i.getId());
                file.setName(i.getAttrName());
                file.setUrl(i.getAttrValue());
                data.add(file);
            });
        }
        return data;
    }

    public List<PremiumTableVo> getPremiumTable(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        List<SmProductCoveragePremiumDTO> premiums = historyMapper.listProductCoveragePremiums(productId, version);
        SmProductDetailVO productDetail = historyMapper.getProductById(productId, version);
        String ocpn = productDetail.getGlOcpnGroup();
        if (StringUtils.isEmpty(ocpn)) {
            throw new BizException(ExcptEnum.PRODUCT_OCPN_ERROR_501111);
        }
        String[] ocpns = ocpn.split(",");
        if (ocpns.length == 0) {
            throw new BizException(ExcptEnum.PRODUCT_OCPN_ERROR_501111);
        }
        String[] sortedOcpns = Arrays.stream(ocpns).sorted().toArray(String[]::new);
        List<PremiumTableVo> planPremiumTables = getCoverageByPlan(productId, version);
        if (CollectionUtils.isEmpty(planPremiumTables)) {
            return planPremiumTables;
        }
        for (PremiumTableVo c : planPremiumTables) {
            if (CollectionUtils.isEmpty(c.getCoverages())) {
                continue;
            }
            for (PremiumItem b : c.getCoverages()) {
                if (CollectionUtils.isEmpty(b.getCoverageAmounts())) {
                    continue;
                }
                for (SmProductCoverageAmountVO a : b.getCoverageAmounts()) {
                    List<SmProductCoveragePremiumDTO> ps = new ArrayList<>();
                    a.setPlanId(c.getPlanId());
                    a.setPlanName(c.getPlanName());
                    for (String gp : sortedOcpns) {
                        Optional<SmProductCoveragePremiumDTO> optional = premiums.stream().filter(p -> Objects.equals(p.getPlanId(), c.getPlanId()) && Objects.equals(p.getSpcaId(), a.getSpcaId()) && Objects.equals(gp, p.getOccupationGroup())).findFirst();
                        if (optional.isPresent()) {
                            SmProductCoveragePremiumDTO d = optional.get();
                            d.setPlanName(c.getPlanName());
                            ps.add(d);
                        } else {
                            SmProductCoveragePremiumDTO premium = new SmProductCoveragePremiumDTO();
                            premium.setProductId(b.getProductId());
                            premium.setSpcaId(a.getSpcaId());
                            premium.setSpcId(b.getSpcId());
                            premium.setPlanId(c.getPlanId());
                            premium.setPlanName(c.getPlanName());
                            premium.setOccupationGroup(gp);
                            premium.setCvgItemName(b.getCvgItemName());
                            premium.setCvgAmount(a.getCvgAmount());
                            premium.setCvgNotice(a.getCvgNotice());
                            ps.add(premium);
                        }
                    }
                    a.setPremiums(ps);
                }
            }
        }
        return planPremiumTables;
    }

    //初始化一个新的快照版本
    public void initNewHistory(int productId, int oldVersion, int version) {

        //主表信息
        historyMapper.insertVersionProduct(productId, version);
        //计划
        historyMapper.insertVersionPlan(productId, version);
        //费率表
        historyMapper.insertVersionPlanFactorPrice(productId, version);
        //条款
        historyMapper.insertVersionProductClause(productId, version);
        //条款内容
        historyMapper.insertVersionProductClauseContent(productId, version);
        //团险相关
        historyMapper.insertVersionProductCoverage(productId, version);
        historyMapper.insertVersionProductCoverageAmount(productId, version);
        historyMapper.insertVersionProductCoverageDiscount(productId, version);
        historyMapper.insertVersionProductCoveragePremium(productId, version);
        //费率因子
        historyMapper.insertVersionProductFactorOptionals(productId, version);
        //限购
        historyMapper.insertVersionProductFormField(productId, version);
        historyMapper.insertVersionProductFormLimit(productId, version);
        //客户告知书配置
        historyMapper.insertVersionProductNotify(productId, version);
        historyMapper.insertVersionProductQuoteLimit(productId, version);
        //智能问卷
        historyMapper.insertVersionProductQuestionnaire(productId, version);
        //自主确认条款
        historyMapper.insertVersionProductConfirm(productId, version);
        //产品扩展属性
        historyMapper.insertVersionProductAttr(productId, version);
        /**
         * 加减费因子-责任因子
         */
        historyMapper.insertVersionDutyFactor(productId, oldVersion, version);
        /**
         * 加减费因子-时间因子
         */
        historyMapper.insertVersionTimeFactor(productId, oldVersion, version);
        /**
         * 健康告知-问答列表
         */
        historyMapper.insertVersionQuestion(productId, oldVersion, version);
    }

    public int defaultIfAbsent(int productId, Integer version) {

        return Optional.ofNullable(version).orElseGet(() -> getProductUpdateVersion(productId));
    }

    /**
     * newSmProductCoverageAmountItem
     *
     * @param productId
     * @param cv
     * @return
     */
    private SmProductCoverageAmountItemVO newSmProductCoverageAmountItem(int productId, SmProductCoverageVO cv) {
        SmProductCoverageAmountItemVO item = new SmProductCoverageAmountItemVO();
        item.setCoverageAmounts(new ArrayList<>());
        item.setProductId(productId);
        item.setCvgItemName(cv.getCvgItemName());
        item.setCvgNameDetail(cv.getCvgNameDetail());
        item.setCvgType(cv.getCvgType());
        item.setSpcId(cv.getSpcId());
        return item;
    }

    /**
     * newSmProductCoverageAmountItem
     *
     * @param productId
     * @param cv
     * @return
     */
    private PremiumItem createPremiumItem(int productId, SmProductCoverageVO cv) {
        PremiumItem item = new PremiumItem();
        item.setCoverageAmounts(new ArrayList<>());
        item.setProductId(productId);
        item.setCvgItemName(cv.getCvgItemName());
        item.setCvgType(cv.getCvgType());
        item.setSpcId(cv.getSpcId());
        return item;
    }

    /**
     * 查询产品保险价格选项
     *
     * @param id
     * @return
     */
    public SmPriceFactorOptionalsVO getProductPriceFactorOptionals(int id, Integer version) {
        List<SmFactorOptionalDTO> optionals = historyMapper.listFieldOptionals(id, defaultIfAbsent(id, version));
        SmPriceFactorOptionalsVO optionalsVo = new SmPriceFactorOptionalsVO();
        optionalsVo.setUnderWritingAge(getOptionalFieldDto(optionals, SmConstants.UNDER_WRITING_AGE));
        optionalsVo.setValidPeriod(getOptionalFieldDto(optionals, SmConstants.VALID_PERIOD));
        optionalsVo.setSex(getOptionalFieldDto(optionals, SmConstants.SEX));
        optionalsVo.setSocialSecurity(getOptionalFieldDto(optionals, SmConstants.SOCIAL_SECURITY));
        optionalsVo.setVehicleSeatNumber(getOptionalFieldDto(optionals, SmConstants.VEHICLE_SEAT_NUMBER));
        optionalsVo.setOccuplCategory(getOptionalFieldDto(optionals, SmConstants.OCCUPL_CATEGORY));
        optionalsVo.setProtonHeavyIonMedicine(getOptionalFieldDto(optionals, SmConstants.PHI_MEDICAINE));
        optionalsVo.setSpecifiedDiseaseSpecificCare(getOptionalFieldDto(optionals, SmConstants.SDS_CARE));
        optionalsVo.setJapanMedicalTreatment(getOptionalFieldDto(optionals, SmConstants.JP_MEDICAL));
        optionalsVo.setSmoke(getOptionalFieldDto(optionals, SmConstants.OPTIONS_SMOKE));
        return optionalsVo;
    }

    /**
     * 获取所有费率option
     *
     * @param id
     * @param version
     * @return
     */
    public List<SmFactorOptionalDTO> getProductPriceFactorOptionalsList(int id, Integer version) {
        return historyMapper.listFieldOptionals(id, defaultIfAbsent(id, version));
    }

    /**
     * @param id
     * @param version
     * @return
     */
    public Map<String, List<SmFactorOptionalDTO>> groupOptionalsByCode(int id, Integer version) {
        return LambdaUtils.groupBy(historyMapper.listFieldOptionals(id, defaultIfAbsent(id, version)), SmFactorOptionalDTO::getFieldCode);
    }

    /**
     * 查询产品价格因素字段code
     *
     * @param id
     * @return
     */
    public List<String> getProductPriceFactor(int id, Integer version) {
        SmProductDetailVO detailVo = getProductById(id, defaultIfAbsent(id, version));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(detailVo.getPriceCode())) {
            return JSONArray.parseArray(detailVo.getPriceCode(), String.class);
        }
        List<String> factors = new ArrayList<>();
        if (detailVo.getUnderWritingAgeSelect() != null && detailVo.getUnderWritingAgeSelect()) {
            factors.add(SmConstants.UNDER_WRITING_AGE);
        }
        if (detailVo.getValidPeriodSelect() != null && detailVo.getValidPeriodSelect()) {
            factors.add(SmConstants.VALID_PERIOD);
        }
        if (detailVo.getSocialSecuritySelect() != null && detailVo.getSocialSecuritySelect()) {
            factors.add(SmConstants.SOCIAL_SECURITY);
        }
        if (detailVo.getVehicleSeatNumberSelect() != null && detailVo.getVehicleSeatNumberSelect()) {
            factors.add(SmConstants.VEHICLE_SEAT_NUMBER);
        }
        if (detailVo.getSexSelect() != null && detailVo.getSexSelect()) {
            factors.add(SmConstants.SEX);
        }
        if (detailVo.getOccuplCategorySelect() != null && detailVo.getOccuplCategorySelect()) {
            factors.add(SmConstants.OCCUPL_CATEGORY);
        }
        if (detailVo.getProtonHeavyIonMedicineSelect() != null && detailVo.getProtonHeavyIonMedicineSelect()) {
            factors.add(SmConstants.PHI_MEDICAINE);
        }
        if (detailVo.getSpecifiedDiseaseSpecificCareSelect() != null && detailVo.getSpecifiedDiseaseSpecificCareSelect()) {
            factors.add(SmConstants.SDS_CARE);
        }
        if (detailVo.getJapanMedicalTreatmentSelect() != null && detailVo.getJapanMedicalTreatmentSelect()) {
            factors.add(SmConstants.JP_MEDICAL);
        }
        return factors;
    }

    public SmProductNotify getProductCustNotify(int productId, Integer version) {
        return notifyMapper.selectByProductId4His(productId, defaultIfAbsent(productId, version));
    }


    /**
     * 查询产品保险价格某个选项
     *
     * @param optionals
     * @param fieldCode
     * @return
     */
    private List<SmFactorOptionalDTO> getOptionalFieldDto(List<SmFactorOptionalDTO> optionals, String fieldCode) {
        return optionals.stream().filter(o -> Objects.equals(o.getFieldCode(), fieldCode)).collect(toList());
    }

    /**
     * 查询团险产品保费折扣比例
     *
     * @param productId
     * @return
     */
    public List<SmProductCoverageDiscountVO> getProductCoverageDiscountList(int productId, Integer version) {
        return historyMapper.listProductCoverageDiscounts(productId, defaultIfAbsent(productId, version));
    }


    /**
     * 查询产品投保信息录入列表
     *
     * @param productId
     * @return
     */
    public List<SmProductFormFieldCombDTO> getProductFormFields(int productId, Integer version) {
        int realVersion = defaultIfAbsent(productId, version);
        List<SmProductFormFieldVO> formFieldVos = historyMapper.listProductFormFields(productId, realVersion);
        if (formFieldVos.isEmpty()) {
            formFieldVos = defaultFormFields(productId, realVersion);
            if (!formFieldVos.isEmpty()) {
                List<SmProductFormFieldDTO> dtos = formFieldVos.stream().map(f -> {
                    f.setProductId(productId);
                    f.setModifyBy(HttpRequestUtil.getUserId());
                    return (SmProductFormFieldDTO) f;
                }).collect(toList());
                historyMapper.insertProductFormField(dtos, realVersion);
            }
        }
        Map<String, SmProductFormFieldCombDTO> combMap = new LinkedHashMap<>();

        formFieldVos.stream().forEach(vo -> {
            SmProductFormFieldCombDTO group = combMap.get(vo.getGroupCode());
            if (group == null) {
                group = new SmProductFormFieldCombDTO();
                group.setGroupCode(vo.getGroupCode());
                group.setGroupName(vo.getGroupName());
                group.setList(new ArrayList<>());
            }
            group.getList().add(vo);
            combMap.put(vo.getGroupCode(), group);
        });
        return new ArrayList<>(combMap.values());
    }

    /**
     * 获取默认的字段列表
     *
     * @param productId
     * @param realVersion
     * @return
     */
    public List<SmProductFormFieldVO> defaultFormFields(Integer productId, Integer realVersion) {
        SmProductDetailVO product = historyMapper.getProductById(productId, realVersion);
        int defaultProductId = SmConstants.DEFAULT_PRODUCT_ID;
        if (product != null) {
            if (!ObjectUtils.equals(product.getProductAttrCode(), SmConstants.PRODUCT_ATTR_PERSON)) {
                defaultProductId = SmConstants.DEFAULT_FIELD_GROUP_PRODUCT_ID;
            }
        }
        return mapper.listProductFormFields(defaultProductId);


    }

    /**
     * 团险费率模板
     *
     * @param productId
     * @param version
     * @param resp
     */
    public void downloadPremiumTableTemplate4Group(int productId, Integer version, SmProductDetailVO productDetail, HttpServletResponse resp) {
        version = defaultIfAbsent(productId, version);
        List<PremiumTableVo> tables = getPremiumTable(productId, version);
        ExcelBuilderUtil excelBuilderUtil = ExcelBuilderUtil.newInstance().createSheet("费率表");
        Sheet sheet = excelBuilderUtil.getSheet();
        Workbook workbook = excelBuilderUtil.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        String ocs = productDetail.getGlOcpnGroup();
        buildHeader(workbook, sheet, ocs);

        int rowNo = 1;
        int cvgIndex = 1;
        int planIndex = 1;
        for (PremiumTableVo vo : tables) {
            int planNum = 0;
            for (PremiumItem pi : vo.getCoverages()) {
                int cvgNum = 0;
                for (SmProductCoverageAmountVO a : pi.getCoverageAmounts()) {
                    Row row = sheet.createRow(rowNo);
                    Cell cell = createCell(row, 0, cellStyle);
                    cell.setCellValue(vo.getPlanName());
                    cell = createCell(row, 1, cellStyle);
                    cell.setCellValue(pi.getRiskCode());
                    cell = createCell(row, 2, cellStyle);
                    cell.setCellValue(pi.getCvgItemName());
                    cell = createCell(row, 3, cellStyle);
                    cell.setCellValue(pi.getCvgCode());
                    cell = createCell(row, 4, cellStyle);
                    cell.setCellValue(a.getCvgNotice());
                    if (org.apache.commons.lang.StringUtils.isNotBlank(ocs)) {
                        String[] ocsList = ocs.split(",");
                        int i = 5;
                        for (String o : ocsList) {
                            cell = createCell(row, i, cellStyle);
                            cell.setCellValue("");
                            i++;
                        }
                    }
                    rowNo++;
                    cvgNum++;
                    planNum++;
                }
                cvgNum--;
                if (cvgNum > 0) {
                    CellRangeAddress region = new CellRangeAddress(cvgIndex, cvgIndex + cvgNum, 1, 1);
                    sheet.addMergedRegion(region);
                    region = new CellRangeAddress(cvgIndex, cvgIndex + cvgNum, 2, 2);
                    sheet.addMergedRegion(region);
                    region = new CellRangeAddress(cvgIndex, cvgIndex + cvgNum, 3, 3);
                    sheet.addMergedRegion(region);
                    cvgIndex = cvgIndex + cvgNum + 1;
                }
            }
            planNum--;
            if (planNum > 0) {
                CellRangeAddress region = new CellRangeAddress(planIndex, planIndex + planNum, 0, 0);
                sheet.addMergedRegion(region);
                planIndex = planIndex + planNum + 1;
            }
        }
        sheet.setColumnWidth(0, 256 * 20);
        sheet.setColumnWidth(1, 256 * 10);
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(productDetail.getProductName() + "保障项目费率表模板", StandardCharsets.UTF_8.name()) + ".xlsx");
            resp.setContentType("application/octet-stream");
            excelBuilderUtil.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    private void buildHeader(Workbook workbook, Sheet sheet, String ocs) {
        CellStyle cellStyle = workbook.createCellStyle();
        Row header = sheet.createRow(0);
        Cell cell = createCell(header, 0, cellStyle);
        cell.setCellValue("保障计划");
        cell = createCell(header, 1, cellStyle);
        cell.setCellValue("险种代码");
        cell = createCell(header, 2, cellStyle);
        cell.setCellValue("保障责任");
        cell = createCell(header, 3, cellStyle);
        cell.setCellValue("责任编码");
        cell = createCell(header, 4, cellStyle);
        cell.setCellValue("保额");
        int i = 5;
        if (org.apache.commons.lang.StringUtils.isNotBlank(ocs)) {
            String[] ocsList = ocs.split(",");
            for (String o : ocsList) {
                cell = createCell(header, i, cellStyle);
                cell.setCellValue(o + "类");
                i++;
            }
        }
    }

    /**
     * 品保障项目保费模板下载
     *
     * @param productId
     * @return
     */
    public void downloadPremiumTableTemplate(int productId, Integer version, HttpServletResponse resp) {
        int realVersion = defaultIfAbsent(productId, version);
        SmProductDetailVO productDetail = historyMapper.getProductById(productId, realVersion);
        if (ProductConfig.groupTemplate(productDetail.getProductAttrCode())) {
            downloadPremiumTableTemplate4Group(productId, version, productDetail, resp);
        } else {
            downloadProductCoveragePremiumTemplate(productId, version, productDetail, resp);
        }
    }

    /**
     * 品保障项目保费模板下载
     *
     * @param productId
     * @return
     */
    public void downloadProductCoveragePremiumTemplate(int productId, Integer version, SmProductDetailVO productDetail, HttpServletResponse resp) {
        int realVersion = defaultIfAbsent(productId, version);
        List<SmProductCoverageAmountItemVO> coverageAmountItems = getProductCoverageAmountList(productId, realVersion);

        String glOcpnGroup = productDetail.getGlOcpnGroup();
        if (StringUtils.isEmpty(glOcpnGroup)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品职业配置错误");
        }
        String[] ocpnGroups = glOcpnGroup.split(",");
        if (ocpnGroups.length == 0) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品职业配置错误");
        }
        ocpnGroups = Stream.of(ocpnGroups).sorted().toArray(String[]::new);
        ExcelBuilderUtil excelBuilderUtil = ExcelBuilderUtil.newInstance().createSheet("费率表");
        Sheet sheet = excelBuilderUtil.getSheet();
        Workbook workbook = excelBuilderUtil.getWorkbook();
        // 字体加粗
        CellStyle cellStyle = workbook.createCellStyle();

        int rowNo = 0;
        for (SmProductCoverageAmountItemVO ca : coverageAmountItems) {
            Row row = sheet.createRow(rowNo);
            Cell cell = createCell(row, 0, cellStyle);
            cell.setCellValue("保障项目");
            cell = createCell(row, 1, cellStyle);
            cell.setCellValue("保额");
            for (int i = 0, len = ocpnGroups.length; i < len; i++) {
                cell = createCell(row, i + 2, cellStyle);
                cell.setCellValue(ocpnGroups[i] + "类");
            }
            rowNo++;
            row = sheet.createRow(rowNo);
            cell = createCell(row, 0, cellStyle);
            cell.setCellValue(ca.getCvgItemName());

            List<SmProductCoverageAmountVO> coverageAmounts = ca.getCoverageAmounts();
            if (!coverageAmounts.isEmpty()) {
                cell = createCell(row, 1, cellStyle);
                cell.setCellValue(coverageAmounts.get(0).getCvgNotice());
            }
            rowNo++;
            for (int i = 1, len = coverageAmounts.size(); i < len; i++) {
                row = sheet.createRow(rowNo);
                cell = createCell(row, 0, cellStyle);
                cell.setCellValue("");
                cell = createCell(row, 1, cellStyle);
                cell.setCellValue(coverageAmounts.get(i).getCvgNotice());
                rowNo++;
            }
            row = sheet.createRow(rowNo);
            cell = createCell(row, 0, cellStyle);
            cell.setCellValue("");
            rowNo++;
        }
        sheet.setColumnWidth(0, 256 * 20);
        sheet.setColumnWidth(1, 256 * 10);
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(productDetail.getProductName() + "保障项目费率表模板", StandardCharsets.UTF_8.name()) + ".xlsx");
            resp.setContentType("application/octet-stream");
            excelBuilderUtil.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    /**
     * 加粗字体单元格
     *
     * @param row
     * @param i
     * @param cellStyle
     * @return
     */
    private Cell createCell(Row row, int i, CellStyle cellStyle) {
        Cell cell = row.createCell(i);
        cell.setCellStyle(cellStyle);
        return cell;
    }


    /**
     * 查询团险产品报价限制
     *
     * @param productId
     * @return
     */
    public SmProductQuoteLimitVO getProductQuoteLimit(int productId, Integer version) {

        int realVersion = defaultIfAbsent(productId, version);
        List<SmProductQuoteLimitItemVO> quoteLimits = historyMapper.listProductQuoteLimit(productId, null, realVersion);
        SmProductQuoteLimitVO quoteLimitVO = new SmProductQuoteLimitVO();

        quoteLimitVO.setOcpnPerLimit(new ArrayList<>());
        quoteLimitVO.setCvgRelationLimits(new ArrayList<>());
        quoteLimitVO.setCvgAmountLimits(new ArrayList<>());
        quoteLimitVO.setOcp4LiabAmountLimits(new ArrayList<>());
        quoteLimitVO.setCvgSupportTimeLimits(new ArrayList<>());

        quoteLimits.forEach(q -> {
            if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER)) {

                SmProductQuoteLimitDTO.OcpnPerLimit limit = new SmProductQuoteLimitDTO.OcpnPerLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getOcpnPerLimit().add(limit);

            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY)) {

                SmProductQuoteLimitDTO.CvgRelationLimit limit = new SmProductQuoteLimitDTO.CvgRelationLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getCvgRelationLimits().add(limit);

            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT)) {

                SmProductQuoteLimitDTO.CvgAmountLimit limit = new SmProductQuoteLimitDTO.CvgAmountLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getCvgAmountLimits().add(limit);

            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT_2)) {

                SmProductQuoteLimitDTO.Ocp4LiabAmountLimit limit = new SmProductQuoteLimitDTO.Ocp4LiabAmountLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getOcp4LiabAmountLimits().add(limit);

            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_PER)) {

                quoteLimitVO.setSpqlId(q.getSpqlId());
                quoteLimitVO.setGlPerStartQty(q.getMinPerQty());

            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_TIME)) {

                SmProductQuoteLimitDTO.CvgSupportTimeLimit limit = new SmProductQuoteLimitDTO.CvgSupportTimeLimit();
                limit.setOperation(q.getOperation());
                limit.setSupportTime(q.getSupportTime());
                limit.setOccupationGroup(q.getOccupationGroup());
                limit.setSpqlId(q.getSpqlId());
                limit.setUnit(q.getUnit());
                quoteLimitVO.getCvgSupportTimeLimits().add(limit);

            }
        });
        return quoteLimitVO;
    }

    /**
     * 查询所有产品计划列表
     *
     * @return
     */
    public List<SmPlanVO> getProductPlanList() {
        return historyMapper.listProductPlansById(null, true, null);
    }

    /**
     * 查询所有产品计划列表
     *
     * @return
     */
    public List<SmPlanVO> getOnlineProductPlansList() {
        return mapper.listProductPlansById(null, true);
    }

    /**
     * 分页查询所有产品计划列表
     *
     * @return
     */
    public PageInfo<SmPlanVO> getProductPlanListPage(SmPlanPageQuery query) {

        PageInfo<SmPlanVO> objectPageInfo = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> historyMapper.listProductPlans(query));
        return objectPageInfo;

    }

    /**
     * 通过计划Id查询计划详情信息
     *
     * @param planId
     * @return
     */
    public SmPlanVO getPlanById(int planId, int version) {
        return historyMapper.getPlanById(planId, version);
    }

    /**
     * 微信查询产品所有计划选项
     *
     * @param id 计划id
     * @return
     */
    public SmPriceFactorOptionalsVO getPlanPriceFactorOptions(int id, int version) {
        SmPlanFactorQuery query = new SmPlanFactorQuery();
        query.setPlanId(id + "");
        query.setAvailable(Boolean.TRUE);
        query.setVersion(version);
        List<SmPlanFactorPriceDT> planSpecs = historyMapper.getPlanSpecPrice(query);
        List<Integer> underWritingAgeOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getUnderWritingAgeOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> occuplCategoryOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getOccuplCategoryOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> socialSecurityOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getSocialSecurityOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> sexOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getSexOptional).filter(Objects::nonNull)
                .collect(toList());
        List<Integer> validPeriodOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getValidPeriodOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> vehicleSeatNumberOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getVehicleSeatNumberOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> protonHeavyIonMedicineOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getProtonHeavyIonMedicineOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> specifiedDiseaseSpecificCareOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getSpecifiedDiseaseSpecificCareOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> japanMedicalTreatmentOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getJapanMedicalTreatmentOptional)
                .filter(Objects::nonNull).collect(toList());

        SmPlanVO planVo = getPlanById(id, version);
        List<SmFactorOptionalDTO> optionals = historyMapper.listFieldOptionals(planVo.getProductId(), version);
        SmPriceFactorOptionalsVO optionalsVo = new SmPriceFactorOptionalsVO();
        List<SmFactorOptionalDTO> underWritingAgeDtos = getOptionalFieldDto(optionals, SmConstants.UNDER_WRITING_AGE).stream()
                .filter(u -> underWritingAgeOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!underWritingAgeDtos.isEmpty()) {
            optionalsVo.setUnderWritingAge(underWritingAgeDtos);
        }
        List<SmFactorOptionalDTO> validPeriodDtos = getOptionalFieldDto(optionals, SmConstants.VALID_PERIOD).stream()
                .filter(u -> validPeriodOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!validPeriodDtos.isEmpty()) {
            optionalsVo.setValidPeriod(validPeriodDtos);
        }
        List<SmFactorOptionalDTO> sexDtos = getOptionalFieldDto(optionals, SmConstants.SEX).stream()
                .filter(u -> sexOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!sexDtos.isEmpty()) {
            optionalsVo.setSex(sexDtos);
        }
        List<SmFactorOptionalDTO> socialSecurityDtos = getOptionalFieldDto(optionals, SmConstants.SOCIAL_SECURITY).stream()
                .filter(u -> socialSecurityOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!socialSecurityDtos.isEmpty()) {
            optionalsVo.setSocialSecurity(socialSecurityDtos);
        }
        List<SmFactorOptionalDTO> vehicleSeatNumberDtos = getOptionalFieldDto(optionals, SmConstants.VEHICLE_SEAT_NUMBER).stream()
                .filter(u -> vehicleSeatNumberOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!vehicleSeatNumberDtos.isEmpty()) {
            optionalsVo.setVehicleSeatNumber(vehicleSeatNumberDtos);
        }
        List<SmFactorOptionalDTO> occupationDtos = getOptionalFieldDto(optionals, SmConstants.OCCUPL_CATEGORY).stream()
                .filter(u -> occuplCategoryOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!occupationDtos.isEmpty()) {
            optionalsVo.setOccuplCategory(occupationDtos);
        }
        List<SmFactorOptionalDTO> protonHeavyIonMedicines = getOptionalFieldDto(optionals, SmConstants.PHI_MEDICAINE).stream()
                .filter(u -> protonHeavyIonMedicineOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!protonHeavyIonMedicines.isEmpty()) {
            optionalsVo.setProtonHeavyIonMedicine(protonHeavyIonMedicines);
        }
        List<SmFactorOptionalDTO> specifiedDiseaseSpecificCares = getOptionalFieldDto(optionals, SmConstants.SDS_CARE).stream()
                .filter(u -> specifiedDiseaseSpecificCareOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!specifiedDiseaseSpecificCares.isEmpty()) {
            optionalsVo.setSpecifiedDiseaseSpecificCare(specifiedDiseaseSpecificCares);
        }
        List<SmFactorOptionalDTO> japanMedicalTreatments = getOptionalFieldDto(optionals, SmConstants.JP_MEDICAL).stream()
                .filter(u -> japanMedicalTreatmentOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!japanMedicalTreatments.isEmpty()) {
            optionalsVo.setJapanMedicalTreatment(japanMedicalTreatments);
        }
        return optionalsVo;
    }

    /**
     * 查询 某个时间点的版本号
     *
     * @param productId
     * @param submitTime
     * @return
     */
    public int getProductVersion(Integer productId, String submitTime) {
        return productVersionMapper.selectVersionBySubTime(productId, submitTime);
    }

    /**
     * 查询快照 自主确认条款
     *
     * @param productId
     * @param version
     * @return
     */
    public List<SmProductConfirmHistory> getClauseConfirm(int productId, Integer version) {
        int queryVersion = defaultIfAbsent(productId, version);
        SmProductConfirmHistory queryModel = new SmProductConfirmHistory();
        queryModel.setVersion(queryVersion);
        queryModel.setProductId(productId);
        List<SmProductConfirmHistory> select = confirmHistoryMapper.select(queryModel);
        select.forEach(a -> a.setId(a.getSpcId()));
        select.sort(Comparator.comparing(SmProductConfirmHistory::getDisplaySort));
        return select;
    }

    public SmProductAttrHistory getProductAttr(Integer productId, String code, Integer version) {
        int curVersion = defaultIfAbsent(productId, version);
        SmProductAttrHistory param = new SmProductAttrHistory();
        param.setProductId(productId);
        param.setAttrCode(code);
        param.setVersion(curVersion);
        return attrHistoryMapper.selectOne(param);
    }

    /**
     * 同一个产品的每一个属性只能配置一次
     * 2023-05-10：增加数据库约束
     *
     * @param productId
     * @param attr
     */
    public int saveProductAttr(int productId, SmProductAttrDTO attr) {
        int curVersion = getProductUpdateVersion(productId);

        String attrCode = attr.getAttrCode();
        String attrValue = attr.getAttrVal();

        SmProductAttrHistory history = new SmProductAttrHistory();
        history.setProductId(productId);
        history.setAttrCode(attrCode);
        history.setAttrVal(attrValue);
        history.setVersion(curVersion);
        int i = attrHistoryMapper.saveOrUpdate(history);
        log.info("保存产品属性:{},{},{}", productId, attrCode, i);
        return i;
    }

    /**
     * 获取费率因子
     *
     * @param productId
     * @param version
     * @return
     */
    public PremiumFlowFactorVo getPremiumFactor(int productId, Integer version) {
        version = defaultIfAbsent(productId, version);
        PremiumFlowFactorVo vo = new PremiumFlowFactorVo();
        vo.setFlowType("RATIO");
        vo.setCalType("1");
        version = defaultIfAbsent(productId, version);

        /**
         * 产品属性-数据精度
         */
        Map<String, String> accMap = new HashMap<>();
        List<ProductAttrDTO> accList = historyMapper.listProductAttr(productId, version);
        if (!CollectionUtils.isEmpty(accList)) {
            accMap = LambdaUtils.toMap(accList, ProductAttrDTO::getAttrCode, ProductAttrDTO::getAttrVal);
        }

        /**
         * 人数浮动因子
         */
        List<SmProductCoverageDiscountVO> discount = historyMapper.listProductCoverageDiscounts(productId, version);
        String acc = accMap.get(EnumProductAttr.PREMIUM_PEOPLE_FACTOR_ACC.getCode());
        SmProductCoverageDiscountWrap warp1 = new SmProductCoverageDiscountWrap();
        warp1.setAccuracy(acc);
        warp1.setItems(discount);
        vo.setInsuredQtyF(warp1);

        /**
         * 责任因子
         */
        SmProductCoverageDiscountWrap warp2 = new SmProductCoverageDiscountWrap();
        String acc2 = accMap.get(EnumProductAttr.PREMIUM_DUTY_FACTOR_ACC.getCode());
        warp2.setAccuracy(acc2);
        warp2.setItems(convertBean(historyMapper.getDutyFactors(productId, version)));
        vo.setDutyFactor(warp2);

        /**
         * 短期因子
         */
        SmProductCoverageDiscountWrap warp3 = new SmProductCoverageDiscountWrap();
        String acc3 = accMap.get(EnumProductAttr.PREMIUM_SHORT_TERM_ACC.getCode());
        warp3.setAccuracy(acc3);
        warp3.setItems(mapper.queryPremiumFlow(productId, EnumPremiumFlow.SHORT_TERM_MONTH_FACTOR.name(), version));
        vo.setTimeFactor(warp3);

        /**
         * 企业风险系数因子
         */
        SmProductCoverageDiscountWrap warp4 = new SmProductCoverageDiscountWrap();
        String acc4 = accMap.get(EnumProductAttr.PREMIUM_ENTERPRISE_RISK_ACC.getCode());
        warp4.setAccuracy(acc4);
        warp4.setItems(mapper.queryPremiumFlow(productId, EnumPremiumFlow.ENTERPRISE_RISK_FACTOR.name(), version));
        vo.setEnterpriseRiskFactor(warp4);

        return vo;
    }

    private Collection<DutyFactorFlowVo> convertBean(List<DutyFactorDTO> data) {
        Map<String, DutyFactorFlowVo> mapper = new HashMap<>();
        data.forEach(d -> {
            String key = d.getDutyCode() + d.getDutyFactorCode();
            DutyFactorFlowVo.DutyFactorField f = new DutyFactorFlowVo.DutyFactorField();
            f.setFactorName(d.getFactorName());
            f.setFactorValue(d.getFactorValue());
            f.setOptionName(d.getOptionName());
            f.setFlow(d.getFlow());
            f.setIsDefault(d.getIsDefault());
            if (mapper.containsKey(key)) {
                mapper.get(key).addItem(f);
            } else {
                DutyFactorFlowVo vo = new DutyFactorFlowVo();
                BeanUtils.copyProperties(d, vo);
                vo.addItem(f);
                mapper.put(key, vo);
            }
        });
        return mapper.values();
    }

    public List<SysDutyConfigDTO> listDuty(Integer productId, Integer type, Integer version) {
        version = defaultIfAbsent(productId, version);
        List<SysDutyConfigDTO> rs = new ArrayList<>();
        if (type == 0) {
            List<SmProductCoverageVO> coverages = historyMapper.listProductCoverages(productId, version);
            coverages.forEach(c -> {
                SysDutyConfigDTO config = new SysDutyConfigDTO();
                config.setType(0);
                config.setCode(c.getCvgCode());
                config.setSpcId(c.getSpcId());
                config.setName(c.getCvgItemName());
                rs.add(config);
            });
            return rs;
        } else if (type == 1) {
            SysDutyConfig param = new SysDutyConfig();
            param.setType(type);
            param.setStatus(0);
            List<SysDutyConfig> cfgs = dutyConfigMapper.select(param);
            if (cfgs != null) {
                cfgs.forEach(c -> {
                    SysDutyConfigDTO config = new SysDutyConfigDTO();
                    config.setCode(c.getCode());
                    config.setName(c.getName());
                    config.setType(c.getType());
                    rs.add(config);
                });
            }
            return rs;
        }
        throw new RuntimeException("非法參數");
    }

    public void deleteProductPlan(int productId, int planId) {
        String userId = HttpRequestUtil.getUserId();
        int version = defaultIfAbsent(productId, null);
        historyMapper.deleteProductPlan(planId, userId, version);
    }

    /**
     * 从回溯表中查询产品属性
     *
     * @param productId
     * @param attrCode
     * @return
     */
    public List<ProductAttrVo> listProductAttr(Integer productId, String attrCode, Integer version) {
        if (version == null) {
            version = getProductUpdateVersion(productId);
        }
        List<SmProductAttrHistory> productAttrList = attrHistoryMapper.listProductAttr(productId, attrCode, version);
        if (CollectionUtils.isEmpty(productAttrList)) {
            return Collections.emptyList();
        }
        return productAttrList.stream().map(attr -> {
            ProductAttrVo vo = new ProductAttrVo();
            vo.setId(attr.getId());
            vo.setProductId(attr.getProductId());
            vo.setAttrCode(attr.getAttrCode());
            vo.setAttrVal(attr.getAttrVal());
            return vo;
        }).collect(toList());
    }

}

