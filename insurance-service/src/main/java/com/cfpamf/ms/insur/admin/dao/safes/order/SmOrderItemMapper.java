package com.cfpamf.ms.insur.admin.dao.safes.order;

import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderItemDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.po.order.correct.CorrectOrderPo;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
@Mapper
public interface SmOrderItemMapper extends MyMappler<SmOrderItem> {

    /**
     * 查询某个订单的所有数据
     *
     * @param fhOrderId
     * @return
     */
    default List<SmOrderItem> selectByOrderId(String fhOrderId) {
        SmOrderItem query = new SmOrderItem();
        query.setFhOrderId(fhOrderId);
        return select(query);
    }

    /**
     * 查询某个订单的所有数据
     *
     * @param fhOrderId
     * @return
     */
    default List<SmOrderItem> listEnabledByOrderId(String fhOrderId) {
        SmOrderItem query = new SmOrderItem();
        query.setFhOrderId(fhOrderId);
        query.setEnabledFlag(0);
        return select(query);
    }

    /**
     * 根据订单号以及证件号查询第一行数据
     *
     * @param fhOrderId
     * @param idNumber
     * @return
     */
    default SmOrderItem selectByOrderIdAndIdNumber(String fhOrderId, String idNumber) {
        SmOrderItem query = new SmOrderItem();
        query.setFhOrderId(fhOrderId);
        query.setIdNumber(idNumber);
        PageHelper.startPage(1, 1, false);
        return selectOne(query);
    }

    default List<SmOrderItem> listOriginalPolicyByOrderId(String fhOrderId, List<String> idNumberList) {
        Example e = new Example(SmOrderItem.class);
        e.createCriteria().andLike("fhOrderId", fhOrderId + "%")
                .andEqualTo("appStatus", "1")
                .andIn("idNumber", idNumberList);
        e.orderBy("id").desc();

        return this.selectByExample(e);
    }

    /**
     * 查询保单分单列表
     *
     * @param orderId
     * @param idNumberList
     * @return
     */
    default List<SmOrderItem> listPolicyItem(String orderId, List<String> idNumberList) {
        Example e = new Example(SmOrderItem.class);
        e.createCriteria()
                .andLike("fhOrderId", orderId + "%")
                .andIn("appStatus", Arrays.asList("1", "4"))
                .andIn("idNumber", idNumberList)
                .andEqualTo("enabledFlag",0);
        e.orderBy("id");
        return this.selectByExample(e);
    }

    default List<SmOrderItem> listItemByPolicyNo(String policyNo, List<String> idNumberList) {
        if(CollectionUtils.isEmpty(idNumberList)){
            return Collections.emptyList();
        }
        Example e = new Example(SmOrderItem.class);
        e.createCriteria()
                .andEqualTo("thPolicyNo", policyNo)
                .andIn("appStatus", Arrays.asList("1", "4"))
                .andIn("idNumber", idNumberList)
                .andEqualTo("enabledFlag",0);
        e.orderBy("id");
        return this.selectByExample(e);
    }

    List<SmOrderItem> listSomeItemByEndorsementList(@Param("policyNo") String policyNo, @Param("endorsementNoList") Collection<String> endorsementNoList,@Param("idCardList") Collection<String> idCardList) ;

    List<SmOrderItem> listSomeItemByBranchIdList(@Param("policyNo") String policyNo, @Param("endorsementNoList") Collection<String> endorsementNoList,@Param("branchIdList") Collection<String> branchIdList) ;


    default List<SmOrderItem> listOriginalPolicyByBranchId(String fhOrderId, List<String> branchIds) {
        Example e = new Example(SmOrderItem.class);
        e.createCriteria().andLike("fhOrderId", fhOrderId + "%")
                .andIn("branchId", branchIds);
        e.orderBy("id").desc();

        return this.selectByExample(e);
    }

    int insertOrderItemBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    int insertOrderItemEntityList(@Param("entityList") List<SmOrderItem> itemList);

    /**
     * 承保后更新分单表数据
     *
     * @param orderId
     * @param policyNo
     * @param status
     * @return
     */
    @Update("update sm_order_item set policy_no=#{policyNo},th_policy_no=#{policyNo},app_status=#{status} where fh_order_id=#{orderId}")
    int batchUpdateIns(@Param("orderId") String orderId, @Param("policyNo") String policyNo, @Param("status") String status);

    /**
     * 更新item退保状态
     *
     * @param orderId
     * @param idNumbers
     */
    void updateOrderItemSurrenderTimeBatch(@Param("orderId") String orderId, @Param("idNumbers") List<String> idNumbers);

    /**
     * 更新item信息
     *
     * @param orderItemList
     */
    int updateOrderItemBatch(@Param("orderItemList") List<SmOrderItem> orderItemList);

    /**
     * 根据保单号修改退保状态
     *
     * @param policyNo
     * @param idNumbers
     * @return
     */
    int updateOrderItemSurrenderTimeByPolicyNo(@Param("policyNo") String policyNo, @Param("idNumbers") List<String> idNumbers);

    /**
     * 修改提层id
     *
     * @param orderId
     * @param commissionId
     * @return
     */
    int updateCommission(@Param("orderId") String orderId, @Param("idNumber") String idNumber, @Param("commissionId") Integer commissionId);

    /**
     * 修改保单状态
     *
     * @param orderId
     * @return
     */
    int updateOrderPolicyInfo(@Param("orderId") String orderId, @Param("idNumber") String idNumber, @Param("policyInfo") OrderQueryResponse.PolicyInfo policyInfo);

    /**
     * 修改退保时间
     *
     * @param fhOrderId
     * @param idNumber
     * @return
     */
    int updateOrderSurrenderTimeSimple(@Param("orderId") String fhOrderId, @Param("idNumber") String idNumber);

    /**
     * --
     *
     * @param fhOrderIdList
     * @return
     */
    List<SmOrderItemDTO> listOrderItemByFhOrderIds(@Param("fhOrderIdList") List<String> fhOrderIdList);

    /**
     * 批量更新被保人证件号
     *
     * @param correctList
     * @return
     */
    Integer batchCorrectIdNumber(@Param("data") List<CorrectOrderPo> correctList);

    /**
     * 更改sm_order_item 身份证号
     *
     * @param fhOrderId
     * @param oldIdNumber
     * @param newIdNumber
     * @return
     */
    default Integer updateIdNumber(String fhOrderId, String oldIdNumber, String newIdNumber) {
        Example e = new Example(SmOrderItem.class);
        e.createCriteria().andEqualTo("fhOrderId", fhOrderId)
                .andEqualTo("idNumber", oldIdNumber);

        SmOrderItem update = new SmOrderItem();
        update.setIdNumber(newIdNumber);

        return updateByExampleSelective(update, e);
    }

    /**
     * 更改sm_order_item 身份证号
     *
     * @param fhOrderId
     * @param oldIdNumber
     * @param newIdNumber
     * @param branchId
     * @return
     */
    default Integer updateIdNumberAndBranchId(String fhOrderId, String oldIdNumber, String newIdNumber, String branchId) {
        Example e = new Example(SmOrderItem.class);
        e.createCriteria().andEqualTo("fhOrderId", fhOrderId)
                .andEqualTo("idNumber", oldIdNumber).andEqualTo("branchId", branchId);

        SmOrderItem update = new SmOrderItem();
        update.setIdNumber(newIdNumber);

        return updateByExampleSelective(update, e);
    }

    @Delete("delete from sm_order_item where fh_order_id=#{orderId} ")
    int deleteByFhOrderId(@Param("orderId") String orderId);

    /**
     * 根据保单号查询item表信息
     *
     * @param policyNoList
     * @return
     */
    default List<SmOrderItem> listByPolicyList(List<String> policyNoList) {
        Example e = new Example(SmOrderItem.class);
        e.createCriteria().andIn("policyNo", policyNoList);
        return selectByExample(e);
    }

    /**
     * 变更保单号
     *
     * @param sourcePolicyNo
     * @param toPolicyNo
     * @return
     */
    @Update(" UPDATE sm_order_item set th_policy_no=#{toPolicyNo},policy_no=#{toPolicyNo} where policy_no=#{sourcePolicyNo} ")
    int updatePolicyNo(@Param("sourcePolicyNo") String sourcePolicyNo, @Param("toPolicyNo") String toPolicyNo);


    /**
     * 根据保单号查询item表信息
     *
     * @param thPolicyNos
     * @return
     */
    List<SmOrderItem> queryThPolicyNo(@Param("thPolicyNos") List<String> thPolicyNos);

    int countEndorsementNo(@Param("policyNo") String policyNo,@Param("endorsementNo") String endorsementNo);

    int countPolicyNo(@Param("policyNo") String policyNo);

    int logicDel(@Param("id") Integer id);

    @Update("update sm_order_item set enabled_flag=-1,update_time=now() where fh_order_id = #{orderId} and enabled_flag=0;")
    int logicDeleteByOrderId(@Param("orderId") String orderId);

    /**
     * 根据批改单号查询item表信息
     *
     * @param endorsementNos
     * @return
     */
    default List<SmOrderItem> selectByEndorsementNoList(List<String> endorsementNos){
        if (CollectionUtils.isEmpty(endorsementNos)) {
            return Collections.emptyList();
        }
        Example example = new Example(SmOrderItem.class);
        example.createCriteria().andIn("thEndorsementNo", endorsementNos)
                .andEqualTo("enabledFlag",0);
        return selectByExample(example);
    }

    default List<SmOrderItem> selectByEndorsementNo(String policyNo,String endorsementNo){
        if (StringUtils.isBlank(endorsementNo)) {
            return Collections.emptyList();
        }
        Example example = new Example(SmOrderItem.class);
        example.createCriteria().andEqualTo("thEndorsementNo", endorsementNo)
                .andEqualTo("thPolicyNo", policyNo)
                .andEqualTo("enabledFlag",0);
        return selectByExample(example);
    }

    /**
     * 根据保单号查询新契约item表信息
     *
     * @param policyNo
     * @return
     */
    List<SmOrderItem> qryNewItemByPolicyNo(@Param("policyNo") String policyNo);

    int changePlan(@Param("orderId") String orderId, @Param("plan")SmPlanVO planVO);

    default void updateItemAppStatus(String orderId, String idNumber,String status) {
        SmOrderItem updateParam = new SmOrderItem();
        updateParam.setEnabledFlag(null);
        updateParam.setAppStatus(status);
        Example example = new Example(SmOrderItem.class);
        example.createCriteria()
                .andEqualTo("idNumber", idNumber)
                .andEqualTo("fhOrderId", orderId);
        updateByExampleSelective(updateParam, example);
    }

}
