package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.distribution.dto.InsuranceVillageAgentInvalidOrderDTO;
import com.cfpamf.ms.distribution.dto.InsuranceVillageAgentOrderDTO;
import com.cfpamf.ms.distribution.dto.InsuranceVillageAgentRefundOrderDTO;
import com.cfpamf.ms.distribution.dto.OrderCommissionDTO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.distribution.config.dao.ProductDistributionConfigMapper;
import com.cfpamf.ms.insur.admin.distribution.config.entity.ProductDistributionConfig;
import com.cfpamf.ms.insur.admin.enums.EnumInsuredAppStatus;
import com.cfpamf.ms.insur.admin.enums.order.EnumDistributionPushState;
import com.cfpamf.ms.insur.admin.enums.order.EnumDistributionState;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.external.whale.enums.EnumWhalePolicyStatus;
import com.cfpamf.ms.insur.admin.pojo.po.order.*;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.activitie.SmOrderDistributionCorrectVo;
import com.cfpamf.ms.insur.admin.pojo.vo.order.activitie.SmOrderVillageActivityFourVo;
import com.cfpamf.ms.insur.admin.renewal.vo.OrderDistribution4CommissionListVo;
import com.cfpamf.ms.insur.admin.renewal.vo.OrderDistribution4CommissionVo;
import com.cfpamf.ms.insur.admin.service.DistributionProxyService;
import com.cfpamf.ms.insur.admin.service.commission.CommissionQueryService;
import com.cfpamf.ms.insur.admin.settlement.service.WhaleManagerInnerService;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.facade.dto.dis.*;
import com.cfpamf.ms.insur.facade.dto.whale.ZhSettlementCostInfo;
import com.cfpamf.ms.insur.weixin.pojo.query.DistributionCommissionQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderInsuredBaseInfoVo;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Function;


/**
 * 4级分销
 *
 * <AUTHOR> 2021/5/25 10:28
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmOrderDistribution4Service {

    static final int DIST_API_REQUEST_MAX_SIZE = 50;
    SmOrderMapper mapper;

    SmOrderInsuredMapper orderInsuredMapper;

    SmOrderDistributionMapper distributionMapper;

    CommissionQueryService commissionQueryService;

    DistributionProxyService proxyService;

    SmOrderDistributionBatchPushPushMapper distributionBatchPushPushMapper;


    ProductDistributionConfigMapper productDistributionConfigMapper;

    SmOrderDistributionSettlementCostMapper smOrderDistributionSettlementCostMapper;

    WhaleManagerInnerService whaleManagerInnerService;

    SmOrderDistributionCompensateMapper smOrderDistributionCompensateMapper;

    SmOrderDistributionPushMapper smOrderDistributionPushMapper;

    SmOrderDistributionSettlementMapper smOrderDistributionSettlementMapper;

    static final String idNumber = "999bx0019999999999";

    /**
     * 分配结算
     * @param start 开始日期
     * @param end 结束日期
     */
    public void distributionSettlement(LocalDate start, LocalDate end) {

        String batchNo = start.toString();

        // 插入上个月的所有订单
        distributionBatchPushPushMapper.insertLastMonthAllOrder(start, end);

        // 查询状态为初始化或错误的所有推送批次
        List<SmOrderDistributionBatchPush> pushes = distributionBatchPushPushMapper.selectByState(Arrays.asList(EnumDistributionPushState.INIT.getCode(),
                EnumDistributionPushState.ERROR.getCode()));

        // 根据推送状态是否为取消成功，将推送批次分为两个列表
        Map<Boolean, List<SmOrderDistributionBatchPush>> map =
                pushes.stream().collect(Collectors.partitioningBy(push -> Objects.equals(push.getPolicyStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)));

        // 推送所有承保的
        List<SmOrderDistributionBatchPush> success = map.get(Boolean.FALSE);
        pushLastMonthAllDate5(batchNo, success);

        // 推送所有退保的
        List<SmOrderDistributionBatchPush> cancels = map.get(Boolean.TRUE);
        pushLastMonthAllDate5Cancel(batchNo, cancels);
    }



    /**
     * 只推送指定时间段的保单
     *
     * @param start 开始时间
     * @param end   结束时间
     */
    public void distributionSettlementOnlyPush(LocalDate start, LocalDate end) {

        // 生成批次号
        String batchNo = start.toString();

        // 根据状态和时间筛选需要推送的保单
        List<SmOrderDistributionBatchPush> pushes = distributionBatchPushPushMapper.selectByStateAndAccountTime(Arrays.asList(EnumDistributionPushState.INIT.getCode(),
                EnumDistributionPushState.ERROR.getCode()), start, end);

        // 根据保单状态是否为取消成功进行分组
        Map<Boolean, List<SmOrderDistributionBatchPush>> map =
                pushes.stream().collect(Collectors.partitioningBy(push -> Objects.equals(push.getPolicyStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)));

        // 推送所有承包的保单
        List<SmOrderDistributionBatchPush> success = map.get(Boolean.FALSE);
        pushLastMonthAllDate5(batchNo, success);

        // 推送所有退保的保单
        List<SmOrderDistributionBatchPush> cancels = map.get(Boolean.TRUE);
        pushLastMonthAllDate5Cancel(batchNo, cancels);
    }


    /**
     * 4级分销结算
     */
    public void pushLastMonthAllDate5(String batchNo, List<SmOrderDistributionBatchPush> distributions) {

        // 将数据分割为多个部分，每个部分的长度不超过DIST_API_REQUEST_MAX_SIZE
        List<List<SmOrderDistributionBatchPush>> partitions = Lists.partition(distributions, DIST_API_REQUEST_MAX_SIZE);

        // 将SmOrderDistribution4Service对象赋值给me变量
        SmOrderDistribution4Service me = (SmOrderDistribution4Service) AopContext.currentProxy();

        // 遍历分割后的数据部分
        partitions.stream().filter(col -> !CollectionUtils.isEmpty(col)).forEach(partition -> {

            // 将每个部分的数据转换为InsuranceVillageAgentOrderDTO对象的列表
            List<InsuranceVillageAgentOrderDis4DTO> distReqs = partition.stream().map(me::toInsuranceVillageAgentOrder).collect(Collectors.toList());
            Map<String,InsuranceVillageAgentOrderDis4DTO> reqMap = distReqs.stream().collect(Collectors.toMap(InsuranceVillageAgentOrderDis4DTO::getOrderId, dto -> dto));

            // 调用proxyService的createOrder4方法，将distReqs作为参数传入，并返回OrderCommissionDTO的列表
            List<InsuranceVillageAgentOrderVO> insuranceVillageAgentOrderVOS = proxyService.createOrder4V2(distReqs);

            if (CollectionUtils.isEmpty(insuranceVillageAgentOrderVOS)) {
                log.error("分销中心返回空map");
                return;
            }

            List<InsuranceVillageAgentOrderVO> success = insuranceVillageAgentOrderVOS.stream().filter(InsuranceVillageAgentOrderVO::getSuccessFlag)
                    .collect(Collectors.toList());
            List<InsuranceVillageAgentOrderVO> fail = insuranceVillageAgentOrderVOS.stream().filter(vo->!vo.getSuccessFlag())
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(fail)) {
                log.error("存在同步失败的分销结算数据");
                //直接更新推送状态为失败
                updatePushStateFail(fail,batchNo);
            }

            if (CollectionUtils.isEmpty(success)) {
                log.info("没有同步成功的分销结算数据");
                return ;
            }

            Map<String, List<OrderDis4CommissionDTO>> maps = new HashMap<>();
            success.forEach(vo->{
                maps.put(vo.getOrderSn(), vo.getOrderCommissionList());
            });

            // 获取返回结果的订单ID集合
            Set<String> orderIds = maps.keySet();

            log.info("分销中心返回结果:{},数量:{}", JSONObject.toJSONString(orderIds), orderIds.size());

            // 根据fhOrderId属性对SmOrderInsured对象进行分组，并将每个分组的第一个元素作为SmOrderInsured对象获取到policyMap中
            Map<String, OrderInsuredBaseInfoVo> policyMap = LambdaUtils.groupByAndToFirstMap(orderInsuredMapper.listGroupInsuredByOrderIds(distReqs.stream().map(InsuranceVillageAgentOrderDis4DTO::getOrderId).collect(Collectors.toList())), OrderInsuredBaseInfoVo::getFhOrderId);

            // 遍历maps中的每个OrderCommissionDTO列表，将每个列表转换为SmOrderDistribution对象，并将转换后的对象添加到newDistOrders列表中
            List<SmOrderDistribution> newDistOrders = maps.entrySet()
                    .stream()
                    .map(oen -> {
                        return oen.getValue()
                                .stream().map(distOrder -> this.cvtOrderDistributionV2(oen.getKey(),
                                        policyMap.getOrDefault(oen.getKey(), new OrderInsuredBaseInfoVo()), distOrder))
                                .collect(Collectors.toList());
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            // 开启事务
            me.transactional(() -> {
                //1、创建分销订单
                createDistributionOrder(orderIds,newDistOrders);

                //2、修改SmOrderDistributionBatchPush对象的PushState为SUCCESS
                updatePushState(orderIds,batchNo);

                //3、保存推送记录
                List<SmOrderDistributionPush> pushDatas = distReqs
                        .stream()
                        .map(distOrder -> {
                            return this.cvtOrderDistributionPushV2(distOrder.getOrderId(),
                                            policyMap.getOrDefault(distOrder.getOrderId(), new OrderInsuredBaseInfoVo()), distOrder,maps.get(distOrder.getOrderId()));
                        })
                        .collect(Collectors.toList());
                smOrderDistributionPushMapper.insertList(pushDatas);

                //4、修改补偿记录表中状态为已补偿
                smOrderDistributionCompensateMapper.updateCompensateStatus(new ArrayList<>(orderIds),2);
            });
        });
    }

    private void updatePushStateFail(List<InsuranceVillageAgentOrderVO> fail, String batchNo) {
        List<SmOrderDistributionBatchPush> batchPushList = fail.stream().map(order -> {
            SmOrderDistributionBatchPush batchPush = new SmOrderDistributionBatchPush();
            batchPush.setOrderId(order.getOrderSn());
            batchPush.setPushState(EnumDistributionPushState.ERROR.getCode());
            batchPush.setErrorMsg(order.getFailReason());
            return batchPush;
        }).collect(Collectors.toList());

        distributionBatchPushPushMapper.updateByBatchNoAndOrderId(batchPushList);
    }

    private void updateCancelPushStateFail(List<InsuranceVillageAgentRefundOrderVO> fail, String batchNo) {
        List<SmOrderDistributionBatchPush> batchPushList = fail.stream().map(order -> {
            SmOrderDistributionBatchPush batchPush = new SmOrderDistributionBatchPush();
            batchPush.setOrderId(order.getOrderSn());
            batchPush.setPushState(EnumDistributionPushState.ERROR.getCode());
            batchPush.setErrorMsg(order.getFailReason());
            return batchPush;
        }).collect(Collectors.toList());

        distributionBatchPushPushMapper.updateByBatchNoAndOrderId(batchPushList);
    }

    /**
     * 创建分销订单
     * @param orderIds 订单号
     * @param newDistOrders 分销订单
     */
    private void createDistributionOrder(Set<String> orderIds, List<SmOrderDistribution> newDistOrders) {
        // 创建SmOrderDistribution对象的Example查询条件
        Example distQuery = new Example(SmOrderDistribution.class);
        distQuery.createCriteria().andIn("fhOrderId", orderIds);

        // 根据查询条件删除SmOrderDistribution对象
        distributionMapper.deleteByExample(distQuery);

        // 将newDistOrders中的SmOrderDistribution对象设置DistributionState为OVER，并插入数据库
        newDistOrders.forEach(dist -> dist.setDistributionState(EnumDistributionState.OVER.getCode()));
        distributionMapper.insertList(newDistOrders);
    }

    /**
     * 更新推送状态为推送成功
     * @param orderIds 订单号
     * @param batchNo 批次号
     */
    private void updatePushState(Set<String> orderIds, String batchNo) {
        // 创建SmOrderDistributionBatchPush对象的Example查询条件
        Example example = new Example(SmOrderDistributionBatchPush.class);
        example.createCriteria().andIn("orderId", orderIds)
                .andEqualTo("policyStatus", SmConstants.POLICY_STATUS_SUCCESS);

        // 创建SmOrderDistributionBatchPush对象，设置PushState为SUCCESS
        SmOrderDistributionBatchPush update = new SmOrderDistributionBatchPush();
        update.setPushState(EnumDistributionPushState.SUCCESS.getCode());
        // 设置推送时间 2025-04-25分销优化
        update.setMonthlyBatchNo(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));

        // 根据查询条件更新SmOrderDistributionBatchPush对象
        distributionBatchPushPushMapper.updateByExampleSelective(update, example);
    }

    private SmOrderDistributionPush cvtOrderDistributionPush(String key, OrderInsuredBaseInfoVo insuredBaseInfoVo, InsuranceVillageAgentOrderDTO distOrder,List<OrderCommissionDTO> response) {
        SmOrderDistributionPush push = new SmOrderDistributionPush();
        push.setPushReason("结算单推送");
        push.setFhOrderId(key);
        push.setEndorsementNo(insuredBaseInfoVo.getEndorsementNo());
        push.setPolicyNo(insuredBaseInfoVo.getPolicyNo());
        push.setPushData(JSONObject.toJSONString(distOrder));
        push.setResponseData(JSONObject.toJSONString(response));
        push.setPushState(CollectionUtils.isEmpty(response)?2:1);
        return push;
    }
    private SmOrderDistributionPush cvtOrderDistributionPushV2(String key, OrderInsuredBaseInfoVo insuredBaseInfoVo, InsuranceVillageAgentOrderDis4DTO distOrder,List<OrderDis4CommissionDTO> response) {
        SmOrderDistributionPush push = new SmOrderDistributionPush();
        push.setPushReason("结算单推送");
        push.setFhOrderId(key);
        push.setEndorsementNo(insuredBaseInfoVo.getEndorsementNo());
        push.setPolicyNo(insuredBaseInfoVo.getPolicyNo());
        push.setPushData(JSONObject.toJSONString(distOrder));
        push.setResponseData(JSONObject.toJSONString(response));
        push.setPushState(CollectionUtils.isEmpty(response)?2:1);
        return push;
    }


    /**
     * 4级分销退费结算
     */
    public void pushLastMonthAllDate5Cancel(String batchNo, List<SmOrderDistributionBatchPush> distributions) {
        if (CollectionUtils.isEmpty(distributions)) {
            return;
        }

        //查询所有退保订单对应的正向分销订单号
        List<String> fhOrderIds = distributions.stream().map(SmOrderDistributionBatchPush::getOrderId).collect(Collectors.toList());
        List<SmOrderDistribution> orderDistributions = distributionMapper.selectDistributionByOrderIds(fhOrderIds);
        Map<String, SmOrderDistribution> orderDistributionMap = orderDistributions.stream().collect(Collectors.toMap(SmOrderDistribution::getFhOrderId, Function.identity()));

        // 查询所有退保订单对应正向记录结算时间
        List<SmOrderDistributionBatchPush> batchPushes = distributionBatchPushPushMapper.selectByFhOrderIds(fhOrderIds);
        Map<String, SmOrderDistributionBatchPush> batchPushMap = batchPushes.stream().collect(Collectors.toMap(SmOrderDistributionBatchPush::getOrderId, Function.identity()));

        //一二级均为客户经理的退保单，无需推送分销，直接更新推送状态为已推送
        List<SmOrderDistributionBatchPush> empDistributionBatchPushList = distributions.stream()
                .filter(distribution -> orderDistributionMap.containsKey(distribution.getOrderId()) && StringUtils.isEmpty(orderDistributionMap.get(distribution.getOrderId()).getDistributionOrderNo()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(empDistributionBatchPushList)) {
            log.info("存在一二级均为客户经理的退保结算单：{}", JSON.toJSONString(empDistributionBatchPushList));
            Example example = new Example(SmOrderDistributionBatchPush.class);
            example.createCriteria().andIn("orderId", empDistributionBatchPushList.stream().map(SmOrderDistributionBatchPush::getOrderId).collect(Collectors.toList()))
                    .andEqualTo("policyStatus", SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            SmOrderDistributionBatchPush update = new SmOrderDistributionBatchPush();
            update.setPushState(EnumDistributionPushState.SUCCESS.getCode());
            update.setMonthlyBatchNo(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));
            distributionBatchPushPushMapper.updateByExampleSelective(update, example);

            distributions.removeAll(empDistributionBatchPushList);
        }

        List<List<SmOrderDistributionBatchPush>> partitions = Lists.partition(distributions, DIST_API_REQUEST_MAX_SIZE);

        //将数据推送到分销中心
        SmOrderDistribution4Service me = (SmOrderDistribution4Service) AopContext.currentProxy();
        partitions.forEach(partition -> {
            List<String> orderIds = partition.stream()
                    .map(SmOrderDistributionBatchPush::getOrderId)
                    .collect(Collectors.toList());
            LocalDate localDate = LocalDate.of(2025, 5, 6);
            Date correctDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            List<String> historyOrderIds = batchPushes.stream()
                    .filter(batchPush -> batchPush.getCommissionSettlementDate().before(correctDate))
                    .collect(Collectors.toList())
                    .stream()
                    .map(SmOrderDistributionBatchPush::getOrderId)
                    .collect(Collectors.toList());

            // 根据fhOrderId属性对SmOrderInsured对象进行分组，并将每个分组的第一个元素作为SmOrderInsured对象获取到policyMap中
            Map<String, OrderInsuredBaseInfoVo> policyMap = LambdaUtils.groupByAndToFirstMap(orderInsuredMapper.listGroupInsuredByOrderIds(orderIds), OrderInsuredBaseInfoVo::getFhOrderId);

            // SmOrderDistributionBatchPush to InsuranceVillageAgentOrderDTO
            List<com.cfpamf.ms.insur.facade.dto.dis.InsuranceVillageAgentRefundOrderDTO> distReqs = partition.stream().map(
                    batchPush->this.toInsuranceVillageAgentRefundOrderDTOV2(batchPush,orderDistributionMap.get(batchPush.getOrderId()),batchPushMap.get(batchPush.getOrderId())))
                    .collect(Collectors.toList());
            log.info("4级分销退费结算请求：{}", JSON.toJSONString(distReqs));
            List<InsuranceVillageAgentRefundOrderVO> refundOrderVOS = proxyService.refund4V2(distReqs);
            log.info("4级分销退费结算返回结果：{}", JSON.toJSONString(refundOrderVOS));

            List<InsuranceVillageAgentRefundOrderVO> success = refundOrderVOS.stream().filter(InsuranceVillageAgentRefundOrderVO::getSuccessFlag)
                    .collect(Collectors.toList());
            List<InsuranceVillageAgentRefundOrderVO> fail = refundOrderVOS.stream().filter(vo->!vo.getSuccessFlag())
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(fail)) {
                log.info("存在同步失败的分销退订单结算数据：{}", JSON.toJSONString(fail));
                log.error("存在同步失败的分销退订单结算数据");
                //直接更新推送状态为失败
                updateCancelPushStateFail(fail,batchNo);
            }
            if (CollectionUtils.isEmpty(success)) {
                log.info("没有同步成功的分销退订单结算数据");
                return ;
            }

            log.info("分销中心退订单返回结果:{},数量:{}", JSONObject.toJSONString(success), success.size());

            Map<String,List<InsuranceVillageAgentRefundOrderVO>> refundOrderVOSMap = refundOrderVOS.stream().collect(Collectors.groupingBy(InsuranceVillageAgentRefundOrderVO::getOrderSn));
            List<String> successOrderIds = success.stream().map(InsuranceVillageAgentRefundOrderVO::getOrderSn)
                    .collect(Collectors.toList());

            //将推送成功的对象中的orderCommissionList，合并到一个List中
            List<OrderRefundCommissionDTO> orderRefundCommissionDTOList = success.stream()
                    .map(InsuranceVillageAgentRefundOrderVO::getOrderCommissionList)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            //根据分销订单号更新sm_order_distribution表等级(结算日期为20250506之后的不需要更新)
            List<SmOrderDistribution> updateDistributionDTOS = orderRefundCommissionDTOList.stream()
                    .map(orderRefundCommissionDTO -> {
                        SmOrderDistribution smOrderDistribution = new SmOrderDistribution();
                        smOrderDistribution.setFhOrderId(orderRefundCommissionDTO.getOrderId());
                        smOrderDistribution.setDistributionOrderNo(orderRefundCommissionDTO.getId().toString());
                        smOrderDistribution.setDistributionLevel(orderRefundCommissionDTO.getLevel());
                        return smOrderDistribution;
                    })
                    .collect(Collectors.toList())
                    .stream()
                    .filter(smOrderDistribution -> historyOrderIds.contains(smOrderDistribution.getFhOrderId()))
                    .collect(Collectors.toList());
            me.transactional(() -> {
                //更新分销订单等级
                if (!CollectionUtils.isEmpty(updateDistributionDTOS)) {
                    log.info("退订单结算 更新分销订单等级:{}", JSON.toJSONString(updateDistributionDTOS));
                    distributionMapper.updateDistributionLevel(updateDistributionDTOS);
                }

                Example example = new Example(SmOrderDistributionBatchPush.class);
                example.createCriteria().andIn("orderId", successOrderIds)
                        .andEqualTo("policyStatus", SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
                SmOrderDistributionBatchPush update = new SmOrderDistributionBatchPush();
                update.setPushState(EnumDistributionPushState.SUCCESS.getCode());
                update.setMonthlyBatchNo(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));
                distributionBatchPushPushMapper.updateByExampleSelective(update, example);

                //保存推送记录
                List<SmOrderDistributionPush> pushDatas = distReqs
                        .stream()
                        .map(req -> {
                            return this.cvtOrderRefund4PushV2(req.getOrderId(),
                                            policyMap.getOrDefault(req.getOrderId(), new OrderInsuredBaseInfoVo()), req, refundOrderVOSMap.get(req.getOrderId()));
                        })
                        .collect(Collectors.toList());
                smOrderDistributionPushMapper.insertList(pushDatas);
            });
        });
    }

    /**
     * 4级分销退费推送记录vo转化
     * @param refundOrderId 退保订单号
     * @param insuredBaseInfoVo 被保人信息
     * @param req 退费请求
     * @return SmOrderDistributionPush
     */
    private SmOrderDistributionPush cvtOrderRefund4Push(String refundOrderId, OrderInsuredBaseInfoVo insuredBaseInfoVo, InsuranceVillageAgentRefundOrderDTO req) {
        SmOrderDistributionPush push = new SmOrderDistributionPush();
        push.setPushReason("退保结算单推送");
        push.setFhOrderId(refundOrderId);
        push.setEndorsementNo(insuredBaseInfoVo.getEndorsementNo());
        push.setPolicyNo(insuredBaseInfoVo.getPolicyNo());
        push.setPushData(JSONObject.toJSONString(req));
        return push;
    }

    /**
     * 4级分销退费推送记录vo转化
     * @param refundOrderId 退保订单号
     * @param insuredBaseInfoVo 被保人信息
     * @param req 退费请求
     * @return SmOrderDistributionPush
     */
    private SmOrderDistributionPush cvtOrderRefund4PushV2(String refundOrderId, OrderInsuredBaseInfoVo insuredBaseInfoVo,
                                                          com.cfpamf.ms.insur.facade.dto.dis.InsuranceVillageAgentRefundOrderDTO req,
                                                          List<InsuranceVillageAgentRefundOrderVO> refundOrderVOS) {
        SmOrderDistributionPush push = new SmOrderDistributionPush();
        push.setPushReason("退保结算单推送");
        push.setFhOrderId(refundOrderId);
        push.setEndorsementNo(insuredBaseInfoVo.getEndorsementNo());
        push.setPolicyNo(insuredBaseInfoVo.getPolicyNo());
        push.setPushData(JSONObject.toJSONString(req));
        push.setResponseData(JSONObject.toJSONString(refundOrderVOS));
        return push;
    }

    /**
     * 将SmOrderDistributionBatchPush转换为InsuranceVillageAgentOrderDTO
     *
     * @param smOrder SmOrderDistributionBatchPush对象，表示待转换的SmOrderDistributionBatchPush对象
     * @return 转换后的InsuranceVillageAgentOrderDTO对象
     */
    private InsuranceVillageAgentOrderDis4DTO toInsuranceVillageAgentOrder(SmOrderDistributionBatchPush smOrder) {
        InsuranceVillageAgentOrderDis4DTO dto = new InsuranceVillageAgentOrderDis4DTO();

        dto.setCommodityNum(1);
        dto.setOrderId(smOrder.getOrderId());
        dto.setPayTime(smOrder.getAccountTime());
        dto.setLevelOneUserIdNumber(smOrder.getLevelOneUserIdNumber());
        dto.setLevelTwoUserIdNumber(smOrder.getLevelTwoUserIdNumber());
        dto.setLevelOneOrderCommission(smOrder.getLevelOneOrderCommission());
        dto.setLevelTwoOrderCommission(smOrder.getLevelTwoOrderCommission());
        dto.setProductName(smOrder.getProductMapperName());
        dto.setProductId(smOrder.getProductId() + "");
        dto.setCustomerIdNo(smOrder.getCustomerIdNo());
        dto.setCustomerName(smOrder.getCustomerName());
        dto.setCustomerPhoneNum(smOrder.getCustomerPhoneNum());
        dto.setPayAmount(smOrder.getPayAmount());
        dto.setOrderStatus(5);
        dto.setCommissionSettlementDate(smOrder.getCommissionSettlementDate());
        dto.setIsLifeServicePartner(smOrder.getIsLifeServicePartner());
        return dto;
    }


    /**
     * 将SmOrderDistributionBatchPush转换为InsuranceVillageAgentOrderDTO
     *
     * @param smOrder
     * @return
     */
    private InsuranceVillageAgentRefundOrderDTO toInsuranceVillageAgentRefundOrderDTO(SmOrderDistributionBatchPush smOrder) {
        InsuranceVillageAgentRefundOrderDTO dto = new InsuranceVillageAgentRefundOrderDTO();
        dto.setRefundOrderId("RF-" + smOrder.getOrderId());
        dto.setOrderId(smOrder.getOrderId());
        dto.setRefundTime(smOrder.getAccountTime());
        dto.setReturnQty(1);
        dto.setLevelOneOrderCommission(smOrder.getLevelOneOrderCommission().abs());
        dto.setLevelTwoOrderCommission(smOrder.getLevelTwoOrderCommission().abs());
        dto.setRefundMoney(smOrder.getPayAmount().abs());

        return dto;
    }

    /**
     * 将SmOrderDistributionBatchPush转换为InsuranceVillageAgentOrderDTO
     *
     * @param smOrder
     * @return
     */
    private com.cfpamf.ms.insur.facade.dto.dis.InsuranceVillageAgentRefundOrderDTO toInsuranceVillageAgentRefundOrderDTOV2(SmOrderDistributionBatchPush smOrder,SmOrderDistribution orderDistribution,SmOrderDistributionBatchPush orderDistributionBatchPush) {
        com.cfpamf.ms.insur.facade.dto.dis.InsuranceVillageAgentRefundOrderDTO  dto = new com.cfpamf.ms.insur.facade.dto.dis.InsuranceVillageAgentRefundOrderDTO();
        dto.setRefundOrderId("RF-" + smOrder.getOrderId());
        dto.setOrderId(smOrder.getOrderId());
        dto.setRefundTime(smOrder.getAccountTime());
        dto.setReturnQty(1);
        dto.setLevelOneOrderCommission(smOrder.getLevelOneOrderCommission().abs());
        dto.setLevelTwoOrderCommission(smOrder.getLevelTwoOrderCommission().abs());
        dto.setRefundMoney(smOrder.getPayAmount().abs());
        if (!Objects.isNull(orderDistributionBatchPush)) {
            dto.setCommissionSettlementDate(orderDistributionBatchPush.getCommissionSettlementDate());
        }
        if (!Objects.isNull(orderDistribution)) {
            String[] ids = orderDistribution.getDistributionOrderNo().split(",");
            List<Long> idsList = Arrays.stream(ids).map(Long::parseLong).collect(Collectors.toList());
            dto.setIdList(idsList);
        }
        return dto;
    }


    /**
     * 单独一个事务
     *
     * @param runnable
     */
    @Transactional(rollbackFor = Exception.class)
    public void transactional(Runnable runnable) {
        runnable.run();
    }

    /**
     * 处理4级分销订单
     *
     * @param fhOrderId 订单号
     */
    public void distribution(String fhOrderId, SmOrderListVO orderInfo, SmOrderVillageActivity activity) {
        log.info("生成四级分销单，订单号{}",fhOrderId);
        SmOrderDistribution4Service me = (SmOrderDistribution4Service) AopContext.currentProxy();

        // 非空判断
        if (Objects.isNull(activity) || !activity.is4Dist()) {
            log.error("非4级分销订单调用了4级分销{}", fhOrderId);
            return;
        }

        List<SmOrderDistribution> dbDatas = distributionMapper.selectByFhOrderId(fhOrderId);
        if (!CollectionUtils.isEmpty(dbDatas)) {
            log.info("已经生成过分销订单了");
            return;
        }

        ProductDistributionConfig mapperProduct = productDistributionConfigMapper.getByProductId(Long.valueOf(orderInfo.getProductId()));
        // 查询投保人
        FhProposer applicant = mapper.getOrderApplicant(fhOrderId);

        //查询支出端佣金明细
        List<ZhSettlementCostInfo> commissionByPolicyNo = whaleManagerInnerService.listZhSettlementCostByPolicyNos(Collections.singletonList(orderInfo.getThPolicyNo()),0);
        log.info("佣金明细：{}",JSONObject.toJSONString(commissionByPolicyNo));

        if (CollectionUtils.isEmpty(commissionByPolicyNo)) {
            log.info("4级分销订单佣金未计算{},保单号：{},批单号:{}", fhOrderId, orderInfo.getThPolicyNo(), orderInfo.getEndorsementNo());
            if (smOrderDistributionCompensateMapper.selectByOrderId(fhOrderId) == 0) {
                //保存至补偿表
                SmOrderDistributionCompensate compensate = cvtCompensate(orderInfo, activity, mapperProduct, applicant);
                smOrderDistributionCompensateMapper.insert(compensate);
            }
            return;
        }

        // 分销订单数据
        InsuranceVillageAgentOrderDTO order = cvtVillageAgentOrderDTO(fhOrderId, orderInfo, activity, applicant, commissionByPolicyNo, mapperProduct);
        List<OrderCommissionDTO> order4 = proxyService.createOrder4(order);
        if (CollectionUtils.isEmpty(order4)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "生成普通分销订单失败：" + fhOrderId);
        }

        me.transactional(()->{
            //支出端佣金明细快照入库
            Integer version = smOrderDistributionSettlementCostMapper.getVersion(fhOrderId);
            smOrderDistributionSettlementCostMapper.insertList(commissionByPolicyNo.stream().map(x->{
                SmOrderDistributionSettlementCost cost = new SmOrderDistributionSettlementCost();
                BeanUtils.copyProperties(x,cost);
                cost.setVersion(version);
                cost.setFhOrderId(fhOrderId);
                return cost;
            }).collect(Collectors.toList()));

            //插入到普通分销
            log.info("四级分销返回信息：{}",JSONObject.toJSONString(order4));
            List<SmOrderDistribution> distributions = order4.stream()
                    .map(orderCommissionDist -> cvtOrderDistribution(fhOrderId, orderInfo.getPolicyNo(), orderCommissionDist,activity.getIsLifeServicePartner()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(distributions)) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "生成普通分销订单失败：" + fhOrderId);
            }
            log.info("分销单信息：{}",JSONObject.toJSONString(distributions));
            distributionMapper.insertList(distributions);

            //保存推送记录
            SmOrderDistributionPush push = new SmOrderDistributionPush();
            push.setPushReason("预单推送");
            push.setVersion(version);
            push.setFhOrderId(fhOrderId);
            push.setEndorsementNo(orderInfo.getEndorsementNo());
            push.setPolicyNo(orderInfo.getThPolicyNo());
            push.setPushData(JSONObject.toJSONString(order));
            push.setResponseData(JSONObject.toJSONString(order4));
            smOrderDistributionPushMapper.insert(push);

            //修改补偿记录表中状态为已补偿
            smOrderDistributionCompensateMapper.updateCompensateStatus(Collections.singletonList(fhOrderId),2);
        });
    }

    private InsuranceVillageAgentOrderDTO cvtVillageAgentOrderDTO(String fhOrderId, SmOrderListVO orderInfo, SmOrderVillageActivity activity, FhProposer applicant, List<ZhSettlementCostInfo> commissionByPolicyNo, ProductDistributionConfig mapperProduct) {
        // sum paymentAmount
        java.math.BigDecimal orderPaymentAmount = commissionByPolicyNo.stream().
                filter(s -> EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getGrantAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        java.math.BigDecimal amount = commissionByPolicyNo.stream().filter(s -> EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getProductPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

        // FIND MAX accountTime
        Date accountTime = orderInfo.getSubmitTime();

        //计算客户经理和村代的佣金
        BigDecimal oneCommission = BigDecimal.ZERO;
        BigDecimal twoCommission = BigDecimal.ZERO;
        if(Objects.equals(activity.getIsLifeServicePartner(),1)) {

            oneCommission = BigDecimal.ZERO;
            twoCommission = orderPaymentAmount;
            log.info("生服合伙人订单，订单号{},一级佣金{},二级佣金{}", fhOrderId, orderPaymentAmount, orderPaymentAmount);
        }else{
            oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
            twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
            log.info("普通四级分销订单，订单号{},一级佣金{},二级佣金{}", fhOrderId, orderPaymentAmount, orderPaymentAmount);
        }
        InsuranceVillageAgentOrderDTO order = new InsuranceVillageAgentOrderDTO();
        order.setOrderId(fhOrderId);
        // 预定单统一传4
        order.setOrderStatus(4);
        order.setPayTime(accountTime);
        order.setPayAmount(amount);
        // 一级分销用户
        order.setLevelOneUserIdNumber(activity.getManagerIdNumber());
        order.setLevelOneOrderCommission(oneCommission);
        // 二级分销用户
        order.setLevelTwoUserIdNumber(activity.getVillageRepresentativeIdNumber());
        order.setLevelTwoOrderCommission(twoCommission);
        order.setCommissionSettlementDate(getNextMonth15th(accountTime));
        order.setCommodityNum(1);
        order.setCustomerName(applicant.getPersonName());
        order.setCustomerIdNo(applicant.getIdNumber());
        order.setCustomerPhoneNum(applicant.getCellPhone());
        order.setProductId(orderInfo.getProductId() + "");
        if (Objects.nonNull(mapperProduct)) {
            order.setProductName(mapperProduct.getMapperName());
        }
        return order;
    }

    private SmOrderDistributionCompensate cvtCompensate(SmOrderListVO orderInfo, SmOrderVillageActivity activity, ProductDistributionConfig mapperProduct, FhProposer applicant) {
        SmOrderDistributionCompensate compensate = new SmOrderDistributionCompensate();
        compensate.setFhOrderId(orderInfo.getFhOrderId());
        compensate.setPolicyNo(orderInfo.getThPolicyNo());
        compensate.setEndorsementNo(StringUtils.isEmpty(orderInfo.getEndorsementNo()) ? null : orderInfo.getEndorsementNo().split("-")[0]);
        compensate.setPayTime(orderInfo.getSubmitTime());
        compensate.setPayAmount(orderInfo.getTotalAmount());
        compensate.setAccountTime(orderInfo.getAccountTime());
        // 一级分销用户
        compensate.setLevelOneUserIdNumber(activity.getManagerIdNumber());
        // 二级分销用户
        compensate.setLevelTwoUserIdNumber(activity.getVillageRepresentativeIdNumber());
        compensate.setCommissionSettlementDate(getNextMonth15th(orderInfo.getSubmitTime()));
        compensate.setCustomerName(applicant.getPersonName());
        compensate.setCustomerIdNo(applicant.getIdNumber());
        compensate.setCustomerPhoneNum(applicant.getCellPhone());
        compensate.setProductId(orderInfo.getProductId());
        compensate.setIsLifeServicePartner(activity.getIsLifeServicePartner());
        if (Objects.nonNull(mapperProduct)) {
            compensate.setProductName(mapperProduct.getMapperName());
        }
        compensate.setProductAttrCode(orderInfo.getProductAttrCode());
        //补偿状态 1-未补偿 2补偿成功
        compensate.setCompensateState(1);
        return compensate;
    }

    /**
     * 作废4级分销订单
     * @param fhOrderId 订单号
     * @return String
     */
    public void orderInvalid(String fhOrderId) {
        log.info("作废四级分销单，订单号{}",fhOrderId);
        InsuranceVillageAgentInvalidOrderDTO dto = new InsuranceVillageAgentInvalidOrderDTO();
        dto.setInvalidReason(5);
        dto.setOrderIdList(Collections.singletonList(fhOrderId));
        dto.setBusinessChannel(3);
        dto.setBusinessCode("bxnew001");
        dto.setRemarks("保险作废分销订单");
        proxyService.invalidOrder4(dto);

        SmOrderDistributionPush push = new SmOrderDistributionPush();
        push.setPushReason("作废分销订单推送");
        push.setFhOrderId(fhOrderId);
        push.setPushData(JSONObject.toJSONString(dto));
        smOrderDistributionPushMapper.insert(push);
    }

    /**
     * 保险订单业绩转移 批改村代/客户经理
     * @param fhOrderId 订单号
     */
    public void orderTransfer(String fhOrderId, SmOrderListVO orderInfo, SmOrderVillageActivityFourVo start, SmOrderVillageActivityFourVo activity) {
        log.info("批改四级分销单村代/客户经理，订单号{}",fhOrderId);
        SmOrderDistribution4Service me = (SmOrderDistribution4Service) AopContext.currentProxy();

        // 查询投保人
        FhProposer applicant = mapper.getOrderApplicant(fhOrderId);

        ProductDistributionConfig mapperProduct = productDistributionConfigMapper.getByProductId(Long.valueOf(orderInfo.getProductId()));

        //查询支出端佣金明细
        List<ZhSettlementCostInfo> commissionByPolicyNo = whaleManagerInnerService
                .listZhSettlementCostByPolicyNos(Collections.singletonList(orderInfo.getThPolicyNo()),0);
        if (CollectionUtils.isEmpty(commissionByPolicyNo)) {
            if (smOrderDistributionCompensateMapper.selectByOrderId(fhOrderId) > 0) {
                log.info("4级分销订单佣金未计算{},保单号：{},批单号:{}", fhOrderId, orderInfo.getThPolicyNo(), orderInfo.getEndorsementNo());
                //保存至补偿表
                SmOrderDistributionCompensate compensate = cvtCompensate(orderInfo, activity, mapperProduct, applicant);
                smOrderDistributionCompensateMapper.insert(compensate);
            }
            return;
        }

        // 分销订单数据
        InsuranceVillageAgentOrderDTO order = cvtVillageAgentOrderDTO(fhOrderId,orderInfo,activity,applicant,commissionByPolicyNo,mapperProduct);
        proxyService.transferOrder4(order);

        //修改四级分销订单一二级分销用户
        me.transactional(() -> {
            //支出端佣金明细快照入库
            Integer version = smOrderDistributionSettlementCostMapper.getVersion(fhOrderId);
            smOrderDistributionSettlementCostMapper.insertList(commissionByPolicyNo.stream().map(x->{
                SmOrderDistributionSettlementCost cost = new SmOrderDistributionSettlementCost();
                BeanUtils.copyProperties(x,cost);
                cost.setFhOrderId(fhOrderId);
                cost.setVersion(version);
                return cost;
            }).collect(Collectors.toList()));

            List<SmOrderDistributionCorrectVo> distributionList = initDistributionList(start, activity);
            log.info("批改四级分销信息：{}", JSONObject.toJSONString(distributionList));
            if (!CollectionUtils.isEmpty(distributionList)) {
                distributionMapper.updateDistributionCustName(distributionList);
            }

            //保存推送记录
            SmOrderDistributionPush push = new SmOrderDistributionPush();
            push.setPushReason("批改推送");
            push.setVersion(version);
            push.setFhOrderId(fhOrderId);
            push.setEndorsementNo(orderInfo.getEndorsementNo());
            push.setPolicyNo(orderInfo.getThPolicyNo());
            push.setPushData(JSONObject.toJSONString(order));
            smOrderDistributionPushMapper.insert(push);

            //修改补偿记录表中状态为已补偿
            smOrderDistributionCompensateMapper.updateCompensateStatus(Collections.singletonList(fhOrderId),2);
        });
    }

    private List<SmOrderDistributionCorrectVo> initDistributionList(SmOrderVillageActivityFourVo start,SmOrderVillageActivityFourVo activity) {
        List<SmOrderDistributionCorrectVo> distributionList = new ArrayList<>();
        distributionList.add(new SmOrderDistributionCorrectVo(
                activity.getFhOrderId(),
                getManagerDistributionLevel(start.getManagerIdNumber()),
                getManagerDistributionLevel(activity.getManagerIdNumber()),
                getDistributionCustName(activity.getManagerIdNumber(),activity.getManagerName())));
        distributionList.add(new SmOrderDistributionCorrectVo(
                activity.getFhOrderId(),
                getVillageRepresentativeDistributionLevel(start.getVillageRepresentativeIdNumber()),
                getVillageRepresentativeDistributionLevel(activity.getVillageRepresentativeIdNumber()),
                getDistributionCustName(activity.getVillageRepresentativeIdNumber(),activity.getVillageRepresentativeName())));
        return distributionList;
    }

    private Integer getManagerDistributionLevel(String managerIdNumber) {
        return idNumber.equals(managerIdNumber)?0:1;
    }
    private Integer getVillageRepresentativeDistributionLevel(String villageRepresentativeIdNumber) {
        return idNumber.equals(villageRepresentativeIdNumber)?0:2;
    }

    private String getDistributionCustName(String distributionCustIdNumber,String distributionCustName) {
        return idNumber.equals(distributionCustIdNumber)?"小鲸向海":distributionCustName;
    }


    /**
     * 将订单信息转换为SmOrderDistribution对象
     * @param fhOrderId 保单关联的订单号
     * @param policyNo 保单号
     * @param com 订单佣金信息
     * @return 转换后的SmOrderDistribution对象
     */
    private SmOrderDistribution cvtOrderDistribution(String fhOrderId, String policyNo, OrderCommissionDTO com,Integer isLifeServicePartner) {
        SmOrderDistribution di = new SmOrderDistribution();
        // 设置保单关联的订单号
        di.setFhOrderId(fhOrderId);
        // 设置保单号
        di.setPolicyNo(policyNo);

        di.setDistributionAmount(com.getOrderCommission());
        // 设置分配状态
        di.setDistributionState(com.getCommissionPaymentStatus() + "");
        // 设置分配订单号
        di.setDistributionOrderNo(com.getOrderId());
        // 设置身份证号码
        di.setIdNumber(com.getUniqueId());
        // 设置分配等级
        di.setDistributionLevel(com.getLevel());
        // 设置分配客户姓名
        di.setDistributionCustName(com.getUserName());
        // 设置分配客户手机号码
        di.setDistributionCustMobile(com.getUserMobile());
        //设置分配客户证件号
        di.setCommissionIdNumber(com.getUserIdNumber());
        // 设置分配类型
        di.setDistributionType(2);
        // 返回转换后的SmOrderDistribution对象
        if(Objects.equals(com.getLevel(),"2")) {
            di.setIsLifeServicePartner(isLifeServicePartner);
        }else{
            di.setIsLifeServicePartner(0);
        }
        return di;
    }

    /**
     * 将订单信息转换为SmOrderDistribution对象
     * @param fhOrderId 保单关联的订单号
     * @param policyVo 保单
     * @param com 订单佣金信息
     * @return 转换后的SmOrderDistribution对象
     */
    private SmOrderDistribution cvtOrderDistributionV2(String fhOrderId, OrderInsuredBaseInfoVo policyVo, OrderDis4CommissionDTO com) {
        SmOrderDistribution di = new SmOrderDistribution();
        // 设置保单关联的订单号
        di.setFhOrderId(fhOrderId);
        // 设置保单号
        di.setPolicyNo(policyVo.getPolicyNo());

        di.setDistributionAmount(com.getOrderCommission());
        // 设置分配状态
        di.setDistributionState(com.getCommissionPaymentStatus() + "");
        // 设置分配订单号
        di.setDistributionOrderNo(com.getOrderId());
        // 设置身份证号码
        di.setIdNumber(com.getUniqueId());
        // 设置分配等级
        di.setDistributionLevel(com.getLevel());
        // 设置分配客户姓名
        di.setDistributionCustName(com.getUserName());
        // 设置分配客户手机号码
        di.setDistributionCustMobile(com.getUserMobile());
        // 设置分配类型
        di.setDistributionType(2);
        //设置分配客户证件号
        di.setCommissionIdNumber(com.getUserIdNumber());
        // 返回转换后的SmOrderDistribution对象
        if(Objects.equals(com.getLevel(),"2")){
            di.setIsLifeServicePartner(policyVo.getIsLifeServicePartner());
        }else{
            di.setIsLifeServicePartner(0);
        }
        return di;
    }


    /**
     * 获取下个月15号
     *
     * @param date
     * @return
     */
    private static Date getNextMonth15th(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 将日期设置为下个月
        calendar.add(Calendar.MONTH, 1);
        // 设置日期为 15 号
        calendar.set(Calendar.DAY_OF_MONTH, 15);

        return calendar.getTime();
    }


    /**
     * 四级分销单补偿推送
     */
    public void distributionCompensate() {
        log.info("开始执行四级分销单补偿推送");
        PageHelper.startPage(1,50);
        Example example = new Example(SmOrderDistributionCompensate.class);
        example.createCriteria().andEqualTo("compensateState",1);
        example.orderBy("updateTime").asc();
        List<SmOrderDistributionCompensate> compensates = smOrderDistributionCompensateMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(compensates)) {
            return;
        }

        //修改update_time
        smOrderDistributionCompensateMapper.updateByOrderIds(compensates.stream().map(SmOrderDistributionCompensate::getFhOrderId).collect(Collectors.toList()));

        //个险/新契约补偿
        personCompensate(compensates);

        log.info("四级分销单补偿推送完成");
    }

    /**
     * 个险补偿
     * @param smOrderDistributionCompensates 补偿vo
     * @return SmOrderDistributionBatchPush
     */
    private void personCompensate(List<SmOrderDistributionCompensate> smOrderDistributionCompensates) {
        List<String> policyNos = smOrderDistributionCompensates.stream().map(SmOrderDistributionCompensate::getPolicyNo).collect(Collectors.toList());
        //查询支出端佣金明细
        List<ZhSettlementCostInfo> commissionByPolicyNo = whaleManagerInnerService.listZhSettlementCostByPolicyNos(policyNos,0);
        //根据保单号分组
        Map<String, List<ZhSettlementCostInfo>> personMap =
                commissionByPolicyNo.stream().collect(Collectors.groupingBy(ZhSettlementCostInfo::getPolicyNo));

        compensate(smOrderDistributionCompensates,personMap);
    }

    /**
     * 补偿推送预结算/结算单
     * @param smOrderDistributionCompensates 补偿vo
     * @param personMap 支出端佣金明细
     */
    private void compensate(List<SmOrderDistributionCompensate> smOrderDistributionCompensates, Map<String, List<ZhSettlementCostInfo>> personMap) {
        SmOrderDistribution4Service me = (SmOrderDistribution4Service) AopContext.currentProxy();

        for (SmOrderDistributionCompensate compensate:smOrderDistributionCompensates) {
            List<SmOrderDistributionBatchPush> pushes = new ArrayList<>();
            //获取本月1号
            Date date =  Date.from(YearMonth.now().atDay(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
            List<ZhSettlementCostInfo> zhSettlementCostInfos = personMap.get(compensate.getPolicyNo());

            if (CollectionUtils.isEmpty(zhSettlementCostInfos)) {
                continue;
            }

            String fhOrderId = compensate.getFhOrderId();
            // 分销订单数据
            InsuranceVillageAgentOrderDTO order = cvtCompensate2VillageAgentOrderDTO(compensate, zhSettlementCostInfos);
            log.info("补偿-转换分销VO：{}",JSONObject.toJSONString(order));
            List<OrderCommissionDTO> order4 = proxyService.createOrder4(order);
            if (CollectionUtils.isEmpty(order4)) {
                continue;
            }

            me.transactional(()->{
                //支出端佣金明细快照入库
                Integer version = smOrderDistributionSettlementCostMapper.getVersion(fhOrderId);
                smOrderDistributionSettlementCostMapper.insertList(zhSettlementCostInfos.stream().map(x->{
                    SmOrderDistributionSettlementCost cost = new SmOrderDistributionSettlementCost();
                    BeanUtils.copyProperties(x,cost);
                    cost.setVersion(version);
                    cost.setFhOrderId(fhOrderId);
                    return cost;
                }).collect(Collectors.toList()));

                //插入到普通分销
                log.info("分销中心返回：{}",JSONObject.toJSONString(order4));
                List<SmOrderDistribution> distributions = order4.stream()
                        .map(orderCommissionDist -> cvtOrderDistribution(fhOrderId, compensate.getPolicyNo(), orderCommissionDist,compensate.getIsLifeServicePartner()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(distributions)) {
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "生成普通分销订单失败：" + fhOrderId);
                }
                distributionMapper.insertList(distributions);

                //保存推送记录
                SmOrderDistributionPush push = new SmOrderDistributionPush();
                push.setPushReason("预单推送");
                push.setVersion(version);
                push.setFhOrderId(fhOrderId);
                push.setEndorsementNo(compensate.getEndorsementNo());
                push.setPolicyNo(compensate.getPolicyNo());
                push.setPushData(JSONObject.toJSONString(order));
                push.setResponseData(JSONObject.toJSONString(order4));
                smOrderDistributionPushMapper.insert(push);

                //修改补偿记录表中状态为已补偿
                smOrderDistributionCompensateMapper.updateCompensateStatus(Collections.singletonList(fhOrderId),2);

                //记账日期为上月，需推送结算数据
                if (compensate.getAccountTime().before(date)) {
                    //承保单结算
                    pushes.add(cvt2Push(compensate, zhSettlementCostInfos));
                    //退保单结算
                    if (zhSettlementCostInfos.stream().anyMatch(s -> !EnumWhalePolicyStatus.isInsure(s.getProductStatus()))) {
                        pushes.add(cvtRefund4Push(compensate, zhSettlementCostInfos));
                    }
                    distributionBatchPushPushMapper.insertListOrUpdate(pushes);
                }
            });
        };
    }

    private SmOrderDistributionBatchPush cvtRefund4Push(SmOrderDistributionCompensate compensate, List<ZhSettlementCostInfo> zhSettlementCostInfos) {
        SmOrderDistributionBatchPush push = new SmOrderDistributionBatchPush();
        java.math.BigDecimal orderPaymentAmount = zhSettlementCostInfos.stream().filter(s -> !EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getGrantAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        java.math.BigDecimal amount = zhSettlementCostInfos.stream().filter(s -> !EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getProductPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

//        //计算客户经理和村代的佣金
//        BigDecimal oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
//        BigDecimal twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
        //计算客户经理和村代的佣金
        BigDecimal oneCommission = BigDecimal.ZERO;
        BigDecimal twoCommission = BigDecimal.ZERO;
        if(Objects.equals(compensate.getIsLifeServicePartner(),1)) {

            oneCommission = BigDecimal.ZERO;
            twoCommission = orderPaymentAmount;
            log.info("生服合伙人订单，订单号{},一级佣金{},二级佣金{}", compensate.getFhOrderId(), orderPaymentAmount, orderPaymentAmount);
        }else{
            oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
            twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
            log.info("普通四级分销订单，订单号{},一级佣金{},二级佣金{}", compensate.getFhOrderId(), orderPaymentAmount, orderPaymentAmount);
        }


        BeanUtils.copyProperties(compensate,push);
        push.setBatchNo(YearMonth.now().atDay(1).toString());
        push.setOrderId(compensate.getFhOrderId());
        push.setPayAmount(amount);
        push.setLevelOneOrderCommission(oneCommission);
        push.setLevelTwoOrderCommission(twoCommission);
        push.setCommissionSettlementDate(new Date());
        push.setPolicyStatus(EnumInsuredAppStatus.CANCEL_SUCCESS.getCode());
        push.setCommissionSettlementDate(getNextMonth15th(new Date()));
        push.setPushState(0);
        push.setIsLifeServicePartner(compensate.getIsLifeServicePartner());
        log.info("补偿-转化退保结算VO:{}",JSONObject.toJSONString(push));
        return push;
    }

    private SmOrderDistributionBatchPush cvt2Push(SmOrderDistributionCompensate compensate, List<ZhSettlementCostInfo> zhSettlementCostInfos) {
        SmOrderDistributionBatchPush push = new SmOrderDistributionBatchPush();
        java.math.BigDecimal orderPaymentAmount = zhSettlementCostInfos.stream().filter(s -> EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getGrantAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        java.math.BigDecimal amount = zhSettlementCostInfos.stream().filter(s -> EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getProductPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

//        //计算客户经理和村代的佣金
//        BigDecimal oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
//        BigDecimal twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
        //计算客户经理和村代的佣金
        BigDecimal oneCommission = BigDecimal.ZERO;
        BigDecimal twoCommission = BigDecimal.ZERO;
        if(Objects.equals(compensate.getIsLifeServicePartner(),1)) {

            oneCommission = BigDecimal.ZERO;
            twoCommission = orderPaymentAmount;
            log.info("生服合伙人订单，订单号{},一级佣金{},二级佣金{}", compensate.getFhOrderId(), orderPaymentAmount, orderPaymentAmount);
        }else{
            oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
            twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
            log.info("普通四级分销订单，订单号{},一级佣金{},二级佣金{}", compensate.getFhOrderId(), orderPaymentAmount, orderPaymentAmount);
        }

        BeanUtils.copyProperties(compensate,push);
        push.setBatchNo(YearMonth.now().atDay(1).toString());
        push.setOrderId(compensate.getFhOrderId());
        push.setPayAmount(amount);
        push.setLevelOneOrderCommission(oneCommission);
        push.setLevelTwoOrderCommission(twoCommission);
        push.setCommissionSettlementDate(new Date());
        push.setPolicyStatus(EnumInsuredAppStatus.SUCCESS.getCode());
        push.setCommissionSettlementDate(getNextMonth15th(new Date()));
        push.setPushState(0);
        push.setIsLifeServicePartner(compensate.getIsLifeServicePartner());
        log.info("补偿-转化结算VO:{}",JSONObject.toJSONString(push));
        return push;
    }

    private InsuranceVillageAgentOrderDTO cvtCompensate2VillageAgentOrderDTO(SmOrderDistributionCompensate compensate, List<ZhSettlementCostInfo> zhSettlementCostInfos) {
        // sum paymentAmount
        java.math.BigDecimal orderPaymentAmount = zhSettlementCostInfos.stream().filter(s -> EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getGrantAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        java.math.BigDecimal amount = zhSettlementCostInfos.stream().filter(s -> EnumWhalePolicyStatus.isInsure(s.getProductStatus())).map(ZhSettlementCostInfo::getProductPremium).reduce(BigDecimal.ZERO, BigDecimal::add);

        // FIND MAX accountTime
        Date accountTime = compensate.getAccountTime();

        //计算客户经理和村代的佣金
        //BigDecimal oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
        //BigDecimal twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
        //计算客户经理和村代的佣金
        BigDecimal oneCommission = BigDecimal.ZERO;
        BigDecimal twoCommission = BigDecimal.ZERO;
        if(Objects.equals(compensate.getIsLifeServicePartner(),1)) {

            oneCommission = BigDecimal.ZERO;
            twoCommission = orderPaymentAmount;
            log.info("生服合伙人订单，订单号{},一级佣金{},二级佣金{}", compensate.getFhOrderId(), orderPaymentAmount, orderPaymentAmount);
        }else{
            oneCommission = orderPaymentAmount.multiply(new BigDecimal("0.3")).setScale(2, RoundingMode.HALF_UP);
            twoCommission = orderPaymentAmount.subtract(oneCommission).setScale(2, RoundingMode.HALF_UP);
            log.info("普通四级分销订单，订单号{},一级佣金{},二级佣金{}", compensate.getFhOrderId(), orderPaymentAmount, orderPaymentAmount);
        }

        InsuranceVillageAgentOrderDTO order = new InsuranceVillageAgentOrderDTO();
        BeanUtils.copyProperties(compensate,order);
        order.setOrderId(compensate.getFhOrderId());
        // 预定单统一传4
        order.setOrderStatus(4);
        order.setPayTime(accountTime);
        order.setPayAmount(amount);
        order.setLevelOneOrderCommission(oneCommission);
        order.setLevelTwoOrderCommission(twoCommission);
        order.setCommissionSettlementDate(getNextMonth15th(accountTime));
        order.setCommodityNum(1);
        order.setProductId(compensate.getProductId()+"");
        return order;
    }

    public List<OrderDistribution4CommissionVo> getDistributionCommissionByPage(DistributionCommissionQuery query) {
        return distributionBatchPushPushMapper.getDistributionCommissionByPage(query);
    }

    public void updateSyncState(List<Long> ids) {
        distributionBatchPushPushMapper.updateSyncState(ids);
    }

    public List<OrderDistribution4CommissionListVo> getDistributionCommissionByPageV2(DistributionCommissionQuery query) {
        List<OrderDistribution4CommissionVo> list = distributionBatchPushPushMapper.getDistributionCommissionByPageV2(query);

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        Map<Long, List<OrderDistribution4CommissionVo>> groupedMap = list.stream()
                .collect(Collectors.groupingBy(OrderDistribution4CommissionVo::getId));

        return groupedMap.entrySet().stream()
                .map(entry -> {
                    OrderDistribution4CommissionListVo listVo = new OrderDistribution4CommissionListVo();
                    listVo.setId(entry.getKey());
                    listVo.setCommissionVos(entry.getValue());
                    return listVo;
                })
                .collect(Collectors.toList());
    }
}
