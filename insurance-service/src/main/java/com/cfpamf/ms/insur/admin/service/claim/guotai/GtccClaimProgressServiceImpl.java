package com.cfpamf.ms.insur.admin.service.claim.guotai;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.claim.form.GtccFileValidateParams;
import com.cfpamf.ms.insur.admin.constant.ClaimRiskType;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimFileUploadConfigMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementGtccMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderApplicantMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.enums.claim.guotai.EnumAction;
import com.cfpamf.ms.insur.admin.enums.claim.guotai.EnumGtccFileType;
import com.cfpamf.ms.insur.admin.enums.claim.guotai.EnumGtccRiskReason;
import com.cfpamf.ms.insur.admin.enums.claim.guotai.EnumGtccRiskType;
import com.cfpamf.ms.insur.admin.event.ClaimProcessNodeChangeEvent;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.external.gtcc.api.GtccApiProperties;
import com.cfpamf.ms.insur.admin.external.gtcc.api.GtccApiService;
import com.cfpamf.ms.insur.admin.external.gtcc.api.GtccSignUtil;
import com.cfpamf.ms.insur.admin.external.gtcc.models.GtccMailInfoModel;
import com.cfpamf.ms.insur.admin.external.gtcc.models.GtccReportModel;
import com.cfpamf.ms.insur.admin.external.gtcc.models.GtccResult;
import com.cfpamf.ms.insur.admin.external.gtcc.models.callback.GtccNotifyBaseCallback;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileCombDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileUnitDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.claim.GroovyCodeEntity;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimFileUploadConfig;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementGtcc;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimFileSimpleVo;
import com.cfpamf.ms.insur.admin.service.BaseCommonClaimService;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.SmClaimServiceImpl;
import com.cfpamf.ms.insur.admin.service.claim.base.AbsGtccStatusIdempotentUpdate;
import com.cfpamf.ms.insur.admin.service.claim.base.GtccClaimStatusUpdateService;
import com.cfpamf.ms.insur.admin.util.GtccClaimResponse;
import com.cfpamf.ms.insur.admin.util.UtilDate;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimFileCombDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.gtcc.WxGtccClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.gtcc.WxGtccClaimDetailVO;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/12/13 11:03
 * @Version 1.0
 */
@Service
@Slf4j
public class GtccClaimProgressServiceImpl extends BaseCommonClaimService {

    @Autowired
    private SmClaimReimbursementGtccMapper claimReimbursementGtccMapper;

    @Autowired
    private SmClaimFileUploadConfigMapper claimFileUploadConfigMapper;

    @Autowired
    private SmClaimServiceImpl claimService;

    @Autowired
    private SmClaimMapper claimMapper;

    @Autowired
    private SmOrderInsuredMapper insuredMapper;

    @Autowired
    private GroovyHelper groovyHelper;

    @Autowired
    private GtccApiService gtccApiService;

    private Map<EnumAction, AbsGtccStatusIdempotentUpdate<? extends GtccNotifyBaseCallback>> callBackHandlerMap;

    @Autowired
    private DLockTemplate lockTemplate;

    @Autowired
    private GtccApiProperties gtccApiProperties;

    @Autowired
    private SmOrderItemMapper smOrderItemMapper;

    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private SmProductMapper productMapper;



    @Autowired
    public void constructCallbackService(@Autowired List<AbsGtccStatusIdempotentUpdate<? extends GtccNotifyBaseCallback>> caseClosedReportCallbackList) {
        this.callBackHandlerMap = caseClosedReportCallbackList.stream()
                .collect(
                        Collectors.toMap(GtccClaimStatusUpdateService::getAction, Function.identity())
                );
    }

    @Override
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimFIleIdList(int claimId, List<Integer> cfIdList) {
        List<SmClaimFileUnitVO> existedFiles = claimMapper.listSmClaimFileUnitByClaimFileIdList(claimId, cfIdList);

        if (CollectionUtils.isNotEmpty(existedFiles)) {
            //获取同类型文件集合
            Map<String, List<ClaimFileSimpleVo>> fileSimpleVoListMap = existedFiles.stream()
                    .map(smClaimFileUnitVO -> {
                        ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
                        claimFileSimpleVo.setCfId(smClaimFileUnitVO.getCfId());
                        claimFileSimpleVo.setFileUrl(smClaimFileUnitVO.getFileUrl());
                        claimFileSimpleVo.setNewFlag(smClaimFileUnitVO.getNewFlag());
                        claimFileSimpleVo.setFileTypeCode(smClaimFileUnitVO.getFileTypeCode());
                        return claimFileSimpleVo;
                    })
                    .collect(Collectors.groupingBy(ClaimFileSimpleVo::getFileTypeCode));

            return fileSimpleVoListMap.entrySet().stream().map(entry -> {
                SmClaimFileCombVO claimFileCombVO = new SmClaimFileCombVO();
                claimFileCombVO.setFileUrls(entry.getValue().stream().map(ClaimFileSimpleVo::getFileUrl).collect(Collectors.toList()));
                claimFileCombVO.setFileTypeName(EnumGtccFileType.getNameByCode(entry.getKey()));
                claimFileCombVO.setFileTypeCode(entry.getKey());
                return claimFileCombVO;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProgress(ProgressDTO dto) {
        log.info("国泰ProgressDTO={}", JSON.toJSON(dto));

        WxClaimDetailVO claimDetail = mapper.getSmClaimById(dto.getClaimId());
        if (StrUtil.isNotEmpty(claimDetail.getCurrentStatusMemo())) {
            if (StrUtil.isNotEmpty(dto.getDataJson())) {
                JSONObject jsonObject = JSON.parseObject(dto.getDataJson());
                jsonObject.put(BaseConstants.companyNotify, claimDetail.getCurrentStatusMemo());
                dto.setDataJson(jsonObject.toJSONString());
            } else {
                dto.setDataJson(new JSONObject().fluentPut(BaseConstants.companyNotify, claimDetail.getCurrentStatusMemo()).toJSONString());
            }
            claimMapper.updateCurrentNodeMemo(dto.getClaimId(), null);

        }
        //添加案件流程节点事件触发器 add by zhangjian 2024-10-29
        busEngine.publish(new ClaimProcessNodeChangeEvent(dto.getClaimId()));
        if (
                Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY)
                        && !(ClaimWorkflow.CLAIM_MANAGER.contains(HttpRequestUtil.getUserId()) || StringUtils.isEmpty(HttpRequestUtil.getUserId()))
        ) {
            throw new BizException(ExcptEnum.CLAIM_ZA_STEP_TO_PAY.getCode(), "国泰案件请勿在保司核赔阶段手动操作");
        }

        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY) && Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CLAIM_UPDATE_ZA_STATUS)) {
            log.info("刷新国泰节点状态-{}", JSONObject.toJSONString(dto));
            triggerGtccStatus(dto);
            return;
        }

        // 如果页面不输入时间 创建时间取当前时间
        if (dto.getOTime() == null) {
            dto.setOTime(new Date());
        } else {
            dto.setOTime(DateUtil.getBeginOfDay(dto.getOTime()));
        }
        // 保存当前操作步骤
        dto.setSettlement(claimDetail.getSettlement());
        ClaimWorkflow.Step nextStep = workflow.goToNextStep(dto.getSCode(), dto.getOCode());

        if (Objects.isNull(nextStep)) {
            throw new MSBizNormalException("-1", "不可操作该流程，下一步选择错误");
        }


        boolean isSendCompany = Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_CHECK_BY_SAFES_CENTER);

        //当前节点不为保司审核，插入节点
        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY) || ClaimWorkflow.END_STEPS.contains(dto.getOCode())) {
            //更新选择的节点
            mapper.updateZaProgress(dto.getClaimId(), dto.getSCode(), dto);
        } else {
            mapper.insertProgress(dto);
        }

        if (!isSendCompany) {
            // 管理后台理操作已邮寄资料  邮寄资料日期
            if (dto.getOTime() != null && Objects.equals(dto.getOCode(), ClaimWorkflow.STEP_DATA_HAS_POST_EXPRESS)) {
                updateExpressTime(dto.getClaimId(), CommonUtil.getStartTimeOfDay(dto.getOTime()));
            }

            // 微信理赔填写邮寄资料  邮寄资料日期
            if (dto.getExpressTime() != null) {
                updateExpressTime(dto.getClaimId(), CommonUtil.getStartTimeOfDay(dto.getExpressTime()));
            }

            // 资料审核(渠道pco)需补充资料审批节需要修改下一节点名称为补充资料提交
            if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) && Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)) {
                nextStep.setSName("补充资料提交");
            }

            // 更新理赔状态
            int claimId = dto.getClaimId();
            // 如果下一步骤结案  自动结案
            if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_FINISH)) {
                ProgressDTO pDto = new ProgressDTO();
                pDto.setClaimId(claimId);
                convertStepToDTO(nextStep, pDto);
                pDto.setSettlement(claimDetail.getSettlement());
                pDto.setCreateBy(dto.getCreateBy());
                dto.setEvaluationStatus(0);
                mapper.updateFinishInfo(claimId, dto.getCreateBy(), dto);
                mapper.insertProgress(pDto);
            }
            // 更新邮寄资料时间/结案时间/邮寄纸质资料时间 当前环节开始时间/ 保险业务中心审核的次数
            mapper.updateClaimStatus(
                    claimId, dto.getOCode()
                    , dto.getCreateBy()
                    , dto.getOTime()
                    , nextStep
                    , claimHastenProcessor.calHastenTime(nextStep.getSCode(), claimId)
            );
            if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_TO_PAY) ||
                    Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) ||
                    Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE2) ||
                    Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE3)
            ) {
                claimHastenProcessor.updateExpectDays(nextStep.getSCode(), claimId);
            }

            //提交邮寄
            addExpressToGtcc(dto, claimDetail);
            //第一次从机构审核跳过保司审核
            jumpHeadQuarterFirstTime(dto, claimDetail, nextStep);
            // 补充资料提醒/邮件资料提醒微信提醒客户经理
            // 理赔赔付或者拒赔微信提醒提醒客户经理
            // 提交资料提醒机构对接人
            // 资料审核提醒保险业务中心理赔专员
            try{
                if (Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_POST_EXPRESS)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_GUIDED)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.FINISH_STATE_PAYED)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.FINISH_STATE_PAYREJECTED)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED)
                        || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)
                        || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE)
                        || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_CODE)
                ) {
                    log.info("发送理赔推送 bean={}", JSON.toJSONString(dto));
                    String otherExplain = null;
                    if (!StringUtils.isEmpty(dto.getDataJson())) {
                        otherExplain = JSON.parseObject(dto.getDataJson()).getString("otherExplain");
                    }
                    SpringFactoryUtil.getBean(EventBusEngine.class).post(new WxClaimNotifyEvent(
                            dto.getClaimId(),
                            dto.getOCode(),
                            null,
                            otherExplain
                    ));
                }
            } catch(Exception e) {
                log.info("众安理赔，发送理赔推送信息失败");
            }
        } else {
            // 处理远程接口调用
            reportToGtcc(dto, claimDetail, nextStep);
        }
    }

    private void addExpressToGtcc(ProgressDTO dto, WxClaimDetailVO claimDetail) {
        if (Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_POST_EXPRESS)
                && (Objects.equals(dto.getOCode(), ClaimWorkflow.STEP_DATA_HAS_POST_EXPRESS) || Objects.equals(dto.getOCode(), ClaimWorkflow.POST_EXPRESS_INFO))) {
            GtccMailInfoModel gtccMailInfoModel = new GtccMailInfoModel();
            SmClaimReimbursementGtcc reimbursementGtcc = claimReimbursementGtccMapper.selectByClaimId(dto.getClaimId());
            SmClaimExpressVO expressVO = claimMapper.getLastClaimExpressByClaimId(dto.getClaimId());
            if (Objects.isNull(expressVO)) {
                throw new MSBizNormalException("", "未查询到快递信息");
            }
            gtccMailInfoModel.setReportNo(reimbursementGtcc.getReportNo());
            gtccMailInfoModel.setInstSerialNo(UUID.randomUUID().toString().replaceAll("-", ""));
            gtccMailInfoModel.setLogisticsBillNos(Collections.singletonList(expressVO.getExpressNo()));
            gtccMailInfoModel.setOutReportNo(claimDetail.getClaimNo());
            gtccApiService.reportMail(gtccMailInfoModel);

            //进入保司核赔阶段
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setClaimId(dto.getClaimId());
            progressDTO.setSCode(ClaimWorkflow.STEP_TO_PAY);
            progressDTO.setSName("保司核赔");
            progressDTO.setDataJson("{}");
            mapper.insertProgress(progressDTO);
        }
    }

    @Autowired
    private SmOrderApplicantMapper orderApplicantMapper;

    private void reportToGtcc(ProgressDTO dto, WxClaimDetailVO claimDetail, BaseWorkflow.Step nextStep) {
        boolean isOrgAudit = Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
        boolean isSendToHeadQuarter = Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_CHECK_BY_SAFES_CENTER);

        if (isOrgAudit && isSendToHeadQuarter) {
            SmClaimReimbursementGtcc reimbursementGtcc = claimReimbursementGtccMapper.selectByClaimId(dto.getClaimId());
            SmOrderInsured insured = insuredMapper.selectByPrimaryKeyMustExists(claimDetail.getInsuredId());
            List<SmClaimFileUnitVO> fileUnitVOS = claimMapper.listSmClaimFileUnitByClaimIdReal(dto.getClaimId());
            fileUnitVOS.forEach(r -> {
                if (!UrlUtils.isAbsoluteUrl(r.getFileUrl())) {
                    r.setFileUrl(AliYunOssUtil.generatePresignedUri1D(r.getFileUrl()));
                }
            });


            if (StringUtils.isEmpty(reimbursementGtcc.getReportNo())) {
                //第一次报案
                SmOrder order = Optional.ofNullable(orderMapper.listSmOrderByOrderIds(Collections.singletonList(insured.getFhOrderId())))
                        .map(list -> list.get(0)).orElseThrow(() -> new BizException("", "未查询到订单信息"));

                //真实的保单号在sm_order_item表里面
                List<SmOrderItem> orderItems = smOrderItemMapper.selectByOrderId(insured.getFhOrderId());
                SmOrderItem orderItem = Optional.ofNullable(orderItems).orElseGet(ArrayList::new).stream()
                        .filter(item -> Objects.equals(item.getPolicyNo(), insured.getPolicyNo()))
                        .findAny().orElse(null);

                SmProductDetailVO product = productMapper.getProductById(order.getProductId());

                SmOrderApplicant applicant = orderApplicantMapper.selectByOrderId(order.getFhOrderId());

                GtccReportModel reportModel = GtccReportModel.from(applicant, product, reimbursementGtcc, claimDetail, insured, fileUnitVOS, orderItem);
                GtccResult result = gtccApiService.report(reportModel);
                String gtccReportNo = result.getReportNo();
                claimReimbursementGtccMapper.updateReportNoByClaimId(claimDetail.getId(), gtccReportNo, UtilDate.dateToLocalDateTime(reportModel.getReportTime()));
            } else {
                //补充材料
                GtccReportModel reportModel = GtccReportModel.constructModifyFile(reimbursementGtcc, claimDetail, fileUnitVOS);
                gtccApiService.modifyMaterial(reportModel);
            }

            //先默认插入一条保司审核的审批节点
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setClaimId(claimDetail.getId());
            progressDTO.setSCode(ClaimWorkflow.STEP_TO_PAY);
            progressDTO.setSName("保司核赔");
            progressDTO.setDataJson("{}");
            mapper.insertProgress(progressDTO);
            //设置当前步骤为保司核赔中
//                    mapper.updateZaProgress(claimDetail.getId(), ClaimWorkflow.STEP_TO_PAY, dto);
            mapper.updateClaimStatus(
                    claimDetail.getId()
                    , dto.getOCode()
                    , dto.getCreateBy()
                    , dto.getOTime()
                    , nextStep
                    , claimHastenProcessor.calHastenTime(nextStep.getSCode(), dto.getClaimId())
            );

        }
    }

    private void triggerGtccStatus(ProgressDTO dto) {

        throw new BizException("", "国泰api流程不支持该操作");
    }

    @Override
    public List<String> channel() {
        return Collections.singletonList(EnumClaimProcessType.GUO_TAI_API.getCode());
    }

    @Override
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimId(int claimId) {
        List<SmClaimFileUnitVO> existedFiles = claimMapper.listSmClaimFileUnitByClaimId(claimId);

        //获取同类型文件集合
        Map<String, List<ClaimFileSimpleVo>> fileSimpleVoListMap = existedFiles.stream()
                .map(smClaimFileUnitVO -> {
                    ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
                    claimFileSimpleVo.setCfId(smClaimFileUnitVO.getCfId());
                    claimFileSimpleVo.setFileUrl(smClaimFileUnitVO.getFileUrl());
                    claimFileSimpleVo.setNewFlag(smClaimFileUnitVO.getNewFlag());
                    claimFileSimpleVo.setFileTypeCode(smClaimFileUnitVO.getFileTypeCode());
                    claimFileSimpleVo.setFileUniqueCode(smClaimFileUnitVO.getFileUniqueCode());
                    return claimFileSimpleVo;
                })
                .collect(Collectors.groupingBy(ClaimFileSimpleVo::getFileTypeCode));

        Map<String, List<SmClaimFileUnitVO>> existedFileCodeMap = existedFiles.stream()
                .collect(Collectors.groupingBy(SmClaimFileUnitVO::getFileTypeCode));

        GtccFileValidateParams fileValidateParams = constructValidatorParams(claimId);

        List<SmClaimFileUploadConfig> defaultFiles = claimFileUploadConfigMapper.queryByClaimProcessTypeAndRowCode(
                channel().get(0)
                , EnumGtccRiskType
                        .mapByClaimRiskTypeCode(fileValidateParams.getRiskType())
                        .getCode()
        );
        if (CollectionUtils.isEmpty(defaultFiles)) {
            log.warn("理赔-{}找不到文件信息-{}", claimId, JSONObject.toJSONString(fileValidateParams));
            throw new BizException("", "国泰理赔文件信息初始化失败");
        }

        List<GroovyCodeEntity> scriptList =
                claimFileUploadConfigMapper.queryScriptCodeList(
                        "claim",
                        defaultFiles.stream().map(SmClaimFileUploadConfig::getScriptCode).distinct()
                                .collect(Collectors.toList())
                );

        if (CollectionUtils.isEmpty(scriptList)) {
            throw new BizException("", "脚本未发现");
        }

        Map<String, String> scriptCodeMap = scriptList.stream().collect(Collectors.toMap(GroovyCodeEntity::getScriptCode, GroovyCodeEntity::getScript));


        Map<String, SmClaimFileCombVO> defaultMap = defaultFiles.stream()
                .map(x -> {
                    SmClaimFileCombVO combVO = new SmClaimFileCombVO();
                    if (!scriptCodeMap.containsKey(x.getScriptCode())) {
                        throw new BizException("", String.format("脚本未发现-%s", x.getScriptCode()));
                    }

                    String result = (String) groovyHelper.executeScript(
                            scriptCodeMap.get(x.getScriptCode()),
                            String.format("%s:%s", SmConstants.CLAIM_MAIL_SCRIPT, x.getScriptCode()),
                            fileValidateParams
                    );
                    if (Objects.equals(result, "2")) {
                        return null;
                    }
                    combVO.setFileRequire(Objects.equals(result, "1"));

                    combVO.setFileTypeCode(x.getColumnCode());
//                    combVO.setExampleFileUrlDescription(ZaClaimFileType.getByCode(item.getKey()).getExampleFileUrlDescription());
                    combVO.setFileTypeName(EnumGtccFileType.getNameByCode(x.getColumnCode()));
                    return combVO;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SmClaimFileCombVO::getFileTypeCode, Function.identity()));
        for (String fileTypeCode : existedFileCodeMap.keySet()) {
            if (!defaultMap.containsKey(fileTypeCode)) {
                defaultMap.put(
                        fileTypeCode,
                        getSmClaimFileCombVO(fileTypeCode)
                );
            }
        }

        for (String fileCode : defaultMap.keySet()) {
            if (Objects.nonNull(defaultMap.get(fileCode))) {
                defaultMap.get(fileCode).setFileUrls(
                        Optional.ofNullable(fileSimpleVoListMap.get(fileCode))
                                .map(i -> i.stream()
                                        .map(ClaimFileSimpleVo::getFileUrl)
                                        .collect(Collectors.toList()))
                                .orElse(Collections.emptyList())

                );
                if (Objects.nonNull(EnumGtccFileType.getByCode(fileCode))) {
                    defaultMap.get(fileCode).setExampleFileUrlList(EnumGtccFileType.getByCode(fileCode).getExampleFileUrlList());
                }
                defaultMap.get(fileCode).setFileSimpleVoList(
                        CollectionUtils.isEmpty(fileSimpleVoListMap.get(fileCode)) ? Collections.emptyList() : fileSimpleVoListMap.get(fileCode)
                );
            }
        }

        return handleAiResult(defaultMap.values().stream().filter(Objects::nonNull)
                                      .sorted(Comparator.comparingInt(a -> EnumGtccFileType.getByCode(a.getFileTypeCode())
                                              .ordinal())
                                      ).collect(Collectors.toList()), claimId);

    }


    private GtccFileValidateParams constructValidatorParams(Integer claimId) {
        //查找理赔信息
        SmClaim claim = claimMapper.getByClaimId(claimId);
        //查找众安理赔信息
        //根据保单号查询被保人信息
        SmOrderInsured insured = insuredMapper.selectByPrimaryKeyMustExists(claim.getInsuredId());
        SmClaimReimbursementGtcc gtcc = claimReimbursementGtccMapper.selectByClaimId(claimId);

        if (Objects.isNull(gtcc)) {
            throw new BizException("", "国泰理赔信息不存在");
        }

        GtccFileValidateParams fileValidateParams = new GtccFileValidateParams();
        if (!IdcardUtils.validateIdCard15(insured.getIdNumber()) && !IdcardUtils.validateIdCard18(insured.getIdNumber())) {
            throw new BizException("", "被保人身份证异常");
        }
        fileValidateParams.setAge(IdcardUtils.getAgeByIdCard(insured.getIdNumber()));
        fileValidateParams.setRiskType(claim.getRiskType());
        fileValidateParams.setRiskReason(claim.getClaimRiskReason());
        fileValidateParams.setGtccRiskReason(EnumGtccRiskReason.mapByRiskTypeAndReason(claim.getRiskType(), claim.getClaimRiskReason()));
        fileValidateParams.setApplierType(gtcc.getReportorRelationCd());

        return fileValidateParams;
    }


    private SmClaimFileCombVO getSmClaimFileCombVO(String fileTypeCode) {

        SmClaimFileCombVO combVO = new SmClaimFileCombVO();
        combVO.setFileRequire(false);
//        combVO.setExampleFileUrlList(ZaClaimFileType.getByCode(item.getKey()).getExampleFileUrlList());
        combVO.setFileTypeCode(fileTypeCode);
//        combVO.setExampleFileUrlDescription(ZaClaimFileType.getByCode(item.getKey()).getExampleFileUrlDescription());
        combVO.setFileTypeName(EnumGtccFileType.getNameByCode(fileTypeCode));
        return combVO;
    }


    @Override
    public String deFirstRow(String rowName) {
        String rowCode = Arrays.stream(EnumGtccRiskType.values())
                .filter(x -> Objects.equals(x.getDesc(), rowName))
                .findAny().map(EnumGtccRiskType::getCode)
                .orElse(null);
        if (Objects.isNull(rowCode)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("导入行%s不匹配", rowName));
        }

        return rowCode;
    }

    @Override
    public String deFirstColumn(String columnName) {
        String columnCode = Arrays.stream(EnumGtccFileType.values())
                .filter(x -> Objects.equals(x.getName(), columnName))
                .findAny().map(EnumGtccFileType::getCode)
                .orElse(null);

        if (Objects.isNull(columnCode)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("导入列%s不匹配", columnName));
        }

        return columnCode;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveGtccClaimData(WxGtccClaimDTO gtccClaimDTO) {
        log.info("国泰理赔参数：{}", JSONObject.toJSONString(gtccClaimDTO));
        //查找id
        Integer claimId = gtccClaimDTO.getWxClaimDTO().getId();
        SmClaim claim = claimMapper.getByClaimId(claimId);
        if (Objects.isNull(claim)) {
            throw new BizException("", "理赔信息不存在");
        }

        if (Objects.equals(claim.getProcessType(), EnumClaimProcessType.GUO_TAI_API.getCode())) {
            if (ClaimRiskType.gtccExclude().contains(gtccClaimDTO.getWxClaimDTO().getRiskType())) {
                throw new BizException("", "该保单暂不支持所选出险类型");
            }
        }

        checkGtccData(gtccClaimDTO);
        SmClaimReimbursementGtcc reimbursementGtcc = WxGtccClaimDTO.convertToSmClaimReimbursementTtcc(gtccClaimDTO);

        SmClaimReimbursementGtcc existedGtccClaim = claimReimbursementGtccMapper.selectByClaimId(claimId);

        if (Objects.isNull(existedGtccClaim)) {
            reimbursementGtcc.setClaimId(claimId);
            reimbursementGtcc.setCreateBy(HttpRequestUtil.getUserId());
            claimReimbursementGtccMapper.insert(reimbursementGtcc);
        } else {
            reimbursementGtcc.setId(existedGtccClaim.getId());
            claimReimbursementGtccMapper.updateByPrimaryKeySelective(reimbursementGtcc);
        }
        claimService.updateClaim(gtccClaimDTO.getWxClaimDTO());

    }


    private void checkGtccData(WxGtccClaimDTO gtccClaimDTO) {

        if (!StringUtils.isEmpty(gtccClaimDTO.getReportorEmail()) && !RegExUtils.validMail(gtccClaimDTO.getReportorEmail())) {
            throw new BizException("", "邮箱格式不正确，请删除或重新填写！");
        }
    }

    /**
     * 查询详情
     *
     * @param claimId
     * @return
     */
    public WxGtccClaimDetailVO detail(Integer claimId) {

        //查询详情
        WxClaimDetailVO claimDetailVO = claimMapper.getSmClaimById(claimId);
        if (Objects.isNull(claimDetailVO)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR);
        }

        SmClaimProgressVO claimProgressVO = getClaimProgress(claimId);
        if (Objects.nonNull(claimProgressVO.getNextProgress())) {
            List<BaseWorkflow.Option> options = Optional.ofNullable(claimProgressVO.getNextProgress()).map(BaseWorkflow.Step::getOptions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(x -> !org.apache.commons.lang3.StringUtils.equals(x.getOType(), "-6"))
                    .collect(Collectors.toList());
            claimProgressVO.getNextProgress().setOptions(options);
        }
        claimDetailVO.setProgress(claimProgressVO);

        //返回保险公司信息
        SmClaimReimbursementGtcc claimReimbursementGtcc = claimReimbursementGtccMapper.selectByClaimId(claimId);

        WxGtccClaimDetailVO result = new WxGtccClaimDetailVO();

        if (Objects.isNull(claimReimbursementGtcc)) {
            result.setClaimDetailVO(claimDetailVO);
            return result;
        }
        BeanUtils.copyProperties(claimReimbursementGtcc, result);
        result.setClaimFiles(getSmClaimFileCombByClaimId(claimId));
        return result;
    }


    public void handlerGtccReportStatusCallBack(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String outReportNo = null;
        String reportNo = null;
        try{
            String s = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8.toString());
            log.info("国泰报文回调消息-{}", s);


            //
            Map<String, String> rsaJsonObject = JSONObject.parseObject(s, new TypeReference<Map<String, String>>() {
            });

            String sign = rsaJsonObject.get("sign");
            String bizData = rsaJsonObject.get("bizData");

            if (StringUtils.isEmpty(sign) || StringUtils.isEmpty(bizData)) {
                response.getWriter().write(GtccClaimResponse.error("内容不存在", reportNo, outReportNo).toString());
                return;
            }
            boolean verify = GtccSignUtil.rsa256CheckContent(GtccSignUtil.dealParam(rsaJsonObject), sign, gtccApiProperties.getGtccRsaPublicKey(), GtccSignUtil.CHARSET);
            if (!verify) {
                response.getWriter().write(GtccClaimResponse.error("验签失败", reportNo, outReportNo).toString());
                return;
            }


            JSONObject jsonObject = JSONObject.parseObject(bizData);
            String action = jsonObject.getString("action");
            outReportNo = jsonObject.getString("outReportNo");
            reportNo = jsonObject.getString("reportNo");

            if (Objects.equals("4", action)) {
                response.getWriter().write(GtccClaimResponse.ok("成功", reportNo, outReportNo).toString());
                return;
            }

            if (Objects.isNull(claimMapper.getByClaimNo(outReportNo))) {
                response.getWriter().write(GtccClaimResponse.error(String.format("报案编码不存在-%s", outReportNo), reportNo, outReportNo).toString());
                log.warn("报案编码不存在-{}", outReportNo);
            }

            if (StringUtils.isEmpty(action) || Objects.isNull(EnumAction.mapAction(action))) {
                response.getWriter().write(GtccClaimResponse.error(String.format("国泰理赔报文回调，action=%s未对接", action), reportNo, outReportNo).toString());
                log.warn("国泰理赔报文回调，action={}未对接", action);
            }

            lockTemplate.lock(outReportNo, 5);

            callBackHandlerMap.get(EnumAction.mapAction(action)).handleProcess(jsonObject);
            response.getWriter().write(GtccClaimResponse.ok("成功", reportNo, outReportNo).toString());
        } catch(Exception e) {
            log.warn("国泰理赔回调通知处理失败", e);
            response.getWriter().write(GtccClaimResponse.error("失败", reportNo, outReportNo).toString());
        } finally {
            if (!StringUtils.isEmpty(outReportNo)) {
                lockTemplate.unLock(outReportNo);
            }
        }

    }

    public void manualExecute(JSONObject jsonObject) {
        String action = jsonObject.getString("action");
        callBackHandlerMap.get(EnumAction.mapAction(action)).handleProcess(jsonObject);
    }

    HashSet<String> va = Sets.newHashSet(EnumGtccFileType.H.getCode(), EnumGtccFileType.BIRTH_CERT.getCode());

    @Override
    public Boolean checkFiles(int claimId, List<SmClaimFileCombDTO> fileCombDTOS) {
        GtccFileValidateParams params = constructValidatorParams(claimId);

        if (Objects.nonNull(params.getAge()) && params.getAge() < 18) {
            fileCombDTOS.stream().filter(
                    x -> va.contains(x.getFileTypeCode())
            ).filter(x -> CollectionUtils.isNotEmpty(x.getNewFlagFileList())).findAny().orElseThrow(() -> new BizException("", "户口簿或出生证明必须上传一种"));
        }

        return Boolean.TRUE;
    }

    @Override
    public List<SmClaimFileCombVO> getSupplementFileByClaimId(int claimId) {
        return getSmClaimFileCombByClaimId(claimId);
    }


    @Override
    public void saveFiles(int claimId, WxClaimFileCombDTO dto) {
        checkFiles(claimId, dto.getFileCombs());
        List<SmClaimFileUnitDTO> fileUnitDTOS = new ArrayList<>();
        dto.getFileCombs().stream()
                .forEach(fc -> fc.getNewFlagFileList().forEach(newFlagFileForm -> {
                    String url = newFlagFileForm.getFileUrl();
                    SmClaimFileUnitDTO fileUnitDTO = new SmClaimFileUnitDTO();
                    fileUnitDTO.setClaimId(claimId);
                    fileUnitDTO.setFileTypeCode(fc.getFileTypeCode());
                    fileUnitDTO.setFileTypeName(fc.getFileTypeName());
                    fileUnitDTO.setNewFlag(newFlagFileForm.getNewFlag());
                    fileUnitDTO.setFileUniqueCode(newFlagFileForm.getFileUniqueCode());
                    if (UrlUtils.isAbsoluteUrl(url)) {
                        //去掉path 中的 第一个/
                        String substring = URI.create(url).getPath().substring(1);
                        fileUnitDTO.setFileUrl(substring);
                    } else {
                        fileUnitDTO.setFileUrl(url);
                    }

                    fileUnitDTOS.add(fileUnitDTO);
                }));

        mapper.deleteClaimFiles(claimId);
        if (!fileUnitDTOS.isEmpty()) {
            mapper.insertClaimFiles(fileUnitDTOS);
        }
    }
}
