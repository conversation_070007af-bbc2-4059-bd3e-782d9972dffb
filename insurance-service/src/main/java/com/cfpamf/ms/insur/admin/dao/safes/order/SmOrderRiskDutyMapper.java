package com.cfpamf.ms.insur.admin.dao.safes.order;

import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR> 2020/10/10 17:37
 */
@Mapper
public interface SmOrderRiskDutyMapper extends MyMappler<SmOrderRiskDuty> {
    /**
     * 根据订单id获取险种纬度获取保费信息
     * @param fhOrderId
     * @return
     */
    @Select("select fh_order_id  fhOrderId ,risk_id riskId,risk_name riskName,insured_id_number insuredIdNumber," +
            "   insured_period_type insuredPeriodType,insured_period insuredPeriod," +
            "        period_type periodType,payment_period paymentPeriod,payment_period_type paymentPeriodType," +
            "       app_status appStatus,main_insurance mainInsurance, " +
            "        sum(premium) as premium,sum(amount) as amount,sum(refund_amount) as refundAmount,surrender_time surrenderTime,surrender_type surrenderType" +
            "        from sm_order_risk_duty " +
            "        where fh_order_id=#{fhOrderId} and premium>0 and enabled_flag=0 " +
            "        group by fh_order_id ,risk_id,insured_id_number")
    List<SmOrderRiskDuty> listRiskPremiumByFhOrderId(@Param("fhOrderId")String fhOrderId);


    /**
     * 根据订单Id集合查询险种责任列表
     * @param orderIdList
     * @param appStatus
     * @return
     */
    List<SmOrderRiskDuty> listByOrderIdList(@Param("orderIdList")List<String> orderIdList,@Param("appStatus") String appStatus);

    /**
     * 根据订单Id集合查询险种责任列表
     * @param policyNoList
     * @return
     */
    List<SmOrderRiskDuty> listByPolicyNoList(@Param("policyNoList")List<String> policyNoList);

    /**
     * 修改险总为退保
     * @param update
     * @param query
     * @return
     */
    int updateSurrenderByRiskCode(@Param("update") SmOrderRiskDuty update, @Param("query") SmOrderRiskDuty query);

    /**
     * 修改险种状态
     * @param update
     * @return
     */
    int updateRiskState(@Param("update") SmOrderRiskDuty update);

    /**
     * 保单险种退费(全额退)
     * @param orderId 订单Id
     * @param policyStatus 保单状态
     * @return 更新行数
     */
    int policyRefund(@Param("orderId") String orderId,@Param("policyStatus") String policyStatus);
    /**
     * 保单险种重新缴费
     * @param orderId 订单Id
     * @param policyStatus 保单状态
     * @return 更新行数
     */
    int policyRepay(@Param("orderId") String orderId,@Param("policyStatus") String policyStatus);

    @Update("update sm_order_risk_duty set enabled_flag=-1,update_time=now() where fh_order_id=#{orderId} and enabled_flag=0;")
    int logicDelete(@Param("orderId") String orderId);
}
