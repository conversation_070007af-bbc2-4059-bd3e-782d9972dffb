package com.cfpamf.ms.insur.admin.dao.safes.order;

import com.cfpamf.ms.insur.admin.external.whale.model.GscPolicyAllParams;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrderCar;
import com.cfpamf.ms.insur.admin.pojo.po.order.FastOrderInsuredPo;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderNewVO;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.weixin.pojo.po.order.correct.CorrectInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.GroupInsuredFixVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderInsuredBaseInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> 2020/10/10 17:37
 */
@Mapper
public interface SmOrderInsuredMapper extends MyMappler<SmOrderInsured> {

    /**
     * 通过订单id获取
     *
     * @param orderId
     * @return
     */
    default List<SmOrderInsured> selectByOrderId(String orderId) {
        SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setFhOrderId(orderId);
        return select(smOrderInsured);
    }

    /**
     * 保单中心
     *
     * @param policyNo 保单号码
     * @param idNumber 证件号码
     * @return
     */
    default List<SmOrderInsured> selectByPolicyNoAndIdNumber(String policyNo, String idNumber) {
        SmOrderInsured smOrderInsured = new SmOrderInsured();
        if(StringUtils.isNotBlank(idNumber)) {
            smOrderInsured.setIdNumber(idNumber);
        }
        smOrderInsured.setPolicyNo(policyNo);
        return select(smOrderInsured);
    }


    /**
     * 根据保单号查询被保人记录
     * @param policyNo
     * @return
     */
    default List<SmOrderInsured> selectByPolicyNo(String policyNo){
        SmOrderInsured smOrderInsured = new SmOrderInsured();
        smOrderInsured.setPolicyNo(policyNo);
        return select(smOrderInsured);
    }

    default List<SmOrderInsured> selectByPolicyNoList(List<String> policyNoList){
        if (CollectionUtils.isEmpty(policyNoList)) {
            return Collections.emptyList();
        }
        Example example = new Example(SmOrderInsured.class);
        example.createCriteria().andIn("policyNo", policyNoList).andEqualTo("enabledFlag",0);
        return selectByExample(example);
    }

    default SmOrderInsured selectByInsuredId(Integer id){
        if (Objects.isNull(id)) {
            return null;
        }
        SmOrderInsured insured = new SmOrderInsured();
        insured.setId(id);
        insured.setEnabledFlag(0);
        return selectOne(insured);
    }


    /**
     * 获取实体
     *
     * @param fhOrderId
     * @param insuredIdNumber
     * @param policyStatus
     * @return
     */
    SmOrderInsured getByLikeOrderId(@Param("fhOrderId") String fhOrderId, @Param("insuredIdNumber") String insuredIdNumber, @Param("policyStatus") String policyStatus);

    int batchUpdate(List<CorrectInsured> insuredList);

    int batchSurrender(List<CorrectInsured> insureds);

    /**
     * 根据保单号列表查询对应的保司产品编码
     * @param policyNoList
     * @return
     */
    Integer countByFhProductIdAndPolicyNo(@Param("policyNoList") List<String> policyNoList, @Param("fhProductIdList") List<String> fhProductIdList);

    List<GscPolicyAllParams> queryWhaleGscRefreshPolicy(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("start") Integer start,
                                                        @Param("end") Integer end);

    Integer queryWhaleGscRefreshPolicyTotal(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 变更保单号
     *
     * @param sourcePolicyNo
     * @param toPolicyNo
     * @return
     */
    @Update(" UPDATE sm_order_insured set policyNo=#{toPolicyNo} where policyNo=#{sourcePolicyNo} ")
    int updatePolicyNo(@Param("sourcePolicyNo") String sourcePolicyNo, @Param("toPolicyNo") String toPolicyNo);

    @Update("update sm_order_insured set otherIdentifiers = #{claimExemptionState} WHERE fhOrderId = #{fhOrderId} ")
    int updateOtherIdentifiersState(@Param("fhOrderId") String fhOrderId,@Param("claimExemptionState") Integer claimExemptionState);

    @Update("update sm_order_insured set otherIdentifiers = #{claimTerminationState} , appStatus = #{policyStatusCancelSuccess} WHERE fhOrderId = #{fhOrderId}")
    int updateOtherIdentifiersStateAndAppStatus(@Param("fhOrderId") String fhOrderId,@Param("claimTerminationState") Integer claimTerminationState,@Param("policyStatusCancelSuccess") String policyStatusCancelSuccess);

    List<GroupInsuredFixVo> listGroupInsuredFix(@Param("policyNo") String policyNo);

    List<FastOrderInsuredPo> queryFastInsuredListByCodeList(@Param("policyNo") String policyNo,@Param("endorsementList") Collection<String> endorsementList,@Param("branchIdList")Collection<String> branchCodeList);

    List<SmOrderInsured> listGroupInsuredByIdCard(@Param("policyNo") String policyNo,@Param("idCard")String idCard);

    int batchUpdateEPolicyUrl(@Param("data")List<SmOrderInsured> orderInsuredList);

    List<SmOrderInsured> listByEndorsementNo(@Param("policyNo")String policyNo, @Param("endorsementNo")String endorsementNo);

    /**
     * 获取团险新契约被保人基本信息
     * @param policyNo
     * @return
     */
    @Select(" select o.fhOrderId,o.paymentTime,d.policyNo,o.recommendId,ifnull(i.th_endorsement_no,'') as endorsementNo,d.idNumber,d.idType,d.personName as insuredName,d.appStatus " +
            " from sm_order_item i,sm_order_insured d,sm_order o  " +
            " where i.fh_order_id = d.fhOrderId " +
            " and o.fhOrderId = d.fhOrderId " +
            " and i.id_number=d.idNumber " +
            " and i.app_status = d.appStatus " +
            " and i.th_policy_no = #{policyNo} " +
            " and i.enabled_flag = 0 and o.enabled_flag=0 and d.enabled_flag=0 " +
            " and (i.th_endorsement_no = '' or i.th_endorsement_no is null ) ")
    List<OrderInsuredBaseInfoVo>  listNewGroupInsuredByThPolicyNo(@Param("policyNo")String policyNo);

    /**
     * 获取团险批改单被保人基本信息
     * @param policyNo
     * @param endorsementNos
     * @param appStatus
     * @return
     */
    @Select(" <script> " +
            " select o.fhOrderId,o.paymentTime,d.policyNo,o.recommendId,i.th_endorsement_no as endorsementNo,d.idNumber,d.idType,d.personName as insuredName,d.appStatus " +
            " from sm_order_item i,sm_order_insured d ,sm_order o " +
            " where i.fh_order_id = d.fhOrderId " +
            " and o.fhOrderId = d.fhOrderId " +
            " and i.id_number=d.idNumber " +
            " and i.app_status = d.appStatus " +
            " and i.th_policy_no = #{policyNo} " +
            " and i.enabled_flag = 0 and o.enabled_flag=0 and d.enabled_flag=0 " +
            " <if test='appStatus!= null'> " +
            " and d.appStatus=#{appStatus} " +
            " </if> " +
            " and i.th_endorsement_no in " +
            " <foreach collection='endorsementNos' item='endorsementNo' open='(' separator=',' close=')'> " +
            "    #{endorsementNo}" +
            " </foreach> " +
            " </script>")
    List<OrderInsuredBaseInfoVo>  listGroupInsuredByThPolicyNoAndThEndorsementNo(@Param("policyNo")String policyNo, @Param("endorsementNos") List<String> endorsementNos, @Param("appStatus") String appStatus);

    @Select(" <script> " +
            " select o.fhOrderId,o.paymentTime,d.policyNo,o.recommendId,i.th_endorsement_no as endorsementNo,d.idNumber,d.idType,d.personName as insuredName,d.appStatus,o.is_life_service_order as isLifeServicePartner \n" +
            "            from sm_order_insured d" +
            " left join sm_order_item i on i.fh_order_id = d.fhOrderId and i.id_number=d.idNumber and i.app_status = d.appStatus and i.enabled_flag = 0 " +
            " left join sm_order o on o.fhOrderId = d.fhOrderId and o.enabled_flag=0 " +
            "            where d.enabled_flag=0 " +
            " and d.fhOrderId in " +
            " <foreach collection='fhOrderIds' item='fhOrderId' open='(' separator=',' close=')'> " +
            "    #{fhOrderId}" +
            " </foreach> " +
            " </script>")
    List<OrderInsuredBaseInfoVo>  listGroupInsuredByOrderIds(@Param("fhOrderIds") List<String> fhOrderIds);


    /**
     * 获取团险新契约被保人基本信息
     * @param policyNo
     * @return
     */
    @Select(" select select distinct d.* " +
            " from sm_order_item i,sm_order_insured d,sm_order o  " +
            " where i.fh_order_id = d.fhOrderId " +
            " and o.fhOrderId = d.fhOrderId " +
            " and i.id_number=d.idNumber " +
            " and i.app_status = d.appStatus " +
            " and i.th_policy_no = #{policyNo} " +
            " and i.enabled_flag = 0 and o.enabled_flag=0 and d.enabled_flag=0 " +
            " and (i.th_endorsement_no = '' or i.th_endorsement_no is null )")
    List<SmOrderInsured>  listInsuredByThPolicyNo(@Param("policyNo")String policyNo);

    /**
     * 获取团险批改单被保人基本信息
     * @param policyNo
     * @param endorsementNos
     * @return
     */
    @Select(" <script> " +
            " select distinct d.* " +
            " from sm_order_item i,sm_order_insured d ,sm_order o " +
            " where i.fh_order_id = d.fhOrderId " +
            " and o.fhOrderId = d.fhOrderId " +
            " and i.id_number=d.idNumber " +
            " and i.app_status = d.appStatus " +
            " and i.th_policy_no = #{policyNo} " +
            " and i.enabled_flag = 0 and o.enabled_flag=0 and d.enabled_flag=0 " +
            " and i.th_endorsement_no in " +
            " <foreach collection='endorsementNos' item='endorsementNo' open='(' separator=',' close=')'> " +
            "    #{endorsementNo}" +
            " </foreach> " +
            " </script>")
    List<SmOrderInsured>  listInsuredByThPolicyNoAndThEndorsementNo(@Param("policyNo")String policyNo, @Param("endorsementNos") List<String> endorsementNos);

    /**
     * 获取一个整单的被保人，条件是，同一个个证件号码的同一个产品的同一个生效日期
     * @param insuredId
     * @return
     */
    List<SmOrderInsured> getAllByInsuredId(@Param("insuredId") Integer insuredId);

    int updateJobVersion(@Param("entry") SmOrderInsured orderInsured);

    @Update("update sm_order_insured set enabled_flag=-1,update_time=now() where fhOrderId=#{orderId} and enabled_flag=0;")
    int logicDelete(@Param("orderId") String orderId);
}
