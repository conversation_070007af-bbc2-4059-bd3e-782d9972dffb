package com.cfpamf.ms.insur.admin.external.tk;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendTkMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMsgMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderOutType;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.fh.dto.OrderDTO;
import com.cfpamf.ms.insur.admin.external.tk.api.TKMessageBuilder;
import com.cfpamf.ms.insur.admin.external.tk.api.TkCvtHelper;
import com.cfpamf.ms.insur.admin.external.tk.model.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupInsuredInfo;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.aicheck.FamilyMemberQuestionnaireResultDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.IdCardUtils;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.constant.EnumEndor;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.correct.CorrectLogMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OfflinePayDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ApplicantDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.po.order.correct.OrderInsuredCorrectLog;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.chongho.response.GroupUnderwriteResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.item.TKDeductionPersonal;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.item.TKPersonal;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.item.TKPlan;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.request.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.response.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.MemberChange;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.constant.SmConstants.SM_ORDER_API_INVOKE_SUCCESS;

/**
 * 泰康见费接口适配器
 *
 * <AUTHOR>
 */
@Slf4j
@Service("tkPay")
@FieldDefaults(level = AccessLevel.PROTECTED)
public class TkPayOrderServiceAdapter extends TkOrderServiceAdapter {

    @Autowired
    SmOrderExtendTkMapper extendTkMapper;

    @Autowired
    SmProductService productService;

    @Autowired
    private TKMessageBuilder tkBuilder;

    @Autowired
    ObjectMapper jsonMapper;

    @Autowired
    private SmOrderGroupNotifyMapper smOrderGroupNotifyMapper;

    @Autowired
    private SmOrderGroupNotifyMsgMapper smOrderGroupNotifyMsgMapper;

    @Autowired
    private EndorMapper endorMapper;

    @Autowired
    private SmOrderGroupService orderGroupService;

    @Autowired
    private TxServiceManager txServiceManager;


    public TkApiProperties properties() {
        return tkApiProperties;
    }

    @Override
    public OrderPrePayResponse prePayChannelOrder(OrderPrePayRequest request) {
        String orderId = request.getOrderId();
        List<SmOrderListVO> orders = orderMapper.listOrderInsuredDetailByOrderId(orderId);

        if (CollectionUtils.isEmpty(orders)) {
            throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "订单不存在");
        }
        SmOrderListVO mainInsured = orders.get(0);
        SmPlanVO planById = productService.getPlanById(mainInsured.getPlanId());
        TkProductModel tkProductModel = TkProductModel.valueOf(planById.getFhProductId());
        TkPayLinkResp payLink = apiService.getPayLink(orderId, mainInsured.getAppNo(), tkProductModel, mainInsured, HttpRequestUtil.getIpAddr());

        OrderPrePayResponse orderPrePayResponse = new OrderPrePayResponse();
        orderPrePayResponse.setPayUrl(payLink.getPayUrl());
        orderPrePayResponse.setPayId(payLink.getBillno());
        return orderPrePayResponse;
    }

    @Override
    public OrderSubmitResponse submitChannelOrder(OrderSubmitRequest request) {

        String nextNo = orderNoGenerator.getNextNo(EnumChannel.TK_PAY.getCode());
        request.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());

        //如果有只能核保信息
        List<FamilyMemberQuestionnaireResultDTO> dtos = Collections.emptyList();
        if (StringUtils.isNotBlank(request.getQuestionnaireId())) {
            dtos = questionnaireService.listFamilyQuestionnaireRetByQuestionnaireId(
                    Integer.valueOf(request.getQuestionnaireId()), request.getPreOrderId());
        }
        TkProposalResp check = apiService.advanceCheck(request, nextNo, dtos);
        OrderSubmitResponse response = new OrderSubmitResponse();
        response.setAppNo(check.getFamilyProposalNo());
        response.setNoticeCode(SM_ORDER_API_INVOKE_SUCCESS);
        //防止重复 加上泰康前缀
        response.setOrderId(nextNo);
        return response;
    }

    /**
     * 泰康见费渠道 没有查询的接口 只能依赖回调数据
     *
     * @param request
     * @return
     */
    @Override
    public OrderQueryResponse queryChannelOrderInfo(OrderQueryRequest request) {

        return OrderConvertor.mapperOrderQuery4LocalV3(request.getOrderId());
    }

    public TkIssueNotify parseNotify(String content) {

        try {
            TkRespBox<TkIssueNotify> notifyTkRespBox = jsonMapper.readValue(content, new TypeReference<TkRespBox<TkIssueNotify>>() {
            });
            return notifyTkRespBox.getResponseData();
        } catch (IOException e) {
            throw new MSException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "解析泰康回调失败", e);
        }

    }

    /**
     * 团险报价
     *
     * @param req
     * @return
     * <AUTHOR>
     */
    @Override
    public GroupQuoteResponse quotePrice4Group(GroupUnderwriting req) {

        TKGroupQuoteRequest rew = tkBuilder.buildQuoteRequestV1(tkApiProperties, req);

        TkRespBox<TKQuoteResponse> resp = apiService.quotePrice4Group(rew);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        TKQuoteResponse body = resp.getResponseData();
        GroupQuoteResponse data = new GroupQuoteResponse();
        double premium = body.getSumPremium() / 100.0;
        data.setTotalPremium(String.valueOf(premium));
        return data;
    }

    /**
     * 泰康核保(创建团单)流程
     * 固定见费出单
     *
     * @return
     */
    @Override
    public GroupQuoteResponse underwriting(GroupUnderwriting data) {

        Integer productId = data.getProductId();
        SmProductDetailVO product = productService.getProductById(productId);
        if (product == null) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_NOT_ONLINE_201001.getCode(), ExcptEnum.PRODUCT_NOT_ONLINE_201001.getMsg());
        }

        if (StringUtils.isBlank(data.getOrderOutType())) {
            data.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());
        }
        /**
         * 1.重新试算保费
         */
        TKGroupQuoteRequest quoteReq = tkBuilder.buildQuoteRequestV1(tkApiProperties, data);
        TkRespBox<TKQuoteResponse> quoteRespBox = apiService.quotePrice4Group(quoteReq);
        if (!quoteRespBox.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), quoteRespBox.errorMsg());
        }

        /**
         * 2.企业注册
         */
        TKGroupApplicantRequest applicantRequest = tkBuilder.buildApplicant(data);
        TkRespBox<TKEnterpriseRegResponse> applicantResponse = apiService.enterpriseReg(applicantRequest);
        if (!applicantResponse.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), applicantResponse.errorMsg());
        }
        String orgId = applicantResponse.getResponseData().getOrgId();
        /**
         * 2.记录原始数据
         */
        GroupApplicant applicant = data.getApplicant();
        applicant.addField("orgId", orgId);
        applicant.addField("enterpriseCategory", applicant.getEnterpriseCategory());
        applicant.addField("employees", String.valueOf(applicant.getEmployees()));
        String orderId = txServiceManager.excute(() -> super.saveGroupOrder(product, data));
        log.info("开始调用渠道核保接口-{}", orderId);
        /**
         * 3.调用保司接口核保
         */
        TKQuoteResponse quoteResp = quoteRespBox.getResponseData();
        List<PlanPremium> planPremiumList = quoteResp.getPlanPremiumList();
        Map<String, Long> premiumMap = LambdaUtils.safeToMap(planPremiumList, PlanPremium::getPlanNo, PlanPremium::getPremium);

        Function<String, Long> premiumFunc = premiumMap::get;
        TKGroupCreateRequest createRequest = TKMessageBuilder.buildCreatePolicy(orgId, tkApiProperties, data, quoteReq, quoteResp);
        GroupUnderwriteResponse createResponse = createPolicy(createRequest);
        /**
         * 4.状态更新
         */
        txServiceManager.excute(() -> afterApply(orderId, premiumFunc, data, createRequest, createResponse));
        /**
         * 5.回包数据组装
         */
        GroupQuoteResponse response = new GroupQuoteResponse();
        response.setOrderId(orderId);
        response.setProposalNo(createResponse.getProposalNo());
        response.setEffectiveDate(data.getStartTime());
        response.setExpireDate(data.getEndTime());
        return response;
    }

    /**
     * 1.保存投保数据[sm_order_item]
     * 2.更新订单状态
     */
    public void afterApply(String orderId,
                           Function<String, Long> premiumFunc,
                           GroupUnderwriting data,
                           TKGroupCreateRequest createRequest,
                           GroupUnderwriteResponse createResponse) {
        List<TKPersonal> personalPolicyList = createRequest.getPersonalPolicyList();
        List<TKPlan> planList = createRequest.getPlanList();
        Integer productId = data.getProductId();
        Integer planId = data.getProduct().getPlanId();
        String planCode = planList.get(0).getPlanCode();
        List<SmOrderItem> orderItems = new ArrayList<>();
        for (TKPersonal entry : personalPolicyList) {
            SmOrderItem item = new SmOrderItem();
            item.setFhOrderId(orderId);
            item.setActiveBranchId(entry.getChannelPolicyNo());
            item.setAppStatus(SmConstants.ORDER_STATUS_TO_PAY);

            String certNo = entry.getCredentialNo();
            item.setIdNumber(certNo);
            item.setIdType(entry.getCredentialType());
            item.setType(0);
            item.setProductId(productId);
            item.setPlanId(planId);
            item.setPlanCode(planCode);
            item.setQty(1);
            item.setAppStatus(SmConstants.POLICY_STATUS_BLANK);
            Long unitPremium = premiumFunc.apply(entry.getPlanNo());
            BigDecimal premium = new BigDecimal(unitPremium);
            item.setTotalAmount(premium);
            item.setUnitPrice(premium);
            orderItems.add(item);
        }
        int r0 = orderMapper.clearTempItem(orderId);
        int r1 = orderItemMapper.insertList(orderItems);
        log.info("被保人分单数据写入完成:{},{},{}", orderId, r0, r1);

        String appNo = createResponse.getProposalNo();
        if (StringUtils.isNotBlank(appNo)) {
            OrderDTO param = new OrderDTO();
            param.setAppNo(appNo);
            param.setPayStatus(SmConstants.ORDER_STATUS_TO_PAY);
            param.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);
            param.setPayUrl(createResponse.getPayUrl());
            param.addField("payNotifyUrl", createResponse.getPaymentNoticeUrl());
            int rtn = orderMapper.updateOrder(orderId, param);
            log.info("更新投保订单状态完成:{},{}", orderId, rtn);
        }
    }

    /**
     * 核保(数据落地)
     *
     * @param createRequest
     * @return
     */
    private GroupUnderwriteResponse createPolicy(TKGroupCreateRequest createRequest) {
        TkRespBox<TKCreatePolicyResponse> response = apiService.createPolicy(createRequest);
        if (!response.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), response.errorMsg());
        }
        TKCreatePolicyResponse data = response.getResponseData();
        return convert(createRequest.getChannelContractNo(), data);

//        } else {
//            TkRespBox<TKOfflineResponse> response = apiService.createPolicy4Offline(createRequest);
//            if (!response.isSuccess()) {
//                throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), response.errorMsg());
//            }
//            TKOfflineResponse data = response.getResponseData();
//            return convert(createRequest.getChannelContractNo(), data);
//        }
    }

    private GroupUnderwriteResponse convert(String orderId, TKCreatePolicyResponse data) {
        GroupUnderwriteResponse response = new GroupUnderwriteResponse();
        response.setOrderId(orderId);
        response.setProposalNo(data.getProposalNo());
        response.setPayUrl(data.getPayUrl());
        return response;
    }

    private GroupUnderwriteResponse convert(String orderId, TKOfflineResponse data) {
        GroupUnderwriteResponse response = new GroupUnderwriteResponse();
        response.setOrderId(orderId);
        response.setProposalNo(data.getGroupProposalNo());
        response.setPaymentNoticeUrl(data.getGroupPaymentNoticeUrl());
        return response;
    }

    /**
     * 该方法已被废弃，不要再使用
     * 目前泰康团险的api对接都迁移到了保险业务中台
     */
    @Deprecated
    @Override
    public String submitOfflinePay(OfflinePayDTO req) {
        log.info("开始提交线下支付资料-{}，Data:[{}]", req.getOrderId(), req);

        FastOrderDTO order = orderMapper.getFastOrderByOrderId(req.getOrderId());

        if (order == null) {
            throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "订单信息为空");
        }
        TKPayNoticeRequest request = TkCvtHelper.cvtTransferAccountBean(order.getProposalNo(), req);
        TkRespBox<Void> resp = apiService.transferAccount(request);

        if (resp.isSuccess()) {
            log.info("{},线下支付资料提交成功，更新订单状态为支付中", req.getOrderId());
            OrderDTO updateOrder = new OrderDTO();
            updateOrder.setFhOrderId(req.getOrderId());
            updateOrder.setPayStatus(SmConstants.PAY_STATUS_DOING);
            int rtn = orderMapper.updateOrder(req.getOrderId(), updateOrder);
            log.info("{},线下支付资料提交成功，状态更新结果,{}", req.getOrderId(), rtn);
        }
        return order.getOrderId();
    }

    /**
     * 批改试算
     *
     * @param req
     * @return
     */
    @Override
    public GroupEndorResponse endorCalPremium(GroupEndorsement req) {
        return endorCalPremiumV1(req);
    }

    /**
     * 批改试算
     * v1版本：批增和批减是分开计算的
     *
     * @param req
     * @return
     */
    private GroupEndorResponse endorCalPremiumV1(GroupEndorsement req) {
        List<GroupInsured> insureds = req.getInsuredList();
        Map<Integer, List<GroupInsured>> insuredMap = LambdaUtils.groupBy(insureds, GroupInsured::getOpType);
        List<GroupInsured> addList = insuredMap.get(1);
        List<GroupInsured> deductionList = insuredMap.get(2);
        int addSize = addList != null ? addList.size() : 0;
        int deductionSize = deductionList != null ? deductionList.size() : 0;
        if (addSize > 0 && deductionSize > 0) {
            throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "暂不支持同时批增批减");
        }
        if (addSize > 0) {
            return addCalPremium(req);
        }
        if (deductionSize > 0) {
            return dedutionCalPremium(req);
        }
        throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人列表不能为空");
    }

    private GroupEndorResponse dedutionCalPremium(GroupEndorsement req) {
        TKDeductionRequest endor = TkCvtHelper.cvtDeductionCalPremium(req);
        TkRespBox<TKDeductionResponse> response = apiService.calPremium4Deduction(endor);
        if (!response.isSuccess()) {
            throw new MSException("-1", "渠道服务器异常");
        }
        TKDeductionResponse data = response.getResponseData();
        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());
        BigDecimal premium = new BigDecimal(0);
        for (TKDeductionPersonal entry : data.getPersonalPolicyList()) {
            Long refundPremium = entry.getRefundPremium();
            premium = premium.add(new BigDecimal(refundPremium));
        }
        premium = premium.multiply(new BigDecimal(-1));
        premium = TKMessageBuilder.cvtFen2Yuan(premium);
        rtn.setTotalPremium(premium);
        return rtn;
    }

    private GroupEndorResponse addCalPremium(GroupEndorsement req) {
        TKGroupQuoteRequest quoteRequest = tkBuilder.buildAddQuoteRequest(tkApiProperties, req);
        TkRespBox<TKQuoteResponse> quoteResponse = apiService.quotePrice4Group(quoteRequest);
        if (!quoteResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR);
        }
        GroupEndorResponse response = new GroupEndorResponse();
        response.setPolicyNo(req.getPolicyNo());

        TKQuoteResponse quoteBody = quoteResponse.getResponseData();
        Long sumPremium = quoteBody.getSumPremium();
        BigDecimal premium = new BigDecimal(sumPremium);
        premium = premium.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

        response.setTotalPremium(premium);
        return response;
    }

    @Override
    public GroupEndorResponse endorUnderwriting(GroupEndorsement req) {
        return endorCalPremiumV1(req);
    }

    /**
     * 提交批改
     *
     * @param req
     * @return
     */
    @Override
    public GroupEndorResponse endorCommit(GroupEndorsement req) {
        return endorCommitV1(req);
    }

    /**
     * 批改提交
     *
     * @param req
     * @return
     */
    private GroupEndorResponse endorCommitV1(GroupEndorsement req) {

        List<GroupInsured> insureds = req.getInsuredList();
        Map<Integer, List<GroupInsured>> insuredMap = LambdaUtils.groupBy(insureds, GroupInsured::getOpType);
        List<GroupInsured> addList = insuredMap.get(1);
        List<GroupInsured> deductionList = insuredMap.get(2);
        int addSize = addList != null ? addList.size() : 0;
        int deductionSize = deductionList != null ? deductionList.size() : 0;
        if (addSize > 0 && deductionSize > 0) {
            throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "暂不支持同时批增批减");
        }
        if (addSize > 0) {
            return addCommit(req);
        }
        if (deductionSize > 0) {
            return dedutionCommit(req);
        }
        throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人列表不能为空");
    }

    /**
     * 伪造一个批改消息
     *
     * @param req
     * @param getPremium
     * @return
     */
    private ZaGroupEndorsementRes buildMessageForAdd(GroupEndorsement req, Function<String, Long> getPremium) {

        ZaGroupEndorsementRes res = new ZaGroupEndorsementRes();
        res.setCode("0");
        ZaGroupEndorsementInfo data = new ZaGroupEndorsementInfo();
        data.setValidateTime(req.getEffectiveTime());
        List<ZaGroupInsuredInfo> insureds = new ArrayList<>();
        List<GroupInsured> groupInsureds = req.getInsuredList();
        for (GroupInsured entry : groupInsureds) {
            ZaGroupInsuredInfo vo = new ZaGroupInsuredInfo();
            vo.setName(entry.getPersonName());
            vo.setGender(entry.getPersonGender());
            vo.setActiveBranchId(entry.getActiveBranchId());
            vo.setBranchId(entry.getBranchId());
            String idNumber = entry.getIdNumber();
            vo.setCertNo(idNumber);
            vo.setCertType(entry.getIdType());
            vo.setHandleType(String.valueOf(entry.getOpType()));

            String planNo = TKMessageBuilder.genVirtualPlanCode(entry.getOccupationGroup());

            Long premium = getPremium.apply(planNo);
            vo.setTotalPremium(premium == null ? new BigDecimal(0) : TKMessageBuilder.cvtFen2Yuan(new BigDecimal(premium)));
            vo.setIndividualStatus(entry.convertIndividualStatus());

            vo.setOccupationCode(entry.getOccupationCode());
            vo.setOccupationGroup(entry.getOccupationGroup());
            vo.setIsSecurity(entry.getIsSecurity());
            if (entry.getOpType() == 2) {
                vo.setTerminateTime(req.getReductionEffectiveTime());
            }
            if (StringUtils.isBlank(entry.getBirthday())) {
                vo.setBirthday(IdCardUtils.getBirthday(entry.getIdNumber(), "yyyy-MM-dd"));
            } else {
                vo.setBirthday(entry.getBirthday());
            }
            insureds.add(vo);
        }
        data.setValidateTime(req.getAdditionEffectiveTime());
        data.setReductionEffectiveTime(req.getReductionEffectiveTime());
        data.setInsuredList(insureds);
        res.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        res.setResult(data);
        return res;
    }

    /**
     * 伪造一个批改消息
     *
     * @param req
     * @param getPremium
     * @return
     */
    private ZaGroupEndorsementRes buildMessageByDecrease(GroupEndorsement req, Function<String, Long> getPremium) {

        ZaGroupEndorsementRes res = new ZaGroupEndorsementRes();
        res.setCode("0");
        ZaGroupEndorsementInfo data = new ZaGroupEndorsementInfo();
        data.setValidateTime(req.getEffectiveTime());
        List<ZaGroupInsuredInfo> insureds = new ArrayList<>();
        List<GroupInsured> groupInsureds = req.getInsuredList();
        for (GroupInsured entry : groupInsureds) {
            ZaGroupInsuredInfo vo = new ZaGroupInsuredInfo();
            vo.setName(entry.getPersonName());
            vo.setGender(entry.getPersonGender());
            vo.setActiveBranchId(entry.getActiveBranchId());
            vo.setBranchId(entry.getBranchId());
            String idNumber = entry.getIdNumber();
            vo.setCertNo(idNumber);
            vo.setCertType(entry.getIdType());
            vo.setHandleType(String.valueOf(entry.getOpType()));


            Long premium = getPremium.apply(idNumber.toLowerCase());
            vo.setTotalPremium(premium == null ? new BigDecimal(0) : TKMessageBuilder.cvtFen2Yuan(new BigDecimal(premium)));
            vo.setIndividualStatus(entry.convertIndividualStatus());

            vo.setOccupationCode(entry.getOccupationCode());
            vo.setOccupationGroup(entry.getOccupationGroup());
            vo.setIsSecurity(entry.getIsSecurity());
            if (entry.getOpType() == 2) {
                vo.setTerminateTime(req.getReductionEffectiveTime());
            }
            if (StringUtils.isBlank(entry.getBirthday())) {
                vo.setBirthday(IdCardUtils.getBirthday(entry.getIdNumber(), "yyyy-MM-dd"));
            } else {
                vo.setBirthday(entry.getBirthday());
            }
            insureds.add(vo);
        }
        data.setValidateTime(req.getAdditionEffectiveTime());
        data.setReductionEffectiveTime(req.getReductionEffectiveTime());
        data.setInsuredList(insureds);
        res.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        res.setResult(data);
        return res;
    }

    /**
     * 批增提交
     *
     * @param req
     * @return
     */
    private GroupEndorResponse addCommit(GroupEndorsement req) {


        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(req.getOrderId());
        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSException("-1", "订单不存在");
        }
        String endorId = IdGenerator.getNextNo(EnumChannel.TK_PAY.getCode());
        req.setEndorId(endorId);
        /**
         * 1.保司试算保费
         */
        TKGroupQuoteRequest quoteRequest = tkBuilder.buildAddQuoteRequest(tkApiProperties, req);
        TkRespBox<TKQuoteResponse> quoteResponse = apiService.quotePrice4Group(quoteRequest);

        if (!quoteResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), quoteResponse.errorMsg());
        }
        TKQuoteResponse quoteResBody = quoteResponse.getResponseData();
        Date startTime = order.getStartTime();
        Date endTime = order.getEndTime();
        String tkStartTime = DateUtils.format(startTime, "yyyyMMddHHmmss");
        String tkEndTime = DateUtils.format(endTime, "yyyyMMddHHmmss");

        TKGroupEndorRequest addRequest = tkBuilder.buildAddCommitRequest(
                tkStartTime,
                tkEndTime,
                tkApiProperties,
                quoteRequest,
                quoteResBody,
                req);

        List<PlanPremium> personalPolicyList = quoteResBody.getPlanPremiumList();
        final Map<String, Long> premiumMap = LambdaUtils.safeToMap(personalPolicyList, PlanPremium::getPlanNo, PlanPremium::getPremium);
        ZaGroupEndorsementRes res = buildMessageForAdd(req, premiumMap::get);

        /**
         * 2.数据存档
         */
        Endor payment = new Endor();
        payment.setRawOrderId(req.getOrderId());
        payment.setChannel(EnumChannel.TK_PAY.getCode());
        payment.setPolicyNo(req.getPolicyNo());
        payment.setOperaor(HttpRequestUtil.getUserId());
        payment.setOrderId(endorId);
        payment.setStatus(EnumEndor.TO_COMMIT.getCode());
        payment.setOrderType(order.getOrderOutType());
        payment.setEffectiveTime(req.chooseEffectiveTime(1));
        payment.setOpType(1);
        payment.setRefund(req.getRefundInfo() != null ? JSON.toJSONString(req.getRefundInfo()) : null);
        payment.setCreateTime(new Date());
        int notifyId = super.preDumpCorrectMsg(payment, req);

        /**
         * 3.提交批改
         */
        TkRespBox<TKCreatePolicyResponse> commitResponse = apiService.addCommit(addRequest);

        if (commitResponse == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!commitResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), commitResponse.errorMsg());
        }
        Long premium = quoteResBody.getSumPremium();
        BigDecimal premium4Yuan = new BigDecimal(premium).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        TKCreatePolicyResponse createPolicyResponse = commitResponse.getResponseData();

        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());
        rtn.setTotalPremium(premium4Yuan);
        rtn.setEndorStatus(0);
        rtn.setOrderId(endorId);

        GroupNotify.StatusEnum notifyCode = GroupNotify.StatusEnum.UN_INIT;
        payment.setAmount(premium4Yuan);
        payment.setStatus(EnumEndor.COMMITED.getCode());
        payment.setPayUrl(createPolicyResponse.getPayUrl());
        txServiceManager.excute(() -> super.afterCorrectMsg(notifyId, notifyCode, null, payment, res));

        return rtn;
    }

    /**
     * 批减提交
     *
     * @param req
     * @return
     */
    private GroupEndorResponse dedutionCommit(GroupEndorsement req) {
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(req.getOrderId());
        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSException("-1", "订单不存在");
        }
        String endorId = IdGenerator.getNextNo(EnumChannel.TK.getCode());
        req.setEndorId(endorId);
        /**
         * 先试算保费
         */
        TKDeductionRequest quoteRequest = tkBuilder.buildDelQuote(req);
        TkRespBox<TKDeductionResponse> quoteResponse = apiService.calPremium4Deduction(quoteRequest);

        if (!quoteResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), quoteResponse.errorMsg());
        }
        TKDeductionResponse quoteResBody = quoteResponse.getResponseData();
        TKDeductionRequest delCommitRequest = tkBuilder.buildDelCommitRequest(quoteRequest, quoteResBody, req);

        List<TKDeductionPersonal> personalPolicyList = quoteResBody.getPersonalPolicyList();
        final Map<String, Long> premiumMap = LambdaUtils.safeToMap(personalPolicyList, ite -> ite.getCredentialNo().toLowerCase(), TKDeductionPersonal::getRefundPremium);
        ZaGroupEndorsementRes res = buildMessageByDecrease(req, premiumMap::get);
        /**
         * 预存信息
         */
        Endor payment = new Endor();
        payment.setRawOrderId(req.getOrderId());
        payment.setChannel(EnumChannel.TK_PAY.getCode());
        payment.setPolicyNo(req.getPolicyNo());
        payment.setOperaor(HttpRequestUtil.getUserId());
        payment.setOrderId(req.getEndorId());
        payment.setStatus(EnumEndor.TO_COMMIT.getCode());
        payment.setOrderType(order.getOrderOutType());
        payment.setEffectiveTime(req.chooseEffectiveTime(2));
        payment.setOpType(2);
        payment.setRefund(req.getRefundInfo() != null ? JSON.toJSONString(req.getRefundInfo()) : null);
        payment.setCreateTime(new Date());
        int notifyId = super.preDumpCorrectMsg(payment, req);

        /**
         * 提交批改
         */
        TkRespBox<TKDeductionResponse> commitResponse = apiService.deductionCommit(delCommitRequest);

        GroupNotify.StatusEnum notifyCode = GroupNotify.StatusEnum.UN_INIT;
        if (commitResponse == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!commitResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), commitResponse.errorMsg());
        }
        TKDeductionResponse respBody = commitResponse.getResponseData();
        BigDecimal premium = new BigDecimal(quoteResBody.sumRefundPremium());
        premium = premium.multiply(new BigDecimal(-1));
        premium = TKMessageBuilder.cvtFen2Yuan(premium);

        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());

        rtn.setEndorStatus(0);
        rtn.setOrderId(req.getEndorId());
        rtn.setTotalPremium(premium);

        payment.setApplyEndorsementNo(respBody.getGroupAppEndorNo());
        payment.setAmount(premium);
        payment.setStatus(EnumEndor.COMMITED.getCode());
        txServiceManager.excute(() -> super.afterCorrectMsg(notifyId, notifyCode, null, payment, res));

        return rtn;
    }

    @Override
    public InvoiceResponse openInvoice(InvoiceVo req) {
        log.info("[{}]-开始请求众安统一开票流程，Items:[{}]", req.getPolicyNo(), req.getPolicyItemList());

        TKInvoiceRequest request = TKMessageBuilder.buildInvoiceRequest(req);
        TkRespBox<TKInvoiceResponse> resp = apiService.openInvoice(request);
        TKInvoiceResponse data = resp.getResponseData();
        InvoiceResponse rtn = new InvoiceResponse();
        rtn.setInvoiceNo(data.getInvoiceNumber());
        rtn.setInvoiceCode(data.getInvoiceCode());
        rtn.setInvoiceUrl(data.getInvoiceUrl());
        return rtn;
    }

    @Override
    @Deprecated
    public MemberChange memberChange(GroupEndorsement data) {
        String orderId = data.getOrderId();
        String orgId = queryOrgId(orderId);
        String endorId = IdGenerator.getNextNo(EnumChannel.TK_PAY.getCode());
        data.setEndorId(endorId);
        /**
         * 1.预存数据
         */
        Endor endor = txServiceManager.excute(() -> dumpMemberChangeMsg(data));

        TKMemberChangeRequest request = tkBuilder.buildMemberChangeRequest(orgId, tkApiProperties, data);
        TkRespBox<TKMemberChangeResponse> resp = apiService.memberChange(request);
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        TKMemberChangeResponse output = resp.getResponseData();
        String applyEndorsementNo = output.getGroupEndorNo();
        endor.setApplyEndorsementNo(applyEndorsementNo);
        int crudFlag = endorMapper.updateByPrimaryKey(endor);
        log.warn("替换人流程结束，数据更新结果：{}", crudFlag);
        MemberChange vo = new MemberChange();
        vo.setPolicyNo(data.getPolicyNo());
        vo.setOrderId(data.getOrderId());
        return vo;
    }

    /**
     * 预存人员替换信息
     *
     * @param data
     */
    private Endor dumpMemberChangeMsg(GroupEndorsement data) {
        /**
         * TODO：数据规则校验
         */
        List<GroupInsured> insuredList = data.getInsuredList();
        List<OrderInsuredCorrectLog> logs = insuredList.stream()
                .map(entry -> {
                    OrderInsuredCorrectLog log = new OrderInsuredCorrectLog();
                    log.setOrderId(data.getEndorId());
                    log.setIdNumber(entry.getIdNumber());
                    log.setIdType(entry.getIdType());
                    log.setBranchId(entry.getBranchId());
                    log.setOccupationCode(entry.getOccupationCode());
                    log.setOccupationGroup(entry.getOccupationGroup());
                    return log;
                })
                .collect(Collectors.toList());
        correctLogMapper.insertList(logs);

        Endor endor = new Endor();
        endor.setOrderId(data.getEndorId());
        endor.setRawOrderId(data.getOrderId());
        endor.setAmount(new BigDecimal(0));
        endor.setOpType(4);
        endor.setChannel(data.getChannel());
        endor.setEffectiveTime(data.getEffectiveTime());
        endor.setOperaor(HttpRequestUtil.getUserId());
        endor.setPolicyNo(data.getPolicyNo());
        endor.setStatus(0);
        endorMapper.insertSelective(endor);
        return endor;
    }

    @Autowired
    private CorrectLogMapper correctLogMapper;

    @Autowired
    private WxOrderMapper wxOrderMapper;

    private String queryOrgId(String orderId) {
        ApplicantDTO applicant = wxOrderMapper.queryApplicant(orderId);
        if (applicant == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单不存在或投保人信息为空");
        }
        return applicant.queryField("orgId", String.class);
    }


}
