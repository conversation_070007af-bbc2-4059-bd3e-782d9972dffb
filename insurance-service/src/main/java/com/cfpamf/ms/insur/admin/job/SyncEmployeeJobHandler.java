package com.cfpamf.ms.insur.admin.job;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.bms.facade.vo.FdSimpleUserVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.pojo.dto.UserDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.SmAgentService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.base.util.XxlLogger;
import com.github.pagehelper.PageInfo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 已经停了
 *
 * @description: 与运营平台同步员工信息最新数据
 * @author: zhangnayi
 * @create: 2018-07-30 15:10
 *
 * use {@link com.cfpamf.ms.insur.admin.job.SyncUserPostJobHandler#execute(String) instead}
 **/
@Slf4j
@Component
@Deprecated
//@JobHandler(value = "sync-employee")
public class SyncEmployeeJobHandler {

    /**
     * OmsService
     */
    @Lazy
    @Autowired
    private BmsService omsService;

    /**
     * SmAgentService
     */
    @Lazy
    @Autowired
    private SmAgentService agentService;

    public void execute() {
        UserService userService = SpringFactoryUtil.getBean(UserService.class);
        XxlLogger.info(log, "开始与运营平台同步员工信息最新数据");

        XxlLogger.info(log, "get运营平台员工信息最新数据");
        int pageSize = 1000000;
        PageInfo<FdSimpleUserVO> bmsPageInfo = omsService.getSimpleOrgUsers(1, pageSize);
        List<FdSimpleUserVO> bmsUsers = bmsPageInfo.getList()
                .stream()
                .filter(b -> !StringUtils.isEmpty(b.getJobNumber()))
                .collect(Collectors.toList());

        //可能出现工号重复
        Map<String, List<FdSimpleUserVO>> tempGroup = bmsUsers
                .stream()
                .collect(Collectors.groupingBy(FdSimpleUserVO::getJobNumber));

        Map<String, FdSimpleUserVO> bmsUserMap = new HashMap<>();
        // 离职用户组
        tempGroup.forEach((key, value) -> {
            if (value.size() > 1) {
                log.info("同步bms员工数据出现工号重复数据:{}", value);
                value.sort(Comparator.comparing(FdSimpleUserVO::getUserId).reversed());
            }
            bmsUserMap.put(key, value.get(0));
        });
        XxlLogger.info(log, "get保险系统员工信息最新数据");
        List<UserVO> insUsers = userService.getUserList(null)
                .stream()
                .filter(iu -> !StringUtils.isEmpty(iu.getUserId()))
                .collect(Collectors.toList());
        Map<String, UserVO> insUserMap = insUsers.stream()
                .collect(Collectors.toMap(t -> t.getUserId().toUpperCase(), t -> t));

        UserDTO insUserDTO = new UserDTO();
        Set<String> hasSyncUserIds = new HashSet<>();
        bmsUsers.forEach(bmu -> {
            String userId = bmu.getJobNumber();
            if (userId != null) {
                userId = userId.toUpperCase();
            }
            if (!hasSyncUserIds.contains(userId)) {
                UserVO insUserVO = insUserMap.get(userId);

                // 更新已经同步的员工信息
                if (insUserVO != null) {
                    copyBmsUserInfo(insUserDTO, bmu, insUserVO.getUserType());
                    userService.updateEmployeeBaseInfo(insUserVO.getId(), insUserDTO);
                }// 更新新建新员工
                else {
                    copyBmsUserInfo(insUserDTO, bmu, SmConstants.USER_TYPE_EMPLOYEE);
//                    // 查询是否存在相同手机号
//                    AuthUserVO sameMobileUserVO = userService.getAuthUserByUserMobile(bmu.getMobile());
//                    // 不存在相同手机号
//                    if (sameMobileUserVO == null) {
                    userService.insertUser(insUserDTO);
                    // 新员工入职自动开通代理人账号
                    // userService.openAgentAccountByEmployeeUserId(bmu.getJobNumber());
//                    }
//                    // 存在相同手机号
//                    else {
//                        userService.updateEmployeeBaseInfo(sameMobileUserVO.getId(), insUserDTO);
//                        // 代理人成为内部员工逻辑
//                        if (Objects.equals(sameMobileUserVO.getUserType(), "agent")) {
//                            agentService.updateAgentEmployeeInfo(bmu.getMobile(), bmu.getJobNumber());
//                        }
//                    }
                }
                hasSyncUserIds.add(userId);
            }
        });
        XxlLogger.info(log, "运营平台同步员工处理离职员工数据");
        // bms没有同步到的员工 按员工离职处理
        insUsers.forEach(iu -> {
            if (bmsUserMap.get(iu.getUserId()) == null) {
                userService.updateEmployeeStatus(iu.getId(), 8);
            }
        });

        //更新离职数据
        List<String> collect = bmsUserMap.values().stream()
                .filter(user -> Objects.equals(user.getEmployeeStatus(), "8"))
                .map(u -> u.getJobNumber().toUpperCase()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            userService.batchUpdateEmployeeStatus(collect, 8);
        }
        XxlLogger.info(log, "完成与运营平台同步员工信息最新数据 + 同步数据数量=" + bmsUsers.size());
        //return SUCCESS;
    }

    private void copyBmsUserInfo(UserDTO dto, FdSimpleUserVO bu, String userType) {
        dto.setRegionName(bu.getRegionName());
        if (!Objects.equals(bu.getRegionName(), BaseConstants.ORG_REGION_HQ)) {
            dto.setOrganizationName(bu.getOrgName());
        } else {
            dto.setOrganizationName(BaseConstants.ORG_BRANCH_HQ);
        }
        dto.setUserMobile(bu.getMobile());
        dto.setUserId(bu.getJobNumber().toUpperCase());
        dto.setOrganizationFullName(bu.getOrgName());
        dto.setUserType(userType);
        dto.setUserName(bu.getEmployeeName());
        dto.setBatchNo(bu.getBatchNo());
        dto.setPostCode(bu.getPostCode());
        dto.setPostName(bu.getPostName());
        dto.setOrgPath(bu.getOrgPath());
        dto.setStatus(bu.getEmployeeStatus());
        dto.setHrOrgId(bu.getHrOrgId());
        dto.setRegionCode(bu.getRegionCode());
        dto.setOrgCode(bu.getOrgCode());
        dto.setUserIdCard(bu.getEmployeeIdCard());
        dto.setServiceType(bu.getServiceType());
    }
}
