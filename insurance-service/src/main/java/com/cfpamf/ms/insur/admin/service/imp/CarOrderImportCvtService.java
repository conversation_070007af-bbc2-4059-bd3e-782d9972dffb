package com.cfpamf.ms.insur.admin.service.imp;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhOrderInfo;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProduct;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.pojo.convertor.CarOrderImportConvert;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCarOrderExcelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderPolicyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.*;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 车险订单转换
 * @date 2021/5/21 11:42 上午
 * @Version 1.0
 */
@Slf4j
@Component
public class CarOrderImportCvtService {
    static DateTimeFormatter FMT_SUB = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");



    /*****************  begin  *********************************************************/
    /**
     * 转换excel 成对应的insert对象【沿用以前的下单的对象】
     *
     * @param validSuccessData
     * @param operator
     * @return
     */
    public  List<SmCreateOrderSubmitRequest> mapperExcel2Create(String channel,List<SmCarOrderExcelDTO> validSuccessData, String operator){
        if (CollectionUtils.isEmpty(validSuccessData)) {
            return Collections.emptyList();
        }
        List<String> recommendUserIdList = new ArrayList<>();
        //根据订单分组
        validSuccessData.stream().forEach(smCarOrderExcelDTO->{
            recommendUserIdList.add(smCarOrderExcelDTO.getRecommendUserId());
        });

        List<UserPost> userPostList = listUserPostByUserIds(recommendUserIdList);

        List<SmCreateOrderSubmitRequest> maps = new ArrayList<>();
        validSuccessData.stream().forEach(smCarOrderExcelDTO->{
            maps.add(excelToSubmitRequest(channel,smCarOrderExcelDTO,userPostList,operator));
        });
        return maps;
    }

    public  SmCreateOrderSubmitRequest excelToSubmitRequest(String channel,SmCarOrderExcelDTO excelDTO,List<UserPost> userPostList, String operator){
        //取第一个被保人信息
        //SmCarOrderExcelDTO first = smCarOrderExcelDTOS.get(0);

        //初始化
        SmCreateOrderSubmitRequest createDto = initSmCreateOrderSubmitRequest();
        createDto.setFhOrderId(excelDTO.getOrderNo());
        createDto.setOrderDbExists(excelDTO.isPolicyDbExists());
        FhOrderInfo fhOrderInfo = new FhOrderInfo();
        //excel文件中的订单金额=保费+车船税，但是佣金 = 保费*佣金比例，所以这个订单金额取保费
        fhOrderInfo.setTotalAmount(excelDTO.getPremium());
        fhOrderInfo.setStartTime(excelDTO.getStartTime());

        String applyTimeStr = excelDTO.getApplyTime();
        fhOrderInfo.setSubmitTime(applyTimeStr);

        Date applyTime = LocalDateUtil.parseTime(applyTimeStr);
        fhOrderInfo.setApplyTime(applyTime);

        fhOrderInfo.setEndTime(excelDTO.getEndTime());
        fhOrderInfo.setPaymentTime(excelDTO.getPaymentTime());

        FhProduct product = new FhProduct();
        product.setRecommendId(excelDTO.getRecommendUserId());
        createDto.setProductInfo(product);

        //这两个没有
        fhOrderInfo.setUnderWritingAge("");
        fhOrderInfo.setValidPeriod(excelDTO.getValidPeriod()+"");
        createDto.setOrderInfo(fhOrderInfo);
        createDto.setPlanId(excelDTO.getPlanId());
        createDto.setProductId(excelDTO.getProductId()+"");
        createDto.setQty(1);
        createDto.setCommissionId(excelDTO.getCommissionId());

        //因为可以多次导入 不知道单价 每次导入之后做一次计算
        createDto.setUnitPrice(BigDecimal.ZERO);
        createDto.setChannel(channel);
        //推荐人
        addRecommondUser(createDto,userPostList,excelDTO.getRecommendUserId());
        //投保人
        createFhProposer(createDto,excelDTO);
        //被保人
        createInsureder(createDto, excelDTO);
        //车辆信息
        createDto.setCarInfo(CarOrderImportConvert.CVT.carInfoDto(excelDTO));

        return createDto;
    }

    public   SmCreateOrderSubmitRequest initSmCreateOrderSubmitRequest(){
        SmCreateOrderSubmitRequest createDto = new SmCreateOrderSubmitRequest();
        //这个字段没用
        createDto.setOrderState("0");
        createDto.setNoticeCode("");
        //createDto.setSubChannel(EnumOrderSubChannel.IMPORT.getCode());
        createDto.setNoticeMsg("");
        // 初始化订单支付状态为已支付
        createDto.setPayStatus(SmConstants.ORDER_STATUS_PAYED);



        return createDto;
    }
    //推荐人
    public  void addRecommondUser(SmCreateOrderSubmitRequest createDto,List<UserPost> userPostList,String recommendId){
        log.info("从数据库获取推荐人信息|{}",recommendId);
        if (!StringUtils.isEmpty(recommendId)) {
            Optional<UserPost> userPostOpt = userPostList.stream().filter(p -> Objects.equals(p.getJobNumber(), recommendId)).findFirst();
            if (userPostOpt.isPresent()) {
                UserPost userPost = userPostOpt.get();
                log.info("从数据库获取用户职位信息|{}",userPost);
                createDto.setRecommendJobCode(userPost.getJobCode());
                createDto.setRecommendMainJobNumber(userPost.getMainJobNumber());
                createDto.setRecommendOrgCode(userPost.getOrgCode());
                createDto.setCustomerAdminJobCode(userPost.getJobCode());
                createDto.setCustomerAdminMainJobNumber(userPost.getMainJobNumber());
                createDto.setCustomerAdminOrgCode(userPost.getOrgCode());
            }
        }
    }
    //投保人
    public  void createFhProposer(SmCreateOrderSubmitRequest createDto,SmCarOrderExcelDTO first){
        FhProposer fhProposer = new FhProposer();
        fhProposer.setPersonName(first.getApplicantName());
        fhProposer.setIdType(first.getApplicantIdType());
        fhProposer.setIdNumber(first.getApplicantIdNumber());
        fhProposer.setEmail(first.getApplicantEmail());
        createDto.setProposerInfo(fhProposer);
    }
    //被保人
    public  void createInsureder(SmCreateOrderSubmitRequest createDto, SmCarOrderExcelDTO excel){
        //初始化 被保人
        /*List<FhInsuredPerson> insuredPeople = smOrderExcelDTOS.stream()
                .map(excel -> {*/
                    FhInsuredPerson insuredPerson = new FhInsuredPerson();
                    insuredPerson.setPersonName(excel.getInsuredName());
                    insuredPerson.setRelationship(excel.getInsuredRelationship());
                    insuredPerson.setIdType(excel.getInsuredIdType());
                    insuredPerson.setIdNumber(excel.getInsuredIdNumber());
                    insuredPerson.setAppStatus(excel.getAppStatus());
                    insuredPerson.setDownloadURL(excel.getPolicyUrl());
                    insuredPerson.setPolicyNo(excel.getPolicyNo());

                    if (Objects.equals(SmConstants.POLICY_STATUS_CANCEL_SUCCESS, excel.getAppStatus())) {
                        insuredPerson.setSurrenderTime(new Date());
                    }
                    /*return insuredPerson;
                }).collect(Collectors.toList());*/
        createDto.setInsuredPerson(Collections.singletonList(insuredPerson));
    }
    /*****************  end *********************************************************/
    /**
     * 转换excel 成对应的车险订单
     *
     * @param validSuccessData
     * @param operator
     * @return
     */
    public  List<AutoOrderDTO> mapperExcel2CreateAutoOrder( String channel,List<SmCarOrderExcelDTO> validSuccessData, String operator){
        if (CollectionUtils.isEmpty(validSuccessData)) {
            return Collections.emptyList();
        }
        List<Integer> planIds = validSuccessData.stream().map(SmCarOrderExcelDTO::getPlanId).distinct().collect(Collectors.toList());
        List<String> recommendUserIdList = new ArrayList<>();
        validSuccessData.stream().forEach(smCarOrderExcelDTO->{
            recommendUserIdList.add(smCarOrderExcelDTO.getRecommendUserId());
        });

        List<UserPost> userPostList = listUserPostByUserIds(recommendUserIdList);

        List<AutoOrderDTO> list = new ArrayList<>();
        validSuccessData.stream().forEach(smCarOrderExcelDTO->{
            list.add(excel2AutoOrder(channel,smCarOrderExcelDTO,operator));
        });
        return list;
    }


    public  AutoOrderDTO excel2AutoOrder(String channel,SmCarOrderExcelDTO smCarOrderExcelDTO, String operator){
        log.info("创建订单开始,订单号：{},保单号:{}",smCarOrderExcelDTO.getOrderNo(),smCarOrderExcelDTO.getPolicyNo());
        AutoOrderDTO autoOrderDTO = new AutoOrderDTO();
        autoOrderDTO.setOrderDbExists(smCarOrderExcelDTO.isPolicyDbExists());
        autoOrderDTO.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        autoOrderDTO.setOrderNo(smCarOrderExcelDTO.getOrderNo());
        //车辆信息
        AutoOrderCar autoOrderCar = CarOrderImportConvert.CVT.cvtAutoOrderCar(smCarOrderExcelDTO);
        if(autoOrderCar.getIsTransfer()==null) {
            autoOrderCar.setIsTransfer("否");
        }
        autoOrderDTO.setCar(autoOrderCar);

        //订单信息
        autoOrderDTO.setOrder(createAutoOrder(channel,smCarOrderExcelDTO));
        //订单人员信息
        autoOrderDTO.setPeople(createOrderPerson(smCarOrderExcelDTO));
        //保单信息
        autoOrderDTO.setPolicies(createAutoPolicy(smCarOrderExcelDTO));
        //收获人信息
        autoOrderDTO.setReceiver(createAutoOrderReceiver(smCarOrderExcelDTO));

        log.info("车险信息(订单号：{},保单号:{} )创建完毕",smCarOrderExcelDTO.getOrderNo(),smCarOrderExcelDTO.getPolicyNo());
        return  autoOrderDTO;
    }
    public  AutoOrder createAutoOrder(String channel,SmCarOrderExcelDTO excelDTO){
        log.info("创建车险订单信息开始");
        AutoOrder order = new AutoOrder();
        //产品信息
        order.setPlanId(excelDTO.getPlanId());
        order.setProductId(excelDTO.getProductId());
        order.setSubmitTime(LocalDateTime.parse(excelDTO.getApplyTime(),FMT_SUB));
        order.setPaymentTime(LocalDateTime.parse(excelDTO.getPaymentTime(),FMT_SUB));
        order.setChannel(channel);
        order.setSubChannel(excelDTO.getSubChannel());
        //订单信息
        order.setOrderNo(excelDTO.getOrderNo());
        order.setCommissionId(excelDTO.getCommissionId());
        order.setQty(1);
        order.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        order.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        order.setRecommendId(excelDTO.getRecommendUserId());
        order.setCustomerAdminId(excelDTO.getRecommendUserId());

        //总保额
        order.setAmount(excelDTO.getInsuredAmount());
        //保费总额
        order.setPremium(excelDTO.getPremium());
        //订单金额
        order.setTotalAmount(excelDTO.getAmount());
        //车船税
        order.setTax(excelDTO.getCarBoatTax());

        log.info("创建车险订单信息结束");
        return order;
    }

    /**
     * 车险订单人员信息
     * @param excelDTO
     * @return
     */
    public  List<AutoOrderPerson>  createOrderPerson(SmCarOrderExcelDTO excelDTO){
        log.info("创建车险订单人员信息开始");
        List<AutoOrderPerson> list = new ArrayList<>();
        //投保人
        AutoOrderPerson applicant = new AutoOrderPerson();
        applicant.setPersonFlag("1");
        applicant.setIdType(excelDTO.getApplicantIdType());
        applicant.setIdNumber(excelDTO.getApplicantIdNumber());
        applicant.setPersonName(excelDTO.getApplicantName());
        applicant.setEmail(excelDTO.getApplicantEmail());
        applicant.setOrderNo(excelDTO.getOrderNo());
        list.add(applicant);
        //被保人
        AutoOrderPerson insure = new AutoOrderPerson();
        insure.setPersonFlag("2");
        insure.setIdType(excelDTO.getInsuredIdType());
        insure.setIdNumber(excelDTO.getInsuredIdNumber());
        insure.setPersonName(excelDTO.getInsuredName());
        if(Objects.equals(excelDTO.getApplicantIdNumber(),excelDTO.getInsuredIdNumber())){
            insure.setEmail(excelDTO.getApplicantEmail());
        }
        insure.setOrderNo(excelDTO.getOrderNo());
        list.add(insure);
        //车主
        AutoOrderPerson owner = new AutoOrderPerson();
        owner.setPersonFlag("6");
        owner.setIdType(excelDTO.getOwnerIdType());
        owner.setIdNumber(excelDTO.getOwnerIdNumber());
        owner.setPersonName(excelDTO.getOwnerPersonName());
        owner.setOrderNo(excelDTO.getOrderNo());
        if(Objects.equals(excelDTO.getApplicantIdNumber(),excelDTO.getOwnerIdNumber())){
            owner.setEmail(excelDTO.getApplicantEmail());
        }
        list.add(owner);

        log.info("创建车险订单人员信息结束");
        return list;
    }

    /**
     * 保单信息
     * @param excelDTO
     * @return
     */
    public List<AutoOrderPolicyDTO> createAutoPolicy(SmCarOrderExcelDTO excelDTO){
        log.info("创建车险保单信息开始");
        AutoOrderPolicyDTO dto = new AutoOrderPolicyDTO();
        AutoOrderPolicy autoOrderPolicy = new AutoOrderPolicy();
        autoOrderPolicy.setOrderNo(excelDTO.getOrderNo());
        //保单号
        autoOrderPolicy.setPolicyNo(excelDTO.getPolicyNo());
        //因为套交保单已经拆分，所以保单号就是子保单号
        autoOrderPolicy.setSubPolicyNo(excelDTO.getPolicyNo());
        autoOrderPolicy.setStartDate(LocalDateTime.parse(excelDTO.getStartTime(),FMT_SUB));
        autoOrderPolicy.setEndDate(LocalDateTime.parse(excelDTO.getEndTime(),FMT_SUB));
        //保额
        autoOrderPolicy.setAmount(excelDTO.getInsuredAmount());
        autoOrderPolicy.setPolicyUrl(excelDTO.getPolicyUrl());
        //保费
        autoOrderPolicy.setPremium(excelDTO.getPremium());

        autoOrderPolicy.setRiskCode(excelDTO.getFhProductId());
        autoOrderPolicy.setRiskName(excelDTO.getRiskName());
        autoOrderPolicy.setPolicyState(excelDTO.getAppStatus());
        autoOrderPolicy.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        autoOrderPolicy.setPlanId(excelDTO.getPlanId());
        autoOrderPolicy.setIssueDate(LocalDateTime.parse(excelDTO.getApplyTime(),FMT_SUB));

        AutoOrderRisk risk = new AutoOrderRisk();
        risk.setOrderNo(excelDTO.getOrderNo());
        risk.setPolicyNo(excelDTO.getOrderNo());
        risk.setSubPolicyNo(excelDTO.getOrderNo());
        risk.setRiskCode(excelDTO.getFhProductId());
        risk.setRiskName(excelDTO.getRiskName());
        risk.setAmount(excelDTO.getInsuredAmount());
        risk.setPremium(excelDTO.getPremium());

        dto.setRiskList(Collections.singletonList(risk));
        dto.setPolicy(autoOrderPolicy);

        log.info("创建车险保单信息结束");
        return Collections.singletonList(dto);

    }

    /**
     * 接收者信息
     * @param excelDTO
     * @return
     */
    public AutoOrderReceiver createAutoOrderReceiver(SmCarOrderExcelDTO excelDTO){
        log.info("创建车险订单收获人信息：AutoOrderReceiver");
        return CarOrderImportConvert.CVT.cvtAutoOrderReceiver(excelDTO);
    }




    public  List<UserPost> listUserPostByUserIds(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        UserService userService = SpringFactoryUtil.getBean(UserService.class);
        return userService.listUserPostByUserIds(userIdList);
    }
}
