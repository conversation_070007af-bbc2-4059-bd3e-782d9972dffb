package com.cfpamf.ms.insur.admin.service.impl;

import com.cfpamf.ms.insur.admin.dao.safes.SmPolicyCacheMapper;
import com.cfpamf.ms.insur.admin.event.OrderPolicyUrlChangeEvent;
import com.cfpamf.ms.insur.admin.pojo.po.SmPolicyCache;
import com.cfpamf.ms.insur.admin.service.SmPolicyCacheService;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class SmPolicyCacheServiceImpl implements SmPolicyCacheService {
    @Autowired
    private SmPolicyCacheMapper policyCacheMapper;

    @Transactional(rollbackFor = Exception.class)
    public void savePolicyCache(OrderPolicyUrlChangeEvent event,SmPolicyCache smPolicyCache,String ossKey){
        policyCacheMapper.insertOnDuplicate(smPolicyCache);
        policyCacheMapper.updatePolicyUrl(event.getOrderId(), event.getPolicyNo(),
                "https://" + AliYunOssUtil.ALIYUN_OSS_BUCKET + ".oss-cn-beijing.aliyuncs.com/" + ossKey);
    }
}
