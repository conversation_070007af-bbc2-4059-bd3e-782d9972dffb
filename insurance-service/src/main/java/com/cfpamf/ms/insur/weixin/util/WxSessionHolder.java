package com.cfpamf.ms.insur.weixin.util;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 读取微信当前用户信息
 *
 * <AUTHOR>
 */
public class WxSessionHolder {

    private static final Logger log = LoggerFactory.getLogger(WxSessionHolder.class);

    public static RedisUtil redisUtil = SpringFactoryUtil.getBean(RedisUtil.class);

    public static WxSessionVO getSession() {
        String wxOpenId = getRequest().getHeader(BaseConstants.WX_HTTP_HEAD_OPENID);
        String authorization = getRequest().getHeader(BaseConstants.WX_HTTP_HEAD_AUTH);
        if (StringUtils.isEmpty(wxOpenId)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}", wxOpenId, authorization);
            log.warn("微信用户token失效 user-agent={}, referer={}", HttpRequestUtil.getUserAgent(), HttpRequestUtil.getReferer());
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        String json = redisUtil.get(wxOpenId).toString();
        if (!StringUtils.isEmpty(json)) {
            WxSessionVO session = JSON.parseObject(json, WxSessionVO.class);
            if (session == null || !Objects.equals(session.getAuthorization(), authorization)) {
                log.warn("微信用户token失效 wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
                log.warn("微信用户token失效 user-agent= {}, referer={}", HttpRequestUtil.getUserAgent(), HttpRequestUtil.getReferer());
                throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
            }
            return session;
        }
        return null;
    }

    /**
     * 获取session
     *
     * @return
     */
    private static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }
}
