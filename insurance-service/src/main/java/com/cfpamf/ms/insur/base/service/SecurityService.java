package com.cfpamf.ms.insur.base.service;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.RSAUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * @description: 安全加密service
 * @author: zhangnayi
 * @create: 2018-08-03 11:08
 **/
@Slf4j
@Service
public class SecurityService {

    /**
     * redis缓存
     */
    @Autowired
    private RedisUtil<String, String> cache;

    /**
     * 分布式锁
     */
    @Autowired
    private DLockTemplate dLockTemplate;

    /**
     * 解密页面传入的密文
     *
     * @param cipher
     * @return
     * @throws NoSuchAlgorithmException
     */
    public String decryptCipher(String cipher) {
        try {
            String privateKey = getPrivateKey();
            return new String(RSAUtil.decryptByPrivateKey(RSAUtil.decryptBASE64(cipher), privateKey), StandardCharsets.UTF_8.name());
        } catch (Exception e) {
//            throw new BizException(ExcptEnum.DECRYPT_ERROR_100000, e);
            throw new BizException(ExcptEnum.PARAMS_ERROR, e);
        }
    }

    /**
     * 加密公钥获取
     *
     * @return
     * @throws NoSuchAlgorithmException
     */
    public String getPublicKey() throws NoSuchAlgorithmException {
        String publicKey = cache.get(BaseConstants.PUB_KEY);
        if (!StringUtils.isEmpty(publicKey)) {
            return publicKey;
        }
        initRsaKeyPair();
        return cache.get(BaseConstants.PUB_KEY);
    }

    /**
     * 加密私钥获取
     *
     * @return
     * @throws NoSuchAlgorithmException
     */
    private String getPrivateKey() throws NoSuchAlgorithmException {
        String privateKey = cache.get(BaseConstants.PRI_KEY);
        if (!StringUtils.isEmpty(privateKey)) {
            return privateKey;
        }
        initRsaKeyPair();
        return cache.get(BaseConstants.PRI_KEY);
    }

    /**
     * 初始化Rsa秘钥
     *
     * @throws NoSuchAlgorithmException
     */
    private void initRsaKeyPair() throws NoSuchAlgorithmException {
        String lockKey = "initRSAKey";
        try {
            dLockTemplate.tryLock(lockKey, 10);
            Map<String, Object> keyMap = RSAUtil.genKeyPair();
            String publicKey = RSAUtil.getPublicKey(keyMap);
            String privateKey = RSAUtil.getPrivateKey(keyMap);
            cache.set(BaseConstants.PRI_KEY, privateKey);
            cache.set(BaseConstants.PUB_KEY, publicKey);
        } finally {
            dLockTemplate.unLock(lockKey);
        }
    }
}
