package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizeMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizePlanMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmUpChannelScalePrizeProductMapper;
import com.cfpamf.ms.insur.admin.enums.EnumPlanState;
import com.cfpamf.ms.insur.admin.pojo.convertor.UpChannelScaleConvert;
import com.cfpamf.ms.insur.admin.pojo.dto.SmUpChannelScalePrizeFormula;
import com.cfpamf.ms.insur.admin.pojo.dto.UpChannelScalePrizeDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmUpChannelScalePrize;
import com.cfpamf.ms.insur.admin.pojo.po.SmUpChannelScalePrizePlan;
import com.cfpamf.ms.insur.admin.pojo.po.SmUpChannelScalePrizeProduct;
import com.cfpamf.ms.insur.admin.pojo.query.UpChannelScalePrizeQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.DictionaryVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UpChannelScalePrizeDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UpChannelScalePrizeProductVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UpChannelScalePrizeVO;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * (SmUpChannelScalePrize)表服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-26 14:32:09
 */
@Service
public class SmUpChannelScalePrizeService {
    @Resource
    private SmUpChannelScalePrizeMapper upChannelScalePrizeMapper;
    @Resource
    private SmUpChannelScalePrizePlanMapper upChannelScalePrizePlanMapper;
    @Resource
    private SmUpChannelScalePrizeProductMapper upChannelScalePrizeProductMapper;
    @Resource
    private DLockTemplate lockTemplate;
    @Resource
    private RedisUtil<String, Long> redisUtil;
    @Resource
    private DictionaryService dictionaryService;

    private final String UP_CHANNEL_PRIZE_INCR_CODE = "UP_CHANNEL_PRIZE_INCR_CODE";
    private final String UP_CHANNEL_PRIZE_INCR_CODE_LOCK = "UP_CHANNEL_PRIZE_INCR_CODE_LOCK";

    @Transactional(rollbackFor = Exception.class)
    public void addChannelScalePrize(UpChannelScalePrizeDTO upChannelScalePrizeDTO) {
        //插入基本信息
        SmUpChannelScalePrize upChannelScalePrize = UpChannelScaleConvert.CNT.toUpChannelScalePrize(
                upChannelScalePrizeDTO);

        if (upChannelScalePrize.getStartTime().isAfter(upChannelScalePrize.getEndTime())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "开始时间不能小于结束时间");
        }

        List<SmUpChannelScalePrizeFormula> formulaList = serializeFormula(upChannelScalePrizeDTO);
        //生成流水号
        upChannelScalePrize.setCreateBy(HttpRequestUtil.getUserId());
        upChannelScalePrize.setUpdateBy(HttpRequestUtil.getUserId());
        upChannelScalePrize.setComputeParamsJson(JSON.toJSONString(formulaList));
        upChannelScalePrize.setPlanState(EnumPlanState.UNPUBLISHED.getCode());
        upChannelScalePrize.setPlanCode(generateCode());

        upChannelScalePrizeMapper.insertSelective(upChannelScalePrize);
        //插入产品
        upChannelScalePrizeDTO.getChannelScalePrizePlanList().forEach(
                item -> item.setChannelScalePrizeId(upChannelScalePrize.getId())
        );
        upChannelScalePrizePlanMapper.insertList(upChannelScalePrizeDTO.getChannelScalePrizePlanList());
        //插入计划信息
        upChannelScalePrizeDTO.getChannelScalePrizeProductList().forEach(
                item -> item.setChannelScalePrizeId(upChannelScalePrize.getId())
        );
        upChannelScalePrizeProductMapper.insertList(upChannelScalePrizeDTO.getChannelScalePrizeProductList());
    }

    private List<SmUpChannelScalePrizeFormula> serializeFormula(UpChannelScalePrizeDTO upChannelScalePrizeDTO) {
        return upChannelScalePrizeDTO.getChannelScalePrizePlanList().stream()
                .map(SmUpChannelScalePrizePlan::toChannelScalePrizeFormula)
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateChannelScalePrize(UpChannelScalePrizeDTO upChannelScalePrizeDTO) {

        if (upChannelScalePrizeDTO.getStartTime().isAfter(upChannelScalePrizeDTO.getEndTime())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "开始时间不能小于结束时间");
        }
        //查询当前的状态
        SmUpChannelScalePrize condition = new SmUpChannelScalePrize();
        condition.setId(upChannelScalePrizeDTO.getId());
        condition.setEnabledFlag(0);
        SmUpChannelScalePrize existed = upChannelScalePrizeMapper.selectByPrimaryKey(condition);

        EnumPlanState realState = EnumPlanState.translateToRealState(
                existed.getPlanState(), existed.getStartTime(), existed.getEndTime()
        );
        if (!(realState.equals(EnumPlanState.UNSTART) || realState.equals(EnumPlanState.UNPUBLISHED))) {
            throw new BizException(ExcptEnum.UP_CHANNEL_PRIZE_EDIT);
        }
        //更新主表
        SmUpChannelScalePrize upChannelScalePrize = UpChannelScaleConvert.CNT.toUpChannelScalePrize(
                upChannelScalePrizeDTO);
        List<SmUpChannelScalePrizeFormula> formulaList = upChannelScalePrizeDTO.getChannelScalePrizePlanList()
                .stream()
                .map(SmUpChannelScalePrizePlan::toChannelScalePrizeFormula)
                .collect(Collectors.toList());
        upChannelScalePrize.setUpdateBy(HttpRequestUtil.getUserId());
        upChannelScalePrize.setComputeParamsJson(JSON.toJSONString(formulaList));
        upChannelScalePrizeMapper.updateByPrimaryKeySelective(upChannelScalePrize);
        //删除子表数据
        upChannelScalePrizePlanMapper.delete(
                SmUpChannelScalePrizePlan.builder().channelScalePrizeId(upChannelScalePrizeDTO.getId()).build()
        );
        upChannelScalePrizeProductMapper.delete(
                SmUpChannelScalePrizeProduct.builder().channelScalePrizeId(upChannelScalePrizeDTO.getId()).build()
        );
        //插入产品
        upChannelScalePrizeDTO.getChannelScalePrizePlanList().forEach(
                item -> item.setChannelScalePrizeId(upChannelScalePrize.getId())
        );
        upChannelScalePrizePlanMapper.insertList(upChannelScalePrizeDTO.getChannelScalePrizePlanList());
        //插入计划信息
        upChannelScalePrizeDTO.getChannelScalePrizeProductList().forEach(
                item -> item.setChannelScalePrizeId(upChannelScalePrize.getId())
        );
        upChannelScalePrizeProductMapper.insertList(upChannelScalePrizeDTO.getChannelScalePrizeProductList());

    }

    /**
     * 分页
     *
     * @param queryParams 查询参数
     * @return 分页结果
     */
    public PageInfo<UpChannelScalePrizeVO> page(UpChannelScalePrizeQuery queryParams) {
        return PageHelper.startPage(queryParams.getPage(), queryParams.getSize())
                .doSelectPageInfo(() -> upChannelScalePrizeMapper.pageQuery(queryParams));
    }


    @Transactional(rollbackFor = Exception.class)
    public void editStateFlow(Integer id, String state) {
        //查询当前的状态
        SmUpChannelScalePrize condition = new SmUpChannelScalePrize();
        condition.setId(id);
        SmUpChannelScalePrize existed = upChannelScalePrizeMapper.selectByPrimaryKey(condition);

        EnumPlanState realState = EnumPlanState.translateToRealState(
                existed.getPlanState(), existed.getStartTime(), existed.getEndTime()
        );

        StateFlowCtrl.of(realState).ableToNextState(
                        item -> Arrays.stream(item.getNextPlanState())
                                .anyMatch(nextState -> EnumPlanState.getEnumByCode(state).equals(nextState))
                ).withThrow(
                        () -> new BizException(
                                ExcptEnum.UP_CHANNEL_PRIZE.getCode(),
                                "不能由当前状态" + EnumPlanState.translateToRealState(
                                        existed.getPlanState(),
                                        existed.getStartTime(),
                                        existed.getEndTime()
                                ).getDesc()
                        )
                )
                .excute(existState -> {
                    existed.setPlanState(state);
                    upChannelScalePrizeMapper.updateByPrimaryKeySelective(existed);
                });
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateDeleteState(Integer id) {
        //查询当前的状态
        SmUpChannelScalePrize condition = new SmUpChannelScalePrize();
        condition.setId(id);
        SmUpChannelScalePrize existed = upChannelScalePrizeMapper.selectByPrimaryKey(condition);

        EnumPlanState realState = EnumPlanState.translateToRealState(
                existed.getPlanState(), existed.getStartTime(), existed.getEndTime()
        );
        StateFlowCtrl.of(realState)
                .ableToNextState(
                        item -> Arrays.asList(realState.getNextPlanState())
                                .contains(EnumPlanState.DELETED)
                )
                .withThrow(
                        () -> new BizException(
                                ExcptEnum.UP_CHANNEL_PRIZE.getCode(),
                                "处于" + EnumPlanState.translateToRealState(
                                        existed.getPlanState(),
                                        existed.getStartTime(),
                                        existed.getEndTime()
                                ).getDesc() + "不能删除"
                        )
                )
                .map(existState -> {
//                    existed.setPlanState(EnumPlanState.DELETED.getCode());
                    existed.setEnabledFlag(1);
                    return existed;
                }).excute(updateObj -> upChannelScalePrizeMapper.updateByPrimaryKeySelective(updateObj));
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    public UpChannelScalePrizeDetailVO queryDetailById(Integer id) {
        SmUpChannelScalePrize condition = new SmUpChannelScalePrize();
        condition.setId(id);
        condition.setEnabledFlag(0);
        UpChannelScalePrizeDetailVO result = UpChannelScaleConvert.CNT.toUpChannelScalePrizeVO(
                upChannelScalePrizeMapper.selectOne(condition)
        );
        if (result != null) {
            List<SmUpChannelScalePrizePlan> planList = upChannelScalePrizePlanMapper.select(
                    SmUpChannelScalePrizePlan.builder().channelScalePrizeId(id).build()
            );
            planList.sort(Comparator.comparingInt(SmUpChannelScalePrizePlan::getSort));
            result.setChannelScalePrizePlanList(planList);

            List<UpChannelScalePrizeProductVO> scalePrizeProductVOList = upChannelScalePrizeProductMapper.queryProductListByConfigId(
                    id);
            handleDic(scalePrizeProductVOList);
            result.setChannelScalePrizeProductList(scalePrizeProductVOList);
        }
        return result;
    }

    private void handleDic(List<UpChannelScalePrizeProductVO> scalePrizeProductVOList) {
        // 多个分类处理
        Map<String, String> categoryMap = dictionaryService.getDictionarysByPage(
                        SmConstants.DICTIONARY_PRODUCT_TYPE,
                        null,
                        null,
                        false
                )
                .getList().stream()
                .collect(toMap(d -> d.getId() + "", DictionaryVO::getName));
        scalePrizeProductVOList.stream().forEach(p -> {
            String categoryId = p.getProductCategoryId();
            if (!StringUtils.isEmpty(categoryId)) {
                String[] categoryIds = categoryId.split(",");
                StringBuilder sb = new StringBuilder();
                for (String s : categoryIds) {
                    sb.append(categoryMap.get(s)).append(" / ");
                }
                p.setProductCategoryName(sb.substring(0, sb.length() - 2));
            }
        });
    }


    public static class StateFlowCtrl<T> {
        private final T value;

        private StateFlowCtrl(T value) {
            this.value = value;
        }

        public static <T> StateFlowCtrl<T> of(T value) {
            return new StateFlowCtrl<>(value);
        }

        public StateFlowCtrl<T> ableToNextState(Predicate<T> t) {
            if (t.test(value)) {
                return this;
            }
            return StateFlowCtrl.of(null);
//            throw new BizException("", "不能由当前状态" + value + "变成状态");
        }

        public <V> StateFlowCtrl<V> map(Function<T, V> function) {
            return new StateFlowCtrl<>(function.apply(value));
        }

        public void excute(Consumer<T> consumer) {
            consumer.accept(value);
        }

        public <X extends Throwable> StateFlowCtrl<T> withThrow(Supplier<? extends X> exceptionSupplier) throws X {
            if (value != null) {
                return this;
            } else {
                throw exceptionSupplier.get();
            }
        }
    }

    private String generateCode() {
        //处理一下redis的缓存被手动清空的问题
        if (!redisUtil.exists(UP_CHANNEL_PRIZE_INCR_CODE)) {
            try{
                lockTemplate.lock(UP_CHANNEL_PRIZE_INCR_CODE_LOCK, 5);
                //从数据库中查询最大的值
                if (!redisUtil.exists(UP_CHANNEL_PRIZE_INCR_CODE)) {
                    redisUtil.setnx(
                            UP_CHANNEL_PRIZE_INCR_CODE
                            , Optional.ofNullable(upChannelScalePrizeMapper.getMaxValue()).orElse(1L)
                    );
                }
            } catch(Exception e) {
                e.printStackTrace();
            } finally {
                lockTemplate.unLock(UP_CHANNEL_PRIZE_INCR_CODE_LOCK);
            }

        }
        Long incrNumber = redisUtil.increment(UP_CHANNEL_PRIZE_INCR_CODE, 1);
        return String.format("%s%05d", "GM", incrNumber);
    }

}
