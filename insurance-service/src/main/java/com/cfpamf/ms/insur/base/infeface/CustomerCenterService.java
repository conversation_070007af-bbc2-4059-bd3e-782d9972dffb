package com.cfpamf.ms.insur.base.infeface;

/**
 * 客户中心service
 *
 * <AUTHOR>
 **/

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.cmis.common.base.CommonResult;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.customer.facade.api.AuthenticationFacade;
import com.cfpamf.ms.customer.facade.api.InsuranceFacade;
import com.cfpamf.ms.customer.facade.enums.CappSMSEnum;
import com.cfpamf.ms.customer.facade.enums.DomainEnum;
import com.cfpamf.ms.customer.facade.enums.IDTypeEnum;
import com.cfpamf.ms.customer.facade.enums.UserBizCodeEnum;
import com.cfpamf.ms.customer.facade.request.*;
import com.cfpamf.ms.customer.facade.request.insurance.AddExcepIdnoReq;
import com.cfpamf.ms.customer.facade.request.insurance.CreateCustInfoReq;
import com.cfpamf.ms.customer.facade.request.insurance.GetExcepIdnoReq;
import com.cfpamf.ms.customer.facade.vo.CustCertifyVo;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.customer.facade.vo.UserInfoVo;
import com.cfpamf.ms.customer.facade.vo.cust.CustExceptionIdnoVo;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.AddExcepIdnoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.CustomerDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.CustomerRemdDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserNameVO;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.DataMaskUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxAgentRegisterDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxCustomerVO;
import com.github.pagehelper.PageInfo;
import com.itextpdf.text.pdf.qrcode.MaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 运营平台同步用户接口
 * @author: zhangnayi
 * @create: 2018-06-21 20:10
 **/
@Slf4j
@Service
public class CustomerCenterService {
    /**
     * 实名认证，异常录入有效天数
     */
    @Value("${author.realNameAuth.expireDays:30}")
    private Integer expireDays;

    public static final int AGE_LIMIT = 18;
    /**
     * 客户中心 授权service
     */
    @Autowired
    private AuthenticationFacade facade;

    /**
     * 客户中心保险相关创建客户service
     */
    @Autowired
    private InsuranceFacade insFacade;

    @Autowired
    private UserService userService;

    /**
     * 客户mapper
     */
    @Autowired
    private CustomerMapper cMapper;

    /**
     * 判断手机号是否已注册
     *
     * @param mobile
     */
    public boolean checkRegisterMobile(String mobile) {
        log.info(" facade.acctStatusCheckMobile(mobile, UserBizCodeEnum.INSURANCE.getCode()):{}", mobile);
        CommonResult result = facade.acctStatusCheckMobile(mobile, UserBizCodeEnum.INSURANCE.getCode());
        return !result.isSuccess();
    }


    /**
     * 保险客户实名认证，如果实名认证通过，创建保险客户
     * 否则抛出异常
     *
     * @param custName
     * @param idNo
     */
    public CustInfoVo createCustInfo(String custName, String idNo, String orgCode, String userId) {
        return doCreateCustInfo(custName, idNo, false, orgCode, userId);
    }

    public CustInfoVo createInsuredCustInfo(String custName, String idNo, String orgCode, String userId) {
        return doCreateCustInfo(custName, idNo, true, orgCode, userId);
    }

    private CustInfoVo doCreateCustInfo(String custName, String idNo, boolean isInsured, String orgCode, String userId) {
        CreateCustInfoReq req = new CreateCustInfoReq();
        req.setCustName(custName);
        req.setIdNo(idNo);
        req.setIdType(IDTypeEnum.IDENTITY_CARD);
        req.setOrgNo(orgCode);
        req.setOperator(userId);
        log.info("insFacade.createCustInfo(req),{}", JSON.toJSONString(req));
        //add by luoshicaho 2020-04-22 强制入库逻辑客户中心已经删除
        //add by zhangjian 2020-02-19 被保人并且年龄小于18岁时，如果实名信息在客户中心不存在，就强制写入,客户中心返回成功
//        if (isInsured && IdcardUtils.getAgeByIdCard(idNo) < AGE_LIMIT) {
//            req.setForceFlag(Boolean.TRUE);
//        }
        Result<CustInfoVo> result;
        try {
            result = insFacade.createCustInfo(req);
        } catch (Exception e) {
            log.warn("客户中心接口调用失败", e);
            throw new BizException(ExcptEnum.CUST_CENTER_ERROR);
        }

        throwExceptionIfFail(result, custName, idNo);
        return result.getData();
    }

    /**
     * 发送代理人注册短信验证码
     *
     * @param mobile
     */
    public void sendRegisterCode(String mobile) {
        CappSendSmsRequest request = new CappSendSmsRequest();
        request.setMobile(mobile);
        request.setType(CappSMSEnum.REGIST);
        log.info(" facade.sendCappSMSCode(request):{}", JSON.toJSONString(request));
        CommonResult result = facade.sendCappSMSCode(request);
        throwExceptionIfFail(result);
    }

    /**
     * 发送代理人找回密码验证码
     *
     * @param mobile
     */
    public void sendForgetCode(String mobile) {
        CappSendSmsRequest request = new CappSendSmsRequest();
        request.setMobile(mobile);
        request.setType(CappSMSEnum.FORGET);
        log.info("facade.sendCappSMSCode(request):{}", JSON.toJSONString(request));
        CommonResult result = facade.sendCappSMSCode(request);
        throwExceptionIfFail(result);
    }

    /**
     * 校验代理人注册短信验证码
     *
     * @param mobile
     * @param verifyCode
     */
    public void checkRegisterCode(String mobile, String verifyCode) {
        CappCheckSmsRequest request = new CappCheckSmsRequest();
        request.setMobile(mobile);
        request.setCode(verifyCode);
        request.setType(CappSMSEnum.REGIST);
        CommonResult result = facade.checkCappSMSCode(request);
        throwExceptionIfFail(result);
    }

    /**
     * 校验代理人注册
     *
     * @param dto
     */
    public String register(WxAgentRegisterDTO dto) {
        CustRegisterRequest request = new CustRegisterRequest();
        request.setBizCode(UserBizCodeEnum.INSURANCE);
        request.setChannel("weixin");
        request.setMobile(dto.getAgentMobile());
        request.setPassword(dto.getPassword());
        request.setVerifyCode(dto.getVerifyCode());
        request.setVerifyType(CappSMSEnum.REGIST);
        log.info(" facade.register(request):{}", JSON.toJSONString(request));
        CommonResult<UserInfoVo> result = facade.register(request);
        throwExceptionIfFail(result);
        return result.getData().getUserNo();
    }


    /**
     * 实名认证
     *
     * @param userName
     * @param userIdCard
     * @param userNo
     */
    public String realCheck(String userName, String userIdCard, String userNo) {
        CustCertificationRequest request = new CustCertificationRequest();
        request.setCustName(userName);
        request.setIdNo(userIdCard);
        request.setIdType("20");
        request.setUserNo(userNo);
        CommonResult<CustCertifyVo> result = facade.certification(request);
        throwExceptionIfFail(result);
        if (result.getData() != null && result.getData().getExtInfo() != null) {
            Object custNo = result.getData().getExtInfo().get("custNo");
            if (custNo != null && !StringUtils.isEmpty(custNo.toString())) {
                return custNo.toString();
            }
        }
        throw new BizException(ExcptEnum.REAL_CHECK_FAIL_801037);
    }


    /**
     * 校验代理人找回密码验证码
     *
     * @param mobile
     * @param verifyCode
     */
    public void checkForgetCode(String mobile, String verifyCode) {
        CappCheckSmsRequest request = new CappCheckSmsRequest();
        request.setMobile(mobile);
        request.setCode(verifyCode);
        request.setType(CappSMSEnum.FORGET);
        CommonResult result = facade.checkCappSMSCode(request);
        throwExceptionIfFail(result);
    }

    /**
     * 修改密码
     *
     * @param mobile
     * @param code
     */
    public void modifyPassword(String mobile, String code, String password) {
        CustForgetPwdRequest request = new CustForgetPwdRequest();
        request.setMobile(mobile);
        request.setVerifyCode(code);
        request.setNewPassword(password);
        request.setBizCode(UserBizCodeEnum.INSURANCE);
        request.setVerifyType(CappSMSEnum.FORGET);
        CommonResult result = facade.forgetPwd(request);
        throwExceptionIfFail(result);
    }

    /**
     * 判断手机号是否已注册
     *
     * @param mobile
     */
    public String login(String mobile, String password) {
        CustLoginRequest request = new CustLoginRequest();
        request.setMobile(mobile);
        request.setBizCode(UserBizCodeEnum.INSURANCE);
        request.setPassword(password);
        request.setChannel(UserBizCodeEnum.INSURANCE.getCode());
        CommonResult<UserInfoVo> result = facade.login(null, null, null, null, null, null, request);
        throwExceptionIfFail(result);
        if (result.getData() != null && result.getData().getCustNo() != null) {
            return result.getData().getCustNo();
        }
        return null;
    }

    /**
     * 新增身份异常数据
     *
     * @param dto
     */
    public void addExcpIdNo(AddExcepIdnoDTO dto) {
        if (isValidExcpIdNo(dto.getCustName(), dto.getIdNo())) {
            throw new BizException(ExcptEnum.DATA_EXISTS_ERROR_801303);
        }

        AddExcepIdnoReq req = new AddExcepIdnoReq();
        req.setCustName(dto.getCustName());
        req.setMobile(dto.getMobile());
        req.setIdNo(dto.getIdNo());
        req.setIdType(dto.getIdType());
        req.setImage(dto.getImage());
        req.setDocType(dto.getDocType());
        req.setChannel(DomainEnum.INSURANCE.getCode());

//        JwtUserInfo user = HttpRequestUtil.getUser();
        String jobNumer = HttpRequestUtil.getUserId();
        //如果没有员工信息 就取身份证
        if (StringUtils.isEmpty(jobNumer)) {
            req.setIdNo(dto.getIdNo());
        } else {
            req.setStaffNo(jobNumer);
        }


        log.info("insFacade.addExcpIdNo(req),{}", JSON.toJSONString(req));
        Result<Void> result = insFacade.addExcpIdNo(req);
        if (!result.isSuccess()) {
            log.warn("客户中心接口addExcpIdNo调用失败, {}", result.getMessage());
            throw new BizException(result.getCode(), result.getMessage());
        }

        //身份证才录入  添加到我的客户
        if ("20".equals(dto.getIdType())) {
            Set<String> idNumbers = new HashSet<>();
            idNumbers.add(dto.getIdNo());
            List<WxCustomerVO> existCustomerList = cMapper.listCustomerIdNumbers(idNumbers);
            if (existCustomerList.stream().noneMatch(c -> Objects.equals(c.getIdNumber(), dto.getIdNo()))) {
                CustomerDTO customerDTO = new CustomerDTO();
                //customerDTO.setCustomerType("");
                //customerDTO.setFromXinDai("");
                customerDTO.setCustomerName(dto.getCustName());
                customerDTO.setGender(CommonUtil.getSex(dto.getIdNo()));
                customerDTO.setIdType("居民身份证");
                customerDTO.setIdNumber(dto.getIdNo());
                String birthDay = IdcardUtils.getBirthByIdCard(dto.getIdNo());
                customerDTO.setBirthday(birthDay.substring(0, 4) + "-" + birthDay.substring(4, 6) + "-" + birthDay.substring(6, 8));
                customerDTO.setCellPhone(dto.getMobile());
                //customerDTO.setEmail("");
                //customerDTO.setArea("");
                //customerDTO.setAddress("");
                //customerDTO.setAnnualIncome("");
                //customerDTO.setOccupation("");
                customerDTO.setApPolicyQty(0);
                customerDTO.setApPolicyAmount(BigDecimal.ZERO);
                customerDTO.setIsPolicyQty(0);
                customerDTO.setIsPolicyAmount(BigDecimal.ZERO);
                customerDTO.setNewestAdmin(jobNumer);
                cMapper.insertCustomer(customerDTO);

                CustomerRemdDTO remdDTO = new CustomerRemdDTO();
                remdDTO.setCustomerAdminId(jobNumer);
                remdDTO.setCustomerId(customerDTO.getId());
                cMapper.insertCustomerRemd(remdDTO);
            }
        }
    }

    /**
     * 判断异常数据是存在或者有效
     *
     * @param idNo
     */
    public boolean isValidExcpIdNo(String custName, String idNo) {
        log.info("insFacade.getSingleExcpIdNo(req),custName：{}，idNo：{}", custName, idNo);
        Result<CustExceptionIdnoVo> result = insFacade.getSingleExcpIdNo(custName, idNo);
        if (!result.isSuccess()) {
            log.warn("客户中心接口getSingleExcpIdNo调用失败, {}", result.getMessage());
            throw new BizException(result.getCode(), result.getMessage());
        }
        CustExceptionIdnoVo vo = result.getData();
        if (vo != null) {
            //获取最新一个vo，判断是否过期
            if (isExcpIdNoExpire(vo.getCreateTime())) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 分页获取异常数据
     *
     * @param req
     */
    public PageInfo<CustExceptionIdnoVo> getExcpIdNoByExample(GetExcepIdnoReq req) {
        log.info("insFacade.getExcpIdNoByExample(req),{}", JSON.toJSONString(req));
        Result<PageVO<CustExceptionIdnoVo>> result = insFacade.getExcpIdNoByExample(req);
        if (!result.isSuccess()) {
            log.warn("客户中心接口getExcpIdNoByExample调用失败, {}", result.getMessage());
            throw new BizException(result.getCode(), result.getMessage());
        }
        PageVO<CustExceptionIdnoVo> pageResult = result.getData();
        PageInfo<CustExceptionIdnoVo> pageInfo = new PageInfo<>();
        pageInfo.setTotal(pageResult.getRowTotal());
        pageInfo.setPageNum(pageResult.getPageNumber());
        pageInfo.setSize(pageResult.getPageSize());
        pageInfo.setHasNextPage(req.getPageIndex() * req.getPageSize() < pageResult.getPageTotal());
        if (pageResult.getRows() == null) {
            pageResult.setRows(Collections.emptyList());
        } else {
            List<CustExceptionIdnoVo> list = pageResult.getRows().stream().filter(m -> StringUtils.isEmpty(m.getSubmitStaff()))
                    .collect(Collectors.toList());
            List<String> jobNumbers = list.stream().map(CustExceptionIdnoVo::getStaffNo).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(jobNumbers)) {
                List<AuthUserNameVO> listUserName = userService.getUserNameByUserId(jobNumbers);
                list.forEach(item -> {
                    AuthUserNameVO vo = listUserName.stream().filter(m -> m.getUserId().equals(item.getStaffNo())).findFirst().orElse(null);
                    item.setSubmitStaff(vo == null ? "" : vo.getUserName());
                });
            }
        }
        for (CustExceptionIdnoVo exceptionIdnoVo : pageResult.getRows()) {
            exceptionIdnoVo.setMobile(DataMaskUtil.maskMobile(exceptionIdnoVo.getMobile()));
            exceptionIdnoVo.setIdNo(DataMaskUtil.maskIdCardNo(exceptionIdnoVo.getIdNo()));
        }
        pageInfo.setList(pageResult.getRows());
        return pageInfo;
    }

    /**
     * 库无此人 客户中心返回认证失败
     * 如果接口错误抛出异常
     * 中心出现库无可能的原因为:
     * 1、真有其人，但户籍地派出所漏报信息
     * 2、本无其人，号码为随意编造
     * 3、被认证人因出国定居、死亡、参军等原因进行了户口注销，故显示库无
     * 4、被认证人因上学、工作等原因进行了户籍迁移,原户籍地先进行户口注销，先户籍地再落户，我们称为“口袋户口”，口袋户口期间会显示“库中无此号”，待新户籍地重新上报信息后就可以查到。
     *
     * @param result
     */
    private void throwExceptionIfFail(CommonResult result) {
        if (!result.isSuccess()) {
            String errorMsg = Objects.equals(result.getMessage(), "实名认证失败，请核实身份信息") ?
                    "姓名或身份证号有误，请核实身份信息" : result.getMessage();
            log.warn("客户中心接口调用失败, {}", result.getMessage());
            throw new BizException(result.getCode(), errorMsg);
        }
    }

    /**
     * 库无此人 客户中心返回认证失败
     * 中心出现库无可能的原因为:
     * 1、真有其人，但户籍地派出所漏报信息
     * 2、本无其人，号码为随意编造
     * 3、被认证人因出国定居、死亡、参军等原因进行了户口注销，故显示库无
     * 4、被认证人因上学、工作等原因进行了户籍迁移,原户籍地先进行户口注销，先户籍地再落户，我们称为“口袋户口”，口袋户口期间会显示“库中无此号”，待新户籍地重新上报信息后就可以查到。
     *
     * @param result
     * @param custName
     * @param idNo
     */
    private void throwExceptionIfFail(Result<CustInfoVo> result, String custName, String idNo) {
        String inputCode = "001001033";
        String inputMes = "身份认证查无此人，客户身份认证异常数据不存在";
        if (!result.isSuccess()) {
            // CERTIFY_FAIL("001001014", "实名认证失败，请核实身份信息"),
            if (Objects.equals(result.getCode(), "001001014")) {
                throw new BizException(result.getCode(), "姓名或身份证号有误，请核实身份信息");
            }
            //EXCEPTION_IDNO_NOT_EXISTS("001001033", "身份认证查无此人，客户身份认证异常数据不存在"),
            if (Objects.equals(result.getCode(), inputCode)) {
                //库中无此号，如数据没问题，请手动录入异常数据，再提交下单
                throw new BizException(inputCode, getResultMes(custName, idNo, inputCode, inputMes));
            }
            throw new BizException(result.getCode(), result.getMessage());
        } else {
            if (isExcpIdNoExpire(result.getData().getCustExcepCreateTime())) {
                //库中无此号，如数据没问题，请手动录入异常数据，再提交下单
                throw new BizException(inputCode, getResultMes(custName, idNo, inputCode, inputMes));
            }
        }
    }

    private boolean isExcpIdNoExpire(Date createTime) {
        if (createTime != null) {
            if (expireDays > 0) {
                //需判断时间是否超时 （如果认证成功：1、认证是成功的（返回时间是当前时间） 2、异步表存在数据（返回时间是录入异步表时间））
                LocalDateTime startDateTime = LocalDateUtil.dateToLocaldatetime(createTime).plusDays(expireDays);
                LocalDateTime endDateTime = LocalDateUtil.dateToLocaldatetime(new Date());
                if (startDateTime.isBefore(endDateTime)) {
                    log.info("过期,createTime：{},expireDays:{},startDateTime:{},endDateTime:{}", createTime, expireDays, startDateTime, endDateTime);
                    return true;
                }
            } else {
                //过期时间配置存在问题，全部算过期
                log.info("过期时间配置存在问题，全部算过期,createTime：{},expireDays:{}", createTime, expireDays);
                return true;
            }
        }
        return false;
    }

    private String getResultMes(String custName, String idNo, String inputCode, String inputMes) {
        JSONObject json = new JSONObject();
        json.put("code", inputCode);
        json.put("custName", custName);
        json.put("idNo", idNo);
        json.put("message", inputMes);
        return json.toJSONString();
    }
}
