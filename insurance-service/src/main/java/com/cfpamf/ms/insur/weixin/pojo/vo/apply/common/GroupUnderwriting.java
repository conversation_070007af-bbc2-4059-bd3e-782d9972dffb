package com.cfpamf.ms.insur.weixin.pojo.vo.apply.common;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.base.util.IdCardUtils;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.ProductReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.VatInvoice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 核保参数
 * <AUTHOR>
@Data
public class GroupUnderwriting extends ApplyHolder {

    @ApiModelProperty(value = "订单Id")
    private String orderId;

    @ApiModelProperty(value = "出单渠道:za,tk......")
    private String channel;

    @NotNull(message = "产品信息不能为空")
    @ApiModelProperty(value = "产品Id")
    private Integer productId;

    @ApiModelProperty("微信OpenId")
    private String openId;

    @ApiModelProperty("购买产品数")
    private Integer qty;

    @ApiModelProperty(value = "*团险保险期限")
    private String validPeriod;

    @ApiModelProperty("业务推荐码")
    private String bizCode;

    @ApiModelProperty(value = "保障起期(投保次日零时零分零秒)[yyyymmddHHmmss]")
    protected String startTime;

    @ApiModelProperty(value = "保障止期(默认一年期)[yyyymmddHHmmss]")
    protected String endTime;

    @ApiModelProperty(value = "子渠道 乡助微服务（xiangzhu）/CAPP（capp）", hidden = true)
    private String subChannel;

    @ApiModelProperty(value = "支付方式：online/offline")
    private String payType;

    @ApiModelProperty("付款人信息")
    private PayerInfo payerInfo;

    @ApiModelProperty(value = "企业投保人信息")
    @NotNull(message = "投保人信息不能为空")
    protected GroupApplicant applicant;

    @ApiModelProperty(value = "被保人列表")
    @NotEmpty(message = "被保人信息不能为空")
    protected List<GroupInsured> insuredList;

    @ApiModelProperty(value = "*自定义产品计划列表:团险自定义套餐计划必传")
    @NotNull(message = "投保险种信息不能为空")
    protected ProductReq product;

    @ApiModelProperty(value = "开票信息")
    private VatInvoice invoiceInfo;

    @ApiModelProperty(value = "[核保]团险总保费(由后端试算得出)")
    private BigDecimal totalAmount;

    /**
     * 该字段目前主要作为佣金结算时间使用,佣金结算模块会使用该时间从配置表匹配佣金比例
     * ⚠️⚠️⚠️:格式必须为[yyyy-MM-dd HH:mm:ss]
     */
    @ApiModelProperty(value = "提交时间(服务端重置)")
    private String submitTime;

    @ApiModelProperty(value = "支付状态", hidden = true)
    private String payStatus;

    @ApiModelProperty(value = "订单状态", hidden = true)
    private String orderState;

    @ApiModelProperty(value = "出单类型:seeFee", hidden = true)
    private String orderOutType;

    @ApiModelProperty(value = "???", hidden = true)
    private Integer orderType = 0;

    /**
     * 真实续保标志，是否续保，
     */
    @ApiModelProperty(value = "是否续保")
    private boolean realRenewFlag;

    /**
     * 原待续保订单ID fhOrderId
     */
    @ApiModelProperty(value = "原待续保订单ID fhOrderId")
    private String oldOrderId;

    /**
     * 初始化状态为待核保
     */
    public void init() {
        payStatus = SmConstants.ORDER_STATUS_TO_ORDER;
        orderState = SmConstants.ORDER_STATUS_TO_ORDER;
        fillBirthday();
    }

    public void fillBirthday() {
        if (!CollectionUtils.isEmpty(insuredList)) {
            for (GroupInsured entry : insuredList) {
                if (StringUtils.isBlank(entry.getBirthday())) {
                    String birthDay = IdCardUtils.getBirthday(entry.getIdNumber(), "yyyy-MM-dd");
                    entry.setBirthday(birthDay);
                }
            }
        }
    }

//    public void fillAgent(Agent agent) {
//        holder.setAgent(agent);
//    }

//    public void fillCommission(SmCommissionSettingVO commission) {
//        holder.setCommission(commission);
//    }
//
//    public void fillEnterpriseRiskFactor(String enterpriseRiskFactor) {
//        holder.setEnterpriseRiskFactor(enterpriseRiskFactor);
//    }
}
