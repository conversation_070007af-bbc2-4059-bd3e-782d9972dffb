package com.cfpamf.ms.insur.admin.job;

import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupNotifyService;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 团险同步补偿
 *
 * <AUTHOR>
/**
 * <AUTHOR>
@Slf4j
@Component
public class SyncOrderGroupNotifyJobHandler {

    @Autowired
    private SmOrderGroupNotifyService smOrderGroupNotifyService;

    @Autowired
    private SmOrderGroupService smOrderGroupService;


    /**
     * 只处理众安的待处理的回调消息
     *
     * @throws Exception
     */
    @XxlJob("syncOrderGroupNotifyJobHandler")
    public void execute() throws Exception {
        log.info("开始处理众安-待处理的回调消息");
        List<SmOrderGroupNotify> groupNotifyList = smOrderGroupNotifyService.listGroupNotifyByStatus(EnumChannel.ZA.getCode(), GroupNotify.StatusEnum.UNDO.getCode());
        groupNotifyList.stream().forEach(orderGroupNotify -> {
            try {
                if (Objects.equals(orderGroupNotify.getChannel(), EnumChannel.ZA.getCode())) {
                    smOrderGroupService.process(orderGroupNotify);
                }
            } catch (Exception e) {
                log.error("众安回调消息处理失败:{}", orderGroupNotify, e);
            }
        });
    }


    /**
     * 慎用:主要补偿批改流程失败任务
     * 该方法十分暴力，没有判断状态
     *
     * @throws Exception
     */
    @XxlJob("group-correct-retry-handler")
    public void retryCorrect() {
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(param)) {
            log.warn("请求参数不能为空");
            return;
        }
        log.info("开始补偿团险批改失败记录:{}", param);
        Integer id = Integer.valueOf(param);

        SmOrderGroupNotify data = smOrderGroupNotifyService.getById(id);
        log.info("团单批改报文:{}", data);
        if (param != null) {
            smOrderGroupService.process(data);
        }
    }

}
