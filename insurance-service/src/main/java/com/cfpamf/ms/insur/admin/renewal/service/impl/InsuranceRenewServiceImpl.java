package com.cfpamf.ms.insur.admin.renewal.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.unit.DataUnit;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderApplicantMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderWaitRenewalMapper;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.enums.order.OrderWaitRenewalStatus;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderWaitRenewal;
import com.cfpamf.ms.insur.admin.pojo.po.renewal.InsuranceRenewPo;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.renewal.dao.InsuranceRenewMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.PhoenixEmpTodoMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalOrderMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalFollowMapper;
import com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalStatus;
import com.cfpamf.ms.insur.admin.renewal.enums.RenewalOrderStatusType;
import com.cfpamf.ms.insur.admin.renewal.exception.RenewalConfigBusinessException;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.form.WxOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.service.InsuranceRenewService;
import com.cfpamf.ms.insur.admin.renewal.vo.*;
import com.cfpamf.ms.insur.admin.renewal.vo.excel.InsuranceRenewedOrderExcelVo;
import com.cfpamf.ms.insur.admin.renewal.vo.excel.OverRenewalOrderExcelVo;
import com.cfpamf.ms.insur.admin.renewal.vo.excel.RenewedOrderExcelVo;
import com.cfpamf.ms.insur.admin.renewal.vo.excel.WaitRenewalOrderExcelVo;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.common.datasource.annotation.DataSourceReadOnly;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.renewal.RenewalBasicQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.service.WxCcOrderService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMAT;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class InsuranceRenewServiceImpl implements InsuranceRenewService {

    public static String KEY_RENEWED_ORDER = "key:renewed:order";
    @Autowired
    InsuranceRenewMapper insuranceRenewMapper;
    @Autowired
    WxCcOrderService wxCcOrderService;
    @Autowired
    SmOrderWaitRenewalMapper smOrderWaitRenewalMapper;
    @Autowired
    RenewalOrderMapper renewalOrderMapper;
    @Autowired
    SmOrderRenewalFollowMapper smOrderRenewalFollowMapper;
    @Autowired
    SmOrderManageService smOrderManageService;
    @Autowired
    PhoenixEmpTodoMapper phoenixEmpTodoMapper;
    @Autowired
    SmOrderMapper orderMapper;
    @Autowired
    SmOrderApplicantMapper applicantMapper;
    @Autowired
    SmClaimMapper smClaimMapper;

    @Autowired
    private RedisUtil<String, String> redisUtil;

    @Override
    public void initRenewPolicy() {
        insuranceRenewMapper.insertInsuranceRenew();

        //失效保单将待办置为已完成
        phoenixEmpTodoMapper.updateRenewShortTdo();
    }

    @Override
    public PageInfo<InsuranceRenewedOrderVo> searchWxRenewalOrderVo(WxOrderSearchForm wxOrderSearchForm, WxSessionVO wxSessionVO) {
        wxCcOrderService.initQuery(wxOrderSearchForm, wxSessionVO);
        PageInfo<InsuranceRenewedOrderVo> renewalOrderVoPageInfo = PageHelper.startPage(wxOrderSearchForm.getPage(), wxOrderSearchForm.getSize())
                .doSelectPageInfo(() -> insuranceRenewMapper.searchWxRenewalOrderVo(wxOrderSearchForm));

        if (RenewalOrderStatusType.WAITED.getCode().equals(wxOrderSearchForm.getInsStatus())) {
            //待续保列表数据处理
            searchNonRenewableReason(renewalOrderVoPageInfo);
        }

        if (RenewalOrderStatusType.RENEWED.getCode().equals(wxOrderSearchForm.getInsStatus())) {
            //已续购列表数据处理
            processRenewalInfo(renewalOrderVoPageInfo.getList());
        }

        return renewalOrderVoPageInfo;
    }

    @Override
    public List<HomePageRenewedOrderVo> listHomePageRenewedOrder(JwtUserInfo userInfo) {
        if(userInfo == null){
            return Collections.emptyList();
        }
        String dayStr = DateUtil.format(DateUtil.date(),PURE_DATE_FORMAT);
        String redisKey = StrUtil.format("{}:{}",KEY_RENEWED_ORDER,userInfo.getJobNumber());
        String redisDayStr = redisUtil.get(redisKey);
        //当redis存在缓存信息，则已经弹开过一次，不需要在弹窗
        if(redisDayStr!=null && Objects.equals(dayStr,redisDayStr)){
            return Collections.emptyList();
        }
        List<HomePageRenewedOrderVo> lst = insuranceRenewMapper.listHomePageRenewedOrder(userInfo.getJobNumber());
        if(CollectionUtils.isNotEmpty(lst)){
            redisUtil.set(redisKey,dayStr);
            return lst;
        }
        return Collections.emptyList();
    }

    @Override
    public Integer searchWxWaitRenewalOrderVoCount(WxSessionVO wxSessionVO) {
        //初始化查询信息
        log.info("登录用户信息：{}", JSON.toJSONString(wxSessionVO));
        WxOrderSearchForm wxOrderSearchForm = new WxOrderSearchForm();
        wxCcOrderService.initQuery(wxOrderSearchForm, wxSessionVO);
        if (StringUtils.isBlank(wxOrderSearchForm.getUserId()) && Objects.isNull(wxOrderSearchForm.getAgentId())) {
            log.warn("异常用户信息：wxSession:{},wxOrderSearchForm:{}",JSON.toJSONString(wxSessionVO),JSON.toJSONString(wxOrderSearchForm));
            return 0;
        }
        return insuranceRenewMapper.searchWxWaitRenewalOrderVoCount(wxOrderSearchForm);
    }

    @Override
    public OrderDetailVo findOrderByPolicyNo(OrderDetailQuery query) {
        OrderDetailVo orderDetailVo =getOrderDetailVo(query);

        if (orderDetailVo == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        if (StringUtils.isNoneBlank(orderDetailVo.getRenewalOrderId())) {
            //如果续保单号不为空则查找续保订单信息
            OrderDetailVo renewalOrderDetailVo = renewalOrderMapper.findByPolicyNo(orderDetailVo.getRenewalPolicyNo());
            if (Objects.isNull(renewalOrderDetailVo)) {
                return orderDetailVo;
            }

            List<PersonVo> insuredPersons = initInsuredInfo(renewalOrderDetailVo.getPolicyNo(),query.getProductAttrCode());
            if (CollectionUtils.isEmpty(insuredPersons)) {
                return orderDetailVo;
            }

            renewalOrderDetailVo.setInsuredPersons(insuredPersons);

            //初始化被保人的年龄和性别
            renewalOrderDetailVo.initPersonAgeSex();
            orderDetailVo.setRenewalOrderDetailVo(renewalOrderDetailVo);
        }

        //服务记录
        orderDetailVo.setRenewalFollowDtoList(smOrderRenewalFollowMapper.selectByPolicyNo(orderDetailVo.getPolicyNo()));

        return orderDetailVo;
    }

    @Override
    public void addOverRenewalReason(String policyNo,String productAttrCode, String overRenewalReason) {
        Integer count = insuranceRenewMapper.updateByPolicyNo(policyNo,overRenewalReason);

        //判断当前订单被保记录id是否存在  且未续保
        if (Objects.isNull(count) || count == 0) {
            throw RenewalConfigBusinessException.RENEWAL_OVER_ORDER_INSURANCE_ID_ERROR;
        }
        List<PersonVo> insuredPersons = insuranceRenewMapper.queryInsuredsByPolicyNo(policyNo,productAttrCode);
        if (CollectionUtils.isNotEmpty(insuredPersons)) {
            renewalOrderMapper.addInsuredsOverRenewalReason(insuredPersons.stream().map(PersonVo::getOrderInsuredId).collect(Collectors.toList()), overRenewalReason);
        }
    }

    @Override
    public RenewalStatisticsVo renewalStatistics(RenewalBasicQuery query) {
        WxSessionVO wxSessionVO = wxCcOrderService.checkAuthority(query.getOpenId(), query.getAuthorization());
        //初始化查询数据
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));
        if (wxSessionVO.isBindEmployee()) {
            query.setUserId(wxSessionVO.getUserId());
            query.setOpenId(null);
        } else if (wxSessionVO.isBindAgent()) {
            query.setAgentId(wxSessionVO.getAgentId());
            query.setOpenId(null);
        }
        RenewalStatisticsVo renewalStatisticsVo = new RenewalStatisticsVo();
        renewalStatisticsVo.setWaitRenewal(insuranceRenewMapper.waitRenewalStatistics(query));
        renewalStatisticsVo.setRenewed(insuranceRenewMapper.renewedStatistics(query));
        renewalStatisticsVo.setOverRenewal(insuranceRenewMapper.overRenewalStatistics(query));

        return renewalStatisticsVo;
    }

    @Override
    @DataSourceReadOnly
    public PageInfo<InsuranceRenewedOrderVo> searchRenewalOrderVo(RenewalOrderSearchForm renewalOrderSearchForm) {
        smOrderManageService.initOrderQuery(renewalOrderSearchForm);

        PageInfo<InsuranceRenewedOrderVo> renewalOrderVoPageInfo = PageHelper.startPage(renewalOrderSearchForm.getPage(), renewalOrderSearchForm.getSize())
                .doSelectPageInfo(() -> insuranceRenewMapper.searchRenewalOrderVo(renewalOrderSearchForm));

        if (RenewalOrderStatusType.WAITED.getCode().equals(renewalOrderSearchForm.getInsStatus())) {
            //待续保列表数据处理
            searchNonRenewableReason(renewalOrderVoPageInfo);
        }

        if (RenewalOrderStatusType.RENEWED.getCode().equals(renewalOrderSearchForm.getInsStatus())) {
            //已续购列表数据处理
            processRenewalInfo(renewalOrderVoPageInfo.getList());
        }
        if (CollUtil.isNotEmpty(renewalOrderVoPageInfo.getList())){
            List<String> insuredIdNumbers = renewalOrderVoPageInfo.getList().stream().map(InsuranceRenewedOrderVo::getInsuredIdNumber).distinct().collect(Collectors.toList());
            List<String> applicantIdNumbers = renewalOrderVoPageInfo.getList().stream().map(InsuranceRenewedOrderVo::getApplicantIdNumber).distinct().collect(Collectors.toList());
            Set<String> loanCustomerIdNumbers = smClaimMapper.listLoanCustomer(
                    Stream.concat(insuredIdNumbers.stream(), applicantIdNumbers.stream()).collect(Collectors.toList())
            );

            // 处理数据
            renewalOrderVoPageInfo.getList().forEach(o->{
                // 是否自保件
                o.setSelfInsuredName(Objects.equals(o.getSelfInsured(), SmConstants.LABEL_Y)?"是":"否");
                // 是否异业客户
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(loanCustomerIdNumbers)
                        && loanCustomerIdNumbers.contains(o.getApplicantIdNumber()) || loanCustomerIdNumbers.contains(o.getInsuredIdNumber())) {
                    o.setJudgeCustomerLoan("是");
                } else {
                    o.setJudgeCustomerLoan("否");
                }
            });
        }
        return renewalOrderVoPageInfo;
    }

    @Override
    @DataSourceReadOnly
    public List getReportRenewalOrderList(RenewalOrderSearchForm renewalOrderSearchForm, String reportType) {
        //获取导出类型
        RenewalOrderStatusType renewalOrderStatusType = RenewalOrderStatusType.getRenewalOrderStatusType(reportType);
        if (Objects.isNull(renewalOrderStatusType)) {
            return Lists.newArrayList();
        }
        //初始化查询
        smOrderManageService.initOrderQuery(renewalOrderSearchForm);
        renewalOrderSearchForm.setQueryPage(false);
        renewalOrderSearchForm.setAll(true);
        List<InsuranceRenewedOrderVo> renewedOrderVoList = insuranceRenewMapper.searchRenewalOrderVo(renewalOrderSearchForm);
        if (CollUtil.isNotEmpty(renewedOrderVoList)){
            if (renewalOrderStatusType == RenewalOrderStatusType.RENEWED){
                processRenewalInfo(renewedOrderVoList);
            }
            List resultList = new ArrayList();
            CollUtil.split(renewedOrderVoList, 1000).forEach(action->{
                List<String> insuredIdNumbers = action.stream().map(InsuranceRenewedOrderVo::getInsuredIdNumber).distinct().collect(Collectors.toList());
                List<String> applicantIdNumbers = renewedOrderVoList.stream().map(InsuranceRenewedOrderVo::getApplicantIdNumber).distinct().collect(Collectors.toList());
                Set<String> loanCustomerIdNumbers = smClaimMapper.listLoanCustomer(
                        Stream.concat(insuredIdNumbers.stream(), applicantIdNumbers.stream()).collect(Collectors.toList())
                );
                 action.forEach(o -> {
                    // 是否自保件
                    o.setSelfInsuredName(Objects.equals(o.getSelfInsured(), SmConstants.LABEL_Y) ? "是" : "否");
                    // 是否异业客户
                    if (CollectionUtils.isNotEmpty(loanCustomerIdNumbers)
                            && loanCustomerIdNumbers.contains(o.getApplicantIdNumber()) || loanCustomerIdNumbers.contains(o.getInsuredIdNumber())) {
                        o.setJudgeCustomerLoan("是");
                    } else {
                        o.setJudgeCustomerLoan("否");
                    }
                    if(renewalOrderStatusType == RenewalOrderStatusType.OVER){
                        resultList.add(BeanUtil.copyProperties(o, OverRenewalOrderExcelVo.class));
                    }else if (renewalOrderStatusType == RenewalOrderStatusType.WAITED) {
                        resultList.add(BeanUtil.copyProperties(o, WaitRenewalOrderExcelVo.class));
                    }else{
                        resultList.add(BeanUtil.copyProperties(o, RenewedOrderExcelVo.class));
                    }

                });
            });
            return resultList;
        }

        return Collections.emptyList();
    }

    /**
     * add by zhangjian
     * 查询不可续保原因 临时处理方案，后续吧带续保列表从新表里获取
     *
     * @param waitRenewalOrderVoPageInfo
     */
    private void searchNonRenewableReason(PageInfo<InsuranceRenewedOrderVo> waitRenewalOrderVoPageInfo) {
        if (!CollectionUtils.isEmpty(waitRenewalOrderVoPageInfo.getList())) {
            List<InsuranceRenewedOrderVo> voList = waitRenewalOrderVoPageInfo.getList();
            List<String> policyNoList = voList.stream().map(InsuranceRenewedOrderVo::getPolicyNo).collect(Collectors.toList());
            List<SmOrderWaitRenewal> waitRenewalList = smOrderWaitRenewalMapper.listByPolicyNos(policyNoList);
            if (CollectionUtils.isEmpty(waitRenewalList)) {
                return;
            }
            for (SmOrderWaitRenewal waitRenewal : waitRenewalList) {
                List<InsuranceRenewedOrderVo> filterList = voList.stream().filter(vo -> Objects.equals(vo.getFhOrderId(), waitRenewal.getFhOrderId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(filterList)) {
                    return;
                }
                InsuranceRenewedOrderVo orderVo = filterList.get(0);
                if (Objects.equals(waitRenewal.getStatus(), OrderWaitRenewalStatus.NON_RENEWAL.getCode())) {
                    orderVo.setRenewable(0);
                    orderVo.setNonRenewableReason(waitRenewal.getErrorMsg());
                } else if (Objects.equals(waitRenewal.getStatus(), OrderWaitRenewalStatus.WAIT_RENEWAL.getCode())) {
                    orderVo.setRenewable(1);
                }
            }
        }
    }

    /**
     * 处理续保信息
     * 1.设置续保订单号
     * 1.设置续保产品名
     * 1.设置续保生效时间
     *
     * @param renewedOrderVoList
     */
    private void processRenewalInfo(List<InsuranceRenewedOrderVo> renewedOrderVoList) {
        if (CollectionUtils.isEmpty(renewedOrderVoList)) {
            return;
        }

        //获取续保订单id集合
        List<String> renewalOrderIdList = renewedOrderVoList.stream()
                .map(InsuranceRenewedOrderVo::getRenewalOrderId)
                .collect(Collectors.toList());

        Map<String, BaseOrderVo> baseOrderVoMap = renewalOrderMapper.searchBaseOrderVoByFhOrderIdList(renewalOrderIdList)
                .stream()
                .collect(Collectors.toMap(BaseOrderVo::getFhOrderId, e -> e, (e1, e2) -> e1));
        //设置续购的产品名和订单号 TODO  续购类型暂时不知道 后期解决
        for (InsuranceRenewedOrderVo renewedOrderVo : renewedOrderVoList) {
            BaseOrderVo baseOrderVo = baseOrderVoMap.get(renewedOrderVo.getRenewalOrderId());
            if (Objects.isNull(baseOrderVo)) {
                continue;
            }
            renewedOrderVo.setRenewalPolicyNo(baseOrderVo.getPolicyNo());
            renewedOrderVo.setRenewalProductName(baseOrderVo.getProductName());
            renewedOrderVo.setRenewalPlanName(baseOrderVo.getPlanName());
            renewedOrderVo.setRenewalStartTime(baseOrderVo.getStartTime());
        }

    }

    /**
     * 获取订单详情
     *
     * @param query
     * @return
     */
    private OrderDetailVo getOrderDetailVo(OrderDetailQuery query) {
        OrderDetailVo orderDetailVo = insuranceRenewMapper.queryOrderDetail(query);
        if (Objects.isNull(orderDetailVo)) {
            return null;
        }
        //处理被保人信息
        List<PersonVo> insuredPersons = initInsuredInfo(orderDetailVo.getPolicyNo(),query.getProductAttrCode());

        if (CollectionUtils.isEmpty(insuredPersons)) {
            return null;
        }

        orderDetailVo.setInsuredPerson(insuredPersons.get(0));
        orderDetailVo.setDownloadURL(orderDetailVo.getInsuredPerson().getDownloadURL());
        orderDetailVo.setOrderInsuredId(orderDetailVo.getInsuredPerson().getOrderInsuredId());
        orderDetailVo.setInsuredPersons(insuredPersons);

        //处理详情的不可续保信息
        doOrderDetailNonRenewable(orderDetailVo);
        //初始化被保人的年龄和性别
        orderDetailVo.initPersonAgeSex();
        return orderDetailVo;
    }

    /**
     * 处理详情的不可续保信息
     *
     * @param renewalOrderDetailVo
     */
    private void doOrderDetailNonRenewable(OrderDetailVo renewalOrderDetailVo) {
        log.info("保单号：{}", renewalOrderDetailVo.getPolicyNo());
        SmOrderWaitRenewal nonRenewable = smOrderWaitRenewalMapper.getByPolicyNo(renewalOrderDetailVo.getPolicyNo());
        log.info("续保信息：{}", nonRenewable);
        if (Objects.nonNull(nonRenewable)) {
            if (Objects.equals(nonRenewable.getStatus(), OrderWaitRenewalStatus.NON_RENEWAL.getCode())) {
                renewalOrderDetailVo.setRenewable(0);
                renewalOrderDetailVo.setNonRenewableReason(nonRenewable.getErrorMsg());
            } else if (Objects.equals(nonRenewable.getStatus(), OrderWaitRenewalStatus.WAIT_RENEWAL.getCode())) {
                renewalOrderDetailVo.setRenewable(1);
            }
        }
    }

    /**
     * 初始化被保人信息
     * @param policyNo
     * @return
     */
    @Override
    public List<PersonVo> initInsuredInfo(String policyNo, String productAttrCode){
        //处理被保人信息
        List<PersonVo> insuredPersons = insuranceRenewMapper.queryInsuredsByPolicyNo(policyNo,productAttrCode);

        if (CollectionUtils.isEmpty(insuredPersons)) {
            return null;
        }

        //团险剔除批减的被保人
        List<PersonVo> insuredPersonsExceptCancel = new ArrayList<>(insuredPersons.size());
        if (GroupNotify.GroupType.GROUP.getCode().equals(productAttrCode)) {
            Map<String,List<RenewalOrderItemVO>> map = insuranceRenewMapper.queryInsuredsCancelByPolicyNo(policyNo).stream().collect(
                    Collectors.groupingBy(RenewalOrderItemVO::getThPolicyNo));

            for (PersonVo redEnvelopeOrderItemVO:insuredPersons){
                List<RenewalOrderItemVO> redEnvelopeOrderItemVoList = map.get(policyNo);
                if (CollectionUtils.isNotEmpty(redEnvelopeOrderItemVoList)){
                    if (!redEnvelopeOrderItemVoList.stream().anyMatch(
                            x->x.getIdNumber().equals(redEnvelopeOrderItemVO.getIdCard())
                                    && x.getOrderIdIndex().compareTo(redEnvelopeOrderItemVO.getOrderIdIndex())>0)){
                        insuredPersonsExceptCancel.add(redEnvelopeOrderItemVO);
                    }
                }else{
                    insuredPersonsExceptCancel.add(redEnvelopeOrderItemVO);
                }
            }
        }else{
            insuredPersonsExceptCancel = insuredPersons;
        }
        return insuredPersonsExceptCancel;
    }


    /**
     * <li>有已经续保数据，不处理</li>
     * <li>有待续保数据，更新状态</li>
     * <li>无待续保数据，插入已续保数据</li>
     * @param sourcePolicyOrder
     * @param sourceInsuredList
     * @param createOrder
     */
    @Override
    public void addRenewedPolicy(SmBaseOrderVO sourcePolicyOrder, List<PersonVo> sourceInsuredList, SmCreateOrderSubmitRequest createOrder) {
        if (CollectionUtils.isEmpty(sourceInsuredList)) {
            return;
        }

        String newPolicyNo = createOrder.getPolicyNo();
        String oldPolicyNo = sourceInsuredList.get(0).getPolicyNo();
        String newOrderId = createOrder.getFhOrderId();

        List<InsuranceRenewPo> insuranceRenewPoList = insuranceRenewMapper.listRenewDataByOldPolicyNo(oldPolicyNo);

        if (CollectionUtils.isNotEmpty(insuranceRenewPoList)) {
            if (insuranceRenewPoList.stream().anyMatch(x -> OrderRenewalStatus.renewalOverStatus().contains(x.getInsStatus()))) {
                log.warn("该原单{}已续保或退保", oldPolicyNo);
                return;
            }

            InsuranceRenewPo insuranceRenewPo = insuranceRenewPoList.get(0);
            insuranceRenewPo.setNewPolicyNo(newPolicyNo);
            insuranceRenewPo.setNewOrderId(newOrderId);
            insuranceRenewPo.setInsStatus(OrderRenewalStatus.RENEWED.name().toLowerCase());
            insuranceRenewMapper.updateByPrimaryKeySelective(insuranceRenewPo);
            return;
        }

        SmOrderApplicant orderApplicant = applicantMapper.selectByOrderId(sourcePolicyOrder.getFhOrderId());

        //处理insurance_renewal逻辑
        InsuranceRenewPo insuranceRenewPo = new InsuranceRenewPo();
        //查找订单id
        insuranceRenewPo.setPlanId(sourcePolicyOrder.getPlanId());
        insuranceRenewPo.setOldOrderId(sourcePolicyOrder.getFhOrderId());
        insuranceRenewPo.setOldPolicyNo(oldPolicyNo);
        insuranceRenewPo.setNewOrderId(newOrderId);
        insuranceRenewPo.setNewPolicyNo(newPolicyNo);
        if (Objects.nonNull(orderApplicant)) {
            insuranceRenewPo.setApplicantPersonName(orderApplicant.getPersonName());
            insuranceRenewPo.setApplicantIdNumber(orderApplicant.getIdNumber());
        }
        insuranceRenewPo.setInsuredPersonName(sourceInsuredList.stream().map(PersonVo::getName).collect(Collectors.joining(",")));
        insuranceRenewPo.setInsuredIdNumber(sourceInsuredList.stream().map(PersonVo::getIdCard).collect(Collectors.joining(",")));

        insuranceRenewPo.setInsStatus(OrderRenewalStatus.RENEWED.name().toLowerCase());
//        insuranceRenewPo.setGraceDay();
//        insuranceRenewPo.setSurplusDay();
//        insuranceRenewPo.setBeforeExpirationDay();
//        insuranceRenewPo.setAfterExpirationDay();
        BigDecimal totalAmount = sourcePolicyOrder.getTotalAmount();
        insuranceRenewPo.setTotalAmount(sourcePolicyOrder.getTotalAmount());
        if (Objects.equals(EnumProductAttr.GROUP.getCode(), sourcePolicyOrder.getProductAttrCode())) {
            totalAmount = sourceInsuredList.stream().map(
                    x -> Optional.ofNullable(x.getUnitPrice()).filter(StringUtils::isNotEmpty).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                            .multiply(
                                    Optional.ofNullable(x.getQty()).map(String::valueOf).map(BigDecimal::new).orElse(BigDecimal.ZERO)
                            )
            ).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }

        insuranceRenewPo.setTotalAmount(totalAmount);
        insuranceRenewPo.setProductId(sourcePolicyOrder.getProductId());
        insuranceRenewPo.setPlanId(sourcePolicyOrder.getPlanId());
        insuranceRenewPo.setProductAttrCode(sourcePolicyOrder.getProductAttrCode());
        insuranceRenewPo.setProductName(sourcePolicyOrder.getProductName());
        insuranceRenewPo.setChannel(sourcePolicyOrder.getChannel());
        insuranceRenewPo.setStartTime(LocalDateUtil.dateToLocaldatetime(sourcePolicyOrder.getStartTime()));
        insuranceRenewPo.setInvalidTime(LocalDateUtil.dateToLocaldatetime(sourcePolicyOrder.getEndTime()));
        insuranceRenewPo.setCustomerAdminId(sourcePolicyOrder.getCustomerAdminId());
        insuranceRenewPo.setWxOpenId(sourcePolicyOrder.getWxOpenId());
        insuranceRenewPo.setTodoState(0);

        insuranceRenewMapper.insert(insuranceRenewPo);
    }

    /**
     * 个险按被保人维度获取转投保单
     * @param insuranceRenewPo
     * @return
     */
    public List<TransferPolicyVo> personTransferPolicy(InsuranceRenewPo insuranceRenewPo) {

        WaitedPolicyVo policyVo = new WaitedPolicyVo();
        policyVo.setEndTime(Objects.isNull(insuranceRenewPo.getInvalidTime())?null:insuranceRenewPo.getInvalidTime());
        policyVo.setIdNumber(insuranceRenewPo.getInsuredIdNumber());


        List<TransferPolicyVo> policyVos = insuranceRenewMapper.getPersonTransferPolicy(policyVo);
        return policyVos;
    }

    /**
     * 个险按被保人维度获取转投保单
     * @param insuranceRenewPo
     * @return
     */
    public List<TransferPolicyVo> autoQueryPersonTransferPolicy(InsuranceRenewPo insuranceRenewPo) {

        WaitedPolicyVo policyVo = new WaitedPolicyVo();
        policyVo.setEndTime(Objects.isNull(insuranceRenewPo.getInvalidTime())?null:insuranceRenewPo.getInvalidTime());
        policyVo.setIdNumber(insuranceRenewPo.getInsuredIdNumber());
        policyVo.setRiskCategory2(insuranceRenewPo.getRiskCategory2());
            policyVo.setStartTime(insuranceRenewPo.getStartTime());
        policyVo.setStartDate(LocalDateTimeUtil.offset(insuranceRenewPo.getInvalidTime(),-60, ChronoUnit.DAYS));
        policyVo.setEndDate(LocalDateTimeUtil.offset(insuranceRenewPo.getInvalidTime(),30, ChronoUnit.DAYS));
        List<TransferPolicyVo> policyVos = insuranceRenewMapper.autoQueryPersonTransferPolicy(policyVo);
        return policyVos;
    }

    /**
     * 团险按投保人维度获取转投保单
     * @param insuranceRenewPo
     * @return
     */
    public List<TransferPolicyVo> autoQueryGroupTransferPolicy(InsuranceRenewPo insuranceRenewPo) {

        WaitedPolicyVo policyVo = new WaitedPolicyVo();
        policyVo.setEndTime(Objects.isNull(insuranceRenewPo.getInvalidTime())?null:insuranceRenewPo.getInvalidTime());
        policyVo.setIdNumber(insuranceRenewPo.getApplicantIdNumber());
        policyVo.setRiskCategory2(insuranceRenewPo.getRiskCategory2());
        policyVo.setStartTime(insuranceRenewPo.getStartTime());
        policyVo.setStartDate(LocalDateTimeUtil.offset(insuranceRenewPo.getInvalidTime(),-60, ChronoUnit.DAYS));
        policyVo.setEndDate(LocalDateTimeUtil.offset(insuranceRenewPo.getInvalidTime(),30, ChronoUnit.DAYS));
        List<TransferPolicyVo> policyVos = insuranceRenewMapper.autoQueryGroupTransferPolicy(policyVo);

        return CollectionUtils.isNotEmpty(policyVos)?
                //过滤续保保单与原保单号是同一个的情况
                policyVos.stream().filter(p->!Objects.equals(p.getPolicyNo(),insuranceRenewPo.getOldPolicyNo())).collect(Collectors.toList())
                : policyVos;
    }



    /**
     * 团险按投保人维度获取转投保单
     * @param insuranceRenewPo
     * @return
     */
    public List<TransferPolicyVo> groupTransferPolicy(InsuranceRenewPo insuranceRenewPo) {

        WaitedPolicyVo policyVo = new WaitedPolicyVo();
        policyVo.setEndTime(Objects.isNull(insuranceRenewPo.getInvalidTime())?null:insuranceRenewPo.getInvalidTime());
        policyVo.setIdNumber(insuranceRenewPo.getApplicantIdNumber());

        List<TransferPolicyVo> policyVos = insuranceRenewMapper.getGroupTransferPolicy(policyVo);

        return CollectionUtils.isNotEmpty(policyVos)?
                //过滤续保保单与原保单号是同一个的情况
                policyVos.stream().filter(p->!Objects.equals(p.getPolicyNo(),insuranceRenewPo.getOldPolicyNo())).collect(Collectors.toList())
                : policyVos;
    }

    @Override
    public List<TransferPolicyVo> getTransferPolicyList(String fhOrderId, String productAttrCode) {
        InsuranceRenewPo insuranceRenewPo = insuranceRenewMapper.getRenewDataByOldOrderId(fhOrderId);

        if (EnumProductAttr.PERSON.getCode().equals(productAttrCode)) {
            //个险按被保人维度获取转投保单
            return personTransferPolicy(insuranceRenewPo);
        } else {
            //团险按投保人维度获取转投保单
            return groupTransferPolicy(insuranceRenewPo);
        }
    }

    @Override
    public List<TransferPolicyVo> listAutoQueryTransferPolicyList(InsuranceRenewPo insuranceRenewPo) {
        //InsuranceRenewPo insuranceRenewPo = insuranceRenewMapper.getRenewDataByOldOrderId(fhOrderId);

        if (EnumProductAttr.PERSON.getCode().equals(insuranceRenewPo.getProductAttrCode())) {
            //个险按被保人维度获取转投保单
            return autoQueryPersonTransferPolicy(insuranceRenewPo);
        } else {
            //团险按投保人维度获取转投保单
            return autoQueryGroupTransferPolicy(insuranceRenewPo);
        }
    }
    /**
     * 注意，该方法已注销
     * 待续列表数据由insurance_renewal表改成policy_renewal_base_info表
     * @param size
     * @return
     */
    @Override
    public List<InsuranceRenewPo> listWaitInsuranceRenewal(Integer size){
        return insuranceRenewMapper.listWaitInsuranceRenewal(size>1000?1000:size);
    }

    public List<InsuranceRenewPo> listWaitInsuranceRenewalV2(Integer size){
        return insuranceRenewMapper.listWaitInsuranceRenewalV2(size>1000?1000:size);
    }

    public int updateUpdateTime(Integer id){

        return insuranceRenewMapper.modifyUpdateTime(id);
    }

    public int modifyUpdateTimeByOldPolicyNo(String oldPolicyNo){

        return insuranceRenewMapper.modifyUpdateTimeByOldPolicyNo(oldPolicyNo);
    }
}
