package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.dto.ElementTreeNodeDTO;
import com.cfpamf.ms.bms.facade.vo.EmployeeVO;
import com.cfpamf.ms.bms.facade.vo.FdSimpleUserVO;
import com.cfpamf.ms.bms.facade.vo.UserLoginVO;
import com.cfpamf.ms.insur.admin.constant.BizExceptEnum;
import com.cfpamf.ms.insur.admin.dao.safes.BmsSyncRecordMapper;
import com.cfpamf.ms.insur.admin.enums.SyncStatusEnum;
import com.cfpamf.ms.insur.admin.enums.SyncTypeEnum;
import com.cfpamf.ms.insur.admin.pojo.dto.UserDTO;
import com.cfpamf.ms.insur.admin.pojo.po.BmsSyncRecord;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.XxlLogger;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: zhangjian
 * @date: 2020/6/30 11:50
 * @description:
 */
@Slf4j
@Service
public class UserSyncService {

    private static final String DEFAULT_START_DATE = "2015-01-01 00:00:00";

    @Autowired
    private BmsService bmsService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserPostService userPostService;

    @Autowired
    private BmsSyncRecordMapper bmsSyncRecordMapper;

    @Autowired
    private ObjectMapper jsonMapper;

    public void autoSyncIncrementUserPost() {
        log.info("增量同步职位信息");
        String batchNo = DateUtils.getCurrentOrder();
        BmsSyncRecord syncRecord = addSyncRecord(SyncTypeEnum.POST, batchNo);
        syncUserPost(syncRecord);
    }

    public void autoSyncAllUserPost() {
        log.info("全量同步职位信息");
        manualAllSyncUserPost();
    }

    public void manualSyncUserPost(Date startDate, Date endDate) {
        String batchNo = DateUtils.getCurrentOrder();
        BmsSyncRecord syncRecord = addSyncRecord(SyncTypeEnum.POST, batchNo, startDate, endDate);
        syncUserPost(syncRecord);
    }

    public void manualAllSyncUserPost() {
        String batchNo = DateUtils.getCurrentOrder();
        BmsSyncRecord syncRecord = addSyncRecord(SyncTypeEnum.POST, batchNo, DateUtil.parseDate(DEFAULT_START_DATE), null);
        syncAllUserPost(syncRecord);
    }


    private void syncUserPost(BmsSyncRecord syncRecord) {
        log.info("start sync,startdate：{},stopdate：{}", syncRecord.getStartDate(), syncRecord.getStopDate());
        XxlLogger.info(log, "开始同步职位信息,同步开始时间为" + syncRecord.getStartDate() + "结束时间为" + syncRecord.getStopDate());
        long begin = DateUtil.getNowSeconds();
        List<EmployeeVO> employeeVOList = bmsService.listEmployeeByUpdateTime(syncRecord.getStartDate(), syncRecord.getStopDate());
        //输出同步数据
        log.info("同步{}bms员工数据，employeeVOList={}", "增量", JSONObject.toJSONString(employeeVOList));
        toJSON("增量", employeeVOList);

        log.info("sync employee's position count:{}", employeeVOList.size());
        XxlLogger.info(log, "同步员工职位信息条数" + employeeVOList.size());
        doUserPost(syncRecord, employeeVOList);
        log.info("end sync,cost time:{}", DateUtil.getNowSeconds() - begin);
        XxlLogger.info(log, "同步职位信息结束,花费时间为:" + (DateUtil.getNowSeconds() - begin));

        //每次同步完都为没有业务编码的生成一个业务编码
        userService.initBizCodes();
    }

    private void syncAllUserPost(BmsSyncRecord syncRecord) {
        log.info("开始全量同步职位信息,同步开始时间为{},结束时间为{}", syncRecord.getStartDate(), syncRecord.getStopDate());
        XxlLogger.info(log, "开始同步职位信息,同步开始时间为" + syncRecord.getStartDate() + "结束时间为" + syncRecord.getStopDate());
        long begin = DateUtil.getNowSeconds();
        List<EmployeeVO> employeeVOList = bmsService.listEmployeeByUpdateTime(syncRecord.getStartDate(), syncRecord.getStopDate());
        //获取保险系统所有客户
        Map<String, UserVO> insUserMap = loadAllUserInfo();
        //获取所有区域信息
        Map<String, String> regionMap = getRegionMap();
        try {

            syncRecord.setSyncCount(employeeVOList.size());
            userPostService.batchAddUserPost(employeeVOList, insUserMap, regionMap, syncRecord.getBatchNo());
            log.info("同步员工职位信息条数:{}", employeeVOList.size());
            XxlLogger.info(log, "同步员工职位信息条数:" + employeeVOList.size());
            syncRecord.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            syncRecord.setFailCount(employeeVOList.size());
            syncRecord.setRemark(e.getMessage().length() > 1000 ? e.getMessage().substring(0, 1000) : e.getMessage());
            syncRecord.setSyncStatus(SyncStatusEnum.FAIL.getCode());
            log.warn("employee sync exception ", e);
        }
        bmsSyncRecordMapper.updateByPrimaryKeySelective(syncRecord);
        log.info("同步职位信息结束,花费时间为:{}", DateUtil.getNowSeconds() - begin);
        XxlLogger.info(log, "同步职位信息结束,花费时间为:" + (DateUtil.getNowSeconds() - begin));
        userService.initBizCodes();
        XxlLogger.info(log, "生成推荐码完成");
    }


    private void doUserPost(BmsSyncRecord syncRecord, List<EmployeeVO> employeeList) {
        //获取保险系统所有客户
        Map<String, UserVO> insUserMap = loadAllUserInfo();
        //获取所有区域信息
        Map<String, String> regionMap = getRegionMap();

        Set<String> failSyncRecord = new HashSet<String>();
        Date now = DateUtil.getNow();
        employeeList.forEach(employeeVO -> {
            try {
                userPostService.addUserPost(employeeVO, insUserMap, regionMap, syncRecord.getBatchNo(), now);
            } catch (Exception e) {
                failSyncRecord.add(employeeVO.getMainJobNumber());
                e.printStackTrace();
                log.warn("employee[{}] sync exception ", employeeVO.getMainJobNumber(), e);
            }
        });
        syncRecord.setSyncCount(employeeList.size() - failSyncRecord.size());
        syncRecord.setFailCount(failSyncRecord.size());
        String str = failSyncRecord.toString();
        syncRecord.setRemark(str.length() > 1000 ? str.substring(0, 1000) : str);
        syncRecord.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        bmsSyncRecordMapper.updateByPrimaryKeySelective(syncRecord);
    }

    public BmsSyncRecord addSyncRecord(SyncTypeEnum syncType, String batchNo, Date startDate, Date stopDate) {
        //手动同步不需要校验未同步成功记录
//        validateSync(syncType);
        BmsSyncRecord syncRecordPO = new BmsSyncRecord();
        syncRecordPO.setStartDate(startDate);
        if (stopDate == null) {
            syncRecordPO.setStopDate(DateUtil.getNow());
        } else {
            syncRecordPO.setStopDate(stopDate);
        }
        syncRecordPO.setBatchNo(batchNo);
        syncRecordPO.setSyncStatus(SyncStatusEnum.DOING.getCode());
        syncRecordPO.setSyncType(syncType.getCode());
        bmsSyncRecordMapper.insertSelective(syncRecordPO);
        return syncRecordPO;
    }


    private BmsSyncRecord addSyncRecord(SyncTypeEnum syncType, String batchNo) {
        //查询最近未同步成功的记录
        BmsSyncRecord lastDoingSyncRecord = getLastDoingBmsSyncRecord(syncType);
        Date startDate = null;
        if(lastDoingSyncRecord != null){
            //如果有未处理完成的记录,则把未处理完成记录的开始时间设置为当前开始时间
            startDate = lastDoingSyncRecord.getStartDate();
        }else{
            BmsSyncRecord lastSyncSuccessRecord = bmsSyncRecordMapper.getLastSyncRecordBySyncType(syncType.getCode(), SyncStatusEnum.SUCCESS.getCode());
            if (lastSyncSuccessRecord != null) {
                // 开始时间往前推 30 分钟
                startDate = getMinutesBeforeOrAfter(lastSyncSuccessRecord.getStopDate(), -30);
            } else {
                startDate = DateUtil.parseDate(DEFAULT_START_DATE);
            }
        }
        //结束时间往后推 5 分钟
        Date stopDate = getMinutesBeforeOrAfter(DateUtil.getNow(), 5);
        BmsSyncRecord syncRecordPO = new BmsSyncRecord();
        syncRecordPO.setBatchNo(batchNo);
        syncRecordPO.setSyncStatus(SyncStatusEnum.DOING.getCode());
        syncRecordPO.setStartDate(startDate);
        syncRecordPO.setStopDate(stopDate);
        syncRecordPO.setSyncType(syncType.getCode());
        bmsSyncRecordMapper.insertSelective(syncRecordPO);
        return syncRecordPO;
    }

    private boolean validateSync(SyncTypeEnum syncType) {
        if (syncType != null) {
            BmsSyncRecord existsPreSync = bmsSyncRecordMapper.getLastSyncRecordBySyncType(syncType.getCode(), SyncStatusEnum.DOING.getCode());
            if (existsPreSync != null) {
                throw new MSBizNormalException(BizExceptEnum.RECORD_IS_DOING.getCode(), BizExceptEnum.RECORD_IS_DOING.getDesc());
            }
        } else {
            throw new MSBizNormalException(BizExceptEnum.NOT_SUPPORT_SYNC_TYPE.getCode(), BizExceptEnum.NOT_SUPPORT_SYNC_TYPE.getDesc());
        }
        return true;
    }

    public void syncSuccess(BmsSyncRecord bmsSyncRecord) {
        bmsSyncRecord.setSyncStatus(SyncStatusEnum.SUCCESS.getCode());
        bmsSyncRecord.setRemark("同步完成");
        bmsSyncRecordMapper.updateByPrimaryKeySelective(bmsSyncRecord);
    }

    public void syncFail(BmsSyncRecord bmsSyncRecord) {
        bmsSyncRecord.setSyncStatus(SyncStatusEnum.FAIL.getCode());
        bmsSyncRecordMapper.updateByPrimaryKeySelective(bmsSyncRecord);
    }

    public Map<String, UserVO> loadAllUserInfo() {
        XxlLogger.info(log, "get保险系统员工信息最新数据");
        log.info("get保险系统员工信息最新数据");
        List<UserVO> insUsers = userService.getUserList(null)
                .stream()
                .filter(iu -> !StringUtils.isEmpty(iu.getUserId()))
                .collect(Collectors.toList());
        return insUsers.stream()
                .collect(Collectors.toMap(t -> t.getUserId().toUpperCase(), t -> t));
    }


    public Map<String, String> getRegionMap() {
        List<ElementTreeNodeDTO> orgTree = bmsService.getAreaBranchTree();
        Map<String, String> orgMap = new HashMap<>();
        orgTree.forEach(o -> {
            if (o.getChildren() != null) {
                o.getChildren().forEach(c -> {
                    orgMap.put(c.getPath(), o.getOrgName());
                });
            }
            orgMap.put(o.getPath(), o.getOrgName());
            orgMap.put(o.getOrgName(), o.getOrgCode());
        });
        return orgMap;
    }

    public AuthUserVO syncSingleLoginUser(UserLoginVO userLoginVO, String mobile, String jobCode) {
        try {
            AuthUserVO vo = userService.getAuthUserByUserId(userLoginVO.getBranchVO().getJobNumber().toUpperCase());
            if (vo == null) {
                vo = new AuthUserVO();
                UserDTO dto = userPostService.addUserPost(getRegionMap(), userLoginVO, mobile, jobCode);
                BeanUtils.copyProperties(dto, vo);
            } else {
                userPostService.insertUserPost(userLoginVO, mobile, jobCode, vo.getRegionCode(), vo.getRegionName());
            }
            return vo;
        } catch (MSBizNormalException e) {
            if (e.getErrorCode().equals(BizExceptEnum.NOT_EXIST_JOBCODE.getCode())) {
                throw e;
            }
        } catch (Exception e) {
            log.warn("同步登录岗位信息失败", e);
        }
        return null;
    }

    public void syncSingleBindUser(FdSimpleUserVO mainJob, String regionCode, String organizationName, Integer hrUserId) {
        UserPost userPost = new UserPost();
        BeanUtils.copyProperties(mainJob, userPost);
        userPost.setMainJobNumber(mainJob.getMasterJobNumber());
        userPost.setHrUserId(hrUserId);
        userPost.setBmsUserId(mainJob.getUserId());
        userPost.setRegionCode(regionCode);
        userPost.setHrOrgId(mainJob.getHrOrgId().toString());
        userPost.setOrganizationName(organizationName);
        userPostService.insert(userPost);

    }

    private void toJSON(String desc, List<EmployeeVO> employeeVOList) {
        try {
            log.info("同步{}bms员工数据，employeeVOList={}", desc, jsonMapper.writeValueAsString(employeeVOList));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取指定分钟前或者指定分钟后的 Date 对象
     *
     * @param date    原始日期对象
     * @param minutes 指定的分钟数，可以为正数也可以为负数
     * @return 返回新的 Date 对象
     */
    private static Date getMinutesBeforeOrAfter(Date date, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }

    //获取最近96条同步记录是否有未完成的记录
    private BmsSyncRecord getLastDoingBmsSyncRecord(SyncTypeEnum syncType) {
        List<BmsSyncRecord> existsPreSyncList = null;
        BmsSyncRecord lastExistsPreSync = null;
        if (syncType != null) {
            //查询最近96条同步记录
            existsPreSyncList = bmsSyncRecordMapper.getSyncRecordBySyncType(syncType.getCode(), 96);
            if(CollectionUtils.isEmpty(existsPreSyncList)){
                //没有同步未完成的记录返回null
                return null;
            }
            //从最近48条同步记录找连续未同步成功的第一条记录
            for(BmsSyncRecord syncRecord : existsPreSyncList){
                //如果找到一条处理成功则返回前一条处理失败的记录
                if(Objects.equals(SyncStatusEnum.SUCCESS.getCode(),syncRecord.getSyncStatus())){
                    return lastExistsPreSync;
                }else{
                    lastExistsPreSync = syncRecord;
                }
            }
        } else {
            throw new MSBizNormalException(BizExceptEnum.NOT_SUPPORT_SYNC_TYPE.getCode(), BizExceptEnum.NOT_SUPPORT_SYNC_TYPE.getDesc());
        }
        //如果所有记录都为失败则抛异常,需要人工干预
        throw new MSBizNormalException(BizExceptEnum.RECORD_IS_DOING.getCode(), BizExceptEnum.RECORD_IS_DOING.getDesc());
    }
}
