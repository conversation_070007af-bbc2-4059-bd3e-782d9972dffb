package com.cfpamf.ms.insur.admin.service;

import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.bms.facade.vo.UserRoleVO;
import com.cfpamf.ms.insur.admin.claim.form.PushOutTimeDayVO;
import com.cfpamf.ms.insur.admin.claim.vo.TextDingDingMsgContent;
import com.cfpamf.ms.insur.admin.constant.ClaimRiskType;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimHastenMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.event.handler.WxPushMessageHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.ClaimProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmClaimHasten;
import com.cfpamf.ms.insur.admin.pojo.query.SmClaimHastenQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmClaimHastenResultVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmClaimHastenVO;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.config.ClaimConfig;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.config.mq.MqConstants;
import com.cfpamf.ms.insur.base.config.mq.RabbitMqUtils;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimExpressReceiverVO;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Create By zhengjing on 2019-08-22 17:14
 */
@Service
@Slf4j
public class SmClaimHastenService {

    private static final String HASTEN_SUCCESS = "";
    private static String zbOpenId;
    /**
     * 微信参数配置
     */
    @Autowired
    private WechatConfig wechatConfig;
    @Autowired
    private SmClaimHastenMapper hastenMapper;
    @Autowired
    private SmClaimMapper claimMapper;

    @Autowired
    private BmsService bmsService;

    @Autowired
    private RabbitMqUtils rabbitMqUtils;

    @Autowired
    private UserService userService;


    /**
     * 微信代理service
     */
    @Lazy
    @Autowired
    private WxMpServiceProxy serviceProxy;
    @Autowired
    private ClaimConfig claimConfig;
    @Autowired
    private AuthUserMapper authUserMapper;
    @NotNull
    @Value("${wechat.message.claimResultId}")
    private String templateId;

    @Lazy
    @Autowired
    private WxPushMessageHandler wxPushMessageHandler;


    /**
     * bms配置
     */
    @Autowired
    private BmsConfig bmsConfig;

    @Autowired
    private SmClaimServiceImpl service;

    /**
     * 批量催办
     *
     * @param claimIds 需要催办的id集合
     * @param userId   催办人【自动催办传null】
     * @return 返回每个claimId 的处理结果
     */
    public List<SmClaimHastenResultVO> hasten(List<Integer> claimIds, String userId) {

        if (CollectionUtils.isEmpty(claimIds)) {
            return Collections.emptyList();
        }
        //查询上一个环节的处理结果

        Map<Integer, ClaimProgressDTO> proMap = service.getIntegerClaimProgressDTOMap(claimIds);
        List<SmClaimHastenResultVO> res = Collections.synchronizedList(new ArrayList<>());

        log.info("上一个环节的处理结果以及处理人:{}", proMap);
        LocalDateTime now = LocalDateTime.now();
        claimIds.parallelStream().forEach(claimId -> {
            ClaimProgressDTO claimProgressDTO = proMap.get(claimId);
            if (claimProgressDTO == null) {
                res.add(new SmClaimHastenResultVO(claimId, getHastenError(claimId + "", " 未找到上一个处理流程！")));
            } else {

                // 判断是够可以催办
                if (Objects.isNull(claimProgressDTO.getProcessorUserName())) {
                    res.add(new SmClaimHastenResultVO(claimId, getHastenError(claimProgressDTO.getClaimNo(), "当前环节处理人为空")));
                    return;
                }

                if (Objects.nonNull(claimProgressDTO.getHastenTime()) &&
                        ChronoUnit.DAYS.between(claimProgressDTO.getHastenTime(), now) < 2) {
                    res.add(new SmClaimHastenResultVO(claimId, getHastenError(claimProgressDTO.getClaimNo(), "距离上次催办间隔2天以上才可以再次催办！")));
                    return;
                }
                if (Objects.equals("HOLDER", claimProgressDTO.getProcessorUserId())) {
                    claimProgressDTO.setProcessorUserId(claimConfig.getApprover().getUserId());
                    claimProgressDTO.setProcessorUserName(claimConfig.getApprover().getUserName());
                    claimProgressDTO.setWxOpenId(getZbOpenId());
                }
                //如果当前环节是机构审核 获取主任openId
                String orgHeadOpenId = null;
                if (claimProgressDTO.isOrgAudit()) {
                    orgHeadOpenId = getOrgHeadOpenId(claimProgressDTO.getClaimId());
                }

                //如果不是
                if ((Objects.isNull(claimProgressDTO.getWxOpenId()) && !claimProgressDTO.isOrgAudit()) ||
                        (claimProgressDTO.isOrgAudit() && Objects.isNull(orgHeadOpenId))) {
                    res.add(new SmClaimHastenResultVO(
                            claimId,
                            getHastenError(claimProgressDTO.getClaimNo(), " 当前环节处理人" + claimProgressDTO.getProcessorUserName() + "未绑定微信")
                    ));
                    return;
                }

                //构建催办对象
                String claimState = claimProgressDTO.getClaimState();
                SmClaimHasten hasten = new SmClaimHasten();
                hasten.setHastenTime(now);
                hasten.setCreateBy(userId);
                hasten.setClaimId(claimId);
                hasten.setClaimState(claimState);
                LocalDateTime claimStateTime = claimProgressDTO.getClaimStateTime();
                long between = ChronoUnit.DAYS.between(claimStateTime, now);
                SmClaimHastenResultVO result = new SmClaimHastenResultVO(claimId, HASTEN_SUCCESS);

                //催办 补充资料
                if (StringUtils.startsWith(claimState, "stepDataPrepare")) {
                    if (between >= 3) {
                        serviceProxy.sendTemplateMsg(buildDataPreMessage(claimProgressDTO, getTimeoutStr(claimStateTime, 3)));
                    } else {
                        result.setResult(getHastenError(claimProgressDTO.getClaimNo(), "补充资料未超过三天"));
                    }
                    //催办 邮寄资料
                } else if (Objects.equals(claimState, "stepPostExpress")) {
                    if (between >= 3) {
                        serviceProxy.sendTemplateMsg(buildMailMessage(claimProgressDTO, getTimeoutStr(claimStateTime, 3)));
                    } else {
                        result.setResult(getHastenError(claimProgressDTO.getClaimNo(), "邮寄资料未超过三天"));
                    }
                    //催办 审核资料  机构对接人 主任
                } else if (claimProgressDTO.isOrgAudit()) {
                    if (between >= 2) {
                        WxMpTemplateMessage wxMpTemplateMessage = buildCheckMessage(claimProgressDTO, getTimeoutStr(claimStateTime, 2));

                        //机构对接人
//                        if (StringUtils.isNotBlank(wxMpTemplateMessage.getToUser())) {
//                            serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
//                        }
                        //主任
                        if (Objects.nonNull(orgHeadOpenId)) {
                            wxMpTemplateMessage.setToUser(orgHeadOpenId);
                            serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
                        }
                    } else {
                        result.setResult(getHastenError(claimProgressDTO.getClaimNo(), "审核时间未超过两天"));
                    }
                } else {
                    result.setResult(getHastenError(claimProgressDTO.getClaimNo(), " 状态[" + claimProgressDTO.getClaimState() + "]无法催办"));
                }

                if (Objects.equals(result.getResult(), HASTEN_SUCCESS)) {
                    hastenMapper.insert(hasten);
                }
                res.add(result);

            }
        });
        return res;
    }


    private static String formatInsured(ClaimProgressDTO dto) {

        if (com.alibaba.druid.util.StringUtils.isEmpty(dto.getCustomerAdminName())) {
            return dto.getCustomerName();
        }
        return String.format("%s（%s提交）", dto.getCustomerName(), dto.getCustomerAdminName());
    }

    // 消息
    private WxMpTemplateMessage buildDataPreMessage(ClaimProgressDTO dto, String timeoutStr) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
//        WxClaimExpressReceiverVO expressVO = getWxClaimExpressVO(dto.getClaimId());
        wtm.setUrl(getWxNotifyLinkUrl());
        wtm.setTemplateId(templateId);
        wtm.setToUser(dto.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，您的客户 %s 的理赔报案需补充资料，请您尽快处理，以免影响正常理赔。\r\n", dto.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(dto)));
        //        wtm.addData(new WxMpTemplateData("keyword1", String.format("请尽快上传快递信息！请将已提交的所有电子版资料的纸质原件邮寄至该地址: %s, 联系人: %s, 电话: %s, 备注: %s, 邮寄资料后请上传快递信息。", expressVO.getReceiverAddress(), expressVO.getReceiverName(), expressVO.getReceiverPhone(), expressVO.getOtherMsg()), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("需补充资料： %s ", dto.getOValue()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", String.format("超时：%s", timeoutStr), "#030303"));
        return wtm;
    }


    /**
     * 获取机构主任 openId
     *
     * @param claimId
     * @return
     */
    private String getOrgHeadOpenId(Integer claimId) {
        try{
            return wxPushMessageHandler.getOrgHeadOpenId(claimId);
        } catch(Exception e) {
            return null;
        }
    }

    /**
     * 获取总部对接人的openId
     *
     * @return
     */
    private synchronized String getZbOpenId() {
        if (zbOpenId == null) {
            zbOpenId = authUserMapper.getAuthUserByUserId(claimConfig.getApprover().getUserId()).getWxOpenId();
        }
        return zbOpenId;
    }

    /**
     * 邮寄资料
     *
     * @param dto
     * @return
     */
    private WxMpTemplateMessage buildMailMessage(ClaimProgressDTO dto, String timeoutStr) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();

        WxClaimExpressReceiverVO expressVO = new WxClaimExpressReceiverVO();
        expressVO.setDataJson(dto.getDataJson());
        wtm.setUrl(getWxNotifyLinkUrl());
        wtm.setTemplateId(templateId);
        wtm.setToUser(dto.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，您的客户 %s 的理赔报案需邮寄纸质资料，请您尽快处理，以免影响正常理赔。\r\n", dto.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(dto)));
        wtm.addData(new WxMpTemplateData(
                "keyword2",
                String.format(
                        "请尽快上传快递信息！请将已提交的 所有电子版资料的纸质原件邮寄至该地址: %s, 联系人:%s，电话: %s，邮寄资料后，请上传快递信息",
                        expressVO.getReceiverAddress(),
                        expressVO.getReceiverName(),
                        expressVO.getReceiverPhone()
                ),
                "#0000FF"
        ));
//        wtm.addData(new WxMpTemplateData("remark", String.format("超时：%s\n" +
//                "理赔专员：%s", timeoutStr, StringUtils.isNotBlank(dto.getSettlement()) ? dto.getSettlement() : ""), "#030303"));
        return wtm;
    }

    /**
     * 审核
     *
     * @param dto
     * @param timeoutStr
     * @return
     */
    private WxMpTemplateMessage buildCheckMessage(ClaimProgressDTO dto, String timeoutStr) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();

        wtm.setUrl(getWxNotifyLinkUrl());
        wtm.setTemplateId(templateId);
        wtm.setToUser(dto.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，请您尽快审核客户 %s 的理赔资料，以免影响正常理赔。\r\n", dto.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(dto), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "审批资料", "#030303"));
//        wtm.addData(new WxMpTemplateData("remark", String.format("超时：%s", timeoutStr), "#030303"));
        return wtm;
    }

    /**
     * 获取微信理赔待办链接URL
     *
     * @return
     */
    private String getWxNotifyLinkUrl() {
        return String.format(wechatConfig.getBackJumpUrl(), wechatConfig.getBackClaimTodoUrl(), "");
    }

    /**
     * 计算超时天数  超时0天计算超时小时
     *
     * @param claimStateTime
     * @param days
     * @return
     */
    private String getTimeoutStr(LocalDateTime claimStateTime, long days) {
        long between = ChronoUnit.DAYS.between(claimStateTime, LocalDateTime.now());
        long timeoutDays = between - days;
        if (timeoutDays > 0) {
            return timeoutDays + "天";
        }
        long timeoutHours = ChronoUnit.HOURS.between(claimStateTime, LocalDateTime.now()) - days * 24;
        if (timeoutHours == 0) {
            timeoutHours = 1;
        }
        return timeoutHours + "小时";
    }

    /**
     * @param claimNo 理赔编号
     * @param message 失败原因
     * @return
     */
    private String getHastenError(String claimNo, String message) {

        return String.format("编号:%s处理失败，失败原因:%s。", claimNo, message);
    }

    /**
     * 理赔邮件资料
     *
     * @return
     */
    private WxClaimExpressReceiverVO getWxClaimExpressVO(int claimId) {
        return claimMapper.getWxClaimExpress(claimId);
    }

    public PageInfo<SmClaimHastenVO> page(SmClaimHastenQuery query) {

        if (Objects.isNull(query.getTimeout()) || query.getTimeout() < 0) {
            query.setTimeout(0);
        }

        if (StringUtils.isNotEmpty(query.getClaimStateList())) {
            query.setClaimStates(Arrays.asList(query.getClaimStateList().split(",")));
        }

        List<UserRoleVO> userRoleVOS = bmsService.listUserByRoleCode(bmsConfig.getClaimClerkCode());

        Map<String, String> settelementMap = Optional.ofNullable(userRoleVOS)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .orElse(Collections.emptyList())
                .stream()
                .collect(
                        Collectors.toMap(UserRoleVO::getJobNumber, UserRoleVO::getUserName, (t1, t2) -> t1)
                );

        return PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> {
                    List<SmClaimHastenVO> smClaimHastenVOS = hastenMapper.fetchVoByQuery(query);
                    smClaimHastenVOS.stream()
                            //总部员工 取配置
                            .filter(vo -> Objects.equals(vo.getProcessor(), "HOLDER"))
                            .forEach(vo ->
                                     {
                                         vo.setProcessor(claimConfig.getApprover().getUserName());
                                     }
                            );
                });
    }

    String format = " 尊敬的理赔专员%s：\n" +
            "        以下是保司核赔中案件的统计数据，请您及时登录后台进行处理，谢谢!\n" +
            "        超过5天的门诊案件：%s件\n" +
            "        超过7天的住院案件：%s件\n" +
            "        超过20天的身故、伤残案件：%s件  ";

    public void msgPush(String mode) {

        List<PushOutTimeDayVO> clinicPushList = hastenMapper.statisticNotifyDate(5, Collections.singletonList(ClaimRiskType.ACCIDENT_CLINIC.getCode()));

        List<PushOutTimeDayVO> hospPushList = hastenMapper.statisticNotifyDate(7, Collections.singletonList(ClaimRiskType.ACCIDENT_HOSP.getCode()));

        List<PushOutTimeDayVO> deathPushList = hastenMapper.statisticNotifyDate(
                20,
                Lists.newArrayList(
                        ClaimRiskType.Death.getCode(),
                        ClaimRiskType.DISABILITY.getCode(),
                        ClaimRiskType.DISEASE_DEATH.getCode()
                )
        );
        Map<String, String> clinicPushMap = Optional.ofNullable(clinicPushList).filter(org.apache.commons.collections.CollectionUtils::isNotEmpty).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(PushOutTimeDayVO::getSettlement, PushOutTimeDayVO::getCount, (t1, t2) -> t1));

        Map<String, String> hospPushMap = Optional.ofNullable(hospPushList).filter(org.apache.commons.collections.CollectionUtils::isNotEmpty).orElse(Collections.emptyList())
                .stream().collect(Collectors.toMap(PushOutTimeDayVO::getSettlement, PushOutTimeDayVO::getCount, (t1, t2) -> t1));

        Map<String, String> deathPushMap = java.util.Optional.ofNullable(deathPushList).filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .orElse(Collections.emptyList()).stream().collect(Collectors.toMap(PushOutTimeDayVO::getSettlement, PushOutTimeDayVO::getCount, (t1, t2) -> t1));
        List<ClaimConfig.User> settlements = userService.getSettlements();

        if (org.apache.commons.collections.CollectionUtils.isEmpty(settlements)) {
            return;
        }

        settlements = settlements.stream().distinct().collect(Collectors.toList());

        Set<String> needPushSet = Stream.of(clinicPushMap.keySet(), hospPushMap.keySet(), deathPushMap.keySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        for (ClaimConfig.User user : settlements) {
            try{
                if (needPushSet.contains(user.getUserName())) {
                    if (Objects.equals(mode, "op")) {
                        rabbitMqUtils.sendMessage(
                                new TextDingDingMsgContent(
                                        user.getUserId(),
                                        String.format(
                                                format, user.getUserName()
                                                , clinicPushMap.getOrDefault(user.getUserName(), "0")
                                                , hospPushMap.getOrDefault(user.getUserName(), "0")
                                                , deathPushMap.getOrDefault(user.getUserName(), "0")
                                        )
                                ),
                                MqConstants.VALUE_EXCHANGE_INS,
                                MqConstants.VALUE_ROUTING_INS
                        );
                    } else if (Objects.equals(mode, "bms")) {
                        bmsService.sendDingTalkMessage(
                                String.format(
                                        format, user.getUserName()
                                        , clinicPushMap.getOrDefault(user.getUserName(), "0")
                                        , hospPushMap.getOrDefault(user.getUserName(), "0")
                                        , deathPushMap.getOrDefault(user.getUserName(), "0")
                                ),
                                user.getUserId()
                        );
                    }

                }

            } catch(Exception e) {
                log.warn("钉钉消息推送失败-{}", user.getUserId());
                log.warn("exceptionMsg-{}", e);
            }


        }


    }

}
