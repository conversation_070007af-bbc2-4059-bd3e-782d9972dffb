package com.cfpamf.ms.insur.admin.external.whale.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/25 9:46 上午
 * @Version 1.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class InsuredBaseInfo {

    private String productCode;

    private BigDecimal premium;

    @ApiModelProperty(value = "小鲸计划编码")
    private String planCode;

    @ApiModelProperty(value = "保司计划编码")
    private String companyPlanCode;

    @ApiModelProperty(value = "被保人编码")
    private String insuredCode;

    @ApiModelProperty(value = "被保主体姓名")
    private String insuredName;

    @ApiModelProperty("被保人性别 0：女 1：男")
    private Integer insuredGender;

    @ApiModelProperty("被保人国籍")
    private String insuredNation;

    @ApiModelProperty("被保人民族")
    private String insuredNationality;

    @ApiModelProperty("被保人手机号码")
    private String insuredMobile;

    @ApiModelProperty("被保人邮箱")
    private String insuredEmail;

    @ApiModelProperty("被保人婚姻状况")
    private String insuredMarital;

    @ApiModelProperty(value = "被保主体证件类型", example = "POLICY_ID_TYPE:0")
    private String insuredIdType;

    @ApiModelProperty(value = "被保主体证件号",example = "110101199003073853")
    private String insuredIdCard;

    @ApiModelProperty("被保人地址邮编")
    private String insuredPostcode;

    @ApiModelProperty("被保人居住省份编码")
    private String insuredProvinceCode;

    @ApiModelProperty("被保人居住城市编码")
    private String insuredCityCode;

    @ApiModelProperty("被保人居住地区编码")
    private String insuredRegionCode;

    @ApiModelProperty("被保人家庭住址")
    private String insuredAddress;

    @ApiModelProperty("被保人学历")
    private String insuredEducation;

    @ApiModelProperty("被保人工作单位")
    private String insuredCompany;

    @ApiModelProperty("职业类别")
    private String insuredOccupationalCategory;

    @ApiModelProperty("被保人职业")
    private String insuredCareer;

    @ApiModelProperty("是否拥有社会医疗保险 0:否 1:是")
    private Integer insuredMedicare;

    @ApiModelProperty("被保人职位")
    private String insuredPosition;

    @ApiModelProperty("被保主体证件起始时间")
    private String insuredIdCardValidityStart;

    @ApiModelProperty("被保主体证件截止时间")
    private String insuredIdCardValidityEnd;

    @ApiModelProperty("被保主体证件永久有效 0：否；1：是")
    private Integer insuredIdCardPermanent;

    @ApiModelProperty(value = "被保人出生日期", example = "1945-03-19")
    private String insuredBirthday;

    @ApiModelProperty(value = "被保人保单生效时年龄")
    private Integer insuredPolicyAge;

    @ApiModelProperty(value = "渠道推荐人编码", example = "123567")
    private String channelReferrerWno;

    @ApiModelProperty(value = "推荐人编码")
    private String referrerWno;

    @ApiModelProperty("电子保单地址")
    private String insuredPolicyUrl;

    @ApiModelProperty("险种列表")
    private List<ProductInfoList> productInfoList;

    public BigDecimal sumPremium() {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return BigDecimal.ZERO;
        } else {
            return productInfoList.stream()
                    .map(ProductInfoList::getPremium)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    public BigDecimal sumAmount() {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return BigDecimal.ZERO;
        } else {
            return productInfoList.stream()
                    .map(ProductInfoList::getCoverage)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }
}
