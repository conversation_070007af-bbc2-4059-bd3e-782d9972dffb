package com.cfpamf.ms.insur.admin.service.order;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingRespDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingValidateDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.JumpH5OtherParams;
import com.cfpamf.ms.insur.admin.pojo.dto.order.AsyncOrderHandDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderInsuredBuyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.query.BankListQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.BankListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.group.GroupAcceptContext;
import com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEPolicyResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKPayNotify;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 各个渠道的抽象
 *
 * <AUTHOR> 2020/3/17 16:01
 */
public interface SmOrderService {


    boolean support(String channel);

    OrderSubmitResponse submitOrder(String userUniqueId, OrderSubmitRequest dto);

    OrderSubmitResponse holdOrder(String userId, OrderSubmitRequest order);

    OrderSubmitResponse checkOrderData(String userUniqueId, OrderSubmitRequest dto);

    /**
     * 异步保存订单信息
     *
     * @param dto
     * @param smOrderResp
     * @param planVo
     */
    void saveOrderInfo(OrderSubmitRequest dto, OrderSubmitResponse smOrderResp, SmPlanVO planVo);

    /**
     * 泛华保险支付回调
     *
     * @param orderId
     * @return
     */
    void handFhOrderPayedCallback(String orderId);

    /**
     * 更新订单保单状态
     *
     * @param orderId
     * @param appStatus
     */
    void updateOrderAppStatus(String orderId, String appStatus);

    /**
     * 更新订单支付状态
     *
     * @param orderId
     * @param payStatus
     */
    void updateOrderPayStatus(String orderId, String payStatus);

    /**
     * 更新订单支付佣金和支付时间，
     * 抽取用户，计算佣金明细，代理人佣金明细
     * =========================================
     * 注意 一定要更新订单状态支付成功才能调用此方法
     *
     * @param orderId
     */
    void updateOrderPaymentTimeAndCommission(String orderId);

    /**
     * 与泛华同步更新存小额保险保单信息
     *
     * @param orderId
     * @return 更新保单后身份证保单状态map insIdNumber->appStatus
     */
    Map<String, String> updateOrderPolicyInfo(String orderId);

    /**
     * 同步电子保单信息
     *
     * @param orderId
     * @return 电子保单地址
     */
    default GroupEPolicyResponse notifyEPolicy(String orderId) {
        throw new UnsupportedOperationException("功能实现中...");
    }

    /**
     * 更新订单成已过期状态
     *
     * @param orderId
     * @return
     */
    void updateExpireOrderInfo(String orderId);

    /**
     * 查询订单信息
     *
     * @param orderId
     * @return
     */
    OrderQueryResponse getOrderInfo(String orderId);

    /**
     * 查询订单信息 （纯本地查询）
     *
     * @param orderId
     * @return
     */
    OrderQueryResponse getOrderInfoForLocal(String orderId);

    /**
     * 查询订单信息 （纯本地查询）
     *
     * @param orderId
     * @param version
     * @return
     */
    OrderQueryResponse getOrderInfoForLocal(String orderId, String version);

    /**
     * 查询车险订单信息
     *
     * @param orderId
     * @param version
     * @return
     */
    AutoOrderQueryResponse getAutoOrderInfoForLocal(String orderId, String version);

    /**
     * 获取订单支付URL
     *
     * @param orderId
     * @param type
     * @return
     */
    String getOrderPayUrl(String orderId, Integer type);

    /**
     * 处理异步回调
     *
     * @param object
     * @param response
     * @param request
     * @return
     */
    void handAsyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException;


    /**
     * 渠道支付后回调通知接口
     *
     * @param orderId
     * @param object
     * @param response
     * @param request
     * @throws IOException
     */
    void handSyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException;

    /**
     * 团单支付通知
     *
     * @param data
     * @return
     */
    default Object groupPayNotify(Object data) {
        throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR);
    }

    OrderPreAiCheckResp jumpAiCheck(String userUniqueId, OrderSubmitRequest dto, HttpServletResponse response);

    default List<AICheckQueryResponse> aiCheckQuery(String orderId, String questionnaireId) {
        return Collections.emptyList();
    }

    /**
     * 校验被保人是否超过上
     *
     * @param idNumbers
     * @param productId
     * @return
     */
    boolean checkSubmitOrderInsuredProductBuyLimit(List<SmOrderInsuredBuyDTO> idNumbers,
                                                   int productId);

    /**
     * @param orderId
     * @param familyQuestionnaireId
     * @param request
     * @param response
     * @throws IOException
     */
    default void handAICheckAccept(String orderId, String familyQuestionnaireId, HttpServletRequest request, HttpServletResponse response) throws IOException {

    }

    default void handAcceptSuccess(HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持回调！");
    }

    /**
     * 退保回调接口-针对个险的
     *
     * @param request
     * @param response
     * @throws IOException
     */
    default void handCancelCallBack(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持回调！");
    }

    /**
     * @param request
     * @param response
     * @throws IOException
     */
    default void handChannelCallBackGroupAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持团险回调！");
    }

    /**
     * 雇主责任险异步回调保单信息
     *
     * @param object
     * @param request
     * @param response
     * @throws IOException
     */
    default void handChannelCallBackEmployerAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持雇主责任险回调！");
    }

    /**
     * 雇主责任险异步回调保单批改信息
     *
     * @param object
     * @param request
     * @param response
     * @throws IOException
     */
    default void handChannelCallBackEmployerEndorAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持雇主责任险批改回调！");
    }

    /**
     * 雇主责任险异步回调保单信息
     *
     * @param object
     * @param request
     * @param response
     * @throws IOException
     */
    default void handChannelCallBackV2EmployerAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持雇主责任险回调！");
    }

    /**
     * 雇主责任险异步回调保单批改信息
     *
     * @param object
     * @param request
     * @param response
     * @throws IOException
     */
    default void handChannelCallBackV2EmployerEndorAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持雇主责任险批改回调！");
    }

    /**
     * 雇主责任险异步回调保单专票信息
     *
     * @param object
     * @param request
     * @param response
     * @throws IOException
     */
    default void handChannelCallBackV2EmployerTicketAccept(Object object, HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持雇主责任险专票回调！");
    }


    default void queryChannelGroupOrder(String groupPolicyNo) {
        throw new UnsupportedOperationException("该渠道不支持回调！");
    }

    default void queryChannelGroupEndorsement(String groupPolicyNo, String groupEndorsementNo) {
        throw new UnsupportedOperationException("该渠道不支持回调！");
    }


    /**
     * 团险回调消息处理
     *
     * @param order
     * @param notifyMessage
     * @param recommendId
     * @return
     * @throws IOException
     */
    default AsyncOrderHandDTO handleChannelGroupAccept(SmOrderGroupNotify order, SmOrderGroupNotifyMsg notifyMessage, String recommendId) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持回调！");
    }

    /**
     * 团险回调消息处理
     *
     * @param context
     * @return
     */
    default AsyncOrderHandDTO handleChannelGroupAcceptV2(GroupAcceptContext context) {
        throw new UnsupportedOperationException("该功能暂未实现");
    }

    /**
     * 手工处理回调
     *
     * @param request
     * @param response
     * @throws IOException
     */
    default void manualHandCallBack(HttpServletRequest request, HttpServletResponse response) throws IOException {

        throw new UnsupportedOperationException("该渠道不支持回调！");
    }

    /**
     * 渠道回访
     *
     * @param policyNoList
     */
    default void queryOrderVisitResult(List<String> policyNoList) {
        throw new UnsupportedOperationException("该渠道不支持回访！");
    }


    /**
     * 查询保司支持的银行列表
     *
     * @param query
     * @return
     */
    default BankListVO listBankInfo(BankListQuery query) {
        return new BankListVO();
        //throw new UnsupportedOperationException("该渠道不支持查询保司银行列表信息！");
    }

    /**
     * 绑卡
     *
     * @param reqDTO
     * @return
     */
    default BankCardBindingRespDTO bindingCard(BankCardBindingReqDTO reqDTO) {
        throw new UnsupportedOperationException("该渠道不支持绑卡！");
    }

    /**
     * @param validateDTO
     * @return
     */
    default boolean validateBind(BankCardBindingValidateDTO validateDTO) {
        throw new UnsupportedOperationException("该渠道不支持绑卡验证！");
    }

    default void redoNotify(String orderId) {
        throw new UnsupportedOperationException("该渠道不支持该操作！");
    }

    /**
     * 该渠道不支持绑卡消息回调
     */
    default void handBindCardCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持绑卡消息回调！");
    }

    /**
     * 该渠道不支持续保消息回调
     */
    default void handRenewalCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持续保消息回调！");
    }

    /**
     * 跳转绑卡页
     *
     * @param fhOrderId
     * @param request
     * @param response
     * @throws Exception
     */
    default void toInsurCompanyBind(String fhOrderId, String policyNo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        throw new UnsupportedOperationException("该渠道不支持跳转绑卡页！");
    }

    /**
     * 绑卡重定向
     *
     * @param request
     * @param response
     * @throws Exception
     */
    default void handChannelBindRedirect(HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持绑卡重定向！");
    }

    /**
     * 根据保单获取续保地址
     *
     * @param policyNo
     * @param customerAdminId
     */
    default String getRenewalUrl(String policyNo, String customerAdminId) {
        throw new UnsupportedOperationException("该渠道续保地址不存在！");
    }

    /**
     * 该渠道不支待续保资质推送
     */
    default void handWaitRenewalNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持待续保资质推送！");
    }

    /**
     * 获取h5跳转地址
     *
     * @param productId
     * @param otherParams
     * @return
     */
    default String getH5JumpUrl(Integer productId, String userId, String subChannel,
                                JumpH5OtherParams otherParams) {
        throw new UnsupportedOperationException("该渠道不支持H5跳转！");
    }

    /**
     * 补偿投保流程
     *
     * @param orderId
     */
    default void redoSelfPayNotify(String orderId) {
        throw new UnsupportedOperationException("功能未实现...");
    }


    /**
     * 初始化续期数据
     *
     * @param beforeDay
     */
    default void pushRenewalTerm(Integer beforeDay) {
    }

    /**
     * 线下支付出单
     *
     * @param data
     * @return
     */
    default Object handleOfflinePayNotify(Object data) {
        throw new UnsupportedOperationException("功能未实现...");
    }


    /**
     * 初始化续期数据
     *
     * @param beforeDay
     * @param defaultGraceDays
     */
    default void pushRenewalTerm(Integer beforeDay, Integer defaultGraceDays) {
    }

    default void notifyBackVisit(String orderId) {
    }

    /**
     * 团险批减人员-渠道通知请求
     *
     * @param data
     * @return
     */
    default Object groupReductionNotify(TKPayNotify data) {
        return null;
    }

    /**
     * 团险-人员替换
     *
     * @param data
     * @return
     */
    default Object memberChangeNotify(Object data) {
        throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR);
    }

    /**
     * 该渠道不支团险待续保资质推送
     */
    default void handGroupWaitRenewalNotify(HttpServletRequest request, HttpServletResponse response) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持团险待续保资质推送！");
    }

    default PlanRenewalConfigVo getPlanRenewalConfigByPlanId(String channel, Integer planId, String orderId) throws IOException {
        throw new UnsupportedOperationException("该渠道不支持获取续保产品服务！");
    }
}
