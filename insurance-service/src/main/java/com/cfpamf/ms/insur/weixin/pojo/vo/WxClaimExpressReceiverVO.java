package com.cfpamf.ms.insur.weixin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.Data;

/**
 * 微信理赔邮寄信息DTO
 *
 * <AUTHOR>
 **/
@Data
public class WxClaimExpressReceiverVO {

    /**
     * 邮寄地址
     */
    private String receiverAddress;

    /**
     * 联系人
     */
    private String receiverName;

    /**
     * 电话
     */
    private String receiverPhone;

    /**
     * 备注
     */
    private String otherMsg;

    /**
     * dataJson
     */
    private String dataJson;

    public String getReceiverAddress() {
        if (!StringUtils.isEmpty(dataJson)) {
            return JSON.parseObject(dataJson).getString("receiverAddress");
        }
        return null;
    }

    public String getReceiverName() {
        if (!StringUtils.isEmpty(dataJson)) {
            return JSON.parseObject(dataJson).getString("receiver");
        }
        return null;
    }

    public String getReceiverPhone() {
        if (!StringUtils.isEmpty(dataJson)) {
            return JSON.parseObject(dataJson).getString("receiverPhone");
        }
        return null;
    }

    public String getOtherMsg() {
        if (!StringUtils.isEmpty(dataJson)) {
            return JSON.parseObject(dataJson).getString("otherMsg");
        }
        return null;
    }
}
