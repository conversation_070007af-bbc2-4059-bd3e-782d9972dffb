package com.cfpamf.ms.insur.admin.policy.vo.chongho;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/***
 * @version 1.0.0
 * @date 2025/1/9
 * <AUTHOR>
 ***/
@Data
@ApiModel("保单扩展信息")
public class PolicyExtendInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "交单日期", example = "2021-01-01")
    private Date orderTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "投保日期", example = "2021-01-01")
    private Date applicantTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "承保日期", example = "2021-01-01")
    private Date approvedTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退保日期")
    private Date surrenderTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效日期", example = "2019-01-02", required = true)
    private Date enforceTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "失效日期", example = "2019-01-02", required = true)
    private Date insuredPeriodEndTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "中止日期", example = "2019-01-02", required = true)
    private Date discontinueTime;

    @ApiModelProperty(value = "是否需要录入回执", example = "true")
    private boolean needReceipt;

    @ApiModelProperty(value = "是否需要录入回访", example = "true")
    private boolean needRevisit;

    @ApiModelProperty(value = "回执签署日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiptSignTime;

    @ApiModelProperty(value = "回执录入日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiptRecordTime;

    @ApiModelProperty(value = "回访日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date revisitTime;

    @ApiModelProperty(value = "回访录入日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date revisitRecordTime;

    @ApiModelProperty(value = "犹豫期(天)", example = "10")
    private Integer hesitatePeriod;

    @ApiModelProperty(value = "过犹豫期日期", example = "2019-01-02")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date overHesitatePeriod;

    @ApiModelProperty(value = "回访结果 1:成功; 0:失败，2：无需回访", example = "0")
    private Integer revisitResult;

    @ApiModelProperty(value = "回访失败原因", example = "家里没人")
    private String revisitFailReason;

    @ApiModelProperty(value = "复效日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date replyEffectiveTime;

    @ApiModelProperty("承保录入日期")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approvedRecordTime;
}
