package com.cfpamf.ms.insur.admin.job;

import com.alibaba.druid.util.StringUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * S13上线修复线上数据
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
//@JobHandler(value = "fix-data")
public class FixOnlineDataJobHandler{

    @Autowired
    private JdbcTemplate template;

    @XxlJob("fix-data")
    public void execute() throws Exception {
        String param = XxlJobHelper.getJobParam();
        if (param == null) {
            //return ReturnT.SUCCESS;
            return ;
        }
        String bacthNo = param.trim();
        Resource resource = new ClassPathResource("sql/fix_data_" + bacthNo + ".sql");
        BufferedReader br = new BufferedReader(new InputStreamReader(resource.getInputStream()));
        List<String> sqls = new ArrayList<>();
        String str = null;
        while ((str = br.readLine()) != null) {
            if (!StringUtils.isEmpty(str)) {
                sqls.add(str);
            }
        }
        template.setIgnoreWarnings(true);
        sqls.forEach(sql -> {
            try {
                log.info("执行SQL={}", sql);
                //XxlJobLogger.log("执行SQL=" + sql);
                template.execute(sql);
            } catch (Exception e) {
                log.error("", e);
            }
        });
        //return ReturnT.SUCCESS;
    }
}
