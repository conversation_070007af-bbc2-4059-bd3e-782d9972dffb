package com.cfpamf.ms.insur.admin.pojo.vo.renewal;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumRenewalTermIntention;
import com.cfpamf.ms.insur.admin.service.renewalterm.EnumRenewalTermStatus;
import com.cfpamf.ms.insur.admin.service.renewalterm.RenewalTermGraceDays;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 后台续期列表对象
 *
 * <AUTHOR>
 * @date 2022/3/14 17:22
 */
@Data
public class SmRenewalTermVo extends RenewalTermGraceDays {
    /**
     * 续期id
     */
    @ApiModelProperty(value = "续期id")
    private Integer id;
    /**
     * 订单ID
     */
    @ApiModelProperty(value = "订单ID")
    private String orderId;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 被保人 uuid
     */
    @ApiModelProperty("被保人 uuid")
    private String insuredSn;

    /**
     * 原保单号
     */
    @ApiModelProperty(value = "原保单号")
    private String policyNo;

    @ApiModelProperty("投保人姓名")
    private String applicantPersonName;

    @ApiModelProperty("被保人姓名")
    private String insuredPersonName;
    /**
     * 续期保费
     */
    @ApiModelProperty(value = "续期保费")
    private BigDecimal renewalAmount;

    @ApiModelProperty(value = "原始单保费")
    private BigDecimal rawOrderAmount;

    /**
     * 续保期数
     */
    @ApiModelProperty(value = "续保期数")
    private Integer termNum;

    /**
     * 总期数
     */
    @ApiModelProperty(value = "总期数")
    private Integer totalTerm;

    /**
     * 应缴时间
     */
    @ApiModelProperty(value = "应缴时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dueTime;


    @ApiModelProperty(value = "管护客户经理工号")
    private String customerAdminId;
    /**
     * 管护客户经理
     */
    @ApiModelProperty(value = "管护客户经理")
    private String customerAdminName;

    @ApiModelProperty("管护客户经理区域")
    private String customerAdminRegionName;

    @ApiModelProperty("管护客户经理机构")
    private String customerAdminOrgName;
    /**
     * 缴费时间
     */
    @ApiModelProperty(value = "缴费时间")
    private Date paymentTime;

    /**
     * 状态：0 待续期,1 已续期,2 已断保
     */
    @ApiModelProperty(value = "状态：0 待续期,1 已续期,2 已断保")
    private Integer renewalStatus;

    /**
     * 是否分享，0否，1是
     */
    @ApiModelProperty(value = "是否分享，0否，1是")
    private Integer shareFlag;

    @ApiModelProperty(value = "是否自保件 0 否 1 是")
    private String selfInsured;

    @ApiModelProperty(value = "自保件")
    private String selfInsuredName;

    @ApiModelProperty(value = "是否异业客户")
    private String judgeCustomerLoan;

    @ApiModelProperty(value = "投保人证件号")
    private String applicantIdNumber;

    @ApiModelProperty(value = "被保人证件号")
    private String insuredIdNumber;

    @JsonIgnore
    public SmRenewalTermBaseExcelVo convertExcelVo() {

        if (Objects.equals(renewalStatus, EnumRenewalTermStatus.WAIT.getCode())) {
            return new SmWaitRenewalTermExcelVo(this);
        }
        if (Objects.equals(renewalStatus, EnumRenewalTermStatus.SUCCESS.getCode())) {
            return new SmSuccessRenewalTermExcelVo(this);
        }
        if (Objects.equals(renewalStatus, EnumRenewalTermStatus.OVER.getCode())) {
            return new SmOverRenewalTermExcelVo(this);
        }
        return null;
    }

    public BigDecimal getRenewalAmount() {
        if (renewalAmount == null || Objects.equals(renewalAmount, 0)) {
            return rawOrderAmount;
        }
        return renewalAmount;
    }

    @ApiModelProperty("跟进情况 99:未跟进,1:愿意续保,2:不愿意续期, 3:意愿不明,4:联系不上")
    String intention;

    @ApiModelProperty("服务方式")
    String serviceMode;

    public String getIntention() {
        if (EnumRenewalTermIntention.LOSE_CONTACT.getCode().equals(serviceMode)){
            return EnumRenewalTermIntention.LOSE_CONTACT.getDesc();
        }

        if (StringUtils.isEmpty(intention)){
            return EnumRenewalTermIntention.NO_FOLLOW_UP.getDesc();
        }
        return EnumRenewalTermIntention.dict(intention);
    }
}
