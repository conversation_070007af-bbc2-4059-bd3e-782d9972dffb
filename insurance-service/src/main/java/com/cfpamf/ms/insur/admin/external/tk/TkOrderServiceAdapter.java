package com.cfpamf.ms.insur.admin.external.tk;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmCmpySettingMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmCancelRefundMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderOutType;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.event.OrderAppSuccessRetryEvent;
import com.cfpamf.ms.insur.admin.event.OrderAppSuccessSmsEvent;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.CommonTools;
import com.cfpamf.ms.insur.admin.external.common.model.CompanyPolicyInfo;
import com.cfpamf.ms.insur.admin.external.common.model.OrderExtendInfo;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhOrderInfo;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProduct;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.external.tk.api.TkApiService;
import com.cfpamf.ms.insur.admin.external.tk.model.*;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.*;
import com.cfpamf.ms.insur.admin.external.tk.util.URLParser;
import com.cfpamf.ms.insur.admin.pojo.SmCreateOrderSubmitRequestBuilder;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.aicheck.FamilyMemberQuestionnaireResultDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.InsuredInfoReplaceDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.aicheck.AkProductQuestionnaire;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmCancelRefund;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.query.CmpySettingQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigMapper;
import com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfig;
import com.cfpamf.ms.insur.admin.service.aicheck.AkFamilyQuestionnaireService;
import com.cfpamf.ms.insur.admin.service.correct.GroupHelper;
import com.cfpamf.ms.insur.admin.service.order.group.GroupRuleAdaptor;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.IdCardUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.common.enums.EnumMonitor;
import com.cfpamf.ms.insur.common.service.monitor.MonitorHelper;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.constant.SmConstants.ENDTOSEMENT_DECRE;
import static com.cfpamf.ms.insur.admin.constant.SmConstants.ENDTOSEMENT_INCRE;

/**
 * 中华联合保险API接口适配器
 *
 * <AUTHOR>
 */
@Slf4j
@Service("tk")
@Primary
public class TkOrderServiceAdapter extends ZhnxPayOrderServiceAdapter {

    @Autowired
    protected TkApiService apiService;
    @Autowired
    protected TkApiProperties tkApiProperties;

    @Autowired
    protected SmCancelRefundMapper mapper;

    @Autowired
    protected SmProductMapper productMapper;

    @Autowired
    protected RenewalConfigMapper renewalConfigMapper;

    /**
     * 智能核保查询
     */
    @Autowired
    protected AkFamilyQuestionnaireService questionnaireService;

    /**
     * 调用api调用保存订单
     *
     * @param request
     * @return
     */
    @Override
    public OrderSubmitResponse submitChannelOrder(OrderSubmitRequest request) {

        request.setOrderOutType(EnumOrderOutType.NON_SEE_FEE.getCode());

        //如果有只能核保信息
        List<FamilyMemberQuestionnaireResultDTO> dtos = Collections.emptyList();
        if (StringUtils.isNotBlank(request.getQuestionnaireId())) {
            dtos = questionnaireService.listFamilyQuestionnaireRetByQuestionnaireId(
                    Integer.valueOf(request.getQuestionnaireId()), request.getPreOrderId());
        }
        TkProposalResp check = apiService.check(request, dtos);
        OrderSubmitResponse response = new OrderSubmitResponse();
        response.setAppNo(check.getProposalNo());
        response.setNoticeCode(SmConstants.SM_ORDER_API_INVOKE_SUCCESS);
        //防止重复 加上泰康前缀
        response.setOrderId(orderNoGenerator.getNextNo(EnumChannel.TK.getCode()));
        return response;
    }

    @Override
    public void pushEvent(String orderId, OrderQueryResponse.PolicyInfo policyInfo) {
        busEngine.publish(new OrderCommissionChangeEvent(orderId));
        if (Objects.equals(policyInfo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
            //泰康的不缓存保单文件 因为这个地址需要过一段时间才可以用
            busEngine.publish(new OrderAppSuccessSmsEvent(orderId));
            busEngine.publish(new OrderAppSuccessRetryEvent(orderId));
        }
    }

    @Override
    protected CompanyPolicyInfo acceptPolicy(OrderQueryResponse response, SmOrderListVO orderInfo, QueryOrderVO queryOrderVO) {
        log.info("[开始请求泰康出单Api]-OrderId:{}",orderInfo.getFhOrderId());
        CompanyPolicyInfo res = new CompanyPolicyInfo();
        try {
            TkAcceptPolicyResp acceptPolicyResp = apiService.acceptPolicy(orderInfo.getFhOrderId(),
                    orderInfo.getAppNo(), queryOrderVO);
            res.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            res.setPolicyNo(acceptPolicyResp.getPolicyNo());
            res.setPolicyUrl(acceptPolicyResp.getPolicyUrl());
            res.setInsuredDate(new Date());
        } catch (Exception e) {
            log.error("出单失败 {}", orderInfo.getFhProductId(), e);
            res.setAppStatus(SmConstants.POLICY_STATUS_FAIL);
        }
        return res;
    }

    public void refundNotify(Integer refundId) {
        SmCancelRefund cancelRefund = mapper.selectByPrimaryKey(refundId);
        if (Objects.isNull(cancelRefund)) {
            return;
        }
        apiService.refundNotify(cancelRefund);
    }

    public TkRespWrapper policyLose(TkReqWrapper<TkPolicyLose> lose) {
        return null;
    }

    @Override
    public OrderPreAiCheckResp aiCheck(OrderSubmitRequest request, String localProductId) {
        log.info("localProductId={}", localProductId);
        String orderId = orderNoGenerator.getNextNo(EnumChannel.TK.getCode());
        AkProductQuestionnaire productQuestionnaire = super.getAkProductQuestionnaireByProductId(localProductId);

        if (productQuestionnaire == null) {
            throw new BizException("", "该产品未配置智能核保问卷");
        }
        Integer familyQuestionnaireId = super.addAkFamilyQuestionnaire(EnumChannel.TK.getCode(), orderId, productQuestionnaire.getQuestionnaireCode(), request);
        if (familyQuestionnaireId == null) {
            throw new BizException("", "生成智能核保问卷失败");
        }

        OrderPreAiCheckResp aiCheckResp = new OrderPreAiCheckResp();
        aiCheckResp.setOrderId(orderId);
        aiCheckResp.setQuestionnaireId(familyQuestionnaireId.toString());
        String redirectPage = tkApiProperties.getAiCheckQuestionnaireUrl() + "?questionnaireId=" + aiCheckResp.getQuestionnaireId();
        aiCheckResp.setRedirectPage(redirectPage);
        return aiCheckResp;
    }

    public void onlineServiceRes(String userId, String productId, HttpServletResponse resp) throws IOException {

        TkOnlineServiceRes tkOnlineServiceRes = apiService.onlineServiceSign();
        String onlineServiceChannelGateway = tkApiProperties.getOnlineServiceChannelGateway();
        resp.sendRedirect(onlineServiceChannelGateway + "/H5/" + tkApiProperties.getOnlineServiceChannelCode() + "?key=" +
                tkOnlineServiceRes.getKey() + "&visitorId=" + userId + "&productCode=" + productId + "&channel=" + tkApiProperties.getOnlineServiceChannelEntrance());
    }


    @Override
    public List<AICheckQueryResponse> aiCheckQuery(AICheckQueryRequest request) {

        String questionnaireId = request.getQuestionnaireId();
        if (StringUtils.isBlank(questionnaireId)) {
            questionnaireId = super.queryQuestionnaireIdByOrderId(request.getOrderId());
        }

        if (StringUtils.isBlank(questionnaireId)) {
            return Collections.emptyList();
        }

        return super.queryAiCheckRet(questionnaireId, request.getOrderId());
    }
    /***************** begin add by zhangjian 泰康雇主责任险 *******************************/
    /**
     * 泰康雇主责任险新契约参数转换
     * @param empOrderReq
     * @return
     */
    public SmCreateOrderSubmitRequest cvtByEmployerOrderNotify(TkEmpOrderReq empOrderReq){
        String fhOrderId = orderNoGenerator.getNextNo(EnumChannel.TK.getCode());
        SmCreateOrderSubmitRequest submitReq = SmCreateOrderSubmitRequestBuilder.initCallBackOrderSubmitRequest(EnumChannel.TK.getCode(), EnumOrderSubChannel.H5.getCode(),fhOrderId);
        //泰康保单产品信息,目前保单产品列表中只有一个产品，与保司沟通过，只有团险后续可能存在多个产品，后续存在多个产品时再修改，
        TkEmpPolMain polMain =  empOrderReq.getPolMainList().get(0);
        //总保费 = 险种实际保费之和 = 标的物增保保费之和 （已与保司开发人员沟通确认）
        BigDecimal totalAmount = polMain.getRiskKindList().stream().map(TkEmpRiskKind::getActualPremium).reduce(BigDecimal.ZERO,(a, b)->a.add(b));
        BigDecimal insuredAmount = polMain.getRiskKindList().stream().map(TkEmpRiskKind::getInsuredAmount).reduce(BigDecimal.ZERO,(a, b)->a.add(b));

        //订单信
        FhOrderInfo fhOrderInfo = new FhOrderInfo();
        fhOrderInfo.setTotalAmount(totalAmount);
        fhOrderInfo.setInsuredAmount(insuredAmount);
        fhOrderInfo.setSubmitTime(FMT_SUB.format(empOrderReq.getApplyInsureTime()));
        fhOrderInfo.setStartTime(FMT_SUB.format(empOrderReq.getInsuranceStartTime()));
        fhOrderInfo.setEndTime(FMT_SUB.format(empOrderReq.getInsuranceEndTime()));

        fhOrderInfo.setApplyTime(LocalDateUtil.localDateTimeToUdate(empOrderReq.getApplyInsureTime()));
        submitReq.setOrderInfo(fhOrderInfo);

        //保险产品信息
        //Map<String, SmPlanVO> planMap = getSmPlan(groupOrderInfo);
        FhProduct productInfo = new FhProduct();
        if(StringUtils.isNotBlank(empOrderReq.getEmpId()) && !Objects.equals(empOrderReq.getEmpId(),"null")){
            productInfo.setRecommendId(empOrderReq.getEmpId());
        }
        String fhProductId = polMain.getOutProductCode();
        productInfo.setProductId(fhProductId);
        submitReq.setProductInfo(productInfo);
        submitReq.setProductId(fhProductId);

        //订单明细
        submitReq.setOrderItemList(getOrderItemList(submitReq.getFhOrderId(), empOrderReq.getPolicyNo(), fhProductId,"", polMain.getItemMainList(),0));


        //投保人信息
        FhProposer proposer = TkConvert.CNT.cvtPolicyHolder(empOrderReq.getPolicyHolder());

        List<OrderExtendInfo> proposerExtendInfoList = Lists.newArrayListWithCapacity(2);
        proposerExtendInfoList.add(new OrderExtendInfo("nationality", empOrderReq.getPolicyHolder().getHolderType()));
        proposer.setExtendInfoList(proposerExtendInfoList);
        submitReq.setProposerInfo(proposer);

        //被保人
        List<FhInsuredPerson> insuredPersonList = polMain.getItemMainList().stream()
                .map(tkEmpItemMain -> {
                    FhInsuredPerson insuredPerson = TkConvert.CNT.cvtEmployerInsured(tkEmpItemMain);
                    insuredPerson.setPolicyNo(empOrderReq.getPolicyNo());
                    insuredPerson.setBirthday(CommonTools.getBirthday(tkEmpItemMain.getSubjectId()));
                    insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_PROCESS);
                    List<OrderExtendInfo> extendInfos = Lists.newArrayListWithCapacity(2);
                    extendInfos.add(new OrderExtendInfo("businessLevel", tkEmpItemMain.getBusinessLevel()));
                    extendInfos.add(new OrderExtendInfo("subjectTypeCode", tkEmpItemMain.getSubjectTypeCode()));

                    insuredPerson.setExtendInfoList(extendInfos);
                    return insuredPerson;
                }).collect(Collectors.toList());
        submitReq.setInsuredPerson(insuredPersonList);

        submitReq.setQty(1);

        return submitReq;
    }

    private List<SmOrderItem> getOrderItemList(String fhOrderId, String policyNo,String planCode, String endorsementNo, List<TkEmpItemMain> tkEmpItemMainList, int type){
        List<SmOrderItem> batchIncreaseList = new ArrayList<>();
        tkEmpItemMainList.stream().forEach(insured->{

            SmOrderItem incrItem = new SmOrderItem();
            incrItem.setFhOrderId(fhOrderId);
            incrItem.setThPolicyNo(policyNo);
            incrItem.setType(type);
            incrItem.setThEndorsementNo(endorsementNo);
            incrItem.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            incrItem.setIdType(insured.getSubjectIdTypeCode());
            incrItem.setIdNumber(insured.getSubjectId());
            incrItem.setPlanCode(planCode);
            if(type == 0 ){
                incrItem.setPolicyNo(policyNo);
            }else{
                incrItem.setPolicyNo(endorsementNo+ ENDTOSEMENT_INCRE);
            }
            incrItem.setUnitPrice(insured.getInsuranceAddPremium());
            incrItem.setQty(1);
            incrItem.setTotalAmount(insured.getInsuranceAddPremium());
            incrItem.setEndorsementAmount(BigDecimal.ZERO);
            batchIncreaseList.add(incrItem);

        });
        return batchIncreaseList;
    }

    public String genAgentEmployerUrl(String productUrl, String userId) {
        //modify 因为保司给的自定义已经变更了，所有自定义字段也配置到产品配置的h5地址里
        //return productUrl + "?referralCode=" + userId;
        return productUrl+userId;
    }
    /**  s52 雇主责任险批改  **/
    public List<SmCreateOrderSubmitRequest> cvtByEmployerEndorsementNotify(String policyNo, TkEmpEndorOrderReq endorOrderReq, SmBaseOrderVO baseOrder, SmOrderApplicant applicant, SmPlanVO planVO){
        //批增是一个单
        List<SmCreateOrderSubmitRequest> list = new ArrayList<>();

        String endorType = endorOrderReq.getEndorType();
        //+2是因为一个bug导致部分订单下标已经超过count的个数2个位置
        Integer count = groupHelper.nextCorrectSeq(baseOrder.getFhOrderId());

        GroupRuleAdaptor.addSubtractCheck(endorOrderReq);
        if(Objects.equals(endorType,TkConsts.ENDOR_TYPE_INCREASE)){
            list.add(cvtByEmployerBatchIncrease(count,endorOrderReq,baseOrder,applicant,planVO));
        }else if(Objects.equals(endorType,TkConsts.ENDOR_TYPE_DECREASE)){
            list.addAll(cvtByEmployerBatchDecrease(count,endorOrderReq,baseOrder,applicant,planVO));
        }else{
            MonitorHelper.addMonitor(EnumMonitor.TK_EMPLOYER_CORRECT,"泰康雇主险V1不支持的批改类型:{},{}",policyNo,endorType);
            throw new MSBizNormalException("","不支持该类型的批改");
        }
        return list;
    }

    private SmCreateOrderSubmitRequest cvtByEmployerBatchIncrease(int count,TkEmpEndorOrderReq endorOrderReq, SmBaseOrderVO smBaseOrder, SmOrderApplicant applicant, SmPlanVO planVO){
        List<TkEmpEndorStakeHolder> batchIncreList = endorOrderReq.getStakeholderList();
        if(CollectionUtils.isEmpty(batchIncreList)){
            return null;
        }
        String newOrderId = smBaseOrder.getFhOrderId()+"_"+count;
        SmCreateOrderSubmitRequest submitReq =  SmCreateOrderSubmitRequestBuilder.initCallBackOrderSubmitRequest(EnumChannel.TK.getCode(), EnumOrderSubChannel.H5.getCode(),newOrderId);
        List<SmOrderItem> orderItemList = getBatchIncreaseList(newOrderId, endorOrderReq.getPolicyNo(), endorOrderReq.getEndorNo(),planVO.getFhProductId(), batchIncreList);
        submitReq.setOrderItemList(orderItemList);

        //推荐人信息处理
        SmCreateOrderSubmitRequestBuilder.createEndtosementRecommendInfo(submitReq,smBaseOrder);

        //订单信息
        BigDecimal totalAmount = orderItemList.stream().map(SmOrderItem::getTotalAmount).reduce(BigDecimal.ZERO,(a, b)->a.add(b));
        SmCreateOrderSubmitRequestBuilder.createOrderInfo(submitReq,endorOrderReq.getEndorNo(),totalAmount,endorOrderReq.getEndorDate(),LocalDateUtil.dateToLocaldatetime(smBaseOrder.getEndTime()),smBaseOrder);

        //保险产品信息
        SmCreateOrderSubmitRequestBuilder.createProductInfo(submitReq,smBaseOrder.getCustomerAdminId(),smBaseOrder.getCustomerAdminId(),planVO);

        //投保人信息
        SmCreateOrderSubmitRequestBuilder.createEndtosementFhPropose(submitReq,applicant);
        //被保人
        List<FhInsuredPerson> insuredPersonList = batchIncreList.stream()
                .map(groupInsuredInfo -> {
                    FhInsuredPerson insuredPerson = new FhInsuredPerson();
                    insuredPerson.setPersonName(groupInsuredInfo.getName());
                    insuredPerson.setIdType(groupInsuredInfo.getCredentialType());
                    insuredPerson.setIdNumber(groupInsuredInfo.getCredentialNo());
                    insuredPerson.setBirthday(CommonTools.getBirthday(groupInsuredInfo.getCredentialNo()));
                    insuredPerson.setPolicyNo(endorOrderReq.getEndorNo()+ENDTOSEMENT_INCRE);
                    insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
                    insuredPerson.setOccupationGroup(groupInsuredInfo.getOccupationLevel());
                    insuredPerson.setOccupationCode(groupInsuredInfo.getProfessionCode());

                    return insuredPerson;
                }).collect(Collectors.toList());
        submitReq.setInsuredPerson(insuredPersonList);
        submitReq.setQty(1);

        return submitReq;
    }




    private List<SmOrderItem> getBatchIncreaseList(String fhOrderId, String policyNo,String endorsementNo,String planCode,  List<TkEmpEndorStakeHolder> endorStakeHolderList){
        List<SmOrderItem> batchIncreaseList = new ArrayList<>();
        endorStakeHolderList.stream().forEach(insured->{
            SmOrderItem incrItem = new SmOrderItem();
            incrItem.setFhOrderId(fhOrderId);
            incrItem.setThPolicyNo(policyNo);
            incrItem.setType(1);
            incrItem.setThEndorsementNo(endorsementNo);
            incrItem.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            incrItem.setIdType(insured.getCredentialType());
            incrItem.setIdNumber(insured.getCredentialNo());
            incrItem.setPlanCode(planCode);
            incrItem.setPolicyNo(endorsementNo+ ENDTOSEMENT_INCRE);
            BigDecimal amt = new BigDecimal(insured.getChangeGrossPremium());
            incrItem.setUnitPrice(amt);
            incrItem.setQty(1);
            incrItem.setTotalAmount(amt);
            incrItem.setEndorsementAmount(BigDecimal.ZERO);
            batchIncreaseList.add(incrItem);

        });
        return batchIncreaseList;
    }


    private List<SmCreateOrderSubmitRequest> cvtByEmployerBatchDecrease(int count,TkEmpEndorOrderReq endorOrderReq, SmBaseOrderVO smBaseOrder, SmOrderApplicant applicant, SmPlanVO planVO){
        //批单中的身份证列表
        List<SmCreateOrderSubmitRequest> list = new ArrayList<>();
        List<TkEmpEndorStakeHolder> batchDecreList = endorOrderReq.getStakeholderList();
        List<String> idNumberList = batchDecreList.stream().map(TkEmpEndorStakeHolder::getCredentialNo).collect(Collectors.toList());
        //批减人员的投保时的订单明细,需要按id降序排列，存在一个人多次投保退保的可能。
        List<SmOrderItem> orderItemList = super.listOriginalPolicyByOrderId(smBaseOrder.getFhOrderId(),idNumberList);
        //订单明细对应的订单号（订单明细可能分布在不同订单中）
        List<String> fhOrderIds = orderItemList.stream().map(SmOrderItem::getFhOrderId).collect(Collectors.toList());
        //订单明细对应的订单信息
        List<SmOrder> orderList = null;
        if(CollectionUtils.isEmpty(orderItemList)){
            orderList = Collections.emptyList();
        }else {
            orderList = orderMapper.listSmOrderByOrderIds(fhOrderIds);
        }

        int index = count;
        for(int i=0 ;i<batchDecreList.size();i++) {
            TkEmpEndorStakeHolder insured = batchDecreList.get(i);
            //取最新的一条投保记录
            Optional<SmOrderItem> opt = orderItemList.stream().filter(item -> Objects.equals(insured.getCredentialNo(), item.getIdNumber())).findFirst();
            if(opt.isPresent()){
                SmCreateOrderSubmitRequest submitReq = SmCreateOrderSubmitRequestBuilder.initCallBackOrderSubmitRequest(EnumChannel.TK.getCode(), EnumOrderSubChannel.H5.getCode(),smBaseOrder.getFhOrderId()+"_"+index);;
                //批减人对应保单信息
                SmOrder order = orderList.stream().filter(smOrder -> Objects.equals(opt.get().getFhOrderId(),smOrder.getFhOrderId())).findFirst().get();
                SmOrderItem o = opt.get();
                SmOrderItem decrItem = toSmOrderItem(submitReq.getFhOrderId(),insured,SmConstants.POLICY_STATUS_CANCEL_SUCCESS,endorOrderReq.getPolicyNo(),o.getTotalAmount(),endorOrderReq.getEndorNo(),index);
                List<SmOrderItem> itemList = new ArrayList<>();
                itemList.add(decrItem);
                submitReq.setOrderItemList(itemList);

                //推荐人信息处理
                SmCreateOrderSubmitRequestBuilder.createEndtosementRecommendInfo(submitReq,smBaseOrder);
                //订单信息
                SmCreateOrderSubmitRequestBuilder.createOrderInfo(submitReq,endorOrderReq.getEndorNo(),decrItem.getTotalAmount(),order.getStartTime(),endorOrderReq.getUnderWriteEndDate(),smBaseOrder);
                submitReq.setCommissionId(order.getCommissionId());
                //产品信息
                SmCreateOrderSubmitRequestBuilder.createProductInfo(submitReq,order.getRecommendId(),order.getCustomerAdminId(),planVO);

                //投保人信息
                SmCreateOrderSubmitRequestBuilder.createEndtosementFhPropose(submitReq,applicant);

                //被保人信息
                setDecreaseInsuredInfo(submitReq,insured,endorOrderReq.getEndorNo(),endorOrderReq.getUnderWriteEndDate(),index);

                list.add(submitReq);
                index = index+1;

            }else{
                log.info("众安团险保单号为{}的批单信息，批单号为{}中用户{}证件号为{}的在表sm_order_item中未找到原始信息",endorOrderReq.getPolicyNo(),endorOrderReq.getEndorNo(),insured.getName(),insured.getCredentialNo());
            }
        }
        return list;
    }

    private void setDecreaseInsuredInfo(SmCreateOrderSubmitRequest submitReq, TkEmpEndorStakeHolder stakeHolder, String endorsementNo, LocalDateTime surrenderTime,Integer index){
        FhInsuredPerson insuredPerson = new FhInsuredPerson();
        insuredPerson.setPersonName(stakeHolder.getName());
        insuredPerson.setIdType(stakeHolder.getCredentialType());
        insuredPerson.setIdNumber(stakeHolder.getCredentialNo());
        insuredPerson.setBirthday(CommonTools.getBirthday(stakeHolder.getCredentialNo()));
        insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        insuredPerson.setSurrenderTime(LocalDateUtil.localDateTimeToUdate(surrenderTime));
        insuredPerson.setPolicyNo(endorsementNo+ENDTOSEMENT_DECRE+index);
        insuredPerson.setOccupationGroup(stakeHolder.getOccupationLevel());
        insuredPerson.setOccupationCode(stakeHolder.getProfessionCode());
        List<FhInsuredPerson> insuredPersonList = new ArrayList<>();
        insuredPersonList.add(insuredPerson);
        submitReq.setInsuredPerson(insuredPersonList);
    }

    private SmOrderItem toSmOrderItem(String fhOrderId,TkEmpEndorStakeHolder stakeHolder,String appStatus,String policyNo,BigDecimal originalTotalAmount,String endorsementNo,int index){
        SmOrderItem item = new SmOrderItem();
        item.setFhOrderId(fhOrderId);
        item.setThPolicyNo(policyNo);
        item.setThEndorsementNo(endorsementNo);
        //decrItem.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        item.setAppStatus(appStatus);
        item.setType(1);
        item.setIdType(stakeHolder.getCredentialType());
        item.setIdNumber(stakeHolder.getCredentialNo());
        //item.setPlanCode(insured.getPlanCode());
        item.setPolicyNo(endorsementNo+ENDTOSEMENT_DECRE+index);
        //批减时接口,金额为负数
        BigDecimal amt = new BigDecimal(stakeHolder.getChangeGrossPremium());
        amt = amt.multiply(new BigDecimal("-1"));
        item.setUnitPrice(amt);
        item.setQty(1);
        item.setTotalAmount(amt);
        BigDecimal balance = originalTotalAmount.subtract(amt);
        if(balance.compareTo(BigDecimal.ZERO)<0){
            balance = BigDecimal.ZERO;
        }
        item.setEndorsementAmount(balance);
        return item;
    }



    /***************** end add by zhangjian 泰康雇主责任险 *******************************/

    /****begin  雇主责任险V2 接口***/

    public SmCreateOrderSubmitRequest cvtByV2EmployerOrderNotify(TkEmpV2Order order){
        String fhOrderId = orderNoGenerator.getNextNo(EnumChannel.TK.getCode());
        SmCreateOrderSubmitRequest submitReq = SmCreateOrderSubmitRequestBuilder.initCallBackOrderSubmitRequest(EnumChannel.TK.getCode(), EnumOrderSubChannel.H5.getCode(),fhOrderId);
        //泰康保单产品信息,目前保单产品列表中只有一个产品，与保司沟通过，只有团险后续可能存在多个产品，后续存在多个产品时再修改，
        TkEmpV2Policy policy =  order.getPolicy();
        String policyNo = policy.getPolicyNo();
        submitReq.setPolicyNo(policyNo);
        List<TkEmpV2Employee> employeeList = order.getEmployeeDtoList();
        TkEmpV2Applicant applicant = order.getApplicant();

        //订单信息
        FhOrderInfo fhOrderInfo = new FhOrderInfo();
        BigDecimal totalAmount = BigDecimal.ZERO;
        if(policy.getPremium()!=null){
            totalAmount = new BigDecimal(policy.getPremium());
        }
        fhOrderInfo.setTotalAmount(totalAmount);
        if(policy.getAmount()!=null) {
            fhOrderInfo.setInsuredAmount(new BigDecimal(policy.getAmount()));
        }
        fhOrderInfo.setSubmitTime(policy.getApplyDate());

        Date applyTime = LocalDateUtil.parseTime(policy.getApplyDate());
        fhOrderInfo.setApplyTime(applyTime);
        fhOrderInfo.setStartTime(policy.getStartDate());
        fhOrderInfo.setEndTime(policy.getEndDate());
        fhOrderInfo.setAppNo(policy.getProposalNo());
        submitReq.setOrderInfo(fhOrderInfo);

        //Map<String, SmPlanVO> planMap = getSmPlan(groupOrderInfo);
        //保险产品信息
        FhProduct productInfo = new FhProduct();
        if(StringUtils.isNotBlank(policy.getAgentNo()) && !Objects.equals(policy.getAgentNo(),"null")){
            productInfo.setRecommendId(policy.getAgentNo());
        }
        String fhProductId = policy.getProductCode();
        productInfo.setProductId(fhProductId);
        submitReq.setProductInfo(productInfo);
        submitReq.setProductId(fhProductId);
        //订单明细
        submitReq.setOrderItemList(createV2IncreaseOrderItemList(submitReq,submitReq.getFhOrderId(), policy.getPolicyNo(),"", fhProductId, employeeList,0));

        //投保人信息
        if(Objects.nonNull(applicant)) {
            submitReq.setProposerInfo(TkConvert.CNT.cvtV2EmployerApplicant(applicant));
        }else{
            submitReq.setProposerInfo(TkConvert.CNT.cvtV2EmployerCorporationApplicant(order.getCorporationApplicant()));
        }

        //被保人
        List<FhInsuredPerson> insuredPersonList = createV2InsuredPerson(policyNo,policy.getPolicyURL(),employeeList);
        submitReq.setInsuredPerson(insuredPersonList);
        submitReq.setQty(1);

        return submitReq;
    }

    public List<SmCreateOrderSubmitRequest> cvtByV2EmployerEndorsementNotify(String policyNo,TkEmpV2CancelDetail cancelDetail,SmBaseOrderVO baseOrder,SmOrderApplicant applicant,SmPlanVO planVO){
        //批增是一个单
        List<SmCreateOrderSubmitRequest> list = new ArrayList<>();

        String endorType = cancelDetail.getEndorType();
        //+2是因为一个bug导致部分订单下标已经超过count的个数2个位置
        Integer count = groupHelper.nextCorrectSeq(baseOrder.getFhOrderId());
        if(Objects.equals(endorType,TkConsts.V2_ENDOR_TYPE_INCREASE)){
            list.add(cvtByV2EmployerBatchIncrease(count,cancelDetail,baseOrder,applicant,planVO));
        }else if(Objects.equals(endorType,TkConsts.V2_ENDOR_TYPE_DECREASE) || Objects.equals(endorType,TkConsts.V2_ENDOR_TYPE_CANCEL) ){
            list.addAll(cvtV2ByEmployerBatchDecrease(count,cancelDetail,baseOrder,applicant,planVO));
        }else{
            throw new MSBizNormalException("","不支持该类型的批改");
        }
        return list;
    }

    /**
     * TODO 泰康雇主责任险-替换逻辑处理
     * @param policyNo
     * @param cancelDetail
     * @param baseOrder
     * @return
     */
    public List<InsuredInfoReplaceDTO> cvtByV2EmployerReplaceNotify(String policyNo, TkEmpV2CancelDetail cancelDetail, SmBaseOrderVO baseOrder){
        //批增是一个单
        List<InsuredInfoReplaceDTO> list = new ArrayList<>();
        List<TkEmpV2Employee> employeeList = cancelDetail.getEmployeeList();
        if(CollectionUtils.isEmpty(employeeList)){
            return Collections.emptyList();
        }
        List<String> itemCodeList = employeeList.stream().map(TkEmpV2Employee::getItemCode).collect(Collectors.toList());
        List<SmOrderItem> orderItemList = super.listOriginalPolicyByBranchId(baseOrder.getFhOrderId(),itemCodeList);
        String errorMsg = "";
        for(TkEmpV2Employee emp :employeeList){
            Optional<SmOrderItem> opt = orderItemList.stream().filter(i->Objects.equals(emp.getItemCode(),i.getActiveBranchId())).findFirst();
            if(opt.isPresent()){
                SmOrderItem item = opt.get();
                if(Objects.equals(item.getAppStatus(),SmConstants.POLICY_STATUS_SUCCESS)){
                    //身份证好没有变，则是更新雇员信息，不做处理
                    if(Objects.equals(item.getIdNumber(),emp.getIdentifyNumber())){
                        continue;
                    }
                    InsuredInfoReplaceDTO replaceDTO = new InsuredInfoReplaceDTO();
                    replaceDTO.setFhOrderId(item.getFhOrderId());
                    replaceDTO.setPolicyNo(item.getPolicyNo());
                    replaceDTO.setOldIdNumber(item.getIdNumber());
                    //replaceDTO.setOldInsuredName(item.get);
                    replaceDTO.setBirthday(CommonTools.getBirthday(emp.getIdentifyNumber()));
                    replaceDTO.setPersonGender(emp.getSex());
                    replaceDTO.setNewInsuredName(emp.getEmployeeName());
                    replaceDTO.setNewIdNumber(emp.getIdentifyNumber());
                    list.add(replaceDTO);

                }else{
                    errorMsg = String.format("保单%s的替换批单%s中雇员编号%s在系统中是退保状态",policyNo,cancelDetail.getEndorNo(),emp.getItemCode());
                    log.warn("保单{}的替换批单{}中雇员编号{}在系统中是退保状态",policyNo,cancelDetail.getEndorNo(),emp.getItemCode());
                    throw new MSBizNormalException("",errorMsg);
                }
            }else{
                errorMsg = String.format("保单%s的替换批单%s中雇员编号%s在系统中不存在",policyNo,cancelDetail.getEndorNo(),emp.getItemCode());
                log.warn("保单{}的替换批单{}中雇员编号{}在系统中不存在",policyNo,cancelDetail.getEndorNo(),emp.getItemCode());
                throw new MSBizNormalException("",errorMsg);

            }
        }

        return list;
    }



    private List<FhInsuredPerson> createV2InsuredPerson(String policyNo,String policyUrl,List<TkEmpV2Employee> employeeList){
        if(CollectionUtils.isEmpty(employeeList)){
            return Collections.emptyList();
        }
        List<FhInsuredPerson> insuredPersonList  = Lists.newArrayListWithCapacity(employeeList.size());
        employeeList.forEach(employee -> {
            FhInsuredPerson insuredPerson = TkConvert.CNT.cvtV2EmployerInsured(employee);

            insuredPerson.setBirthday(CommonTools.getBirthday(employee.getIdentifyNumber()));
            insuredPerson.setPolicyNo(policyNo);
            insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            insuredPerson.setDownloadURL(policyUrl);

            insuredPersonList.add(insuredPerson);
        });
        return insuredPersonList;
    }
    private List<SmOrderItem> createV2IncreaseOrderItemList(SmCreateOrderSubmitRequest submitReq,String fhOrderId,String policyNo,String endorsementNo,String planCode,List<TkEmpV2Employee> employeeList,Integer type){


        List<SmOrderItem> batchIncreaseList = new ArrayList<>();
        employeeList.stream().forEach(employee->{
                SmOrderItem incrItem = createV2IncreaseOrderItem(fhOrderId,type,policyNo,endorsementNo,employee,planCode);
                batchIncreaseList.add(incrItem);

            });
        BigDecimal total = batchIncreaseList.stream().map(SmOrderItem::getTotalAmount).reduce(BigDecimal.ZERO,(a, b)->a.add(b));
        if(total.compareTo(submitReq.getOrderInfo().getTotalAmount())!=0){
            throw new MSBizNormalException("","总保费与雇员明细记录保费之和不相等");
        }

        return batchIncreaseList;

    }


    private SmOrderItem createV2IncreaseOrderItem(String fhOrderId,Integer type,String policyNo,String endorNo,TkEmpV2Employee employee,String planCode){
        SmOrderItem incrItem = new SmOrderItem();
        incrItem.setFhOrderId(fhOrderId);
        incrItem.setThPolicyNo(policyNo);
        incrItem.setType(type);
        incrItem.setThEndorsementNo(endorNo);
        incrItem.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
        incrItem.setIdType(employee.getIdentifyType());
        incrItem.setIdNumber(employee.getIdentifyNumber());
        incrItem.setPlanCode(planCode);
        if(type == 0 ){
            incrItem.setPolicyNo(policyNo);
        }else{
            incrItem.setPolicyNo(endorNo+ ENDTOSEMENT_INCRE);
        }
        incrItem.setQty(1);
        BigDecimal amount = new BigDecimal(employee.getPremium());
        incrItem.setUnitPrice(amount);
        incrItem.setTotalAmount(amount);
        incrItem.setInsuredAmount(new BigDecimal(employee.getAmount()));
        incrItem.setEndorsementAmount(BigDecimal.ZERO);
        incrItem.setActiveBranchId(employee.getItemCode());
        incrItem.setBranchId(employee.getItemCode());
        return incrItem;
    }

    private SmCreateOrderSubmitRequest cvtByV2EmployerBatchIncrease(int count ,TkEmpV2CancelDetail cancelDetail,SmBaseOrderVO smBaseOrder,SmOrderApplicant applicant,SmPlanVO planVO){


        List<TkEmpV2Employee> employeeList = cancelDetail.getEmployeeList();
        if(CollectionUtils.isEmpty(employeeList)){
            return null;
        }
        String newOrderId = smBaseOrder.getFhOrderId()+"_"+count;
        SmCreateOrderSubmitRequest submitReq =  SmCreateOrderSubmitRequestBuilder.initCallBackOrderSubmitRequest(EnumChannel.TK.getCode(), EnumOrderSubChannel.H5.getCode(),newOrderId);

        //推荐人信息处理
        SmCreateOrderSubmitRequestBuilder.createEndtosementRecommendInfo(submitReq,smBaseOrder);

        //订单信息
        BigDecimal totalAmount = new BigDecimal(cancelDetail.getDrawPremium());//orderItemList.stream().map(SmOrderItem::getTotalAmount).reduce(BigDecimal.ZERO,(a, b)->a.add(b));
        SmCreateOrderSubmitRequestBuilder.createOrderInfo(submitReq,cancelDetail.getEndorNo(),totalAmount, LocalDateUtil.dateToLocaldatetime(new Date(cancelDetail.getValidDate())),LocalDateUtil.dateToLocaldatetime(smBaseOrder.getEndTime()),smBaseOrder);

        //item表明细
        submitReq.setOrderItemList(createV2IncreaseOrderItemList(submitReq,submitReq.getFhOrderId(), cancelDetail.getPolicyNo(),cancelDetail.getEndorNo(), planVO.getFhProductId(), employeeList,1));

        //保险产品信息
        SmCreateOrderSubmitRequestBuilder.createProductInfo(submitReq,smBaseOrder.getCustomerAdminId(),smBaseOrder.getCustomerAdminId(),planVO);

        //投保人信息
        SmCreateOrderSubmitRequestBuilder.createEndtosementFhPropose(submitReq,applicant);
        //被保人
        List<FhInsuredPerson> insuredPersonList = createV2InsuredPerson(cancelDetail.getEndorNo()+ENDTOSEMENT_INCRE,"",employeeList);
        submitReq.setInsuredPerson(insuredPersonList);
        submitReq.setQty(1);
        return submitReq;
    }

    private List<SmCreateOrderSubmitRequest> cvtV2ByEmployerBatchDecrease(int count ,TkEmpV2CancelDetail cancelDetail,SmBaseOrderVO smBaseOrder,SmOrderApplicant applicant,SmPlanVO planVO){


        //批单中的身份证列表
        List<SmCreateOrderSubmitRequest> list = new ArrayList<>();
        List<TkEmpV2Employee> employeeList = cancelDetail.getEmployeeList();
        List<String> idNumberList = employeeList.stream().map(TkEmpV2Employee::getIdentifyNumber).collect(Collectors.toList());
        //批减人员的投保时的订单明细,需要按id降序排列，存在一个人多次投保退保的可能。
        List<SmOrderItem> orderItemList = super.listOriginalPolicyByOrderId(smBaseOrder.getFhOrderId(),idNumberList);
        //订单明细对应的订单号（订单明细可能分布在不同订单中）
        List<String> fhOrderIds = orderItemList.stream().map(SmOrderItem::getFhOrderId).collect(Collectors.toList());
        //订单明细对应的订单信息
        List<SmOrder> orderList = null;
        if(CollectionUtils.isEmpty(orderItemList)){
            orderList = Collections.emptyList();
        }else {
            orderList = orderMapper.listSmOrderByOrderIds(fhOrderIds);
        }

        int index = count;
        for(int i=0 ;i<employeeList.size();i++) {
            TkEmpV2Employee employee = employeeList.get(i);
            //取最新的一条投保记录
            Optional<SmOrderItem> opt = orderItemList.stream().filter(item -> Objects.equals(employee.getIdentifyNumber(), item.getIdNumber())).findFirst();
            if(opt.isPresent()){
                SmCreateOrderSubmitRequest submitReq = SmCreateOrderSubmitRequestBuilder.initCallBackOrderSubmitRequest(EnumChannel.TK.getCode(), EnumOrderSubChannel.H5.getCode(),smBaseOrder.getFhOrderId()+"_"+index);;
                LocalDateTime valideTime= LocalDateUtil.dateToLocaldatetime(new Date(cancelDetail.getValidDate()));
                //批减人对应保单信息
                SmOrder order = orderList.stream().filter(smOrder -> Objects.equals(opt.get().getFhOrderId(),smOrder.getFhOrderId())).findFirst().get();
                SmOrderItem o = opt.get();
                SmOrderItem decrItem = toDecreaseOrderItem(submitReq.getFhOrderId(),employee,SmConstants.POLICY_STATUS_CANCEL_SUCCESS,cancelDetail.getPolicyNo(),o.getTotalAmount(),cancelDetail.getEndorNo(),planVO.getFhProductId(),o.getBranchId(),o.getActiveBranchId(),index);
                List<SmOrderItem> itemList = new ArrayList<>();
                itemList.add(decrItem);
                submitReq.setOrderItemList(itemList);

                //推荐人信息处理
                SmCreateOrderSubmitRequestBuilder.createEndtosementRecommendInfo(submitReq,smBaseOrder);
                //订单信息
                SmCreateOrderSubmitRequestBuilder.createOrderInfo(submitReq,cancelDetail.getEndorNo(),decrItem.getTotalAmount(),order.getStartTime(),valideTime,smBaseOrder);
                submitReq.setCommissionId(order.getCommissionId());
                //产品信息
                SmCreateOrderSubmitRequestBuilder.createProductInfo(submitReq,order.getRecommendId(),order.getCustomerAdminId(),planVO);

                //投保人信息
                SmCreateOrderSubmitRequestBuilder.createEndtosementFhPropose(submitReq,applicant);

                //被保人信息
                setV2DecreaseInsuredInfo(submitReq,employee,cancelDetail.getEndorNo(),valideTime,index);

                list.add(submitReq);
                index = index+1;

            }else{
                log.info("众安团险保单号为{}的批单信息，批单号为{}中用户{}证件号为{}的在表sm_order_item中未找到原始信息",cancelDetail.getPolicyNo(),cancelDetail.getEndorNo(),employee.getEmployeeName(),employee.getIdentifyNumber());
            }
        }
        return list;
    }

    private SmOrderItem toDecreaseOrderItem(String fhOrderId,TkEmpV2Employee employee,String appStatus,String policyNo,BigDecimal originalTotalAmount,String endorsementNo,String planCode,String branchId,String activeBranchId,int index){
        SmOrderItem item = new SmOrderItem();
        item.setFhOrderId(fhOrderId);
        item.setThPolicyNo(policyNo);
        item.setThEndorsementNo(endorsementNo);
        //decrItem.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        item.setAppStatus(appStatus);
        item.setType(1);
        item.setIdType(employee.getIdentifyType());
        item.setIdNumber(employee.getIdentifyNumber());
        item.setPlanCode(planCode);
        item.setPolicyNo(endorsementNo+ENDTOSEMENT_DECRE+index);
        //批减时接口,金额为负数
        BigDecimal amt = new BigDecimal(employee.getPremium());
        amt = amt.multiply(new BigDecimal("-1"));
        item.setUnitPrice(amt);
        item.setQty(1);
        item.setTotalAmount(amt);
        BigDecimal balance = originalTotalAmount.subtract(amt);
        if(balance.compareTo(BigDecimal.ZERO)<0){
            balance = BigDecimal.ZERO;
        }
        item.setEndorsementAmount(balance);
        item.setInsuredAmount(BigDecimal.ZERO);
        item.setBranchId(branchId);
        item.setActiveBranchId(activeBranchId);

        return item;
    }

    private void setV2DecreaseInsuredInfo(SmCreateOrderSubmitRequest submitReq, TkEmpV2Employee employee, String endorsementNo, LocalDateTime surrenderTime,Integer index){
        FhInsuredPerson insuredPerson = TkConvert.CNT.cvtV2EmployerInsured(employee);
        insuredPerson.setBirthday(CommonTools.getBirthday(employee.getIdentifyNumber()));
        insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        insuredPerson.setSurrenderTime(LocalDateUtil.localDateTimeToUdate(surrenderTime));
        insuredPerson.setPolicyNo(endorsementNo+ENDTOSEMENT_DECRE+index);
        List<FhInsuredPerson> insuredPersonList = new ArrayList<>();
        insuredPersonList.add(insuredPerson);
        submitReq.setInsuredPerson(insuredPersonList);
    }

    /**
     * 泰康百万医疗续保产品出单信息回调解密方法
     * @param content
     * @param key
     * @return
     */
    public String h5CallBackDecrypt(String content, String key) throws Exception {
        String ret = content;
        try {
            if (content == null || content.length() < 1) {
                return null;
            }
            byte[] byteRresult = new byte[content.length() / 2];
            for (int i = 0; i < content.length() / 2; i++) {
                int high = Integer.parseInt(content.substring(i * 2,
                        i * 2 + 1), 16);
                int low = Integer.parseInt(content.substring(i * 2 +
                        1, i * 2 + 2), 16);
                byteRresult[i] = (byte) (high * 16 + low);
            }
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(key.getBytes());
            kgen.init(128, random);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec secretKeySpec = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] result = cipher.doFinal(byteRresult);
            ret = new String(result);
        } catch (Exception e) {
            log.warn("泰康百万医疗续保回调解密 error", e);
            throw e;
        }
        if(StringUtils.isBlank(ret)){
            log.warn("泰康百万医疗续保回调解密后字符串为空 ret= [{}]",ret);
            throw new MSBizNormalException(ExcptEnum.DECRYPT_ERROR_100000.getCode(),ExcptEnum.DECRYPT_ERROR_100000.getMsg());
        }
        return ret;
    }

    public SmCreateOrderSubmitRequest cvtRenewalOrder(TkMedicalPolicyCallBackReq policyInfo,SmBaseOrderVO originalBaseOrderVO,SmPlanVO planVO) {
        String orderId = orderNoGenerator.getNextNo(EnumChannel.TK_PAY.getCode());
        SmCreateOrderSubmitRequest submitReq = initRenewalOrderSubmitRequest(orderId, policyInfo.getProposalNo());
        //订单信息
        FhOrderInfo fhOrderInfo = new FhOrderInfo();

        fhOrderInfo.setTotalAmount(new BigDecimal(policyInfo.getPremium()).divide(BigDecimal.valueOf(100),2, RoundingMode.DOWN));
        fhOrderInfo.setSubmitTime(DateUtil.format((LocalDateUtil.stringToUdate(policyInfo.getIssueTime())), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setStartTime(DateUtil.format((LocalDateUtil.stringToUdate(policyInfo.getEffectiveTime())), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setEndTime(DateUtil.format((LocalDateUtil.stringToUdate(policyInfo.getExpiredTime())), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setUnderWritingAge("");
        //fhOrderInfo.setValidPeriod(riskInfo.getInsureYears()+riskInfo.getInsureYearsIntv());
        List<TkRisk> tkRiskList = policyInfo.getRiskList();
        if(!CollectionUtils.isEmpty(tkRiskList)){
            BigDecimal sumAmount = BigDecimal.ZERO;
            for(TkRisk tkRisk : tkRiskList){
                sumAmount = sumAmount.add(new BigDecimal(tkRisk.getAmount()));
            }
            fhOrderInfo.setInsuredAmount(sumAmount.divide(BigDecimal.valueOf(100),2, RoundingMode.DOWN));
        }
        submitReq.setOrderInfo(fhOrderInfo);

        TkMedicalPolicyCallBackParameter tkMedicalPolicyCallBackParameter = policyInfo.getParameterMap();

        //保险产品信息
        FhProduct productInfo = new FhProduct();
        String productId = tkMedicalPolicyCallBackParameter.getRubikCode();
        submitReq.setProductId(productId);
        productInfo.setRecommendId(originalBaseOrderVO.getCustomerAdminId());

        productInfo.setProductId(productId);
        submitReq.setQty(1);
        submitReq.setProductInfo(productInfo);


        //投保人信息
        FhProposer fhProposer = TkConvert.CNT.cvtV2TkMedicalPolicyCallBackReqApplicant(policyInfo);
        fhProposer.setPersonGender(tkPersonGender(fhProposer.getIdNumber()));
        submitReq.setProposerInfo(fhProposer);

        //被保险人信息
        submitReq.setInsuredPerson(createInsuredPersonList(policyInfo, policyInfo.getPolicyNo(), policyInfo.getPolicyUrl(), originalBaseOrderVO.getPolicyNo()));
        submitReq.getInsuredPerson().stream().forEach(x-> {
            x.setIsSecurity(tkTransferSocialSecurityType(x.getIsSecurity()));
            x.setPersonGender(tkPersonGender(x.getIdNumber()));
        });


        return submitReq;
    }

    /**
     * 初始化
     *
     * @param fhOrderId 订单号
     * @param appNo     保司投保单号
     * @return
     */
    private SmCreateOrderSubmitRequest initRenewalOrderSubmitRequest(String fhOrderId, String appNo) {
        SmCreateOrderSubmitRequest submitReq = new SmCreateOrderSubmitRequest();

        submitReq.setChannel(EnumChannel.TK_PAY.getCode());
        submitReq.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());
        submitReq.setFhOrderId(fhOrderId);
        submitReq.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        submitReq.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        submitReq.setAppNo(appNo);
        submitReq.setNoticeCode("");
        submitReq.setSubChannel(EnumOrderSubChannel.H5.getCode());
        submitReq.setNoticeMsg("");
        return submitReq;
    }

    /**
     * 根据保单号获取已支付的原保单记录
     * @param originalPolicyNo
     * @return
     */
    public SmBaseOrderVO getSmBaseOrderVO(String originalPolicyNo) {
        SmBaseOrderVO originalBaseOrderVO = orderMapper.getByPolicyNoStatus(originalPolicyNo, null);
        if (Objects.nonNull(originalBaseOrderVO) && Objects.equals(SmConstants.ORDER_STATUS_PAYED, originalBaseOrderVO.getPayStatus())) {
            return originalBaseOrderVO;
        }
        return null;
    }

    /**
     * 被保险人信息
     *
     * @param policyInfo
     * @param policyNo
     * @return
     */
    private List<FhInsuredPerson> createInsuredPersonList(TkMedicalPolicyCallBackReq policyInfo, String policyNo, String policyUrl, String oldPolicyNo) {
        FhInsuredPerson insuredPerson = TkConvert.CNT.cvtTkMedicalPolicyCallBackReqInsured(policyInfo);
        insuredPerson.setPolicyNo(policyNo);
        insuredPerson.setOldPolicyNo(oldPolicyNo);
        insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_PROCESS);
        insuredPerson.setDownloadURL(policyUrl);
        List<FhInsuredPerson> insuredPersonList = Lists.newArrayList();
        insuredPersonList.add(insuredPerson);
        return insuredPersonList;
    }

    /**
     * 泰康H5续保,性别只能从身份证中解析,
     */
    private String tkPersonGender(String idCard){
        String sex = IdCardUtils.getSex(idCard);
        if(IdCardUtils.SEX_FEMALE.equals(sex)){
            return TkConsts.TK_H5_MEDICAL_SEX_FEMALE;
        }else if(IdCardUtils.SEX_MALE.equals(sex)){
            return TkConsts.TK_H5_MEDICAL_SEX_MALE;
        }else{
            return null;
        }
    }

    /**
     * 由于泰康H5续保的有无社保与API对接的有无社保不一致需要转换
     * @param socialSecurityType
     * @return
     */
    private String tkTransferSocialSecurityType(String socialSecurityType){
        if(TkConsts.TK_H5_MEDICAL_HAS_SOCIALSECURITYTYPE.equals(socialSecurityType)){
            return TkConsts.TK_API_MEDICAL_HAS_SOCIALSECURITYTYPE;
        }else if(TkConsts.TK_H5_MEDICAL_NO_SOCIALSECURITYTYPE.equals(socialSecurityType)){
            return TkConsts.TK_API_MEDICAL_NO_SOCIALSECURITYTYPE;
        }
        return null;
    }

}
