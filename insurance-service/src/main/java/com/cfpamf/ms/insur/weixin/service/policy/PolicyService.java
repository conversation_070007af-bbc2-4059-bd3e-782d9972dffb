package com.cfpamf.ms.insur.weixin.service.policy;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.channel.BankMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.product.EnumPremiumFlow;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.whale.api.group.WhaleMessageBuilder;
import com.cfpamf.ms.insur.admin.external.whale.client.WhaleGroupApi;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfig;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.po.channel.Bank;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.OccupationService;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.cfpamf.ms.insur.admin.service.sys.SysNotifyService;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.constant.RedisCache;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.base.util.email.JavaMailHelper;
import com.cfpamf.ms.insur.weixin.common.Constants;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.dao.safes.*;
import com.cfpamf.ms.insur.weixin.dao.safes.correct.CorrectLogMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.*;
import com.cfpamf.ms.insur.weixin.pojo.po.order.PayNotice;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.base.WhalePlatformData;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.form.OuterBank;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.request.GroupOuterBankListInput;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.response.GroupOuterBankListOutput;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.ProductReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.*;
import com.cfpamf.ms.insur.weixin.service.WxAbstractService;
import com.cfpamf.ms.insur.weixin.service.config.ApplyConfig;
import com.cfpamf.ms.insur.weixin.service.underwriting.GroupApplyHandler;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.exception.ExcptEnum.COMMISSION_ERROR_201014;

/**
 * 团险投保流程对接
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class PolicyService extends WxAbstractService {

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    SmProductVersionMapper productVersionMapper;

    @Autowired
    protected AuthUserMapper userMapper;

    @Autowired
    private SmCommissionMapper commissionMapper;

    @Autowired
    private EndorMapper endorMapper;

    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private WxOrderMapper wxOrderMapper;

    @Autowired
    private TimeFactorMapper timeFactorMapper;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private GroupApplyHandler applyHandler;

    @Autowired
    private SmProductService productService;

    @Autowired
    private SysNotifyService notifyService;

    @Autowired
    private WhaleGroupApi whaleGroupApi;

    @Autowired
    private SmOrderManageService orderManageService;

    @Autowired
    private OccupationService occupationService;

    /**
     * 投保-查询产品基本信息
     *
     * @param productId
     * @param region
     */
    public ProductDetailVo queryProductInfo(int productId, String region) {
        ProductDTO productDTO = policyMapper.getProductDetailById(productId);

        if (productDTO == null) {
            throw new BizException(ExcptEnum.PRODUCT_NOT_ONLINE_201001);
        }
        Integer productVersion = productVersionMapper.getMaxVersion(productId);

        List<PlanVo> plans = convert2Vo(getPlans(productId, region));
        List<CoverageVo> coverages = buildCoverage(productId);
        plans.forEach(p -> {
            p.setCoverages(coverages);
        });
        ProductDetailVo detail = ProductDetailVo.build(productDTO);
        detail.setPlans(plans);
        detail.setPolicyHolderCondition(buildQuoteLimitText(productId));
        if (productVersion != null) {
            detail.setProductVersion(String.valueOf(productVersion));
        }
        /**
         * 前端提示内容
         */
        ShowTips tips = new ShowTips();
        tips.setMessage("此产品暂不支持对公转账，如有此需求请登录i云保进行询价及投保，保费以i云保展示为准。");
        detail.setTips(tips);

        /**
         * 判断是否支持短期险
         */
        List<PremiumFlow> flows = productMapper.queryPremiumFactor(productId, null, EnumPremiumFlow.SHORT_TERM_MONTH_FACTOR.name());
        if (flows != null && flows.size() > 0) {
            detail.setValidPeriod(fixPeriod2ShortTerm(detail.getValidPeriod()));
            detail.setTimeFactorItems(cvtTimeFactor("MONTH", flows));
        }
        return detail;
    }

    private List<TimeFactorItem> cvtTimeFactor(String unit, List<PremiumFlow> flows) {
        return flows.stream().map(entry -> {
            TimeFactorItem item = new TimeFactorItem();
            item.setFactorValue(entry.getFactorValue());
            item.setFlow(entry.getFlow());
            item.setTimeUnit(unit);
            item.fixFactorName();
            return item;
        }).collect(Collectors.toList());
    }

    public String fixPeriod2ShortTerm(String period) {
        String reg = "^[0-9]+[年|月|日]{1}$";
        if (period.matches(reg)) {
            String timeStr = period.substring(0, period.length() - 1);
            Integer time = Integer.valueOf(timeStr);
            if (time > 1) {
                return "1~" + period;
            }
        }
        return period;
    }


    /**
     * 查询团险起保人数限制
     */
    public String buildQuoteLimitText(Integer productId) {
        String quoteLimit = null;
        StringBuilder ocpQuoteLimit = new StringBuilder();
        List<QuoteLimitDTO> limits = policyMapper.queryQuoteLimit(productId);
        for (QuoteLimitDTO dto : limits) {
            if (QuoteLimitDTO.LIMIT_TYPE_PER.equals(dto.getLimitType())) {
                quoteLimit = String.valueOf(dto.getMinPerQty());
            } else if (QuoteLimitDTO.LIMIT_TYPE_OCPN_PER.equals(dto.getLimitType())) {
                ocpQuoteLimit
                        .append(String.format(Constants.QUOTE_LIMIT, dto.getOccupationGroup(), dto.getMinPerQty()));
                ocpQuoteLimit.append(";");
            }
        }
        String text = ocpQuoteLimit.toString();
        if (text.length() > 0) {
            text = text.substring(0, text.length() - 1);
            text = "(" + text + ")";
        }
        return quoteLimit + text;
    }

    /**
     * 查询投保告知
     *
     * @param productId
     * @return
     */
    public String queryNotice(int productId) {
        return policyMapper.getNotice(productId);
    }

    /**
     * 查询产品-投保须知，常见问题，保险条款
     *
     * @param productId
     * @return
     */
    public ProductExtendVo queryExtend(int productId) {
        ProductExtendVo vo = new ProductExtendVo();
        ProductDTO productDTO = policyMapper.getProductDetailById(productId);
        if (productDTO != null) {
            vo.setAttentions(productDTO.getAttentions());
            vo.setProductNotice(productDTO.getGlProductNotice());
        }
        ClauseMasterVo clause = new ClauseMasterVo();
        clause.setAttach(policyMapper.queryProductClausesByProductId(productId));
        String content = policyMapper.getProductClauseContent(productId);
        clause.setSelfConfirms(querySelfConfirm(productId));
        clause.setContent(content);
        vo.setClause(clause);
        return vo;
    }

    /**
     * 查询自主确认文件包
     *
     * @param productId
     * @return
     */
    public List<SelfConfirmVo> querySelfConfirm(Integer productId) {
        List<SelfConfirmDTO> data = policyMapper.querySelfConfirm(productId);
        List<SelfConfirmVo> results = new ArrayList<>();
        for (SelfConfirmDTO dto : data) {
            SelfConfirmVo vo = new SelfConfirmVo();
            vo.setContent(dto.getFileContent());
            vo.setName(dto.getFileName());
            if (org.apache.commons.lang.StringUtils.isNotBlank(dto.getFileUrl())) {
                vo.setAttach(JSON.parseArray(dto.getFileUrl(), ConfirmAttach.class));
            }
            results.add(vo);
        }
        return results;
    }

    public List<CoverageVo> buildCoverage(int productId) {
        List<CoverageVo> coverages = policyMapper.getCoverage(productId);
        if (coverages != null) {
            List<CoverageAmountVo> coverageAmounts = policyMapper.getCoverageAmounts(productId);
            Map<Integer, List<CoverageAmountVo>> map = convert2Map(coverageAmounts);

            List<DutyFactorFlowDTO> flows = policyMapper.getDutyFactorFlows(productId);
            Map<Integer, Collection<DutyFactorFlowVo>> flowMap = convertFlow2Map(flows);
            coverages.forEach(c -> {
                c.setAmounts(map.get(c.getSpcId()));
                c.setDutyFlowFactor(flowMap.get(c.getSpcId()));
            });
        }
        return coverages;
    }

    public Map<Integer, Collection<DutyFactorFlowVo>> convertFlow2Map(List<DutyFactorFlowDTO> flows) {
        Map<Integer, Collection<DutyFactorFlowVo>> map = new HashMap<>();
        Map<Integer, List<DutyFactorFlowDTO>> superMap = new HashMap<>();
        flows.forEach(a -> {
            List<DutyFactorFlowDTO> list = superMap.get(a.getSpcId());
            if (list != null) {
                list.add(a);
            } else {
                list = new ArrayList<>();
                list.add(a);
                superMap.put(a.getSpcId(), list);
            }
        });
        for (Map.Entry<Integer, List<DutyFactorFlowDTO>> entry : superMap.entrySet()) {
            Map<String, DutyFactorFlowVo> itemMap = new HashMap<>();
            entry.getValue().forEach(i -> {
                DutyFactorFlowVo.DutyFactorField field = new DutyFactorFlowVo.DutyFactorField();
                field.setFactorName(i.getFactorName());
                field.setOptionName(i.getOptionName());
                field.setFactorValue(i.getFactorValue());
                field.setFlow(i.getFlow());
                field.setId(i.getId());
                field.setIsDefault(i.getIsDefault());
                if (itemMap.containsKey(i.getDutyFactorCode())) {
                    itemMap.get(i.getDutyFactorCode()).addItem(field);
                } else {
                    DutyFactorFlowVo vo = new DutyFactorFlowVo();
                    vo.setDutyCode(i.getDutyCode());
                    vo.setDutyFactorCode(i.getDutyFactorCode());
                    vo.setDutyFactorName(i.getDutyFactorName());
                    vo.setSpcId(i.getSpcId());
                    vo.addItem(field);
                    itemMap.put(i.getDutyFactorCode(), vo);
                }
            });
            map.put(entry.getKey(), itemMap.values());
        }
        return map;
    }

    private Map<Integer, List<CoverageAmountVo>> convert2Map(List<CoverageAmountVo> amounts) {
        Map<Integer, List<CoverageAmountVo>> map = new HashMap<>();
        amounts.forEach(a -> {
            List<CoverageAmountVo> list = map.get(a.getSpcId());
            if (list != null) {
                list.add(a);
            } else {
                list = new ArrayList<>();
                list.add(a);
                map.put(a.getSpcId(), list);
            }
        });
        return map;
    }

    public List<PlanVo> convert2Vo(List<PlanDTO> planDTOS) {
        List<PlanVo> plans = new ArrayList<>();
        if (planDTOS != null) {
            Map<Integer, SmCommissionSettingVO> map =
                    getCommition(planDTOS.stream().map(PlanDTO::getId).collect(Collectors.toList()));
            SmCommissionSettingVO setting = null;
            for (PlanDTO dto : planDTOS) {
                PlanVo vo = new PlanVo();
                vo.setId(dto.getId());
                vo.setPlanName(dto.getPlanName());
                vo.setMinPremium(dto.getMinPremium());
                vo.setPlanCode(dto.getPlanCode());
                vo.setPlanType(dto.getPlanType());
                if ((setting = map.get(dto.getId())) != null) {
                    vo.setCommission(setting.getPaymentProportion());
                }
                plans.add(vo);
            }
        }
        return plans;
    }

    /**
     * 获取计划提成设置信息
     *
     * @param planIds
     * @return
     */
    private Map<Integer, SmCommissionSettingVO> getCommition(List<Integer> planIds) {
        List<SmCommissionSettingVO> data = commissionMapper.getCommissionSettingByPlanIds(planIds, new Date());
        if (data != null) {
            return data.stream().collect(Collectors.toMap(SmCommissionSettingVO::getPlanId, Function.identity()));
        }
        return new HashMap<>();
    }

    /**
     * 根据区域获取有权限的产品计划
     *
     * @param productId
     * @param regionName
     * @return
     */
    @Cacheable(cacheNames = "query.plan", key = "#productId+':'+#regionName")
    public List<PlanDTO> getPlans(int productId, String regionName) {
        List<PlanDTO> plans = policyMapper.getPlans(productId, regionName);
        plans = CollectionUtils.isEmpty(plans) ? policyMapper.getPlans(productId, null) : plans;
        return plans;
    }

    public HealthNoticeVo queryHealth(int productId) {
        HealthNoticeVo vo = new HealthNoticeVo();
        ProductDTO detailVO = policyMapper.getProductDetailById(productId);
        if (detailVO != null) {
            vo.setContent(detailVO.getHealthNotification());
        }
        List<QuestionVo> questions = policyMapper.queryHealth(productId);
        vo.setQuestions(questions);
        return vo;
    }

    /**
     * 团险报价接口
     *
     * @param channel
     * @param req
     * @return
     */
    public GroupQuoteResponse quotePrice4Group(String channel, GroupUnderwriting req) {
        Integer productId = req.getProductId();
        Integer planId = req.getProduct().getPlanId();
        setCommission(productId, planId, req);
        setEnterpriseRiskFactor(productId, planId, req);
        setSysDutys(channel, req);
        ChannelOrderService serice = ServiceConfig.getService(channel);
        return serice.quotePrice4Group(req);
    }

    private void setEnterpriseRiskFactor(Integer productId, Integer planId, ApplyHolder holder) {
        String factor = queryEnterpriseRiskFactor(productId, planId);
        holder.setEnterpriseRiskFactor(factor);
    }


    private void setSysDutys(String channel, ApplyHolder holder) {
        List<SysDutyConfig> data = orderMapper.queryDutyConfig(channel, null);
        data = data == null ? Collections.EMPTY_LIST : data;
        holder.setSysDutys(data);
    }

    /**
     * 查询企业风险系数因子
     *
     * @param productId
     * @param planId
     * @return
     */
    private String queryEnterpriseRiskFactor(Integer productId, Integer planId) {
        String factor = null;
        List<PremiumFlow> premiumFlows = productMapper.queryPremiumFactor(productId, planId, EnumPremiumFlow.ENTERPRISE_RISK_FACTOR.name());
        if (!CollectionUtils.isEmpty(premiumFlows)) {
            PremiumFlow premiumFlow = premiumFlows.get(0);
            factor = premiumFlow.getFactorValue();
        }
        return factor;
    }

    /**
     * 1.验证产品是否在维护
     * 2.设置每一个产品计划的结算佣金
     *
     * @param holder
     */
    private void setCommission(Integer productId, Integer planId, ApplyHolder holder) {
        Date now = new Date();
        Map<Integer, SmCommissionSettingVO> commissionMap = getCommissionMap(productId, now);

        /**
         * 设置佣金比例
         */
        SmCommissionSettingVO settingVo = commissionMap.get(planId);
        if (settingVo == null) {
            throw new MSBizNormalException(COMMISSION_ERROR_201014.getCode(), COMMISSION_ERROR_201014.getMsg());
        }
        holder.setCommission(settingVo);
    }

    /**
     * 验证出单渠道是否在维护中???
     *
     * @param subChannel
     * @param planId
     */
    private void verifyPlan(String subChannel, Integer planId) {
        SmPlanVO plan = productService.getPlanById(planId);
        if (plan == null) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201011.getCode(), ExcptEnum.PRODUCT_ERROR_201011.getMsg());
        }
        if (notifyService.isMaintenance(plan.getChannel(), subChannel)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_8020001);
        }
    }

    /**
     * @param productId
     * @param now
     * @return
     */
    public Map<Integer, SmCommissionSettingVO> getCommissionMap(Integer productId, Date now) {
        List<SmCommissionSettingVO> settings = commissionMapper.listCommissionSettingsByProductId(productId, now);
        Map<Integer, SmCommissionSettingVO> map = new HashMap<>();
        settings.forEach(i -> {
            map.put(i.getPlanId(), i);
        });
        return map;
    }


    /**
     * 团险投保[核保]接口
     *
     * @param channel
     * @param req
     * @return
     */
    public GroupQuoteResponse underwriting(String channel, GroupUnderwriting req) {
        req.init();
        ProductReq product = req.getProduct();
        Integer productId = req.getProductId();
        Integer planId = product.getPlanId();

        /**
         * 1.验证出单渠道是否在维护中...
         */
        verifyPlan(req.getSubChannel(), planId);
        selfCheck(productId, req.getStartTime(), req.getInsuredList());
        /**
         * ☆☆☆☆☆☆☆☆☆☆☆
         * 1.设置推荐人信息
         * 2.设置产品结算提成信息
         * 3.设置企业风险系数
         * ☆☆☆☆☆☆☆☆☆☆☆
         */
        setCommission(productId, planId, req);
        setEnterpriseRiskFactor(productId, planId, req);
        setAgent(req.getBizCode(), req);
        setSysDutys(channel, req);

        req.setSubChannel(ChannelConstant.ORDER_CHANNEL_XIANGZHU);
        req.setSubmitTime(LocalDateUtil.commonFormat());
        req.setChannel(channel);

        genActiveBranchId(req.getInsuredList());
        fixInsuredInfo(req.getInsuredList());
        ChannelOrderService service = ServiceConfig.getService(channel);

        String orderId = req.getOrderId();
        orderId = StringUtils.isBlank(orderId)? IdGenerator.getUuid():orderId;
        String key = RedisCache.buildGroupApplyCache(orderId);

        boolean r = tokenService.lockBusinessToken(key,300L);
        if(!r){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"请勿重复提交");
        }
        try{
            return service.underwriting(req);
        } finally {
            tokenService.unlockBusinessToken(key);
        }
    }

    private void fixInsuredInfo(List<GroupInsured> insuredList) {
        if(CollectionUtils.isEmpty(insuredList)){
            return;
        }
        insuredList.forEach(insured->{
            String insuredIdCard = insured.getIdNumber();
            if(StringUtils.isNotBlank(insuredIdCard)) {
                insured.setIdNumber(insuredIdCard.toUpperCase());
            }
        });
    }

    @Autowired
    ProductSpecialRuleProperties productSpecialRuleProperties;

    /**
     * 自主校验数据，理论上保司会做投保校验；
     * 但是有些保司可能不规范，导致用户重复投保或者违反合规要求
     *
     * @param productId
     * @param insuredList
     */
    private void selfCheck(Integer productId, String startTime, List<GroupInsured> insuredList) {
        SmProductDetailVO product = productService.getProductById(productId);
        if (product == null) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_NOT_ONLINE_201001.getCode(), ExcptEnum.PRODUCT_NOT_ONLINE_201001.getMsg());
        }
        if (product.getState() != 1) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_NOT_ONLINE_201001.getCode(), ExcptEnum.PRODUCT_NOT_ONLINE_201001.getMsg());
        }
        List<String> idNumbers = insuredList.stream().map(GroupInsured::getIdNumber).collect(Collectors.toList());
        Date effTime = LocalDateUtil.parseTime(startTime);
        orderManageService.checkBuyLimit(productId, 1, effTime, idNumbers);
    }

    /**
     * 生成分单号
     *
     * @param insuredList
     */
    protected void genActiveBranchId(List<GroupInsured> insuredList) {
        insuredList.stream().forEach(entry -> {
            entry.setActiveBranchId(IdGenerator.getUuid());
        });
    }

    private void setAgent(String bizCode, ApplyHolder holder) {
        Agent agent = holder.getAgent();
        if (agent == null) {
            agent = new Agent();
        }
        agent = fixAgent(agent, bizCode);
        holder.setAgent(agent);
    }

    @Autowired
    private InquiryService inquiryService;

    /**
     * 团险询价
     *
     * @param channel
     * @param req
     * @return
     */
    public GroupInquiryResponse inquiry(String channel, GroupInquiry req) {
        return inquiryService.inquery(req);
    }

    /**
     * 团险批改报价
     *
     * @param channel
     * @param req
     * @return
     */
    public GroupEndorResponse endorCalPremium(String channel, GroupEndorsement req) {
        applyHandler.endorCheck(req);

        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("%s订单对应的产品信息为空", req.getOrderId()));
        }
        Integer productId = orderProduct.getProductId();
        Integer planId = orderProduct.getPlanId();
        setCommission(productId, planId, req);
        setSysDutys(channel, req);
        ChannelOrderService serice = ServiceConfig.getService(channel);
        return serice.endorCalPremium(req);
    }

    /**
     * 核保校验
     *
     * @param req
     * @return
     */
    public boolean applyCheck(GroupCheckReq req) {
        return applyHandler.applyCheck(req);
    }

    /**
     * 查询批改信息
     *
     * @param policyNo
     * @return
     */
    public EndorPolicyInfo queryPolicyInfo(String policyNo, String orderId) {
        OrderDetailQuery query = buildQuery();
        query.setId(orderId);
        query.setPolicyNo(policyNo);
        EndorPolicyInfo endor = policyMapper.queryPolicyByVo(query);
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        if (endor != null) {
            OrderProductDTO orderProduct = policyMapper.queryOrderProduct(endor.getOrderId());
            if (orderProduct != null) {
                List<OrderCoverageDTO> coverages = orderProduct.parseCoverage();
                endor.setCoverages(coverages);
            }
            EndorConfig config = policyMapper.queryEndorConfig(endor.getProductId());
            endor.setEndorConfig(config);
        }
        return endor;
    }

    /**
     * 批改核保
     *
     * @param channel
     * @param req
     * @return
     */
    public GroupEndorResponse endorUnderwriting(String channel, GroupEndorsement req) {
        setSysDutys(channel, req);
        groupApplyHandler.endorCheck(req);
        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("%s订单对应的产品信息为空", req.getOrderId()));
        }
        Integer productId = orderProduct.getProductId();
        Integer planId = orderProduct.getPlanId();
        setCommission(productId, planId, req);

        ChannelOrderService service = ServiceConfig.getService(channel);
        return service.endorUnderwriting(req);
    }

    @Autowired
    private GroupApplyHandler groupApplyHandler;

    /**
     * 批改提交
     *
     * @param channel
     * @param req
     * @return
     */
    public GroupEndorResponse endorCommit(String channel, GroupEndorsement req) {
        setAgent(null, req);
        setSysDutys(channel, req);
        groupApplyHandler.endorCheck(req);

        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("%s订单对应的产品信息为空", req.getOrderId()));
        }
        Integer productId = orderProduct.getProductId();
        Integer planId = orderProduct.getPlanId();
        setCommission(productId, planId, req);
        genActiveBranchId(req.getInsuredList());
        fixInsuredInfo(req.getInsuredList());

        ChannelOrderService serice = ServiceConfig.getService(channel);
        return serice.endorCommit(req);
    }

    /**
     * 重新设置Agent
     *
     * @param agent
     * @param bizNo
     * @return
     */
    private Agent fixAgent(Agent agent, String bizNo) {
        if (agent == null) {
            agent = new Agent();
        }

        if (StringUtils.isNotBlank(bizNo)) {
            String jobNumberByBizCode = userMapper.getMainJobNumberByBizCode(bizNo);
            if (StringUtils.isEmpty(jobNumberByBizCode)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "推荐码不存在！");
            }
            agent.setRecommendId(jobNumberByBizCode);
        }
        if (StringUtils.isNotBlank(agent.getRecommendId())) {
            try {
                UserPostService userPostService = SpringFactoryUtil.getBean(UserPostService.class);
                List<UserPost> postList = userPostService.listUserPostByJobNumber(agent.getRecommendId());
                UserPost userPost;
                if (postList != null && postList.size() > 0) {
                    String jobCode = agent.getJobCode();
                    Optional<UserPost> op =
                            postList.stream().filter(p -> Objects.equals(jobCode, p.getJobCode())).findFirst();
                    if (op.isPresent()) {
                        userPost = op.get();
                    } else {
                        userPost = postList.get(0);
                    }
                    agent.setRecommendJobCode(userPost.getJobCode());
                    agent.setRecommendMainJobNumber(userPost.getMainJobNumber());
                    agent.setRecommendOrgCode(userPost.getOrgCode());
                    agent.setCustomerAdminJobCode(userPost.getJobCode());
                    agent.setCustomerAdminMainJobNumber(userPost.getMainJobNumber());
                    agent.setCustomerAdminOrgCode(userPost.getOrgCode());
                    agent.setRecommendMasterName(userPost.getUserMasterName());
                    agent.setRecommendAdminName(userPost.getUserAdminName());
                    agent.setRecommendEntryDate(userPost.getEntryDate());
                    agent.setRecommendPostName(userPost.getPostName());

                }
            } catch (Exception e) {
                log.warn("BMS 查询用户失败", e);
            }
        }
        return agent;
    }

    public List<BankVo> banklist(String channel, String keyword) {
        if (EnumChannel.TK_PAY.getCode().equals(channel)) {
            return queryByChannel(channel, keyword);
        } else if (EnumChannel.ZA.getCode().equals(channel)) {
            return queryZaBankList(EnumChannel.ZA.getCode());
        }
        return ApplyConfig.getBanks();
    }


    private List<BankVo> queryByChannel(String channel, String keyword) {
        List<Bank> banks = bankMapper.queryByChannel(channel, keyword);
        if (CollectionUtils.isEmpty(banks)) {
            return Collections.emptyList();
        }
        return banks.stream().map(bank -> {
            return new BankVo(bank.getCode(), bank.getName());
        }).collect(Collectors.toList());
    }

    private List<BankVo> queryZaBankList(String channel) {
        WhalePlatformData platformData = WhaleMessageBuilder.initWhalePlatformData(channel);
        GroupOuterBankListInput input = new GroupOuterBankListInput();
        input.setPlatformData(platformData);
        WhaleResp<GroupOuterBankListOutput> whaleResp = whaleGroupApi.outerGetBankList(input);
        if (!Objects.equals(WhaleGroupApi.WHALE_RESP_SUCCESS_CODE, whaleResp.getCode())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "获取银行列表异常");
        }
        GroupOuterBankListOutput groupOuterBankListOutput = whaleResp.getData();
        List<OuterBank> banks = groupOuterBankListOutput.getBanks();
        if (CollectionUtils.isEmpty(banks)) {
            return Collections.emptyList();
        }
        return banks.stream().map(bank -> {
            return new BankVo(bank.getCode(), bank.getName(), bank.getBankAbbrCode());
        }).collect(Collectors.toList());
    }

    @Autowired
    private BankMapper bankMapper;

    @Autowired
    private SmOccupationMapper mapper;

    /**
     * 查询所有职业列表； 批改规则会在核保时校验是否可以新增职业类别；
     * 跟保司确认-批改流程职业表的版本跟新契约投保的职业表版本一致
     *
     * @param orderId
     * @return
     */
    public List<WxTreeVO> getJobListV2(String orderId) {
        FastOrderProduct product = orderMapper.queryFastProduct(orderId);
        if (product == null) {
            log.warn("订单险种信息为空:{}", orderId);
            return Collections.emptyList();
        }
        /**
         * 判断批改配置
         */
        Integer companyId = product.getCompanyId();
        Integer productId = product.getProductId();

        return occupationService.listOccupation4Correct(companyId, productId, orderId);
    }

    /**
     * 查询所有职业列表； 批改规则会在核保时校验是否可以新增职业类别；
     *
     * @param orderId
     * @param policyNo
     * @return
     */
    @Deprecated
    public List<WxTreeVO> getJobList(String orderId, String policyNo) {
        FastOrderProduct product = orderMapper.queryFastProduct(orderId);
        if (product == null) {
            return null;
        }
        /**
         * 判断批改配置
         */
        Integer companyId = product.getCompanyId();
        List<SmOccupationVO> occupationVos = mapper.listOccupations(companyId, null, null, true);

        List<WxTreeVO> wxTreeVos = occupationVos.stream().map(this::mapperToWxTreeVo)
                .sorted(Comparator.comparing(WxTreeVO::getValue)).collect(Collectors.toList());
        // 职业第二级类别编码
        List<String> categoryCodes1 =
                occupationVos.stream().filter(g -> !com.alibaba.druid.util.StringUtils.isEmpty(g.getOccupationGroup()))
                        .map(SmOccupationVO::getParentCode).collect(Collectors.toList());
        // 职业第一级类别编码
        List<String> categoryCodes2 = occupationVos.stream()
                .filter(o -> categoryCodes1.stream().anyMatch(j -> Objects.equals(o.getCategoryCode(), j)))
                .map(SmOccupationVO::getParentCode).collect(Collectors.toList());
        //
        return wxTreeVos.stream()
                .filter(o -> categoryCodes2.stream().anyMatch(c -> Objects.equals(c, o.getValue()))
                        || categoryCodes1.stream().anyMatch(c -> Objects.equals(c, o.getValue()))
                        || categoryCodes1.stream().anyMatch(c -> Objects.equals(c, o.getParent())))
                .collect(Collectors.toList());
    }

    private WxTreeVO mapperToWxTreeVo(SmOccupationVO o) {
        WxTreeVO wtv = new WxTreeVO();
        wtv.setParent(o.getParentCode());
        if (com.alibaba.druid.util.StringUtils.isEmpty(o.getOccupationName())) {
            wtv.setName(o.getCategoryName());
            wtv.setValue(o.getCategoryCode());
            wtv.setType(o.getOccupationGroup());
        } else {
            wtv.setName(o.getOccupationName());
            wtv.setValue(o.getOccupationCode());
            wtv.setType(o.getOccupationGroup());
        }
        wtv.setParent(o.getParentCode());
        return wtv;
    }

    /**
     * 用原单Id查询保单的被保人列表[出单成功的被保人列表，会剔除已退保的用户]
     *
     * @param orderId
     * @param policyNo
     * @return
     */
    public Collection<InsuredListDTO> getInsureds(String orderId, String policyNo, String keyword) {
        OrderDetailQuery query = buildQuery();
        query.setId(orderId);
        query.setPolicyNo(policyNo);
        query.setKeyword(keyword);

        int i = policyMapper.checkPermission(query);
        if (i < 1) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        log.info("开始查询保单被保人列表:{}", query);
        List<InsuredListDTO> insureds = policyMapper.queryInsureds(query);
        if (CollectionUtils.isEmpty(insureds)) {
            log.warn("保单的被保人列表为空：{}", query);
            return Collections.emptyList();
        }
        Map<String, InsuredListDTO> rtnMap = new TreeMap<>();

        for (InsuredListDTO dto : insureds) {
            String idNumber = dto.getIdNumber();
            if (Objects.isNull(idNumber)) {
                continue;
            }
            idNumber = idNumber.toUpperCase();
            if (SmConstants.POLICY_STATUS_SUCCESS.equals(dto.getAppStatus())) {
                rtnMap.put(idNumber, dto);
            }
            if (SmConstants.POLICY_STATUS_CANCEL_SUCCESS.equals(dto.getAppStatus())) {
                rtnMap.remove(idNumber);
            }
        }

        return rtnMap.values();
    }

    /**
     * 获取批单列表
     * TODO :S65迭代需要优化
     * 统一规则：
     * orderId约定是原单的订单Id
     * endorId约定是批改单的订单Id
     *
     * @param policyNo
     * @return
     */
    public List<EndorListDTO> getEndorList(String orderId, String policyNo) {
        OrderDetailQuery query = buildQuery();
        query.setId(orderId);
        query.setPolicyNo(policyNo);
        EndorListDTO rawOrder = policyMapper.getEndorRawOrder(query);
        if (rawOrder == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        List<EndorListDTO> data = new ArrayList<>();
        data.add(rawOrder);
        List<EndorListDTO> endorList = policyMapper.getEndorList(orderId, policyNo);
        if (data != null) {
            data.addAll(endorList);
        }
        return data;
    }

    /**
     * [电子保单]-获取批改单列表
     *
     * @param orderId       原单-订单Id
     * @param policyNo      保单号
     * @param invoiceStatus 0=未申请,1=已申请
     * @return
     */
    public List<EndorListDTO> getEndorList4Invoice(String orderId, String policyNo, String invoiceStatus) {
        OrderDetailQuery query = buildQuery();
        query.setId(orderId);
        query.setPolicyNo(policyNo);
        /**
         * 查询原单-发票信息
         */
        EndorListDTO rawOrder = policyMapper.getInvoiceRawOrder(query);

        if (rawOrder == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        List<EndorListDTO> data = new ArrayList<>();
        if (Objects.isNull(invoiceStatus)) {
            data.add(rawOrder);
        } else {
            /**
             * 过滤-未申请发票的订单
             */
            if (Objects.equals("0", invoiceStatus) && Objects.equals(0, rawOrder.getInvoiceFlag())) {
                data.add(rawOrder);
            }
            /**
             * 过滤-已申请发票的订单
             */
            if (Objects.equals("1", invoiceStatus) && Objects.equals(1, rawOrder.getInvoiceFlag())) {
                data.add(rawOrder);
            }
        }
        List<EndorListDTO> endorList =
                policyMapper.getEndorList4Invoice(rawOrder.getChannel(), policyNo, invoiceStatus);
        if (endorList != null) {
            data.addAll(endorList);
        }
        return data;
    }

    /**
     * 获取保单信息
     *
     * @param orderId
     * @param policyNo
     * @param endorsementNo
     * @return
     */
    public PolicyVo policyInfo(String orderId, String policyNo, String endorsementNo) {
        if (StringUtils.isBlank(endorsementNo)) {
            return queryRawPolicy(orderId, policyNo);
        }

        return queryEndor(orderId, policyNo, endorsementNo);
    }

    @Autowired
    private CorrectLogMapper correctLogMapper;

    /**
     * 获取批改信息
     *
     * @param orderId
     * @param endorNo
     * @return
     */
    private PolicyVo queryEndor(String orderId, String policyNo, String endorNo) {

        OrderDetailQuery query = buildQuery();
        query.setId(orderId);
        EndorDTO endor = policyMapper.queryEndorInfo(query);
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_EXISTS);
        }
        int opType = endor.getOpType();
        PolicyVo vo = new PolicyVo();
        BeanCopier copier = BeanCopier.create(EndorDTO.class, PolicyVo.class, false);
        copier.copy(endor, vo, null);

        vo.setTotalPremium(endor.getAmount());
        List<InsuredListDTO> insureds = policyMapper.queryEndorInsuredList(policyNo, endorNo);
        if (StringUtils.isBlank(vo.getEPolicyUrl())) {
            String ePolicyUrl = insureds.stream()
                    .filter(entry -> {
                        return StringUtils.isNotBlank(entry.getDownloadURL());
                    })
                    .map(InsuredListDTO::getDownloadURL)
                    .findFirst()
                    .orElse("");
            vo.setEPolicyUrl(ePolicyUrl);
        }


        List<InsuredListDTO> grantList = new ArrayList<>();
        List<InsuredListDTO> writeOffList = new ArrayList<>();
        for (InsuredListDTO ins : insureds) {
            if ("1".equals(ins.getAppStatus())) {
                grantList.add(ins);
            } else if ("4".equals(ins.getAppStatus())) {
                writeOffList.add(ins);
            }
        }
        vo.setGrantList(grantList);
        vo.setWriteOffList(writeOffList);

        if (opType == 4) {
            List<InsuredReplaceListDTO> replaceList = buildReplaceMembers(opType, insureds);
            vo.setReplaceList(replaceList);
        }
        return vo;
    }

    private List<InsuredReplaceListDTO> buildReplaceMembers(int opType, List<InsuredListDTO> insureds) {
        Map<String, List<InsuredListDTO>> insuredMap = LambdaUtils.groupBy(insureds, InsuredListDTO::getAppStatus);
        List<InsuredListDTO> onlineList = insuredMap.get("1");
        if (onlineList == null) {
            return Collections.emptyList();
        }
        List<InsuredListDTO> offlineList = insuredMap.get("4");
        Map<String, InsuredListDTO> offlineMap = LambdaUtils.safeToMap(offlineList, InsuredListDTO::getActiveBranchId);

        List<InsuredReplaceListDTO> replaceList = new ArrayList<>();

        for (InsuredListDTO log : onlineList) {
            InsuredReplaceListDTO entry = new InsuredReplaceListDTO();
            entry.setOpType(opType);
            entry.setReplaceIdType(log.getIdType());
            entry.setReplaceIdNumber(log.getIdNumber());
            entry.setReplaceName(log.getPersonName());
            entry.setReplaceOccupationCode(log.getOccupationCode());
            entry.setBranchId(log.getBranchId());
            entry.setActiveBranchId(log.getActiveBranchId());
            entry.setFhOrderId(log.getFhOrderId());
            entry.setPolicyNo(log.getPolicyNo());
            entry.setOccupationGroup(log.getOccupationGroup());
            InsuredListDTO replacedMan = offlineMap.get(log.getActiveBranchId());
            if (replacedMan != null) {
                entry.setIdType(replacedMan.getIdType());
                entry.setIdNumber(replacedMan.getIdNumber());
                entry.setPersonName(replacedMan.getPersonName());
                entry.setOccupationCode(replacedMan.getOccupationCode());
            }
            replaceList.add(entry);
        }
        return replaceList;
    }

    /**
     * 获取原始保单
     *
     * @param orderId
     * @param policyNo
     * @return
     */
    public PolicyVo queryRawPolicy(String orderId, String policyNo) {
        OrderDetailQuery query = buildQuery();
        query.setPolicyNo(policyNo);
        query.setId(orderId);

        EndorPolicyInfo policy = policyMapper.queryPolicyByVo(query);
        if (policy == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_EXISTS);
        }
        PolicyVo vo = new PolicyVo();
        BeanCopier copier = BeanCopier.create(EndorPolicyInfo.class, PolicyVo.class, false);
        copier.copy(policy, vo, null);

        vo.setOpType(0);
        GroupApplicant applicant = policyMapper.queryApplicant(orderId);
        vo.setApplicant(applicant);
        List<InsuredListDTO> insureds = policyMapper.queryRawInsureds(orderId);
        vo.setInsuredList(insureds);
        if (insureds != null) {
            String ePolicyUrl = insureds.stream()
                    .filter(entry -> {
                        return StringUtils.isNotBlank(entry.getDownloadURL());
                    })
                    .map(InsuredListDTO::getDownloadURL)
                    .findFirst()
                    .orElse("");
            vo.setEPolicyUrl(ePolicyUrl);
        }

        OrderProductDTO orderProduct = policyMapper.queryOrderProduct(orderId);
        if (orderProduct != null) {
            List<OrderCoverageDTO> coverages = orderProduct.parseCoverage();
            vo.setCoverages(coverages);
        }
        return vo;
    }

    /**
     * 团险撤销批改
     *
     * @param channel
     * @param req
     * @return
     */
    public Boolean revokeEndor(String channel, GroupRevokeVo req) {
        Endor endor = endorMapper.queryEndorInfo(channel, req.getPolicyNo(), req.getEndorsementNo());
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        if (endor.finish()) {
            throw new MSBizNormalException(ExcptEnum.DATA_REVOKED.getCode(), ExcptEnum.DATA_REVOKED.getMsg());
        }
        /**
         * [众安]已支付的单不能做撤销操作
         */
        if (endor.getStatus() == 1) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "该单已支付，不能再撤销");
        }
        ChannelOrderService serice = ServiceConfig.getService(channel);
        Boolean res = serice.revokeEndor(req);
        if (res) {
            Endor param = new Endor();
            param.setId(endor.getId());
            param.setStatus(-1);
            param.setOperaor(HttpRequestUtil.getUserId());
            int r1 = endorMapper.updateByPrimaryKeySelective(param);
            log.info("[{}]-撤销批改结果更新入库！", r1);
        }
        return res;
    }

    /**
     * 管理员-团险撤销批改
     *
     * @param channel
     * @param req
     * @return
     */
    public Boolean revokeEndor4Admin(String channel, GroupRevokeVo req) {
        ChannelOrderService serice = ServiceConfig.getService(channel);
        Boolean res = serice.revokeEndor(req);
        return res;
    }

    /**
     * 批改生效接口
     *
     * @param channel
     * @param req
     * @return
     */
    public Boolean effectEndor(String channel, GroupEndorEffectReq req) {

        ChannelOrderService serice = ServiceConfig.getService(channel);
        return serice.endorEffect(req);
    }

    /**
     * 继续批改，暂时只针对未支付的单可执行该操作 检查批改单状态，告诉前端后续的操作 接口返回状态：0=待支付；1=已完成
     *
     * @param channel
     * @param orderId
     * @return
     */
    public GroupEndorResponse endorSupply(String channel, String orderId) {
        Endor endor = endorMapper.queryEndorPaymentByOrderId(orderId);
        return endorSupply(channel, endor);
    }

    public GroupEndorResponse endorSupply(String channel, Endor endor) {
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        if (endor.finish()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "订单已完成");
        }
        String orderId = endor.getOrderId();
        /**
         * 如果批改单的状态为0，并且订单金额小于或等于0，则该单属于异常单，前端执行[批改生效]接口即可
         */
        GroupEndorResponse resp = new GroupEndorResponse();
        resp.setEndorsementNo(endor.getEndorsementNo());
        resp.setTotalPremium(endor.getAmount());
        resp.setOrderId(orderId);
        resp.setPolicyNo(endor.getPolicyNo());
        resp.setEndorStatus(endor.getStatus());

        if (0 == endor.getStatus() && endor.getAmount().compareTo(new BigDecimal(0)) > 0) {
            resp.setEndorStatus(0);
        }
        /**
         * 该状态也属于异常状态或属于临时状态，理论上支付完成后会在支付回调时执行生效操作
         */
        boolean toEffect =
                1 == endor.getStatus() || (0 == endor.getStatus() && endor.getAmount().compareTo(new BigDecimal(0)) <= 0);
        if (toEffect) {
            ChannelOrderService serice = ServiceConfig.getService(channel);
            boolean res = serice.endorEffect(orderId);
            log.info("[{}]批改生效完成,res:[{}]", orderId, res);
        }
        return resp;
    }

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private InvoiceRelationMapper invoiceRelationMapper;

    /**
     * 查询发票信息
     *
     * @param id
     * @return
     */
    public InvoiceVo queryInvoice(Integer id) {
        PolicyInvoice invoice = invoiceMapper.selectByPrimaryKey(id);
        if (invoice != null) {
            String data = invoice.getInvoiceInfo();
            if (StringUtils.isNotBlank(data)) {
                InvoiceVo rtn = JSON.parseObject(data, InvoiceVo.class);
                rtn.setInvoiceUrl(invoice.getInvoiceUrl());
                rtn.setInvoiceUrlList(appendInvoiceList(invoice));
                return rtn;
            }
        }
        throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
    }

    /**
     * 开票接口
     *
     * @param channel
     * @param data
     * @return
     */
    public InvoiceResponse openInvoice(String channel, InvoiceVo data) {
        /**
         * 1.数据校验
         */
        data.selfCheck();

        /**
         * 2.获取(校验)开票节点
         */
        String policyNo = data.getPolicyNo();
        String orderId = data.getOrderId();
        List<PolicyItem> policyItems = invoiceMapper.queryPolicyItem(orderId);
        if (CollectionUtils.isEmpty(policyItems)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前订单暂无可开票信息");
        }
        data.setPolicyItemList(policyItems);
        data.formatAmount();

        /**
         * 3.数据入库
         */
        PolicyService proxy = (PolicyService) AopContext.currentProxy();
        PolicyInvoice invoice = proxy.preDumpInvoice(channel, data);

        Integer id = invoice.getId();
        ChannelOrderService orderSerice = ServiceConfig.getService(channel);

        PolicyInvoice param = new PolicyInvoice();
        param.setId(id);
        InvoiceResponse response = null;
        try {
            response = orderSerice.openInvoice(data);
        } catch (Exception e) {
            log.warn("[众安]合并开票异常:{}", policyNo, e);
            param.setErrorMessage(e.getMessage());
            proxy.openInviceFail(param);
            throw e;
        }
        log.info("统一开票流程结果-{}-{}", policyNo, response);
        String invoiceNo = response.getInvoiceNo();
        param.setInvoiceNo(invoiceNo);
        param.setInvoiceCode(response.getInvoiceCode());
        param.setInvoiceUrl(response.getInvoiceUrl());
        param.setStatus(1);
        List<InvoiceRelation> relations = buildInvoiceRelation(id, policyItems);
        if (response != null) {
            param.setErrorMessage(JSON.toJSONString(response));
        }
        log.info("更新开票状态-{},{}", policyNo, id);
        proxy.openInviceSuccess(param, relations);
        /**
         * 电子发票需发送邮件
         */
        if ("2".equals(data.getInvoiceType())) {
            sendEmail4Invoice(response.getInvoiceUrl(), data,response);
        }
        return response;
    }

    /**
     * @param opType        A:新增；R:删除
     * @param orderId
     * @param policyNo
     * @param endorsementNo
     */
    public int mockInvoice(String opType, String orderId, String policyNo, String endorsementNo) {
        if (StringUtils.isBlank(endorsementNo)) {
            endorsementNo = "000";
        }

        if (Objects.equals(opType, "A")) {
            InvoiceRelation relation = new InvoiceRelation();
            relation.setInvoiceId(-1);
            relation.setOrderId(orderId);
            relation.setPolicyNo(policyNo);
            relation.setEndorsementNo(endorsementNo);
            relation.setCreateTime(new Date());
            List<InvoiceRelation> relationList = Collections.singletonList(relation);
            log.warn("开始模拟电子发票数据：{}", relationList);
            return invoiceRelationMapper.insertList(relationList);
        }

        if (Objects.equals(opType, "R")) {
            InvoiceRelation param = new InvoiceRelation();
            param.setOrderId(orderId);
            param.setPolicyNo(policyNo);
            param.setEndorsementNo(endorsementNo);
            param.setInvoiceId(-1);

            List<InvoiceRelation> data = invoiceRelationMapper.select(param);
            if (data != null) {
                log.warn("开始模拟电子发票数据：{}", data);
                for (InvoiceRelation e : data) {
                    invoiceRelationMapper.deleteByPrimaryKey(e.getId());
                }
                return data.size();
            }
            return 0;
        }
        throw new MSBizNormalException("-1", "功能维护中...");
    }

    @Autowired
    private JavaMailHelper mailHelper;

    private void sendEmail4Invoice(String invoiceUrl, InvoiceVo data,InvoiceResponse response) {
        String title = "【电子发票】" + data.getInvoiceTitle();
        String content = null;
        if(CollectionUtils.isEmpty(response.getInvoiceUrlList())){
            content = String.format("您好，%s公司申请的电子发票已开出，点击链接下载。链接地址: %s", data.getInvoiceTitle(), invoiceUrl);
        }else{
            if(response.getInvoiceUrlList().size() == 1){
                content = String.format("您好，%s公司申请的电子发票已开出，点击链接下载。链接地址: %s", data.getInvoiceTitle(), invoiceUrl);
            }else{
                String secondInvoiceUrl = response.getInvoiceUrlList().get(1);
                content = String.format("您好，%s公司申请的电子发票已开出，点击链接下载。链接地址: %s;%s", data.getInvoiceTitle(), invoiceUrl,secondInvoiceUrl);
            }
        }
        log.info("sendEmail4Invoice req= {}; email content= {}",JSON.toJSONString(data),content);
        String[] mailArr = new String[]{data.getRecipientEmail()};
        try {
            mailHelper.sendMail(title, mailArr, content, null, null);
        } catch (Exception e) {
            log.warn("电子发票发送邮件失败", e);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void openInviceSuccess(PolicyInvoice param, List<InvoiceRelation> relations) {
        invoiceMapper.updateByPrimaryKeySelective(param);
        invoiceRelationMapper.insertList(relations);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public PolicyInvoice preDumpInvoice(String channel, InvoiceVo data) {
        PolicyInvoice invoice = data.build(channel);
        int i = invoiceMapper.insertSelective(invoice);
        log.info("预存发票信息：{},{}", invoice, i);
        return invoice;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public int openInviceFail(PolicyInvoice param) {
        param.setStatus(-1);
        return invoiceMapper.updateByPrimaryKeySelective(param);
    }

    public List<InvoiceRelation> buildInvoiceRelation(Integer invoiceId, List<PolicyItem> policyItems) {
        List<InvoiceRelation> data = new ArrayList<>();
        Date now = new Date();
        for (PolicyItem item : policyItems) {
            InvoiceRelation relation = new InvoiceRelation();
            relation.setPolicyNo(item.getPolicyNo());
            String endorNo = StringUtils.isBlank(item.getEndorsementNo()) ? "000" : item.getEndorsementNo();
            relation.setEndorsementNo(endorNo);
            relation.setOrderId(item.getOrderId());
            relation.setInvoiceId(invoiceId);
            relation.setCreateTime(now);
            data.add(relation);
        }
        return data;
    }

    public List<PolicyItem> invoiceItemList(String orderId) {
        return invoiceMapper.queryPolicyItem(orderId);
    }

    public PayNotice queryPayNotice(String orderId, String type) {
        PayNotice notice = null;
        if (Objects.equals(type, "0")) {
            notice = wxOrderMapper.queryPayNotice(orderId);
        } else {
            notice = endorMapper.queryPayNotice(orderId);
        }
        return notice;
    }

    /**
     * 提交线下支付材料
     *
     * @param data
     */
    public String submitOfflinePay(String channel, OfflinePayDTO data) {
        if (StringUtils.isBlank(data.getOrderId())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单号不能为空");
        }
        String orderId = data.getOrderId();
        /**
         * 批改数据暂时不知道怎么处理
         */
        if (!data.isCorrect()) {
            orderManageService.checkUnpayOrderInsuredProductBuyLimit(orderId);
        }

        ChannelOrderService serice = ServiceConfig.getService(channel);
        return serice.submitOfflinePay(data);
    }

    /**
     * 替换人
     *
     * @param channel
     * @param request
     * @return
     */
    public MemberChange memberChange(String channel, GroupEndorsement request) {
        fixInsuredInfo(request.getInsuredList());

        ChannelOrderService service = ServiceConfig.getService(channel);
        return service.memberChange(request);
    }

    public GroupOrderVo groupOrder(String orderId, String endorId) {
        if (StringUtils.isNotBlank(endorId)) {
            return groupCorrectOrder(endorId);
        }
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (order == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT);
        }
        GroupOrderVo vo = new GroupOrderVo();
        vo.setTotalAmount(order.getTotalAmount());
        vo.setOrderId(orderId);
        vo.setEffectiveTime(DateUtils.format(order.getStartTime()));
        vo.setSubmitTime(DateUtils.format(order.getSubmitTime()));
        vo.setChannel(order.getChannel());
        return vo;
    }

    public GroupOrderVo groupCorrectOrder(String endorId) {
        Endor endor = endorMapper.queryEndorPaymentByOrderId(endorId);
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT);
        }
        GroupOrderVo vo = new GroupOrderVo();
        vo.setOrderId(endor.getRawOrderId());
        vo.setEndorId(endorId);
        vo.setChannel(endor.getChannel());
        vo.setEffectiveTime(endor.getEffectiveTime());
        vo.setSubmitTime(DateUtils.format(endor.getCreateTime()));
        vo.setTotalAmount(endor.getAmount());
        return vo;
    }

    public GroupEndorResponse cancel(String channel, GroupRevokeVo req) {

        ChannelOrderService serice = ServiceConfig.getService(channel);
        return serice.cancel(req);
    }


    /**
     * 查询发票信息
     *
     * @param policyNo
     * @return
     */
    public EmpInvoiceVo queryInvoiceByPolicyNo(String policyNo) {


        if (StringUtil.isBlank(policyNo)) {
            return EmpInvoiceVo.builder().resultCode(EmpInvoiceVo.POLICY_NOT_EXIST).errorMessage("保单号不存在").build();
        }
        InvoiceOrderDTO orderDTO = wxOrderMapper.getOneInvoiceOrder(policyNo);
        if (orderDTO == null) {
            return EmpInvoiceVo.builder().resultCode(EmpInvoiceVo.POLICY_NOT_EXIST).errorMessage("保单号不存在").build();
        }
        EmpInvoiceVo vo = EmpInvoiceVo.builder()
                .applicantName(orderDTO.getApplicantName())
                .policyNo(policyNo)
                .productName(orderDTO.getProductName())
                .totalAmount(orderDTO.getTotalAmount()).build();


        PolicyInvoice query = new PolicyInvoice();
        query.setPolicyNo(policyNo);
        List<PolicyInvoice> invoiceList = invoiceMapper.select(query);
        if (CollectionUtils.isEmpty(invoiceList)) {
            vo.setResultCode(EmpInvoiceVo.INVOICE_NOT_EXIST);
            vo.setErrorMessage("开票信息不存在");
            return vo;
        }
        PolicyInvoice invoice = invoiceList.get(0);
        vo.setWaybillNo(invoice.getWaybillNo());
        vo.setResultCode(EmpInvoiceVo.SUCCESS_CODE);
        vo.setInvoiceCode(invoice.getInvoiceCode());
        vo.setInvoiceDate(invoice.getInvoiceDate());
        vo.setInvoiceStatus(invoice.getInvoiceStatus());
        vo.setInvoiceType(invoice.getInvoiceType());
        vo.setMailingTime(invoice.getMailingTime());
        vo.setInvoiceNo(invoice.getInvoiceNo());

        return vo;
    }


    /**
     * 1.验证产品是否在维护
     * 2.设置每一个产品计划的结算佣金
     *
     * @param holder
     */
    public void setCommissionPublic(Integer productId, Integer planId, ApplyHolder holder) {
        Date now = new Date();
        Map<Integer, SmCommissionSettingVO> commissionMap = getCommissionMap(productId, now);

        /**
         * 设置佣金比例
         */
        SmCommissionSettingVO settingVo = commissionMap.get(planId);
        if (settingVo == null) {
            throw new MSBizNormalException(COMMISSION_ERROR_201014.getCode(), COMMISSION_ERROR_201014.getMsg());
        }
        holder.setCommission(settingVo);
    }

    private List<String> appendInvoiceList(PolicyInvoice invoice){
        String json = invoice.getErrorMessage();
        InvoiceResponse invoiceResponse = JSON.parseObject(json,InvoiceResponse.class);
        List<String> invoiceUrlList = null;
        if(CollectionUtils.isEmpty(invoiceResponse.getInvoiceUrlList())){
            invoiceUrlList = Lists.newArrayList();
            invoiceUrlList.add(invoice.getInvoiceUrl());
            return invoiceUrlList;
        }else{
            return invoiceResponse.getInvoiceUrlList();
        }
    }
}
