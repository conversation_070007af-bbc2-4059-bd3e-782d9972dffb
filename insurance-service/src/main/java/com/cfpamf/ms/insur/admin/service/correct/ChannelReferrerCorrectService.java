package com.cfpamf.ms.insur.admin.service.correct;

import com.beust.jcommander.internal.Lists;
import com.beust.jcommander.internal.Sets;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.UserPostMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.correct.CorrectProject;
import com.cfpamf.ms.insur.admin.enums.correct.EnumWhaleCorrectProject;
import com.cfpamf.ms.insur.admin.enums.order.OrderSourceEnum;
import com.cfpamf.ms.insur.admin.external.whale.enums.EnumWhalePolicyProductType;
import com.cfpamf.ms.insur.admin.external.whale.model.OpenPreservationCustomerManagerChangeVo;
import com.cfpamf.ms.insur.admin.external.whale.model.PreservationDetail;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmSaveOrderCorrectDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserNameVO;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderToCorrectVO;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.cfpamf.ms.insur.admin.util.UtilDate;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeItemDetailVo;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeItemVo;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderInsuredBaseInfoVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicyRecommenderCheckInfo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PreservationCheckResultDetail;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PreservationCheckResultVo;
import com.google.common.collect.Maps;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/3/8 9:26 上午
 * @Version 1.0
 */
@Service
@Slf4j
public class ChannelReferrerCorrectService extends BaseCorrectService<PreservationDetail> {
    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private SmOrderInsuredMapper insuredMapper;

    @Autowired
    private SmOrderManageService orderManageService;

    @Autowired
    private AuthUserMapper authUserMapper;

    @Autowired
    private UserPostMapper userPostMapper;

    @Autowired
    private UserPostService userPostService;

    @Override
    public PreservationDetail queryCorrectContextParams(String preservationCode) {
        return super.callXjPreservationDetail(preservationCode);
    }

    @Override
    public void doCorrect(PreservationDetail preservationDetail) {
        log.info("团险分单层渠道推荐人变更：{}",preservationDetail);

        String policyCode = preservationDetail.getPolicyCode();
        if (StringUtils.isEmpty(policyCode)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "保单号为空");
        }
        ChannelRecommenderChangeVo changeVo = preservationDetail.getChannelRecommenderChangeVo();
        if(Objects.isNull(changeVo)){
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "分单层批改信息为空");
        }
        List<ChannelRecommenderChangeItemVo> details = changeVo.getChannelRecommenderChangeItemVos();
        List<String> addEndorsementNos = Lists.newArrayList();
        List<String> subEndorsementNos = Lists.newArrayList();
        boolean containNewPolicy = false;
        Set<String> subPersonSet = Sets.newHashSet();
        //增员
        for(ChannelRecommenderChangeItemVo itemVo : details){
            List<ChannelRecommenderChangeItemDetailVo> itemDetailVos = itemVo.getItemDetailVos().stream().filter(o->Objects.equals(o.getOperationType(),"new")||Objects.equals(o.getOperationType(),"add")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(itemDetailVos)){
                if(StringUtils.isNotBlank(itemVo.getEndorsementNo())) {
                    addEndorsementNos.add(itemVo.getEndorsementNo());
                }else{
                    containNewPolicy = true;
                }
            }
            List<ChannelRecommenderChangeItemDetailVo> detailVos = itemVo.getItemDetailVos().stream().filter(o->Objects.equals(o.getOperationType(),"batch")).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(detailVos)){
                subEndorsementNos.add(itemVo.getEndorsementNo());
                List<ChannelRecommenderChangeItemDetailVo> subDetails = Lists.newArrayList();
                for(ChannelRecommenderChangeItemDetailVo detailVo : detailVos){
                    subPersonSet.add(itemVo.getEndorsementNo()+"-"+detailVo.getInsuredIdNo());
                }
            }
        }
        log.info("增员批单号={}",addEndorsementNos);
        log.info("减员员批单号={}",subEndorsementNos);
        log.info("新契约是否存在={}",containNewPolicy);
        List<OrderInsuredBaseInfoVo> insuredBaseInfos = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(addEndorsementNos)){
            List<OrderInsuredBaseInfoVo> insureds1 = insuredMapper.listGroupInsuredByThPolicyNoAndThEndorsementNo(policyCode,addEndorsementNos,SmConstants.POLICY_STATUS_SUCCESS);
            insuredBaseInfos.addAll(insureds1);
        }
        log.info("insuredBaseInfos={}",insuredBaseInfos);
        if(containNewPolicy){
            List<OrderInsuredBaseInfoVo> insureds2 = insuredMapper.listNewGroupInsuredByThPolicyNo(policyCode);
            insuredBaseInfos.addAll(insureds2);
        }
        log.info("insuredBaseInfos={}",insuredBaseInfos);
        if(CollectionUtils.isNotEmpty(subEndorsementNos)){
            //并不是所有的
            List<OrderInsuredBaseInfoVo> insureds3 = insuredMapper.listGroupInsuredByThPolicyNoAndThEndorsementNo(policyCode,subEndorsementNos,SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            insureds3 = insureds3.stream().filter(o->subPersonSet.contains(o.getEndorsementNo()+"-"+o.getIdNumber())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(insureds3)){
                insuredBaseInfos.addAll(insureds3);
            }
        }
        log.info("insuredBaseInfos={}",insuredBaseInfos);

        String afterManager = changeVo.getAfterChannelRecommender();
        String afterManagerOrgCode = changeVo.getAfterCustomerManagerChannelOrgCode();
        String correctedRecommendMainJobNumber = null;
        String correctedRecommendOrgCode = null;
        if(StringUtils.isNotBlank(afterManager)) {
            WxUserVO wxUserVO = authUserMapper.getUserByUserId(afterManager);
            if (Objects.isNull(wxUserVO)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("变更后的客户经理或机构不存在-%s", afterManager));
            }
            List<UserPost> afterUserPost = userPostService.listUserPostByJobNumber(afterManager);
            if (CollectionUtils.isEmpty(afterUserPost)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("客户经理-%s机构无归属职位-%s", afterManager, afterManagerOrgCode));
            }
            UserPost afterMainPost = afterUserPost.stream()
                    .filter(x -> Objects.equals(x.getOrgCode(), afterManagerOrgCode))
                    .findAny()
                    .orElseGet(
                            () -> afterUserPost.stream()
                                    .filter(p -> Objects.equals(p.getServiceType(), 0))
                                    .findFirst()
                                    .orElse(null)
                    );

            if (Objects.isNull(afterMainPost)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("客户经理-%s机构无归属职位-%s", afterManager, afterManagerOrgCode));
            }
            correctedRecommendMainJobNumber =wxUserVO.getMainJobNumber();
            correctedRecommendOrgCode = afterMainPost.getOrgCode();
        }
        //排查记录中推荐人已经为变更后的记录
        insuredBaseInfos = insuredBaseInfos.stream().filter(o->!Objects.equals(afterManager,o.getRecommendId())).collect(Collectors.toList());
        Set<String> fhOrderIdSet = Sets.newHashSet();
        for(OrderInsuredBaseInfoVo insureds : insuredBaseInfos){
            if(fhOrderIdSet.contains(insureds.getFhOrderId())){
                continue;
            }
            log.info("订单id{}推荐人由{}变更为{}",insureds.getFhOrderId(),insureds.getRecommendId(),afterManager);
            SmOrderCorrectDTO dto = new SmOrderCorrectDTO();
            dto.setFieldCode(CorrectProject.RECOMMEND_NAME.getCode());
            dto.setOldValue(insureds.getRecommendId());
            dto.setNewValue(afterManager);

            SmOrderToCorrectVO toCorrectVO = new SmOrderToCorrectVO();
            toCorrectVO.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);

            toCorrectVO.setRecommendId(insureds.getRecommendId());
            toCorrectVO.setPaymentTime(UtilDate.localDateTimeToDate(insureds.getPaymentTime()));
            toCorrectVO.setPolicyNo(insureds.getPolicyNo());
            toCorrectVO.setFhOrderId(insureds.getFhOrderId());

            dto.setNewRecommendMainJobNumber(correctedRecommendMainJobNumber);
            dto.setNewRecommendOrgCode(correctedRecommendOrgCode);
            dto.setPolicyNo(insureds.getPolicyNo());
            dto.setFhOrderId(insureds.getFhOrderId());

            orderManageService.correctRecommendV1(dto, toCorrectVO);
            orderMapper.updateOrderErrorRecommendInfo(dto);
            orderMapper.updateOrderErrorCommissionInfo(dto);
            //更新新的佣金表
            if (StringUtils.isEmpty(dto.getOldValue())) {
                dto.setOldValue(null);
            }
            orderManageService.updateNewCommissionInfo(dto);
            SmSaveOrderCorrectDTO cDto = OrderConvertor.buildSmSaveOrderCorrectDTO(dto, toCorrectVO);
            cDto.setUpdateBy("SYSTERM");
            orderMapper.insertOrderCorrect(cDto);
        }
    }

    /**
     * 批量校验:只校验外层数据，结合当前业务场景，批量导入时只支持保单全量变更数据
     * @param param 被保人变更集合
     * @return 校验结果
     */
    public List<PreservationCheckResultVo> preCheck(List<ChannelRecommenderChangeVo> param) {
        if(CollectionUtils.isEmpty(param)){
            return Collections.emptyList();
        }
        Set<String> afterManager = param.stream()
                .filter(entry->StringUtils.isNotBlank(entry.getAfterChannelRecommender()))
                .map(ChannelRecommenderChangeVo::getAfterChannelRecommender)
                .collect(Collectors.toSet());
         Map<String,WxUserVO> userMap = new HashMap<>();
         Map<String,List<UserPost>> userPostMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(afterManager)){
            List<WxUserVO> userList = authUserMapper.queryUserList(afterManager);
            List<UserPost> userPostList = userPostService.listAllUserPostByJobNumberList(afterManager);

            userMap = LambdaUtils.safeToMap(userList,WxUserVO::getUserId);
            userPostMap = LambdaUtils.groupBy(userPostList,UserPost::getJobNumber);
        }
        List<PreservationCheckResultVo> data = new ArrayList<>();
        for(ChannelRecommenderChangeVo entry:param){
            String policyNo = entry.getPolicyCode();
            String afterManagerCode = entry.getAfterChannelRecommender();
            String afterChannelCode = entry.getAfterChannelCode();
            String afterManagerOrgCode = entry.getAfterCustomerManagerChannelOrgCode();
            if(StringUtil.isBlank(policyNo)){
                data.add(PreservationCheckResultVo.err("保单号不能为空"));
                continue;
            }
            if(StringUtils.isBlank(afterManagerCode)){
                data.add(PreservationCheckResultVo.success());
                continue;
            }
            if (StringUtils.isNotBlank(afterChannelCode)) {
                if (!Objects.equals(afterChannelCode, OrderSourceEnum.ZHNX.getCode())) {
                    data.add(PreservationCheckResultVo.err(String.format("变更后渠道不为中和农信-%s-%s", afterChannelCode, policyNo)));
                    continue;
                }
            }
            WxUserVO manager = userMap.get(afterManager);
            if (Objects.isNull(manager)) {
                data.add(PreservationCheckResultVo.err(String.format("变更后的客户经理在中和农信系统中不存在-%s", afterManager)));
                continue;
            }
            List<UserPost> afterUserPost = userPostMap.get(afterManager);
            if (CollectionUtils.isEmpty(afterUserPost)) {
                data.add(PreservationCheckResultVo.err(String.format("客户经理-%s的所属机构无归属职位-%s", afterManager, afterManagerOrgCode)));
                continue;
            }
            UserPost afterMainPost = afterUserPost.stream()
                    .filter(x -> Objects.equals(x.getOrgCode(), afterManagerOrgCode))
                    .findAny()
                    .orElseGet(() -> afterUserPost.stream()
                                    .filter(p -> Objects.equals(p.getServiceType(), 0))
                                    .findFirst()
                                    .orElse(null)
                    );
            if (Objects.isNull(afterMainPost)) {
                data.add(PreservationCheckResultVo.err(String.format("客户经理-%s机构无归属职位-%s", afterManager, afterManagerOrgCode)));
                continue;
            }
        }
        return data;
    }

    /**
     * 团险分单层推荐人变更-前置规则校验
     * @param param 被保人变更集合
     * @return 校验结果
     */
    public PreservationCheckResultVo preCheck(ChannelRecommenderChangeVo param) {
        String policyNo = param.getPolicyCode();
        List<ChannelRecommenderChangeItemVo> channelRecommenderChangeItemVos = param.getChannelRecommenderChangeItemVos();
        if(StringUtil.isBlank(policyNo)){
            return PreservationCheckResultVo.err("保单号不能为空");
        }
        String afterManager = param.getAfterChannelRecommender();
        String afterManagerChannel = param.getAfterChannelCode();
        String afterManagerOrgCode = param.getAfterCustomerManagerChannelOrgCode();
        //1. 变更后的渠道推荐人必须为中和农信渠道
        if(StringUtils.isNotBlank(afterManager)) {
            if (!Objects.equals(afterManagerChannel, OrderSourceEnum.ZHNX.getCode())) {
                return PreservationCheckResultVo.err(String.format("变更后渠道不为中和农信-%s-%s", afterManagerChannel, policyNo));
            }
            //2. 变更后的渠道推荐人在农保系统是否存在
            WxUserVO wxUserVO = authUserMapper.getUserByUserId(afterManager);
            if (Objects.isNull(wxUserVO)) {
                return PreservationCheckResultVo.err(String.format("变更后的客户经理在中和农信系统中不存在-%s", afterManager));
            }
            //3. 变更后的渠道推荐人的主职工号在农保系统是否存在
            List<UserPost> afterUserPost = userPostService.listUserPostByJobNumber(afterManager);
            if (CollectionUtils.isEmpty(afterUserPost)) {
                return PreservationCheckResultVo.err(String.format("客户经理-%s的所属机构无归属职位-%s", afterManager, afterManagerOrgCode));
            }
            UserPost afterMainPost = afterUserPost.stream()
                    .filter(x -> Objects.equals(x.getOrgCode(), afterManagerOrgCode))
                    .findAny()
                    .orElseGet(
                            () -> afterUserPost.stream()
                                    .filter(p -> Objects.equals(p.getServiceType(), 0))
                                    .findFirst()
                                    .orElse(null)
                    );
            if (Objects.isNull(afterMainPost)) {
                return PreservationCheckResultVo.err(String.format("客户经理-%s机构无归属职位-%s", afterManager, afterManagerOrgCode));
            }
        }

        //4. 分单被保人推荐人变更规则@zhangjian
        if(CollectionUtils.isNotEmpty(channelRecommenderChangeItemVos)) {
            List<ChannelRecommenderChangeItemVo> endorsementList = channelRecommenderChangeItemVos.stream().filter(o -> StringUtils.isNotBlank(o.getEndorsementNo())).collect(Collectors.toList());
            List<String> endorsementNos = endorsementList.stream().map(ChannelRecommenderChangeItemVo::getEndorsementNo).collect(Collectors.toList());
            List<PreservationCheckResultDetail> resultDetails = Lists.newArrayList();

            log.info("需要验证的批单列表:{}", endorsementNos);
            if (CollectionUtils.isNotEmpty(endorsementNos)) {
                List<OrderInsuredBaseInfoVo> insureds = insuredMapper.listGroupInsuredByThPolicyNoAndThEndorsementNo(policyNo, endorsementNos, null);
                insureds = insureds.stream().filter(o -> Objects.equals(o.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)).collect(Collectors.toList());
                resultDetails.addAll(checkResult(endorsementList, insureds));
            }

            Optional<ChannelRecommenderChangeItemVo> opt = channelRecommenderChangeItemVos.stream().filter(o -> StringUtils.isBlank(o.getEndorsementNo())).findFirst();
            if (opt.isPresent()) {
                log.info("需要验证的原始保单列表:{}", policyNo);
                List<OrderInsuredBaseInfoVo> insureds = insuredMapper.listNewGroupInsuredByThPolicyNo(param.getPolicyCode());
                insureds = insureds.stream().filter(o -> Objects.equals(o.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)).collect(Collectors.toList());
                resultDetails.addAll(checkResult(Arrays.asList(opt.get()), insureds));
            }
            //完善校验结果
            return buildCheckResult(param,resultDetails);
        }
        return PreservationCheckResultVo.success();
    }

    /**
     * 组装分单初始渠道推荐人校验结果对象
     * @param resultDetailList 校验明细列表
     * @return 校验结果
     */
    private PreservationCheckResultVo buildCheckResult(ChannelRecommenderChangeVo param,List<PreservationCheckResultDetail> resultDetailList) {
        if(CollectionUtils.isEmpty(resultDetailList)){
            return PreservationCheckResultVo.success();
        }
        List<PreservationCheckResultDetail.PreservationLosePerson> errInsuredList = resultDetailList.stream()
                .map(PreservationCheckResultDetail::getLosePersonList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(x->x.stream())
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(errInsuredList)){
            return PreservationCheckResultVo.success();
        }
        List<String> recommendIdList = new ArrayList<>();
        recommendIdList.add(param.getAfterChannelRecommender());
        for(PreservationCheckResultDetail.PreservationLosePerson entry:errInsuredList){
            recommendIdList.add(entry.getWhaleRecommender());
            recommendIdList.add(entry.getChongHoRecommender());
        }
        List<AuthUserNameVO> userList = authUserMapper.listUserNameByUserId(recommendIdList);
        Map<String,AuthUserNameVO> userMap = LambdaUtils.safeToMap(userList,AuthUserNameVO::getUserId);
        Boolean checkOk = true;
        PreservationCheckResultVo vo = PreservationCheckResultVo.success();

        List<PolicyRecommenderCheckInfo> policyRecommenderCheckVo = new ArrayList<>();
        for(PreservationCheckResultDetail.PreservationLosePerson entry:errInsuredList){
            PolicyRecommenderCheckInfo checkVo = PolicyRecommenderCheckInfo.builder().build();
            checkVo.setPolicyNo(param.getPolicyCode());
            BeanUtils.copyProperties(entry,checkVo);
            String whaleRecommender = entry.getWhaleRecommender();
            AuthUserNameVO whaleAuthUser = userMap.get(whaleRecommender);
            if(whaleAuthUser!=null){
                checkVo.setWhaleRecommenderName(whaleAuthUser.getUserName());
            }
            String chongHoRecommender = entry.getChongHoRecommender();
            AuthUserNameVO chongHoAuthUser = userMap.get(chongHoRecommender);
            if(chongHoAuthUser!=null){
                checkVo.setChongHoRecommenderName(chongHoAuthUser.getUserName());
            }
            checkVo.setAfterRecommender(param.getAfterChannelRecommender());
            AuthUserNameVO afterAuthUser = userMap.get(param.getAfterChannelRecommender());
            if(afterAuthUser!=null){
                checkVo.setAfterRecommenderName(afterAuthUser.getUserName());
            }
            policyRecommenderCheckVo.add(checkVo);
            if(!Objects.equals(entry.getType(),1)){
                checkOk = false;
            }
        }
        vo.setPolicyRecommenderCheckVo(policyRecommenderCheckVo);
        if(!checkOk){
            vo.setCode(PreservationCheckResultVo.ERR_CODE);
            vo.setMessage("当前保全无法完成，批改单或者新契约的分单数据必须同时变更，小鲸和农保系统保全数据不一致");
        }
        return vo;
    }

    /**
     * 团险分单层推荐人变更逻辑校验规则 @zhangjian
     * @param whaleInsuredList 变更的分单信息
     * @param chongHoInsuredList 农保被保人信息
     * @return 校验规则
     */
    private List<PreservationCheckResultDetail> checkResult(List<ChannelRecommenderChangeItemVo> whaleInsuredList,List<OrderInsuredBaseInfoVo> chongHoInsuredList){

        Map<String,PreservationCheckResultDetail> resultDetailMap = Maps.newHashMap();
        Map<String,List<OrderInsuredBaseInfoVo>> insuredMap = LambdaUtils.groupBy(chongHoInsuredList,OrderInsuredBaseInfoVo::getEndorsementNo);

        //对比小鲸分单和农保的分单信息
        whaleInsuredList.stream().forEach(o->{
            log.info("ChannelRecommenderChangeItemVo={}",o);
            List<ChannelRecommenderChangeItemDetailVo> itemDetailVos = o.getItemDetailVos();

            List<OrderInsuredBaseInfoVo> correctInsuredList = insuredMap.get(o.getEndorsementNo());
            List<OrderInsuredBaseInfoVo> baseInfoVos = correctInsuredList!=null?correctInsuredList:Collections.EMPTY_LIST;

            Map<String,OrderInsuredBaseInfoVo> chongHoInsuredMap = LambdaUtils.safeToMap(baseInfoVos,entry->{
                return entry.getIdNumber().toLowerCase();
            });
            List<PreservationCheckResultDetail.PreservationLosePerson> losePersonList = Lists.newArrayList();

            itemDetailVos.stream().forEach(p->{
                PreservationCheckResultDetail.PreservationLosePerson person = null;
                String idCard = p.getInsuredIdNo().toLowerCase();
                OrderInsuredBaseInfoVo insured = chongHoInsuredMap.get(idCard);
                if(insured!=null){
                    //农保的推荐人与小鲸推荐人不一致的被保人信息
                    if(!Objects.equals(insured.getRecommendId(),p.getBeforeChannelRecommender())) {
                        person = PreservationCheckResultDetail.PreservationLosePerson.builder()
                                .idNumber(p.getInsuredIdNo())
                                .insuredName(p.getInsuredIdName())
                                .chongHoRecommender(insured.getRecommendId())
                                .whaleRecommender(p.getBeforeChannelRecommender())
                                .message("该被保人在农保和小鲸系统中的推荐人不一致，请确认是否需要变更")
                                .type(1).build();
                    }
                }else{
                    person = PreservationCheckResultDetail.PreservationLosePerson.builder()
                            .idNumber(p.getInsuredIdNo())
                            .insuredName(p.getInsuredIdName())
                            .message("该被保人在此次变更列表中不存在，批单或者新契约的分单数据必须同时变更")
                            .type(2).build();
                }
                if(person!=null){
                    losePersonList.add(person);
                }
            });
            if(CollectionUtils.isNotEmpty(losePersonList)){
                PreservationCheckResultDetail detail = new PreservationCheckResultDetail();
                detail.setEndorsementNo(o.getEndorsementNo());
                detail.setLosePersonList(losePersonList);
                resultDetailMap.put(o.getEndorsementNo(),detail);
            }
        });
        //找出农保有，小鲸缺失数据
        insuredMap.forEach((k,v)->{
            Optional<ChannelRecommenderChangeItemVo> opt = whaleInsuredList.stream().filter(o->Objects.equals(k,o.getEndorsementNo())).findFirst();

            if(opt.isPresent()){
                v.stream().forEach(z->{
                    String idCard = z.getIdNumber().toLowerCase();
                    Optional<ChannelRecommenderChangeItemDetailVo> itemDetailVoOpt = opt.get().getItemDetailVos().stream().filter(m->Objects.equals(idCard,m.getInsuredIdNo().toLowerCase())).findFirst();
                    if(!itemDetailVoOpt.isPresent()){
                        PreservationCheckResultDetail.PreservationLosePerson person = PreservationCheckResultDetail.PreservationLosePerson.builder()
                                .idNumber(z.getIdNumber())
                                .insuredName(z.getInsuredName())
                                .message("该被保人在此次变更列表中不存在，批单或者新契约的分单数据必须同时变更")
                                .type(0).build();
                        if(resultDetailMap.get(k)!=null){
                            resultDetailMap.get(k).getLosePersonList().add(person);
                        }else{
                            PreservationCheckResultDetail resultDetail = new PreservationCheckResultDetail();
                            List<PreservationCheckResultDetail.PreservationLosePerson> losePersonList = Lists.newArrayList();
                            losePersonList.add(person);
                            resultDetail.setEndorsementNo(k);
                            resultDetail.setLosePersonList(losePersonList);
                            resultDetailMap.put(k,resultDetail);
                        }
                    }
                });
            }else{
                PreservationCheckResultDetail resultDetail = new PreservationCheckResultDetail();
                List<PreservationCheckResultDetail.PreservationLosePerson> losePersonList = Lists.newArrayList();
                resultDetail.setEndorsementNo(k);
                resultDetail.setLosePersonList(losePersonList);
                v.stream().forEach(z->{
                    PreservationCheckResultDetail.PreservationLosePerson person = PreservationCheckResultDetail.PreservationLosePerson.builder()
                            .idNumber(z.getIdNumber())
                            .insuredName(z.getInsuredName())
                            .message("该被保人在此次变更列表中不存在，批单或者新契约的分单数据必须同时变更")
                            .type(0).build();
                    losePersonList.add(person);
                });
                resultDetailMap.put(k,resultDetail);
            }
        });
        return resultDetailMap.values().stream().collect(Collectors.toList());
    }

    @Override
    public String correctProject() {
        return EnumWhaleCorrectProject.CHANNEL_REFERRER.getCode();
    }
}
