package com.cfpamf.ms.insur.admin.job.renewal;

import cn.hutool.core.date.StopWatch;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.product.ProductAttrEnum;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumRenewalIntention;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.SmOrderRenewalFollowDto;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.renewal.InsuranceRenewPo;
import com.cfpamf.ms.insur.admin.renewal.service.InsuranceRenewService;
import com.cfpamf.ms.insur.admin.renewal.service.PolicyRenewalBaseInfoService;
import com.cfpamf.ms.insur.admin.renewal.service.SmOrderRenewalFollowService;
import com.cfpamf.ms.insur.admin.renewal.vo.TransferPolicyVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/19 13:53
 */
@Slf4j
@Component
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class InsuranceRenewJobHandler {

    InsuranceRenewService insuranceRenewService;

    SmOrderRenewalFollowService smOrderRenewalFollowService;

    TransactionTemplate transactionTemplate;


    PolicyRenewalBaseInfoService policyRenewalBaseInfoService;

    SmOrderInsuredMapper smOrderInsuredMapper;

    /**
     * 处理客户数据
     */
    @XxlJob("insurance_renew")
    public void handlerInsurance() {

        insuranceRenewService.initRenewPolicy();
        try{
            log.info("开始生成基表待办数据");
            policyRenewalBaseInfoService.initRenewalBaseInfo();
            log.info("生成基表待办数据结束");
        }catch (Exception e){
            log.warn("基表待办数据生成异常",e);
        }
    }

    /**
     * 处理续保基表数据
     */
    @XxlJob("policy_renewal_base_info_handler")
    public void policyRenewalBaseInfoHandler() {
        policyRenewalBaseInfoService.initRenewalBaseInfo();
    }

    /**
     * 转投保单处理
     */
    @XxlJob("insurance_renew_transfer")
    public void handlerTransfer() {
        smOrderRenewalFollowService.transferPolicyHandler();
    }

    /**
     * 自动匹配转投保单
     */
    @XxlJob("autoMatcherTransferHandler")
    public void autoMatcherTransferHandler() {
        log.info("开始自动处理转投保保单");
        StopWatch stopWatch = new StopWatch();
        // 设置开始
        stopWatch.start();
        List<InsuranceRenewPo> waitList = insuranceRenewService.listWaitInsuranceRenewalV2(1000);
        if(CollectionUtils.isEmpty(waitList)){
            return ;
        }
        for(InsuranceRenewPo po:waitList){
            try {
                log.info("处理保单{}的转保操作",po.getOldPolicyNo());
                List<SmOrderInsured> insuredLst = smOrderInsuredMapper.selectByPolicyNo(po.getOldPolicyNo());
                if(CollectionUtils.isEmpty(insuredLst)){
                    throw new MSBizNormalException("1000001","保单"+po.getOldPolicyNo()+"被保人列表不存在");
                }
                String oldOrderId = insuredLst.get(0).getFhOrderId();
                po.setOldOrderId(oldOrderId);
                //获取可转投保保单
                List<TransferPolicyVo> transferPolicyVos = insuranceRenewService.listAutoQueryTransferPolicyList(po);

                if(CollectionUtils.isNotEmpty(transferPolicyVos)){
                    log.info("");
                    TransferPolicyVo vo = matcherTransferPolicy(transferPolicyVos);
                    if(Objects.nonNull(vo)) {
                        SmOrderRenewalFollowDto dto = toSmOrderRenewalFollowDto(po,vo);

                        Boolean result = transactionTemplate.execute(new TransactionCallback<Boolean>() {
                            @Override
                            public Boolean doInTransaction(TransactionStatus transactionStatus) {
                                try {
                                    smOrderRenewalFollowService.saveRenewalFollowRecord(dto);
                                    policyRenewalBaseInfoService.updateTransferBaseInfo(vo,po.getOldPolicyNo());
                                    return Boolean.TRUE;
                                } catch (Exception e){
                                    //回滚
                                    log.warn("转保信息保存失败",e);
                                    transactionStatus.setRollbackOnly();
                                    return Boolean.FALSE;
                                }
                            }
                        });
                        if(!result){
                            throw new RuntimeException("保单"+po.getOldPolicyNo()+"转保异常");
                        }
                    }
                }
                policyRenewalBaseInfoService.modifyUpdateTimeByOldPolicyNo(po.getOldPolicyNo());
                insuranceRenewService.modifyUpdateTimeByOldPolicyNo(po.getOldPolicyNo());
            }catch (Exception e){
                log.info("转投保异常.",e);
                policyRenewalBaseInfoService.modifyUpdateTimeByOldPolicyNo(po.getOldPolicyNo());
                insuranceRenewService.modifyUpdateTimeByOldPolicyNo(po.getOldPolicyNo());

            }
        }
        // 设置结束
        stopWatch.stop();
        // 获取执行毫秒值
        long millis = stopWatch.getTotalTimeMillis();
        XxlJobHelper.handleSuccess("自动处理转投保保...耗时=" + millis);
        log.info("完成自动处理转投保保单,耗时={}",millis);
    }

    private TransferPolicyVo matcherTransferPolicy(List<TransferPolicyVo> transferPolicyVos){
        List<String> policyNos = transferPolicyVos.stream().map(TransferPolicyVo::getPolicyNo).collect(Collectors.toList());
        List<String> existPolicy = smOrderRenewalFollowService.listExistTransferPolicyNo(policyNos);
        transferPolicyVos = transferPolicyVos.stream().filter(c->!existPolicy.contains(c.getPolicyNo())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(transferPolicyVos)){
            return null;
        }
        return transferPolicyVos.stream().min(Comparator.comparing(TransferPolicyVo::getPaymentTime)).get();
    }

    private SmOrderRenewalFollowDto toSmOrderRenewalFollowDto(InsuranceRenewPo po,TransferPolicyVo vo){
        SmOrderRenewalFollowDto dto = new SmOrderRenewalFollowDto();
        dto.setFollowPeople("system");

        dto.setFollowTime(LocalDateTime.now());
        dto.setOrderId(po.getOldOrderId());
        dto.setPolicyNo(po.getOldPolicyNo());
        dto.setProductId(vo.getProductId());
        dto.setPlanId(vo.getPlanId());
        if(Objects.equals(po.getProductAttrCode(), EnumProductAttr.PERSON.getCode())){
            dto.setIdNumber(po.getInsuredIdNumber());
        }else {
            dto.setIdNumber(po.getApplicantIdNumber());
        }
        dto.setIntention(EnumRenewalIntention.TRANSFER.getCode());
        dto.setIntentionName(EnumRenewalIntention.TRANSFER.getDesc());
        dto.setTransferOrderId(vo.getFhOrderId());
        dto.setTransferPolicyNo(vo.getPolicyNo());
        return dto;
    }


}
