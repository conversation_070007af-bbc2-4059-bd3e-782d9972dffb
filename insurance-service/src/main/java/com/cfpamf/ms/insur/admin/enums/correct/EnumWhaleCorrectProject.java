package com.cfpamf.ms.insur.admin.enums.correct;

import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;

/**
 * 保全项目枚举
 *
 * <AUTHOR>
 */
@Getter
public enum EnumWhaleCorrectProject {

    SURRENDER("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:SURRENDER", "退保"),

    PROTOCOL_TERMINATION("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:PROTOCOL_TERMINATION", "协议解约"),

    HESITATION_SURRENDER("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:HESITATION_SURRENDER", "犹豫期退保"),

    TERMINATION_PRODUCT("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:TERMINATION_PRODUCT", "解除附加险"),

    POLICY_REFUND("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:POLICY_REFUND", "保单退费"),

    CHANGE_PRODUCT("POLICY:PRESERVATION:TYPE:POLICY_SENSITIVE_INFO_CHANGE:CHANGE_PRODUCT", "险种信息变更"),

    CLAIM_EXEMPTION("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:CLAIM_EXEMPTION","理赔豁免"),

    CLAIMS_TERMINATION("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:CLAIMS_TERMINATION","理赔终止"),
    ADD_OR_SUBTRACT("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:ADD_OR_SUBTRACT", "增减员"),

    CHANNEL_REFERRER("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:CHANNEL_REFERRER", "渠道推荐人变更"),

    CUSTOMER_MANAGER_CHANGE("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:CUSTOMER_MANAGER_CHANGE", "初始渠道推荐人变更"),

    INSURED_BASE_INFO_CHANGE("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:INSURED_INFO_CHANGE", "被保人重要信息变更"),

    APPLICANT_INFO_CHANGE("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:APPLICANT_INFO_CHANGE", "投保人变更"),
    COMMISSION_RATIO_UP("POLICY:PRESERVATION:TYPE:POLICY_BASIC_CHANGE:COMMISSION_RATIO_UP","保单佣金比例变更"),
    ;

    private final String code;

    @Getter
    private final String name;

    EnumWhaleCorrectProject(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取理保全项目枚举
     *
     * @param code 材料编码
     * @return PreservationProjectEnum
     */
    public static EnumWhaleCorrectProject decode(String code) {
        return Arrays.stream(EnumWhaleCorrectProject.values())
                .filter(x -> x.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     *  个险&团险:公共保全
     */
    private final static Set<String> COMMON_PRESERVATION_SET =
            Sets.newHashSet(
                    EnumWhaleCorrectProject.CUSTOMER_MANAGER_CHANGE.getCode(),
                    EnumWhaleCorrectProject.INSURED_BASE_INFO_CHANGE.getCode(),
                    EnumWhaleCorrectProject.APPLICANT_INFO_CHANGE.getCode(),
                    EnumWhaleCorrectProject.CLAIM_EXEMPTION.getCode(),
                    EnumWhaleCorrectProject.CLAIMS_TERMINATION.getCode(),
                    EnumWhaleCorrectProject.CHANNEL_REFERRER.getCode(),
                    EnumWhaleCorrectProject.COMMISSION_RATIO_UP.getCode()
                    );
    /**
     *  个险保全
     */
    private final static Set<String> personalV1 =
            Sets.newHashSet(
                    EnumWhaleCorrectProject.SURRENDER.getCode(),
                    EnumWhaleCorrectProject.HESITATION_SURRENDER.getCode(),
                    EnumWhaleCorrectProject.PROTOCOL_TERMINATION.getCode(),
                    EnumWhaleCorrectProject.CHANGE_PRODUCT.getCode(),
                    EnumWhaleCorrectProject.PROTOCOL_TERMINATION.getCode(),
                    EnumWhaleCorrectProject.POLICY_REFUND.getCode(),
                    EnumWhaleCorrectProject.TERMINATION_PRODUCT.getCode()
            );

    public static boolean acceptPreservationMsg(String code) {
        return COMMON_PRESERVATION_SET.contains(code);
    }

    public static boolean personalPreservationV1(String code) {
        return personalV1.contains(code);
    }

}