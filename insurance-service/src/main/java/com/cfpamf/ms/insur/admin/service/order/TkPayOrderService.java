package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.config.CappProperties;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.event.OrderAppSuccessSmsEvent;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.fh.dto.OrderDTO;
import com.cfpamf.ms.insur.admin.external.tk.TkApiProperties;
import com.cfpamf.ms.insur.admin.external.tk.TkPayOrderServiceAdapter;
import com.cfpamf.ms.insur.admin.external.tk.model.TkIssueNotify;
import com.cfpamf.ms.insur.admin.external.tk.model.TkIssueSub;
import com.cfpamf.ms.insur.admin.external.zhongan.model.enums.ZaGroupPolicyStatusEnum;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupInsuredInfo;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.AsyncOrderHandDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderExtendTk;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured;
import com.cfpamf.ms.insur.admin.pojo.vo.group.GroupAcceptContext;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupService;
import com.cfpamf.ms.insur.admin.service.order.group.GroupRuleAdaptor;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.common.enums.EnumMonitor;
import com.cfpamf.ms.insur.common.service.monitor.MonitorHelper;
import com.cfpamf.ms.insur.weixin.constant.EnumEndor;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKCorrectPersonal;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKMemberChangeNotify;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKNotifyPersonal;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKPayNotify;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 泰康见费
 *
 * <AUTHOR> 2020/3/19 09:30
 */
@Slf4j
@Service
public class TkPayOrderService extends TkOrderService {

    @Autowired
    TkPayOrderServiceAdapter adapter;

    @Autowired
    SmOrderDDDMapper dddMapper;

    @Autowired
    SmOrderExtendTkMapper extendTkMapper;

    @Autowired
    SmOrderInsuredMapper insuredMapper;

    @Autowired
    CappProperties cappProperties;

    @Override
    public boolean support(String channel) {
        return Objects.equals(channel, EnumChannel.TK_PAY.getCode());
    }

    @Override
    protected String channel() {
        return EnumChannel.TK_PAY.getCode();
    }

    @Override
    protected ChannelOrderService orderService() {
        return adapter;
    }

    /**
     * 泰康见费的全部依赖回调
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderQueryResponse getOrderInfo(String orderId) {
        return OrderConvertor.mapperOrderQuery4LocalV3(orderId);
    }

    @Override
    public String getOrderPayUrl(String orderId) {

        String productType = productMapper.queryProductTypeByOrder(orderId);
        if (EnumProductCreateType.GROUP_INSURANCE.name().equals(productType)) {
            return super.getOrderPayUrl(orderId);
        }
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
        OrderPrePayRequest prePayDTO = new OrderPrePayRequest();
        prePayDTO.setOrderId(orderId);
        prePayDTO.setTransDate(order.getSubmitTime());
        prePayDTO.setOrderAmount(order.getTotalAmount());
        prePayDTO.setAppNo(order.getAppNo());
        OrderPrePayResponse orderPrePayResponse = adapter.prePayChannelOrder(prePayDTO);
        orderMapper.updateOrderPaymentId(orderId, orderPrePayResponse.getPayId(), null);
        return orderPrePayResponse.getPayUrl();

    }

    @Override
    public OrderSubmitResponse channelSubmitOrder(String userUniqueId, SmPlanVO planVo, OrderSubmitRequest source, OrderSubmitRequest change) {

        OrderSubmitResponse orderSubmitResponse = adapter.submitChannelOrder(change);

        SmOrderExtendTk extendTk = new SmOrderExtendTk();
        extendTk.setAppNo(orderSubmitResponse.getAppNo());
        extendTk.setFhOrderId(orderSubmitResponse.getOrderId());
        extendTkMapper.insertUseGeneratedKeys(extendTk);
        return orderSubmitResponse;
    }


    @Override
    public void handSyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException {
        TkApiProperties properties = adapter.properties();
        SmBaseOrderVO orderIndo = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (Objects.nonNull(orderIndo)
                && StringUtils.startsWith(orderIndo.getSubChannel(), EnumOrderSubChannel.CAPP.getCode())) {
            response.sendRedirect(cappProperties.getNewWechatPay() + "?orderId=" + orderId);
        } else {
            //直接重定向到前端
            response.sendRedirect(properties.getPayRedirectUrl() + "?orderId=" + orderId);
        }
    }

    @Override
    public void handAsyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException {

        String s = IOUtils.toString(request.getInputStream());

        log.info("泰康出单回调：{}", s);

        TkIssueNotify tkIssueNotify = adapter.parseNotify(s);

        SmOrderExtendTk smOrderExtendTk = extendTkMapper.selectByAppNo(tkIssueNotify.getFamilyProposalNo());
        if (Objects.isNull(smOrderExtendTk)) {
            log.error("订单不存在{}", tkIssueNotify);
            response.setStatus(500);
            return;
        } else {
            SmOrderExtendTk extendTk = new SmOrderExtendTk();
            extendTk.setId(smOrderExtendTk.getId());
            extendTk.setPayNotify(s);
            extendTkMapper.updateByPrimaryKeySelective(extendTk);
        }

        TkPayOrderService that = (TkPayOrderService) AopContext.currentProxy();
        that.issueTransactional(tkIssueNotify, smOrderExtendTk);

        OrderQueryResponse.PolicyInfo policyInfo = new OrderQueryResponse.PolicyInfo();
        policyInfo.setPolicyNo(tkIssueNotify.getOrderList().get(0).getPolicyNo());
        policyInfo.setDownloadURL(tkIssueNotify.getFamilyUrl());
        pushEvent(smOrderExtendTk.getFhOrderId(), policyInfo);
        PrintWriter writer = response.getWriter();
        writer.write("SUCCESS");
        response.setStatus(200);
        writer.flush();
    }


    /**
     * 出单事物
     *
     * @param tkIssueNotify
     * @param smOrderExtendTk
     */
    @Transactional(rollbackFor = Exception.class)
    public void issueTransactional(TkIssueNotify tkIssueNotify, SmOrderExtendTk smOrderExtendTk) {
        AtomicInteger sortNo = new AtomicInteger(0);
        List<SmOrderInsured> smOrderInsureds = insuredMapper.selectByOrderId(smOrderExtendTk.getFhOrderId());
        //刷新订单数据
        Map<String, TkIssueSub> policyMap = LambdaUtils.toMap(tkIssueNotify.getOrderList(), TkIssueSub::getOrderNo);
        for (SmOrderInsured insured : smOrderInsureds) {
            TkIssueSub tkIssueSub = policyMap.get(smOrderExtendTk.getFhOrderId() + "-" + sortNo.incrementAndGet());
            if (Objects.isNull(tkIssueSub)) {
                log.error("保单数量不匹配{}", tkIssueNotify);
                return;
            }
            SmOrderInsured update = new SmOrderInsured();
            update.setId(insured.getId());
            update.setPolicyNo(tkIssueSub.getPolicyNo());
            update.setDownloadUrl(tkIssueNotify.getFamilyUrl());
            update.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            update.setInsuredTime(LocalDateTime.now());
            insuredMapper.updateByPrimaryKeySelective(update);
        }
        orderMapper.updateOrderPayStatus(smOrderExtendTk.getFhOrderId(), SmConstants.ORDER_STATUS_PAYED);
        updateOrderPaymentTimeAndCommission(smOrderExtendTk.getFhOrderId());
    }

    /**
     * 团单支付通知
     *
     * @param data
     * @return
     */
    @Override
    public Object groupPayNotify(Object data) {
        if (!(data instanceof TKPayNotify)) {
            log.error("泰康团单支付回调异常，参数无法解析:{}", data);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR);
        }
        TKPayNotify notify = (TKPayNotify) data;

        String orderId = notify.getChannelContractNo();
        /**
         * 1.外部请求-数据存档
         */
        dumpNotifyMessage(notify.getChannelContractNo(), EnumChannel.TK_PAY.getCode(), JSON.toJSONString(data));

        String endorsementNo = notify.getGroupEndorNo();
        log.info("开始处理众安回调逻辑:{},{}", orderId, endorsementNo);
        try {
            if (StringUtils.isNotBlank(endorsementNo)) {
                return addPeople(orderId, notify);
            }
            return onlineApply(notify);
        }catch (Exception e){
            log.warn("泰康团险回调数据处理异常:{}",orderId,e);
            MonitorHelper.addMonitor(EnumMonitor.TK_GROUP_APPLY,"泰康团险回调流程异常,{},{}",orderId,endorsementNo);
            throw e;
        }
    }

    private SmOrderGroupNotify cvtGroupNotify(TKPayNotify data) {
        String orderId = data.getChannelContractNo();
        if (StringUtils.isBlank(orderId)) {
            orderId = data.getGroupAppEndorNo();
        }
        SmOrderGroupNotify notify = new SmOrderGroupNotify();
        notify.setOrderId(orderId);
        notify.setChannel(EnumChannel.TK_PAY.getCode());
        notify.setEndorsementNo(data.getGroupEndorNo());
        notify.setGroupPolicyNo(data.getGroupPolicyNo());
        notify.setGroupType("group");
        notify.setStatus(2);
        notify.setInterfaceVersion(0);
        return notify;
    }

    @Autowired
    SmOrderGroupNotifyMapper smOrderGroupNotifyMapper;

    @Autowired
    SmOrderGroupNotifyMsgMapper notifyMsgMapper;

    /**
     * 批增被保人
     *
     * @param orderId 此次批改对应的订单Id[sm_order_endor]
     * @param notify
     */
    private boolean addPeople(String orderId, TKPayNotify notify) {
        Endor payment = paymentMapper.queryEndorPaymentByOrderId(orderId);
        if (payment == null) {
            log.error("{}-批改单数据不存在，请联系开发人员~", orderId);
            return true;
        }

        if (payment.finish()) {
            log.warn("{}-批改单数据状态已完成，无需处理~", orderId);
            return true;
        }

        Integer correctId = payment.getId();
        String payType = payment.getPayWay();

        return correctSuccess(correctId, orderId, payType, notify);
    }

    @Autowired
    private SmOrderGroupService orderGroupService;

    /**
     * 批改成功
     *
     * @param correctId
     * @param orderId   批改的订单id
     * @param payType
     * @param notify
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean correctSuccess(Integer correctId, String orderId, String payType, TKPayNotify notify) {
        String policyNo = notify.getGroupPolicyNo();
        String endorsementNo = notify.getGroupEndorNo();
        String ePolicyUrl = notify.getGroupEpolicyUrl();

        SmOrderGroupNotify orderNotify = smOrderGroupNotifyMapper.queryByOrderId(orderId);
        if (orderNotify == null) {
            log.error("批改预留数据丢失,请开发人员尽快核实:{},{}", EnumChannel.TK_PAY.getCode(), endorsementNo);
            return false;
        }

        Integer notifyId = orderNotify.getId();
        SmOrderGroupNotifyMsg correctMessage = notifyMsgMapper.getByNotifyId(notifyId);
        if (correctMessage == null) {
            log.error("[SmOrderGroupNotifyMsg]数据丢失,请开发人员尽快核实-{}", endorsementNo);
            return false;
        }

        Endor param = new Endor();
        param.setId(correctId);
        param.setStatus(EnumEndor.EFFECT.getCode());
        param.setEndorsementNo(endorsementNo);
        param.setEPolicyUrl(ePolicyUrl);
        ZaGroupEndorsementRes correctBody = rebuildCorrectMessage(correctMessage, notify);
        correctMessage.setNotifyContent(JSON.toJSONString(correctBody));

        txService.excute(() -> {
            int r1 = paymentMapper.updateByPrimaryKeySelective(param);
            int r2 = smOrderGroupNotifyMapper.updateStatusByOrder(EnumChannel.TK_PAY.getCode(), orderId, GroupNotify.StatusEnum.UNDO.getCode());
            int r3 = notifyMsgMapper.updateBody(notifyId, correctMessage.getNotifyContent());
            log.info("批改成功状态更新结果:{},{},{}", r1, r2, r3);
        });

        orderNotify.setPayType(payType);

        GroupAcceptContext context = GroupAcceptContext.builder(EnumChannel.TK_PAY.getCode(), policyNo);
        context.setEndorsementNo(endorsementNo);
        context.setOrderNotify(orderNotify);
        context.setEndorsementBody(correctBody);

        orderGroupService.groupCorrectFlow(context);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean correctFail(Integer endorId, String orderId, TKPayNotify notify) {
        Endor param = new Endor();
        param.setId(endorId);
        param.setStatus(EnumEndor.FAIL.getCode());

        int r1 = paymentMapper.updateByPrimaryKeySelective(param);
        int r2 = smOrderGroupNotifyMapper.updateStatusByOrder(EnumChannel.TK_PAY.getCode(), orderId, GroupNotify.StatusEnum.UNDO.getCode());
        log.info("批改成功状态更新结果：{}，{}", r1, r2);
        return (r1 & r2) > 0;
    }

    /**
     * 更新批改暂存报文
     *
     * @param correctMessage
     * @param notify
     */
    private ZaGroupEndorsementRes rebuildCorrectMessage(SmOrderGroupNotifyMsg correctMessage, TKPayNotify notify) {
        String policyNo = notify.getGroupPolicyNo();
        String endorsementNo = notify.getGroupEndorNo();
        String notifyContent = correctMessage.getNotifyContent();

        ZaGroupEndorsementRes res = JSON.parseObject(notifyContent, ZaGroupEndorsementRes.class);
        Map<String, TKNotifyPersonal> personMap = LambdaUtils.safeToMap(notify.getPersonalPolicyList(), TKNotifyPersonal::getCredentialNo);

        ZaGroupEndorsementInfo result = res.getResult();
        result.setPolicyNo(policyNo);
        result.setEndorsementNo(endorsementNo);

        List<ZaGroupInsuredInfo> insuredList = res.getResult().getInsuredList();
        for (ZaGroupInsuredInfo entry : insuredList) {
            TKNotifyPersonal person = personMap.get(entry.getCertNo());
            entry.setActiveBranchId(person.getChannelPolicyNo());
            entry.setBranchId(person.getPersonalPolicyNo());
            entry.setEpolicyUrl(notify.getGroupEpolicyUrl());
        }
        return res;
    }

    /**
     * 更新批改暂存报文
     *
     * @param correctMessage
     * @param request
     */
    private ZaGroupEndorsementRes rebuildCorrectMessage4ReplaceMember(SmOrderGroupNotifyMsg correctMessage, TKMemberChangeNotify request) {
        log.info("开始重组批改替换人员报文:{}", JSON.toJSONString(correctMessage));
        String policyNo = request.getGroupPolicyNo();
        String endorsementNo = request.getGroupEndorNo();

        String notifyContent = correctMessage.getNotifyContent();

        ZaGroupEndorsementRes res = JSON.parseObject(notifyContent, ZaGroupEndorsementRes.class);
        Map<String, TKCorrectPersonal> personMap = LambdaUtils.safeToMap(request.getPersonalPolicyList(),
                a -> {
                    return a.getCredentialNo().toLowerCase();
                });

        ZaGroupEndorsementInfo result = res.getResult();
        result.setEndorsementNo(endorsementNo);
        result.setPolicyNo(policyNo);

        List<ZaGroupInsuredInfo> insuredList = res.getResult().getInsuredList();
        for (ZaGroupInsuredInfo entry : insuredList) {
            String idCard = entry.getCertNo().toLowerCase();

            Integer individualStatus = entry.getIndividualStatus();
            TKCorrectPersonal person = personMap.get(idCard);
            if (person != null) {
                if (Objects.equals(ZaGroupPolicyStatusEnum.INFORCE.getCode(), individualStatus)) {
                    entry.setBranchId(person.getPersonalPolicyNo());
                    entry.setEpolicyUrl(person.getPersonPolicyUrl());
                }
            }
        }
        log.info("重组批改替换人员报文结束:{}", JSON.toJSONString(res));
        return res;
    }

    /**
     * 人员替换-预处理数据状态
     *
     * @param request
     * @param notifyMessage
     */
    @Transactional(rollbackFor = Exception.class)
    public void memberChangePreHandle(TKMemberChangeNotify request, SmOrderGroupNotifyMsg notifyMessage, Endor endor) {
        Integer notifyId = notifyMessage.getNotifyId();
        String endorsementNo = request.getGroupEndorNo();
        String endorsementApplyNo = request.getGroupEndorAppNo();
        String ePolicyUrl= request.getGroupEpolicyUrl();

        endor.setApplyEndorsementNo(endorsementApplyNo);
        endor.setEndorsementNo(endorsementNo);
        endor.setStatus(EnumEndor.EFFECT.getCode());
        endor.setChannel(EnumChannel.TK_PAY.getCode());
        endor.setEPolicyUrl(ePolicyUrl);

        int i = notifyMsgMapper.updateBody(notifyId, notifyMessage.getNotifyContent());
        int j = smOrderGroupNotifyMapper.updateEndorsementNo(notifyId, endorsementNo);
        int k = endorMapper.updateByApplyId(endor);
        log.info("泰康团险人员替换预处理状态-更新数据包完成:{},{},{}", i, j, k);
    }


    @Autowired
    private TxServiceManager txService;

    /**
     * 团险在线出单
     *
     * @param notify
     * @return
     */
    private boolean onlineApply(TKPayNotify notify) {
        String orderId = notify.getChannelContractNo();
        log.info("新契约出单通知:{}", orderId);

        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (order == null) {
            log.error("{}-众安团险回调通知:订单信息不存在", orderId);
            return true;
        }

        if (finishOrder(order.getPolicyNo(), order.getPayStatus())) {
            log.warn("{}-众安团险回调通知:已出单(重复通知消息)", orderId);
            return true;
        }

        boolean crudFlag = txService.excute(() -> applySuccess(orderId, notify));
        if (crudFlag) {
            log.info("开始推送承保成功短信:{}", orderId);
            busEngine.publish(new OrderCommissionChangeEvent(orderId));
            busEngine.publish(new OrderAppSuccessSmsEvent(orderId));
        }
        return crudFlag;
    }

    /**
     * 投保成功后更新订单记录
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean applySuccess(String orderId, TKPayNotify notify) {
        log.info("出单成功，开始更新订单状态:{},{},{}", orderId);
        String policyNo = notify.getGroupPolicyNo();
        String endorsementNo = notify.getGroupEndorNo();
        String ePolicyUrl = notify.getGroupEpolicyUrl();
        OrderDTO param = new OrderDTO();
        param.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        param.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        param.setPaymentTime(new Date());
        param.setPolicyNo(policyNo);
        param.setEndorsementNo(endorsementNo);
        int rs = orderMapper.updateOrder(orderId, param);
        int rs2 = orderMapper.batchUpdateIns(orderId, policyNo, SmConstants.POLICY_STATUS_SUCCESS, ePolicyUrl);

        List<TKNotifyPersonal> personals = notify.getPersonalPolicyList();
        Map<String, TKNotifyPersonal> personMap = LambdaUtils.safeToMap(personals, TKNotifyPersonal::getChannelPolicyNo);

        List<SmOrderItem> orderItems = orderItemMapper.selectByOrderId(orderId);
        for (SmOrderItem item : orderItems) {
            TKNotifyPersonal entry = personMap.get(item.getActiveBranchId());
            if (entry != null) {
                item.setBranchId(entry.getPersonalPolicyNo());
                item.setThPolicyNo(policyNo);
                item.setThEndorsementNo(endorsementNo);
                item.setPolicyNo(policyNo);
                item.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            }
        }
        int rs3 = bulkUpdateOrderItem(orderId, orderItems);
        log.warn("{}-订单状态处理结果,订单记录:{},被保人记录:{},被保人扩展表:{}", orderId, rs, rs2, rs3);
        return true;
    }


    /**
     * 判断团单是否已经完成
     *
     * @param policyNo
     * @param payState
     * @return
     */
    private boolean finishOrder(String policyNo, String payState) {
        return StringUtils.isNotBlank(policyNo) && Objects.equals(payState, SmConstants.ORDER_STATUS_PAYED);
    }

    /**
     * 批减通知接口
     *
     * @param data
     * @return
     */
    @Override
    public Object groupReductionNotify(TKPayNotify data) {

        String applyEndorsementNo = data.getGroupAppEndorNo();
        Endor payment = paymentMapper.queryEndorPaymentByApplyNo(EnumChannel.TK_PAY.getCode(), applyEndorsementNo);
        if (payment == null) {
            log.error("{}-批改单数据不存在，请联系开发人员~", data);
            return true;
        }
        if (payment.finish()) {
            log.warn("{}-批改单数据状态已完成，无需处理~", data);
            return true;
        }
        dumpNotifyMessage(data.getChannelContractNo(), EnumChannel.TK_PAY.getCode(), JSON.toJSONString(data));

        String orderId = payment.getOrderId();
        try {
            if (data.success()) {
                correctSuccess(payment.getId(), orderId, payment.getPayWay(), data);
            } else {
                correctFail(payment.getId(), orderId, data);
            }
            return true;
        } catch (Exception e) {
            log.error("[{}-{}]批改生效失败,请联系保司核实！", orderId, payment.getEndorsementNo(), e);
            endorEffectError(payment.getId());
            MonitorHelper.addMonitor(EnumMonitor.TK_GROUP_CORRECT,"泰康团险批减流程异常:{}",applyEndorsementNo);
            return false;
        }
    }

    public void dumpNotifyMessage(String orderId, String channel, String message) {
        log.info("记录外部回调日志:{},{}", orderId, channel);
        saveNotifyLog(orderId, channel, message);
    }

    /**
     * 批改生效异常:[sm_order_endor]状态停留在支付成功，保留ErrorMsg，方便跟踪
     *
     * @param endorId
     * @return
     */
    public boolean endorEffectError(Integer endorId) {
        Endor param = new Endor();
        param.setId(endorId);
        param.setStatus(1);
        int r0 = paymentMapper.updateByPrimaryKeySelective(param);
        log.info("批改失败状态更新结果：{}", r0);
        return (r0) > 0;
    }

    @Autowired
    private ObjectMapper jsonMapper;

    @Override
    public AsyncOrderHandDTO handleChannelGroupAccept(SmOrderGroupNotify order,
                                                      SmOrderGroupNotifyMsg notifyMessage,
                                                      String recommendId) throws IOException {
        Integer type = order.getType();
        if (Objects.equals(type, GroupNotify.TypeEnum.ORDER.getCode())) {
            throw new MSBizNormalException("-1", "功能未实现");
        }
        ZaGroupEndorsementRes endorsementWrap = jsonMapper.readValue(notifyMessage.getNotifyContent(), ZaGroupEndorsementRes.class);
        String policyNo = order.getGroupPolicyNo();
        String endorsementNo = order.getEndorsementNo();
        GroupAcceptContext context = GroupAcceptContext.builder(EnumChannel.TK_PAY.getCode(), policyNo);
        context.setEndorsementNo(endorsementNo);
        context.setOrderNotify(order);
        context.setEndorsementBody(endorsementWrap);
        return handleChannelGroupAcceptV2(context);
    }

    @Override
    public AsyncOrderHandDTO handleChannelGroupAcceptV2(GroupAcceptContext context) {
        SmOrderGroupNotify orderNotify = context.getOrderNotify();
        Integer type = orderNotify.getType();
        if (Objects.equals(type, GroupNotify.TypeEnum.ORDER.getCode())) {
            throw new MSBizNormalException("-1", "功能未实现");
        }
        return groupCorrectFlow(context);
    }

    /**
     * 团险保单批改流程
     *
     * @param context
     * @return
     * @throws IOException
     */
    public AsyncOrderHandDTO groupCorrectFlow(GroupAcceptContext context) {
        log.info("开始解析团险批改报文:{}", context);
        if (!context.selfCheck()) {
            log.error("团险批改报文有误,请检查参数");
            throw new MSBizNormalException("-1", "团险批改报文有误,请检查参数");
        }

        String channel = context.getChannel();
        String policyNo = context.getPolicyNo();
        String endorsementNo = context.getEndorsementNo();

        SmOrderGroupNotify orderNotify = context.getOrderNotify();
        ZaGroupEndorsementRes endorsementWrap = context.getEndorsementBody();
        ZaGroupEndorsementInfo endorsementBody = endorsementWrap.getResult();

        if (endorsementBody != null && StringUtils.isBlank(endorsementBody.getEndorsementNo())) {
            endorsementBody.setEndorsementNo(endorsementNo);
        }

        String lockKey = super.genCorrectLock(channel, policyNo, endorsementNo);
        try {
            boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
            if (!lockSuccess) {
                log.warn("锁冲突:{}", lockKey);
                throw new MSBizNormalException(ExcptEnum.INVALID_TOKEN_501009);
            }

            log.info("团险批改-开始组装订单:{},{},{}", policyNo, endorsementNo, endorsementWrap);

            SmPolicyInsured policyInsured = orderMapper.listOneInsured(policyNo);
            if (policyInsured == null) {
                throw new MSBizNormalException("-1", "保单信息不存在,请检查报文");
            }

            String orderId = policyInsured.getFhOrderId();
            SmPlanVO planVO = productService.getPlanByOrderId(orderId);

            List<SmCreateOrderSubmitRequest> submitRequestList = adapter.genCorrectOrder(policyNo, endorsementWrap, planVO);
            for (SmCreateOrderSubmitRequest entry : submitRequestList) {
                entry.setPayType(orderNotify.getPayType());
            }
            super.batchSaveOrderInfoGroup(submitRequestList);
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }
        return null;
    }

    @Autowired
    private EndorMapper endorMapper;
    @Autowired
    private PolicyMapper policyMapper;

    @Override
    public Object memberChangeNotify(Object data) {
        TKMemberChangeNotify request = (TKMemberChangeNotify) data;

        dumpNotifyMessage(request.getChannelContractNo(), EnumChannel.TK_PAY.getCode(), JSON.toJSONString(data));

        if (!request.success()) {
            return memberChangeFail(request);
        }

        String endorsementApplyNo = request.getGroupEndorAppNo();
        String endorsementNo = request.getGroupEndorNo();
        String policyNo = request.getGroupPolicyNo();

        Endor endor = endorMapper.queryEndorPaymentByApplyNo(EnumChannel.TK_PAY.getCode(), endorsementApplyNo);
        if (endor == null) {
            log.error("批单记录信息不存在,请与保司核对数据-{},{}", endorsementApplyNo, policyNo);
            return false;
        }
        if (endor.success()) {
            log.warn("保司通知请求重复-数据已处理完成:{},{}", endorsementApplyNo, policyNo);
            return false;
        }
        String correctOrderId = endor.getOrderId();
        SmOrderGroupNotify orderNotify = smOrderGroupNotifyMapper.queryByOrderId(correctOrderId);
        if (orderNotify == null) {
            log.error("批改数据丢失,请开发人员尽快核实-{}-{}", EnumChannel.TK_PAY.getCode(), endorsementNo);
            return false;
        }

        orderNotify.setEndorsementNo(endorsementNo);
        Integer notifyId = orderNotify.getId();

        SmOrderGroupNotifyMsg correctMessage = notifyMsgMapper.getByNotifyId(notifyId);
        if (correctMessage == null) {
            log.error("批改数据丢失,请开发人员尽快核实-{}", endorsementNo);
            return false;
        }

        try {
            ZaGroupEndorsementRes correctBody = rebuildCorrectMessage4ReplaceMember(correctMessage, request);
            correctMessage.setNotifyContent(JSON.toJSONString(correctBody));

            ((TkPayOrderService) AopContext.currentProxy()).memberChangePreHandle(request, correctMessage, endor);

            GroupAcceptContext context = GroupAcceptContext.builder(EnumChannel.TK_PAY.getCode(), policyNo);
            context.setEndorsementNo(endorsementNo);
            context.setOrderNotify(orderNotify);
            context.setEndorsementBody(correctBody);

            orderGroupService.groupCorrectFlow(context);
        }catch (Exception e){
            log.warn("泰康团险替换人员流程失败:{},{}",policyNo,endorsementNo,e);
            MonitorHelper.addMonitor(EnumMonitor.TK_GROUP_CORRECT,"泰康团险批改流程异常:{}",policyNo);
            throw e;
        }
        return Boolean.TRUE;
    }

    private Boolean memberChangeFail(TKMemberChangeNotify memberchange) {
        log.info("泰康-人员替换失败：", memberchange);
        String applyEndorsementNo = memberchange.getGroupEndorAppNo();
        Endor endor = new Endor();
        endor.setApplyEndorsementNo(applyEndorsementNo);
        endor.setEndorsementNo(memberchange.getGroupEndorNo());
        endor.setStatus(EnumEndor.FAIL.getCode());
        endor.setChannel(EnumChannel.TK_PAY.getCode());
        int rtn = endorMapper.updateByApplyId(endor);
        log.info("批单状态更新结果:{},{}", applyEndorsementNo, rtn);
        return Boolean.TRUE;
    }
}
