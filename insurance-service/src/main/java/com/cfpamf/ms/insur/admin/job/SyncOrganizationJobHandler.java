package com.cfpamf.ms.insur.admin.job;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.bms.facade.vo.FdOrgVO;
import com.cfpamf.ms.insur.admin.pojo.dto.OrgDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.OrgVO;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.admin.service.OrgService;
import com.cfpamf.ms.insur.base.util.XxlLogger;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 与运营平台同步员工信息最新数据
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
//@JobHandler(value = "sync-organization")
public class SyncOrganizationJobHandler{

    /**
     * OmsService
     */
    @Autowired
    private BmsService bmsService;

    /**
     * OrgService
     */
    @Autowired
    private OrgService orgService;

    @XxlJob("sync-organization")
    public void execute() {
        //新版本传参方式
        String param = XxlJobHelper.getJobParam();
        XxlLogger.info(log, "开始与运营平台同步区域分支最新数据");

        boolean notCompareBatchNo = !StringUtils.isEmpty(param);
        if (notCompareBatchNo) {
            orgService.deleteAllOrgs();
        }

        List<FdOrgVO> bmsOrgs = bmsService.getAllRegionBranchs();
        List<OrgVO> insOrgs = orgService.getOrgs();

        bmsOrgs.forEach(bo -> {
            Optional<OrgVO> optional = insOrgs.stream().filter(io -> Objects.equals(io.getHrOrgId(), bo.getHrOrgId().toString())).findFirst();
            compareAndSave(bo, optional, 1);
            bo.getSubOrgs().forEach(io -> {
                io.setHrParentId(bo.getHrOrgId());
                Optional<OrgVO> optional2 = insOrgs.stream().filter(iio -> Objects.equals(iio.getHrOrgId(), io.getHrOrgId().toString())).findFirst();
                compareAndSave(io, optional2, 2);
            });
        });

        XxlLogger.info(log, "开始与运营平台同步区域分支最新数据");
        //return SUCCESS;
    }

    /**
     * 比较组织如果新增或者更新保存
     *
     * @param newOrg
     * @param optional
     */
    private void compareAndSave(FdOrgVO newOrg, Optional<OrgVO> optional, int type) {
        if (!optional.isPresent()) {
            OrgDTO dto = new OrgDTO();
            BeanUtils.copyProperties(newOrg, dto);
            copyProperties(newOrg, dto);
            dto.setOrgType(type);
            orgService.insertOrg(dto);
        } else if (!Objects.equals(optional.get().getBatchNo(), newOrg.getBatchNo())) {
            OrgVO vo = optional.get();
            OrgDTO dto = new OrgDTO();
            BeanUtils.copyProperties(newOrg, dto);
            copyProperties(newOrg, dto);
            dto.setOrgType(type);
            dto.setId(vo.getId());
            orgService.updateOrg(dto);
        }
    }

    /**
     * copyProperties
     *
     * @param vo
     * @param dto
     */
    private void copyProperties(FdOrgVO vo, OrgDTO dto) {
        dto.setHrParentId(vo.getHrParentId().toString());
        dto.setHrOrgId(vo.getHrOrgId().toString());
        dto.setOrgName(vo.getOrgName());
        dto.setBatchNo(vo.getBatchNo());
        dto.setOrgCode(vo.getOrgCode());
        dto.setOrgPath(vo.getTreePath());
    }
}
