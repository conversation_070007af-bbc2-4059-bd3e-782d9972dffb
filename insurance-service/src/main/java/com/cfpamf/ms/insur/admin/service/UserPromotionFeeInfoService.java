package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.UserPromotionFeeInfoMapper;
import com.cfpamf.ms.insur.admin.pojo.po.UserPromotionFeeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 员工推广费记录表服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserPromotionFeeInfoService {

    @Autowired
    private UserPromotionFeeInfoMapper userPromotionFeeInfoMapper;

    /**
     * 新增员工推广费记录
     *
     * @param userPromotionFeeInfo 员工推广费记录
     * @param operator 操作人
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(UserPromotionFeeInfo userPromotionFeeInfo, String operator) {
        if (userPromotionFeeInfo == null) {
            log.warn("员工推广费记录为空");
            return false;
        }

        // 设置创建信息
        LocalDateTime now = LocalDateTime.now();
        userPromotionFeeInfo.setCreateTime(now);
        userPromotionFeeInfo.setUpdateTime(now);
        userPromotionFeeInfo.setCreateUser(operator);
        userPromotionFeeInfo.setUpdateUser(operator);
        userPromotionFeeInfo.setDeleted(0);
        userPromotionFeeInfo.setRevision(1);

        // 设置默认值
        if (userPromotionFeeInfo.getPremium() == null) {
            userPromotionFeeInfo.setPremium(BigDecimal.ZERO);
        }
        if (userPromotionFeeInfo.getAmount() == null) {
            userPromotionFeeInfo.setAmount(BigDecimal.ZERO);
        }

        int result = userPromotionFeeInfoMapper.insert(userPromotionFeeInfo);
        log.info("新增员工推广费记录，用户ID：{}，统计日期：{}，结果：{}", 
                userPromotionFeeInfo.getUserId(), userPromotionFeeInfo.getStatisticalDate(), result > 0);
        return result > 0;
    }

    /**
     * 批量新增员工推广费记录
     *
     * @param userPromotionFeeInfoList 员工推广费记录列表
     * @param operator 操作人
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBatch(List<UserPromotionFeeInfo> userPromotionFeeInfoList, String operator) {
        if (CollectionUtils.isEmpty(userPromotionFeeInfoList)) {
            log.warn("员工推广费记录列表为空");
            return false;
        }

        LocalDateTime now = LocalDateTime.now();
        for (UserPromotionFeeInfo info : userPromotionFeeInfoList) {
            info.setCreateTime(now);
            info.setUpdateTime(now);
            info.setCreateUser(operator);
            info.setUpdateUser(operator);
            info.setDeleted(0);
            info.setRevision(1);

            // 设置默认值
            if (info.getPremium() == null) {
                info.setPremium(BigDecimal.ZERO);
            }
            if (info.getAmount() == null) {
                info.setAmount(BigDecimal.ZERO);
            }
        }

        int result = userPromotionFeeInfoMapper.insertBatch(userPromotionFeeInfoList);
        log.info("批量新增员工推广费记录，数量：{}，结果：{}", userPromotionFeeInfoList.size(), result > 0);
        return result > 0;
    }

    /**
     * 更新员工推广费记录
     *
     * @param userPromotionFeeInfo 员工推广费记录
     * @param operator 操作人
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(UserPromotionFeeInfo userPromotionFeeInfo, String operator) {
        if (userPromotionFeeInfo == null || userPromotionFeeInfo.getId() == null) {
            log.warn("员工推广费记录或ID为空");
            return false;
        }

        userPromotionFeeInfo.setUpdateTime(LocalDateTime.now());
        userPromotionFeeInfo.setUpdateUser(operator);

        int result = userPromotionFeeInfoMapper.updateById(userPromotionFeeInfo);
        log.info("更新员工推广费记录，ID：{}，结果：{}", userPromotionFeeInfo.getId(), result > 0);
        return result > 0;
    }

    /**
     * 删除员工推广费记录（逻辑删除）
     *
     * @param id 记录ID
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Integer id) {
        if (id == null) {
            log.warn("记录ID为空");
            return false;
        }

        int result = userPromotionFeeInfoMapper.deleteById(id);
        log.info("删除员工推广费记录，ID：{}，结果：{}", id, result > 0);
        return result > 0;
    }

    /**
     * 根据ID查询员工推广费记录
     *
     * @param id 记录ID
     * @return 员工推广费记录
     */
    public UserPromotionFeeInfo selectById(Integer id) {
        if (id == null) {
            log.warn("记录ID为空");
            return null;
        }
        return userPromotionFeeInfoMapper.selectById(id);
    }

    /**
     * 根据用户ID查询员工推广费记录列表
     *
     * @param userId 用户ID
     * @return 员工推广费记录列表
     */
    public List<UserPromotionFeeInfo> selectByUserId(String userId) {
        if (!StringUtils.hasText(userId)) {
            log.warn("用户ID为空");
            return null;
        }
        return userPromotionFeeInfoMapper.selectByUserId(userId);
    }

    /**
     * 根据用户ID和数据类型查询员工推广费记录列表
     *
     * @param userId 用户ID
     * @param dataType 数据类型
     * @return 员工推广费记录列表
     */
    public List<UserPromotionFeeInfo> selectByUserIdAndDataType(String userId, String dataType) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(dataType)) {
            log.warn("用户ID或数据类型为空");
            return null;
        }
        return userPromotionFeeInfoMapper.selectByUserIdAndDataType(userId, dataType);
    }

    /**
     * 根据统计日期范围查询员工推广费记录列表
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 员工推广费记录列表
     */
    public List<UserPromotionFeeInfo> selectByDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            log.warn("开始日期或结束日期为空");
            return null;
        }
        return userPromotionFeeInfoMapper.selectByDateRange(startDate, endDate);
    }

    /**
     * 根据用户ID和统计日期查询员工推广费记录
     *
     * @param userId 用户ID
     * @param statisticalDate 统计日期
     * @param dataType 数据类型
     * @return 员工推广费记录
     */
    public UserPromotionFeeInfo selectByUserIdAndDate(String userId, LocalDate statisticalDate, String dataType) {
        if (!StringUtils.hasText(userId) || statisticalDate == null || !StringUtils.hasText(dataType)) {
            log.warn("用户ID、统计日期或数据类型为空");
            return null;
        }
        return userPromotionFeeInfoMapper.selectByUserIdAndDate(userId, statisticalDate, dataType);
    }

    /**
     * 统计用户推广费总额
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param dataType 数据类型
     * @return 推广费总额
     */
    public BigDecimal sumAmountByUserIdAndDateRange(String userId, LocalDate startDate, LocalDate endDate, String dataType) {
        if (!StringUtils.hasText(userId) || startDate == null || endDate == null || !StringUtils.hasText(dataType)) {
            log.warn("参数不完整");
            return BigDecimal.ZERO;
        }
        BigDecimal result = userPromotionFeeInfoMapper.sumAmountByUserIdAndDateRange(userId, startDate, endDate, dataType);
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 统计用户保费总额
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param dataType 数据类型
     * @return 保费总额
     */
    public BigDecimal sumPremiumByUserIdAndDateRange(String userId, LocalDate startDate, LocalDate endDate, String dataType) {
        if (!StringUtils.hasText(userId) || startDate == null || endDate == null || !StringUtils.hasText(dataType)) {
            log.warn("参数不完整");
            return BigDecimal.ZERO;
        }
        BigDecimal result = userPromotionFeeInfoMapper.sumPremiumByUserIdAndDateRange(userId, startDate, endDate, dataType);
        return result != null ? result : BigDecimal.ZERO;
    }

    /**
     * 保存或更新员工推广费记录
     * 如果记录已存在则更新，否则新增
     *
     * @param userPromotionFeeInfo 员工推广费记录
     * @param operator 操作人
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(UserPromotionFeeInfo userPromotionFeeInfo, String operator) {
        if (userPromotionFeeInfo == null) {
            log.warn("员工推广费记录为空");
            return false;
        }

        // 查询是否已存在相同记录
        UserPromotionFeeInfo existingRecord = selectByUserIdAndDate(
                userPromotionFeeInfo.getUserId(),
                userPromotionFeeInfo.getStatisticalDate(),
                userPromotionFeeInfo.getDataType()
        );

        if (existingRecord != null) {
            // 更新现有记录
            userPromotionFeeInfo.setId(existingRecord.getId());
            userPromotionFeeInfo.setRevision(existingRecord.getRevision());
            return updateById(userPromotionFeeInfo, operator);
        } else {
            // 新增记录
            return insert(userPromotionFeeInfo, operator);
        }
    }
}
