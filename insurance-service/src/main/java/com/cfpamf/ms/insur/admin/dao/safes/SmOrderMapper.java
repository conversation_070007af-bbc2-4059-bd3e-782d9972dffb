package com.cfpamf.ms.insur.admin.dao.safes;

import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.fh.dto.FastInsuredDTO;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.external.fh.dto.OrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.ManualQueryOldCommissionDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.SmOrderCommissionDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.*;
import com.cfpamf.ms.insur.admin.pojo.dto.order.za.PaymentDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfig;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.order.FastOrderInsuredPo;
import com.cfpamf.ms.insur.admin.pojo.po.order.InsuredApply;
import com.cfpamf.ms.insur.admin.pojo.po.order.correct.CorrectOrderPo;
import com.cfpamf.ms.insur.admin.pojo.query.AutoOrderV3Query;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderCorrectQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderV3Query;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderNewVO;
import com.cfpamf.ms.insur.admin.pojo.vo.third.CustomerProviderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.third.SeperateCustomerMangerProviderVO;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.base.bean.IdName;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.request.order.OrderBasicVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupApplicant;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupUnderwriting;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.GroupInsuredFixVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicyReferrerFixVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicySettlementTimeFixVo;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 小额保险订单数据库访问mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SmOrderMapper {

    /**
     * 生成批改订单的批次号
     *
     * @return
     */
    @Select("select id from sm_order order by id desc limit 1")
    Integer getMaxNo();

    /**
     * 获取最大的渠道订单号
     *
     * @param startDate
     * @return
     */
    @Select("select channel,max(if(`subChannel`='import',SUBSTRING(`fhOrderId`,3), `fhOrderId`)) orderNo from sm_order" +
            "  where submitTime >#{startDate}" +
            "group by channel")
    List<OrderNoGenerator.ChannelOrderNo> listMaxNoByChannel(@Param("startDate") LocalDate startDate);


    /**
     * 查询订单列表
     *
     * @param query
     * @return
     */
    List<SmOrderListVO> listOrders(SmOrderQuery query);

    /**
     * 查询订单列表V3
     *
     * @param query
     * @return
     */
    List<SmOrderV3ListVO> listOrdersV3(SmOrderV3Query query);

    /**
     * 查询车险订单列表V3
     *
     * @param query
     * @return
     */
    List<SmOrderV3ListVO> listAutoOrdersV3(AutoOrderV3Query query);

    /**
     * 查询提成明细列表
     *
     * @param query
     * @return
     */
    List<SmOrderListVO> listOrderCommission(SmOrderQuery query);

    /**
     * 查询未提取客户信息的订单列表
     *
     * @param fhOrderId 如果订单号为空只会查询30分钟以前的订单
     * @param channel
     * @param appStatus
     * @return
     */
    List<SmExtractOrderVO> listUnExtractCustomerOrders(@Param("fhOrderId") String fhOrderId, @Param("channel") String channel, @Param("appStatus") String appStatus);

    /**
     * 查询需要更新的订单列表
     *
     * @param payStatus
     * @param appStatus
     * @param startDate
     * @param endDate
     * @param channel
     * @return
     */
    List<String> listJobChangeOrderIds(@Param("payStatus") String payStatus, @Param("appStatus") String appStatus, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("channel") String channel);

    /**
     * 查询需要更新的订单列表
     *
     * @param payStatus
     * @param appStatus
     * @param startDate
     * @param endDate
     * @param channel
     * @return
     */
    List<SmOrderListVO> listJobChangeOrders(@Param("payStatus") String payStatus, @Param("appStatus") String appStatus, @Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("channel") String channel);

    /**
     * 查询订单短信发送信息
     *
     * @param fhOrderId
     * @return
     */
    List<SmOrderSmsNotifyVO> getOrderSmsNotifyByOrderId(@Param("fhOrderId") String fhOrderId);

    /**
     * 查询订单被保人
     *
     * @param fhOrderId
     * @return
     */
    List<SmOrderSmsNotifyVO> getOrderInsuredByOrderId(@Param("fhOrderId") String fhOrderId);


    /**
     * 查询用户微信被保人
     *
     * @param ids
     * @return
     */
    List<IdName> listOrderInsuredPersonNamesByOrderId(@Param("ids") List<String> ids);

    /**
     * 查询订单详情
     *
     * @param orderId
     * @return
     */
    List<SmOrderListVO> listOrderInsuredDetailByOrderId(@Param("orderId") String orderId);

    /**
     * 查询订单渠道基础信息信息
     *
     * @param orderId
     * @return
     */
    List<SmBaseOrderInsuredVO> listBaseOrderInsuredByOrderId(@Param("orderId") String orderId);

    /**
     * 查询订单家财信息
     *
     * @param orderId
     * @return
     */
    SmPropertyVO getPropertyInfoByOrderId(@Param("orderId") String orderId);

    /**
     * 查询订单渠道基础信息信息
     *
     * @param orderId
     * @return
     */
    SmBaseOrderVO getBaseOrderInfoByOrderId(@Param("orderId") String orderId);

    /**
     * 查询订单渠道基础信息信息
     *
     * @param policyNo
     * @return
     */
    SmBaseOrderVO getBaseOrderInfoByPolicyNo(@Param("policyNo") String policyNo, @Param("status") String status);

    /**
     * 查询订单渠道基础信息信息
     *
     * @param orderId
     * @return
     */
    SmBaseOrderVO getEndorOrderById(@Param("orderId") String orderId);

    /**
     * 获取产品属性
     *
     * @param orderId
     * @return
     */
    @Select("SELECT p.productAttrCode FROM sm_order o left join sm_product p on o.productId=p.id where o.fhOrderId=#{orderId} limit 1")
    String getProductAttrByOrderId(@Param("orderId") String orderId);

    /**
     * 获取订单信息
     *
     * @param orderId
     * @return
     */
    FastOrderDTO getFastOrderByOrderId(@Param("orderId") String orderId);

    /**
     * 获取订单信息
     *
     * @param endorId
     * @return
     */
    FastOrderDTO getFastOrderByEndorId(@Param("endorId") String endorId);

    /**
     * 获取订单信息
     *
     * @param endorsementNo
     * @return
     */
    FastOrderDTO getFastOrderByEndorsementNo(@Param("endorsementNo") String endorsementNo);

    /**
     * 查询订单被保人信息
     *
     * @param orderId
     * @return
     */
    List<SmOrderInsuredVO> listOrderInsuredsByOrderId(@Param("orderId") String orderId, @Param("companyId") Integer companyId);

    /**
     * 查询订单统计信息
     *
     * @param query
     * @return
     */
    SmOrderSummaryVO getOrderSummary(SmOrderQuery query);

    /**
     * 查询订单统计信息
     *
     * @param query
     * @return
     */
    SmOrderSummaryVO getOrderSummaryV3(SmOrderV3Query query);

    /**
     * 查询车险订单统计信息
     *
     * @param query
     * @return
     */
    SmOrderSummaryVO getAutoOrderSummaryV3(AutoOrderV3Query query);

    /**
     * 查询订单统计信息
     *
     * @param query
     * @return
     */
    SmOrderSummaryVO getOrderCommissionSummary(SmOrderQuery query);

    /**
     * 查询保单下载地址
     *
     * @param id
     * @return
     */
    SmOrderInsuredVO getOrderInsuredByInsId(@Param("id") int id);


    /**
     * 查询投保人信息
     *
     * @param fhOrderId
     * @return
     */
    @ResultType(FhProposer.class)
    @Select("select * from sm_order_applicant where fhOrderId=#{fhOrderId}")
    FhProposer getOrderApplicant(@Param("fhOrderId") String fhOrderId);

    /**
     * 查询订单批改记录列表
     *
     * @return
     */
    List<SmOrderCorrectRecordVO> listOrderCorrectRecords(SmOrderCorrectQuery query);

    /**
     * 查询保单投保人与被保人信息
     *
     * @param policyNo
     * @return
     */
    SmOrderToCorrectVO getOrderToCorrectByPolicyNo(@Param("policyNo") String policyNo,
                                                   @Param("fieldCode") String fieldCode,
                                                   @Param("oldValue") String oldValue,
                                                   @Param("idNumber") String idNumber);

    /**
     * 查询保单投保人与被保人信息
     *
     * @param policyNoList
     * @param beforeMessage
     * @param projectCode
     * @return
     */
    List<SmOrderToCorrectVO> batchQueryCorrectOrder(@Param("policyNoList") List<String> policyNoList,
                                                    @Param("beforeMessage") List<String> beforeMessage,
                                                    @Param("projectCode") String projectCode);

    /**
     * 查询提成明细列表
     *
     * @param query
     * @return
     */
    int countOrderCommission(SmOrderQuery query);

    /**
     * 查询订单列表条数
     *
     * @param query
     * @return
     */
    int countOrders(SmOrderQuery query);

    /**
     * 查询订单列表条数
     *
     * @param query
     * @return
     */
    int countOrdersV3(SmOrderV3Query query);

    /**
     * 查询订单列表条数
     *
     * @param query
     * @return
     */
    int countAutoOrdersV3(AutoOrderV3Query query);

    /**
     * 查询订单产品生效时间信息
     *
     * @param orderId
     * @return
     */
    int countOrderProductEffectWaitingDay(@Param("orderId") String orderId);

    /**
     * 计算客户保单数量
     *
     * @param productId
     * @param idNumber
     * @return
     */
    int countProductPolicyQty(@Param("productId") Integer productId, @Param("idNumber") String idNumber,
                              @Param("occupationCodes") List<String> occupationCodes);

    /**
     * 计算客户保单数量(小于30天的)
     *
     * @param productId       产品id
     * @param idNumber        被保人证件号
     * @param occupationCodes 职业编码
     * @param startTime       保险生效开始时间
     * @return
     */
    int countProductsPolicyQtyLimit30Day(@Param("productId") Integer productId, @Param("idNumber") String idNumber,
                                         @Param("occupationCodes") List<String> occupationCodes, @Param("startTime") String startTime);


    /**
     * 计算客户保单数量
     *
     * @param productIds      产品几个
     * @param idNumber        证件号码
     * @param occupationCodes 职业编码
     * @return
     */
    int countProductsPolicyQty(@Param("productIds") List<Integer> productIds,
                               @Param("idNumber") String idNumber,
                               @Param("occupationCodes") List<String> occupationCodes, @Param("startTime") String startTime);

    /**
     * 计算客户保单数量
     *
     * @param productIds      产品几个
     * @param idNumber        证件号码
     * @param occupationCodes 职业编码
     * @return
     */
    int countSpecialProductsPolicyQty(@Param("productIds") List<Integer> productIds,
                                      @Param("idNumber") String idNumber,
                                      @Param("occupationCodes") List<String> occupationCodes,
                                      @Param("startTime") String startTime
    );

    List<InsuredApply> countApply(@Param("productIds") List<Integer> productIds,
                                  @Param("idNumbers") List<String> idNumbers,
                                  @Param("startTime") Date startTime
    );


    /**
     * 新建订单
     *
     * @param dto
     */
    void insertOrder(SmCreateOrderSubmitRequest dto);

    /**
     * 修建订单投保人
     *
     * @param dto
     */
    void insertOrderApplicant(SmCreateOrderSubmitRequest dto);

    /**
     * 新建订单被保人
     *
     * @param dto
     */
    void insertOrderInsured(@Param("dto") SmCreateOrderSubmitRequest dto);

    /**
     * 批量新建订单
     */
    int insertOrderBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    /**
     * 批量新建订单投保人
     */
    int insertOrderApplicantBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    /**
     * 批量新建订单被保人
     */
    int insertOrderInsuredBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    /**
     * 批量新建订单被保人
     *
     * @param orderList 提交的订单信息
     */
    void insertOrderInsuredBatchV2(@Param("orderList") List<SmCreateOrderSubmitRequest> orderList);

    /**
     * 批量新建订单被保人
     *
     * @param insuredList
     */
    void insertOrderInsuredList(@Param("insuredList") List<FhInsuredPerson> insuredList);

    /**
     * 新建被保人家财信息
     *
     * @param dto
     */
    void insertOrderPropertyInfo(SmCreateOrderSubmitRequest dto);

    /**
     * 支付成功后增加订单提成信息
     *
     * @param fhOrderId
     */
    void insertOrderPayedCommission(@Param("fhOrderId") String fhOrderId);

    /**
     * 退保增加订单退保提成信息
     *
     * @param fhOrderId
     */
    void insertOrderSurrenderCommission(@Param("fhOrderId") String fhOrderId, @Param("idNumber") String idNumber);

    /**
     * 新增保单批改记录
     *
     * @param dto
     */
    void insertOrderCorrect(SmSaveOrderCorrectDTO dto);

    /**
     * 新增保单批改记录
     *
     * @param orderList
     * @return
     */
    int batchInsertCorrectOrder(@Param("data") List<SmSaveOrderCorrectDTO> orderList);

    /**
     * 删除订单退保提成信息
     *
     * @param fhOrderId
     */
    void deleteOrderSurrenderCommission(@Param("fhOrderId") String fhOrderId, @Param("idNumber") String insuredIdNumber);

    /**
     * 更新订单支付信息
     *
     * @param orderId
     * @param commissionId
     * @return
     */
    int updateOrderCommission(@Param("orderId") String orderId, @Param("commissionId") String commissionId);

    /**
     * 修改订单提层配置
     *
     * @param orderId
     * @param commissionId
     * @return
     */
    int updateOrderCommissionOnly(@Param("orderId") String orderId, @Param("commissionId") String commissionId);

    /**
     * 更新订单支付时间
     *
     * @param orderId
     * @return
     */
    int updateOrderPaymentTime(@Param("orderId") String orderId);

    /**
     * 更新订单支付时间
     *
     * @param orderId
     * @param payTime
     * @return
     */
    int updateOrderSpecPaymentTime(@Param("orderId") String orderId, @Param("payTime") Date payTime);

    /**
     * 更新订单支付Id
     *
     * @param orderId
     * @param payId
     * @param payUrl
     * @return
     */
    int updateOrderPaymentId(@Param("orderId") String orderId, @Param("payId") String payId, @Param("payUrl") String payUrl);

    /***
     * 更新订单状态
     *
     * @param fhOrderId
     * @param payStatus
     */
    void updateOrderPayStatus(@Param("fhOrderId") String fhOrderId, @Param("payStatus") String payStatus);

    /**
     * 修改支付状态以及支付时间
     *
     * @param fhOrderId
     * @param payStatus
     */
    void updateOrderPayStatusAndPayTime(@Param("fhOrderId") String fhOrderId, @Param("payStatus") String payStatus);

    /***
     * 更新保单状态
     *
     * @param fhOrderId
     */
    void updateOrderAppStatus(@Param("fhOrderId") String fhOrderId, @Param("appStatus") String appStatus, @Param("idNumber") String idNumber);

    /***
     * 更新保单状态和保单下载地址
     *
     * @param fhOrderId
     */
    void updateOrderAppStatusAndPolicyUrl(@Param("fhOrderId") String fhOrderId, @Param("appStatus") String appStatus, @Param("idNumber") String idNumber, @Param("policyUrl") String policyUrl);

    /***
     * 更新保单状态
     *
     * @param fhOrderId
     */
    void updateOrderItemStatus(@Param("fhOrderId") String fhOrderId, @Param("appStatus") String appStatus, @Param("idNumber") String idNumber);

    /**
     * 更新订单保单信息
     *
     * @param orderId
     * @param idNumber
     * @param policyInfo
     */
    void updateOrderPolicyInfo(@Param("orderId") String orderId, @Param("idNumber") String idNumber, @Param("policyInfo") OrderQueryResponse.PolicyInfo policyInfo);

    /**
     * 更新订单保单信息
     *
     * @param orderId
     * @param idNumber
     * @param policyInfo
     */
    void updateOrderPolicyInfoV2(@Param("orderId") String orderId, @Param("idNumber") String idNumber, @Param("policyInfo") OrderQueryResponse.PolicyInfo policyInfo);

    void updateOrderPolicyInfoByInduredId(@Param("insuredId") int insuredId, @Param("policyInfo") OrderQueryResponse.PolicyInfo policyInfo);

    /**
     * 更新保单不为空的信息
     *
     * @param insuredId
     * @param policyInfo
     */
    void updateOrderPolicyInfoByInduredIdV2(@Param("insuredId") int insuredId, @Param("policyInfo") OrderQueryResponse.PolicyInfo policyInfo);

    /**
     * 更新订单退保信息
     *
     * @param orderId
     * @param idNumber
     */
    void updateOrderSurrenderTime(@Param("orderId") String orderId, @Param("idNumber") String idNumber);

    /**
     * 更新订单退保信息  不会删除保单号
     *
     * @param orderId
     * @param idNumber
     */
    void updateOrderSurrenderTimeSimple(@Param("orderId") String orderId, @Param("idNumber") String idNumber);

    /**
     * 更新订单退保信息  不会删除保单号
     *
     * @param orderId
     * @param idNumber
     */
    void updateOrderSurrender(@Param("orderId") String orderId, @Param("surrenderTime") Date surrenderTime, @Param("idNumber") String idNumber);

    void updateOrderSurrenderTimeByInsuredId(@Param("insuredId") int insuredId);

    /**
     * 更新订单退保信息
     *
     * @param orderId
     */
    void updateOrderSurrenderTimeBatch(@Param("orderId") String orderId, @Param("idNumbers") List<String> idNumbers);

    /**
     * 根据保单批改
     */
    int updateOrderSurrenderTimeByPolicyNo(@Param("policyNo") String policyNo, @Param("idNumbers") List<String> idNumbers);

    /**
     * 根据保单批改
     */
    int updateSurrender(@Param("policyNo") String policyNo,
                        @Param("surrenderTime") Date surrenderTime,
                        @Param("idNumbers") List<String> idNumbers);

    /**
     * 更新订单客户已经抽出flag
     *
     * @param orderIds
     */
    void updateOrderExtractCustomerFlag(@Param("orderIds") List<String> orderIds);

    /**
     * 更新订单被保人身份证
     *
     * @param fhOrderId
     * @param idNumber
     * @param personName
     */
    void updateOrderInsuredIdNo(@Param("fhOrderId") String fhOrderId, @Param("idNumber") String idNumber, @Param("personName") String personName);

    /**
     * 更新订单投保人身份证
     *
     * @param fhOrderId
     * @param idNumber
     */
    void updateOrderApptIdNo(@Param("fhOrderId") String fhOrderId, @Param("idNumber") String idNumber);

    /**
     * 更新续保订单信息
     *
     * @param fhOrderId
     * @param renewOrderId
     */
    void updateOrderRenewInfo(@Param("fhOrderId") String fhOrderId, @Param("renewOrderId") String renewOrderId);

    /**
     * 变更保单客户负责人
     *
     * @param oldAdminId
     * @param oldAdminJobCode
     * @param newAdminId
     * @param newAdminJobCode
     * @return
     */
    int updateOrderCustomerAdmin(@Param("oldAdminId") String oldAdminId,
                                 @Param("oldAdminJobCode") String oldAdminJobCode,
                                 @Param("newAdminId") String newAdminId,
                                 @Param("newAdminJobCode") String newAdminJobCode,
                                 @Param("newAdminMainJobNumber") String newAdminMainJobNumber,
                                 @Param("newCustomerAdminOrgCode") String newCustomerAdminOrgCode,
                                 @Param("idNumbers") List<String> idNumbers);

    /**
     * 变更保单客户负责人
     *
     * @param oldAdminId
     * @param oldCustomerAdminOrgCode 离职员工机构编码
     * @param newAdminId
     * @param newAdminJobCode
     * @param newAdminMainJobNumber
     * @param newCustomerAdminOrgCode
     * @param idNumbers
     * @return
     */
    int updateOrderCustomerAdminV2(@Param("oldAdminId") String oldAdminId,
                                   @Param("oldCustomerAdminOrgCode") String oldCustomerAdminOrgCode,
                                   @Param("newAdminId") String newAdminId,
                                   @Param("newAdminJobCode") String newAdminJobCode,
                                   @Param("newAdminMainJobNumber") String newAdminMainJobNumber,
                                   @Param("newCustomerAdminOrgCode") String newCustomerAdminOrgCode,
                                   @Param("idNumbers") List<String> idNumbers);

    /**
     * 变更订单管控人
     *
     * @param oldAdminId
     * @param newAdminId
     * @param newAdminJobCode
     * @param newAdminMainJobNumber
     * @param newCustomerAdminOrgCode
     * @param idNumbers
     * @return
     */
    int updateOrderCustomerAdminV3(@Param("oldAdminId") String oldAdminId,
                                   @Param("newAdminId") String newAdminId,
                                   @Param("newAdminJobCode") String newAdminJobCode,
                                   @Param("newAdminMainJobNumber") String newAdminMainJobNumber,
                                   @Param("newCustomerAdminOrgCode") String newCustomerAdminOrgCode,
                                   @Param("idNumbers") List<String> idNumbers);

    /**
     * 更新订单提成表中承保信息
     *
     * @param fhOrderId
     */
    void updateOrderCommissionPolicyInfo(@Param("fhOrderId") String fhOrderId);

    /**
     * 修改保单金额
     *
     * @param policyNo
     * @param cancelAmount
     * @param appStatus
     */
    void updateOrderCommissionCancelAmount(@Param("policyNo") String policyNo, @Param("cancelAmount") BigDecimal cancelAmount, @Param("appStatus") String appStatus);

    /**
     * 修改订单推荐人信息
     *
     * @param dto
     * @return
     */
    int updateOrderErrorRecommendInfo(SmOrderCorrectDTO dto);

    /**
     * 批量更新推荐人信息
     *
     * @param orderList
     * @return
     */
    int batchUpdateOrderRecommendInfo(@Param("orderList") List<CorrectOrderPo> orderList);

    /**
     * 只能修改推荐人为空或者空串用户
     *
     * @param dto
     */
    int updateOrderErrorRecommendInfoV2(SmOrderCorrectDTO dto);


    /**
     * 修改订单信息
     *
     * @param dto
     * @return
     */
    int updateOrderErrorInfo(SmOrderCorrectDTO dto);

    /**
     * 修改订单投保人信息
     *
     * @param dto
     * @return
     */
    int updateOrderErrorAppInfo(SmOrderCorrectDTO dto);

    /**
     * 批量更新投保人字段信息
     *
     * @param dto
     * @return
     */
    int batchUpdateOrderAppInfo(@Param("data") List<CorrectOrderPo> dto);

    /**
     * 批量更新投保人字段信息
     *
     * @param dto
     * @return
     */
    int batchUpdateOrderInsuredInfo(@Param("data") List<CorrectOrderPo> dto);

    /**
     * 修改订单被保人信息
     *
     * @param dto
     * @return
     */
    int updateOrderErrorInsInfo(SmOrderCorrectDTO dto);

    /**
     * 修改替换的被保人信息
     *
     * @param dto
     * @return
     */
    int updateOrderReplaceInsInfo(InsuredInfoReplaceDTO dto);

    /**
     * 修改订单提成信息
     *
     * @param dto
     * @return
     */
    int updateOrderErrorCommissionInfo(SmOrderCorrectDTO dto);

    /**
     * 批量更新佣金
     *
     * @param data
     * @return
     */
    int batchUpdateCommissionInfo(@Param("data") List<CorrectOrderPo> data);

    /**
     * 修改佣金表中推荐人信息为空或者空串的
     *
     * @param dto
     * @return
     */
    int updateOrderErrorCommissionRecommendInfoV2(SmOrderCorrectDTO dto);

    int updateSmOrderInsuredBatch(@Param("insuredList") List<FhInsuredPerson> insuredList);

    /**
     * 获取提成记账时间
     *
     * @return
     */
    Date getOrderCommissionAccountTime(@Param("fhOrderId") String fhOrderId, @Param("insIdNumber") String insIdNumber);

    Date queryOrderCommission(@Param("orderList") List<String> orderList);

    /**
     * 删除订单
     *
     * @param fhOrderId
     * @return
     */
    void deleteOrder(@Param("fhOrderId") String fhOrderId);

    /**
     * 强制删除
     *
     * @param fhOrderId
     */
    void deleteOrderForce(@Param("fhOrderId") String fhOrderId);

    /**
     * 删除错误计算的订单提成信息
     *
     * @param fhOrderId
     */
    void deleteOrderCommission(String fhOrderId);

    SmInsuredDetailDTO insuredDetailByInsuredId(@Param("insuredId") Integer insuredId);

    /**
     * 查询一段时间内未支付的单子的id
     *
     * @param start
     * @param end
     * @return
     */
    List<String> listOrderIdNotPay(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    /**
     * 根据订单号查询订单号【校验订单是否存在】
     *
     * @param fhOrderIds
     * @return
     */
    List<String> selectOrderIdByOrderIds(@Param("fhOrderIds") List<String> fhOrderIds);

    /**
     * 重新计算单价
     *
     * @param fhOrderIds
     * @return
     */
    int updateOrderUnit(@Param("fhOrderIds") List<String> fhOrderIds);

    /**
     * 团险
     * 通过保单号查询被保人信息
     *
     * @param policyNo
     * @return
     */
    List<SmPolicyInsured> listOrderInsuredByPolicyNo(@Param("policyNo") String policyNo);

    /**
     * Individual 个险
     * 获取个险订单的被保险人列表
     *
     * @param policyNo
     * @return
     */
    List<SmPolicyInsured> listIndividualOrderInsuredByPolicyNo(@Param("policyNo") String policyNo);

    /**
     * 查一个被保人
     *
     * @param policyNo
     * @return
     */
    SmPolicyInsured listOneInsured(@Param("policyNo") String policyNo);

    /**
     * 通过保单号查询被保人信息
     *
     * @param policyNo
     * @param branchIdList
     * @return
     */
    List<FastOrderInsuredPo> queryInsuredList(@Param("policyNo") String policyNo, @Param("branchIdList") List<String> branchIdList);

    /**
     * 查询提层丢失的数据
     *
     * @param start
     * @param end
     * @return
     */
    List<String> listCommissionOrderIdByLate(@Param("start") LocalDate start, @Param("end") LocalDateTime end);

    /**
     * 查询退保后扣钱记录丢失的数据
     *
     * @param start
     * @return
     */
    List<String> listCommissionCancelOrderIdByLateCancel(@Param("start") LocalDate start, @Param("end") LocalDateTime end);

    List<SmOrderNewVO> listOrdersV2(@Param("orderIds") List<String> orderIds);

    List<String> listFhOrderIdOrders(SmOrderQuery query);

    @Select("select fhOrderId from sm_order where fhOrderId like concat(#{orderId},'%') order by id; ")
    List<String> listOrderIdByLike(@Param("orderId") String orderId);

    /**
     * 根据订单号查询被保人信息
     *
     * @param orderId
     * @return
     */
    List<SmOrderInsured> listSmOrderInsuredByOrderId(@Param("orderId") String orderId);

    /**
     * 统计某个手机号码有多少个证件号码
     *
     * @return
     */
    int countApplicantByPhone(@Param("phone") String phone, @Param("idCard") String idCard,
                              @Param("startTime") LocalDateTime startTime);

    SmOrderApplicant selectOrderApplicantByOrderId(@Param("fhOrderId") String fhOrderId);

//    int countFhOrderIdLikeByOrderId(@Param("fhOrderId") String fhOrderId);

    List<String> listOrderId(@Param("orderId") String orderId);

    /**
     * 批量查询订单
     *
     * @param fhOrderIds
     * @return
     */
    List<SmOrder> listSmOrderByOrderIds(@Param("fhOrderIds") List<String> fhOrderIds);

    /**
     * 支付成功后增加订单提成信息
     *
     * @param fhOrderId
     */
    void insertOrderPayedCommissionV2(@Param("fhOrderId") String fhOrderId);

    /**
     * 退保增加订单退保提成信息
     *
     * @param fhOrderId
     */
    void insertOrderSurrenderCommissionV2(@Param("fhOrderId") String fhOrderId, @Param("idNumber") String idNumber);

    /**
     * 更新订单提成表中承保信息(还未改好，还是与版本1一样)
     *
     * @param fhOrderId
     */
    void updateOrderCommissionPolicyInfoV2(@Param("fhOrderId") String fhOrderId);


    List<SmOrder> listOriginalOrderByOrderId(@Param("fhOrderId") String fhOrderId);

    /**
     * 根据订单号和身份证号获取保单信息
     *
     * @param fhOrderId
     * @param idNumberList
     * @return
     */
    List<SmBaseOrderInsuredVO> listOriginalOrderByOrderIdAndIdNumber(@Param("fhOrderId") String fhOrderId, @Param("idNumberList") List<String> idNumberList);

    /**
     * 通过订单id集合搜索订单的折算保费
     *
     * @param orderIdList
     * @return
     */
    List<SmOrderListVO> findOrderCommissionByOderIdList(@Param("orderIdList") List<String> orderIdList);


    /**
     * @param dtos
     * @return
     */
    int updateSmOrderBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    /**
     * @param dtos
     * @return
     */
    int updateCarSmOrderInsuredBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    /**
     * @param dtos
     * @return
     */
    int updateCarSmOrderApplicantBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> dtos);

    /**
     * 根据订单号批量获取投保人信息
     *
     * @param ids
     * @return
     */
    List<SmOrderApplicant> listOrderApplicantByFhOrderIds(@Param("ids") List<String> ids);

    /**
     * 根据订单号批量获取被保人信息
     *
     * @param ids
     * @return
     */
    List<SmOrderInsured> listSmOrderInsuredByFhOrderIds(@Param("ids") List<String> ids);

    /**
     * @param ids
     */
    int updateSmOrderInsuredCancelBatch(@Param("dtos") List<SmCreateOrderSubmitRequest> ids);

    OrderDetailCommissionDTO getOrderDetailCommissionByFhOrderId(@Param("fhOrderId") String fhOrderId);

    OrderDetailCommissionDTO getOrderDetailCommissionByFhOrderIdV4(@Param("fhOrderId") String fhOrderId);

    Date getSurrenderTime(@Param("fhOrderId") String fhOrderId);

    /**
     * 批量更新车险佣金导入
     *
     * @param ids
     * @return
     */
    @Deprecated
    void updateCarOrderCommissionBatch(@Param("dtos") List<SmCarOrderCommissionExcelDTO> ids);

    /**
     * 批量更新车险佣金导入
     *
     * @param ids
     * @return
     */
    void updateCarOrderCommissionBatch2(@Param("dtos") List<CarCommissionImportDTO> ids);

    /**
     * 获取需要回访的数据
     *
     * @param ids
     * @return
     */
    List<SmOrderPolicyDTO> listNeedVisitOrderByProductIds(@Param("ids") List<String> ids);

    /**
     * 保存订单产品信息
     *
     * @param product
     */
    int saveTempOrderProduct(@Param("orderId") String orderId, @Param("product") OrderProductDTO product);

    /**
     * 保存订单产品信息
     *
     * @param orderId
     */
    @Delete("delete from sm_order_product where order_id=#{orderId} ")
    int deleteOrderProduct(@Param("orderId") String orderId);

    OrderProductDTO queryOrderProduct(@Param("orderId") String orderId);

    /**
     * 临时存储订单信息
     *
     * @param orderInfo
     */
    int saveTempOrder(@Param("userId") String userId, @Param("orderId") String orderId, @Param("order") OrderBasicVo orderInfo);

    /**
     * 临时存储订单-投保人信息
     * @param userId
     * @param applicant
     */
//    int saveTempApplicant(@Param("userId")String userId,@Param("orderId")String orderId, @Param("vatInvoice")VatInvoice vatInvoice, @Param("data")GroupApplicant applicant);

    /**
     * 临时存储订单-投保人信息
     *
     * @param userId
     * @param insuredPerson
     */
    int saveTempInsured(@Param("userId") String userId, @Param("orderId") String orderId, @Param("data") List<FastInsuredDTO> insuredPerson);

    int clearTempOrder(@Param("orderId") String orderId);

    int clearTempApplicant(@Param("orderId") String orderId);

    int clearTempInsured(@Param("orderId") String orderId);

    /**
     * 可能存在同一个订单重复核保的情况，所以此处需要删除临时数据
     *
     * @param orderId
     * @return
     */
    int clearTempItem(@Param("orderId") String orderId);

    int clearTempOrderProduct(@Param("orderId") String orderId);

    int savePayNotify(@Param("orderId") String orderId,
                      @Param("outOrderId") String outOrderId,
                      @Param("message") String message,
                      @Param("state") int state);

    String getAppNo(@Param("orderId") String orderId);

    int saveQuoteOrder(@Param("userId") String userId, @Param("orderId") String orderId, @Param("planId") Integer planId, @Param("data") GroupUnderwriting req);

    /**
     * 更新核保单
     *
     * @param userId
     * @param orderId
     * @param planId
     * @param req
     * @return
     */
    int updateQuoteOrder(@Param("userId") String userId, @Param("orderId") String orderId, @Param("planId") Integer planId, @Param("data") GroupUnderwriting req);

    int saveQuoteApplicant(@Param("orderId") String orderId, @Param("vatInvoice") String vatInvoice, @Param("data") GroupApplicant applicant);

    int updateQuoteApplicant(@Param("orderId") String orderId, @Param("vatInvoice") String vatInvoice, @Param("data") GroupApplicant applicant);

    /**
     * 保存临时被保人数据
     *
     * @param orderId
     * @param insuredList
     * @return
     */
    int saveQuoteInsureds(@Param("orderId") String orderId, @Param("data") List<GroupInsured> insuredList);

    PaymentDTO queryPayment(@Param("orderId") String orderId);

    /**
     * 更新订单的部分信息
     *
     * @param orderId
     * @param data
     * @return
     */
    int updateOrder(@Param("orderId") String orderId, @Param("data") OrderDTO data);

    List<SysDutyConfig> queryDutyConfig(@Param("channel") String channel, @Param("type") Integer type);

    /**
     * 承保后更新被保人数据状态
     *
     * @param orderId
     * @param policyNo
     * @param status
     * @param epolicyUrl
     * @return
     */
    int batchUpdateIns(@Param("orderId") String orderId,
                       @Param("policyNo") String policyNo,
                       @Param("status") String status,
                       @Param("epolicyUrl") String epolicyUrl);

    /**
     * 回调报文状态更新
     *
     * @param orderId
     * @param policyState
     * @param errorMsg
     * @return
     */
    int updateNotifyState(@Param("orderId") String orderId, @Param("state") int policyState, @Param("errorMsg") String errorMsg);

    @Select("SELECT count(1) FROM sm_order WHERE fhOrderId=#{orderId}")
    int count(@Param("orderId") String orderId);

    @Select("SELECT notify_content FROM sm_order_extend_zan WHERE fh_order_id=#{orderId}")
    String getNotifyMsgFromZA(@Param("orderId") String orderId);

    @Select("SELECT p.productAttrCode  FROM sm_order o, sm_product p WHERE o.productId = p.id and o.fhOrderId=#{orderId}")
    String getProductAttrCode(@Param("orderId") String fhOrderId);

    @Select("SELECT productId  FROM sm_order WHERE fhOrderId=#{orderId}")
    String getProductIdByOrderId(@Param("orderId") String fhOrderId);


    @Select("SELECT fhOrderId FROM sm_order_insured WHERE policyNo= #{policyNo} order by id LIMIT 1")
    String getFhOrderIdByPolicyNo(@Param("policyNo") String policyNo);

    @Select("SELECT p.orderOutType FROM sm_order o, sm_product p WHERE o.productId = p.id and o.fhOrderId=#{orderId}")
    String getProductOutType(@Param("orderId") String fhOrderId);

    List<SmOrderSmsNotifyVO> getGroupOrderNotifyMessage(String fhOrderId);

    @Delete("DELETE FROM sm_order_draft WHERE order_id=#{orderId}")
    int deleteDraftOrder(@Param("orderId") String orderId);

    @Select("SELECT product_name from sm_order_product where order_id=#{orderId}")
    String queryProductName(@Param("orderId") String orderId);

    /**
     * 查询电子保单地址
     *
     * @param orderId
     * @return
     */
    @Select("SELECT downloadURL from sm_order_insured where fhOrderId=#{orderId} limit 1")
    String queryEPolicyUrl(@Param("orderId") String orderId);

    /**
     * 更新电子保单地址
     *
     * @param orderId
     * @param epolicyUrl
     * @return
     */
    @Update(" UPDATE sm_order_insured set downloadURL=#{epolicyUrl} where fhOrderId=#{orderId} ")
    int updateEPolicyUrl(@Param("orderId") String orderId, @Param("epolicyUrl") String epolicyUrl);

    @Select("select p.companyId from sm_order o left join sm_product p on o.productId=p.id where fhOrderId=#{orderId} ")
    Integer queryCompanyIdByOrder(@Param("orderId") String orderId);

    /**
     * 查询订单责任信息
     *
     * @param orderId
     * @return
     */
    @Select("select p.companyId,o.productId from sm_order o left join sm_product p on o.productId=p.id where fhOrderId=#{orderId} ")
    FastOrderProduct queryFastProduct(@Param("orderId") String orderId);

    /**
     * 更新订单表的推荐人与管护客户经理(用于管护客户经理与推荐人是同一个人的场景)
     *
     * @param dto
     * @return
     */
    int updateOrderRecommendIdByOrderId(UpdateOrderRecommendDTO dto);

    /**
     * 更新订单表的管护客户经理(用于管护客户经理与推荐人是同一个人的场景)
     *
     * @param dto
     * @return
     */
    int updateOrderCustomerAdminIdByOrderId(UpdateOrderRecommendDTO dto);

    /**
     * 更新订单佣金表的推荐人与管护客户经理(用于管护客户经理与推荐人是同一个人的场景)
     *
     * @param dto
     * @return
     */
    int updateOrderCommissionRecommendIdByOrderId(UpdateOrderRecommendDTO dto);

    /**
     * 根据订单好获取订单基础详情信息
     *
     * @param fhOrderId
     */
    List<OrderBaseDetailDTO> listOrderBaseDetailByOrderId(@Param("fhOrderId") String fhOrderId);

    /**
     * 根据订单好获取保单信息（团险/雇主责任险用）
     *
     * @param fhOrderId
     * @return
     */
    List<SmPolicyInsured> listPolicyInsuredByFhOrderId(@Param("fhOrderId") String fhOrderId);

    /**
     * 活动订单的加佣比例
     *
     * @param orderIdList
     * @return
     */
    List<OrderAddCommissionProportionDTO> getOrderAddCommissionProportion(@Param("orderIdList") List<String> orderIdList);

    /**
     * s54 佣金改造用
     *
     * @param fhOrderId
     * @return
     */
    List<OrderCommissionInsuredInfoDTO> getOrderCommissionInsuredByOrderId(@Param("fhOrderId") String fhOrderId);

    /*****begin s54 佣金配置改造 新版佣金计算提成列表 *******/
    /**
     * 新版佣金计算提成列表
     *
     * @param query
     * @return
     */
    List<SmOrderListVO> listOrderCommissionV3(SmOrderQuery query);

    /**
     * 新版佣金计算
     *
     * @param query
     * @return
     */
    Integer countOrderCommissionV3(SmOrderQuery query);

    /**
     * 新版佣金合计
     *
     * @param query
     * @return
     */
    SmOrderSummaryVO getOrderCommissionSummaryV3(SmOrderQuery query);

    /*****end s54 佣金配置改造 新版佣金计算提成列表 *******/

    /**
     * 查询最大的版本
     *
     * @param productId
     * @param submitTime
     * @return
     */
    Integer queryOrderProductVersion(@Param("productId") Integer productId, @Param("submitTime") Date submitTime);

    /**
     * 客户信息抽取用，每次取300笔
     * 查询未抽取客户信息的订单id
     *
     * @return
     */
    List<String> list30UnExtractFhOrderId();

    /**
     * 获取老的佣金记录
     *
     * @param query
     * @return
     */
    List<SmOrderCommissionDTO> listSmOrderCommission(ManualQueryOldCommissionDTO query);

    SmOrderSummaryVO getOrderSummaryV4(SmOrderV3Query query);

    /**
     * 新版加佣计算提成列表
     *
     * @param query
     * @return
     */
    List<SmOrderListVO> listOrderAddCommission(SmOrderQuery query);

    List<String> listGscLast15Day();

    /**
     * 查询订单渠道基础信息信息(之所以添加这个方法,是因为 getBaseOrderInfoByPolicyNo  这个方法不会返回policyNo)
     *
     * @param policyNo
     * @return
     */
    SmBaseOrderVO getByPolicyNoStatus(@Param("policyNo") String policyNo, @Param("status") String status);

    /**
     * 根据订单编码查询对应保司计划编码
     *
     * @param fhOrderId
     * @return
     */
    @Select("select p.fhProductId  from sm_order o INNER JOIN sm_plan p on p.id  = o.planId where o.fhOrderId  = #{orderId}")
    List<String> getFhProductIdByOrderId(@Param("orderId") String fhOrderId);

    @Select("select o.planId as planId,o.productId as productId,p.state as productState from sm_order o left join sm_product p on o.productId=p.id where fhOrderId=#{orderId} ")
    FastOrderProductPlanDTO queryFastProductPlan(@Param("orderId") String orderId);

    @Select("select t.fhOrderId as fhOrderId,t.productId as productId,t.planId as planId,t.customerAdminId as customerAdminId,t.first_new_order_id as firstNewOrderId from sm_order t where t.fhOrderId = #{orderId} ")
    SmOrderMinInfo querySmOrderMinInfo(@Param("orderId") String orderId);

    /**
     * 按保单号查询
     *
     * @param policyNo
     * @return
     */
    SmOrder queryByPolicyNo(@Param("policyNo") String policyNo);

    /**
     * 按保单号查询
     *
     * @param orderId
     * @return
     */
    SmOrder queryByOrderId(@Param("orderId") String orderId);


    /**
     * 变更保单号
     *
     * @param sourcePolicyNo
     * @param toPolicyNo
     * @return
     */
    @Update(" UPDATE sm_order_correct set policyNo=#{toPolicyNo} where policyNo=#{sourcePolicyNo} ")
    int updateOrderCorrectPolicyNo(@Param("sourcePolicyNo") String sourcePolicyNo, @Param("toPolicyNo") String toPolicyNo);

    /**
     * 变更保单号
     *
     * @param sourcePolicyNo
     * @param toPolicyNo
     * @return
     */
    @Update(" UPDATE sm_order_commission set policyNo=#{toPolicyNo} where policyNo=#{sourcePolicyNo} ")
    int updateOrderCommissionPolicyNo(@Param("sourcePolicyNo") String sourcePolicyNo, @Param("toPolicyNo") String toPolicyNo);

    void updateOrderInterruption(@Param("oldAdminId") String oldAdminId, @Param("newAdminId") String newAdminId);

    void updateHandOrderInterruption(@Param("oldAdminId") String oldAdminId, @Param("newAdminId") String newAdminId, @Param("idNumbers") List<String> idNumbers);

    /**
     * 更新订单的类型
     * @param orderType
     * @param fhOrderId
     * @return
     */
    @Update("UPDATE sm_order SET orderType=#{orderType},update_time=now() " +
            "WHERE fhOrderId=#{fhOrderId} AND orderType!=#{orderType} ;")
    int updateOrderType(@Param("orderType") Integer orderType,
                                      @Param("fhOrderId") String fhOrderId);

    /**
     * 更新订单的类型
     * @param orderType
     * @param fhOrderId
     * @return
     */
    @Update("UPDATE sm_order SET orderType=#{orderType},update_time=now(),is_life_service_order=#{isLifeServiceOrder} " +
            "WHERE fhOrderId=#{fhOrderId} AND orderType!=#{orderType} ;")
    int updateOrderTypeAndIsLifeService(@Param("orderType") Integer orderType,
                        @Param("fhOrderId") String fhOrderId,@Param("isLifeServiceOrder") Integer isLifeServiceOrder);

    /**
     * @Date 2023/11/3
     * @Description
     * @return : java.util.List<java.lang.String>
     */
    @Select("select fhOrderId from sm_order  where productId = #{productId} limit 400")
    List<String> querySelfInsuranceGroupProductCode(@Param("productId") String productId);


    List<CustomerProviderVO> queryPolicyProvideWhaleData(List<String> policyNoList);

    List<SeperateCustomerMangerProviderVO> queryPolicyProvideWhaleDataByEndorId(List<String> endorIdList);

    List<PolicyReferrerFixVo> queryReferrerInfo(@Param("data") List<String> policyNoList);

    List<PolicyReferrerFixVo> policyCorrectReferrerInfo(@Param("data")List<String> endorsementNoList);


    List<SmOrder>  queryRecommendOrgEmptyList(@Param("orderId") String orderId,@Param("startTime") Date startTime,@Param("endTime") Date endTime);

    int updateRecommendOrgEmptyList(SmOrder order);

    List<PolicySettlementTimeFixVo> policySettlementTime(@Param("data")List<String> data);

    List<PolicySettlementTimeFixVo> correctSettlementTime(@Param("data")List<String> data);

    int changePlan(@Param("orderId") String orderId, @Param("plan")SmPlanVO planVO);
}
