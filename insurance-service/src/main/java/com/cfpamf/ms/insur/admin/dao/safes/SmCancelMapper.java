package com.cfpamf.ms.insur.admin.dao.safes;

import com.cfpamf.ms.insur.admin.pojo.dto.cancel.WxCancelMessageDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancel;
import com.cfpamf.ms.insur.admin.pojo.query.SmCancelQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.CancelOrderDetail;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCancelQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.cancel.WxCancelPolicyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 退保
 */
@Mapper
public interface SmCancelMapper extends MyMappler<SmCancel> {


    int updateStateByProInsId(@Param("processInstanceId") String processInstanceId, @Param("taskDefinitionKey") String taskDefinitionKey);

    @Select("select id from sm_cancel order by id desc limit 1")
    Integer getMaxNo();

    /**
     * 查询可以退保的保单
     *
     * @param query
     * @return
     */
    List<WxCancelPolicyVO> listCanCancelPolicy(WxCancelQuery query);


//    /**
//     * 查询可以退保的保单
//     *
//     * @param query
//     * @return
//     */
//    default WxCancelPolicyVO cancelDetailByInsuredId(Integer insuredId) {
//
//    }

    /**
     * 退保id
     *
     * @param cancelId
     * @return
     */
    CancelOrderDetail getCancelOrderDetail(@Param("cancelId") Integer cancelId);

    CancelOrderDetail getCancelDetailByCancelIdAndInsuredId(@Param("cancelId") Integer cancelId, @Param("insuredId") Integer insuredId);

    /**
     * @return
     */
    CancelOrderDetail getCancelOrderDetailByInsuredId(@Param("insuredId") Integer insuredId);

    default SmCancel selectByCancelNo(String cancelNo) {
        SmCancel smCancel = new SmCancel();
        smCancel.setCancelNo(cancelNo);
        return selectOne(smCancel);
    }

    default List<SmCancel> selectByOrderId(String orderId) {
        SmCancel smCancel = new SmCancel();
        smCancel.setFhOrderId(orderId);
        return select(smCancel);
    }

    default SmCancel selectByProcessInstanceId(String processInstanceId) {
        SmCancel smCancel = new SmCancel();
        smCancel.setProcessInstanceId(processInstanceId);
        return selectOne(smCancel);
    }

    /**
     * 后台管理查询
     *
     * @param query
     * @return
     */
    List<WxCancelPolicyVO> listAdminQuery(SmCancelQuery query);

    /**
     * 微信
     *
     * @param processInstanceId
     * @return
     */
    WxCancelMessageDTO getOrgAdminOpenIdByProInsId(String processInstanceId);

    List<SmCancel> selectByInsIdNumberAndOrder(@Param("fhOrderId") String fhOrderId, @Param("idNumbers") List<String> idNumbers);
}
