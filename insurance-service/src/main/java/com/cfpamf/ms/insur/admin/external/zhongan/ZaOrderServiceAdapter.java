package com.cfpamf.ms.insur.admin.external.zhongan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.AmConstants;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderExtendZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMsgMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderOutType;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.enums.order.OrderBindStatusEnum;
import com.cfpamf.ms.insur.admin.event.OrderAppSuccessSmsEvent;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.model.CompanyPolicyInfo;
import com.cfpamf.ms.insur.admin.external.common.model.OrderExtendInfo;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.model.ZaAcceptRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.ZaCheckRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.ZaQueryEPolicyURLResp;
import com.cfpamf.ms.insur.admin.external.zhongan.model.bank.*;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupInsuredInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupOrderRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.notify.ZaAcceptNotify;
import com.cfpamf.ms.insur.admin.external.zhongan.model.notify.ZaCustomerDTO;
import com.cfpamf.ms.insur.admin.external.zhongan.model.renewal.ZaBindCardInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.renewal.ZaRenewalInsured;
import com.cfpamf.ms.insur.admin.external.zhongan.model.renewal.ZaRenewalPolicyInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaCreateFamilyQuestionnaireBody;
import com.cfpamf.ms.insur.admin.external.zhongan.model.wenjuan.ZaFamilySmartUwConclusions;
import com.cfpamf.ms.insur.admin.external.zhongan.util.FileUtils;
import com.cfpamf.ms.insur.admin.external.zhongan.util.ZaAESUtils;
import com.cfpamf.ms.insur.admin.external.zhongan.util.ZaMd5Utils;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingRespDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.za.PaymentDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfig;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.order.*;
import com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendZa;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProduct;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.order.ZaOrderService;
import com.cfpamf.ms.insur.admin.service.order.group.GroupRuleAdaptor;
import com.cfpamf.ms.insur.base.config.mq.MqConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.IdCardUtils;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.constant.EnumEndor;
import com.cfpamf.ms.insur.weixin.constant.za.EnumPayWay;
import com.cfpamf.ms.insur.weixin.constant.za.ZAConstants;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.mq.message.NotifyEPolicyMessage;
import com.cfpamf.ms.insur.weixin.pojo.convertor.ZaConvertor;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OfflinePayDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.SmOrderMinInfo;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.item.EndorCalculation;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.Commodity;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.pay.PaymentReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.request.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceVo;
import com.cfpamf.ms.insur.weixin.service.policy.PolicyService;
import com.cfpamf.ms.pay.facade.constant.PayStatusEnum;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zhongan.filegateway.common.FileUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.config.mq.MqConstants.*;

/**
 * 中华联合保险API接口适配器
 *
 * <AUTHOR>
 */
@Slf4j
@Primary
@Service("za")
public class ZaOrderServiceAdapter extends ZhnxPayOrderServiceAdapter {

    static DateTimeFormatter FMT_PARSE = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    static DateTimeFormatter FMT_SUB = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    SmOrderMapper orderMapper;
    @Autowired
    SmOrderItemMapper orderItemMapper;
    @Autowired
    SmOrderExtendZaMapper extendZaMapper;
    @Autowired
    ZaApiService apiService;
    @Autowired
    private SmProductService productService;
    @Autowired
    AuthUserMapper userMapper;
    @Autowired
    ObjectMapper jsonMapper;

    @Autowired
    private SmCommissionMapper commissionMapper;
    @Autowired
    private SmOrderGroupNotifyMapper smOrderGroupNotifyMapper;
    @Autowired
    private SmOrderGroupNotifyMsgMapper smOrderGroupNotifyMsgMapper;
    @Autowired
    private EndorMapper endorMapper;

    @Autowired
    private ZaOrderService orderService;

    @Autowired
    private RenewalManagerService renewalManagerService;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private PolicyService policyService;

    @Value("${za.occupation.version.change:20220301}")
    private String changeOccupationVersion;

    /**
     * 保险公司职业service
     */
    @Autowired
    private OccupationService occupationService;

    public String decrypt(String content) {
        return apiService.decrypt(content);
    }

    @Override
    public OrderSubmitResponse submitChannelOrder(OrderSubmitRequest request) {
        ZaApiProperties props = apiService.getZaApiProperties();
        request.setOrderOutType(props.getOrderOutType());
        ZaCheckRes check = apiService.check(request);
        OrderSubmitResponse response = new OrderSubmitResponse();
        response.setAppNo(check.getChannelOrderNo());
        response.setNoticeCode(SmConstants.SM_ORDER_API_INVOKE_SUCCESS);
        response.setOrderId(check.getChannelOrderNo());
        //如果 带了智能核保
        if (StringUtils.isNotBlank(request.getQuestionnaireId())) {
            //修改订单号
            extendZaMapper.updateSubmitOrderIdByQuestionnaireId(request.getQuestionnaireId(), check.getChannelOrderNo());
        }
        return response;
    }

    @Override
    public OrderPreAiCheckResp aiCheck(OrderSubmitRequest request, String localProductId) {
        String orderId = orderNoGenerator.getNextNo(EnumChannel.ZA.getCode());
        ZaCreateFamilyQuestionnaireBody family = apiService.createFamily(request, orderId);

        SmOrderExtendZa extendZa = new SmOrderExtendZa();
        extendZa.setPreOrderId(orderId);
        extendZa.setZaPlanId(request.getProductInfo().getProductId());
        extendZa.setQuestionnaireRedirectPage(family.getRedirectPage());
        extendZa.setFamilyQuestionnaireId(family.getFamilyQuestionnaireId());
        extendZa.setQuestionnaireRequestId(family.getFamilyQuestionnaireId());
        extendZaMapper.insert(extendZa);
        OrderPreAiCheckResp aiCheckResp = new OrderPreAiCheckResp();
        aiCheckResp.setOrderId(orderId);
        aiCheckResp.setQuestionnaireId(family.getFamilyQuestionnaireId());
        aiCheckResp.setRedirectPage(family.getRedirectPage());
        return aiCheckResp;
    }

    @Override
    public List<AICheckQueryResponse> aiCheckQuery(AICheckQueryRequest request) {
        String questionnaireId = request.getQuestionnaireId();
        if (StringUtils.isBlank(questionnaireId)) {
            SmOrderExtendZa extendZa = new SmOrderExtendZa();
            extendZa.setPreOrderId(request.getOrderId());
            SmOrderExtendZa s = extendZaMapper.selectOne(extendZa);
            if (Objects.nonNull(s)) {
                questionnaireId = s.getFamilyQuestionnaireId();
            }
        }
        if (StringUtils.isBlank(questionnaireId)) {
            return Collections.emptyList();
        }
        List<ZaFamilySmartUwConclusions> zaFamilySmartUwConclusions = apiService.queryQuestionnaireRes(questionnaireId);
        return zaFamilySmartUwConclusions.stream()
                .map(conclusions -> {
                    AICheckQueryResponse aiCheckQueryResponse = new AICheckQueryResponse();
                    BeanUtils.copyProperties(conclusions, aiCheckQueryResponse);
                    return aiCheckQueryResponse;
                }).collect(Collectors.toList());
    }

    /**
     * 暂不支持团险逻辑处理
     */
    @Override
    public OrderQueryResponse queryChannelOrderInfo(OrderQueryRequest request) {

        if (Objects.equals(request.getProductAttrCode(), EnumProductAttr.GROUP.getCode())) {
            log.info("不支持团险产品的订单处理逻辑-{}", request);
            return null;
        }
        List<SmOrderListVO> smOrderListVOS = orderMapper.listOrderInsuredDetailByOrderId(request.getOrderId());

        if (CollectionUtils.isEmpty(smOrderListVOS)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "订单被保人不存在！");
        }

        SmOrderListVO smOrderListVO = smOrderListVOS.get(0);
        // 初始化返回对象
        OrderQueryResponse response = new OrderQueryResponse();

        OrderQueryResponse.OrderInfo orderInfo = new OrderQueryResponse.OrderInfo();
        OrderQueryResponse.PolicyInfo policyInfo = new OrderQueryResponse.PolicyInfo();

        response.setOrderInfo(orderInfo);
        response.setPolicyInfo(policyInfo);
        response.setNoticeCode(AmConstants.API_FANHUA_SUCCESS_0);
        orderInfo.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);

        List<OrderQueryResponse.PolicyInfo> policyInfos = smOrderListVOS.stream().map(tmpVo -> {
            OrderQueryResponse.PolicyInfo tmp = new OrderQueryResponse.PolicyInfo();
            tmp.setInsuredSn(tmpVo.getInsuredIdNumber());
            tmp.setAppStatus(tmpVo.getAppStatus());
            tmp.setDownloadURL(tmpVo.getDownloadURL());
            return tmp;
        }).collect(Collectors.toList());
        response.setPolicyInfos(policyInfos);

        //如果状态已承保 则说明已经被处理过 直接返回
        orderInfo.setOrderState(smOrderListVO.getPayStatus());
        policyInfo.setAppStatus(smOrderListVO.getAppStatus());
        policyInfo.setPolicyNo(smOrderListVO.getPolicyNo());
        policyInfo.setDownloadURL(smOrderListVO.getDownloadURL());

        //所有人 承包成功 退保成功 退保失败都不会走查询支付 出单等逻辑
        if (smOrderListVOS.stream().allMatch(insured ->
                Objects.equals(insured.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                        || Objects.equals(insured.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)
                        || Objects.equals(insured.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_FAIL))) {
            return response;
        }

        QueryOrderVO queryOrderVO = queryPayment(request);

        if (queryOrderVO == null || !Objects.equals(queryOrderVO.getPayStatus(), PayStatusEnum.PAY_SUCCESS.getStatusCode())) {
            return response;
        }
        /**
         * 支付成功的订单需要重新补偿数据：包括承保，生成电子保单
         */
        SmOrderRenewBindInfo renewBindInfo = smOrderRenewBindService.getByFhOrderId(request.getOrderId());
        if (renewBindInfo != null && Objects.equals(renewBindInfo.getBindStatus(), OrderBindStatusEnum.BIND.getCode())) {
            smOrderListVO.setBindId(renewBindInfo.getId());
            smOrderListVO.setBindStatus(renewBindInfo.getBindStatus());
            smOrderListVO.setBizType(renewBindInfo.getBizType());
        }

        orderInfo.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        for (SmOrderListVO orderListVO : smOrderListVOS) {
            updatePaySuccess(request.getOrderId(), queryOrderVO, orderListVO.getInsuredIdNumber(),
                    SmConstants.POLICY_STATUS_BLANK.equalsIgnoreCase(orderListVO.getAppStatus()));
        }
        log.info("支付成功开始出单{}", request.getOrderId());

        /**
         * 只要有被保人的状态异常，则需要重新处理
         */
        boolean handleFlag = smOrderListVOS.stream().map(SmOrderListVO::getAppStatus).anyMatch(this::needAcceptPolicy);
        if (handleFlag) {
            CompanyPolicyInfo companyPolicyInfo = this.acceptPolicy(response, smOrderListVO, queryOrderVO);
            successPolicy(policyInfo, companyPolicyInfo);

            policyInfos.forEach(tmp -> successPolicy(tmp, companyPolicyInfo));
            if (companyPolicyInfo.isAllSamePolicy()) {
                log.info("投保成功后绑卡信息添加保单号");
                orderMapper.updateOrderPolicyInfo(request.getOrderId(), null, policyInfo);
                //s48 绑卡信息添加保单号(目前众安只支持一个保单号的绑定)
                smOrderRenewBindService.updatePolicyNoByFhOrderId(request.getOrderId(), policyInfo.getPolicyNo());
            } else {

                policyInfos.forEach(tmp -> orderMapper.updateOrderPolicyInfo(request.getOrderId(), tmp.getInsuredSn(), tmp));
                policyInfos.forEach(tmp -> orderItemMapper.updateOrderPolicyInfo(request.getOrderId(), tmp.getInsuredSn(), tmp));
                //s48 绑卡信息添加保单
                List<String> policyNos = policyInfos.stream().map(OrderQueryResponse.PolicyInfo::getPolicyNo).collect(Collectors.toList());
                smOrderRenewBindService.updatePolicyNoListByFhOrderId(request.getOrderId(), policyNos);
            }
        }
        return response;
    }

    @Override
    public void groupApplyFlow(OrderQueryRequest request) {
        String productAttrCode = request.getProductAttrCode();
        if (!Objects.equals(productAttrCode, EnumProductAttr.GROUP.getCode())) {
            return;
        }
        List<SmOrderListVO> smOrderListVOS = orderMapper.listOrderInsuredDetailByOrderId(request.getOrderId());

        if (CollectionUtils.isEmpty(smOrderListVOS)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "订单被保人不存在！");
        }

        SmOrderListVO smOrderListVO = smOrderListVOS.get(0);
        // 初始化返回对象
        OrderQueryResponse response = new OrderQueryResponse();

        OrderQueryResponse.OrderInfo orderInfo = new OrderQueryResponse.OrderInfo();
        OrderQueryResponse.PolicyInfo policyInfo = new OrderQueryResponse.PolicyInfo();

        response.setOrderInfo(orderInfo);
        response.setPolicyInfo(policyInfo);
        response.setNoticeCode(AmConstants.API_FANHUA_SUCCESS_0);
        orderInfo.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);

        List<OrderQueryResponse.PolicyInfo> policyInfos = smOrderListVOS.stream().map(tmpVo -> {
            OrderQueryResponse.PolicyInfo tmp = new OrderQueryResponse.PolicyInfo();
            tmp.setInsuredSn(tmpVo.getInsuredIdNumber());
            tmp.setAppStatus(tmpVo.getAppStatus());
            tmp.setDownloadURL(tmpVo.getDownloadURL());
            return tmp;
        }).collect(Collectors.toList());
        response.setPolicyInfos(policyInfos);

        //如果状态已承保 则说明已经被处理过 直接返回
        orderInfo.setOrderState(smOrderListVO.getPayStatus());
        policyInfo.setAppStatus(smOrderListVO.getAppStatus());
        policyInfo.setPolicyNo(smOrderListVO.getPolicyNo());
        policyInfo.setDownloadURL(smOrderListVO.getDownloadURL());

        //所有人 承包成功 退保成功 退保失败都不会走查询支付 出单等逻辑
        if (smOrderListVOS.stream().allMatch(insured ->
                Objects.equals(insured.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                        || Objects.equals(insured.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)
                        || Objects.equals(insured.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_FAIL))) {
            return;
        }

        QueryOrderVO queryOrderVO = queryPayment(request);

        if (queryOrderVO == null || !Objects.equals(queryOrderVO.getPayStatus(), PayStatusEnum.PAY_SUCCESS.getStatusCode())) {
            return;
        }
        /**
         * 支付成功的订单需要重新补偿数据：包括承保，生成电子保单
         */
        SmOrderRenewBindInfo renewBindInfo = smOrderRenewBindService.getByFhOrderId(request.getOrderId());
        if (renewBindInfo != null && Objects.equals(renewBindInfo.getBindStatus(), OrderBindStatusEnum.BIND.getCode())) {
            smOrderListVO.setBindId(renewBindInfo.getId());
            smOrderListVO.setBindStatus(renewBindInfo.getBindStatus());
            smOrderListVO.setBizType(renewBindInfo.getBizType());
        }

        orderInfo.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        for (SmOrderListVO orderListVO : smOrderListVOS) {
            updatePaySuccess(request.getOrderId(), queryOrderVO, orderListVO.getInsuredIdNumber(),
                    SmConstants.POLICY_STATUS_BLANK.equalsIgnoreCase(orderListVO.getAppStatus()));
        }
        log.info("支付成功开始出单{}", request.getOrderId());

        /**
         * 只要有被保人的状态异常，则需要重新处理
         */
        boolean handleFlag = smOrderListVOS.stream().map(SmOrderListVO::getAppStatus).anyMatch(this::needAcceptPolicy);
        if (handleFlag) {
            this.acceptGroupPolicy(response, smOrderListVO, queryOrderVO);
        }
    }

    /**
     * 众安-个险出单流程
     *
     * @param response     查询接口
     * @param orderInfo    订单信息
     * @param queryOrderVO 查询对象
     * @return
     */
    @Override
    protected CompanyPolicyInfo acceptPolicy(OrderQueryResponse response, SmOrderListVO orderInfo, QueryOrderVO queryOrderVO) {
        CompanyPolicyInfo res = new CompanyPolicyInfo();
        List<OrderQueryResponse.PolicyInfo> policyInfos = response.getPolicyInfos();
        try {
            boolean fixFlag = !policyInfos.stream().allMatch(policyInfo -> StringUtils.isNotBlank(policyInfo.getPolicyNo()));
            if (fixFlag) {
                ZaAcceptRes accept = apiService.accept(response, orderInfo, queryOrderVO);
                res.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
                List<CompanyPolicyInfo> collect = accept.getPolicies().stream()
                        .map(policy -> {
                            CompanyPolicyInfo model = new CompanyPolicyInfo();
                            model.setIdNumber(policy.getCertNo());
                            model.setPolicyNo(policy.getPolicyNo());
                            model.setInsuredDate(new Date());
                            model.setAppStatus(SmConstants.POLICY_STATUS_PROCESS);
                            return model;
                        }).collect(Collectors.toList());
                res.setInsuredPolicyInfos(collect);
            } else {
                List<CompanyPolicyInfo> lists = policyInfos.stream()
                        .map(policyInfo -> {
                            CompanyPolicyInfo model = new CompanyPolicyInfo();
                            model.setIdNumber(policyInfo.getInsuredSn());
                            model.setPolicyNo(policyInfo.getPolicyNo());
                            model.setAppStatus(policyInfo.getAppStatus());
                            model.setPolicyUrl(policyInfo.getDownloadURL());
                            model.setInsuredDate(policyInfo.getInsuredDate());
                            return model;
                        }).collect(Collectors.toList());
                res.setInsuredPolicyInfos(lists);
            }
        } catch (Exception e) {
            log.error("出单失败:{},{}", orderInfo.getFhOrderId(), orderInfo.getFhProductId(), e);
            res.setAppStatus(SmConstants.POLICY_STATUS_FAIL);
        }
        try {
            //如果有保单号没有电子保单 那么就查询电子保单地址
            List<CompanyPolicyInfo> insuredPolicyInfos = res.getInsuredPolicyInfos();

            if (!CollectionUtils.isEmpty(insuredPolicyInfos)) {
                insuredPolicyInfos.stream()
                        .filter(tmp ->
                                //过滤 有保单号没保单地址的数据
                                StringUtils.isNotBlank(tmp.getPolicyNo()) && StringUtils.isBlank(tmp.getPolicyUrl()))
                        .forEach(tmp -> {
                            ZaQueryEPolicyURLResp queryEPolicyURLResp = apiService.queryEPolicyURL(tmp.getPolicyNo());
                            //状态修改成出单
                            tmp.setPolicyUrl(queryEPolicyURLResp.getEPolicyURL());
                            tmp.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
                        });
            }
        } catch (Exception e) {
            log.warn("获取众安电子保单失败:{}", orderInfo.getFhOrderId(), e);
        }
        return res;
    }

    public CompanyPolicyInfo acceptGroupPolicy(OrderQueryResponse response, SmOrderListVO orderInfo, QueryOrderVO queryOrderVO) {
        CompanyPolicyInfo res = new CompanyPolicyInfo();
        List<OrderQueryResponse.PolicyInfo> policyList = response.getPolicyInfos();
        try {
            /**
             * 只要有一个被保人的状态不是承保成功，则需要重新出单处理
             */
            boolean fixFlag = policyList.stream().anyMatch(policy -> StringUtils.isBlank(policy.getPolicyNo()));
            if (fixFlag) {
                ZaQuoteResp data = apiService.groupApply(orderInfo, queryOrderVO);
                Date payTime = queryOrderVO.getPayTime();
                String orderId = orderInfo.getFhOrderId();
                String policyNo = data.getPolicyNo();
                EnumPayWay payWay = EnumPayWay.convertEnum(queryOrderVO.getPayType());

                boolean handleFlag = orderService.afterApply(payTime, orderId, policyNo, payWay);
                if (handleFlag) {
                    log.info("开始推送承保成功短信:{}", orderId);
                    busEngine.publish(new OrderCommissionChangeEvent(orderId));
                    busEngine.publish(new OrderAppSuccessSmsEvent(orderId));
                    orderMapper.updateNotifyState(orderId, 10, null);

                    NotifyEPolicyMessage msg = new NotifyEPolicyMessage(EnumChannel.ZA, orderId);
                    rabbitTemplate.sendMessage(msg, EXC_DELAY_INS, ROUTKEY_INS, m -> {
                        m.getMessageProperties().setHeader(EXC_DELAY_HEADER, MqConstants.NOTIFY_EPOLICY_DELAY_TIME);
                        return m;
                    });
                }
            }
        } catch (Exception e) {
            log.error("团险-出单失败:{},{}", orderInfo.getFhOrderId(), orderInfo.getFhProductId(), e);
        }
        return res;
    }


    public String decryptAes(String aesContent) throws IOException {
        return ZaAESUtils.decryptBase64(apiService.getZaApiProperties().getNotifyKey(), aesContent);
    }

    public String decryptRefundAes(String aesContent) throws IOException {
        return ZaAESUtils.decryptBase64(apiService.getZaApiProperties().getCancelNotifyKey(), aesContent);
    }

    /**
     * 根据明文数据转换成insert对象
     */
    public SmCreateOrderSubmitRequest cvtNotify(ZaAcceptNotify notify) {

        SmCreateOrderSubmitRequest res = new SmCreateOrderSubmitRequest();

        res.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());
        res.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        res.setFhOrderId(orderNoGenerator.getNextNo(EnumChannel.ZA.getCode()));
        res.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        res.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        res.setAppNo(notify.getOrderNo());
        res.setOrderState("0");
        res.setNoticeCode("");
        res.setChannel(EnumChannel.ZA.getCode());
        res.setSubChannel(EnumOrderSubChannel.H5.getCode());
        res.setNoticeMsg("");


        FhOrderInfo fhOrderInfo = new FhOrderInfo();

        //目前来看数据长度都是1
        fhOrderInfo.setTotalAmount(new BigDecimal(notify.getTotalAmount()));
        fhOrderInfo.setStartTime(notify.getEffectiveTime());

        fhOrderInfo.setSubmitTime(FMT_SUB.format(FMT_PARSE.parse(notify.getApplyTime())));
        fhOrderInfo.setEndTime(notify.getExpiryTime());
        //这两个没有
        fhOrderInfo.setUnderWritingAge("");
        fhOrderInfo.setValidPeriod("");
        res.setOrderInfo(fhOrderInfo);

        res.setProductId(notify.getPlanCode());

        FhProduct product = new FhProduct();
        product.setProductId(notify.getPlanCode());

        if (StringUtils.isNotBlank(notify.getChannelExtraInfo())) {
            JSONObject jsonObject = JSONObject.parseObject(notify.getChannelExtraInfo());
            String ext = jsonObject.getString("ext");
            if (StringUtils.isNotBlank(ext)) {
                product.setRecommendId(userMapper.getMainJobNumberByBizCode(ext));
            }
        }

        res.setProductInfo(product);
        res.setQty(1);

        res.setProposerInfo(cvtPeople(notify.getPolicyHolder()));

        List<FhInsuredPerson> insureds = notify.getInsuredList().stream()
                .map(zaModel -> {
                    //这里有一些保单独有的属性了
                    FhInsuredPerson insuredPerson = cvtPeople(zaModel.getInsured());
                    insuredPerson.setPolicyNo(zaModel.getPolicyNo());
                    insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_PROCESS);
                    List<OrderExtendInfo> extendInfos = Lists.newArrayListWithCapacity(3);
                    extendInfos.add(new OrderExtendInfo("premium", zaModel.getPremium()));
                    extendInfos.add(new OrderExtendInfo("totalPremium", zaModel.getTotalPremium()));
                    insuredPerson.setExtendInfoList(extendInfos);
                    return insuredPerson;
                }).collect(Collectors.toList());

        res.setInsuredPerson(insureds);
        return res;
    }

    public static FhInsuredPerson cvtPeople(ZaCustomerDTO dto) {
        FhInsuredPerson fhInsuredPerson = ZaConvert.CNT.cvtPerson(dto);
        if (Objects.equals(dto.getCertType(), "I")) {
            String idNo = IdcardUtils.getBirthByIdCard(dto.getCertNo());
            fhInsuredPerson.setBirthday(idNo.substring(0, 4) + "-" + idNo.substring(4, 6) + "-" + idNo.substring(6, 8));
            fhInsuredPerson.setPersonGender(IdcardUtils.getGenderByIdCard(dto.getCertNo()));
        }
        return fhInsuredPerson;
    }

    public ZaGroupOrderRes queryChannelGroupOrder(String groupPolicyNo) {
        ZaGroupOrderRes groupOrderRes = apiService.queryGroupOrder(groupPolicyNo);
        log.info("groupOrderRes = {}", groupOrderRes);
        return groupOrderRes;
    }

    public ZaGroupEndorsementRes queryChannelGroupEndorsement(String groupEndorsementNo) {
        ZaGroupEndorsementRes endorsementRes = apiService.queryGroupEndorsement(groupEndorsementNo);
        log.info("endorsementRes = {}", endorsementRes);
        return endorsementRes;
    }


//    public SmCreateOrderSubmitRequest cvtByGroupOrderNotify(ZaGroupOrderRes groupOrderRes, Map<String, SmPlanVO> planMap, List<SmCommonSettingVO> commonSettingList, String recommendId) {
//        ZaGroupOrderInfo groupOrderInfo = groupOrderRes.getResult();
//        SmCreateOrderSubmitRequest submitReq = initGroupSubmitRequest(orderNoGenerator.getNextNo(EnumChannel.ZA.getCode()));
//        //取第一个计划，必须保证planMap的计划的佣金比例需要一致，且属于同一个产品
//        //订单信息
//        FhOrderInfo fhOrderInfo = new FhOrderInfo();
//        fhOrderInfo.setTotalAmount(groupOrderInfo.getPolicyAmount());
//
//        fhOrderInfo.setSubmitTime(groupOrderInfo.getApplyTime());
//        fhOrderInfo.setStartTime(groupOrderInfo.getValidateTime());
//        fhOrderInfo.setEndTime(groupOrderInfo.getExpireTime());
//        submitReq.setOrderInfo(fhOrderInfo);
//
//        //订单明细
//        String fhProductId = planMap.keySet().iterator().next();
//        SmPlanVO planVO = planMap.get(fhProductId);
//        submitReq.setOrderItemList(getBatchIncreaseList(submitReq.getFhOrderId(), groupOrderInfo.getHaGroupPolicyNo(), "", groupOrderInfo.getInsuredList(), 0));
//
//        //保险产品信息
//        FhProduct productInfo = new FhProduct();
//        productInfo.setRecommendId(recommendId);
//        productInfo.setProductId(fhProductId);
//        submitReq.setProductInfo(productInfo);
//        submitReq.setProductId(fhProductId);
//
//        //投保人信息
//        submitReq.setProposerInfo(createFhPropose(groupOrderInfo, commonSettingList));
//
//        //被保人
//        List<FhInsuredPerson> insuredPersonList = groupOrderInfo.getInsuredList().stream()
//                .map(groupInsuredInfo -> {
//                    FhInsuredPerson insuredPerson = ZaConvert.CNT.cvtGroupInsured(groupInsuredInfo);
//                    insuredPerson.setPolicyNo(groupOrderInfo.getHaGroupPolicyNo());
//                    return insuredPerson;
//                }).collect(Collectors.toList());
//        submitReq.setInsuredPerson(insuredPersonList);
//
//        submitReq.setQty(1);
//
//        return submitReq;
//    }
//
//
//    private FhProposer createFhPropose(ZaGroupOrderInfo groupOrderInfo, List<SmCommonSettingVO> commonSettingList) {
//        FhProposer fhProposer = new FhProposer();
//        ZaGroupHoldInfo holdInfo = groupOrderInfo.getPolicyHolder();
//        fhProposer.setPersonName(holdInfo.getHolderName());
//        fhProposer.setCellPhone(holdInfo.getContactMobile());
//        Map<String, String> certificateEntry = holdInfo.getCertificateEntry();
//        //List<SmCommonSettingVO> commonSettingList = super.mapperOption2Company(EnumCompanyCode.ZA.getId());
//        Set<String> keySet = certificateEntry.keySet();
//        SmCommonSettingVO vo = null;
//        String certificateCode = "";
//        for (String key : keySet) {
//            Optional<SmCommonSettingVO> opt = commonSettingList.stream().filter(setting -> Objects.equals(key, setting.getOptionName())).findFirst();
//            if (opt.isPresent()) {
//                vo = opt.get();
//                certificateCode = certificateEntry.get(key);
//                break;
//            }
//        }
//        if (vo != null) {
//            fhProposer.setIdType(vo.getOptionCode());
//            fhProposer.setIdNumber(certificateCode);
//        } else {
//            fhProposer.setIdType("");
//            fhProposer.setIdNumber(certificateEntry.get(keySet.iterator().next()));
//        }
//        return fhProposer;
//    }
//
//    public List<SmCreateOrderSubmitRequest> cvtByGroupEndorsementNotify(String policyNo, ZaGroupEndorsementRes endorsementRes, SmBaseOrderVO baseOrder, SmOrderApplicant applicant, SmPlanVO planVO) {
//        ZaGroupEndorsementInfo endorsementInfo = endorsementRes.getResult();
//        List<ZaGroupInsuredInfo> zaInsuredInfoList = endorsementInfo.getInsuredList();
//        //批增(状态为有效，且失效时间为空，因为退保时，分单的状态不会变)
//        List<ZaGroupInsuredInfo> batchIncreList = zaInsuredInfoList.stream().filter(groupInsuredInfo ->
//                Objects.equals(groupInsuredInfo.getIndividualStatus(), ZaGroupPolicyStatusEnum.INFORCE.getCode())
//                        && StringUtils.isBlank(groupInsuredInfo.getTerminateTime())).collect(Collectors.toList());
//        //批减(状态为失效，或者失效时间不为空)
//        List<ZaGroupInsuredInfo> batchDecreList = zaInsuredInfoList.stream().filter(groupInsuredInfo ->
//                Objects.equals(groupInsuredInfo.getIndividualStatus(), ZaGroupPolicyStatusEnum.TERMINATED.getCode())
//                        || StringUtils.isNotBlank(groupInsuredInfo.getTerminateTime())).collect(Collectors.toList());
//        //+2是因为一个bug导致部分订单下标已经超过count的个数2个位置
//        Integer count = orderMapper.countFhOrderIdLikeByOrderId(baseOrder.getFhOrderId()) + 2;
//
//
//        //批增是一个单
//        List<SmCreateOrderSubmitRequest> list = new ArrayList<>();
//        //String increaseOrderId = baseOrder.getFhOrderId()+"_"+count;
//        SmCreateOrderSubmitRequest increase = zcvtByGroupBatchIncrease(count, policyNo, endorsementRes, batchIncreList, baseOrder, applicant, planVO);
//        if (increase != null) {
//            list.add(increase);
//            count = count + 1;
//        }
//        //批减是每一个被保人为一单
//        //String decreaseOrderId = baseOrder.getFhOrderId()+"_"+(count+1);
//
//        List<SmCreateOrderSubmitRequest> decrease = cvtListByGroupBatchDecrease(count, policyNo, endorsementRes, batchDecreList, baseOrder, applicant, planVO);
//        if (decrease != null) {
//            list.addAll(decrease);
//        }
//
//        return list;
//    }


//    /**
//     * @param fhOrderId
//     * @param policyNo
//     * @param endorsementInfo
//     * @param batchDecreList
//     * @param smOrder
//     * @param applicant
//     * @return
//     */
//    public SmCreateOrderSubmitRequest cvtByGroupBatchDecrease(String fhOrderId, String policyNo, ZaGroupEndorsementInfo endorsementInfo, List<ZaGroupInsuredInfo> batchDecreList, SmBaseOrderVO smOrder, SmOrderApplicant applicant, SmPlanVO planVO) {
//        if (CollectionUtils.isEmpty(batchDecreList)) {
//            return null;
//        }
//
//        SmCreateOrderSubmitRequest submitReq = initGroupSubmitRequest(fhOrderId);
//
//        List<String> idNumberList = batchDecreList.stream().map(ZaGroupInsuredInfo::getCertNo).collect(Collectors.toList());
//        List<SmOrderItem> orderItemList = super.listOriginalPolicyByOrderId(smOrder.getFhOrderId(), idNumberList);
//
//        List<SmOrderItem> batchDecreaseList = new ArrayList<>();
//        batchDecreList.stream().forEach(insured -> {
//            Optional<SmOrderItem> opt = orderItemList.stream().filter(item -> Objects.equals(insured.getCertNo(), item.getIdNumber())).findFirst();
//            SmOrderItem decrItem = new SmOrderItem();
//            if (opt.isPresent()) {
//                SmOrderItem o = opt.get();
//                decrItem.setThPolicyNo(o.getThPolicyNo());
//                decrItem.setThEndorsementNo(endorsementInfo.getEndorsementNo());
//                decrItem.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
//                decrItem.setIdType(insured.getCertType());
//                decrItem.setIdNumber(insured.getCertNo());
//                decrItem.setPlanCode(insured.getPlanCode());
//                decrItem.setPolicyNo(endorsementInfo.getEndorsementNo() + ENDTOSEMENT_DECRE);
//                BigDecimal t = o.getTotalAmount().subtract(insured.getTotalPremium());
//                if (t.compareTo(BigDecimal.ZERO) < 0) {
//                    t = BigDecimal.ZERO;
//                }
//                decrItem.setTotalAmount(t);
//                decrItem.setEndorsementAmount(insured.getTotalPremium());
//                batchDecreaseList.add(decrItem);
//            } else {
//                log.error("众安团险保单号为{}的批单信息，批单号为{}中用户{}证件号为{}的原始投保信息未找到", policyNo, endorsementInfo.getEndorsementNo(), insured.getName(), insured.getCertNo());
//                throw new BizException("", "用户原始保单信息未找到");
//            }
//        });
//        BigDecimal totalAmount = batchDecreaseList.stream().map(SmOrderItem::getTotalAmount).reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
//
//        //订单信息
//        FhOrderInfo fhOrderInfo = new FhOrderInfo();
//
//        fhOrderInfo.setTotalAmount(totalAmount);
//        fhOrderInfo.setSubmitTime(DateUtils.format(smOrder.getSubmitTime()));
//        fhOrderInfo.setStartTime(DateUtils.format(smOrder.getStartTime()));
//        fhOrderInfo.setEndTime(endorsementInfo.getValidateTime());
//        submitReq.setOrderInfo(fhOrderInfo);
//
//
//        //保险产品信息
//        //Map<String, SmPlanVO> planMap = getSmPlan(groupOrderInfo);
//        FhProduct productInfo = new FhProduct();
//        //批减单
//        productInfo.setRecommendId(smOrder.getRecommendId());
//
//        //取第一个计划，必须保证planMap的计划的佣金比例需要一致，且属于同一个产品
//        productInfo.setProductId(planVO.getFhProductId());
//        submitReq.setProductInfo(productInfo);
//        submitReq.setProductId(planVO.getFhProductId());
//
//        //投保人信息
//        submitReq.setProposerInfo(createEndtosementFhPropose(applicant));
//        //被保人
//        List<FhInsuredPerson> insuredPersonList = batchDecreList.stream()
//                .map(groupInsuredInfo -> {
//                    FhInsuredPerson insuredPerson = ZaConvert.CNT.cvtGroupInsured(groupInsuredInfo);
//                    insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
//                    insuredPerson.setSurrenderTime(DateUtils.parse(groupInsuredInfo.getTerminateTime()));
//                    insuredPerson.setPolicyNo(endorsementInfo.getEndorsementNo() + ENDTOSEMENT_DECRE);
//                    return insuredPerson;
//                }).collect(Collectors.toList());
//        submitReq.setInsuredPerson(insuredPersonList);
//        submitReq.setQty(1);
//
//        return submitReq;
//    }


    /**** s48 绑卡/自动续保 ****/
    /**
     * 查询保司绑卡支持的银行列表
     *
     * @return
     */
    public BankListVO queryBankList() {
        ZaBankListRes bankBaseRes = apiService.queryBankList();
        BankListVO bankListVO = new BankListVO();
        if (bankBaseRes.isSuccess()) {
            String bankStr = bankBaseRes.getResult();
            ZaBankList banks = JSON.parseObject(JSON.parse(bankStr).toString(), new TypeReference<ZaBankList>() {
            });
            List<ZaBankList.BankInfo> zaBankInfoList = banks.getBankList();
            if (!CollectionUtils.isEmpty(zaBankInfoList)) {
                List<BankInfoVO> bankInfoVOS = Lists.newArrayListWithCapacity(zaBankInfoList.size());
                zaBankInfoList.forEach(bankInfo -> {
                    BankInfoVO vo = new BankInfoVO();
                    vo.setBankName(bankInfo.getBankName());
                    vo.setBankCode(bankInfo.getBankCode());
                    bankInfoVOS.add(vo);
                });
                bankListVO.setBankList(bankInfoVOS);
            }
        }
        return bankListVO;
    }

    /**
     * 获取银行绑定协议号
     *
     * @param cardBindingReqDTO
     * @return
     */
    public BankCardBindingRespDTO bindingCard(BankCardBindingReqDTO cardBindingReqDTO) {
        if (cardBindingReqDTO == null) {
            return null;
        }
        ZaCardBindReq req = new ZaCardBindReq();
        req.setAccountName(cardBindingReqDTO.getAccountName());
        req.setAccountNo(cardBindingReqDTO.getAccountNo());
        req.setCertiNo(cardBindingReqDTO.getIdNumber());
        req.setPhoneNo(cardBindingReqDTO.getMobile());
        req.setTransNo(cardBindingReqDTO.getTransNo());
        ZaCardBindRes res = apiService.bindingCard(req);

        if (res.isSuccess()) {
            BankCardBindingRespDTO respDTO = new BankCardBindingRespDTO();
            respDTO.setProtocolNo(res.getProtocolNo());
            return respDTO;
        }
        return null;
    }

    /**
     * 团险报价
     * TODO 报价数据和暂存订单都是为了提升用户体验，建议使用mongdb存储
     *
     * @param req
     * @return
     * <AUTHOR>
     */
    @Override
    public GroupQuoteResponse quotePrice4Group(GroupUnderwriting req) {
        fixDuty(req);
        //众安团险续保校验当前订单被保人职业等级与上年被保人职业等级是否匹配, 如果当年等级在上年找不断哦则报错
        checkOccupationGroupListByOrderId(req);
        ZaApiProperties props = apiService.getZaApiProperties();
        List<SysDutyConfig> dutys = getSysDutys(EnumChannel.ZA.getCode());
        ZaGroupUnderwritingReq rew = buildQuoteRequest(props, req, dutys);

        GroupQuoteResponse groupQuoteResponse = null;
        ZaQuoteResp resp = null;
        if (!req.isRealRenewFlag()) {
            resp = apiService.quotePrice4Group(rew);
            if (!resp.isSuccess()) {
                throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
            }
            groupQuoteResponse = resp.convertVo();
        } else {
            groupQuoteResponse = buildGroupQuoteResponseForRenewal();
            //如果是续保逻辑则从续保申请接口读取保费
            checkGroupPremium(req, groupQuoteResponse);
        }
        return groupQuoteResponse;
    }

    /**
     * 2022-05-31
     * （众安临时方案）
     * 强制刷新责任因子：部分特殊责任，他的责任因子也是自己本身
     *
     * @param req
     */
    public void fixDuty(GroupUnderwriting req) {
        ProductReq product = req.getProduct();
        List<Clause> clauseList = product.getClauseList();
        if (clauseList == null) {
            return;
        }
        for (Clause entry : clauseList) {
            List<Liability> liabilityList = entry.getLiabilityList();
            if (liabilityList == null) {
                continue;
            }
            for (Liability liab : liabilityList) {
                if ("ZXG156".equals(liab.getLiabCode()) && liab.getLiabAmount() != null) {
                    replaceFactor(liab);
                }
            }
        }
    }

    /**
     * 替换责任因子
     *
     * @param liab
     */
    private void replaceFactor(Liability liab) {
        List<Factor> liabFactorList = liab.getLiabFactorList();
        Factor factor = new Factor();
        factor.setFactorCode("Daily hospitalization allowance");
        factor.setFactorValue(liab.getLiabAmount().toString());
        if (liabFactorList == null) {
            liabFactorList = safePut(liabFactorList, factor);
            liab.setLiabFactorList(liabFactorList);
            return;
        }
        Map<String, Factor> factorMap = LambdaUtils.safeToMap(liabFactorList, Factor::getFactorCode);
        Factor rawFactor = factorMap.get("Daily hospitalization allowance");
        if (rawFactor != null) {
            rawFactor.setFactorValue(String.valueOf(liab.getLiabAmount()));
        } else {
            liabFactorList.add(factor);
        }
        liab.setLiabFactorList(liabFactorList);
    }

    private <T> List<T> safePut(List<T> container, T data) {
        if (container == null) {
            container = new ArrayList<>();
        }
        container.add(data);
        return container;
    }

    private ZaGroupUnderwritingReq buildQuoteRequest(ZaApiProperties zaApiProperties,
                                                     GroupUnderwriting req,
                                                     List<SysDutyConfig> dutys) {
        ZaGroupUnderwritingReq rew = ZaConvertor.convertQuoteBean(zaApiProperties, dutys, req);
        rew.setEnterpriseRiskAdjustmentFactor(req.getEnterpriseRiskFactor());
        return rew;
    }


    /**
     * 验证绑定结果
     *
     * @param protocloNo
     * @param bankSms
     * @return
     */
    public boolean validateBindRet(String protocloNo, String bankSms) {
        ZaBankBaseRes res = apiService.validateBind(protocloNo, bankSms);
        return res.isSuccess();
    }

    /**
     * md5验签
     *
     * @param content
     * @param signStr
     * @return
     */
    public boolean checkMd5Sign(String content, String signStr) {
        try {
            /*String cont = content;
            if(apiService.getZaApiProperties().getBindSignKey()!=null){
                cont = content+apiService.getZaApiProperties().getBindSignKey();
            }*/
            String genSignStr = genSign(content);
            //log.info("绑卡回调签名串：{}",genSignStr);
            //log.info("绑卡回调签名串转大写：{}",genSignStr.toUpperCase());
            if (Objects.equals(signStr, genSignStr.toUpperCase())) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Boolean.FALSE;
    }

    public String genToInsurCompanyBindUrl(String policyNo) throws Exception {
        Map<String, String> param = new TreeMap<String, String>();
        param.put("channelCode", apiService.getZaApiProperties().getBindChannelCode());
        String channelUrserId = apiService.getZaApiProperties().getBindChannelUserId();
        if (StringUtils.isNotBlank(channelUrserId)) {
            param.put("channelUserId", channelUrserId);
        }
        param.put("policyNo", policyNo);
        param.put("successUrl", URLEncoder.encode(apiService.getZaApiProperties().getBindCallbackUrl(), "UTF-8"));
        String paramUrl = buildParamUrl(param);
        String sign = genSign(paramUrl);
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(apiService.getZaApiProperties().getBindH5Url());
        sbUrl.append(paramUrl);
        sbUrl.append("&sign=").append(sign);
        log.info("保障中保单的绑卡地址:{}", sbUrl.toString());
        return sbUrl.toString();
    }

    private String genSign(String content) throws Exception {
        String cont = content;
        if (apiService.getZaApiProperties().getBindSignKey() != null) {
            cont = content + apiService.getZaApiProperties().getBindSignKey();
        }
        return ZaMd5Utils.md5(cont);
    }

    private String buildParamUrl(Map<String, String> querys) {
        StringBuilder sbQuery = new StringBuilder("");
        if (null != querys) {
            for (Map.Entry<String, String> query : querys.entrySet()) {
                if (0 < sbQuery.length()) {
                    sbQuery.append("&");
                }
                if (StringUtils.isNotBlank(query.getKey()) && StringUtils.isNotBlank(query.getValue())) {
                    sbQuery.append(query.getKey());
                    sbQuery.append("=");
                    sbQuery.append(query.getValue());
                }
            }
        }
        return sbQuery.toString();
    }

    public String genRedirectUrl() {

        return apiService.getZaApiProperties().getBindRedirectUrl();
    }


    /******** begin s50 众安续保保单信息 *******/
    /**
     * 生成保司续保投保地址
     *
     * @param policyNo        保单号
     * @param customerAdminId 管护客户经理
     * @param userId          被保人的用户id(暂无，以后存在c端用户体系时可传)
     * @return
     */
    public String genToRenewalUrl(String policyNo, String customerAdminId, String userId) throws Exception {
        Map<String, String> param = new TreeMap<String, String>();
        //渠道编号,由众安分配，需要与保司确认是否与绑卡的channelCode编码一直
        param.put("channelCode", apiService.getZaApiProperties().getBindChannelCode());
        if (StringUtils.isNotBlank(userId)) {
            param.put("channelUserId", userId);
        }
        //原保单号
        param.put("renewPolicyNo", policyNo);
        //通知信息，续保成功回传信息，填写管护客户经理
        param.put("notifyContent", customerAdminId);
        String paramUrl = buildParamUrl(param);
        String sign = genSign(paramUrl);
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(apiService.getZaApiProperties().getRenewalH5Url());
        sbUrl.append(paramUrl);
        sbUrl.append("&sign=").append(sign);
        log.info("续保投保地址:{}", sbUrl.toString());
        return sbUrl.toString();
    }

    public SmCreateOrderSubmitRequest cvtRenewalOrder(ZaRenewalPolicyInfo policyInfo, SmPlanVO planVO) {
        String orderId = orderNoGenerator.getNextNo(EnumChannel.ZA.getCode());
        SmCreateOrderSubmitRequest submitReq = initRenewalOrderSubmitRequest(orderId, policyInfo.getApplyNo());
        //订单信息
        FhOrderInfo fhOrderInfo = new FhOrderInfo();

        fhOrderInfo.setTotalAmount(new BigDecimal(policyInfo.getPremium()));
        fhOrderInfo.setSubmitTime(DateUtil.format(millSecondToDate(Long.valueOf(policyInfo.getOrderTime())), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setStartTime(policyInfo.getEffectiveDate());
        fhOrderInfo.setEndTime(policyInfo.getExpiryDate());
        fhOrderInfo.setUnderWritingAge("");
        //fhOrderInfo.setValidPeriod(riskInfo.getInsureYears()+riskInfo.getInsureYearsIntv());
        fhOrderInfo.setInsuredAmount(new BigDecimal(policyInfo.getAmount()));

        submitReq.setOrderInfo(fhOrderInfo);


        //保险产品信息
        FhProduct productInfo = new FhProduct();
        String productId = policyInfo.getPlanCode();
        submitReq.setProductId(productId);
        productInfo.setRecommendId(policyInfo.getNotifyContent());

        if (StringUtils.isBlank(productInfo.getRecommendId())) {
            //新增被保人跟随续保保单投保的,原保单号是不存在，只有管理保单号TagPolicyNo，这是就需要取关联保单号的管护经理
            if (StringUtils.isBlank(policyInfo.getOriginalPolicyNo())) {
                //新单跟随原保单标识,新增被保人保单有值，代表跟随哪张原保单续保时，新增的被保人
                productInfo.setRecommendId(renewalLastOrderRecommendInfo(policyInfo.getTagPolicyNo()));
            } else {
                productInfo.setRecommendId(renewalLastOrderRecommendInfo(policyInfo.getOriginalPolicyNo()));
            }
        }

        productInfo.setProductId(productId);
        submitReq.setQty(1);
        submitReq.setProductInfo(productInfo);

        //投保人信息
        FhProposer fhProposer = ZaConvert.CNT.cvtProposer(policyInfo.getHolder());
        submitReq.setProposerInfo(fhProposer);

        //被保险人信息
        submitReq.setInsuredPerson(createInsuredPersonList(policyInfo.getInsured(), policyInfo.getPolicyNo(), policyInfo.getElePolicyUrl(), policyInfo.getRenewalPolicyNo()));
        //绑卡信息
        if (Objects.equals(policyInfo.getIsAutoPay(), "Y")) {
            submitReq.setRenewBindInfo(createBindCardInfo(policyInfo.getBindCardInfo(), policyInfo.getPolicyNo(), orderId));
        }


        return submitReq;
    }

    private String renewalLastOrderRecommendInfo(String originalPolicyNo) {
        if (StringUtils.isBlank(originalPolicyNo)) {
            return null;
        }
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByPolicyNo(originalPolicyNo, null);
        if (Objects.nonNull(baseOrderVO) && Objects.equals(SmConstants.ORDER_STATUS_PAYED, baseOrderVO.getPayStatus())) {
            return baseOrderVO.getCustomerAdminId();
        }
        return null;
    }

    /**
     * 初始化
     *
     * @param fhOrderId 订单号
     * @param appNo     保司投保单号
     * @return
     */
    private SmCreateOrderSubmitRequest initRenewalOrderSubmitRequest(String fhOrderId, String appNo) {
        SmCreateOrderSubmitRequest submitReq = new SmCreateOrderSubmitRequest();

        submitReq.setChannel(EnumChannel.ZA.getCode());
        submitReq.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());
        submitReq.setFhOrderId(fhOrderId);
        submitReq.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        submitReq.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        submitReq.setAppNo(appNo);
        submitReq.setNoticeCode("");
        submitReq.setSubChannel(EnumOrderSubChannel.H5.getCode());
        submitReq.setNoticeMsg("");
        return submitReq;
    }

    /**
     * 被保险人信息
     *
     * @param zaRenewalInsured
     * @param policyNo
     * @return
     */
    private List<FhInsuredPerson> createInsuredPersonList(ZaRenewalInsured zaRenewalInsured, String policyNo, String policyUrl, String oldPolicyNo) {
        FhInsuredPerson insuredPerson = ZaConvert.CNT.cvtRenewalInsured(zaRenewalInsured);
        insuredPerson.setPolicyNo(policyNo);
        insuredPerson.setOldPolicyNo(oldPolicyNo);
        insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_PROCESS);
        insuredPerson.setDownloadURL(policyUrl);
        List<FhInsuredPerson> insuredPersonList = Lists.newArrayList();
        insuredPersonList.add(insuredPerson);
        return insuredPersonList;
    }

    private BankCardBindingReqDTO createBindCardInfo(ZaBindCardInfo bindCardInfo, String policyNo, String fhOrderId) {
        BankCardBindingReqDTO renewBindInfo = new BankCardBindingReqDTO();
        renewBindInfo.setChannel(EnumChannel.ZA.getCode());
        renewBindInfo.setFhOrderId(fhOrderId);
        renewBindInfo.setAccountNo(bindCardInfo.getAccountNo());
        renewBindInfo.setAccountName(bindCardInfo.getName());
        renewBindInfo.setIdNumber(bindCardInfo.getCertiNo());
        renewBindInfo.setPolicyNo(policyNo);
        renewBindInfo.setBindStatus(OrderBindStatusEnum.BIND.getCode());
        renewBindInfo.setBizType(bindCardInfo.getBizType());
        renewBindInfo.setOpeTime(LocalDateTime.now());
        return renewBindInfo;
    }

    public static Date millSecondToDate(Long millSecond) {
        Date date = new Date();
        date.setTime(millSecond);
        return date;
    }


    /******** end s50 众安续保保单信息 *******/

    public static void main(String[] args) {

        System.out.println((millSecondToDate(Long.valueOf("*************"))));
    }

    /**
     * 客户中心 service
     */
    @Autowired
    protected CustomerCenterService ccService;

    /**
     * 核保流程
     *
     * @return
     */
    @Override
    public GroupQuoteResponse underwriting(GroupUnderwriting req) {
        fixDuty(req);
        ZaApiProperties zaProp = apiService.getZaApiProperties();
        Integer productId = req.getProductId();
        Integer planId = req.getProduct().getPlanId();
        //众安团险续保校验当前订单被保人职业等级与上年被保人职业等级是否匹配, 如果当年等级在上年找不断哦则报错
        checkOccupationGroupListByOrderId(req);

        SmProductDetailVO product = productService.getProductById(productId);
        if (product == null) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_NOT_ONLINE_201001.getCode(), ExcptEnum.PRODUCT_NOT_ONLINE_201001.getMsg());
        }
        if (StringUtils.isBlank(req.getOrderOutType())) {
            req.setOrderOutType(zaProp.getGroupOrderType());
        }
        /**
         * 1.重新试算保费
         */
        String firstOrderId = null;
        if (req.isRealRenewFlag()) {
            firstOrderId = findFirstOrderId(req.getOldOrderId());
            //众安团意险续保不调用续保,因为续保还是原保费,上游给到后台的保费还是原保费,保费试算无法区分是否是续保会导致试算失败
            if (StringUtils.isNotBlank(firstOrderId)) {
                SmOrderMinInfo smOrderMinInfo = orderMapper.querySmOrderMinInfo(firstOrderId);
                policyService.setCommissionPublic(smOrderMinInfo.getProductId(), smOrderMinInfo.getPlanId(), req);
            }
        }
        List<SysDutyConfig> dutys = getSysDutys(EnumChannel.ZA.getCode());
        ZaGroupUnderwritingReq rew = buildQuoteRequest(zaProp, req, dutys);
        ZaQuoteResp premiumResp = null;
        if (!req.isRealRenewFlag()) {
            premiumResp = apiService.quotePrice4Group(rew);
            if (!premiumResp.isSuccess()) {
                throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), premiumResp.errorMsg());
            }
        }

        /**
         * 2.记录原始数据
         */
        setOccupationVersion(firstOrderId,req);
        String orderId = super.saveGroupOrder(product, req);
        log.info("[开始调用众安核保接口]-{}", orderId);
        /**
         * 3.调用保司接口核保
         */
        ZaGroupUnderwritingReq uwr = buildApplyVo(zaProp, req, dutys);
        ZaQuoteResp resp = null;
        ZaGroupRenewalQuoteResp zaGroupRenewalQuoteResp = null;
        ZaGroupUnderwritingReq zaGroupUnderwritingReq = null;
        if (req.isRealRenewFlag()) {
            List<SmOrderWaitRenewal> smOrderWaitRenewalList = renewalManagerService.querySmOrderWaitRenewalByPolicyNo(null, req.getOldOrderId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(smOrderWaitRenewalList)) {
                throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "未查到续保记录");
            }
            SmOrderWaitRenewal smOrderWaitRenewal = smOrderWaitRenewalList.get(0);
            //众安团险续保新增续保申请接口
            ZaGroupUnderwritingReq zaGroupApplyReq = buildRenewalZaGroupUnderwritingReqForApply(uwr, req, smOrderWaitRenewal.getPolicyNo());
            ZaGroupRenewalApplyRes zaGroupRenewalApplyRes = apiService.renewwalApply4Group(zaGroupApplyReq, req);
            checkZaGroupRenewalDutyCode(req, zaGroupRenewalApplyRes);
            zaGroupUnderwritingReq = buildRenewalZaGroupUnderwritingReq(uwr, req, zaGroupRenewalApplyRes.getResult(), smOrderWaitRenewal.getPolicyNo());
            zaGroupRenewalQuoteResp = apiService.renewwalUnderwriting(zaGroupUnderwritingReq, req, zaGroupRenewalApplyRes.getResult());
//            checkRenewalDate(zaGroupRenewalQuoteResp,req);
        } else {
            resp = apiService.apply4Group(uwr);
        }
        /**
         * 4.状态更新
         */
        if (req.isRealRenewFlag()) {
            if (zaGroupRenewalQuoteResp.isSuccess()) {
                afterApplyForRenewal(orderId, productId, planId, zaGroupRenewalQuoteResp.getApplicationNo(), uwr, zaGroupUnderwritingReq, req);
                return zaGroupRenewalQuoteResp.convertVo();
            }
        } else {
            if (resp.isSuccess()) {
                afterApply(orderId, productId, planId, resp.getProposalNo(), uwr, premiumResp, req);
                return resp.convertVo();
            }
        }
        throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), premiumResp.errorMsg());
    }

    /**
     * 取保单职业表
     * @param req
     */
    public void setOccupationVersion(String firstOrderId,GroupUnderwriting req){
        //如果是续保保单，则取首年保单的职业表
        try {
            List<GroupInsured> insuredList = req.getInsuredList();
            String occupationVersion = null;
            if (req.isRealRenewFlag()) {
                log.info("续保单:取首期保单的职业版本号:{}", firstOrderId);
                if (StringUtils.isBlank(firstOrderId)) {
                    firstOrderId = req.getOldOrderId();
                    log.info("首期订单为空，取上一期订单信息:{}", firstOrderId);
                }
                if (StringUtils.isNotBlank(firstOrderId)) {
                    List<SmOrderInsured> firstPolicyInsuredList = orderInsuredMapper.selectByOrderId(firstOrderId);
                    if (!CollectionUtils.isEmpty(firstPolicyInsuredList)) {
                        SmOrderInsured insured = firstPolicyInsuredList.get(0);
                        occupationVersion = insured.getOccupationVersion();
                        if (StringUtils.isBlank(occupationVersion)) {
                            occupationVersion = defaultOccupationVersion(insured.getCreateTime());
                        }
                    }
                }
            } else {
                if (StringUtils.isBlank(occupationVersion)) {
                    occupationVersion = defaultOccupationVersion(new Date());
                }
            }
            for (GroupInsured entry : insuredList) {
                entry.setOccupationVersion(occupationVersion);
            }
        }catch (Exception e){
            log.error("续保保单-关联职业版本号错误，请开发人员排查:{}",firstOrderId,e );
        }
    }

    private String defaultOccupationVersion(Date createTime){
        log.info("开始获取默认职业版本号:{}-{}",changeOccupationVersion,createTime);
        Date changeTime = LocalDateUtil.convert2DateSilence(changeOccupationVersion,"yyyyMMdd");
        if(changeTime!=null){
            if(createTime.after(changeTime)){
                return ZaConsts.OCCUPATION_VERSION_2;
            }
        }
        return ZaConsts.OCCUPATION_VERSION_1;
    }

    /**
     * 1.保存投保数据[sm_order_item]
     * 2.更新订单状态
     */
    public void afterApply(String orderId,
                           Integer productId,
                           Integer planId,
                           String proposalNo,
                           ZaGroupUnderwritingReq uwr,
                           ZaQuoteResp resp, GroupUnderwriting req) {
        ZaQuoteResp.Insured[] insuredList = resp.getInsuredList();
        Map<String, ZaQuoteResp.Insured> map = convertMap(insuredList);
        List<SmOrderItem> orderItems = new ArrayList<>();
        for (Insured entry : uwr.getInsuredList()) {
            SmOrderItem item = new SmOrderItem();
            String certNo = entry.getCertNo();

            item.setIdNumber(certNo);
            item.setIdType(entry.getCertType());
            item.setType(0);
            item.setProductId(productId);
            item.setFhOrderId(orderId);
            item.setQty(1);
            item.setPlanCode(entry.getProductCode());
            item.setProductId(productId);
            item.setAppStatus(SmConstants.POLICY_STATUS_BLANK);
            String unitPremium = "0";
            if (map != null) {
                /**
                 * 统一转成小写（身份证带X字符）
                 */
                certNo = certNo.toLowerCase();
                ZaQuoteResp.Insured ins = map.get(certNo);
                if (ins != null) {
                    unitPremium = ins.getPremium();
                }
            }
            BigDecimal premium = new BigDecimal(unitPremium);
            item.setTotalAmount(premium);
            item.setUnitPrice(premium);
            item.setPlanId(planId);
            orderItems.add(item);
        }
        int r0 = orderMapper.clearTempItem(orderId);
        int r1 = orderItemMapper.insertList(orderItems);
        String appNo = proposalNo;
        int rtn = 0;
        if (appNo != null) {
            OrderDTO param = new OrderDTO();
            param.setAppNo(appNo);
            param.setPayStatus(SmConstants.ORDER_STATUS_TO_PAY);
            param.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);
            param.setApplyTime(new Date());
            if (req.isRealRenewFlag()) {
                //如果该单子是续保则保存原待续保的订单编码
                param.setRenewOrderId(req.getOldOrderId());
                String firstOrderId = findFirstOrderId(req.getOldOrderId());
                if (StringUtils.isBlank(firstOrderId)) {
                    //说明基于原单续保
                    param.setFirstOrderId(req.getOldOrderId());
                } else {
                    //说明基于续保单再续保
                    param.setFirstOrderId(firstOrderId);
                }
            }
            rtn = orderMapper.updateOrder(orderId, param);
        }
        log.info("更新投保订单状态完成:{},{},{},{}", orderId, rtn,r0,r1);
    }

    /**
     * 构造核保请求类
     *
     * @param zaProps
     * @param req     核保请求
     * @param dutys   保障内容
     * @return
     */
    private ZaGroupUnderwritingReq buildApplyVo(ZaApiProperties zaProps,
                                                GroupUnderwriting req,
                                                List<SysDutyConfig> dutys) {
        ZaGroupUnderwritingReq uwr = ZaConvertor.convertApplyBean(zaProps, dutys, req);
        uwr.setEnterpriseRiskAdjustmentFactor(req.getEnterpriseRiskFactor());
        Attachment[] attachments = uploadFile2ZA(req.getApplicant().getBusinessLicense());
        uwr.setAttachmentList(attachments);
        return uwr;
    }

    public String calcUnitPremium(BigDecimal premium, int ins) {
        try {
            if (premium == null) {
                return "0";
            }
            BigDecimal num = new BigDecimal(ins);
            return String.valueOf(premium.divide(num));
        } catch (Exception e) {
            log.error("获取默认保费失败", e);
            return "0";
        }
    }

    private Map<String, ZaQuoteResp.Insured> convertMap(ZaQuoteResp.Insured[] insuredList) {
        if (insuredList == null) {
            return null;
        }
        Map<String, ZaQuoteResp.Insured> map = new HashMap<>();
        for (ZaQuoteResp.Insured entry : insuredList) {
            String certNo = entry.getCertNo();
            certNo = certNo.toLowerCase();
            map.put(certNo, entry);
        }
        return map;
    }

    public ZaGroupEndorCommit convertGroupEndorCommit(ZaGroupEndorReq endor, GroupEndorsement req) {
        ZaGroupEndorCommit commit = new ZaGroupEndorCommit();
        BeanCopier copier = BeanCopier.create(ZaGroupEndorReq.class, ZaGroupEndorCommit.class, false);
        copier.copy(endor, commit, null);
        commit.setChannelOrderNo(IdGenerator.getNextNo(EnumChannel.ZA.getCode()));
        ZaPayInfo payInfo = convertRefund(req);
        commit.setPayInfo(payInfo);
        return commit;
    }

    /**
     * 退费信息
     *
     * @param req
     * @return
     */
    private ZaPayInfo convertRefund(GroupEndorsement req) {
        RefundReq refund = req.getRefundInfo();
        if (refund != null) {
            ZaPayInfo payInfo = new ZaPayInfo();
            payInfo.setAccountName(refund.getAccountName());
            payInfo.setAccountNumber(refund.getBankAccount());
            payInfo.setAccountType(refund.getAccountType());
            payInfo.setBankLocation(refund.getBankLocation());
            payInfo.setBankOrg(refund.getBankAreaCode());
            payInfo.setBankName(refund.getBankAbbrCode());
            payInfo.setIsZaFund("Y");
            payInfo.setIsAgentPay("N");
            if ("2".equals(refund.getAccountType())) {
                String id = IdGenerator.getUuid() + ".jpg";
                String fileUrl = refund.getCertificate();
                String base64File = getBase64FromFile(fileUrl);
                log.warn("退费代付凭证:{},{}", id, fileUrl);

                payInfo.setIsAgentPay("Y");
                payInfo.setVoucherFileName(id);
                payInfo.setVoucherFilePath(fileUrl);
                payInfo.setVoucherFile(base64File);
            }
            return payInfo;
        }
        return null;
    }

    private String getBase64FromFile(String netFile) {
        netFile = fileService.reGenerateUrl4Expired(netFile);
        return FileUtils.download2Base64(netFile);
    }

    /**
     * 将文件同步到众安OSS
     *
     * @param businessLicense
     * @return
     */
    private Attachment[] uploadFile2ZA(String businessLicense) {
        if (StringUtils.isBlank(businessLicense)) {
            throw new MSBizNormalException(ExcptEnum.NO_BUSINESS_LISCENSE.getCode(), ExcptEnum.NO_BUSINESS_LISCENSE.getMsg());
        }
        String[] files = businessLicense.split(",");
        Attachment[] attachments = new Attachment[files.length];
        int i = 0;
        for (String file : files) {
            attachments[i++] = buildAttach2(ZAConstants.ATTACH_SOCIAL_CREDIT, file);
        }
        return attachments;
    }

    private FGAttachment buildAttach(String attachmentType, String netFile) {
        netFile = fileService.reGenerateUrl4Expired(netFile);
        FGAttachment attachment = new FGAttachment();
        File file = FileUtils.downNetImage(netFile, IdGenerator.getUuid(), ".jpg");
        String name = file.getName();
        if (file != null) {
            /**
             * 开始上传文件
             */
            try {
                FileUploadResponse furs = apiService.uploadFile(file.getPath());
                if (furs != null && "0000".equals(furs.getCode())) {
                    attachment.setLocation(furs.getData());
                    attachment.setAttachmentType(attachmentType);
                    attachment.setAttachmentName(name);
                }
            } finally {
                file.delete();
            }

        }

        return attachment;
    }

    @Autowired
    private SystemFileService fileService;

    /**
     * 众安附件同步
     * 修订时间：2022-08-15
     * 为了防止附件过期，重新生成一个临时文件路径
     */
    private Attachment buildAttach2(String attachmentType, String netFile) {
        netFile = fileService.reGenerateUrl4Expired(netFile);
        Attachment attachment = new Attachment();
        File file = FileUtils.downNetImage(netFile, IdGenerator.getUuid(), ".jpg");
        String name = file.getName();
        if (file != null) {
            /**
             * 开始上传文件
             */
            try {
                FileUploadResponse furs = apiService.uploadFile(file.getPath());
                if (furs != null && "0000".equals(furs.getCode())) {
                    attachment.setAttachmentData(furs.getData());
                    attachment.setAttachmentType(attachmentType);
                    attachment.setAttachmentName(name);
                }
            } finally {
                file.delete();
            }
        }
        return attachment;
    }


//    private void validOrder(SmProductDetailVO product, GroupUnderwriting req) {
//        Agent agent = req.getAgent();
//        List<GroupInsured> ins = req.getInsuredList();
//        validInsured(ins, agent.getRecommendOrgCode(), agent.getRecommendMainJobNumber());
//        if (req.getQty() > product.getBuyLimit()) {
//            throw new MSBizNormalException(ExcptEnum.OVER_PRODUCT_LIMIT.getCode(), ExcptEnum.OVER_PRODUCT_LIMIT.getMsg());
//        }
//
//    }

    /**
     * ★校验被保人实名认证信息
     *
     * @param ins
     */
    private void validInsured(List<GroupInsured> ins, String recommendOrgCode, String jobNumber) {
        if (ins == null || ins.size() == 0) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人不能为空");
        }
        for (GroupInsured e : ins) {
            ccService.createInsuredCustInfo(e.getPersonName(), e.getIdNumber(),
                    recommendOrgCode, jobNumber);
        }
    }

    /**
     * 获取支付信息
     *
     * @return
     */
    @Override
    public String getPayHtml(String orderId, HttpServletResponse response) {
        PaymentDTO payment = orderMapper.queryPayment(orderId);
        if (payment == null) {
            /**
             * 兼容批改支付单~
             */
            payment = endorMapper.queryEndorPayment(orderId);
        }
        if (payment == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        log.info("生成支付地址-获取订单信息-{}", payment);
        BigDecimal amount = payment.getTotalAmount();
        PaymentReq req = new PaymentReq();
        ZaApiProperties prop = apiService.getZaApiProperties();
        req.setOutTradeNo(orderId);
        req.setOrderAmt(String.valueOf(amount));
        req.setPayChannel(prop.getPayChannel().split(","));
        req.setSignType("MD5");
        req.setRequestCharset("UTF-8");
        req.setTradeType("H5");
        /**
         * 透传字段，支付回调时需要用到
         * 主要要区分批改和新契约的流程，方便处理回调逻辑
         */
        if (StringUtils.isNotBlank(payment.getEndorsementNo())) {
            Map<String, String> param = new HashMap<>(1);
            param.put(ZAConstants.PAY_TRANS_FIELD_ENDORSEMENT_NO, payment.getEndorsementNo());
            req.setNotifyInfo(JSON.toJSONString(param));
        }
        req.setSrcType(prop.getSrcType());
        req.setOrderType(prop.getOrderType());
        req.setShowUrl(prop.getShowUrl());
        req.setNotifyUrl(prop.getNotifyUrl());
        req.setMerchantCode(prop.getMerchantCode());

        String payReturnUrl = prop.getPayReturnUrl();
        payReturnUrl = payReturnUrl + "?orderId=" + orderId;
        req.setReturnUrl(payReturnUrl);

        Commodity commodity = new Commodity();

        String productName = payment.getProductName();
        if (StringUtils.isBlank(productName)) {
            productName = prop.getCommoditySubject();
        }
        commodity.setSubject(productName);
        commodity.setPrice(amount + "元");
        req.setCommodity(commodity);
        return apiService.getPayForm(req);
    }

    /**
     * 出单接口
     *
     * @return
     */
    @Override
    public ZaQuoteResp groupInsue(ZaGroupInsureReq req) {
        return apiService.groupInsue(req);
    }

    /**
     * 查询外部支付信息
     *
     * @return
     */
    @Override
    public QueryOrderVO checkPay4SeeFee(String orderId) {
        ZaPayWrapper resp = apiService.checkPay(orderId);
        if (resp == null || !resp.isSuccess()) {
            log.warn("订单支付信息为空或者支付信息查询失败:{}", orderId);
            return null;
        }
        ZaPayment payment = resp.getValue();
        if (Objects.equals(payment.getPayStatus(), 1)) {
            log.info("订单支付成功:{}", orderId);
            return ZaConvertor.convert2CommonPay(payment);
        }
        log.warn("订单未支付:{}", orderId);
        return null;
    }

    public ZaEndorRespWrapper<ZaEndorCommitResp> groupEndorEffective(ZaGroupEndorEffective req) {
        return apiService.endorEffective(req);
    }

    /**
     * 获取订单支付URL
     *
     * @param request
     * @return
     */
    @Override
    public OrderPrePayResponse seeFeePayChannel(OrderPrePayRequest request) {
        ZaApiProperties prop = apiService.getZaApiProperties();
        OrderPrePayResponse orderPrePayResponse = new OrderPrePayResponse();
        String orderId = request.getOrderId();
        orderPrePayResponse.setPayUrl(String.format(prop.getSeefeePayPreUrl(), orderId));
        orderPrePayResponse.setPayId(orderId);
        return orderPrePayResponse;
    }

    @Override
    public GroupEndorResponse endorCalPremium(GroupEndorsement req) {
        String productName = orderMapper.queryProductName(req.getOrderId());
        if (StringUtils.isBlank(productName)) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), "产品名称为空");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        SmOrderMinInfo smOrderMinInfo = orderMapper.querySmOrderMinInfo(req.getOrderId());
        String firstNewOrderId = smOrderMinInfo.getFirstNewOrderId();
        ZaGroupEndorReq endor = null;
        if (StringUtils.isBlank(firstNewOrderId)) {
            //新契约批改
            endor = ZaConvertor.convertGroupEndor(productName, prop, req);
        } else {
            smOrderMinInfo = orderMapper.querySmOrderMinInfo(firstNewOrderId);
            SmProduct smProduct = productMapper.getById(Long.valueOf(smOrderMinInfo.getProductId()));
            endor = ZaConvertor.convertGroupEndor(smProduct.getProductName(), prop, req);
        }

        ZaEndorRespWrapper<ZaEndorCalPremiumResp> resp = apiService.endorCalPremium(endor);
        if (resp == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        GroupEndorResponse rtn = new GroupEndorResponse();
        ZaEndorCalPremiumResp crs = resp.getResult();
        rtn.setPolicyNo(req.getPolicyNo());
        BigDecimal premium = new BigDecimal(0);
        List<EndorCalculation> calcs = crs.getCalculationResult();
        for (EndorCalculation entry : calcs) {
            if (StringUtils.isNotBlank(entry.getTotalPremium())) {
                if (1 == entry.getOpType()) {
                    premium = premium.add(new BigDecimal(entry.getTotalPremium()));
                } else {
                    premium = premium.subtract(new BigDecimal(entry.getTotalPremium()));
                }
            }
        }
        rtn.setTotalPremium(premium);
        return rtn;
    }


    /******** end s50 众安续保保单信息 *******/

    @Override
    public String queryEPolicyUrl(String policyNo, String endorsementNo) {
        ZaEPolicyReq req = new ZaEPolicyReq();
        ZaApiProperties prop = apiService.getZaApiProperties();
        req.setChannelCode(prop.getGroupChannelCode());
        req.setPolicyNo(policyNo);
        req.setEndorsementNo(endorsementNo);
        ZaEPolicyResp resp = apiService.queryEPolicyUrl(req);
        if (resp != null) {
            return resp.getEPolicyURL();
        }
        return null;
    }

    @Override
    public GroupEndorResponse endorUnderwriting(GroupEndorsement req) {
        String productName = orderMapper.queryProductName(req.getOrderId());
        if (StringUtils.isBlank(productName)) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), "产品名称为空");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        SmOrderMinInfo smOrderMinInfo = orderMapper.querySmOrderMinInfo(req.getOrderId());
        String firstNewOrderId = smOrderMinInfo.getFirstNewOrderId();
        ZaGroupEndorReq endor = null;
        if (StringUtils.isBlank(firstNewOrderId)) {
            endor = ZaConvertor.convertGroupEndor(productName, prop, req);
        } else {
            smOrderMinInfo = orderMapper.querySmOrderMinInfo(firstNewOrderId);
            SmProduct smProduct = productMapper.getById(Long.valueOf(smOrderMinInfo.getProductId()));
            endor = ZaConvertor.convertGroupEndor(smProduct.getProductName(), prop, req);
        }
        ZaEndorRespWrapper<ZaEndorUnderwritingResp> resp = apiService.endorUnderwriting(endor);
        if (resp == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setTotalPremium(req.getTotalPremium());
        rtn.setPolicyNo(req.getPolicyNo());
        rtn.setEndorStatus(1);
        return rtn;
    }

    @Autowired
    private SmOrderGroupService orderGroupService;

    /**
     * <p>每次批改会生成一个或多个订单</p>
     * <p>批增的被保人归到一个订单内</p>
     * <p>批减的被保人每个人生成一个订单，主要方便提成计算</p>
     * 提交批改数据
     *
     * @param req
     * @return
     */
    @Override
    public GroupEndorResponse endorCommit(GroupEndorsement req) {

        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(req.getOrderId());
        if (order == null) {
            throw new MSBizNormalException(ExcptEnum.ORDER_NOT_EXISTS);
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        ZaGroupEndorReq endor = null;
        SmOrderMinInfo smOrderMinInfo = orderMapper.querySmOrderMinInfo(req.getOrderId());
        String firstNewOrderId = smOrderMinInfo.getFirstNewOrderId();

        if (StringUtils.isBlank(firstNewOrderId)) {
            endor = ZaConvertor.convertGroupEndor(order.getProductName(), prop, req);
        } else {
            smOrderMinInfo = orderMapper.querySmOrderMinInfo(firstNewOrderId);
            SmProduct smProduct = productMapper.getById(Long.valueOf(smOrderMinInfo.getProductId()));
            endor = ZaConvertor.convertGroupEndor(smProduct.getProductName(), prop, req);
        }

        ZaGroupEndorCommit commitReq = convertGroupEndorCommit(endor, req);

        BigDecimal zero = new BigDecimal(0);
        GroupNotify.StatusEnum notifyCode = GroupNotify.StatusEnum.UN_INIT;
        ZaEndorRespWrapper<ZaEndorCalPremiumResp> premiumResp = apiService.endorCalPremium(endor);
        if (premiumResp == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!premiumResp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), premiumResp.errorMsg());
        }
        ZaEndorCalPremiumResp cps = premiumResp.getResult();


        ZaGroupEndorsementRes res = convertHistoryBean(order.getProductName(), req, cps);
        GroupRuleAdaptor.addSubtractCheck(res.getResult());
        /**
         * 1.预留信息
         */
        Endor payment = new Endor();
        payment.setRawOrderId(req.getOrderId());
        payment.setChannel(EnumChannel.ZA.getCode());
        payment.setPolicyNo(req.getPolicyNo());
        payment.setOperaor(HttpRequestUtil.getUserId());
        payment.setOrderId(commitReq.getChannelOrderNo());
        payment.setStatus(EnumEndor.TO_COMMIT.getCode());
        payment.setOrderType(order.getOrderOutType());
        payment.setEffectiveTime(req.chooseEffectiveTime());
        payment.setOpType(commitReq.judgeOpType());
        payment.setRefund(req.getRefundInfo() != null ? JSON.toJSONString(req.getRefundInfo()) : null);
        payment.setCreateTime(new Date());
        int notifyId = preSaveNotifyMsgV2(payment, req);

        /**
         * 2.开始提交批改
         */
        ZaEndorRespWrapper<ZaEndorCommitResp> resp = apiService.endorSubmit(commitReq);
        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());
        rtn.setTotalPremium(req.getTotalPremium());
        rtn.setEndorStatus(0);
        rtn.setOrderId(commitReq.getChannelOrderNo());
        if (resp == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }

        int apiStatus = 2;
        ZaEndorCommitResp crs = resp.getResult();
        String endorsementNo = crs.getEndorsementNo();
        payment.setEndorsementNo(endorsementNo);
        endorCommited(payment);
        log.info("众安保单批改-数据提交成功");
        /**
         * 当前批改退费或者无费则直接调用生效接口
         */
        if (zero.compareTo(cps.calcTotalPremium()) >= 0) {
            ZaGroupEndorEffective effectReq = new ZaGroupEndorEffective();
            effectReq.setChannelCode(prop.getGroupChannelCode());
            effectReq.setEndorsementNo(crs.getEndorsementNo());
            effectReq.setGroupPolicyNo(req.getPolicyNo());
            resp = apiService.endorEffective(effectReq);
            /**
             * 批改单已生效，可直接触发后续逻辑
             */
            if (resp != null && resp.isSuccess()) {
                payment.setStatus(EnumEndor.EFFECT.getCode());
                notifyCode = GroupNotify.StatusEnum.UNDO;
                apiStatus = 0;
            } else {
                throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "[众安]保司服务异常");
            }
        }
        payment.setAmount(cps.calcTotalPremium());

        afterCommit(notifyId, notifyCode, crs.getEndorsementNo(), payment, res);
        log.info("开始处理批改后续流程：{},{},{}", notifyId, notifyCode, payment.getStatus());
        if (Objects.equals(payment.getStatus(), EnumEndor.EFFECT.getCode())) {
            log.info("批改流程生效操作：{}", notifyId);
            orderGroupService.process(notifyId);
        }
        rtn.setEndorStatus(apiStatus);
        rtn.setEndorsementNo(endorsementNo);
        return rtn;
    }

    /**
     * 记录中间状态：提交完成状态
     *
     * @param endor
     * @return
     */
    private int endorCommited(Endor endor) {
        endor.setStatus(EnumEndor.COMMITED.getCode());
        return endorMapper.updateByPrimaryKey(endor);
    }

    /**
     * 复用之前的批改回调逻辑，通过定时任务轮询处理批改消息
     *
     * @param endor 批改的订单
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public int preSaveNotifyMsgV2(Endor endor, GroupEndorsement req) {
        String policyNo = req.getPolicyNo();
        req.fillBirthDay();

        SmOrderGroupNotify notify = new SmOrderGroupNotify();
        notify.setGroupType(GroupNotify.GroupType.GROUP.getCode());
        notify.setChannel(EnumChannel.ZA.getCode());
        notify.setGroupPolicyNo(req.getPolicyNo());
        notify.setPolicyState("1");
        notify.setType(GroupNotify.TypeEnum.ENDORSEMENT.getCode());
        notify.setOpMethod(GroupNotify.OpMethodEnum.CONTENT.getCode());
        notify.setStatus(GroupNotify.StatusEnum.UN_INIT.getCode());

        int r1 = smOrderGroupNotifyMapper.insertSelective(notify);
        int r2 = endorMapper.insert(endor);
        int r3 = orderMapper.saveQuoteInsureds(endor.getOrderId(), req.getInsuredList());
        log.info("保单数据预处理完成:{},{},{},{},{}", policyNo, r1, r2, r3, notify.getId());
        return notify.getId();
    }

    @Override
    public Boolean endorEffect(GroupEndorEffectReq req) {
        Endor endor = endorMapper.queryEndorInfo(req.getChannel(), req.getPolicyNo(), req.getEndorsementNo());
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        if (endor.finish()) {
            throw new MSBizNormalException(ExcptEnum.DATA_REVOKED.getCode(), "该单已完成或已撤销");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        ZaGroupEndorEffective effective = new ZaGroupEndorEffective();
        effective.setTradeNo(endor.getTradeNo());
        effective.setGroupPolicyNo(req.getPolicyNo());
        effective.setChannelCode(prop.getGroupChannelCode());
        effective.setEndorsementNo(endor.getEndorsementNo());
        ZaEndorRespWrapper<ZaEndorCommitResp> resp = apiService.endorEffective(effective);
        if (resp == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (resp.isSuccess()) {
            afterEndorEffect(endor);
        }
        throw new BizException(ExcptEnum.ENDOR_EFFECT_ERROR.getCode(), resp.errorMsg());
    }

    @Override
    public Boolean endorEffect(String orderId) {
        Endor endor = endorMapper.queryEndorPaymentByOrderId(orderId);
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        if (endor.finish()) {
            throw new MSBizNormalException(ExcptEnum.DATA_REVOKED.getCode(), "该单已完成或已撤销");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        ZaGroupEndorEffective effective = new ZaGroupEndorEffective();
        effective.setTradeNo(endor.getTradeNo());
        effective.setGroupPolicyNo(endor.getPolicyNo());
        effective.setChannelCode(prop.getGroupChannelCode());
        effective.setEndorsementNo(endor.getEndorsementNo());
        ZaEndorRespWrapper<ZaEndorCommitResp> resp = apiService.endorEffective(effective);
        if (resp == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (resp.isSuccess()) {
            afterEndorEffect(endor);
            return true;
        }
        throw new BizException(ExcptEnum.ENDOR_EFFECT_ERROR.getCode(), resp.errorMsg());
    }

    private void afterEndorEffect(Endor endor) {
        Endor param = new Endor();
        param.setId(endor.getId());
        param.setStatus(2);

        SmOrderGroupNotify update = new SmOrderGroupNotify();
        update.setStatus(GroupNotify.StatusEnum.UNDO.getCode());
        update.setEndorsementNo(endor.getEndorsementNo());
        update.setGroupPolicyNo(endor.getPolicyNo());
        update.setChannel(endor.getChannel());

        smOrderGroupNotifyMapper.updateNotifyByPolicy(update);
        endorMapper.updateByPrimaryKey(param);
    }

    @Override
    public Boolean revokeEndor(GroupRevokeVo req) {
        ZaApiProperties prop = apiService.getZaApiProperties();
        ZaGroupEndorRevoke revoke = new ZaGroupEndorRevoke();
        revoke.setChannelCode(prop.getGroupChannelCode());
        revoke.setEndorsementNo(req.getEndorsementNo());
        revoke.setGroupPolicyNo(req.getPolicyNo());
        revoke.setRemark(req.getRemark());
        return apiService.revokeEndor(revoke);
    }

    /**
     * 复用之前的批改回调逻辑，通过定时任务轮询处理批改消息
     *
     * @param req
     * @return
     */
    public int preSaveNotifyMsg(GroupEndorsement req) {
        SmOrderGroupNotify notify = new SmOrderGroupNotify();
        notify.setGroupType(GroupNotify.GroupType.GROUP.getCode());
        notify.setChannel(EnumChannel.ZA.getCode());
        notify.setGroupPolicyNo(req.getPolicyNo());
        notify.setPolicyState("1");
        notify.setType(GroupNotify.TypeEnum.ENDORSEMENT.getCode());
        notify.setOpMethod(GroupNotify.OpMethodEnum.CONTENT.getCode());
        notify.setStatus(GroupNotify.StatusEnum.UN_INIT.getCode());
        smOrderGroupNotifyMapper.insertSelective(notify);
        return notify.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    public void afterCommit(Integer id, GroupNotify.StatusEnum status, String endorNo, Endor payment, ZaGroupEndorsementRes res) {
        log.warn("批改提交后续处理-NotifyId:[{}],Status:[{}],EndorNo:[{}]", id, status, endorNo);
        SmOrderGroupNotify update = new SmOrderGroupNotify();
        update.setId(id);
        update.setStatus(status.getCode());
        update.setEndorsementNo(endorNo);
        int ret = smOrderGroupNotifyMapper.fullUpdateNotify(update);
        endorMapper.updateByPrimaryKey(payment);

        SmOrderGroupNotifyMsg msg = new SmOrderGroupNotifyMsg();
        msg.setNotifyId(id);
        res.getResult().setEndorsementNo(endorNo);
        msg.setNotifyContent(JSON.toJSONString(res));
        smOrderGroupNotifyMsgMapper.insertSelective(msg);
        if (ret == 0) {
            throw new BizException("-1", "记录更新失败");
        }
    }

    /**
     * 伪造一个批改消息
     *
     * @param productName
     * @param req
     * @param premiumResp
     * @return
     */
    private ZaGroupEndorsementRes convertHistoryBean(String productName, GroupEndorsement req, ZaEndorCalPremiumResp premiumResp) {
        ZaGroupEndorsementRes res = new ZaGroupEndorsementRes();
        res.setCode("0");
        ZaGroupEndorsementInfo data = new ZaGroupEndorsementInfo();
        data.setValidateTime(req.getEffectiveTime());
        List<ZaGroupInsuredInfo> insureds = new ArrayList<>();
        List<GroupInsured> groupInsureds = req.getInsuredList();
        Map<String, String> premiumMap = LambdaUtils.safeToMap(premiumResp.getCalculationResult(), EndorCalculation::convertIdCard2LowCase, EndorCalculation::getTotalPremium);
        for (GroupInsured entry : groupInsureds) {
            ZaGroupInsuredInfo vo = new ZaGroupInsuredInfo();
            vo.setName(entry.getPersonName());
            vo.setGender(entry.getPersonGender());

            String idNumber = entry.getIdNumber();
            vo.setCertNo(idNumber);
            vo.setCertType(entry.getIdType());
            vo.setPlanCode(ZaConvertor.genVirtualPlanCode(req.getOrderId(), entry.getOccupationGroup()));
            vo.setPlanName(ZaConvertor.genVirtualPlanNameV2(req.getPolicyNo(),productName, entry.getOccupationGroup()));
            vo.setHandleType(String.valueOf(entry.getOpType()));

            String lowIdNumber = idNumber.toLowerCase();
            String premium = premiumMap.get(lowIdNumber);
            vo.setTotalPremium(premium == null ? new BigDecimal(0) : new BigDecimal(premium));
            vo.setIndividualStatus(entry.convertIndividualStatus());

            vo.setOccupationCode(entry.getOccupationCode());
            vo.setOccupationGroup(entry.getOccupationGroup());
            vo.setIsSecurity(entry.getIsSecurity());
            if (entry.getOpType() == 2) {
                vo.setTerminateTime(req.getReductionEffectiveTime());
            }
            if (StringUtils.isBlank(entry.getBirthday())) {
                vo.setBirthday(IdCardUtils.getBirthday(entry.getIdNumber(), "yyyy-MM-dd"));
            } else {
                vo.setBirthday(entry.getBirthday());
            }
            insureds.add(vo);
        }
        data.setValidateTime(req.getAdditionEffectiveTime());
        data.setReductionEffectiveTime(req.getReductionEffectiveTime());
        data.setInsuredList(insureds);
        res.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        res.setResult(data);
        return res;
    }

    @Override
    public InvoiceResponse openInvoice(InvoiceVo req) {
        log.info("[{}]-开始请求众安统一开票流程，Items:[{}]", req.getPolicyNo(), req.getPolicyItemList());

        ZaInvoiceReq data = ZaConvertor.convertInvoiceBean(req);
        ZaInvoiceResp resp = apiService.openInvoice(data);

        InvoiceResponse rtn = new InvoiceResponse();
        List<ZaInvoiceInfo> zaInvoiceInfoList = resp.getInvoiceInfoList();
        ZaInvoiceInfo zaInvoiceInfo = zaInvoiceInfoList.get(0);
        rtn.setInvoiceNo(zaInvoiceInfo.getInvoiceNum());
        List<String> invooiceList = parseInvoiceUrlList(resp);
        rtn.setInvoiceUrl(invooiceList.get(0));
        rtn.setEndorsementNo(resp.getEndorsementNos());
        rtn.setInvoiceUrlList(invooiceList);
        return rtn;
    }

    @Override
    public String submitOfflinePay(OfflinePayDTO req) {
        log.info("{}-开始提交众安线下支付资料，Data:[{}]", req.getOrderId(), req);

        if (!Objects.equals("0", req.getPayAgent())) {
            if (CollectionUtils.isEmpty(req.getPayAgentMaterials())) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "委托书材料不能为空");
            }
        }
        if (isEndor(req)) {
            return offlinePay4Endor(req);
        }
        return offlinePay4Apply(req);
    }

    /**
     * 批改-线下支付
     *
     * @param req
     * @return
     */
    public String offlinePay4Endor(OfflinePayDTO req) {
        if (StringUtils.isBlank(req.getPolicyNo())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "保单号不能为空");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        ZaOfflinePayEndorRequest data = new ZaOfflinePayEndorRequest();
        data.setChannelCode(prop.getGroupChannelCode());
        data.setGroupPolicyNo(req.getPolicyNo());
        data.setEndorsementNo(req.getEndorsementNo());

        List<FGAttachment> attachmentList = buildAttachment4Endor(req);
        data.setAttachments(attachmentList);
        ZaOfflinePay4EndorResponse response = apiService.submitOfflinePay4Endor(data);
        if (response.isSuccess()) {
            log.info("{},线下支付资料提交成功，更新订单状态为支付中", req.getEndorsementNo());
            int rtn = endorMapper.updateStatusByEndorNo(EnumChannel.ZA.getCode(), req.getEndorsementNo(), SmConstants.PAY_STATUS_DOING);
            log.info("{},线下支付资料提交成功，状态更新结果,{}", req.getEndorsementNo(), rtn);
        }
        return req.getOrderId();
    }

    /**
     * 批改-线下支付-附件上传
     *
     * @param req
     * @return
     */
    private List<FGAttachment> buildAttachment4Endor(OfflinePayDTO req) {
        List<FGAttachment> attachmentList = new ArrayList<>();
        List<String> payMaterials = req.getPayMaterials();
        String now = LocalDateUtil.formatNow();
        if (payMaterials != null) {
            for (String entry : payMaterials) {
                FGAttachment ah = buildAttach(ZAConstants.ATTACH_PAYMENT_VOUCHER, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }
        List<String> payAgentMaterials = req.getPayAgentMaterials();
        if (payAgentMaterials != null) {
            for (String entry : payAgentMaterials) {
                FGAttachment ah = buildAttach(ZAConstants.ATTACH_PAY_AUTHORIZATION, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }
        List<String> payAgentIdCards = req.getPayAgentIdCards();
        if (payAgentIdCards != null) {
            for (String entry : payAgentIdCards) {
                FGAttachment ah = buildAttach(ZAConstants.ATTACH_IDCARD, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }

        List<String> payAgentCompanyFiles = req.getPayAgentCompanyFiles();
        if (payAgentCompanyFiles != null) {
            for (String entry : payAgentCompanyFiles) {
                FGAttachment ah = buildAttach(ZAConstants.ATTACH_BUSINESS_LISCENSE, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }
        return attachmentList;
    }

    private String payAgentInfo = "众安团险线下支付代付";

    private String offlinePay4Apply(OfflinePayDTO req) {
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(req.getOrderId());
        if (order == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单不存在");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();

        ZaOfflinePayRequest data = new ZaOfflinePayRequest();
        data.setChannelOrderNo(req.getOrderId());
        data.setChannelCode(prop.getGroupChannelCode());
        data.setProposalNo(order.getAppNo());
        data.setTotalPremium(String.valueOf(order.getTotalAmount()));
        String payAgent = req.getPayAgent();
        data.setIsPayAgent("0".equals(payAgent) ? "N" : "Y");
        data.setPayAgentInfo(payAgentInfo);

        List<Attachment> attachments = buildAttachment(req);
        data.setAttachmentList(attachments);
        ZaOfflinePayResponse resp = apiService.submitOfflinePay(data);
        /**
         * 修改状态码
         */
        if (resp.isSuccess()) {
            log.info("{},线下支付资料提交成功，更新订单状态为支付中", req.getOrderId());
            OrderDTO updateOrder = new OrderDTO();
            updateOrder.setFhOrderId(req.getOrderId());
            updateOrder.setPayStatus(SmConstants.PAY_STATUS_DOING);
            int rtn = orderMapper.updateOrder(req.getOrderId(), updateOrder);
            log.info("{},线下支付资料提交成功，状态更新结果,{}", req.getOrderId(), rtn);
        }
        return resp.getChannelOrderNo();
    }

    private List<Attachment> buildAttachment(OfflinePayDTO req) {
        List<Attachment> attachmentList = new ArrayList<>();
        List<String> payMaterials = req.getPayMaterials();
        String now = LocalDateUtil.formatNow();
        if (payMaterials != null) {
            for (String entry : payMaterials) {
                Attachment ah = buildAttach2(ZAConstants.ATTACH_PAYMENT_VOUCHER, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }
        List<String> payAgentMaterials = req.getPayAgentMaterials();
        if (payAgentMaterials != null) {
            for (String entry : payAgentMaterials) {
                Attachment ah = buildAttach2(ZAConstants.ATTACH_PAY_AUTHORIZATION, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }
        List<String> payAgentIdCards = req.getPayAgentIdCards();
        if (payAgentIdCards != null) {
            for (String entry : payAgentIdCards) {
                Attachment ah = buildAttach2(ZAConstants.ATTACH_IDCARD, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }

        List<String> payAgentCompanyFiles = req.getPayAgentCompanyFiles();
        if (payAgentCompanyFiles != null) {
            for (String entry : payAgentCompanyFiles) {
                Attachment ah = buildAttach2(ZAConstants.ATTACH_BUSINESS_LISCENSE, entry);
                ah.setUploadTime(now);
                attachmentList.add(ah);
            }
        }
        return attachmentList;
    }

    /**
     * 判断是批改还是新保
     *
     * @param req
     * @return
     */
    private boolean isEndor(OfflinePayDTO req) {
        return StringUtils.isNotBlank(req.getEndorsementNo());
    }

    /**
     * 订单[批改单]取消
     *
     * @param req
     * @return
     */
    @Override
    public GroupEndorResponse cancel(GroupRevokeVo req) {
        if (req.isEndor()) {
            return cancelEndor(req);
        }
        /**
         * 线下支付的单才需要通知保司撤单
         * 线上支付的单可直接操作订单状态
         */
        FastOrderDTO order = orderMapper.getFastOrderByOrderId(req.getOrderId());
        if (order == null) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), "订单信息不存在：" + req.getOrderId());
        }

        OrderDTO entity = new OrderDTO();
        entity.setFhOrderId(req.getOrderId());
        entity.setPayStatus(SmConstants.ORDER_STATUS_CANCEL);
        entity.setOrderState(SmConstants.ORDER_STATUS_CANCEL);
        int i = orderMapper.updateOrder(req.getOrderId(), entity);

        log.info("投保单更新[撤销]状态：{}", i);
        GroupEndorResponse data = new GroupEndorResponse();
        data.setOrderId(req.getOrderId());
        return data;
    }

    public GroupEndorResponse cancelEndor(GroupRevokeVo req) {
        Endor endor = endorMapper.queryEndor(EnumChannel.ZA.getCode(), req.getEndorId(), req.getEndorsementNo());
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "批改信息不存在");
        }
        ZaApiProperties prop = apiService.getZaApiProperties();
        ZaGroupEndorRevoke revoke = new ZaGroupEndorRevoke();
        revoke.setChannelCode(prop.getGroupChannelCode());
        revoke.setEndorsementNo(endor.getEndorsementNo());
        revoke.setGroupPolicyNo(endor.getPolicyNo());
        revoke.setRemark(req.getRemark());
        Boolean cancelFlag = apiService.revokeEndor(revoke);
        log.warn("众安撤单结果：{}，{}", req.getEndorsementNo(), cancelFlag);

        endor.setStatus(EnumEndor.CANCEL.getCode());
        int i = endorMapper.updateByPrimaryKeySelective(endor);
        log.info("批改单更新[撤销]状态-{}", i);
        GroupEndorResponse data = new GroupEndorResponse();
        data.setOrderId(req.getOrderId());
        return data;
    }

    private ZaGroupUnderwritingReq buildRenewalZaGroupUnderwritingReqForApply(ZaGroupUnderwritingReq uwr, GroupUnderwriting req, String policyNo) {

        ZaGroupUnderwritingReq renewwalUwr = new ZaGroupUnderwritingReq();
        renewwalUwr.setChannelCode(uwr.getChannelCode());
        renewwalUwr.setChannelOrderNo(uwr.getChannelOrderNo());
        renewwalUwr.setSourcePolicyNo(policyNo);
        renewwalUwr.setEnterpriseRiskAdjustmentFactor(uwr.getEnterpriseRiskAdjustmentFactor());
        renewwalUwr.setTotalPremium(uwr.getTotalPremium());
        OrgHolder orgHolder = new OrgHolder();
        orgHolder.setOrgCertNo(uwr.getOrgHolder().getOrgCertNo());
        renewwalUwr.setOrgHolder(orgHolder);
        return renewwalUwr;
    }

    private ZaGroupUnderwritingReq buildRenewalZaGroupUnderwritingReq(ZaGroupUnderwritingReq uwr, GroupUnderwriting req, ZaGroupRenewalApplyResult applyResult, String policyNo) {
        SmCommissionSettingVO commission = req.getCommission();
        BigDecimal commissionRate = commission != null ? commission.getSettlementProportion() : null;
        ZaGroupUnderwritingReq renewwalUwr = new ZaGroupUnderwritingReq();
        //设置基础信息
        renewwalUwr.setChannelCode(uwr.getChannelCode());
        renewwalUwr.setChannelOrderNo(uwr.getChannelOrderNo());
        renewwalUwr.setSourcePolicyNo(policyNo);
        renewwalUwr.setEnterpriseRiskAdjustmentFactor(uwr.getEnterpriseRiskAdjustmentFactor());
        renewwalUwr.setTotalPremium(uwr.getTotalPremium());

        //设置投保人信息
        OrgHolder orgHolder = new OrgHolder();
        renewwalUwr.setOrgHolder(orgHolder);
        orgHolder.setOrgName(uwr.getOrgHolder().getOrgName());
        orgHolder.setOrgCertType(uwr.getOrgHolder().getOrgCertType());
        orgHolder.setOrgCertNo(uwr.getOrgHolder().getOrgCertNo());
        orgHolder.setContactPerson(uwr.getOrgHolder().getContactPerson());
        orgHolder.setOrgProvinceCode(uwr.getOrgHolder().getOrgProvinceCode());
        orgHolder.setOrgCityCode(uwr.getOrgHolder().getOrgCityCode());
        orgHolder.setOrgCountryCode(uwr.getOrgHolder().getOrgCountryCode());
        orgHolder.setOrgAddress(uwr.getOrgHolder().getOrgAddress());
        orgHolder.setContactEmail(uwr.getOrgHolder().getContactEmail());
        orgHolder.setContactMobile(uwr.getOrgHolder().getContactMobile());


        //设置附件信息
        List<Attachment> attachments = Lists.newArrayList();
        renewwalUwr.setAttachments(attachments);
        Attachment attachment = null;
        for (Attachment atta : uwr.getAttachmentList()) {
            attachment = new Attachment();
            attachment.setAttachmentData(atta.getAttachmentData());
            attachment.setAttachmentName(atta.getAttachmentName());
            attachment.setAttachmentType(atta.getAttachmentType());
            attachments.add(attachment);
        }

        int idx = 0;

        List<GroupRnewalPlan> plans = Lists.newArrayList();
        renewwalUwr.setPlans(plans);
        GroupRnewalPlan groupRnewalPlan = null;
        ZaGroupRenewalPlans zaGroupRenewalPlan = null;
        List<ZaGroupRenewalPlans> zaGroupRenewalPlansList = applyResult.getPlans();
        List<GroupRnewalProductReq> products = null;
        Map<String, GroupInsured> getJobCodeMap = getJobCodeMap(req);
        Map<String, ZaGroupRenewalPlans> openPlanCodeMap = Maps.newHashMap();
        String openPlanCode = null;
        String occupationGroup = null;
        for (int i = 0; i < zaGroupRenewalPlansList.size(); i++) {
            groupRnewalPlan = new GroupRnewalPlan();
            zaGroupRenewalPlan = zaGroupRenewalPlansList.get(i);
            openPlanCode = zaGroupRenewalPlan.getOpenPlanCode();
            occupationGroup = openPlanCode.substring(openPlanCode.length() - 1, openPlanCode.length());
            if (!openPlanCodeMap.containsKey(occupationGroup)) {
                openPlanCodeMap.put(occupationGroup, zaGroupRenewalPlan);
            }
            groupRnewalPlan.setOpenPlanCode(zaGroupRenewalPlan.getOpenPlanCode());
            groupRnewalPlan.setPlanPremium(zaGroupRenewalPlan.getPlanPremium());
            products = buildGroupRnewalProductReq(zaGroupRenewalPlan, commissionRate);
            if (!CollectionUtils.isEmpty(products)) {
                groupRnewalPlan.setProducts(products);
            }
            plans.add(groupRnewalPlan);
        }

        //设置被保人信息
        List<GroupRnewalInsured> insureds = Lists.newArrayList();
        renewwalUwr.setInsureds(insureds);
        GroupRnewalInsured groupRnewalInsured = null;
        idx = 0;
        GroupInsured groupInsured = null;
        for (Insured insured : uwr.getInsuredList()) {
            groupRnewalInsured = new GroupRnewalInsured();
            groupInsured = getJobCodeMap.get(insured.getCertNo());
            zaGroupRenewalPlan = openPlanCodeMap.get(groupInsured.getOccupationGroup());
            openPlanCode = zaGroupRenewalPlan.getOpenPlanCode();
            groupRnewalInsured.setPremium(zaGroupRenewalPlan.getPlanPremium());
            groupRnewalInsured.setOpenPlanCode(openPlanCode);
            groupRnewalInsured.setChannelInsuredOrderNo(idx + "");
            groupRnewalInsured.setName(insured.getName());
            groupRnewalInsured.setCertType(insured.getCertType());
            groupRnewalInsured.setCertNo(insured.getCertNo());
            groupRnewalInsured.setBirthday(insured.getBirthday());
            groupRnewalInsured.setGender(insured.getGender());
            groupRnewalInsured.setJobCode(insured.getProfession());
            groupRnewalInsured.setHasSocialInsurance(insured.getHasSocialInsurance());
            groupRnewalInsured.setMainInsuredRelation(insured.getRelationToMaster());
            insureds.add(groupRnewalInsured);
            idx++;
        }

        return renewwalUwr;
    }

    private List<GroupRnewalProductReq> buildGroupRnewalProductReq(ZaGroupRenewalPlans zaGroupRenewalPlans, BigDecimal commissionRate) {
        List<ZaGroupRenewalProduct> zaGroupRenewalProducts = zaGroupRenewalPlans.getProducts();
        if (CollectionUtils.isEmpty(zaGroupRenewalProducts)) {
            return null;
        }
        List<GroupRnewalProductReq> products = Lists.newArrayList();
        GroupRnewalProductReq groupRnewalProductReq = null;
        List<GroupRnewalLiabilitiesReq> liabilities = null;
        for (ZaGroupRenewalProduct zaGroupRenewalProduct : zaGroupRenewalProducts) {
            groupRnewalProductReq = new GroupRnewalProductReq();
            groupRnewalProductReq.setCommissionRate(commissionRate == null ? null : commissionRate.toString());
            groupRnewalProductReq.setProductCode(zaGroupRenewalProduct.getProductCode());
            groupRnewalProductReq.setBaseProductCode(zaGroupRenewalProduct.getBaseProductCode());
            liabilities = buildGroupRnewalLiabilitiesReq(zaGroupRenewalProduct.getLiabilities());
            if (!CollectionUtils.isEmpty(liabilities)) {
                groupRnewalProductReq.setLiabilities(liabilities);
            }
            products.add(groupRnewalProductReq);
        }
        return products;
    }

    private List<GroupRnewalLiabilitiesReq> buildGroupRnewalLiabilitiesReq(List<ZaGroupRenewalLiabilities> zaGroupRenewalLiabilitiesList) {
        if (CollectionUtils.isEmpty(zaGroupRenewalLiabilitiesList)) {
            return null;
        }
        List<GroupRnewalLiabilitiesReq> liabilities = Lists.newArrayList();
        GroupRnewalLiabilitiesReq groupRnewalLiabilitiesReq = null;
        for (ZaGroupRenewalLiabilities zaGroupRenewalLiabilities : zaGroupRenewalLiabilitiesList) {
            groupRnewalLiabilitiesReq = new GroupRnewalLiabilitiesReq();
            groupRnewalLiabilitiesReq.setLiabCode(zaGroupRenewalLiabilities.getLiabCode());
            groupRnewalLiabilitiesReq.setLiabPremium(zaGroupRenewalLiabilities.getLiabPremium());
            liabilities.add(groupRnewalLiabilitiesReq);
        }
        return liabilities;
    }

    /**
     * 众安团险续保校验当前订单被保人职业等级与上年被保人职业等级是否匹配, 如果当年等级在上年找不断哦则报错
     *
     * @param req
     */
    public void checkOccupationGroupListByOrderId(GroupUnderwriting req) {
        if (!req.isRealRenewFlag()) {
            return;
        }
        List<String> orderOccupationGroup = req.getInsuredList().stream().map(x -> {
            return x.getOccupationGroup();
        }).collect(Collectors.toList());
        Set<String> lastOrderOccupationGroup = occupationService.getOccupationGroupListByOrderId(req.getOldOrderId());
        for (String occupationGroup : orderOccupationGroup) {
            if (!lastOrderOccupationGroup.contains(occupationGroup)) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "所录职业类别与历史保单不一致，不支持续保");
            }
        }
    }

    /**
     * 查询新契约订单号
     *
     * @param orderId
     * @return
     */
    private String findFirstOrderId(String orderId) {
        SmOrderMinInfo smOrderMinInfo = orderMapper.querySmOrderMinInfo(orderId);
        return smOrderMinInfo.getFirstNewOrderId();
    }

    /**
     * @param req
     * @param zaGroupRenewalApplyRes
     */
    private void checkZaGroupRenewalDutyCode(GroupUnderwriting req, ZaGroupRenewalApplyRes zaGroupRenewalApplyRes) {
        ZaGroupRenewalApplyResult result = zaGroupRenewalApplyRes.getResult();
        List<ZaGroupRenewalProduct> products = null;
        List<ZaGroupRenewalLiabilities> liabilities = null;
        List<ZaGroupRenewalPlans> plans = result.getPlans();
        Set<String> companyProductSet = Sets.newHashSet();
        Set<String> companyliabCodeSet = Sets.newHashSet();
        for (ZaGroupRenewalPlans plan : plans) {
            products = plan.getProducts();
            for (ZaGroupRenewalProduct product : products) {
                companyProductSet.add(product.getProductCode());
                liabilities = product.getLiabilities();
                for (ZaGroupRenewalLiabilities zaGroupRenewalLiabilities : liabilities) {
                    companyliabCodeSet.add(zaGroupRenewalLiabilities.getLiabCode());
                }
            }
        }

        Set<String> myProductSet = Sets.newHashSet();
        Set<String> myliabCodeSet = Sets.newHashSet();

        ProductReq product = req.getProduct();
        List<Clause> clauseList = product.getClauseList();
        List<Liability> liabilityList = null;
        for (Clause clause : clauseList) {
            myProductSet.add(clause.getClauseCode());
            liabilityList = clause.getLiabilityList();
            for (Liability liability : liabilityList) {
                myliabCodeSet.add(liability.getLiabCode());
            }
        }
        //校验条款是否一致
        compareSetIsSame(companyProductSet, myProductSet);
        //校验责任是否一致
        compareSetIsSame(companyliabCodeSet, myliabCodeSet);
    }

    private void compareSetIsSame(Set<String> firstSet, Set<String> secondSet) {
        if (!Objects.equals(firstSet.size(), secondSet.size())) {
            log.warn("compareSetIsSame firstSet={}; secondSet", JSON.toJSONString(firstSet), JSON.toJSONString(secondSet));
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险续保申请责任与请求责任不一致");
        }
        for (String str : secondSet) {
            if (!firstSet.contains(str)) {
                log.warn("compareSetIsSame firstSet={}; secondSet", JSON.toJSONString(firstSet), JSON.toJSONString(secondSet));
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险续保申请责任与请求责任不一致");
            }
        }
    }

    private Map<String, GroupInsured> getJobCodeMap(GroupUnderwriting req) {
        Map<String, GroupInsured> jobCodeMap = Maps.newHashMap();
        List<GroupInsured> insuredList = req.getInsuredList();
        for (GroupInsured groupInsured : insuredList) {
            jobCodeMap.put(groupInsured.getIdNumber(), groupInsured);
        }
        return jobCodeMap;
    }

    private void checkRenewalDate(ZaGroupRenewalQuoteResp zaGroupRenewalQuoteResp, GroupUnderwriting req) {
        if (!req.getStartTime().equals(zaGroupRenewalQuoteResp.getValidateTime())) {
            log.warn("众安团险续保保单生效时间不一致 myStartTime= {}; companyStartTime= {}", req.getStartTime(), zaGroupRenewalQuoteResp.getValidateTime());
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "续保信息错误，请联系客户经理");
        }
        if (!req.getEndTime().equals(zaGroupRenewalQuoteResp.getExpireTime())) {
            log.warn("众安团险续保保单失效时间不一致 myEndTime(= {}; companyEndTime(= {}", req.getEndTime(), zaGroupRenewalQuoteResp.getExpireTime());
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "续保信息错误，请联系客户经理");
        }
    }

    /**
     * 众安团意险续保调用保司续保申请接口读取保费(增加注释)
     *
     * @param req
     */
    private void checkGroupPremium(GroupUnderwriting req, GroupQuoteResponse groupQuoteResponse) {
        if (!req.isRealRenewFlag()) {
            return;
        }
        ZaApiProperties zaProp = apiService.getZaApiProperties();
        List<SysDutyConfig> dutys = getSysDutys(EnumChannel.ZA.getCode());
        ZaGroupUnderwritingReq uwr = buildApplyVo(zaProp, req, dutys);
        List<SmOrderWaitRenewal> smOrderWaitRenewalList = renewalManagerService.querySmOrderWaitRenewalByPolicyNo(null, req.getOldOrderId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(smOrderWaitRenewalList)) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "未查到续保记录");
        }
        SmOrderWaitRenewal smOrderWaitRenewal = smOrderWaitRenewalList.get(0);
        //众安团险续保新增续保申请接口
        ZaGroupUnderwritingReq zaGroupApplyReq = buildRenewalZaGroupUnderwritingReqForApply(uwr, req, smOrderWaitRenewal.getPolicyNo());
        ZaGroupRenewalApplyRes zaGroupRenewalApplyRes = apiService.renewwalApply4Group(zaGroupApplyReq, req);
//        BigDecimal quoteTotalPremium = new BigDecimal(groupQuoteResponse.getTotalPremium());
        BigDecimal actualityTotalPremium = BigDecimal.ZERO;

        List<ZaGroupRenewalPlans> zaGroupRenewalPlansList = zaGroupRenewalApplyRes.getResult().getPlans();
        List<GroupRnewalProductReq> products = null;
        Map<String, GroupInsured> getJobCodeMap = getJobCodeMap(req);
        Map<String, ZaGroupRenewalPlans> openPlanCodeMap = Maps.newHashMap();
        String openPlanCode = null;
        String occupationGroup = null;
        GroupRnewalPlan groupRnewalPlan = null;
        ZaGroupRenewalPlans zaGroupRenewalPlan = null;

        for (int i = 0; i < zaGroupRenewalPlansList.size(); i++) {
            groupRnewalPlan = new GroupRnewalPlan();
            zaGroupRenewalPlan = zaGroupRenewalPlansList.get(i);
            openPlanCode = zaGroupRenewalPlan.getOpenPlanCode();
            occupationGroup = openPlanCode.substring(openPlanCode.length() - 1, openPlanCode.length());
            if (!openPlanCodeMap.containsKey(occupationGroup)) {
                openPlanCodeMap.put(occupationGroup, zaGroupRenewalPlan);
            }
            groupRnewalPlan.setOpenPlanCode(zaGroupRenewalPlan.getOpenPlanCode());
            groupRnewalPlan.setPlanPremium(zaGroupRenewalPlan.getPlanPremium());
            if (!CollectionUtils.isEmpty(products)) {
                groupRnewalPlan.setProducts(products);
            }
        }
        GroupInsured groupInsured = null;
        for (Insured insured : uwr.getInsuredList()) {
            groupInsured = getJobCodeMap.get(insured.getCertNo());
            zaGroupRenewalPlan = openPlanCodeMap.get(groupInsured.getOccupationGroup());
            actualityTotalPremium = actualityTotalPremium.add(new BigDecimal(zaGroupRenewalPlan.getPlanPremium()));
        }

//        if (quoteTotalPremium.compareTo(actualityTotalPremium) != 0) {
        log.info("actualityTotalPremium= {}", actualityTotalPremium.toPlainString());
        groupQuoteResponse.setShowMessageFlag(Boolean.FALSE);
//        groupQuoteResponse.setShowMessage("由于该续保产品调整了费率，实际保费与询价保费不一致，需按保司计算的实际保费缴费");
        groupQuoteResponse.setTotalPremium(actualityTotalPremium.toPlainString());
//        }
    }

    /**
     * 1.保存投保数据[sm_order_item]
     * 2.更新订单状态
     */
    public void afterApplyForRenewal(String orderId,
                                     Integer productId,
                                     Integer planId,
                                     String proposalNo,
                                     ZaGroupUnderwritingReq uwr, ZaGroupUnderwritingReq zaGroupUnderwritingReq, GroupUnderwriting req) {
        List<GroupRnewalInsured> insureds = zaGroupUnderwritingReq.getInsureds();
        Map<String, GroupRnewalInsured> map = convertMapForRenewal(insureds);
        List<SmOrderItem> orderItems = new ArrayList<>();
        for (Insured entry : uwr.getInsuredList()) {
            SmOrderItem item = new SmOrderItem();
            String certNo = entry.getCertNo();

            item.setAppStatus(SmConstants.ORDER_STATUS_TO_PAY);
            item.setIdNumber(certNo);
            item.setIdType(entry.getCertType());
            item.setType(0);
            item.setProductId(productId);
            item.setFhOrderId(orderId);
            item.setQty(1);
            item.setPlanCode(entry.getProductCode());
            item.setProductId(productId);
            item.setAppStatus(SmConstants.ORDER_STATUS_TO_PAY);
            String unitPremium = "0";
            if (map != null) {
                /**
                 * 统一转成小写（身份证带X字符）
                 */
                certNo = certNo.toLowerCase();
                GroupRnewalInsured ins = map.get(certNo);
                if (ins != null) {
                    unitPremium = ins.getPremium();
                }
            }
            BigDecimal premium = new BigDecimal(unitPremium);
            item.setTotalAmount(premium);
            item.setUnitPrice(premium);
            item.setPlanId(planId);
            orderItems.add(item);
        }
        int r0 = orderMapper.clearTempItem(orderId);
        int r1 = orderItemMapper.insertList(orderItems);
        String appNo = proposalNo;
        if (appNo != null) {
            OrderDTO param = new OrderDTO();
            param.setAppNo(appNo);
            param.setPayStatus(SmConstants.ORDER_STATUS_TO_PAY);
            param.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);
            param.setApplyTime(new Date());
            if (req.isRealRenewFlag()) {
                //如果该单子是续保则保存原待续保的订单编码
                param.setRenewOrderId(req.getOldOrderId());
                String firstOrderId = findFirstOrderId(req.getOldOrderId());
                if (StringUtils.isBlank(firstOrderId)) {
                    //说明基于原单续保
                    param.setFirstOrderId(req.getOldOrderId());
                } else {
                    //说明基于续保单再续保
                    param.setFirstOrderId(firstOrderId);
                }
            }
            int rtn = orderMapper.updateOrder(orderId, param);
            log.info("更新投保订单状态完成,{},{},{},{}", orderId, rtn,r0,r1);
        }
    }

    private Map<String, GroupRnewalInsured> convertMapForRenewal(List<GroupRnewalInsured> insuredList) {
        if (insuredList == null) {
            return null;
        }
        Map<String, GroupRnewalInsured> map = new HashMap<>();
        for (GroupRnewalInsured entry : insuredList) {
            String certNo = entry.getCertNo();
            certNo = certNo.toLowerCase();
            map.put(certNo, entry);
        }
        return map;
    }

    /**
     * 续保保费试算构建新的返回报文
     *
     * @return
     */
    private GroupQuoteResponse buildGroupQuoteResponseForRenewal() {
        ZaQuoteResp resp = new ZaQuoteResp();
        return resp.convertVo();
    }

    /**
     * 解析开票返回电子发票地址
     * @return
     */
    private List<String> parseInvoiceUrlList(ZaInvoiceResp resp){
        List<String> invooiceList = Lists.newArrayList();
        List<ZaInvoiceInfo> zaInvoiceInfoList = resp.getInvoiceInfoList();
        for(ZaInvoiceInfo zaInvoiceInfo : zaInvoiceInfoList){
            invooiceList.add(zaInvoiceInfo.getInvoiceUrl());
        }
        return invooiceList;
    }
}
