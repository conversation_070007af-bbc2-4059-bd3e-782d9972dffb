package com.cfpamf.ms.insur.app.util;

import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * {
 *     "bizCode":"LOAN",
 *     "channel":"app",
 *     "custInfoVo":{
 *         "age":30,
 *         "assessLevel":"1",
 *         "createTime":"2018-08-26 09:22:15.0",
 *         "custDetail":{
 *             "address":"中国湖南省岳阳市平江县",
 *             "birthDay":"19900311",
 *             "center":"073899",
 *             "city":"4306",
 *             "contactSts":"99",
 *             "county":"430626",
 *             "education":"1060",
 *             "email":"<EMAIL>",
 *             "ethnicGroup":"001",
 *             "familyNum":1,
 *             "idNoAddress":"湖南省平江县思村乡北山村109号",
 *             "idNoAward":"攸县公安局",
 *             "idNoEndDate":"9999-12-30",
 *             "idNoStartDate":"2016-09-05",
 *             "incomeSource":[
 *                 "0"
 *             ],
 *             "industry":"01",
 *             "isFocus":false,
 *             "isLocal":"Y",
 *             "labourNum":1,
 *             "liveAddress":"岳阳市平江县思村乡北山村109号",
 *             "loanBranch":"HNPJ",
 *             "loanBranchName":"平江",
 *             "loanCustId":"C03791578",
 *             "loanManager":"HNPJ0017",
 *             "loanManagerName":"何小红",
 *             "localResidentStart":"2017-08-03",
 *             "marriage":"10",
 *             "province":"43",
 *             "registerAddress":"湖南省平江县思村乡北山村109号",
 *             "repayDay":"12",
 *             "residentPeriod":41,
 *             "residentStatus":"00",
 *             "residentType":"01",
 *             "sex":"M",
 *             "subIndustry":[
 *                 "1",
 *                 "2",
 *                 "14"
 *             ],
 *             "supportNum":0,
 *             "town":"430626100",
 *             "village":"430626100001"
 *         },
 *         "custExtAttr":{
 *             "loanCustId":"C03791578"
 *         },
 *         "custMemberCard":{
 *             "createTime":"2018-08-26 09:22:15.0",
 *             "custName":"孔文明",
 *             "id":147899,
 *             "loanCustId":"C03791578",
 *             "updateBy":"HNPJ0001",
 *             "updateTime":"2020-11-26 10:03:46.0"
 *         },
 *         "custName":"孔文明",
 *         "custNo":"C2018082600000013905",
 *         "finishCerfify":false,
 *         "finishCreditApply":false,
 *         "finishFaceDetect":false,
 *         "finishRegister":false,
 *         "id":147899,
 *         "idNo":"******************",
 *         "idType":"20",
 *         "isClosed":false,
 *         "isReal":"01",
 *         "mobile":"13762748988",
 *         "questionFinishPer":0,
 *         "registerStatus":"1",
 *         "sts":"01",
 *         "updateTime":"2020-11-26 10:03:46.0"
 *     },
 *     "custNo":"C2018082600000013905",
 *     "id":183434,
 *     "lastLoginTime":"2020-12-09 14:54:17",
 *     "mobile":"13762748988",
 *     "registerTime":"2018-08-23 20:13:20.0",
 *     "sts":"01",
 *     "userNo":"U2018082300000018038",
 *     "sessionId":"978bbf58-3178-4570-b1cd-b3270c6c9629"
 * }
 * <p></p>
 * 获取http request 参数信息
 */
@UtilityClass
@Slf4j
public class AppUserUtil {

    private static final String USER_HEAD = "x-cfpamf-auth-user-info";


    //
    public static void printAuthUser() {
        HttpServletRequest request = HttpRequestUtil.getRequest();
        String header = request.getHeader(USER_HEAD);
        log.info("x-cfpamf-auth-user-info head :{}", header);
        if (StringUtils.isNotBlank(header)) {

            log.info("user head decode:{}", new String(Base64.getDecoder().decode(header), StandardCharsets.UTF_8));
        }
    }

}
