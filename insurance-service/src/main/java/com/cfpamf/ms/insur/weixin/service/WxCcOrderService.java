package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmPlanSaleOrgMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.PlanMapper;
import com.cfpamf.ms.insur.admin.enums.order.OrderBindStatusEnum;
import com.cfpamf.ms.insur.admin.enums.order.OrderBindTypeEnum;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.external.OrderRenewBindInfo;
import com.cfpamf.ms.insur.admin.external.fh.dto.FastInsuredDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderItemDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.DutyFactorDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmPlanSalesOrg;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmAddCommissionDetail;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRenewBindInfo;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlan;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductRenewRange;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.receiver.service.OrderReceiverInfoService;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalOrderMapper;
import com.cfpamf.ms.insur.admin.renewal.form.WxOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.service.RenewalConfigService;
import com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo;
import com.cfpamf.ms.insur.admin.renewal.vo.ZaGroupRenewalConfigVO;
import com.cfpamf.ms.insur.admin.service.ProductRenewRuleService;
import com.cfpamf.ms.insur.admin.service.SmOrderCoreService;
import com.cfpamf.ms.insur.admin.service.SmOrderRenewBindService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.commission.AddCommissionQueryService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.base.bean.IdName;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.config.PageInfoConfig;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.constant.EnumOrderQueryKeyType;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxUserSettingMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.CmsOrderAmountDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxOrcIdCardDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.SmOrderMinInfo;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ApplicantDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.*;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.DutyFactorFlowQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.policy.GroupPolicyQuery;
import com.cfpamf.ms.insur.weixin.pojo.request.order.OrderBasicVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupApplicant;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoicePolicyListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderDraftList;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.code.kaptcha.Producer;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import net.sf.cglib.beans.BeanCopier;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * 微信客户中心/我的订单,保单Service
 **/
@Slf4j
@Service
public class WxCcOrderService extends WxAbstractService {

    public static Date ENDTIME_2021 = DateUtil.parseDate("2021-12-31 23:59:59");
    public static Date STAETTIME_2022 = DateUtil.parseDate("2022-01-01 00:00:00");
    /**
     * 每个员工 OCR http调用接口次数
     */
    @Value("${aliyun.ocr.dayLimit}")
    private int orcDayLimit;

    @Autowired
    SmProductService productService;
    @Autowired
    ProductRenewRuleService productRenewRuleService;

    /**
     * 业务token service
     */
    @Autowired
    private BusinessTokenService tokenService;

    /**
     * 订单mapper
     */
    @Autowired
    private WxOrderMapper orderMapper;


    /**
     * 订单mapper
     */
    @Autowired
    private SmOrderMapper smOrderMapper;


    /**
     * 下载service
     */
    @Autowired
    private WxDownloadService wxDownloadService;

    /**
     * 订单service
     */
    @Autowired
    private SmOrderCoreService orderService;

    @Autowired
    SmOrderServiceWrapper orderServiceWrapper;

    /**
     * 微信用户设置mapper
     */
    @Autowired
    private WxUserSettingMapper wusMapper;

    /**
     * google验证马生成器
     */
    @Autowired
    private Producer captchaProducer;

    @Autowired
    private OrderReceiverInfoService orderReceiverInfoService;
    @Autowired
    private SmOrderRenewBindService smOrderRenewBindService;
    @Autowired
    private RenewalConfigService renewalConfigService;

    @Autowired
    BmsService bmsService;
    @Autowired
    private SmOrderItemMapper smOrderItemMapper;
    @Autowired
    private AddCommissionQueryService addCommissionQueryService;

    @Autowired
    private EndorMapper endorMapper;

    @Autowired
    private SmPlanSaleOrgMapper planSaleOrgMapper;

    @Autowired
    private RenewalOrderMapper renewalOrderMapper;

    /**
     * 保单下载失败跳转的页面
     */
    @Value("${company.error-redirect-url:}")
    private String errorUrl;

    @Autowired
    private PlanMapper planMapper;

    /**
     * 查询用户微信保单
     *
     * @param query
     * @return
     */
    public PageInfo<WxPolicyListVo> getWxPolicyListByPage(WxOrderQuery query) {
        if (query == null) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = checkAuthority(query.getOpenId(), query.getAuthorization());
        query.setChannel(session.getChannel());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));
        query.setKeyword(CommonUtil.trim(query.getKeyword()));
        query.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        exchangeKeyType(query);

        if (query.getClaimable() != null && query.getClaimable()) {
            if (session.isBindEmployee()) {
                query.setCustomerAdminId(session.getUserId());
            } else if (session.isBindAgent()) {
                query.setAgentId(session.getAgentId());
            }
        } else {
            if (session.isBindEmployee()) {
                query.setUserId(session.getUserId());
            } else if (session.isBindAgent()) {
                query.setAgentId(session.getAgentId());
            }
        }
        log.info("查询保单列表参数:{}", query);
        PageHelper.startPage(query.getPage(), query.getSize());
        List<WxPolicyListVo> wxPolicyList = orderMapper.listWxPolicyList(query);
        wxPolicyList.forEach(odr -> {
            if (Objects.equals(odr.getAgentId(), session.getAgentId())) {
                odr.setAgentId(null);
                odr.setAgentName(null);
            }
        });
        return new PageInfo<>(wxPolicyList);
    }

    public static void exchangeKeyType(WxOrderQuery query) {

        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getInsuredName())) {
            if (Pattern.matches("[\\w\\-]+", query.getInsuredName())) {
                query.setInsuredType(EnumOrderQueryKeyType.INSURED_ID_CARD.getCode());
            } else {
                query.setInsuredType(EnumOrderQueryKeyType.INSURED_NAME.getCode());
            }
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(query.getApplicantName())) {
            if (Pattern.matches("[\\w\\-]+", query.getApplicantName())) {
                query.setApplicantType(EnumOrderQueryKeyType.APPLICANT_ID_CARD.getCode());
            } else {
                query.setApplicantType(EnumOrderQueryKeyType.APPLICANT_NAME.getCode());
            }
        }

    }

    /**
     * 获取本地订单的基础数据
     */
    public SmBaseOrderVO getOrderLocalBaseInfo(String orderId) {

        return orderService.getLocalBaseOrderInfo(orderId);
    }

    /**
     * 查询用户微信订单
     *
     * @param query
     * @return
     */
    public PageInfo<WxOrderListVo> getWxOrderListByPage(WxOrderQuery query) {
        if (query == null) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = checkAuthority(query.getOpenId(), query.getAuthorization());
        PageHelper.startPage(query.getPage(), query.getSize());
        List<WxOrderListVo> wxOrderList = queryOrderList(query, session);

//        PageInfo<WxOrderListVo> pageInfo = PageInfoConfig.list2PageInfo(wxOrderList, query.getPage(), query.getSize());
        //先分页再处理
        revertOrderList(wxOrderList, session);
        return new PageInfo<>(wxOrderList);
    }

    private void revertOrderList(List<WxOrderListVo> wxOrderList, WxSessionVO session) {
        if (!wxOrderList.isEmpty()) {

            List<String> canRenewBindProducts = smOrderRenewBindService.getCanRenewBindProducts();

            List<Integer> productIds = wxOrderList.stream().map(WxOrderListVo::getProductId).collect(Collectors.toList());
            Map<Integer, PlanRenewalConfigVo> renewalConfigVoMap = renewalConfigService.listRenewAfterExpirationDayByProductIds(productIds);

            List<String> fhOrderIdList = wxOrderList.stream()
                    .map(WxOrderListVo::getFhOrderId).collect(Collectors.toList());
            List<IdName> insureds = orderMapper.listOrderInsuredPersonNamesByOrderId(fhOrderIdList);
            List<SmOrderItemDTO> smOrderItems = smOrderItemMapper.listOrderItemByFhOrderIds(fhOrderIdList);
            Map<String, List<SmOrderItemDTO>> itemMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(smOrderItems)) {
                itemMap = LambdaUtils.groupBy(smOrderItems, SmOrderItemDTO::getFhOrderId);
            }
            List<SmOrderRenewBindInfo> bindInfos = smOrderRenewBindService.listByFhOrderIds(fhOrderIdList);

            //wxOrderList.forEach(odr -> {
            for (WxOrderListVo odr : wxOrderList) {
                List<String> sameOrderInsured =
                        insureds.stream().filter(is -> Objects.equals(odr.getFhOrderId(), is.getId())).map(IdName::getName)
                                .collect(Collectors.toList());

                odr.setInsuredPersonName(String.join(BaseConstants.STR_LABEL_COMMA, sameOrderInsured));
                if (Objects.equals(odr.getAgentId(), session.getAgentId())) {
                    odr.setAgentId(null);
                    odr.setAgentName(null);
                }
                //映射显示状态
                odr.setShowStatusName(CommonUtil.getShowStatusName(odr.getPayStatus(), odr.getAppStatus(), odr.getEndTime()));
                //添加是否显示自动绑定
                //addShowBindButton(odr,canRenewBindProducts,renewalConfigVoMap);
                addShowManyInsuredBindButton(odr, itemMap, bindInfos, canRenewBindProducts, renewalConfigVoMap);
            }
        }
        // 设置收件信息
        orderReceiverInfoService.setReceiverInfoDisplay(wxOrderList);
        //设置是否需要回访
        List<Integer> needVisitProductList = bmsService.getVisitProductParam()
                .stream()
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        wxOrderList.forEach(wxOrderListVo -> {
            if (!needVisitProductList.contains(wxOrderListVo.getProductId())) {
                wxOrderListVo.setNeedVisit(0);
            }
        });
    }

    public void addShowManyInsuredBindButton(WxOrderListVo vo, Map<String, List<SmOrderItemDTO>> itemMap, List<SmOrderRenewBindInfo> bindInfos, List<String> canRenewBindProducts, Map<Integer, PlanRenewalConfigVo> renewalConfigVoMap) {
        List<SmOrderItemDTO> smOrderItems = itemMap.get(vo.getFhOrderId());
        if (CollectionUtils.isEmpty(smOrderItems)) {
            if (Objects.equals(vo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                    && (vo.getBindStatus() == null || !Objects.equals(vo.getBindStatus(), OrderBindStatusEnum.BIND.getCode()))
                    && canRenewBindProducts.contains(vo.getProductId() + "")
                    && validateRenewalConfig(vo.getProductId(), vo.getEndTime(), renewalConfigVoMap)) {
                vo.setShowBindButton(Boolean.TRUE);
                List<WxPolicyWaitBindVO> waitBindVOList = new ArrayList<>();
                WxPolicyWaitBindVO waitBindVO = new WxPolicyWaitBindVO();
                waitBindVO.setInsureName(vo.getInsuredPersonName());
                waitBindVO.setPolicyNo(vo.getPolicyNo());
                waitBindVOList.add(waitBindVO);
                vo.setWaitBindVOList(waitBindVOList);
            }
        } else {
            List<WxPolicyWaitBindVO> waitBindVOList = new ArrayList<>();
            vo.setWaitBindVOList(waitBindVOList);
            smOrderItems.forEach(item -> {
                Optional<SmOrderRenewBindInfo> bindInfoOpt = bindInfos.stream().filter(bind -> Objects.equals(item.getFhOrderId(), bind.getFhOrderId())
                        && Objects.equals(item.getPolicyNo(), bind.getPolicyNo())).findFirst();
                //每个具体计划的产品id进行比较
                if (Objects.equals(item.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                        && (!bindInfoOpt.isPresent() || !Objects.equals(bindInfoOpt.get().getBindStatus(), OrderBindStatusEnum.BIND.getCode()))
                        && canRenewBindProducts.contains(item.getProductId() + "")
                        && validateRenewalConfig(item.getProductId(), vo.getEndTime(), renewalConfigVoMap)) {
                    //
                    if (!vo.getShowBindButton()) {
                        vo.setShowBindButton(Boolean.TRUE);
                    }
                    WxPolicyWaitBindVO waitBindVO = new WxPolicyWaitBindVO();
                    waitBindVO.setInsureName(item.getPersonName());
                    waitBindVO.setPolicyNo(item.getPolicyNo());
                    waitBindVOList.add(waitBindVO);
                }

            });

        }
    }

    public void addShowBindButton(WxOrderListVo vo, List<String> canRenewBindProducts, Map<Integer, PlanRenewalConfigVo> renewalConfigVoMap) {

        if (Objects.equals(vo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                && (vo.getBindStatus() == null || !Objects.equals(vo.getBindStatus(), OrderBindStatusEnum.BIND.getCode()))
                && canRenewBindProducts.contains(vo.getProductId() + "")
                && validateRenewalConfig(vo.getProductId(), vo.getEndTime(), renewalConfigVoMap)) {
            vo.setShowBindButton(Boolean.TRUE);
        }
    }

    /**
     * ①保障未到期，仍在保障中的保单 ②保障虽然到期但仍在宽限期内的保单
     *
     * @param productId
     * @param endTime
     * @param renewalConfigVoMap
     * @return
     */
    private boolean validateRenewalConfig(Integer productId, Date endTime,
                                          Map<Integer, PlanRenewalConfigVo> renewalConfigVoMap) {
        PlanRenewalConfigVo configVo = renewalConfigVoMap.get(productId);
        return doValidateRenewalConfig(endTime, configVo);
        /*if(configVo!=null && configVo.getAfterExpirationDay()!=null){
            return DateUtil.getNow().compareTo(DateUtil.addDay(endTime,configVo.getAfterExpirationDay()))<0;
        }else{
            return DateUtil.getNow().compareTo(endTime)<0;
        }*/
    }

    private boolean doValidateRenewalConfig(Date endTime, PlanRenewalConfigVo renewalConfigVoMap) {
        if (renewalConfigVoMap != null && renewalConfigVoMap.getAfterExpirationDay() != null) {
            return DateUtil.getNow()
                    .compareTo(DateUtil.addDay(endTime, renewalConfigVoMap.getAfterExpirationDay())) < 0;
        } else {
            return DateUtil.getNow().compareTo(endTime) < 0;
        }
    }

    /**
     * 查询微信端订单列表
     *
     * @param query
     * @return
     */
    private List<WxOrderListVo> queryOrderList(WxOrderQuery query, WxSessionVO session) {
        //查询暂存单逻辑
        if (SmConstants.ORDER_STATUS_TEMP_STORE.equals(query.getOrderState())) {
            String userId = HttpRequestUtil.getUserId();
            if(StringUtils.isBlank(userId)){
                return Collections.emptyList();
            }
            query.setUserId(userId);
            query.setKeyword(CommonUtil.trim(query.getKeyword()));
            exchangeKeyType(query);
            List<OrderDraftList> orders = orderMapper.queryTemporaryOrderList(query);
            return processDraftOrder(orders);
        } else {
            initQuery(query, session);
            exchangeKeyType(query);
            //条件参数校验，防止全表查询
            if(StringUtils.isBlank(query.getUserId())
                    && Objects.isNull(query.getAgentId())
                    && StringUtils.isBlank(query.getCustomerAdminId())){
                return Collections.emptyList();
            }

            //待支付批单显示在订单列表内
            log.info("查询用户订单列表:{}",JSON.toJSONString(query));
            if (Objects.equals(query.getQueryEndorFlag(), "0")) {
                query.setEndorStatus(0);
            }
            List<WxOrderListVo> wxOrderListVos = orderMapper.listApplyUnionCorrectOrder(query);
//            if (Objects.equals(query.getQueryEndorFlag(), "0")) {
//                query.setEndorStatus(0);
//                List<Endor> endorList = endorMapper.queryEndorByOrderInfo(query);
//                if (!CollectionUtils.isEmpty(endorList)) {
//                    List<WxOrderListVo> wxEndorListVos = endorList.stream().map(x -> {
//                        WxOrderListVo wxOrderListVo = new WxOrderListVo();
//                        wxOrderListVo.setFhOrderId(x.getOrderId());
//
//                        wxOrderListVo.setPolicyNo(x.getPolicyNo());
//                        wxOrderListVo.setEndorsementNo(x.getEndorsementNo());
//                        wxOrderListVo.setPayStatus(Objects.isNull(x.getStatus()) ? "" : x.getStatus() + "");
//                        wxOrderListVo.setTotalAmount(x.getAmount());
//                        wxOrderListVo.setOpType(x.getOpType());
//                        wxOrderListVo.setChannel(x.getChannel());
//                        wxOrderListVo.setCreateTime(x.getCreateTime());
//
//                        return wxOrderListVo;
//                    }).collect(Collectors.toList());
//
//                    wxOrderListVos.addAll(wxEndorListVos);
//                    wxOrderListVos.sort(Comparator.comparing(WxOrderListVo::getCreateTime).reversed().thenComparing(WxOrderListVo::getCreateTime));
//                }
//            }
            return wxOrderListVos;

        }
    }

    /**
     * 处理暂存订单数据
     *
     * @param orders
     */
    private List<WxOrderListVo> processDraftOrder(List<OrderDraftList> orders) {
        List<WxOrderListVo> data = new ArrayList<>(orders.size());
        WxOrderListVo entry;
        for (OrderDraftList draft : orders) {
            entry = new WxOrderListVo();
            BeanUtils.copyProperties(draft, entry);
            OrderBasicVo orderInfo = draft.parseBaseInfo();
            entry.setEndTime(LocalDateUtil.stringToUdate(orderInfo.getEndTime()));
            List<FastInsuredDTO> ins = draft.parseInsured();
            if (ins != null) {
                entry.setInsuredNum(ins.size());
            }
            entry.setAppStatus(SmConstants.ORDER_STATUS_TEMP_STORE);
            entry.setPayStatus(SmConstants.ORDER_STATUS_TO_ORDER);
            String applicant = draft.getApplicant();
            if (StringUtils.isNotBlank(applicant)) {
                GroupApplicant proposerInfo = JSON.parseObject(applicant, GroupApplicant.class);
                entry.setApplicantPersonName(proposerInfo.getPersonName());
            }
            entry.setCreateTime(draft.getCreateTime());
            entry.setFhOrderId(draft.getOrderId());
            entry.setProductName(draft.getProductName());
            entry.setProductId(draft.getProductId());
            groupRenewalHandle(draft, entry);
            data.add(entry);
        }
        return data;
    }

    public void initQuery(WxOrderQuery query, WxSessionVO session) {
        query.setChannel(session.getChannel());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));
        if (session.isBindEmployee()) {
            query.setUserId(session.getUserId());
            query.setOpenId(null);
        } else if (session.isBindAgent()) {
            query.setAgentId(session.getAgentId());
            query.setOpenId(null);
        }
        query.setKeyword(CommonUtil.trim(query.getKeyword()));
    }

    /**
     * 根据订单返回 （第一个被保人信息）
     *
     * @param orderId
     * @param wxOpenId
     * @param authorization
     * @return
     */
    public WxOrderQueryDetailVO getWxOrderByFhOrderId(String orderId, String wxOpenId, String authorization) {
        SmBaseOrderVO localBaseOrderInfo = orderService.getLocalBaseOrderInfo(orderId);
        return getWxOrderById(localBaseOrderInfo.getInsuredId());
    }

    /**
     * 从分享者处获取订单的详情 !!!接口不脱敏，不校验权限
     *
     * @param id
     * @return
     */
    public WxOrderQueryDetailVO getShareOrderById(int id) {
        OrderDetailQuery query = new OrderDetailQuery();
        query.setId(String.valueOf(id));
        return queryOrderDetail(query);
    }

    /**
     * 获取订单的详情 !!!接口校验权限
     *
     * @param id
     * @return
     */
    public WxOrderQueryDetailVO getWxOrderById(int id) {
        OrderDetailQuery query = buildQuery();
        query.setId(String.valueOf(id));
        return queryOrderDetail(query);
    }

    /**
     * 获取关闭状态订单的详情 !!!接口校验权限
     *
     * @param id
     * @return
     */
    public WxOrderQueryDetailVO getWxReorderOrderById(String id) {
        OrderDetailQuery query = buildQuery();
        query.setId(String.valueOf(id));
        return queryOrderReorderDetail(query);
    }

    public WxOrderQueryDetailVO queryOrderReorderDetail(OrderDetailQuery query) {

        List<SmOrderInsuredVO> insuredVOs = orderMapper.getOrderInsuredByOrderId(query.getId());
        if (CollectionUtils.isEmpty(insuredVOs)) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        SmOrderInsuredVO insuredVo = insuredVOs.get(0);

        ApplicantDTO applicantDTO = orderMapper.getApplicant(query.getId());
        if (Objects.isNull(applicantDTO)) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        OrderQueryResponse fhOrderDTO = orderServiceWrapper.getOrderInfoForLocal(query.getId());
        WxOrderQueryDetailVO detailVO = new WxOrderQueryDetailVO();
        BeanUtils.copyProperties(fhOrderDTO, detailVO);
        detailVO.getOrderInfo().setCompanyFullName(insuredVo.getCompanyName());
        detailVO.setAgentMobile(insuredVo.getAgentMobile());
        detailVO.setAgentName(insuredVo.getAgentName());
        detailVO.getInsuredInfos().forEach(ins -> ins.setIsSecurity(insuredVo.getIsSecurity()));
        detailVO.getAppntInfo().setBusinessLicense(applicantDTO.getBusinessLicense());
        detailVO.getAppntInfo().setAddressProvider(applicantDTO.getAddressProvider());
        detailVO.getAppntInfo().setProviceCode(applicantDTO.getProviceCode());
        detailVO.getAppntInfo().setCityCode(applicantDTO.getCityCode());
        detailVO.getAppntInfo().setCountryCode(applicantDTO.getCountryCode());

        // 续保
        detailVO.setRenewedFlag(
                renewalConfigService.getValidateRenewFlag(detailVO.getPlanId(), detailVO.getOrderInfo().getEndTime()));

        if (Objects.nonNull(detailVO.getProductId())) {
            SmProductDetailVO detail = productService.getProductById(detailVO.getProductId());
            if (Objects.nonNull(detail)) {
                detailVO.setH5Url(detail.getH5Url());
                detailVO.setApiType(detail.getApiType());
                detailVO.setProductState(detail.getState());
            }
        }

        if (Objects.nonNull(detailVO.getPlanId())) {
            SmPlanSalesOrg planSalesOrg = new SmPlanSalesOrg();
            planSalesOrg.setPlanId(detailVO.getPlanId());
            List<SmPlanSalesOrg> smPlanSalesOrgs = planSaleOrgMapper.selectEnabled(planSalesOrg);
            detailVO.setSmPlanSalesOrgs(smPlanSalesOrgs);
        }


        // 设置收件信息
        orderReceiverInfoService.setReceiverInfo(detailVO);
        // 设置订单绑定信息
        //addOrderRenewBindInfo(detailVO);
        addOrderManyRenewBindInfo(fhOrderDTO);

        //数据清空
        detailVO.setOrderId(null);
        detailVO.setOrderstatus(null);
        detailVO.getOrderInfo().setOrderId(null);
        detailVO.getOrderInfo().setOrderState(null);
        detailVO.getOrderInfo().setOrderStateName(null);
        detailVO.getOrderInfo().setSubmitTime(null);
        detailVO.getOrderInfo().setUpdateTime(null);
        detailVO.getOrderInfo().setPaymentTime(null);
        detailVO.getOrderInfo().setPayStatus(null);
        detailVO.getOrderInfo().setPayStatusName(null);
        detailVO.getOrderInfo().setPayWay(null);
        detailVO.getPolicyInfo().setDownloadURL(null);
        detailVO.getPolicyInfo().setInsuredSn(null);
        for (OrderQueryResponse.InsuredInfo insuredInfo : detailVO.getInsuredInfos()) {
            insuredInfo.setInsuredSn(null);
        }

        return detailVO;
    }

    public WxOrderQueryDetailVO queryOrderDetail(OrderDetailQuery query) {

        SmOrderInsuredVO insuredVo = orderMapper.getOrderInsured(query);
        if (insuredVo == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }

        OrderQueryResponse fhOrderDTO = orderServiceWrapper.getOrderInfoForLocal(insuredVo.getFhOrderId());
        WxOrderQueryDetailVO detailVO = new WxOrderQueryDetailVO();
        BeanUtils.copyProperties(fhOrderDTO, detailVO);
        detailVO.setId(insuredVo.getId());
        detailVO.getOrderInfo().setCompanyFullName(insuredVo.getCompanyName());
        detailVO.setAgentMobile(insuredVo.getAgentMobile());
        detailVO.setAgentName(insuredVo.getAgentName());
        detailVO.getInsuredInfos().forEach(ins -> ins.setIsSecurity(insuredVo.getIsSecurity()));
        // 续保
        detailVO.setRenewedFlag(
                renewalConfigService.getValidateRenewFlag(detailVO.getPlanId(), detailVO.getOrderInfo().getEndTime()));

        if (Objects.nonNull(detailVO.getProductId())) {
            SmProductDetailVO detail = productService.getProductById(detailVO.getProductId());
            if (Objects.nonNull(detail)) {
                detailVO.setH5Url(detail.getH5Url());
                detailVO.setApiType(detail.getApiType());
            }
        }
        String showStatusName = "";
        if (detailVO.getOrderInfo() != null && detailVO.getPolicyInfo() != null
                && StringUtil.isNotEmpty(detailVO.getOrderInfo().getEndTime())) {
            // 映射显示状态
            showStatusName = CommonUtil.getShowStatusName(detailVO.getOrderInfo().getOrderState(),
                    detailVO.getPolicyInfo().getAppStatus(),
                    DateUtil.parseDate(detailVO.getOrderInfo().getEndTime(), DateUtil.CN_LONG_FORMAT));
        }
        detailVO.setShowStatusName(showStatusName);
        // 设置收件信息
        orderReceiverInfoService.setReceiverInfo(detailVO);
        // 设置订单绑定信息
        //addOrderRenewBindInfo(detailVO);
        addOrderManyRenewBindInfo(fhOrderDTO);
        // 保单账号值policyNo替换sm_order_item 表的th_policy_no字段
        policyAccountReplacement(detailVO);
        return detailVO;
    }

    /**
     * 保单账号值替换
     *
     * @param detailVO
     */
    private void policyAccountReplacement(WxOrderQueryDetailVO detailVO) {
        final Map<String, String> documentsAndPoliciesMap = new HashMap<>();
        final SmOrderItemMapper smOrderItemMapper = SpringFactoryUtil.getBean(SmOrderItemMapper.class);
        if (!Objects.isNull(smOrderItemMapper) && !com.alibaba.druid.util.StringUtils.isEmpty(detailVO.getOrderId())) {
            final List<SmOrderItem> smOrderItems = smOrderItemMapper.selectByOrderId(detailVO.getOrderId());
            if (!CollectionUtils.isEmpty(smOrderItems)) {
                Map<String, String> documents = smOrderItems.stream()
                        .filter(e -> !com.alibaba.druid.util.StringUtils.isEmpty(e.getThPolicyNo()) && !com.alibaba.druid.util.StringUtils.isEmpty(e.getIdNumber()))
                        .collect(toMap(SmOrderItem::getPolicyNo, SmOrderItem::getThPolicyNo, (s, t) -> t));
                documentsAndPoliciesMap.putAll(documents);
            }
        }
        detailVO.getPolicyInfos().stream().filter(e -> documentsAndPoliciesMap.containsKey(e.getPolicyNo())).forEach(e -> {
            e.setPolicyNo(documentsAndPoliciesMap.get(e.getPolicyNo()));
        });
    }

    private void addOrderManyRenewBindInfo(OrderQueryResponse fhOrderDTO) {
        List<String> canRenewBindProducts = smOrderRenewBindService.getCanRenewBindProducts();
        List<OrderQueryResponse.PolicyInfo> policyInfos = fhOrderDTO.getPolicyInfos();
        String fhOrderId = fhOrderDTO.getOrderId();
        List<SmOrderRenewBindInfo> infos = smOrderRenewBindService.listByFhOrderId(fhOrderId);
        List<SmOrderItemDTO> smOrderItems = smOrderItemMapper.listOrderItemByFhOrderIds(Arrays.asList(new String[]{fhOrderDTO.getOrderId()}));
        //没有多个保单信息
        if (!CollectionUtils.isEmpty(smOrderItems)) {
            List<Integer> productIds = smOrderItems.stream().map(SmOrderItemDTO::getProductId).collect(Collectors.toList());
            Map<Integer, PlanRenewalConfigVo> renewalConfigVoMap = renewalConfigService.listRenewAfterExpirationDayByProductIds(productIds);
            smOrderItems.forEach(item -> {
                //不存在，则系统数据就有问题
                OrderQueryResponse.PolicyInfo policyInfo = policyInfos.stream().filter(policy -> Objects.equals(item.getFhOrderId(), fhOrderId)
                        && Objects.equals(item.getPolicyNo(), policy.getPolicyNo())).findFirst().get();

                Optional<SmOrderRenewBindInfo> bindInfoOpt = infos.stream().filter(bind -> Objects.equals(item.getFhOrderId(), bind.getFhOrderId())
                        && Objects.equals(item.getPolicyNo(), bind.getPolicyNo())).findFirst();
                OrderRenewBindInfo renewBindInfo = new OrderRenewBindInfo();
                if (bindInfoOpt.isPresent()) {
                    toOrderRenewBindInfo(renewBindInfo, bindInfoOpt.get());
                }
                if (validateRenewalConfig(item.getProductId(), DateUtil.parseDate(fhOrderDTO.getOrderInfo().getEndTime()), renewalConfigVoMap)) {
                    addShowBindButtonV2(policyInfo, renewBindInfo, canRenewBindProducts, item.getProductId());
                }
                policyInfo.setOrderRenewBindInfo(renewBindInfo);
                if (Objects.equals(fhOrderDTO.getPolicyInfo().getPolicyNo(), policyInfo.getPolicyNo())) {
                    fhOrderDTO.setPolicyInfo(policyInfo);
                }
            });

        } else {
            PlanRenewalConfigVo renewalConfigVo = renewalConfigService.listRenewAfterExpirationDayByProductId(fhOrderDTO.getProductId());
            OrderRenewBindInfo renewBindInfo = new OrderRenewBindInfo();
            if (!CollectionUtils.isEmpty(infos)) {
                toOrderRenewBindInfo(renewBindInfo, infos.get(0));
            }
            if (doValidateRenewalConfig(DateUtil.parseDate(fhOrderDTO.getOrderInfo().getEndTime()), renewalConfigVo)) {
                addShowBindButtonV2(fhOrderDTO.getPolicyInfo(), renewBindInfo, canRenewBindProducts, fhOrderDTO.getProductId());
            }

            fhOrderDTO.getPolicyInfo().setOrderRenewBindInfo(renewBindInfo);
            fhOrderDTO.getPolicyInfos().forEach(info -> {
                info.setOrderRenewBindInfo(renewBindInfo);
            });
        }
    }

    private void toOrderRenewBindInfo(OrderRenewBindInfo renewBindInfo, SmOrderRenewBindInfo sm) {
        renewBindInfo.setAccountName(sm.getAccountName());
        renewBindInfo.setAccountNo(sm.getAccountNo());
        renewBindInfo.setBankName(sm.getBankName());
        renewBindInfo.setBindStatus(sm.getBindStatus());
        renewBindInfo.setBindStatusName(OrderBindStatusEnum.dict(sm.getBindStatus()));
        renewBindInfo.setBindType(sm.getBindType());
        renewBindInfo.setBindTypeName(OrderBindTypeEnum.dict(sm.getBindType()));
    }

    private void addShowBindButtonV2(OrderQueryResponse.PolicyInfo policyInfo, OrderRenewBindInfo renewBindInfo, List<String> canRenewBindProducts, Integer productId) {
        if (Objects.equals(policyInfo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                && (renewBindInfo.getBindStatus() == null || !Objects.equals(renewBindInfo.getBindStatus(), OrderBindStatusEnum.BIND.getCode()))
                && canRenewBindProducts.contains(productId + "")) {
            renewBindInfo.setShowBindButton(Boolean.TRUE);
        }
    }

    private void addOrderRenewBindInfo(OrderQueryResponse fhOrderDTO) {
        List<String> canRenewBindProducts = smOrderRenewBindService.getCanRenewBindProducts();
        log.info("支持的绑卡续保产品{}", canRenewBindProducts);
        if (canRenewBindProducts.contains(fhOrderDTO.getProductId() + "")) {
            PlanRenewalConfigVo renewalConfigVoMap =
                    renewalConfigService.listRenewAfterExpirationDayByProductId(fhOrderDTO.getProductId());
            List<SmOrderRenewBindInfo> infos = smOrderRenewBindService.listByFhOrderId(fhOrderDTO.getOrderId());
            OrderRenewBindInfo renewBindInfo = new OrderRenewBindInfo();
            String bindStatus = null;
            if (!CollectionUtils.isEmpty(infos)) {
                SmOrderRenewBindInfo sm = infos.get(0);
                renewBindInfo.setAccountName(sm.getAccountName());
                renewBindInfo.setAccountNo(sm.getAccountNo());
                renewBindInfo.setBankName(sm.getBankName());
                renewBindInfo.setBindStatus(sm.getBindStatus());
                renewBindInfo.setBindStatusName(OrderBindStatusEnum.dict(sm.getBindStatus()));
                renewBindInfo.setBindType(sm.getBindType());
                renewBindInfo.setBindTypeName(OrderBindTypeEnum.dict(sm.getBindType()));
                bindStatus = sm.getBindStatus();
            }
            if (Objects.equals(fhOrderDTO.getPolicyInfo().getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)
                    && (bindStatus == null || !Objects.equals(bindStatus, OrderBindStatusEnum.BIND.getCode()))
                    && doValidateRenewalConfig(DateUtil.parseDate(fhOrderDTO.getOrderInfo().getEndTime()),
                    renewalConfigVoMap)) {
                renewBindInfo.setShowBindButton(Boolean.TRUE);
            }
            fhOrderDTO.setOrderRenewBindInfo(renewBindInfo);
        }
    }

    /**
     * 获取微信用户中心统计信息
     *
     * @param openId
     * @param authorization
     * @return
     */
    public WxUserHomeVo getWxCcCmsSmy(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        WxCmsQuery query = new WxCmsQuery();
        query.setUserId(session.getUserId());
        query.setChannel(session.getChannel());
        Date today = new Date();
        query.setStartTime(CommonUtil.getStartTimeOfDay(today));
        query.setEndTime(CommonUtil.getEndTimeOfDay(today));
        WxCmsSmyVo todaySmy = orderMapper.getWxPolicySummary(query);
        query.setStartTime(CommonUtil.getStartTimeOfMonth(today));
        query.setEndTime(null);
        WxCmsSmyVo monthSmy = orderMapper.getWxPolicySummary(query);
        query.setStartTime(null);
        WxCmsSmyVo addUpSmy = orderMapper.getWxPolicySummary(query);


        WxUserHomeVo homeVo = new WxUserHomeVo();
        if (todaySmy != null && todaySmy.getCmsAmount() != null) {
            homeVo.setTodayTotal(todaySmy.getCmsAmount());
        } else {
            homeVo.setTodayTotal(BigDecimal.ZERO);
        }
        if (monthSmy != null && monthSmy.getCmsAmount() != null) {
            homeVo.setThisMonthTotal(monthSmy.getCmsAmount());
        } else {
            homeVo.setThisMonthTotal(BigDecimal.ZERO);
        }
        if (addUpSmy != null && addUpSmy.getCmsAmount() != null) {
            homeVo.setAddUpTotal(addUpSmy.getCmsAmount());
        } else {
            homeVo.setAddUpTotal(BigDecimal.ZERO);
        }
        WxUserSettingVO settingVO =
                wusMapper.getUserShowCmsSetting(session.getUserId(), SmConstants.WX_USER_SETTING_CODE_CMS);
        if (settingVO != null) {
            homeVo.setShowCmsRatio(Boolean.valueOf(settingVO.getStValue()));
        } else {
            homeVo.setShowCmsRatio(Boolean.FALSE);
        }
        return homeVo;
    }

    /**
     * 获取微信用户中心统计信息
     *
     * @param openId
     * @param authorization
     * @return
     */
    public WxUserHomeVo getWxCcCmsSmyV3(String openId, String authorization) {
        WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        WxCmsQuery query = new WxCmsQuery();
        query.setUserId(session.getUserId());
        query.setChannel(session.getChannel());
        Date today = new Date();
        query.setStartTime(CommonUtil.getStartTimeOfDay(today));
        query.setEndTime(CommonUtil.getEndTimeOfDay(today));
        WxCmsSmyVo todaySmy = orderMapper.getWxPolicySummaryV3(query);
        query.setStartTime(CommonUtil.getStartTimeOfMonth(today));
        query.setEndTime(null);
        WxCmsSmyVo monthSmy = orderMapper.getWxPolicySummaryV3(query);

        //累计数据获取
        query.setStartTime(STAETTIME_2022);
        WxCmsSmyVo addUpSmy = orderMapper.getWxPolicySummaryV3(query);

        WxCmsQuery oldQuery = new WxCmsQuery();
        oldQuery.setUserId(session.getUserId());
        oldQuery.setChannel(session.getChannel());
        oldQuery.setStartTime(null);
        oldQuery.setEndTime(ENDTIME_2021);
        WxCmsSmyVo oldAddUpSmy = orderMapper.getWxPolicySummary(oldQuery);
        if (oldAddUpSmy != null) {
            BigDecimal cms = oldAddUpSmy.getCmsAmount() != null ? oldAddUpSmy.getCmsAmount() : BigDecimal.ZERO;
            BigDecimal add = oldAddUpSmy.getAddCommissionAmount() != null ? oldAddUpSmy.getAddCommissionAmount() : BigDecimal.ZERO;
            if (addUpSmy != null) {
                addUpSmy.setCmsAmount(addUpSmy.getCmsAmount() != null ? addUpSmy.getCmsAmount().add(cms) : cms);
                addUpSmy.setAddCommissionAmount(addUpSmy.getAddCommissionAmount() != null ? addUpSmy.getAddCommissionAmount().add(add) : add);
            } else {
                addUpSmy = oldAddUpSmy;
            }
        }


        WxUserHomeVo homeVo = new WxUserHomeVo();
        if (todaySmy != null && todaySmy.getCmsAmount() != null) {
            BigDecimal addAmt = todaySmy.getAddCommissionAmount() != null ? todaySmy.getAddCommissionAmount() : BigDecimal.ZERO;
            homeVo.setTodayTotal(todaySmy.getCmsAmount().add(addAmt));
        } else {
            homeVo.setTodayTotal(BigDecimal.ZERO);
        }
        if (monthSmy != null && monthSmy.getCmsAmount() != null) {
            BigDecimal addAmt = monthSmy.getAddCommissionAmount() != null ? monthSmy.getAddCommissionAmount() : BigDecimal.ZERO;
            homeVo.setThisMonthTotal(monthSmy.getCmsAmount().add(addAmt));
        } else {
            homeVo.setThisMonthTotal(BigDecimal.ZERO);
        }
        if (addUpSmy != null && addUpSmy.getCmsAmount() != null) {
            BigDecimal addAmt = addUpSmy.getAddCommissionAmount() != null ? addUpSmy.getAddCommissionAmount() : BigDecimal.ZERO;
            homeVo.setAddUpTotal(addUpSmy.getCmsAmount().add(addAmt));
        } else {
            homeVo.setAddUpTotal(BigDecimal.ZERO);
        }
        WxUserSettingVO settingVO =
                wusMapper.getUserShowCmsSetting(session.getUserId(), SmConstants.WX_USER_SETTING_CODE_CMS);
        if (settingVO != null) {
            homeVo.setShowCmsRatio(Boolean.valueOf(settingVO.getStValue()));
        } else {
            homeVo.setShowCmsRatio(Boolean.FALSE);
        }
        return homeVo;
    }

    /**
     * 获取微信用户中心统计信息
     *
     * @param authorization
     * @return
     */
    public WxUserHomeVo getWxCcCmsSmyWithoutOpenId(String authorization) {
        UserDetailVO userDetailVO = bmsService.getUserDetailByToken(authorization);
        //WxSessionVO session = checkAuthorityEmployee(openId, authorization);
        WxCmsQuery query = new WxCmsQuery();
        query.setUserId(userDetailVO.getJobNumber());
        //query.setChannel(session.getChannel());
        Date today = new Date();
        query.setStartTime(CommonUtil.getStartTimeOfDay(today));
        query.setEndTime(CommonUtil.getEndTimeOfDay(today));
        WxCmsSmyVo todaySmy = orderMapper.getWxPolicySummaryV3(query);
        query.setStartTime(CommonUtil.getStartTimeOfMonth(today));
        query.setEndTime(null);
        WxCmsSmyVo monthSmy = orderMapper.getWxPolicySummaryV3(query);

        //累计数据获取
        query.setStartTime(STAETTIME_2022);
        WxCmsSmyVo addUpSmy = orderMapper.getWxPolicySummaryV3(query);

        WxCmsQuery oldQuery = new WxCmsQuery();
        oldQuery.setUserId(userDetailVO.getJobNumber());
        //oldQuery.setChannel(session.getChannel());
        oldQuery.setStartTime(null);
        oldQuery.setEndTime(ENDTIME_2021);
        WxCmsSmyVo oldAddUpSmy = orderMapper.getWxPolicySummary(oldQuery);
        if (oldAddUpSmy != null) {
            BigDecimal cms = oldAddUpSmy.getCmsAmount() != null ? oldAddUpSmy.getCmsAmount() : BigDecimal.ZERO;
            BigDecimal add = oldAddUpSmy.getAddCommissionAmount() != null ? oldAddUpSmy.getAddCommissionAmount() : BigDecimal.ZERO;
            if (addUpSmy != null) {
                addUpSmy.setCmsAmount(addUpSmy.getCmsAmount() != null ? addUpSmy.getCmsAmount().add(cms) : cms);
                addUpSmy.setAddCommissionAmount(addUpSmy.getAddCommissionAmount() != null ? addUpSmy.getAddCommissionAmount().add(add) : add);
            } else {
                addUpSmy = oldAddUpSmy;
            }
        }


        WxUserHomeVo homeVo = new WxUserHomeVo();
        if (todaySmy != null && todaySmy.getCmsAmount() != null) {
            BigDecimal addAmt = todaySmy.getAddCommissionAmount() != null ? todaySmy.getAddCommissionAmount() : BigDecimal.ZERO;
            homeVo.setTodayTotal(todaySmy.getCmsAmount().add(addAmt));
        } else {
            homeVo.setTodayTotal(BigDecimal.ZERO);
        }
        if (monthSmy != null && monthSmy.getCmsAmount() != null) {
            BigDecimal addAmt = monthSmy.getAddCommissionAmount() != null ? monthSmy.getAddCommissionAmount() : BigDecimal.ZERO;
            homeVo.setThisMonthTotal(monthSmy.getCmsAmount().add(addAmt));
        } else {
            homeVo.setThisMonthTotal(BigDecimal.ZERO);
        }
        if (addUpSmy != null && addUpSmy.getCmsAmount() != null) {
            BigDecimal addAmt = addUpSmy.getAddCommissionAmount() != null ? addUpSmy.getAddCommissionAmount() : BigDecimal.ZERO;
            homeVo.setAddUpTotal(addUpSmy.getCmsAmount().add(addAmt));
        } else {
            homeVo.setAddUpTotal(BigDecimal.ZERO);
        }
        WxUserSettingVO settingVO =
                wusMapper.getUserShowCmsSetting(userDetailVO.getJobNumber(), SmConstants.WX_USER_SETTING_CODE_CMS);
        if (settingVO != null) {
            homeVo.setShowCmsRatio(Boolean.valueOf(settingVO.getStValue()));
        } else {
            homeVo.setShowCmsRatio(Boolean.FALSE);
        }
        return homeVo;
    }

    /**
     * 获取微信用户提成列表
     *
     * @param query
     * @return
     */
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsList(WxCmsQuery query) {
        WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        PageHelper.startPage(query.getPage(), query.getSize());
        query.setUserId(session.getUserId());
        query.setChannel(session.getChannel());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));
        List<WxUserCmsListVo> userCmsList = orderMapper.listWxPolicyCmsList(query);
        //设置加佣
        addCommission(userCmsList);

        SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> summaryPage = new SmyPageInfo<>(userCmsList);
        WxCmsSmyVo smyVo = orderMapper.getWxPolicySummary(query);
        if (smyVo == null) {
            smyVo = new WxCmsSmyVo();
            smyVo.setOrderQty(0);
            smyVo.setOrderAmount(BigDecimal.ZERO);
            smyVo.setCmsAmount(BigDecimal.ZERO);
            smyVo.setAddCommissionAmount(BigDecimal.ZERO);
            summaryPage.setSummary(smyVo);
            return summaryPage;
        }
        //统计金额加上加佣金额
        BigDecimal addCommissionAmount = getAddCommissionAmount(query);
        smyVo.setAddCommissionAmount(addCommissionAmount);
        summaryPage.setSummary(smyVo);
        return summaryPage;
    }

    private BigDecimal getAddCommissionAmount(WxCmsQuery query) {
        List<CmsOrderAmountDTO> orderAmount = orderMapper.getOrderAddCommissionAmount(query);
        if (CollectionUtils.isEmpty(orderAmount)) {
            return BigDecimal.ZERO;
        }
        List<String> orderIdList = orderAmount.stream()
                .map(CmsOrderAmountDTO::getFhOrderId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, BigDecimal> orderAddCommissionAmountMap = addCommissionQueryService.getInsuredPersonAddCommissionMap(orderIdList);


        orderAmount.forEach(cmsOrderAmountDTO -> {
            cmsOrderAmountDTO.setAddCommissionAmount(orderAddCommissionAmountMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(cmsOrderAmountDTO.getFhOrderId(), cmsOrderAmountDTO.getPolicyNo(), cmsOrderAmountDTO.getInsuredIdNumber()), BigDecimal.ZERO));
        });

        return orderAmount.stream()
                .map(CmsOrderAmountDTO::getAddCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取微信用户提成列表
     *
     * @param query
     * @return
     */
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsListV3(WxCmsQuery query) {
        WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        PageHelper.startPage(query.getPage(), query.getSize());
        query.setUserId(session.getUserId());
        query.setChannel(session.getChannel());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));
        List<WxUserCmsListVo> userCmsList = orderMapper.listWxPolicyCmsListV3(query);
        //设置加佣
        addCommission(userCmsList);

        SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> summaryPage = new SmyPageInfo<>(userCmsList);
        WxCmsSmyVo smyVo = orderMapper.getWxPolicySummaryV3(query);
        if (smyVo == null) {
            smyVo = new WxCmsSmyVo();
            smyVo.setOrderQty(0);
            smyVo.setOrderAmount(BigDecimal.ZERO);
            smyVo.setCmsAmount(BigDecimal.ZERO);
        }
        //统计金额加上加佣金额 todo
        BigDecimal addCommissionAmount = getAddCommissionAmount(query);
        smyVo.setAddCommissionAmount(addCommissionAmount);
        summaryPage.setSummary(smyVo);
        return summaryPage;
    }

    /**
     * 获取微信用户提成列表
     * 如果查询结束时间<="2021-12-31 23:59:59",则直接查老的佣金表
     * 如果查询 开始时间<=2021-12-31 23:59:59 结束时间 >= 2022-01-01 00:00:00,统计值从两个新、老表里获取，明细直接从新表获取(存在的问题就是新表里没有数据)
     *
     * @param query
     * @return
     */
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsListV4(WxCmsQuery query) {
        //如果查询结束时间<="2021-12-31 23:59:59",则直接查老的佣金表
        if (query.getEndTime() != null && query.getEndTime().compareTo(ENDTIME_2021) <= 0) {
            return getWxUserCmsList(query);
        }

        WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        PageHelper.startPage(query.getPage(), query.getSize());
        query.setUserId(session.getUserId());
        query.setChannel(session.getChannel());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));

        //是否需要查询老的佣金表数据
        WxCmsQuery queryOld = null;
        if (query.getStartTime() == null || (query.getStartTime().compareTo(ENDTIME_2021) <= 0
                && (query.getEndTime() == null || query.getEndTime().compareTo(STAETTIME_2022) >= 0))) {
            queryOld = new WxCmsQuery();
            BeanUtils.copyProperties(query, queryOld);
            queryOld.setEndTime(ENDTIME_2021);
            query.setStartTime(STAETTIME_2022);
        }
        List<WxUserCmsListVo> userCmsList = orderMapper.listWxPolicyCmsListV3(query);
        //设置加佣
        addCommission(userCmsList);

        SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> summaryPage = new SmyPageInfo<>(userCmsList);
        WxCmsSmyVo smyVo = orderMapper.getWxPolicySummaryV3(query);
        if (queryOld != null) {
            WxCmsSmyVo oldSmyVo = getOldWxCmsSmyVo(queryOld);

            if (oldSmyVo != null && smyVo != null) {
                BigDecimal oldCmsAmount = oldSmyVo.getCmsAmount() != null ? oldSmyVo.getCmsAmount() : BigDecimal.ZERO;
                smyVo.setCmsAmount(smyVo.getCmsAmount() != null ? smyVo.getCmsAmount().add(oldCmsAmount) : oldCmsAmount);
                BigDecimal oldOrderAmount = oldSmyVo.getOrderAmount() != null ? oldSmyVo.getOrderAmount() : BigDecimal.ZERO;
                smyVo.setOrderAmount(smyVo.getOrderAmount() != null ? smyVo.getOrderAmount().add(oldOrderAmount) : oldOrderAmount);
                Integer oldQrderQty = oldSmyVo.getOrderQty() != null ? oldSmyVo.getOrderQty() : 0;
                smyVo.setOrderQty(smyVo.getOrderQty() != null ? smyVo.getOrderQty() + oldQrderQty : oldQrderQty);
                BigDecimal oldAdd = oldSmyVo.getAddCommissionAmount() != null ? oldSmyVo.getAddCommissionAmount() : BigDecimal.ZERO;
                smyVo.setAddCommissionAmount(smyVo.getAddCommissionAmount() != null ? smyVo.getAddCommissionAmount().add(oldAdd) : oldAdd);

            } else if (smyVo == null) {
                smyVo = oldSmyVo;
            }
        }

        if (smyVo == null) {
            smyVo = new WxCmsSmyVo();
            smyVo.setOrderQty(0);
            smyVo.setOrderAmount(BigDecimal.ZERO);
            smyVo.setCmsAmount(BigDecimal.ZERO);
        }
        //统计金额加上加佣金额
        //BigDecimal addCommissionAmount = getAddCommissionAmount(query);
        //smyVo.setAddCommissionAmount(addCommissionAmount);
        summaryPage.setSummary(smyVo);
        return summaryPage;
    }

    /**
     * 获取老的佣金合计，（2022-01-01以前的数据合计）
     *
     * @param queryOld
     * @return
     */
    public WxCmsSmyVo getOldWxCmsSmyVo(WxCmsQuery queryOld) {
        //查询是去年全部所有的
        if (queryOld.getStartTime() == null) {
            return getAllOldCommissionSmy(queryOld);
        }
        return orderMapper.getWxPolicySummary(queryOld);
    }

    private WxCmsSmyVo getAllOldCommissionSmy(WxCmsQuery queryOld) {
        String redisKey = String.join(SmConstants.SEPARATOR_1, queryOld.getUserId(), queryOld.getChannel(), queryOld.getOrderType() == null ? "" : queryOld.getOrderType().toString());
        String json = redisUtil.get(redisKey);
        WxCmsSmyVo vo = null;
        if (!com.alibaba.druid.util.StringUtils.isEmpty(json)) {
            vo = JSON.parseObject(json, WxCmsSmyVo.class);
        } else {
            vo = orderMapper.getWxPolicySummary(queryOld);
            redisUtil.set(redisKey, JSON.toJSONString(vo));
        }
        return vo;
    }


    public void getWxUserCmsListV4Condition(WxCmsQuery query) {
        WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        PageHelper.startPage(query.getPage(), query.getSize());
        WxCmsQuery queryOld = null;
        //如果查询条件
        if (query.getStartTime() == null || (query.getStartTime().compareTo(ENDTIME_2021) <= 0
                && (query.getEndTime() == null || query.getEndTime().compareTo(STAETTIME_2022) >= 0))) {
            queryOld = new WxCmsQuery();
            BeanUtils.copyProperties(query, queryOld);
            queryOld.setEndTime(ENDTIME_2021);

            query.setStartTime(STAETTIME_2022);
        } else if (query.getEndTime() != null && query.getEndTime().compareTo(ENDTIME_2021) <= 0) {
            BeanUtils.copyProperties(query, queryOld);
            query = null;
        }
    }


    private void addCommission(List<WxUserCmsListVo> userCmsList) {
        if (!CollectionUtils.isEmpty(userCmsList)) {
            List<String> orderIdList = userCmsList.stream()
                    .map(WxUserCmsListVo::getFhOrderId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<String, BigDecimal> addCommissionAmountMap = addCommissionQueryService.getLongInsuredPersonAddCommissionMap(orderIdList);
            Map<String, BigDecimal> orderAddCommissionProportionMap = addCommissionQueryService.getLongInsuredPersonAddCommissionProportionMap(orderIdList);

            List<String> insuredIdNumbers = userCmsList.stream()
                    .map(WxUserCmsListVo::getInsuredIdNumber)
                    .distinct()
                    .collect(Collectors.toList());
            List<SmAddCommissionDetail> commissionDetails = addCommissionQueryService.getOrderAddCommissionListByIdNumbers(insuredIdNumbers);

            userCmsList.forEach(wxUserCmsListVo -> {
                BigDecimal addCommissionAmount = addCommissionAmountMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(wxUserCmsListVo.getFhOrderId(), wxUserCmsListVo.getPolicyNo(), wxUserCmsListVo.getInsuredIdNumber(), wxUserCmsListVo.getTermNum() + ""), BigDecimal.ZERO);
                //退保的情况下加佣金额要为负数
                if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) > 0 && Objects.equals(wxUserCmsListVo.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    BigDecimal proportion = orderAddCommissionProportionMap.getOrDefault(addCommissionQueryService.getInsuredPersonAddCommissionUuid(wxUserCmsListVo.getFhOrderId(), wxUserCmsListVo.getPolicyNo(), wxUserCmsListVo.getInsuredIdNumber(), wxUserCmsListVo.getTermNum() + ""), BigDecimal.ZERO);
                    if (proportion != null && proportion.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal totalAmount = Objects.isNull(wxUserCmsListVo.getOriginalAmount()) ? wxUserCmsListVo.getTotalAmount() : wxUserCmsListVo.getOriginalAmount().multiply(new BigDecimal("-1"));
                        addCommissionAmount = totalAmount.multiply(proportion).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP);
                    } else {
                        addCommissionAmount = addCommissionAmount.multiply(new BigDecimal("-1"));
                    }
                } else if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) < 0 && Objects.equals(wxUserCmsListVo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                    addCommissionAmount = addCommissionAmount.multiply(new BigDecimal("-1"));
                } else if (addCommissionAmount != null && addCommissionAmount.compareTo(BigDecimal.ZERO) == 0
                        && Objects.equals(wxUserCmsListVo.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    //团险批减单按实际加佣比例显示
                    if (wxUserCmsListVo.getCreateTime() != null) {
                        List<SmAddCommissionDetail> details = commissionDetails.stream().filter(x -> (x.getCreateTime().compareTo(LocalDateTime.ofInstant(wxUserCmsListVo.getCreateTime().toInstant(), ZoneId.systemDefault())) < 0
                                        && x.getOrderId().split("_")[0].equals(wxUserCmsListVo.getFhOrderId().split("_")[0])
                                        && x.getInsuredIdNumber().equals(wxUserCmsListVo.getInsuredIdNumber())))
                                .collect(Collectors.toList())
                                .stream()
                                .sorted(Comparator.comparing(SmAddCommissionDetail::getCreateTime))
                                .limit(1).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(details)) {
                            addCommissionAmount = wxUserCmsListVo.getTotalAmount().multiply(details.get(0).getProportion()).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP);
                        }
                    }
                }
                wxUserCmsListVo.setAddCommissionAmount(addCommissionAmount);
            });
        }
    }

    /**
     * 更新用户微信订单与泛华同步
     *
     * @param fhOrderId
     * @return
     */
    public void updateWxOrder(String fhOrderId, String wxOpenId, String authorization) {
        checkAuthorityEpAg(wxOpenId, authorization);
        orderService.updateOrderPolicyInfo(fhOrderId);
    }

    /**
     * 删除用户微信订单
     *
     * @param fhOrderId
     * @return
     */
    public void deleteWxOrder(String fhOrderId, String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthorityEpAg(wxOpenId, authorization);
        orderMapper.deleteWxOrder(fhOrderId, session.getContextUser());
    }

    /**
     * 保单下载
     *
     * @param id
     * @param openId
     * @param authorization
     * @return
     */
    public void downloadPolicy(int id, String openId, String authorization, HttpServletResponse response) {
        checkAuthority(openId, authorization);
        commonDownloadPolicy(id, response);
    }

    public String downloadPolicyURL(int id, String openId, String authorization) {
        checkAuthority(openId, authorization);
        return commonDownloadPolicy(id);
    }

    /**
     * 保单下载
     *
     * @param endorsementNo
     * @param openId
     * @param authorization
     * @return
     */
    public void downloadEndorPolicy(String endorsementNo, String openId, String authorization, HttpServletResponse response) {
        checkAuthority(openId, authorization);
        SmOrderInsuredVO insuredVO = null;
        String url = "";
        String policyNo = null;
        try {
            Endor endor = endorMapper.queryByEndorNo(endorsementNo, null);
            if (endor != null) {
                url = endor.getEPolicyUrl();
                policyNo = endor.getPolicyNo();
            }


            if (StringUtils.isBlank(url)) {
                insuredVO = orderMapper.getOrderInsuredByEndorsementNo(endorsementNo);
                if (Objects.isNull(insuredVO)) {
                    response.sendRedirect(String.format(errorUrl, URLEncoder.encode("保单下载失败,请稍后重试", StandardCharsets.UTF_8.name())));
                }
                url = insuredVO.getDownloadURL();
                policyNo = insuredVO.getPolicyNo();
            }

            if (StringUtils.isBlank(url)) {
                response.sendRedirect(String.format(errorUrl, URLEncoder.encode("保单下载失败,请稍后重试", StandardCharsets.UTF_8.name())));
            }
            wxDownloadService.downloadPolicy(url, policyNo, response);
        } catch (Exception e) {
            log.warn("保单下载失败{},{}", insuredVO, url, e);
            throw new MSBizNormalException(ExcptEnum.POLICY_ERROR_801005);
        }
    }


    /**
     * 下载查询的保单
     *
     * @param id
     * @param authorization
     * @return
     */
    public void downloadSearchPolicy(int id, String authorization, HttpServletResponse response) {
        tokenService.validateBusinessToken("anonymous", "searchPolicy" + id, authorization);
        commonDownloadPolicy(id, response);
    }

    /**
     * 用户身份证识别接口
     *
     * @param dto
     */
    public AliYunOcrUtil.IdCardResult getIdCardFromImageBase64(WxOrcIdCardDTO dto) {
        // WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        // 内部员工才有接口权限
        // if (StringUtils.isEmpty(session.getUserId())) {
        // throw new BizException(ExcptEnum.OCR_NO_PERMISSION_ERROR);
        // }
        // 调用次数限制
        // String redisKey = SmConstants.REDIS_KEY_TYPE_ORC + "_" + session.getUserId();
        // String count = redisUtil.get(redisKey);
        // if (count == null) {
        // // 一天后过期
        // redisUtil.set(redisKey, "0");
        // count = redisUtil.get(redisKey);
        // }
        // if (Integer.parseInt(count) > orcDayLimit) {
        // throw new BizException(ExcptEnum.OCR_TIMES_OVER_LIMIT_ERROR);
        // }
        AliYunOcrUtil.IdCardResult cardResult = AliYunOcrUtil.getIdCardFromImage(dto.getBase64());
        // if (cardResult.getSuccess() != null && cardResult.getSuccess()) {
        // redisUtil.set(redisKey, (Integer.parseInt(count) + 1) + "", CommonUtil.getTodayNextSeconds());
        // }
        return cardResult;
    }

    /**
     * 查询用户续保保单
     *
     * @param query
     * @return
     */
    public PageInfo<WxRenewListVO> getWxRenewListByPage(WxRenewQuery query) {
        if (query == null) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = checkAuthority(query.getOpenId(), query.getAuthorization());
        query.setChannel(session.getChannel());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));
        query.setKeyword(CommonUtil.trim(query.getKeyword()));

        if (session.isBindEmployee()) {
            query.setCustomerAdminId(session.getUserId());
        } else if (session.isBindAgent()) {
            query.setAgentId(session.getAgentId());
        }

        PageHelper.startPage(query.getPage(), query.getSize());
        List<WxRenewListVO> wxPolicyList = orderMapper.listWxRenews(query);

        // 判断续保
        List<SmProductRenewRange> renewRanges = productRenewRuleService.listAllSmProductRenewRange();
        wxPolicyList.stream().forEach(wxRenewListVO -> {
            wxRenewListVO.setRenewedFlag(productRenewRuleService.validateRenew(wxRenewListVO.getProductId(),
                    wxRenewListVO.getEndTime(), renewRanges));
        });

        return new PageInfo<>(wxPolicyList);
    }

    /**
     * 生成保单查询验证码
     *
     * @param response
     * @throws Exception
     */
    public void getKaptchaImage(String sessionId, HttpServletResponse response) {
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");
        try (ServletOutputStream out = response.getOutputStream()) {
            // 生成验证码
            String capText = captchaProducer.createText();
            redisUtil.set(getKaptchaCacheKey(sessionId), capText, 5 * 60L);
            // 向客户端写出
            BufferedImage bi = captchaProducer.createImage(capText);
            ImageIO.write(bi, "jpg", out);
            out.flush();
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 通过保单号，被保人证件号，验证码保单自助查询接口
     *
     * @param query
     * @throws Exception
     */

    public WxPolicyListVo getSmsPolicy(HttpServletRequest request, WxPolicyQuery query) {
        if (StringUtils.isEmpty(query.getIdNumber())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg() + ",被保人证件号不能为空");
        }
        if (StringUtils.isEmpty(query.getPolicyNo())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg() + ",保单号不能为空");
        }
        if (StringUtils.isEmpty(query.getVerifyCode())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg() + ",验证码不能为空");
        }
        if (StringUtils.isEmpty(query.getSessionId())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg() + ",会话失效！");
        }

        String cacheVerifyCode = redisUtil.get(getKaptchaCacheKey(query.getSessionId()));
        // Object sessionObj = request.getSession().getAttribute(getKaptchaCacheKey());
        // String cacheVerifyCode = null;
        // if (sessionObj != null) {
        // cacheVerifyCode = sessionObj.toString();
        // }
        if (!Objects.equals(cacheVerifyCode, query.getVerifyCode())) {
            throw new BizException(ExcptEnum.VERIFICATION_CODE_ERROR_401002);
        }
        redisUtil.remove(getKaptchaCacheKey(query.getSessionId()));
        WxOrderQuery orderQuery = new WxOrderQuery();
        orderQuery.setIdNumber(query.getIdNumber());
        orderQuery.setPolicyNo(query.getPolicyNo());
        orderQuery.setIdNumberType(1);
        List<WxPolicyListVo> policyList = orderMapper.listWxPolicyList(orderQuery);
        if (!policyList.isEmpty()) {
            WxPolicyListVo policyVo = policyList.get(0);
            Object authorization = tokenService.getBusinessToken("anonymous", "searchPolicy" + policyVo.getId());
            policyVo.setAuthorization(authorization.toString());
            if (Objects.equals(policyVo.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                if (policyVo.getEndTime().compareTo(new Date()) < 0) {
                    policyVo.setAppStatus(SmConstants.POLICY_STATUS_INVALID);
                }
            } else {
                policyVo.setAppStatus(SmConstants.POLICY_STATUS_INVALID);
            }
            return policyVo;
        }
        orderQuery.setIdNumber(null);
        policyList = orderMapper.listWxPolicyList(orderQuery);
        if (!policyList.isEmpty()) {
            throw new BizException(ExcptEnum.POLICY_SEARCH_ID_NUMBER_ERROR_801044);
        }
        throw new BizException(ExcptEnum.POLICY_SEARCH_NOT_EXIST_801043);
    }

    /**
     * 下载查询的保单
     *
     * @param id
     * @return
     */
    public void commonDownloadPolicy(int id, HttpServletResponse response) {
        SmOrderInsuredVO insuredVO = orderMapper.getOrderInsuredByInsId(id);
        if (insuredVO == null) {
            throw new BizException(ExcptEnum.POLICY_ERROR_801005);
        }
        String url = insuredVO.getDownloadURL();
        if (StringUtils.isEmpty(url)) {
            throw new BizException(ExcptEnum.POLICY_ERROR_801005);
        }
        try {
            wxDownloadService.downloadPolicy(url, insuredVO.getPolicyNo(), response);
        } catch (Exception e) {
            log.warn("保单下载失败{},{}", insuredVO, url);
            throw new BizException(ExcptEnum.POLICY_ERROR_801005, e);
        }
    }

    public String commonDownloadPolicy(int id) {
        SmOrderInsuredVO insuredVO = orderMapper.getOrderInsuredByInsId(id);
        if (insuredVO == null) {
            throw new BizException(ExcptEnum.POLICY_ERROR_801005);
        }
        return insuredVO.getDownloadURL();
    }

    /**
     * 获取用户验证码缓存key
     *
     * @return
     */
    private String getKaptchaCacheKey(String sessionId) {
        return "ins:ps_kaptcha:" + sessionId;
    }

    /**
     * 查询订单渠道信息
     *
     * @param orderId
     * @return
     */
    public WxOrderChannelVO getWxOrderChannel(String orderId) {
        return orderMapper.getWxOrderChannel(orderId);
    }

    /**
     * 查询订单产品对接方式
     *
     * @param orderId
     * @return
     */
    public WxProductApiVO getWxOrderProductApi(String orderId) {
        return orderMapper.getWxOrderProductApi(orderId);
    }

    /**
     * 查詢團單列表
     *
     * @param query
     * @return
     */
    public List<PolicyListDTO> queryGroupOrder(GroupPolicyQuery query) {
        query.init();
        WxSessionVO session = getSession();
        if (session != null) {
            if (session.isBindEmployee()) {
                query.setUserId(session.getUserId());
            } else if (session.isBindAgent()) {
                query.setAgentId(session.getAgentId());
            }
        }
        PageHelper.startPage(query.getPage(), query.getSize());
        //因为小程序后续会对接团意险,所以现在针对小程序出的团单不能在公众号做批改
        List<PolicyListDTO> policyList = orderMapper.queryGroupOrderFilterXjChannel(query);
        if (CollectionUtils.isEmpty(policyList)) {
            return policyList;
        }
        /**
         * 查询是否在批改中
         */
        List<String> orderIdList = policyList.stream().map(PolicyListDTO::getOrderId).collect(Collectors.toList());
        List<Endor> endorList = orderMapper.queryEndorByNativeOrderList("4", "0", orderIdList);
        Map<String, Endor> endorMap = LambdaUtils.safeToMap(endorList, Endor::getRawOrderId);
        policyList.stream().forEach(policy -> {
            if (endorMap.containsKey(policy.getOrderId())) {
                policy.setReplaceFlag(1);
            }
        });

        return policyList;
    }

    /**
     * 查詢團單列表
     *
     * @param query
     * @return
     */
    public PageInfo<InvoicePolicyListVo> queryGroupInvoiceList(GroupPolicyQuery query) {
        query.init();
        WxSessionVO session = getSession();
        if (session != null) {
            if (session.isBindEmployee()) {
                query.setUserId(session.getUserId());
            } else if (session.isBindAgent()) {
                query.setAgentId(session.getAgentId());
            }
        }
        log.info("开始查询发票信息:{}", query);
        PageInfo<PolicyListDTO> page = PageHelper.startPage(query.getPage(), query.getSize()).doSelectPageInfo(() -> {
            orderMapper.queryGroupInvoiceList(query);
        });
        PageInfo<InvoicePolicyListVo> rtn = new PageInfo<>();
        BeanCopier copier = BeanCopier.create(PageInfo.class, PageInfo.class, false);
        copier.copy(page, rtn, null);
        List<PolicyListDTO> data = page.getList();
        if (!CollectionUtils.isEmpty(data)) {
            List<InvoicePolicyListVo> vos = new ArrayList<>();
            for (PolicyListDTO entry : data) {
                InvoicePolicyListVo v = InvoicePolicyListVo.build(entry);
                vos.add(v);
            }
            rtn.setList(vos);
        }
        return rtn;
    }

    /**
     * 从分享者处获取团险订单的详情 !!!接口不脱敏，不校验权限
     *
     * @param id
     * @return
     */
    public WxOrderQueryDetailVO getGroupShareOrderById(String orderId) {
        OrderDetailQuery query = buildQuery();
        query.setId(String.valueOf(orderId));
        WxOrderQueryDetailVO wxOrderQueryDetailVO = queryOrderReorderDetail(query);
        //补充当前团险保单号的险种信息
        List<WxProductPremiumVO> productPremiums = buildProductPremium(orderId, wxOrderQueryDetailVO);
        wxOrderQueryDetailVO.getOrderInfo().setPremiums(productPremiums);
        return wxOrderQueryDetailVO;
    }


//    public WxOrderQueryDetailVO queryGroupOrderReorderDetail(OrderDetailQuery query) {
//
//        SmOrderInsuredVO insuredVo = orderMapper.getOrderInsured(query);
//        if (insuredVo == null) {
//            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
//        }
//
//        OrderQueryResponse fhOrderDTO = orderServiceWrapper.getOrderInfoForLocal(insuredVo.getFhOrderId());
//        WxOrderQueryDetailVO detailVO = new WxOrderQueryDetailVO();
//        BeanUtils.copyProperties(fhOrderDTO, detailVO);
//        detailVO.setId(insuredVo.getId());
//        detailVO.getOrderInfo().setCompanyFullName(insuredVo.getCompanyName());
//        detailVO.setAgentMobile(insuredVo.getAgentMobile());
//        detailVO.setAgentName(insuredVo.getAgentName());
//        detailVO.getInsuredInfos().forEach(ins -> ins.setIsSecurity(insuredVo.getIsSecurity()));
//        // 续保
//        detailVO.setRenewedFlag(
//                renewalConfigService.getValidateRenewFlag(detailVO.getPlanId(), detailVO.getOrderInfo().getEndTime()));
//
//        if (Objects.nonNull(detailVO.getProductId())) {
//            SmProductDetailVO detail = productService.getProductById(detailVO.getProductId());
//            if (Objects.nonNull(detail)) {
//                detailVO.setH5Url(detail.getH5Url());
//                detailVO.setApiType(detail.getApiType());
//            }
//        }
//        String showStatusName = "";
//        if (detailVO.getOrderInfo() != null && detailVO.getPolicyInfo() != null
//                && StringUtil.isNotEmpty(detailVO.getOrderInfo().getEndTime())) {
//            // 映射显示状态
//            showStatusName = CommonUtil.getShowStatusName(detailVO.getOrderInfo().getOrderState(),
//                    detailVO.getPolicyInfo().getAppStatus(),
//                    DateUtil.parseDate(detailVO.getOrderInfo().getEndTime(), DateUtil.CN_LONG_FORMAT));
//        }
//        detailVO.setShowStatusName(showStatusName);
//        // 设置收件信息
//        orderReceiverInfoService.setReceiverInfo(detailVO);
//        // 设置订单绑定信息
//        //addOrderRenewBindInfo(detailVO);
//        addOrderManyRenewBindInfo(fhOrderDTO);
//        //补充当前团险保单号的险种信息
//        List<WxGlProductQuoteResultVO.ProductPremium> productPremiums = buildProductPremium();
//        return detailVO;
//    }

    public List<WxProductPremiumVO> buildProductPremium(String fhOrderId, WxOrderQueryDetailVO wxOrderQueryDetailVO) {
        String policyNo = wxOrderQueryDetailVO.getPolicyInfo().getPolicyNo();
        OrderProductDTO orderProductDTO = orderMapper.queryOrderProduct(fhOrderId);
        ZaGroupRenewalConfigVO zaGroupRenewalConfigVO = renewalConfigService.getZaGroupPlanRenewalConfigByPlanId(wxOrderQueryDetailVO.getPlanId(), fhOrderId, policyNo);
        changProductIdAndPlanId(wxOrderQueryDetailVO, zaGroupRenewalConfigVO);
        Integer productId = zaGroupRenewalConfigVO.getProductId();
        List<WxProductPremiumVO> wxProductPremiumVOList = orderMapper.queryWxProductPremium(productId);
        List<WxProductPremiumVO> premiums = null;
        if (orderProductDTO.getVersion() == null) {
            JSONArray jsonArray = JSON.parseArray(orderProductDTO.getCoverage());
            premiums = nullVersionHandle(jsonArray, wxProductPremiumVOList, productId);
        } else if ("2".equals(orderProductDTO.getVersion())) {
            JSONObject jsonObject = JSON.parseObject(orderProductDTO.getCoverage());
            premiums = twoVersionHandle(jsonObject, wxProductPremiumVOList, productId);
        } else {
            //todo
        }
        return premiums;
    }

    public List<WxProductPremiumVO> nullVersionHandle(JSONArray jsonArray, List<WxProductPremiumVO> wxProductPremiumVOList, Integer productId) {
        List<WxProductPremiumVO> premiums = Lists.newArrayList();
        JSONObject jsObject = null;
        String key = null;
        String dutyCode = null;
        String riskCode = null;
        String cvgAmount = null;
        Map<String, WxProductPremiumVO> wxProductPremiumVOMap = getWxProductPremiumVOMap(wxProductPremiumVOList);
        WxProductPremiumVO wxProductPremiumVO = null;
        List<DutyFactorFlowQuery> dutyFactorFlowQueryList = null;
        for (int i = 0; i < jsonArray.size(); i++) {
            jsObject = jsonArray.getJSONObject(i);
            dutyCode = jsObject.getString("riskCode");
            riskCode = jsObject.getString("cvgCode");
            cvgAmount = jsObject.getBigDecimal("cvgAmount").setScale(2, RoundingMode.HALF_UP).toPlainString();
            key = Joiner.on("_").join(dutyCode, riskCode, cvgAmount);
            wxProductPremiumVO = wxProductPremiumVOMap.get(key);
            if (wxProductPremiumVO == null) {
                log.warn("众安团险未找到原保单责任配置 key= {}", key);
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险未找到原保单责任配置");
            }
            JSONArray dutyFactorArray = jsObject.getJSONArray("dutyFactor");
            if (dutyFactorArray != null && dutyFactorArray.size() > 0) {
                dutyFactorFlowQueryList = buildDutyFactorFlowQueryForNullVersion(riskCode, wxProductPremiumVO, dutyFactorArray, productId);
                if (!CollectionUtils.isEmpty(dutyFactorFlowQueryList)) {
                    wxProductPremiumVO.setDutyFactorList(dutyFactorFlowQueryList);
                }
            }
            premiums.add(wxProductPremiumVO);
        }
        return premiums;

    }

    public List<WxProductPremiumVO> twoVersionHandle(JSONObject jsonObject, List<WxProductPremiumVO> wxProductPremiumVOList, Integer productId) {
        List<WxProductPremiumVO> premiums = Lists.newArrayList();
        JSONArray jsonArray = jsonObject.getJSONArray("clauseList");
        JSONObject jsObject = null;
        String key = null;
        String riskCode = null;
        Map<String, WxProductPremiumVO> wxProductPremiumVOMap = getWxProductPremiumVOMap(wxProductPremiumVOList);
        List<WxProductPremiumVO> wxProductPremiumVOS = null;
        JSONArray dutyArray = null;
        for (int i = 0; i < jsonArray.size(); i++) {
            jsObject = jsonArray.getJSONObject(i);
            riskCode = jsObject.getString("clauseCode");
            dutyArray = jsObject.getJSONArray("liabilityList");
            if (dutyArray != null && dutyArray.size() > 0) {
                wxProductPremiumVOS = buildWxProductPremiumVO(riskCode, dutyArray, wxProductPremiumVOMap, productId);
                if (!CollectionUtils.isEmpty(wxProductPremiumVOS)) {
                    premiums.addAll(wxProductPremiumVOS);
                }
            }
        }
        return premiums;
    }

    private List<WxProductPremiumVO> buildWxProductPremiumVO(String riskCode, JSONArray dutyArray, Map<String, WxProductPremiumVO> wxProductPremiumVOMap, Integer productId) {
        String dutyCode = null;
        String cvgAmount = null;
        String key = null;
        WxProductPremiumVO wxProductPremiumVO = null;
        JSONArray liabFactorList = null;
        JSONObject dutyObject = null;
        List<DutyFactorFlowQuery> dutyFactorList = null;
        List<DutyFactorFlowQuery> dutyFactorFlowQueryList = null;
        List<WxProductPremiumVO> wxProductPremiumVOList = Lists.newArrayList();
        for (int i = 0; i < dutyArray.size(); i++) {
            dutyObject = dutyArray.getJSONObject(i);
            liabFactorList = dutyObject.getJSONArray("liabFactorList");
            dutyCode = dutyObject.getString("liabCode");
            cvgAmount = dutyObject.getBigDecimal("liabAmount").setScale(2, RoundingMode.HALF_UP).toPlainString();
            key = Joiner.on("_").join(riskCode, dutyCode, cvgAmount);
            wxProductPremiumVO = wxProductPremiumVOMap.get(key);
            if (wxProductPremiumVO == null) {
                log.warn("众安团险未找到原保单责任配置 key= {}", key);
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险未找到原保单责任配置");
            }
            if (liabFactorList != null && liabFactorList.size() > 0) {
                dutyFactorFlowQueryList = buildDutyFactorFlowQuery(dutyCode, wxProductPremiumVO, liabFactorList, productId);
                if (!CollectionUtils.isEmpty(dutyFactorFlowQueryList)) {
                    wxProductPremiumVO.setDutyFactorList(dutyFactorFlowQueryList);
                }
            }
            wxProductPremiumVOList.add(wxProductPremiumVO);
        }
        return wxProductPremiumVOList;
    }

    private List<DutyFactorFlowQuery> buildDutyFactorFlowQuery(String dutyCode, WxProductPremiumVO wxProductPremiumVO, JSONArray liabFactorList, Integer productId) {
        JSONObject jsonObject = null;
        String factorCode = null;
        String factorValue = null;
        List<DutyFactorDTO> dutyFactorDTOList = null;
        DutyFactorDTO dutyFactorDTO = null;
        List<DutyFactorFlowQuery> dutyFactorFlowQueryList = Lists.newArrayList();
        DutyFactorFlowQuery dutyFactorFlowQuery = null;
        WxProductPremiumQuery wxProductPremiumQuery = null;
        for (int i = 0; i < liabFactorList.size(); i++) {
            jsonObject = liabFactorList.getJSONObject(i);
            factorCode = jsonObject.getString("factorCode");
            factorValue = jsonObject.getString("factorValue");
            if ("ZXG156".equals(dutyCode) && "Daily hospitalization allowance".equals(factorCode)) {
                //因为这是后台自动补充的责任,可以跳过校验
                continue;
            }
            wxProductPremiumQuery = new WxProductPremiumQuery();
            wxProductPremiumQuery.setProductId(productId);
            wxProductPremiumQuery.setSpcId(wxProductPremiumVO.getSpcId());
            wxProductPremiumQuery.setDutyCode(dutyCode);
            wxProductPremiumQuery.setFactorValue(factorValue);
            wxProductPremiumQuery.setDutyFactorCode(factorCode);
            dutyFactorDTOList = orderMapper.queryDutyFactorDTO(wxProductPremiumQuery);
            if (CollectionUtils.isEmpty(dutyFactorDTOList)) {
                log.warn("众安团险未找到原保单责任配置 req= {}", JSON.toJSONString(wxProductPremiumQuery));
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险未找到原保单责任配置");
            }
            if (dutyFactorDTOList.size() != 1) {
                log.warn("众安团险未找到原保单责任配置 req= {}", JSON.toJSONString(wxProductPremiumQuery));
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险原保单责任配置不唯一");
            }
            dutyFactorDTO = dutyFactorDTOList.get(0);
            dutyFactorFlowQuery = new DutyFactorFlowQuery();
            dutyFactorFlowQuery.setId(dutyFactorDTO.getId());
            dutyFactorFlowQuery.setSpcId(dutyFactorDTO.getSpcId());
            dutyFactorFlowQuery.setDutyCode(dutyFactorDTO.getDutyCode());
            dutyFactorFlowQuery.setDutyFactorCode(dutyFactorDTO.getDutyFactorCode());
            dutyFactorFlowQuery.setDutyFactorName(dutyFactorDTO.getFactorName());
            dutyFactorFlowQuery.setFactorValue(dutyFactorDTO.getFactorValue());
            dutyFactorFlowQuery.setOptionName(dutyFactorDTO.getOptionName());
            dutyFactorFlowQueryList.add(dutyFactorFlowQuery);
        }
        return dutyFactorFlowQueryList;
    }

    private Map<String, WxProductPremiumVO> getWxProductPremiumVOMap(List<WxProductPremiumVO> wxProductPremiumVOList) {
        Map<String, WxProductPremiumVO> wxProductPremiumVOMap = Maps.newHashMap();
        wxProductPremiumVOList.forEach(x -> {
            String key = Joiner.on("_").join(x.getRiskCode(), x.getDutyCode(), x.getCvgAmount());
            if (wxProductPremiumVOMap.containsKey(key)) {
                //todo
            }
            wxProductPremiumVOMap.put(key, x);
        });
        return wxProductPremiumVOMap;
    }

    /**
     * 暂时的单可能是团险续保的单也可能是新契约的单
     *
     * @param orders
     */
    private void groupRenewalHandle(OrderDraftList orderDraftList, WxOrderListVo entry) {
        String oldOrderId = null;
        OrderBasicVo orderInfo = null;
        orderInfo = JSON.parseObject(orderDraftList.getBasicInfo(), OrderBasicVo.class);
        oldOrderId = orderInfo.getOldOrderId();
        if (orderInfo.getOldOrderId() == null) {
            //不是续保不做处理
            return;
        }
        SmOrderMinInfo smOrderMinInfo = smOrderMapper.querySmOrderMinInfo(orderInfo.getOldOrderId());
        if (smOrderMinInfo == null) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "待续保订单不存在");
        }
        WxOrderSearchForm wxOrderSearchForm = new WxOrderSearchForm();
        wxOrderSearchForm.setOrderId(oldOrderId);
        wxOrderSearchForm.setCustomerAdminId(smOrderMinInfo.getCustomerAdminId());
        wxOrderSearchForm.setProductId(smOrderMinInfo.getProductId());
        Date now = new Date();
        wxOrderSearchForm.setStartTime(CommonUtil.getStartTimeOfDay(now));
        wxOrderSearchForm.setEndTime(CommonUtil.getEndTimeOfDay(now));
        Integer count = renewalOrderMapper.countWaitRenewalCountByOrderId(wxOrderSearchForm);
        entry.setOldOrderId(oldOrderId);
        if (count != null && count.intValue() > 0) {
            entry.setPolicyOverFlag(Boolean.FALSE);
        } else {
            entry.setPolicyOverFlag(Boolean.TRUE);
        }
    }

    /**
     * 众安续保根据是否白名单替换产品编码和计划编码
     *
     * @param wxOrderQueryDetailVO
     * @param zaGroupRenewalConfigVO
     */
    private void changProductIdAndPlanId(WxOrderQueryDetailVO wxOrderQueryDetailVO, ZaGroupRenewalConfigVO zaGroupRenewalConfigVO) {
        Integer productId = zaGroupRenewalConfigVO.getProductId();
        Integer planId = zaGroupRenewalConfigVO.getPlanId();
        wxOrderQueryDetailVO.setProductId(productId);
        wxOrderQueryDetailVO.setPlanId(planId);
        SmPlan smPlan = planMapper.getById(Long.valueOf(planId));
        String planCode = smPlan.getPlanCode();
        wxOrderQueryDetailVO.getSmPlanSalesOrgs().stream().forEach(x -> {
            x.setPlanId(planId);
            x.setProductId(productId);
        });
        wxOrderQueryDetailVO.getOrderInfo().setProductId(productId + "");
        wxOrderQueryDetailVO.getInsuredInfos().forEach(x -> {
            x.getProduct().setPlanId(planId);
            x.getProduct().setProductId(productId);
            x.getProduct().setPlanCode(planCode);
        });
    }

    private List<DutyFactorFlowQuery> buildDutyFactorFlowQueryForNullVersion(String dutyCode, WxProductPremiumVO wxProductPremiumVO, JSONArray liabFactorList, Integer productId) {
        JSONObject jsonObject = null;
        String factorCode = null;
        String factorValue = null;
        List<DutyFactorDTO> dutyFactorDTOList = null;
        DutyFactorDTO dutyFactorDTO = null;
        List<DutyFactorFlowQuery> dutyFactorFlowQueryList = Lists.newArrayList();
        DutyFactorFlowQuery dutyFactorFlowQuery = null;
        WxProductPremiumQuery wxProductPremiumQuery = null;
        for (int i = 0; i < liabFactorList.size(); i++) {
            jsonObject = liabFactorList.getJSONObject(i);
            factorCode = jsonObject.getString("dutyCode");
            factorValue = jsonObject.getString("factorValue");
            if ("ZXG156".equals(dutyCode) && "Daily hospitalization allowance".equals(factorCode)) {
                //因为这是后台自动补充的责任,可以跳过校验
                continue;
            }
            wxProductPremiumQuery = new WxProductPremiumQuery();
            wxProductPremiumQuery.setProductId(productId);
            wxProductPremiumQuery.setSpcId(wxProductPremiumVO.getSpcId());
            wxProductPremiumQuery.setDutyCode(dutyCode);
            wxProductPremiumQuery.setFactorValue(factorValue);
            wxProductPremiumQuery.setDutyFactorCode(factorCode);
            dutyFactorDTOList = orderMapper.queryDutyFactorDTO(wxProductPremiumQuery);
            if (CollectionUtils.isEmpty(dutyFactorDTOList)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险未找到原保单责任配置");
            }
            if (dutyFactorDTOList.size() != 1) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安团险原保单责任配置不唯一");
            }
            dutyFactorDTO = dutyFactorDTOList.get(0);
            dutyFactorFlowQuery = new DutyFactorFlowQuery();
            dutyFactorFlowQuery.setId(dutyFactorDTO.getId());
            dutyFactorFlowQuery.setSpcId(dutyFactorDTO.getSpcId());
            dutyFactorFlowQuery.setDutyCode(dutyFactorDTO.getDutyCode());
            dutyFactorFlowQuery.setDutyFactorCode(dutyFactorDTO.getDutyFactorCode());
            dutyFactorFlowQuery.setDutyFactorName(dutyFactorDTO.getFactorName());
            dutyFactorFlowQuery.setFactorValue(dutyFactorDTO.getFactorValue());
            dutyFactorFlowQuery.setOptionName(dutyFactorDTO.getOptionName());
            dutyFactorFlowQueryList.add(dutyFactorFlowQuery);
        }
        return dutyFactorFlowQueryList;
    }

    /**
     * 在保险业务助手中获取微信用户提成列表
     * 如果查询结束时间<="2021-12-31 23:59:59",则直接查老的佣金表
     * 如果查询 开始时间<=2021-12-31 23:59:59 结束时间 >= 2022-01-01 00:00:00,统计值从两个新、老表里获取，明细直接从新表获取(存在的问题就是新表里没有数据)
     *
     * @param query
     * @return
     */
    public SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> getWxUserCmsListV4(WxCmsQuery query, JwtUserInfo userInfo) {
        PageHelper.startPage(query.getPage(), query.getSize());
        query.setUserId(userInfo.getJobNumber());
        query.setStartTime(CommonUtil.getStartTimeOfDay(query.getStartTime()));
        query.setEndTime(CommonUtil.getEndTimeOfDay(query.getEndTime()));

        //是否需要查询老的佣金表数据
        WxCmsQuery queryOld = null;
        if (query.getStartTime() == null || (query.getStartTime().compareTo(ENDTIME_2021) <= 0
                && (query.getEndTime() == null || query.getEndTime().compareTo(STAETTIME_2022) >= 0))) {
            queryOld = new WxCmsQuery();
            BeanUtils.copyProperties(query, queryOld);
            queryOld.setEndTime(ENDTIME_2021);
            query.setStartTime(STAETTIME_2022);
        }
        List<WxUserCmsListVo> userCmsList = orderMapper.listWxPolicyCmsListV3(query);
        //设置加佣
        addCommission(userCmsList);

        SmyPageInfo<WxUserCmsListVo, WxCmsSmyVo> summaryPage = new SmyPageInfo<>(userCmsList);
        WxCmsSmyVo smyVo = orderMapper.getWxPolicySummaryV3(query);
        if (queryOld != null) {
            WxCmsSmyVo oldSmyVo = getOldWxCmsSmyVo(queryOld);

            if (oldSmyVo != null && smyVo != null) {
                BigDecimal oldCmsAmount = oldSmyVo.getCmsAmount() != null ? oldSmyVo.getCmsAmount() : BigDecimal.ZERO;
                smyVo.setCmsAmount(smyVo.getCmsAmount() != null ? smyVo.getCmsAmount().add(oldCmsAmount) : oldCmsAmount);
                BigDecimal oldOrderAmount = oldSmyVo.getOrderAmount() != null ? oldSmyVo.getOrderAmount() : BigDecimal.ZERO;
                smyVo.setOrderAmount(smyVo.getOrderAmount() != null ? smyVo.getOrderAmount().add(oldOrderAmount) : oldOrderAmount);
                Integer oldQrderQty = oldSmyVo.getOrderQty() != null ? oldSmyVo.getOrderQty() : 0;
                smyVo.setOrderQty(smyVo.getOrderQty() != null ? smyVo.getOrderQty() + oldQrderQty : oldQrderQty);
                BigDecimal oldAdd = oldSmyVo.getAddCommissionAmount() != null ? oldSmyVo.getAddCommissionAmount() : BigDecimal.ZERO;
                smyVo.setAddCommissionAmount(smyVo.getAddCommissionAmount() != null ? smyVo.getAddCommissionAmount().add(oldAdd) : oldAdd);

            } else if (smyVo == null) {
                smyVo = oldSmyVo;
            }
        }

        if (smyVo == null) {
            smyVo = new WxCmsSmyVo();
            smyVo.setOrderQty(0);
            smyVo.setOrderAmount(BigDecimal.ZERO);
            smyVo.setCmsAmount(BigDecimal.ZERO);
        }
        //统计金额加上加佣金额
        //BigDecimal addCommissionAmount = getAddCommissionAmount(query);
        //smyVo.setAddCommissionAmount(addCommissionAmount);
        summaryPage.setSummary(smyVo);
        return summaryPage;
    }

    public WxOrderQueryDetailVO getWxOrderById(int orderId, JwtUserInfo userInfo) {
        OrderDetailQuery query = new OrderDetailQuery();
        query.setUserId(userInfo.getJobNumber());
        query.setId(String.valueOf(orderId));
        return queryOrderDetail(query);
    }
}
