package com.cfpamf.ms.insur.admin.external.whale;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMsgMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderOutType;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.order.EnumDrainagePlatForm;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.enums.order.OrderSourceEnum;
import com.cfpamf.ms.insur.admin.enums.order.VillageActivityTypeEnum;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.model.CompanyPolicyInfo;
import com.cfpamf.ms.insur.admin.external.fh.dto.*;
import com.cfpamf.ms.insur.admin.external.jdal.util.ParamUtil;
import com.cfpamf.ms.insur.admin.external.whale.api.WhaleApiService;
import com.cfpamf.ms.insur.admin.external.whale.api.group.WhaleMessageBuilder;
import com.cfpamf.ms.insur.admin.external.whale.channel.WhaleOrderChannelService;
import com.cfpamf.ms.insur.admin.external.whale.enums.*;
import com.cfpamf.ms.insur.admin.external.whale.model.*;
import com.cfpamf.ms.insur.admin.external.whale.model.preservation.PreserveSurrenderDetailVo;
import com.cfpamf.ms.insur.admin.external.whale.model.preservation.PreserveSurrenderInsuredVo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.enums.ZaGroupPolicyStatusEnum;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementRes;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupInsuredInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.JumpH5OtherParams;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.TestPremiumProductForm;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRisk;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.SmXjxhService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderVillageActivityService;
import com.cfpamf.ms.insur.admin.service.order.group.GroupRuleAdaptor;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.IdCardUtils;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.constant.EnumChonghoPay;
import com.cfpamf.ms.insur.weixin.constant.EnumEndor;
import com.cfpamf.ms.insur.weixin.constant.EnumGroupCorrectType;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.correct.CorrectLogMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OfflinePayDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ApplicantDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.po.order.correct.OrderInsuredCorrectLog;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.form.CorrectPersonal;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.form.WhalePlan;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.request.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.response.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.InvoiceVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.MemberChange;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.BusinessCheckVo;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import com.cfpamf.ms.pay.facade.vo.QueryOrderVO;
import com.cfpamf.ms.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 中华联合保险API接口适配器
 *
 * <AUTHOR>
 */
@Slf4j
@Service("whale")
public class WhaleOrderServiceAdapterImpl extends ZhnxPayOrderServiceAdapter {


    public static Integer NOT_SURRENDERED = 0;
    public static Integer SURRENDERED = 1;
    public static String ADD_OR_SUBTRACT_TYPE_ADD = "加人";
    public static String ADD_OR_SUBTRACT_TYPE_SUB = "减人";

    @Autowired
    OrderNoGenerator orderNoGenerator;

    @Autowired
    SmXjxhService whaleService;

    @Autowired
    WhaleApiService apiService;

    @Autowired
    SmProductService productService;

    @Autowired
    SysRiskMapper riskMapper;

    @Autowired
    private SmOrderGroupNotifyMapper smOrderGroupNotifyMapper;

    @Autowired
    private SmOrderGroupNotifyMsgMapper smOrderGroupNotifyMsgMapper;
    @Autowired
    private SmOrderVillageActivityService smOrderVillageActivityService;
    @Autowired
    private WhaleOrderChannelService whaleOrderChannelService;

    /**
     * 判断是否在小程序绑定
     *
     * @param jobNumber
     * @return
     */
    public Boolean isAuth(String jobNumber) {
        if (StringUtils.isEmpty(jobNumber)) {
            return null;
        }
        return apiService.isAuth(jobNumber);
    }

    /**
     * 生成动态跳转地址
     *
     * @param urlTemplate
     * @param params
     * @return
     */
    public String genDynamicUrl(String urlTemplate, String params, String subChannel, JumpH5OtherParams otherParams) {
        return String.format(urlTemplate, whaleService.genWhaleParams(params, subChannel, ""));
    }

    @Override
    public OrderSubmitResponse submitChannelOrder(OrderSubmitRequest request) {
        throw new UnsupportedOperationException("channel [whale] not support order");
    }

    @Override
    public OrderPrePayResponse prePayChannelOrder(OrderPrePayRequest request) {
        throw new UnsupportedOperationException("channel [whale] not support order");
    }

    @Override
    protected CompanyPolicyInfo acceptPolicy(OrderQueryResponse response, SmOrderListVO orderInfo, QueryOrderVO queryOrderVO) {
        throw new UnsupportedOperationException("channel [whale] not support acceptPolicy");
    }

    @Override
    public OrderQueryResponse queryChannelOrderInfo(OrderQueryRequest request) {
        throw new UnsupportedOperationException("channel [whale] not support queryChannelOrderInfo");
    }

    @Override
    public String getOrderPayedCallbackResp(String request) {
        throw new UnsupportedOperationException("channel [whale] not support order");
    }

    public WhaleResp<WhaleContract> getPolicyNo(String contractCode, String fhOrderid) {

        return apiService.getPolicyNo(contractCode);

    }


    /**
     * 获取合约策略
     *
     * @param contractCode     合约编码
     * @param businessScenario 订单ID
     * @return 合约策略
     */
    public WhaleResp<WhaleContract> getPolicyNoByBusinessScenario(String contractCode, EnumChannelBusinessScenario businessScenario) {
        // 调用apiService的getPolicyNo方法获取合约策略
        return apiService.getPolicyNo(contractCode, businessScenario.getCode());
    }

    public List<WhaleProductMappingVo> getWhaleProductMapping(List<String> productList) {
        // 调用apiService的getPolicyNo方法获取合约策略
        return apiService.getProductMapping(productList);
    }


    public WhaleResp<WhaleContract> getGroupPolicy(String contractCode) {
        return apiService.getGroupPolicy(contractCode);
    }

    /**
     * 获取保单信息
     * @param contractCode 合同代码
     * @param businessScenario 业务场景
     * @return 保单信息
     */
    public WhaleResp<WhaleContract> getGroupPolicyByBusinessScenario(String contractCode, String businessScenario) {
        // 调用API服务获取保单信息
        return apiService.getGroupPolicy(contractCode, businessScenario);
    }

    public WhaleResp<PreservationDetail> getPreservationDetail(String preservationCode) {
        return apiService.getPreservationDetail(preservationCode);
    }


    private String startPlanCode(InsuredInfoList insuredInfoList, WhaleContract contract) {
        //星盈家
        Optional<ProductInfoList> first = insuredInfoList.getProductInfoList()
                .stream()
                .filter(ProductInfoList::isMain)
                .findFirst();
        LocalDateTime approvedTime = contract.getContractExtendInfo().getApprovedTime();
        if (first.isPresent() && Objects.equals(apiProperties.getStarCode(), first.get().getProductCode())) {
            ProductInfoList info = first.get();
            return startPlanCode(approvedTime,info.getProductCode(),info.getPaymentPeriodType(),info.getPaymentPeriod(),insuredInfoList);
        }
        return null;
    }

    private String startPlanCode(LocalDateTime approvedTime,String productCode,String paymentPeriodType,Integer paymentPeriod,InsuredInfoList insuredInfoList) {
        if (Objects.equals(apiProperties.getStarCode(), productCode)) {
            if(insuredInfoList==null){
                return null;
            }
            EnumWhalePayPeriodType pay = EnumWhalePayPeriodType.valueOfCode(paymentPeriodType);
            if (Objects.equals(EnumWhalePayPeriodType.PAYMENT_PERIOD_TYPE_0, pay)) {
                return "XYJLQ-1";
            }
            StringJoiner xyjlq = new StringJoiner("-").add("XYJLQ").add(String.valueOf(paymentPeriod));
            if (Objects.isNull(approvedTime)) {
                throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "星盈家产品承包日期不能为空！");
            }
            LocalDate localDate = approvedTime.toLocalDate();
            long age = ChronoUnit.MONTHS.between(LocalDate.parse(insuredInfoList.getInsuredBirthday().substring(0, 10)), localDate) / 12L;
            if (age < 50L) {
                xyjlq.add("1");
            } else if (age < 56) {
                xyjlq.add("2");
            } else {
                xyjlq.add("3");
            }
            return xyjlq.toString();
        }
        return null;
    }

    /**
     * 星盈家
     *
     * @param req
     * @return
     */
    private String startPlanCode(WhaleContract req) {
        //星盈家
        if (Objects.equals(apiProperties.getStarCode(), req.getContractBaseInfo().getMainProductCode())) {
            return startPlanCode(req.getInsuredInfoList().get(0), req);
        }
        return null;
    }


    @Deprecated
    public String planCode(WhaleContract req) {

        String s = startPlanCode(req);
        if (Objects.nonNull(s)) {
            return s;
        }
        //取第一个被保人的计划编码 没有就用主险编码
        return   //如果第一个被保人传了计划编码就用这个作为默认的计划编码否则就是主险编码
                CollectionUtils.isNotEmpty(req.getInsuredInfoList()) &&
                        StringUtils.isNotBlank(req.getInsuredInfoList().get(0).getCompanyPlanCode()) ?
                        req.getInsuredInfoList().get(0).getCompanyPlanCode() : req.getContractBaseInfo().getMainProductCode();
    }

    /**
     * 将小鲸的险种映射成农保的计划
     * @param
     * @return
     */
    public SmPlanVO convertPlanV2(LocalDateTime approvedTime,List<String> mainProductCodeList,String paymentPeriodType,Integer paymentPeriod,InsuredInfoList insuredInfoList) {
        //1. 特殊的险种映射关系(星盈佳产品配置)
        String mainProductCode = mainProductCodeList.get(0);
        String planCode = startPlanCode(approvedTime,mainProductCode,paymentPeriodType,paymentPeriod,insuredInfoList);
        if (StringUtils.isNotBlank(planCode)) {
            log.info("小鲸险种映射计划-S1:{}",planCode);
            SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
            if(planVo != null){
                return planVo;
            }
        }

        //2. 通用的映射关系,从被保人的计划编码中取值
        if(Objects.nonNull(insuredInfoList)){
            planCode = insuredInfoList.getCompanyPlanCode();
            if (StringUtils.isNotBlank(planCode)) {
                log.info("小鲸险种映射计划-S2:{}",planCode);
                SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
                if(planVo != null){
                    return planVo;
                }
            }
        }
        //3.从保单的主险上取值
        planCode = mainProductCode;
        if(StringUtils.isNotBlank(planCode)) {
            log.info("小鲸险种映射计划-S3:{}", planCode);
            SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
            if (planVo != null) {
                return planVo;
            }
        }
        //4. 兜底方案，从被保人险种中批量映射
        log.info("小鲸险种映射计划-S4:{}", mainProductCode);
        List<SmPlanVO> planList = productService.listPlanByFhProductIds(EnumChannel.XJ.getCode(),mainProductCodeList);
        if(CollectionUtils.isNotEmpty(planList)){
            return planList.get(0);
        }
        throw new MSBizNormalException(ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getCode(), "农保不存在变更后的险种/计划，请先配置:"+planCode);
    }

    /**
     * 将小鲸的险种映射成农保的计划
     * @param req
     * @return
     */
    public SmPlanVO convertPlan(WhaleContract req) {
        //1. 特殊的险种映射关系
        String planCode = startPlanCode(req);
        if (StringUtils.isNotBlank(planCode)) {
            log.info("小鲸险种映射计划-S1:{}",planCode);
            SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
            if(planVo != null){
                return planVo;
            }
        }

        //2. 通用的映射关系,从被保人的计划编码中取值
        List<InsuredInfoList> insuredInfoList = req.getInsuredInfoList();
        List<String> productCodeList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(insuredInfoList)){
            InsuredInfoList luck = insuredInfoList.get(0);
            planCode = luck.getCompanyPlanCode();
            if (StringUtils.isNotBlank(planCode)) {
                log.info("小鲸险种映射计划-S2:{}",planCode);
                SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
                if(planVo != null){
                    return planVo;
                }
            }
            //2.1 从被保人险种信息中取值，但是该逻辑放在最后面兜底
            List<ProductInfoList> productInfoList = luck.getProductInfoList();
            if(CollectionUtils.isNotEmpty(productInfoList)){
                productCodeList = productInfoList.stream().filter(product-> Objects.equals(product.getMainInsurance(),1)).map(ProductInfoList::getProductCode).collect(Collectors.toList());
            }

        }
        //3.从保单的主险上取值
        planCode = req.getContractBaseInfo().getMainProductCode();
        if(StringUtils.isNotBlank(planCode)) {
            log.info("小鲸险种映射计划-S3:{}", planCode);
            SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
            if (planVo != null) {
                return planVo;
            }
        }
        //4. 兜底方案，从被保人险种中批量映射
        if(CollectionUtils.isNotEmpty(productCodeList)){
            log.info("小鲸险种映射计划-S4:{}", productCodeList);
            List<SmPlanVO> planList = productService.listPlanByFhProductIds(EnumChannel.XJ.getCode(),productCodeList);
            if(CollectionUtils.isNotEmpty(planList)){
                return planList.get(0);
            }
        }
        throw new MSBizNormalException(ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getCode(), "小鲸通知产品计划未配置:"+planCode);
    }

    /**
     * 将小鲸的险种映射成农保的计划
     * @param req
     * @return
     */
    public SmPlanVO convertPlanS1(WhaleContract req,InsuredInfoList insuredVo) {
        //1. 特殊的险种映射关系
        String planCode = startPlanCode(req);
        if (StringUtils.isNotBlank(planCode)) {
            log.info("小鲸险种映射计划-S1:{}",planCode);
            SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
            if(planVo != null){
                return planVo;
            }
        }

        //2. 通用的映射关系,从被保人的计划编码中取值
        if(insuredVo != null){
            planCode = insuredVo.getCompanyPlanCode();
            if (StringUtils.isNotBlank(planCode)) {
                log.info("小鲸险种映射计划-S2:{}",planCode);
                SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
                if(planVo != null){
                    return planVo;
                }
            }
        }
        //3. 从被保人险种中批量映射
        List<ProductInfoList> productInfoList = insuredVo.getProductInfoList();
        List<String> productCodeList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(productInfoList)){
            productCodeList = productInfoList.stream().filter(product-> Objects.equals(product.getMainInsurance(),1)).map(ProductInfoList::getProductCode).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(productCodeList)){
            log.info("小鲸险种映射计划-S4:{}", productCodeList);
            List<SmPlanVO> planList = productService.listPlanByFhProductIds(EnumChannel.XJ.getCode(),productCodeList);
            if(CollectionUtils.isNotEmpty(planList)){
                return planList.get(0);
            }
        }

        //4. 兜底方案，从保单的主险上取值
        planCode = req.getContractBaseInfo().getMainProductCode();
        if(StringUtils.isNotBlank(planCode)) {
            log.info("小鲸险种映射计划-S3:{}", planCode);
            SmPlanVO planVo = productService.getPlanByFhProductId(planCode);
            if (planVo != null) {
                return planVo;
            }
        }
        throw new MSBizNormalException(ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getCode(), "小鲸通知产品计划未配置:"+planCode);
    }
//    /**
//     * 获取被保险人计划编码
//     *
//     * @return
//     */
//    private String insuredPlanCode(InsuredInfoList insuredInfoList, WhaleContract contract, String defaultCode) {
//
//        String s = startPlanCode(insuredInfoList, contract);
//
//        if (Objects.nonNull(s)) {
//            return s;
//        }
//        //如果传了保司计划编码
//        return StringUtils.isNotBlank(insuredInfoList.getCompanyPlanCode()) ? insuredInfoList.getCompanyPlanCode() :
//                //没传就取第一个主险
//                insuredInfoList.getProductInfoList()
//                        .stream().min(Comparator.comparing(ProductInfoList::isMain).reversed()
//                                .thenComparing(ProductInfoList::getProductCode)).map(ProductInfoList::getProductCode).orElse(defaultCode);
//    }


    public SmCreateOrderSubmitRequest cvtByNotify(SmPlanVO planVO,WhaleContract req, String orderId) {

        String fhOrderId = StringUtils.isBlank(orderId) ? orderNoGenerator.getNextNo(EnumChannel.XJ.getCode()) : orderId;
        SmCreateOrderSubmitRequest res = new SmCreateOrderSubmitRequest();

        ContractBaseInfo contractBaseInfo = req.getContractBaseInfo();

        res.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());
        res.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        res.setFhOrderId(fhOrderId);
        res.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        res.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        res.setAppNo(contractBaseInfo.getApplicantPolicyNo());

        res.setOrderState("0");
        res.setNoticeCode("");
        res.setCustomField(req.getCustomField());
        mapChannelInfo(req, res);

        res.setChannel(EnumChannel.XJ.getCode());

        if (StringUtils.isNotBlank(req.getChannelInfo().getPropagandistIdCard())
                || smOrderVillageActivityService.whaleSynCreateCheckFour(req)) {
            res.setOrderType(1);

            List<WhaleContractActivity> contractActivities = req.getPolicyActivities().stream()
                    .filter(e -> e.getType() != null
                            && VillageActivityTypeEnum.FOURTH_LEVEL_DISTRIBUTION.getCode().equals(e.getType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(contractActivities)) {
                res.setIsLifeServicePartner(contractActivities.get(0).getIsLifeServicePartner());
            }

        }

        res.setNoticeMsg("");

        FhOrderInfo fhOrderInfo = mapperOrderInfo(req);

        res.setOrderInfo(fhOrderInfo);
        res.setQty(1);

        FhProposer fhProposer = WhaleConvert.CNT.cvtProposer(req.getApplicantInfo());
        if (StringUtils.isNotBlank(fhProposer.getBirthday())) {
            fhProposer.setBirthday(fhProposer.getBirthday().substring(0, 10));
        }
        res.setProposerInfo(fhProposer);

        //隐射编码转换
        FhProduct product = new FhProduct();

        product.setProductId(planVO.getPlanCode());
        ChannelInfo channelInfo = req.getChannelInfo();
        // 修正保单的推荐人-管护人
        if (Objects.equals(channelInfo.getReferrerType(), 0)) {
            product.setRecommendId(channelInfo.getCustomerManagerChannelCode());
            product.setCustomerAdminId(channelInfo.getReferrerWno());
        }

        //if (Objects.equals(channelInfo.getThirdPartyPlatformCode(), EnumDrainagePlatForm.XIANGZHU_MINI_PROGRAM.getCode())) {
        //if(EnumDrainagePlatForm.containCode(channelInfo.getThirdPartyPlatformCode())){
        if (StringUtils.isNotBlank(channelInfo.getThirdPartyPlatformCode())) {
            res.setDrainagePlatform(channelInfo.getThirdPartyPlatformCode());
            res.setDrainageShareCode(channelInfo.getThirdPartyCustomerCode());
        }

        res.setProductInfo(product);
        res.setProductId(planVO.getPlanCode());

        List<FhInsuredPerson> insureds = req.getInsuredInfoList().stream()
                .map(model -> {
                    FhInsuredPerson insuredPerson = WhaleConvert.CNT.cvtInsured(model);
                    //出生日期只需要到日期就可以
                    if (StringUtils.isNotBlank(model.getInsuredBirthday())) {
                        insuredPerson.setBirthday(model.getInsuredBirthday().substring(0, 10));
                    }

//                    String ipProCode = insuredPlanCode(model, req, planVO.getPlanCode());
                    SmPlanVO planS1 = convertPlanS1(req,model);
                    TestPremiumProductForm ipProduct = new TestPremiumProductForm();

                    ipProduct.setPremium(model.sumPremium());
                    ipProduct.setAmount(model.sumAmount());
                    //如果计划编码不一致
                    ipProduct.setProductId(planS1.getProductId());
                    ipProduct.setPlanId(planS1.getPlanId());
//                    if (Objects.equals(ipProCode, defaultPlanCode)) {
//                        ipProduct.setProductId(planVO.getProductId());
//                        ipProduct.setPlanId(planVO.getPlanId());
//                    } else {
//                        SmPlanVO plan = productService.getPlanByFhProductId(ipProCode);
//                        if (Objects.isNull(plan)) {
//                            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "小鲸" + ipProCode + "计划未配置");
//                        }
//                        ipProduct.setProductId(plan.getProductId());
//                        ipProduct.setPlanId(plan.getPlanId());
//                    }
                    ipProduct.setPlanCode(planS1.getPlanCode());
                    insuredPerson.setProduct(ipProduct);
                    insuredPerson.setPolicyNo(contractBaseInfo.getPolicyNo());
                    insuredPerson.setOldPolicyNo(contractBaseInfo.getSourcePolicyNo());
                    insuredPerson.setRenewalPeriod(contractBaseInfo.getRenewalPeriod());
                    insuredPerson.setDownloadURL(contractBaseInfo.getPolicyUrl());
                    insuredPerson.setAppStatus(
                            EnumWhalePolicyStatus.cvtInsCode(contractBaseInfo.getPolicyStatus()));
                    List<TestPremiumDutyForm> dutyForms = model.getProductInfoList()
                            .stream().map(WhaleConvert.CNT::cvtProduct)
                            .peek(du -> {
                                        du.setPolicyNo(contractBaseInfo.getPolicyNo());
                                        du.setSysRiskId(-1);
                                        du.setSysRiskDutyId(-1);
                                        EnumWhalePolicyStatus by = EnumWhalePolicyStatus.findBy(du.getAppStatus());
                                        if (Objects.nonNull(by) && StringUtils.isNotBlank(by.getSurrenderType())) {
                                            du.setSurrenderType(by.getSurrenderType());
                                            if (Objects.isNull(du.getSurrenderAmount())) {
                                                du.setSurrenderAmount(BigDecimal.ZERO);
                                            }
                                        }
                                        du.setAppStatus(EnumWhalePolicyStatus.cvtInsCode(du.getAppStatus()));
                                        du.setInsuredPeriodType(EnumWhaleInsuredPeriodType.valueOfCode(du.getInsuredPeriodType()).getInsCode());
                                        du.setPaymentPeriodType(EnumWhalePayPeriodType.valueOfCode(du.getPaymentPeriodType()).getInsCode());
                                        du.setPeriodType(EnumWhalePayType.valueOfCode(du.getPeriodType()).getInsCode());

                                    }
                            ).collect(Collectors.toList());
                    insuredPerson.setDuties(dutyForms);
                    return insuredPerson;
                }).collect(Collectors.toList());


        ApplicantInfo applicantInfo = req.getApplicantInfo();
        if (StringUtils.isNotBlank(req.getApplicantInfo().getBankCode())) {
            BankCardBindingReqDTO bankCard = new BankCardBindingReqDTO();

            bankCard.setBankCode(applicantInfo.getBankCode());
            bankCard.setFhOrderId(res.getFhOrderId());
            bankCard.setBankName(applicantInfo.getBankName());
            bankCard.setAccountNo(applicantInfo.getCardNo());
            res.setRenewBindInfo(bankCard);
        }

        res.setInsuredPerson(insureds);

        //处理险总id信息
        List<String> codes = insureds.stream()
                .map(FhInsuredPerson::getDuties).flatMap(Collection::stream)
                .map(TestPremiumDutyForm::getRiskCode)
                .distinct().collect(Collectors.toList());
        Map<String, SysRisk> stringSysRiskMap = LambdaUtils.groupByAndToFirstMap(riskMapper.selectByCodes(codes, planVO.getCompanyId()),
                SysRisk::getRiskCode);
        //赋值险总id
        SysRisk de = new SysRisk();
        de.setId(-1);
        insureds.stream()
                .map(FhInsuredPerson::getDuties).flatMap(Collection::stream)
                .forEach(d -> {
                    d.setSysRiskId(stringSysRiskMap.getOrDefault(d.getRiskCode(), de).getId());
                });


        return res;
    }


    private void mapChannelInfo(WhaleContract req, SmCreateOrderSubmitRequest res) {

        //从引流平台进来的
        if (Objects.equals(req.getChannelInfo().getThirdPartyPlatformCode(), EnumDrainagePlatForm.XIANGZHU_MINI_PROGRAM.getCode())) {
            res.setSubChannel(EnumOrderSubChannel.XJ_ZHNX.getCode());
            res.setRecommendChannel(OrderSourceEnum.ZHNX.getCode());
            return;
        }

        SmXjxhService xjxhService = SpringUtil.getBean(SmXjxhService.class);

        //有回调参数的
        String customField = req.getCustomField();
        if (StringUtils.isNotEmpty(customField)) {
            try {
                Map<String, String> xjMap = ParamUtil.getParamNameAndValue(customField);
                String pid = xjxhService.mapperSubChannel(xjMap);
                res.setSubChannel(pid);
                res.setRecommendChannel(OrderSourceEnum.ZHNX.getCode());
                return;
            } catch (Exception e) {
                log.info("解析小鲸保单自定义参数失败，contractCode:{}", customField);
            }
        }

        //渠道编码为zhnx、zhnx-1的，推荐渠道为zhnx
        String channelCode = Optional.ofNullable(req.getChannelInfo()).map(ChannelInfo::getChannelCode).orElse(null);
        if (OrderSourceEnum.fromZhnx(channelCode)) {
            res.setSubChannel(EnumOrderSubChannel.H5.getCode());
            res.setRecommendChannel(OrderSourceEnum.ZHNX.getCode());
            return;
        }
        //其他，为小鲸向海
        res.setSubChannel(EnumOrderSubChannel.XJ.getCode());
        res.setRecommendChannel(OrderSourceEnum.XJXH.getCode());

    }

    /**
     * 订单基础信息
     *
     * @return
     */
    private FhOrderInfo mapperOrderInfo(WhaleContract contract) {
        FhOrderInfo fhOrderInfo = new FhOrderInfo();

        //计算保费总和
        BigDecimal allPremium = contract.getInsuredInfoList()
                .stream().map(InsuredInfoList::getProductInfoList)
                .flatMap(Collection::stream)
                .map(ProductInfoList::getPremium)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        fhOrderInfo.setTotalAmount(allPremium);

        ContractExtendInfo extendInfo = contract.getContractExtendInfo();
        fhOrderInfo.setStartTime(extendInfo.getEnforceTime());

        String applicantTime = extendInfo.getApplicantTime();
        if (StringUtils.isNotBlank(applicantTime)) {
            fhOrderInfo.setSubmitTime(applicantTime);
            try {
                Date applyTime = LocalDateUtil.parseTime(applicantTime);
                fhOrderInfo.setApplyTime(applyTime);
            } catch (Exception e) {
                log.warn("投保时间解析失败:{}", applicantTime);
            }
        } else if (StringUtils.isNotBlank(extendInfo.getApprovedRecordTime())) {
            fhOrderInfo.setSubmitTime(extendInfo.getApprovedRecordTime());
        } else if (StringUtils.isNotBlank(extendInfo.getOrderTime())) {
            fhOrderInfo.setSubmitTime(extendInfo.getOrderTime());
        } else {
            fhOrderInfo.setSubmitTime(DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT));
        }

        fhOrderInfo.setEndTime(extendInfo.getFailureTime());
        contract.getProductInfoList().stream()
                .filter(s -> Objects.equals(s.getMainInsurance(), 1))
                .findAny()
                .ifPresent(pro -> {
                    EnumWhaleInsuredPeriodType enumYgInsuredPeriodType = EnumWhaleInsuredPeriodType.valueOfCode(pro.getInsuredPeriodType());
                    if (StringUtils.isBlank(fhOrderInfo.getEndTime())) {
                        fhOrderInfo.setEndTime(enumYgInsuredPeriodType.getEndDate(pro, contract));
                    }
                    //这两个没有
                    fhOrderInfo.setUnderWritingAge("");
                    fhOrderInfo.setValidPeriod(enumYgInsuredPeriodType.getString(pro, contract));

                });

        return fhOrderInfo;
    }

    /********************* 小鲸 团险 ********************************/

    public List<SmCreateOrderSubmitRequest> cvtByGroupNotify(WhaleContract req, String orderId) {
        List<SmCreateOrderSubmitRequest> submitRequestList = Lists.newArrayList();
        Map<String, SmPlanVO> smPlanMap = listChangeGroupPlan(req);
        //整合被保人数据，将主、从被保人整合到一个list中
        List<InsuredBaseInfo> arrangeList = arrangeGroupInsuredBaseInfo(req.getGroupInsuredInfoList());
        //todo 按被保人维度的推荐人进行分组
        Map<String, List<InsuredBaseInfo>> insuredMap = LambdaUtils.groupBy(arrangeList, InsuredBaseInfo::getChannelReferrerWno);
        int i = 0;
        ContractBaseInfo contractBaseInfo = req.getContractBaseInfo();
        String thPolicyNo = contractBaseInfo.getPolicyNo();
        String policyNo = thPolicyNo;
        String fhOrderId = orderId;
        for (String key : insuredMap.keySet()) {
            if (i != 0) {
                fhOrderId = orderId + "_" + i;
                policyNo = thPolicyNo + "_" + i;
            }

            SmCreateOrderSubmitRequest res = super.initGroupSubmitRequest(fhOrderId, EnumChannel.XJ.getCode(), null);
            res.setAppNo(contractBaseInfo.getApplicantPolicyNo());
            res.setPolicyNo(thPolicyNo);
            //渠道相关信息
            createGroupChannelByWhaleContract(req, res);

            //投保人信息处理
            createGroupFhProposerByWhaleContract(req, res);


            //产品信息处理
            String defaultPlanCode = groupPlanCode(insuredMap.get(key), contractBaseInfo.getMainProductCode());
            SmPlanVO planVO = smPlanMap.get(defaultPlanCode);
            createGroupProductInfo(planVO, key, res);

            //设置小鲸导入的保单渠道信息
            whaleOrderChannelService.setWhaleImportChannel(planVO, req, res);

            //被保人信息处理
            List<InsuredBaseInfo> groupInsuredInfos = insuredMap.get(key);
            List<FhInsuredPerson> insureds = Lists.newArrayListWithCapacity(groupInsuredInfos.size());
            //订单总金额计算
            BigDecimal totalPremium = BigDecimal.ZERO;
            for (InsuredBaseInfo model : groupInsuredInfos) {
                mapGroupInsuredPerson(insureds, contractBaseInfo, model, smPlanMap, policyNo);
                if (model.getPremium() != null) {
                    totalPremium = totalPremium.add(model.getPremium());
                }
            }
            //处理险总id信息
            mapDutyId(insureds, planVO);

            res.setInsuredPerson(insureds);
            //订单信息
            FhOrderInfo fhOrderInfo = mapperGroupOrderInfo(req);
            fhOrderInfo.setTotalAmount(totalPremium);
            res.setOrderInfo(fhOrderInfo);
            res.setQty(1);

            //分单表处理
            res.setOrderItemList(mapOrderItems(insureds, fhOrderId, thPolicyNo));

            submitRequestList.add(res);
            i++;
        }

        return submitRequestList;
    }

    /**
     * 原单中获取渠道相关信息处理
     *
     * @param req
     * @param res
     */
    private void createGroupChannelByWhaleContract(WhaleContract req, SmCreateOrderSubmitRequest res) {
        //渠道相关信息
        res.setCustomField(req.getCustomField());
        mapChannelInfo(req, res);
        if (StringUtils.isNotBlank(req.getChannelInfo().getPropagandistIdCard())) {
            res.setOrderType(1);
        }
        ChannelInfo channelInfo = req.getChannelInfo();
        if (StringUtils.isNotBlank(channelInfo.getThirdPartyPlatformCode())) {
            res.setDrainagePlatform(channelInfo.getThirdPartyPlatformCode());
            res.setDrainageShareCode(channelInfo.getThirdPartyCustomerCode());
        }
    }

    /**
     * 从原单中获取投保人信息
     *
     * @param req
     * @param res
     */
    private void createGroupFhProposerByWhaleContract(WhaleContract req, SmCreateOrderSubmitRequest res) {
        //投保人信息处理
        FhProposer fhProposer = WhaleConvert.CNT.cvtProposer(req.getApplicantInfo());
        if (StringUtils.isNotBlank(fhProposer.getBirthday())) {
            fhProposer.setBirthday(fhProposer.getBirthday().substring(0, 10));
        }
        res.setProposerInfo(fhProposer);
        ApplicantInfo applicantInfo = req.getApplicantInfo();
        if (StringUtils.isNotBlank(req.getApplicantInfo().getBankCode())) {
            BankCardBindingReqDTO bankCard = new BankCardBindingReqDTO();

            bankCard.setBankCode(applicantInfo.getBankCode());
            bankCard.setFhOrderId(res.getFhOrderId());
            bankCard.setBankName(applicantInfo.getBankName());
            bankCard.setAccountNo(applicantInfo.getCardNo());
            res.setRenewBindInfo(bankCard);
        }
    }

    private void createGroupProductInfo(SmPlanVO planVO, String recommendId, SmCreateOrderSubmitRequest res) {
        FhProduct product = new FhProduct();
        res.setProductId(planVO.getProductId() + "");
        res.setPlanId(planVO.getId());
        res.setFhProductId(planVO.getFhProductId());
        product.setProductId(planVO.getProductId() + "");
        product.setRecommendId(recommendId);
        res.setProductInfo(product);
    }

    /**
     * 将主从被保人整合到一起
     *
     * @param groupInsuredInfos
     * @return
     */
    private List<InsuredBaseInfo> arrangeGroupInsuredBaseInfo(List<GroupInsuredInfo> groupInsuredInfos) {
        if (CollectionUtils.isEmpty(groupInsuredInfos)) {
            return Collections.emptyList();
        }
        List<InsuredBaseInfo> arrangeList = Lists.newArrayList();
        for (GroupInsuredInfo m : groupInsuredInfos) {
            arrangeList.add(m);
            if (CollectionUtils.isNotEmpty(m.getRelatedInsuredList())) {
                for (GroupRelatedInsuredInfo r : m.getRelatedInsuredList()) {
                    arrangeList.add(r);
                }
            }
        }
        return arrangeList;
    }

    private void mapGroupInsuredPerson(List<FhInsuredPerson> insureds, ContractBaseInfo contractBaseInfo, InsuredBaseInfo model, Map<String, SmPlanVO> smPlanMap, String policyNo) {
        mapInsuredPersonBase(insureds, contractBaseInfo, model, smPlanMap, policyNo);
    }


    private void mapInsuredPersonBase(List<FhInsuredPerson> insureds, ContractBaseInfo contractBaseInfo, InsuredBaseInfo model, Map<String, SmPlanVO> smPlanMap, String policyNo) {
        FhInsuredPerson insuredPerson = WhaleConvert.CNT.cvtGroupInsured(model);
        //出生日期只需要到日期就可以
        if (StringUtils.isNotBlank(model.getInsuredBirthday())) {
            insuredPerson.setBirthday(model.getInsuredBirthday().substring(0, 10));
        }
        String planCode = StringUtils.isNotBlank(model.getPlanCode())? model.getPlanCode() : contractBaseInfo.getMainProductCode();
        SmPlanVO smPlanVO = smPlanMap.get(planCode);

        TestPremiumProductForm ipProduct = new TestPremiumProductForm();
        ipProduct.setPremium(model.sumPremium());
        ipProduct.setAmount(model.sumAmount());

        ipProduct.setProductId(smPlanVO.getProductId());
        ipProduct.setPlanId(smPlanVO.getPlanId());
        ipProduct.setPlanCode(model.getPlanCode());
        insuredPerson.setBranchId(model.getInsuredCode());
        insuredPerson.setProduct(ipProduct);
        insuredPerson.setPolicyNo(policyNo);
        insuredPerson.setOldPolicyNo(contractBaseInfo.getSourcePolicyNo());
        insuredPerson.setDownloadURL(contractBaseInfo.getPolicyUrl());
        insuredPerson.setAppStatus(
                EnumWhalePolicyStatus.cvtInsCode(contractBaseInfo.getPolicyStatus()));
        //设置责任信息
        insuredPerson.setDuties(mapDutyInfo(policyNo, model.getProductInfoList()));

        insureds.add(insuredPerson);

    }

    private List<TestPremiumDutyForm> mapDutyInfo(String policyNo, List<ProductInfoList> productInfoList) {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }
        List<TestPremiumDutyForm> dutyForms = productInfoList
                .stream().map(WhaleConvert.CNT::cvtProduct)
                .peek(du -> {
                            du.setPolicyNo(policyNo);
                            du.setSysRiskId(-1);
                            du.setSysRiskDutyId(-1);
                            EnumWhalePolicyStatus by = EnumWhalePolicyStatus.findBy(du.getAppStatus());
                            if (Objects.nonNull(by) && StringUtils.isNotBlank(by.getSurrenderType())) {
                                du.setSurrenderType(by.getSurrenderType());
                                if (Objects.isNull(du.getSurrenderAmount())) {
                                    du.setSurrenderAmount(BigDecimal.ZERO);
                                }
                            }
                            du.setAppStatus(EnumWhalePolicyStatus.cvtInsCode(du.getAppStatus()));
                            du.setInsuredPeriodType(EnumWhaleInsuredPeriodType.valueOfCode(du.getInsuredPeriodType()).getInsCode());
                            du.setPaymentPeriodType(EnumWhalePayPeriodType.valueOfCode(du.getPaymentPeriodType()).getInsCode());
                            du.setPeriodType(EnumWhalePayType.valueOfCode(du.getPeriodType()).getInsCode());

                        }
                ).collect(Collectors.toList());
        return dutyForms;
    }

    private void mapDutyId(List<FhInsuredPerson> insureds, SmPlanVO planVO) {
        List<String> codes = insureds.stream()
                .map(FhInsuredPerson::getDuties).flatMap(Collection::stream)
                .map(TestPremiumDutyForm::getRiskCode)
                .distinct().collect(Collectors.toList());
        Map<String, SysRisk> stringSysRiskMap = LambdaUtils.groupByAndToFirstMap(riskMapper.selectByCodes(codes, planVO.getCompanyId()),
                SysRisk::getRiskCode);
        //赋值险总id
        SysRisk de = new SysRisk();
        de.setId(-1);
        insureds.stream()
                .map(FhInsuredPerson::getDuties).flatMap(Collection::stream)
                .forEach(d -> {
                    d.setSysRiskId(stringSysRiskMap.getOrDefault(d.getRiskCode(), de).getId());
                });
    }

    private List<SmOrderItem> mapOrderItems(List<FhInsuredPerson> insureds, String fhOrderId, String thGroupNo) {
        return insureds.stream().filter(o -> Objects.nonNull(o.getProduct()))
                .map(ip -> {
                    SmOrderItem orderItem = new SmOrderItem();
                    orderItem.setFhOrderId(fhOrderId);
                    orderItem.setIdNumber(ip.getIdNumber());
                    orderItem.setAppStatus(ip.getAppStatus());
                    orderItem.setThPolicyNo(thGroupNo);
                    orderItem.setPolicyNo(ip.getPolicyNo());
                    orderItem.setProductId(ip.getProduct().getProductId());
                    orderItem.setPlanId(ip.getProduct().getPlanId());
                    orderItem.setQty(1);
                    orderItem.setPlanCode(ip.getProduct().getPlanCode());
                    orderItem.setIdType(ip.getIdType());
                    orderItem.setType(0);
                    orderItem.setTotalAmount(ip.getProduct().getPremium());
                    orderItem.setUnitPrice(ip.getProduct().getPremium());
                    orderItem.setBranchId(ip.getBranchId());
                    orderItem.setActiveBranchId(ip.getBranchId());
                    orderItem.setInsuredAmount(ip.getProduct().getAmount());
                    return orderItem;
                }).collect(Collectors.toList());

    }

    /**
     * 注意，团险保费需单独计算
     *
     * @param contract
     * @return
     */
    private FhOrderInfo mapperGroupOrderInfo(WhaleContract contract) {
        FhOrderInfo fhOrderInfo = new FhOrderInfo();


        ContractExtendInfo extendInfo = contract.getContractExtendInfo();
        fhOrderInfo.setStartTime(extendInfo.getEnforceTime());

        if (StringUtils.isNotBlank(extendInfo.getApplicantTime())) {
            fhOrderInfo.setSubmitTime(extendInfo.getApplicantTime());
        } else if (StringUtils.isNotBlank(extendInfo.getApprovedRecordTime())) {
            fhOrderInfo.setSubmitTime(extendInfo.getApprovedRecordTime());
        } else if (StringUtils.isNotBlank(extendInfo.getOrderTime())) {
            fhOrderInfo.setSubmitTime(extendInfo.getOrderTime());
        } else {
            fhOrderInfo.setSubmitTime(DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT));
        }

        fhOrderInfo.setEndTime(extendInfo.getFailureTime());
        contract.getProductInfoList().stream()
                .filter(s -> Objects.equals(s.getMainInsurance(), 1))
                .findAny()
                .ifPresent(pro -> {
                    EnumWhaleInsuredPeriodType enumYgInsuredPeriodType = EnumWhaleInsuredPeriodType.valueOfCode(pro.getInsuredPeriodType());
                    if (StringUtils.isBlank(fhOrderInfo.getEndTime())) {
                        fhOrderInfo.setEndTime(enumYgInsuredPeriodType.getEndDate(pro, contract));
                    }
                    //这两个没有
                    fhOrderInfo.setUnderWritingAge("");
                    fhOrderInfo.setValidPeriod(enumYgInsuredPeriodType.getString(pro, contract));

                });

        return fhOrderInfo;
    }

    private Map<String, SmPlanVO> listChangeGroupPlan(WhaleContract req) {
        if (CollectionUtils.isEmpty(req.getGroupInsuredInfoList())) {
            return null;
        }
        Set<String> planCodes = Sets.newHashSet();
        req.getGroupInsuredInfoList().forEach(insured -> {
            String planCode = StringUtils.isNotBlank(insured.getPlanCode()) ?
                    insured.getPlanCode() : req.getContractBaseInfo().getMainProductCode();
            if (StringUtils.isNotBlank(planCode)) {
                planCodes.add(planCode);
            }
        });
        return listByPlanCodes(Lists.newLinkedList(planCodes));
    }

    private Map<String, SmPlanVO> listByPlanCodes(List<String> planCodes) {
        List<SmPlanVO> smPlanVOS = productService.listPlanByFhProductIds(null, planCodes);
        //当计划编码没有配置或者配置不全，则抛出异常
        if (CollectionUtils.isEmpty(smPlanVOS) || planCodes.size() != smPlanVOS.size()) {
            throw new MSBizNormalException(ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getCode(), ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getMsg() + "[" + String.join(",", planCodes) + "]");
        }
        return LambdaUtils.safeToMap(smPlanVOS, SmPlanVO::getFhProductId);
    }


    public String groupPlanCode(List<InsuredBaseInfo> req, String mainProductCode) {
        //取第一个被保人的计划编码 没有就用主险编码
        return   //如果第一个被保人传了计划编码就用这个作为默认的计划编码否则就是主险编码
                StringUtils.isNotBlank(req.get(0).getPlanCode()) ?
                        req.get(0).getPlanCode() : mainProductCode;
    }


    /************************begin 原单信息不存在的批改记录处理 **************************************/
    public List<SmCreateOrderSubmitRequest> cvtByGroupEndorsementNotifyOriginalNotExist(String fhOrderId, WhaleContract contract, PreservationDetail preservation) {
        EnumPreservationProject type = EnumPreservationProject.decode(preservation.getPreservationProject());

        switch (type) {
            case CHANGE_CHANNEL_REFERRER:
//                validPreservationRecommenderChange(preservation);
                return cvtChangeChannelReferrerOriginalNotExist(fhOrderId, contract, preservation);
            case ADD_OR_SUBTRACT:
//                validAddOrSubtract(preservation);
                return cvtAddOrSubtractOriginalNotExist(fhOrderId, contract, preservation);
            case SURRENDER:
//                validSurrender(preservation);
                return cvtSurrender(fhOrderId,preservation);
            default:
                log.warn("保全类型不支持");
                throw new MSBizNormalException(ExcptEnum.PRESERVATION_TYPE_NOT_SUPPORTED.getCode(), ExcptEnum.PRESERVATION_TYPE_NOT_SUPPORTED.getMsg() + ":" + type.getCode());
        }
    }

    private List<SmCreateOrderSubmitRequest> cvtChangeChannelReferrerOriginalNotExist(String fhOrderId, WhaleContract req, PreservationDetail preservation) {
        //第一个原始保单不存在时，推荐人变更后为非中和农信渠道的，这种情况应该抛异常,可能存在原单信息是中和农信渠道未推送过来。
        if (!Objects.equals(preservation.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)) {
            throw new MSBizNormalException(ExcptEnum.NOT_EXIST_ORIGINAL_AND_NOT_ZHNX_RECOMMEND);
        }
        List<SmCreateOrderSubmitRequest> submitRequestList = Lists.newArrayList();
        //
        //原单不存在的情况下，且变更的是批增单或原单的推荐人，则只需要增加批改后推荐人的信息
        List<PreservationRecommenderChange> notSurrenderList = preservation.getChannelReferrerChangeList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE) && Objects.equals(o.getSurrendered(), NOT_SURRENDERED)).collect(Collectors.toList());

        SmCreateOrderSubmitRequest res = doRecommendChangeAddOriginalNotExist(fhOrderId, preservation.getChannelReferrerWno(), preservation.getPreservationCode(), req, notSurrenderList);
        submitRequestList.add(res);
        return submitRequestList;
    }

    /**
     * 处理原单不存在，且小鲸变更的是批增或者原单的推荐人处理逻辑
     */
    private SmCreateOrderSubmitRequest doRecommendChangeAddOriginalNotExist(String fhOrderId, String recommender, String preservationCode, WhaleContract req, List<PreservationRecommenderChange> notSurrenderList) {
        SmCreateOrderSubmitRequest res = super.initGroupSubmitRequest(fhOrderId, EnumChannel.XJ.getCode(), null);
        res.setAppNo(req.getContractBaseInfo().getApplicantPolicyNo());
        //渠道相关信息
        createGroupChannelByWhaleContract(req, res);

        //投保人信息处理
        createGroupFhProposerByWhaleContract(req, res);
        //产品信息
        Set<String> planCodes = notSurrenderList.stream().map(PreservationRecommenderChange::getPlanCode).collect(Collectors.toSet());
        Map<String, SmPlanVO> planMap = listByPlanCodes(Lists.newLinkedList(planCodes));
        SmPlanVO planVO = planMap.get(planCodes.iterator().next());
        createGroupProductInfo(planVO, recommender, res);


        //被保人信息处理
        List<FhInsuredPerson> insureds = Lists.newArrayListWithCapacity(notSurrenderList.size());
        //订单总金额计算
        BigDecimal totalPremium = BigDecimal.ZERO;
        List<SmOrderItem> orderItemList = Lists.newArrayList();

        for (PreservationRecommenderChange item : notSurrenderList) {
            SmPlanVO plan = planMap.get(item.getPlanCode());
            mapRecommenderChangeToInsuredPerson(res.getFhOrderId(), req.getContractBaseInfo().getPolicyNo(), item, req.getContractBaseInfo().getPolicyNo(), preservationCode, plan, orderItemList, insureds);
        }
        //处理险总id信息
        mapDutyId(insureds, planVO);
        res.setInsuredPerson(insureds);
        //分单表处理
        res.setOrderItemList(orderItemList);

        //订单信息
        FhOrderInfo fhOrderInfo = mapperGroupOrderInfo(req);
        fhOrderInfo.setTotalAmount(totalPremium);
        res.setOrderInfo(fhOrderInfo);
        res.setQty(1);

        return res;
    }


    /************************end 原单信息不存在的批改记录处理 **************************************/

    /************************begin 原单信息存在的批改记录处理 **************************************/
    public List<SmCreateOrderSubmitRequest> cvtByGroupEndorsementNotify(String originalOrderId, String originalPolicyNo, PreservationDetail preservation) throws Exception {
//        if (Objects.isNull(preservation)) {
//            throw new MSBizNormalException(ExcptEnum.PRESERVATION_INFO_IS_NULL);
//        }
//        if (StringUtils.isBlank(preservation.getSellChannelCode())) {
//            throw new MSBizNormalException(ExcptEnum.PRESERVATION_INFO_IS_NULL);
//        }
        EnumPreservationProject type = EnumPreservationProject.decode(preservation.getPreservationProject());
        //+2是因为一个bug导致部分订单下标已经超过count的个数2个位置
        int count = groupHelper.nextCorrectSeq(originalOrderId);

        log.info("获取保单当前的批改序号:{},{}",originalOrderId,count);
        Set<String> planCodes;
        ChangeChannelReferrerContext context;
        switch (type) {
            case CHANGE_CHANNEL_REFERRER:
                //获取所有计划编码
                List<PreservationRecommenderChange> changeList = preservation.getChannelReferrerChangeList();
                planCodes = changeList.stream().map(PreservationRecommenderChange::getPlanCode).collect(Collectors.toSet());
                context = createContext(originalOrderId, originalPolicyNo, count, preservation, planCodes);
//                validPreservationRecommenderChange(preservation);
                if (Objects.equals(preservation.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)) {
                    return cvtChangeChannelReferrerZhnx(context);
                } else {
                    return cvtChangeChannelReferrerOther(context);
                }
            case ADD_OR_SUBTRACT:
                //获取所有计划编码
                List<PreservationAddOrSubtract> addOrSubtracts = preservation.getAddOrSubtractList();
                planCodes = addOrSubtracts.stream().map(PreservationAddOrSubtract::getPlanCode).collect(Collectors.toSet());
                context = createContext(originalOrderId, originalPolicyNo, count, preservation, planCodes);
//                validAddOrSubtract(preservation);
                return cvtAddOrSubtract(context);
            case SURRENDER:
//                validSurrender(preservation);
                return cvtSurrender(originalOrderId,preservation);
            default:
                log.warn("保全类型不支持");
                throw new MSBizNormalException(ExcptEnum.PRESERVATION_TYPE_NOT_SUPPORTED.getCode(), ExcptEnum.PRESERVATION_TYPE_NOT_SUPPORTED.getMsg() + ":" + type.getCode());
        }

    }

    /************************begin 原单信息存在的批改记录处理 **************************************/
    public BusinessCheckVo validCorrectFlow(PreservationDetail preservation) {
        if (Objects.isNull(preservation)) {
            return BusinessCheckVo.fail(809005,ExcptEnum.PRESERVATION_INFO_IS_NULL.getMsg());
        }
        if (StringUtils.isBlank(preservation.getSellChannelCode())) {
            return BusinessCheckVo.fail(809010,ExcptEnum.SELL_CHANNEL_CODE_IS_NULL.getMsg());
        }
        EnumPreservationProject type = EnumPreservationProject.decode(preservation.getPreservationProject());
        switch (type) {
            case CHANGE_CHANNEL_REFERRER:
                return validPreservationRecommenderChange(preservation);
            case ADD_OR_SUBTRACT:
                return validAddOrSubtract(preservation);
            case SURRENDER:
                return validSurrender(preservation);
            default:
                return BusinessCheckVo.ok();
        }

    }

    private BusinessCheckVo validAddOrSubtract(PreservationDetail preservation) {
        if (CollectionUtils.isEmpty(preservation.getAddOrSubtractList())) {
            String errMsg = ExcptEnum.ADD_OR_SUBTRACT_DETAIL_IS_NULL.getMsg() + ",保全编号:"+ preservation.getPreservationCode();
            log.warn(errMsg);
            return BusinessCheckVo.fail(errMsg);
        }
        String policyNo = preservation.getPolicyCode();
        String endorsementNo = preservation.getEndorsementNo();
        if(StringUtils.isNotBlank(endorsementNo)) {
            List<SmOrderItem> orderItemList = orderItemMapper.selectByEndorsementNo(policyNo, endorsementNo);
            if(CollectionUtils.isNotEmpty(orderItemList)){
                return BusinessCheckVo.fail(100000,"该批改单号已存在，请勿重复推送:"+endorsementNo);
            }
        }
        return BusinessCheckVo.ok();
    }

    private BusinessCheckVo validSurrender(PreservationDetail preservation) {
        PreserveSurrenderDetailVo surrenderDetailVo = preservation.getSurrenderDetailVo();
        if (surrenderDetailVo == null) {
            String errMsg = ExcptEnum.SURRENDER_DETAIL_IS_NULL.getMsg() + ",保全编号:"+ preservation.getPreservationCode();
            log.warn(errMsg);
            return BusinessCheckVo.fail(errMsg);
        }
        if (CollectionUtils.isEmpty(surrenderDetailVo.getInsuredList())) {
            String errMsg = ExcptEnum.SURRENDER_DETAIL_IS_NULL.getMsg() + ",保全编号:"+ preservation.getPreservationCode();
            log.warn(errMsg);
            return BusinessCheckVo.fail(errMsg);
        }
        String policyNo = preservation.getPolicyCode();
        String endorsementNo = preservation.getEndorsementNo();
        if(StringUtils.isNotBlank(endorsementNo)) {
            List<SmOrderItem> orderItemList = orderItemMapper.selectByEndorsementNo(policyNo, endorsementNo);
            if(CollectionUtils.isNotEmpty(orderItemList)){
                return BusinessCheckVo.fail(100000,"该批改单号已存在，请勿重复推送:"+endorsementNo);
            }
        }
        return BusinessCheckVo.ok();
    }

    private List<SmCreateOrderSubmitRequest> cvtAddOrSubtract(ChangeChannelReferrerContext context) throws Exception {
        List<SmCreateOrderSubmitRequest> submitList = Lists.newArrayList();
        //1. 原单是中和农信，且是批增单的 处理
        List<PreservationAddOrSubtract> list = context.getPreservation().getAddOrSubtractList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)).collect(Collectors.toList());
        Map<String, List<PreservationAddOrSubtract>> addOrSubtractMap = LambdaUtils.groupBy(list, PreservationAddOrSubtract::getType);

        List<PreservationAddOrSubtract> addList = addOrSubtractMap.get(ADD_OR_SUBTRACT_TYPE_ADD);
        List<PreservationAddOrSubtract> subtractList = addOrSubtractMap.get(ADD_OR_SUBTRACT_TYPE_SUB);

        PreservationDetail preservation = context.getPreservation();
        String preservationCode = preservation.getPreservationCode();
        String endorsementNo = preservation.getEndorsementNo();
        endorsementNo = StringUtils.isBlank(endorsementNo)?preservationCode:endorsementNo;

        //2. 批增人员处理
        doAdd(context,endorsementNo, addList, submitList);
        //3. 减员处理
        doSubtract(context,endorsementNo, subtractList, submitList);

        return submitList;
    }

    private List<SmCreateOrderSubmitRequest> cvtSurrender(String orderId,PreservationDetail preservation){
        List<SmCreateOrderSubmitRequest> submitList = Lists.newArrayList();

        SmOrderApplicant applicant = orderMapper.selectOrderApplicantByOrderId(orderId);

        doSurrender(orderId,applicant,preservation,submitList);
        return submitList;
    }

    private void doAdd(ChangeChannelReferrerContext context, String endorsementNo,List<PreservationAddOrSubtract> addList, List<SmCreateOrderSubmitRequest> submitRequestList) {
        if (CollectionUtils.isEmpty(addList)) {
            return;
        }
        Map<String,List<PreservationAddOrSubtract>> addMap = LambdaUtils.groupBy(addList,PreservationAddOrSubtract::getCustomerManagerChannelCode);
        for(String key : addMap.keySet()){
            doAddByRecommend(context,key,endorsementNo,addMap.get(key),submitRequestList);
        }

    }
    private void doAddByRecommend(ChangeChannelReferrerContext context,String recommendId, String endorsementNo,List<PreservationAddOrSubtract> addList, List<SmCreateOrderSubmitRequest> submitRequestList){
        SmCreateOrderSubmitRequest submitReq = super.initGroupSubmitRequest(context.getOriginalOrderId() + "_" + context.getEmit().addAndGet(1), EnumChannel.XJ.getCode(), null);
        submitReq.setSubChannel(context.getSmBaseOrder().getSubChannel());
        PreservationDetail detail = context.getPreservation();
        //订单信息
        FhOrderInfo fhOrderInfo = new FhOrderInfo();
        BigDecimal totalAmount = addList.stream().map(PreservationAddOrSubtract::getSinglePremium).reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        fhOrderInfo.setTotalAmount(totalAmount);

        fhOrderInfo.setSubmitTime(DateUtil.format(DateUtil.getNow(), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setStartTime(detail.getPreservationEffectTime());
        if(Objects.nonNull(context.getSmBaseOrder().getEndTime())){
            fhOrderInfo.setEndTime(DateUtils.format(context.getSmBaseOrder().getEndTime(),DateUtil.CN_LONG_FORMAT));
        }
        fhOrderInfo.setUnderWritingAge("");
        fhOrderInfo.setValidPeriod("");
        fhOrderInfo.setAppNo(context.getSmBaseOrder().getAppNo());
        fhOrderInfo.setPolicyNo(context.getSmBaseOrder().getPolicyNo());
        fhOrderInfo.setEndorsementNo(endorsementNo);
        submitReq.setOrderInfo(fhOrderInfo);
        submitReq.setQty(1);
        //保险产品信息
        FhProduct productInfo = new FhProduct();
        //推荐人
        //productInfo.setRecommendId(context.getPreservation().getChannelReferrerWno());
        productInfo.setRecommendId(recommendId);
        SmPlanVO planVO = context.getPlanMap().get(addList.get(0).getPlanCode());
        if(StringUtils.equals(EnumOrderSubChannel.IMPORT.getCode(),context.getSmBaseOrder().getSubChannel())){
            submitReq.setChannel(planVO.getChannel());
        }
        //取第一个计划，必须保证planMap的计划的佣金比例需要一致，且属于同一个产品
        productInfo.setProductId(planVO.getProductId() + "");
        submitReq.setProductInfo(productInfo);
        submitReq.setProductId(planVO.getProductId() + "");
        submitReq.setFhProductId(planVO.getFhProductId());
        submitReq.setPlanId(planVO.getPlanId());

        //投保人信息
        submitReq.setProposerInfo(createEndtosementFhPropose(context.getApplicant()));

        List<SmOrderItem> orderItemList = Lists.newArrayList();
        List<FhInsuredPerson> insureds = Lists.newArrayList();
        String correctPolicyNo = endorsementNo + ENDTOSEMENT_INCRE;
        for (PreservationAddOrSubtract item : addList) {
            SmPlanVO plan = context.getPlanMap().get(item.getPlanCode());
            mapAddOrSubtractToInsuredPerson(submitReq.getFhOrderId(), correctPolicyNo, item, context.getSmBaseOrder().getPolicyNo(), endorsementNo, plan, orderItemList, insureds);
        }
        //处理险总id信息
        mapDutyId(insureds, planVO);

        submitReq.setOrderItemList(orderItemList);
        submitReq.setInsuredPerson(insureds);
        submitRequestList.add(submitReq);
    }

//    private void doAdd(ChangeChannelReferrerContext context, String endorsementNo,List<PreservationAddOrSubtract> addList, List<SmCreateOrderSubmitRequest> submitRequestList) {
//        if (CollectionUtils.isEmpty(addList)) {
//            return;
//        }
//        SmCreateOrderSubmitRequest submitReq = super.initGroupSubmitRequest(context.getOriginalOrderId() + "_" + context.getEmit().addAndGet(1), EnumChannel.XJ.getCode(), null);
//        submitReq.setSubChannel(context.getSmBaseOrder().getSubChannel());
//        PreservationDetail detail = context.getPreservation();
//        //订单信息
//        FhOrderInfo fhOrderInfo = new FhOrderInfo();
//        BigDecimal totalAmount = addList.stream().map(PreservationAddOrSubtract::getSinglePremium).reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
//        fhOrderInfo.setTotalAmount(totalAmount);
//
//        fhOrderInfo.setSubmitTime(DateUtil.format(DateUtil.getNow(), DateUtil.CN_LONG_FORMAT));
//        fhOrderInfo.setStartTime(detail.getPreservationEffectTime());
//        fhOrderInfo.setEndTime(DateUtils.format(context.getSmBaseOrder().getEndTime()));
//        fhOrderInfo.setUnderWritingAge("");
//        fhOrderInfo.setValidPeriod("");
//        fhOrderInfo.setAppNo(context.getSmBaseOrder().getAppNo());
//        fhOrderInfo.setPolicyNo(context.getSmBaseOrder().getPolicyNo());
//        fhOrderInfo.setEndorsementNo(endorsementNo);
//        submitReq.setOrderInfo(fhOrderInfo);
//        submitReq.setQty(1);
//        //保险产品信息
//        FhProduct productInfo = new FhProduct();
//        //推荐人
//        productInfo.setRecommendId(context.getPreservation().getChannelReferrerWno());
//        SmPlanVO planVO = context.getPlanMap().get(addList.get(0).getPlanCode());
//        //取第一个计划，必须保证planMap的计划的佣金比例需要一致，且属于同一个产品
//        productInfo.setProductId(planVO.getProductId() + "");
//        submitReq.setProductInfo(productInfo);
//        submitReq.setProductId(planVO.getProductId() + "");
//        submitReq.setFhProductId(planVO.getFhProductId());
//        submitReq.setPlanId(planVO.getPlanId());
//
//        //投保人信息
//        submitReq.setProposerInfo(createEndtosementFhPropose(context.getApplicant()));
//
//        List<SmOrderItem> orderItemList = Lists.newArrayList();
//        List<FhInsuredPerson> insureds = Lists.newArrayList();
//        String correctPolicyNo = endorsementNo + ENDTOSEMENT_INCRE;
//        for (PreservationAddOrSubtract item : addList) {
//            SmPlanVO plan = context.getPlanMap().get(item.getPlanCode());
//            mapAddOrSubtractToInsuredPerson(submitReq.getFhOrderId(), correctPolicyNo, item, context.getSmBaseOrder().getPolicyNo(), endorsementNo, plan, orderItemList, insureds);
//        }
//        //处理险总id信息
//        mapDutyId(insureds, planVO);
//
//        submitReq.setOrderItemList(orderItemList);
//        submitReq.setInsuredPerson(insureds);
//        submitRequestList.add(submitReq);
//    }

    private void mapAddOrSubtractToInsuredPerson(String fhOrderId, String policyNo, PreservationAddOrSubtract item, String thPolicyNo, String thEndorsementNo, SmPlanVO plan, List<SmOrderItem> orderItemList, List<FhInsuredPerson> insuredPersonList) {
        //被保人
        FhInsuredPerson insuredPerson = WhaleConvert.CNT.cvtAddOrSubtractInsured(item);
        insuredPerson.setPolicyNo(policyNo);
        insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
        insuredPerson.setDownloadURL(item.getInsuredPolicyUrl());
        insuredPerson.setPersonGender(cvtMemberGender(item.getInsuredGender()));
        //被保人险种信息
        insuredPerson.setProduct(mapAddInsuredPersonProductInfo(item.getSinglePremium(), item.sumAmount(), plan));
        //设置责任信息
        insuredPerson.setDuties(mapDutyInfo(policyNo, item.getProductInfoList()));
        insuredPersonList.add(insuredPerson);

        //item表
        SmOrderItem orderItem = mapAddSmOrderItem(fhOrderId, thPolicyNo, thEndorsementNo, null, policyNo, insuredPerson, plan, item.getSinglePremium());
        orderItemList.add(orderItem);
    }

    private String cvtMemberGender(String gender){
        if(Objects.equals(gender,"男")){
            return "1";
        }
        if(Objects.equals(gender,"女")){
            return "0";
        }
        return gender;
    }

    private TestPremiumProductForm mapAddInsuredPersonProductInfo(BigDecimal premium, BigDecimal amount, SmPlanVO plan) {
        TestPremiumProductForm ipProduct = new TestPremiumProductForm();
        ipProduct.setPremium(premium);
        ipProduct.setAmount(amount);
        ipProduct.setProductId(plan.getProductId());
        ipProduct.setPlanId(plan.getPlanId());
        ipProduct.setPlanCode(plan.getFhProductId());
        return ipProduct;
    }

    private SmOrderItem mapAddSmOrderItem(String fhOrderId, String thPolicyNo, String thEndorsementNo, String insuredCode,
                                          String policyNo, FhInsuredPerson insuredPerson, SmPlanVO plan, BigDecimal premium) {
        SmOrderItem orderItem = new SmOrderItem();
        orderItem.setActiveBranchId(insuredCode);
        orderItem.setBranchId(insuredCode);
        orderItem.setFhOrderId(fhOrderId);
        orderItem.setThPolicyNo(thPolicyNo);
        orderItem.setType(1);
        orderItem.setThEndorsementNo(thEndorsementNo);
        orderItem.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
        orderItem.setPolicyNo(policyNo);
        orderItem.setIdType(insuredPerson.getIdType());
        orderItem.setIdNumber(insuredPerson.getIdNumber());
        orderItem.setPlanCode(plan.getFhProductId());
        orderItem.setPlanId(plan.getId());
        orderItem.setProductId(plan.getProductId());
        orderItem.setUnitPrice(premium);
        orderItem.setQty(1);
        orderItem.setTotalAmount(premium);
        orderItem.setEndorsementAmount(BigDecimal.ZERO);
        return orderItem;
    }

    private void doSubtract(ChangeChannelReferrerContext context,String endorsementNo, List<PreservationAddOrSubtract> subList, List<SmCreateOrderSubmitRequest> submitRequestList) {
        if (CollectionUtils.isEmpty(subList)) {
            return;
        }
        List<String> surrenderIdCardList = subList.stream().map(PreservationAddOrSubtract::getInsuredIdCard).collect(Collectors.toList());

        String originalOrderId = context.getOriginalOrderId();

        Map<String, SmOrderItem> orderItemMap = getOriginalItemMap(originalOrderId, surrenderIdCardList);
        //变更前的被保人数与数据库被保人数不一致
        if (surrenderIdCardList.size() != orderItemMap.size()) {
            log.warn("保全增减员减员人数{}与数据库待减被保人数{}不一致,原始订单id为{}", surrenderIdCardList.size(), orderItemMap.size(), originalOrderId);
            throw new MSBizNormalException(ExcptEnum.BEFORE_DATABASE_NUM_NOT_MATCH.getCode(), ExcptEnum.BEFORE_DATABASE_NUM_NOT_MATCH.getMsg());
        }
        //订单明细对应的订单号（订单明细可能分布在不同订单中）
        List<SmOrder> orderList = listOriginalOrder(orderItemMap);

        Date preservationEffectTime = DateUtil.parseDate(context.getPreservation().getPreservationEffectTime());
        for (PreservationAddOrSubtract before : subList) {
            if (!Objects.equals(before.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)) {
                continue;
            }
            int curId = context.getEmit().addAndGet(1);
            String surrenderIdCard = before.getInsuredIdCard();

            SmOrderItem o = orderItemMap.get(surrenderIdCard);

            if (Objects.nonNull(o)) {
                if (Objects.equals(o.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    throw new MSBizNormalException(ExcptEnum.THE_NEWEST_IS_SURRENDER.getCode(), ExcptEnum.THE_NEWEST_IS_SURRENDER.getMsg());
                }

                String correctPolicyNo = endorsementNo + ENDTOSEMENT_DECRE + curId;
                //批减人对应保单信息
                SmOrder order = orderList.stream().filter(smOrder -> Objects.equals(o.getFhOrderId(), smOrder.getFhOrderId())).findFirst().get();
                //log.info("数据库的推荐人为{},小鲸推送推荐人为{}",order.getRecommendId(),before.getChannelReferrerWno());
                if (!Objects.equals(order.getRecommendId(), before.getCustomerManagerChannelCode())) {
                    String msg = String.format("推送的减员的被保人%s的渠道推荐人与数据库里不一致，数据库的推荐人为%s,小鲸推送推荐人为%s", before.getInsuredName(), order.getRecommendId(), before.getChannelReferrerWno());
                    log.warn(msg);
                    throw new MSBizNormalException(ExcptEnum.CHANNE_RECOMMENDER_NOT_MATCH.getCode(), msg);
                }

                SmCreateOrderSubmitRequest submitReq = super.initGroupSubmitRequest(originalOrderId + "_" + curId, EnumChannel.XJ.getCode(), null);
                submitReq.setSubChannel(order.getSubChannel());

                //分单处理
                mapSubtractOrderItem(correctPolicyNo, o, endorsementNo, before.getSinglePremium(),submitReq);

                //推荐人信息
                setRecommendInfo(submitReq, order.getRecommendJobCode(), order.getRecommendMainJobNumber(), order.getRecommendOrgCode(), order.getCustomerAdminJobCode(), order.getCustomerAdminMainJobNumber(), order.getCustomerAdminOrgCode());

                //订单信息
                mapChannelReferrerOrderInfo(order, o, context.getPreservation().getPreservationEffectTime(), submitReq);
                submitReq.setCommissionId(order.getCommissionId());
                submitReq.setQty(1);
                //产品信息
                setProductInfo(submitReq, order.getRecommendId(), order.getCustomerAdminId(), context.getPlanMap().get(before.getPlanCode()));
                //农保导单渠道设置
                if(StringUtils.equals(EnumOrderSubChannel.IMPORT.getCode(),context.getSmBaseOrder().getSubChannel())){
                    if(Objects.nonNull(context.getPlanMap().get(before.getPlanCode()))){
                        submitReq.setChannel(context.getPlanMap().get(before.getPlanCode()).getChannel());
                    }
                }
                //投保人信息
                submitReq.setProposerInfo(createEndtosementFhPropose(context.getApplicant()));

                //被保人信息
                //setInsuredInfo(submitReq, insured, context.getPreservationCode(), index);
                FhInsuredPerson insuredPerson = new FhInsuredPerson();
                insuredPerson.setPolicyNo(correctPolicyNo);
                insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
                insuredPerson.setIdType(before.getInsuredIdType());
                insuredPerson.setIdNumber(before.getInsuredIdCard());
                insuredPerson.setBirthday(before.getInsuredBirthday());
                insuredPerson.setPersonName(before.getInsuredName());
//                insuredPerson.setSurrenderTime(preservationEffectTime);
                //⭐️按方亚&李雪的要求，将退保时间修正为数据同步时间⭐️
                insuredPerson.setSurrenderTime(new Date());
                insuredPerson.setDuties(mapDutyInfo(correctPolicyNo, before.getProductInfoList()));
                List<FhInsuredPerson> insureds = Lists.newArrayListWithCapacity(1);
                insureds.add(insuredPerson);
                submitReq.setInsuredPerson(insureds);
                mapDutyId(insureds, context.getPlanMap().get(before.getPlanCode()));

                submitRequestList.add(submitReq);
            }
        }
    }

    private void doSurrender(String originalOrderId,SmOrderApplicant applicant,PreservationDetail preservation, List<SmCreateOrderSubmitRequest> submitRequestList){
        if (preservation == null) {
            log.warn("保全信息为空");
            return;
        }

        PreserveSurrenderDetailVo surrenderDetailVo = preservation.getSurrenderDetailVo();

        if (surrenderDetailVo == null) {
             log.warn("退保详情信息为空");
            return;
        }

        List<PreserveSurrenderInsuredVo> surrenderInsuredList = surrenderDetailVo.getInsuredList();
        if (CollectionUtils.isEmpty(surrenderInsuredList)) {
            log.warn("退保被保人列表为空");
            return;
        }

        List<String> surrenderIdCardList = surrenderInsuredList.stream().map(PreserveSurrenderInsuredVo::getInsuredIdCard).collect(Collectors.toList());

        Collection<SmOrderItem> activeItemList = listOriginalItem(originalOrderId, surrenderIdCardList);

        if(CollectionUtils.isEmpty(activeItemList)){
            String msg = String.format("当前保单的在保人员为空:%s", originalOrderId);
            log.warn(msg);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), msg);
        }
        int chonghoActive = activeItemList.size();
        int whaleActive = surrenderInsuredList.size();
        if(chonghoActive!=whaleActive){
            String msg = String.format("农保系统和小鲸系统的保单在保人员不一致:%s", originalOrderId);
            log.warn(msg);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), msg);
        }

        List<String> orderIdList = activeItemList.stream().map(SmOrderItem::getFhOrderId).collect(Collectors.toList());
        //订单明细对应的订单号（订单明细可能分布在不同订单中）
        List<SmOrder> orderList = listOriginalOrder(orderIdList);

        if(CollectionUtils.isEmpty(orderList)){
            String msg = String.format("订单列表为空:%s", originalOrderId);
            log.warn(msg);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), msg);
        }

        SmOrder luck = orderList.get(0);
        Integer planId = luck.getPlanId();
        SmPlanVO plan = productService.getPlanById(planId);

        int seq = groupHelper.nextCorrectSeq(originalOrderId);
        Date processingTime =preservation.getProcessingTime();

        Map<String,PreserveSurrenderInsuredVo> surrenderInsuredMap = LambdaUtils.safeToMap(surrenderInsuredList,entry -> entry.getInsuredIdCard().toUpperCase());

        Map<String, SmOrder> orderMap = LambdaUtils.safeToMap(orderList,SmOrder::getFhOrderId);

        String endorsementNo = preservation.getEndorsementNo();
        endorsementNo = StringUtils.isBlank(endorsementNo)?preservation.getPreservationCode():endorsementNo;
        AtomicInteger ai = new AtomicInteger(seq);

        for (SmOrderItem orderItem : activeItemList) {

            String orderId = originalOrderId + "_" + ai.getAndAdd(1);
            String surrenderIdCard = orderItem.getIdNumber();
            surrenderIdCard = surrenderIdCard.toUpperCase();

            PreserveSurrenderInsuredVo before = surrenderInsuredMap.get(surrenderIdCard);

            if (before == null) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "农保系统保单被保人不存在:" + surrenderIdCard);
            }

            if (Objects.equals(orderItem.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                throw new MSBizNormalException(ExcptEnum.THE_NEWEST_IS_SURRENDER.getCode(), ExcptEnum.THE_NEWEST_IS_SURRENDER.getMsg());
            }

            String correctPolicyNo = endorsementNo + ENDTOSEMENT_DECRE + seq;
            //批减人对应保单信息
            SmOrder order = orderMap.get(orderItem.getFhOrderId());
            if (!Objects.equals(order.getCustomerAdminId(), before.getChannelReferrerWno())) {
                String msg = String.format("推送的减员的被保人%s的渠道推荐人与数据库里不一致，数据库的推荐人为%s,小鲸推送推荐人为%s", before.getInsuredName(), order.getRecommendId(), before.getChannelReferrerWno());
                log.warn(msg);
                throw new MSBizNormalException(ExcptEnum.CHANNE_RECOMMENDER_NOT_MATCH.getCode(), msg);
            }
            String channel = order.getChannel();

            SmCreateOrderSubmitRequest submitReq = super.initGroupSubmitRequest(orderId, channel, order.getSubChannel());
            submitReq.setSubChannel(order.getSubChannel());

            //分单处理
            mapSurrenderOrderItem(correctPolicyNo, endorsementNo, before,orderItem, submitReq);

            //推荐人信息
            setRecommendInfo(submitReq, order.getRecommendJobCode(), order.getRecommendMainJobNumber(), order.getRecommendOrgCode(), order.getCustomerAdminJobCode(), order.getCustomerAdminMainJobNumber(), order.getCustomerAdminOrgCode());

            //订单信息
            mapChannelReferrerOrderInfo(order, orderItem, preservation.getPreservationEffectTime(), submitReq);
            submitReq.setCommissionId(order.getCommissionId());
            submitReq.setQty(1);
            //产品信息
            setProductInfo(submitReq, order.getRecommendId(), order.getCustomerAdminId(), plan);

            //投保人信息
            submitReq.setProposerInfo(createEndtosementFhPropose(applicant));

            //被保人信息
            //setInsuredInfo(submitReq, insured, context.getPreservationCode(), index);
            FhInsuredPerson insuredPerson = new FhInsuredPerson();
            insuredPerson.setPolicyNo(correctPolicyNo);
            insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            insuredPerson.setIdType(orderItem.getIdType());
            insuredPerson.setIdNumber(before.getInsuredIdCard());
            insuredPerson.setPersonName(before.getInsuredName());
            insuredPerson.setSurrenderTime(processingTime);
            List<FhInsuredPerson> insureds = Lists.newArrayListWithCapacity(1);
            insureds.add(insuredPerson);
            submitReq.setInsuredPerson(insureds);
            submitRequestList.add(submitReq);
        }
    }


    private List<SmCreateOrderSubmitRequest> cvtAddOrSubtractOriginalNotExist(String fhOrderId, WhaleContract req, PreservationDetail preservation) {
        //第一个原始保单不存在时，推荐人变更后为非中和农信渠道的，这种情况应该抛异常,可能存在原单信息是中和农信渠道未推送过来。
        if (!Objects.equals(preservation.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)) {
            throw new MSBizNormalException(ExcptEnum.NOT_EXIST_ORIGINAL_AND_NOT_ZHNX_RECOMMEND);
        }
        List<SmCreateOrderSubmitRequest> submitRequestList = Lists.newArrayList();
        //
        //原单不存在的情况下，且变更的是批增单或原单的推荐人，则只需要增加批改后推荐人的信息
        List<PreservationAddOrSubtract> channelList = preservation.getAddOrSubtractList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)).collect(Collectors.toList());

        //原单不存在的情况下，增减员操作不应该有减员信息
        List<PreservationAddOrSubtract> subtracts = channelList.stream().filter(o -> Objects.equals(o.getType(), ADD_OR_SUBTRACT_TYPE_SUB)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subtracts)) {
            log.error("保全{}存在异常的减员信息", preservation.getPreservationCode());
            throw new MSBizNormalException(ExcptEnum.EXIST_EXCPT_SUBTRACT);
        }

        String endorsementNo = preservation.getEndorsementNo();
        String preservationCode = preservation.getPreservationCode();
        endorsementNo = StringUtils.isBlank(endorsementNo)?preservationCode:endorsementNo;
        Map<String,List<PreservationAddOrSubtract>> addOrSubtractMap = LambdaUtils.groupBy(channelList,PreservationAddOrSubtract::getCustomerManagerChannelCode);
        SmCreateOrderSubmitRequest res;
        //产品信息
        Set<String> planCodes = channelList.stream().map(PreservationAddOrSubtract::getPlanCode).collect(Collectors.toSet());
        Map<String, SmPlanVO> planMap = listByPlanCodes(Lists.newLinkedList(planCodes));
        int index = 0;
        String thPolicyNo = req.getContractBaseInfo().getPolicyNo();
        String thEndorsementNo = endorsementNo;
        String policyNo = thPolicyNo;
        String orderId = fhOrderId;
        for(String key : addOrSubtractMap.keySet()){
            if (index != 0) {
                orderId = fhOrderId + "_" + index;
                policyNo = thPolicyNo + "_" + index;
            }
            res= doAddOriginalNotExist(orderId, policyNo,key,thPolicyNo, thEndorsementNo,planMap, req, addOrSubtractMap.get(key));
            submitRequestList.add(res);
            index++;
        }
        return submitRequestList;
    }

    private SmCreateOrderSubmitRequest doAddOriginalNotExist(String orderId, String policyNo,String recommender,String thPolicyNo, String thEndorsementNo,Map<String, SmPlanVO> planMap, WhaleContract req, List<PreservationAddOrSubtract> channelList) {
        SmCreateOrderSubmitRequest res = super.initGroupSubmitRequest(orderId, EnumChannel.XJ.getCode(), null);
        res.setAppNo(req.getContractBaseInfo().getApplicantPolicyNo());
        //渠道相关信息
        createGroupChannelByWhaleContract(req, res);

        //投保人信息处理
        createGroupFhProposerByWhaleContract(req, res);
        Set<String> planCodes = channelList.stream().map(PreservationAddOrSubtract::getPlanCode).collect(Collectors.toSet());
        SmPlanVO planVO = planMap.get(planCodes.iterator().next());
        createGroupProductInfo(planVO, recommender, res);


        //被保人信息处理
        List<FhInsuredPerson> insureds = Lists.newArrayListWithCapacity(channelList.size());
        //订单总金额计算
        BigDecimal totalPremium = BigDecimal.ZERO;
        List<SmOrderItem> orderItemList = Lists.newArrayList();

        for (PreservationAddOrSubtract item : channelList) {
            SmPlanVO plan = planMap.get(item.getPlanCode());
            mapAddOrSubtractToInsuredPerson(res.getFhOrderId(), policyNo, item, thPolicyNo, thEndorsementNo, plan, orderItemList, insureds);
        }
        //处理险总id信息
        mapDutyId(insureds, planVO);
        res.setInsuredPerson(insureds);
        //分单表处理
        res.setOrderItemList(orderItemList);

        //订单信息
        FhOrderInfo fhOrderInfo = mapperGroupOrderInfo(req);
        fhOrderInfo.setTotalAmount(totalPremium);
        res.setOrderInfo(fhOrderInfo);
        res.setQty(1);

        return res;
    }


    private BusinessCheckVo validPreservationRecommenderChange(PreservationDetail preservation) {
        List<PreservationRecommenderChange> changeList = preservation.getChannelReferrerChangeList();
        if (CollectionUtils.isEmpty(changeList)) {
            String errMsg =ExcptEnum.RECOMMEND_CHANGE_REFERRER_NOT_EXIST.getMsg() + ",保全编号:" + preservation.getPreservationCode();
            log.warn(errMsg);
           return BusinessCheckVo.fail(809007,errMsg);
        }
        return BusinessCheckVo.ok();
    }

    public ChangeChannelReferrerContext createContext(String originalOrderId, String originalPolicyNo, Integer count, PreservationDetail preservation, Collection<String> planCodes) {

        Map<String, SmPlanVO> planMap = listByPlanCodes(Lists.newArrayList(planCodes));

        //获取投保人信息
        SmOrderApplicant applicant = orderMapper.selectOrderApplicantByOrderId(originalOrderId);
        SmBaseOrderVO smBaseOrder = orderMapper.getBaseOrderInfoByOrderId(originalOrderId);
        return ChangeChannelReferrerContext.builder()
                .applicant(applicant)
                .originalOrderId(originalOrderId)
                .originalPolicyNo(originalPolicyNo)
                .count(count)
                .emit(new AtomicInteger(count))
                .smBaseOrder(smBaseOrder)
                .preservation(preservation)
                .planMap(planMap)
                .build();

    }

    /**
     * 处理渠道推荐人变更(变更后的推荐人为其他渠道)
     *
     * @param context
     * @return
     * @throws Exception
     */
    private List<SmCreateOrderSubmitRequest> cvtChangeChannelReferrerOther(ChangeChannelReferrerContext context) throws Exception {
        List<SmCreateOrderSubmitRequest> submitRequestList = Lists.newArrayList();
        //原单是中和农信，且是批增单的 处理
        List<PreservationRecommenderChange> notSurrenderList = context.getPreservation().getChannelReferrerChangeList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE) && Objects.equals(o.getSurrendered(), NOT_SURRENDERED)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notSurrenderList)) {
            doRecommendChangeSubtractOrder(context, notSurrenderList, submitRequestList);
        }

        //todo 原单是中和农信，且单子是退保状态的，暂时不处理
        List<PreservationRecommenderChange> surrenderList = context.getPreservation().getChannelReferrerChangeList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE) && Objects.equals(o.getSurrendered(), SURRENDERED)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(surrenderList)) {
            throw new MSBizNormalException(ExcptEnum.EXIST_SURRENDER);
        }
        return submitRequestList;
    }


    /**
     * 处理渠道推荐人变更(变更后的推荐人为中和农信渠道)
     *
     * @param context
     * @return
     */
    private List<SmCreateOrderSubmitRequest> cvtChangeChannelReferrerZhnx(ChangeChannelReferrerContext context) throws Exception {


        List<SmCreateOrderSubmitRequest> submitRequestList = Lists.newArrayList();
        //处理变更前的数据
        doRecommendChangeBefore(context, submitRequestList);
        //处理变更后的数据
        int count = context.getCount() + submitRequestList.size();
        doRecommendChangeAfter(context, submitRequestList, count);

        return submitRequestList;
    }

    /**
     * @param context           渠道推荐人变更上下文参数
     * @param submitRequestList
     */
    private void doRecommendChangeBefore(ChangeChannelReferrerContext context, List<SmCreateOrderSubmitRequest> submitRequestList) throws Exception {
        //只要过滤变更前是中和农信渠道的记录,且状态为不是退保状态
        List<PreservationRecommenderChange> beforeList = context.getPreservation().getChannelReferrerChangeList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)
                        && Objects.equals(o.getSurrendered(), NOT_SURRENDERED)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(beforeList)) {
            doRecommendChangeSubtractOrder(context, beforeList, submitRequestList);
        }

        //todo 中和农信渠道，状态为退保状态的记录处理
        List<PreservationRecommenderChange> surrenderList = context.getPreservation().getChannelReferrerChangeList().stream()
                .filter(o -> Objects.equals(o.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE) && Objects.equals(o.getSurrendered(), SURRENDERED)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(surrenderList)) {
            throw new MSBizNormalException(ExcptEnum.EXIST_SURRENDER);
        }
    }

    /**
     * 渠道推荐人变更批减单处理
     *
     * @param context
     * @param subList
     * @param submitRequestList
     * @throws Exception
     */
    private void doRecommendChangeSubtractOrder(ChangeChannelReferrerContext context, List<PreservationRecommenderChange> subList, List<SmCreateOrderSubmitRequest> submitRequestList) throws Exception {
        List<String> surrenderIdCardList = subList.stream().map(PreservationRecommenderChange::getInsuredIdCard).collect(Collectors.toList());

        String originalOrderId = context.getOriginalOrderId();
        //List<SmOrderItem> orderItemList = getOriginalItemMap(originalOrderId,surrenderIdCardList);
        Map<String, SmOrderItem> orderItemMap = getOriginalItemMap(originalOrderId, surrenderIdCardList);
        //变更前的被保人数与数据库被保人数不一致
        if (surrenderIdCardList.size() != orderItemMap.size()) {
            log.warn("保全渠道推荐人变更前被保人数{}与数据库被保人数{}不一致,原始订单id为{}，", surrenderIdCardList.size(), orderItemMap.size(), originalOrderId);
            throw new MSBizNormalException(ExcptEnum.BEFORE_DATABASE_NUM_NOT_MATCH.getCode(), ExcptEnum.BEFORE_DATABASE_NUM_NOT_MATCH.getMsg());
        }
        //订单明细对应的订单号（订单明细可能分布在不同订单中）
        List<SmOrder> orderList = listOriginalOrder(orderItemMap);

        int index = context.getCount();
        Date preservationEffectTime = DateUtil.parseDate(context.getPreservation().getPreservationEffectTime());
        for (PreservationRecommenderChange before : subList) {
            if (!Objects.equals(before.getSellChannelCode(), WhaleApiProperties.CHANNEL_CODE)) {
                continue;
            }
            String surrenderIdCard = before.getInsuredIdCard();

            SmOrderItem o = orderItemMap.get(surrenderIdCard);

            if (Objects.nonNull(o)) {
                if (Objects.equals(o.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    throw new MSBizNormalException(ExcptEnum.THE_NEWEST_IS_SURRENDER.getCode(), ExcptEnum.THE_NEWEST_IS_SURRENDER.getMsg());
                }

                String correctPolicyNo = context.getPreservation().getPreservationCode() + ENDTOSEMENT_DECRE + index;
                //批减人对应保单信息
                SmOrder order = orderList.stream().filter(smOrder -> Objects.equals(o.getFhOrderId(), smOrder.getFhOrderId())).findFirst().get();
                //log.info("数据库的推荐人为{},小鲸推送推荐人为{}",order.getRecommendId(),before.getChannelReferrerWno());
                if (!Objects.equals(order.getRecommendId(), before.getChannelReferrerWno())) {
                    String msg = String.format("被保人%s/%s变更前渠道推荐人不一致，数据库的推荐人为%s,小鲸推送推荐人为%s", before.getInsuredName(), before.getInsuredCode(), order.getRecommendId(), before.getChannelReferrerWno());
                    log.warn(msg);
                    throw new MSBizNormalException(ExcptEnum.CHANNE_RECOMMENDER_NOT_MATCH.getCode(), msg);
                }

                SmCreateOrderSubmitRequest submitReq = super.initGroupSubmitRequest(originalOrderId + "_" + index, EnumChannel.XJ.getCode(), null);
                submitReq.setSubChannel(order.getSubChannel());

                //分单处理
                mapSubtractOrderItem(correctPolicyNo, o, context.getPreservation().getPreservationCode(), submitReq);

                //推荐人信息
                setRecommendInfo(submitReq, order.getRecommendJobCode(), order.getRecommendMainJobNumber(), order.getRecommendOrgCode(), order.getCustomerAdminJobCode(), order.getCustomerAdminMainJobNumber(), order.getCustomerAdminOrgCode());

                //订单信息
                mapChannelReferrerOrderInfo(order, o, context.getPreservation().getPreservationEffectTime(), submitReq);
                submitReq.setCommissionId(order.getCommissionId());
                submitReq.setQty(1);
                //产品信息
                setProductInfo(submitReq, order.getRecommendId(), order.getCustomerAdminId(), context.getPlanMap().get(before.getPlanCode()));

                //投保人信息
                submitReq.setProposerInfo(createEndtosementFhPropose(context.getApplicant()));

                //被保人信息
                //setInsuredInfo(submitReq, insured, context.getPreservationCode(), index);
                FhInsuredPerson insuredPerson = new FhInsuredPerson();
                insuredPerson.setPolicyNo(correctPolicyNo);
                insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
                insuredPerson.setIdType(before.getInsuredIdType());
                insuredPerson.setIdNumber(before.getInsuredIdCard());
                insuredPerson.setPersonName(before.getInsuredName());
                insuredPerson.setSurrenderTime(preservationEffectTime);
                insuredPerson.setDuties(mapDutyInfo(correctPolicyNo, before.getProductInfoList()));
                List<FhInsuredPerson> insureds = Lists.newArrayListWithCapacity(1);
                insureds.add(insuredPerson);
                submitReq.setInsuredPerson(insureds);
                mapDutyId(insureds, context.getPlanMap().get(before.getPlanCode()));

                submitRequestList.add(submitReq);
                index = index + 1;
            }
        }
    }

    private void doRecommendChangeAfter(ChangeChannelReferrerContext context, List<SmCreateOrderSubmitRequest> submitRequestList, int count) {
        SmCreateOrderSubmitRequest submitReq = super.initGroupSubmitRequest(context.getOriginalOrderId() + "_" + count, EnumChannel.XJ.getCode(), null);
        submitReq.setSubChannel(context.getSmBaseOrder().getSubChannel());
        PreservationDetail detail = context.getPreservation();
        //如果要处理退保的记录，则需要这里分组，然后吧下面的订单信息创建信息放到未退保记录里
        List<PreservationRecommenderChange> notSurrenderList = detail.getChannelReferrerChangeList().stream().filter(o -> Objects.equals(o.getSurrendered(), NOT_SURRENDERED)).collect(Collectors.toList());
        //订单信息
        FhOrderInfo fhOrderInfo = new FhOrderInfo();
        BigDecimal totalAmount = notSurrenderList.stream().map(PreservationRecommenderChange::getPremium).reduce(BigDecimal.ZERO, (a, b) -> a.add(b));
        fhOrderInfo.setTotalAmount(totalAmount);

        fhOrderInfo.setSubmitTime(DateUtil.format(DateUtil.getNow(), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setStartTime(detail.getPreservationEffectTime());
        fhOrderInfo.setEndTime(DateUtils.format(context.getSmBaseOrder().getEndTime()));
        fhOrderInfo.setUnderWritingAge("");
        fhOrderInfo.setValidPeriod("");
        fhOrderInfo.setAppNo(context.getSmBaseOrder().getAppNo());
        fhOrderInfo.setPolicyNo(context.getSmBaseOrder().getPolicyNo());
        fhOrderInfo.setEndorsementNo(context.getPreservation().getPreservationCode());
        submitReq.setOrderInfo(fhOrderInfo);
        submitReq.setQty(1);
        //保险产品信息
        FhProduct productInfo = new FhProduct();
        //推荐人
        productInfo.setRecommendId(context.getPreservation().getChannelReferrerWno());
        SmPlanVO planVO = context.getPlanMap().get(detail.getChannelReferrerChangeList().get(0).getPlanCode());
        //取第一个计划，必须保证planMap的计划的佣金比例需要一致，且属于同一个产品
        productInfo.setProductId(planVO.getProductId() + "");
        submitReq.setProductInfo(productInfo);
        submitReq.setProductId(planVO.getProductId() + "");
        submitReq.setFhProductId(planVO.getFhProductId());
        submitReq.setPlanId(planVO.getPlanId());

        //投保人信息
        submitReq.setProposerInfo(createEndtosementFhPropose(context.getApplicant()));

        List<SmOrderItem> orderItemList = Lists.newArrayList();
        List<FhInsuredPerson> insureds = Lists.newArrayList();
        String correctPolicyNo = detail.getPreservationCode() + ENDTOSEMENT_INCRE;
        for (PreservationRecommenderChange item : notSurrenderList) {
            SmPlanVO plan = context.getPlanMap().get(item.getPlanCode());
            mapRecommenderChangeToInsuredPerson(submitReq.getFhOrderId(), correctPolicyNo, item, context.getSmBaseOrder().getPolicyNo(), detail.getPreservationCode(), plan, orderItemList, insureds);
        }
        //处理险总id信息
        mapDutyId(insureds, planVO);

        submitReq.setOrderItemList(orderItemList);
        submitReq.setInsuredPerson(insureds);
        submitRequestList.add(submitReq);
    }


    private void mapRecommenderChangeToInsuredPerson(String fhOrderId, String correctPolicyNo, PreservationRecommenderChange item,
                                                     String thPolicyNo,
                                                     String preservationCode, SmPlanVO plan,
                                                     List<SmOrderItem> orderItemList,
                                                     List<FhInsuredPerson> insuredPersonList
    ) {

        //被保人
        FhInsuredPerson insuredPerson = WhaleConvert.CNT.cvtRecommendChangeReferrerInsured(item);
        insuredPerson.setPolicyNo(correctPolicyNo);
        insuredPerson.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
        //被保人险种信息
        insuredPerson.setProduct(mapAddInsuredPersonProductInfo(item.getPremium(), item.sumAmount(), plan));
        //设置责任信息
        insuredPerson.setDuties(mapDutyInfo(correctPolicyNo, item.getProductInfoList()));
        insuredPersonList.add(insuredPerson);

        //item表
        SmOrderItem orderItem = mapAddSmOrderItem(fhOrderId, thPolicyNo, preservationCode, item.getInsuredCode(), correctPolicyNo, insuredPerson, plan, item.getPremium());
        orderItemList.add(orderItem);
    }

    /**
     * @param preservationEffectTime
     * @param order
     * @param o
     * @param submitReq
     */
    private void mapChannelReferrerOrderInfo(SmOrder order, SmOrderItem o, String preservationEffectTime, SmCreateOrderSubmitRequest submitReq) {
        //订单信息
        FhOrderInfo fhOrderInfo = new FhOrderInfo();
        fhOrderInfo.setTotalAmount(o.getTotalAmount());
        fhOrderInfo.setSubmitTime(DateUtil.format(DateUtil.getNow(), DateUtil.CN_LONG_FORMAT));
        fhOrderInfo.setStartTime(LocalDateUtil.commonFormat(order.getStartTime()));


        fhOrderInfo.setEndTime(preservationEffectTime);
        fhOrderInfo.setAppNo(order.getAppNo());

        submitReq.setOrderInfo(fhOrderInfo);
        submitReq.setAppNo(order.getAppNo());
        submitReq.setQty(1);

    }

    private void mapSubtractOrderItem(String policyNo, SmOrderItem o, String preservationCode, SmCreateOrderSubmitRequest submitReq) {

        SmOrderItem decrItem = new SmOrderItem();
        BeanUtils.copyProperties(o, decrItem);
        decrItem.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        decrItem.setThEndorsementNo(preservationCode);
        decrItem.setFhOrderId(submitReq.getFhOrderId());
        decrItem.setPolicyNo(policyNo);
        decrItem.setType(1);
        List<SmOrderItem> itemList = new ArrayList<>();
        itemList.add(decrItem);
        submitReq.setOrderItemList(itemList);
    }

    /**
     * 正常的批减流程
     * @param policyNo 农保保单编号
     * @param o 承保的被保人信息
     * @param preservationCode 批改单号
     * @param premium 退保的保费
     * @param submitReq 数据包
     */
    private void mapSubtractOrderItem(String policyNo, SmOrderItem o, String preservationCode, BigDecimal premium,SmCreateOrderSubmitRequest submitReq) {

        SmOrderItem decrItem = new SmOrderItem();
        BeanUtils.copyProperties(o, decrItem);
        decrItem.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        decrItem.setThEndorsementNo(preservationCode);
        decrItem.setFhOrderId(submitReq.getFhOrderId());
        decrItem.setPolicyNo(policyNo);
        decrItem.setType(1);

        premium=premium.abs();
        decrItem.setTotalAmount(premium);
        decrItem.setUnitPrice(premium);

        BigDecimal applyPremium = o.getTotalAmount();
        BigDecimal s = applyPremium.subtract(premium);
        decrItem.setEndorsementAmount(s);

        List<SmOrderItem> itemList = new ArrayList<>();
        itemList.add(decrItem);
        submitReq.setOrderItemList(itemList);
    }

    private void mapSurrenderOrderItem(String policyNo,String endorsementNo,PreserveSurrenderInsuredVo surrender, SmOrderItem orderItem,  SmCreateOrderSubmitRequest submitReq) {
        SmOrderItem decrItem = new SmOrderItem();
        BeanUtils.copyProperties(orderItem, decrItem);
        decrItem.setType(1);
        decrItem.setAppStatus(SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        decrItem.setThEndorsementNo(endorsementNo);
        decrItem.setTotalAmount(surrender.getInsuredSurrenderPremium());
        decrItem.setUnitPrice(surrender.getInsuredSurrenderPremium());

        BigDecimal before = orderItem.getTotalAmount();
        BigDecimal after = surrender.getInsuredSurrenderPremium();
        if(before!=null && after!=null){
            BigDecimal r = before.subtract(after);
            decrItem.setEndorsementAmount(r);
        }

        decrItem.setFhOrderId(submitReq.getFhOrderId());
        decrItem.setPolicyNo(policyNo);
        List<SmOrderItem> itemList = Arrays.asList(decrItem);
        submitReq.setOrderItemList(itemList);
    }



    private List<SmOrder> listOriginalOrder(List<String> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.emptyList();
        } else {
            return orderMapper.listSmOrderByOrderIds(orderIdList);
        }
    }

    private List<SmOrder> listOriginalOrder(Map<String, SmOrderItem> orderItemMap) {
        List<String> fhOrderIds = orderItemMap.values().stream().map(SmOrderItem::getFhOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fhOrderIds)) {
            return Collections.emptyList();
        } else {
            return orderMapper.listSmOrderByOrderIds(fhOrderIds);
        }
    }

    /**
     * 批减人员的投保时的订单明细,需要按id降序排列，存在一个人多次投保退保的可能。
     *
     * @param originalOrderId
     * @param surrenderIdCardList
     * @return
     */
    private Collection<SmOrderItem> listOriginalItem(String originalOrderId, List<String> surrenderIdCardList) {
        List<SmOrderItem> orderItemList = listPolicyItem(originalOrderId, surrenderIdCardList);
        if (CollectionUtils.isEmpty(orderItemList)) {
            return Collections.emptyList();
        }
        Map<String, SmOrderItem> activeMap = new HashMap<>();
        for (SmOrderItem orderItem : orderItemList) {
            String idCard = orderItem.getIdNumber();
            if (StringUtils.isBlank(idCard)) {
                continue;
            }
            idCard = idCard.toUpperCase();
            String appStatus = orderItem.getAppStatus();
            if (Objects.equals("1", appStatus)) {
                activeMap.put(idCard, orderItem);
            } else if (Objects.equals("4", appStatus)) {
                activeMap.remove(idCard);
            }
        }
        return activeMap.values();
    }

    private Map<String, SmOrderItem> getOriginalItemMap(String originalOrderId, List<String> surrenderIdCardList) {
        Collection<SmOrderItem> orderItemList = listOriginalItem(originalOrderId, surrenderIdCardList);
        Map<String, SmOrderItem> map = Maps.newHashMap();
        for (SmOrderItem item : orderItemList) {
            SmOrderItem old = map.get(item.getIdNumber());
            if (Objects.isNull(old)) {
                map.put(item.getIdNumber(), item);
            } else {
                if (old.getId() < item.getId()) {
                    map.put(item.getIdNumber(), item);
                }
            }
        }
        return map;
    }


    /**************************************泰康团险*Group*****************************************/
    @Autowired
    protected WhaleApiProperties apiProperties;

    @Autowired
    protected WhaleMessageBuilder messageBuilder;

    @Override
    public GroupQuoteResponse quotePrice4Group(GroupUnderwriting req) {

        GroupUnderwritingInput data = messageBuilder.buildQuoteRequestV1(apiProperties, req);

        WhaleResp<GroupQuoteOutput> resp = apiService.groupQuote(data);
        if (!resp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        GroupQuoteOutput body = resp.getData();

        GroupQuoteResponse rtn = new GroupQuoteResponse();
        BigDecimal premium = body.getPremium();
        rtn.setTotalPremium(premium.toString());
        return rtn;
    }

    @Autowired
    private TxServiceManager txServiceManager;

    /**
     * 泰康核保(创建团单)流程
     * 固定见费出单
     *
     * @return
     */
    @Override
    public GroupQuoteResponse underwriting(GroupUnderwriting data) {

        Integer productId = data.getProductId();
        SmProductDetailVO product = productService.getProductById(productId);
        if (product == null) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_NOT_ONLINE_201001.getCode(), ExcptEnum.PRODUCT_NOT_ONLINE_201001.getMsg());
        }

        if (StringUtils.isBlank(data.getOrderOutType())) {
            data.setOrderOutType(EnumOrderOutType.SEE_FEE.getCode());
        }
        /**
         * 1.重新试算保费
         */
        GroupUnderwritingInput quoteReq = messageBuilder.buildQuoteRequestV1(apiProperties, data);

        WhaleResp<GroupQuoteOutput> quoteResp = apiService.groupQuote(quoteReq);
        log.info("保费试算结果：{}", JSON.toJSONString(quoteResp));
        if (!quoteResp.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), quoteResp.errorMsg());
        }

        /**
         * 2.企业注册
         */
        RegisterApplicantInput applicantRequest = messageBuilder.buildApplicant(apiProperties, data);
        WhaleResp<RegisterApplicantOutput> applicantResponse = apiService.registApplicant(applicantRequest);
        if (!applicantResponse.isSuccess()) {
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), applicantResponse.errorMsg());
        }
        String orgId = applicantResponse.getData().getOrgId();
        /**
         * 2.记录原始数据
         */
        GroupApplicant applicant = data.getApplicant();
        applicant.addField("orgId", orgId);
        String orderId = txServiceManager.excute(() -> super.saveGroupOrder(product, data));
        log.info("开始调用渠道核保接口-{}", orderId);
        /**
         * 3.调用保司接口核保
         */
        GroupQuoteOutput quoteResBody = quoteResp.getData();
        List<WhalePlan> planPremiumList = quoteResBody.getPlanList();
        Map<String, BigDecimal> premiumMap = LambdaUtils.safeToMap(planPremiumList, WhalePlan::getPlanCode, WhalePlan::getPremium);

        Function<String, BigDecimal> premiumFunc = premiumMap::get;
        GroupUnderwritingInput input = WhaleMessageBuilder.buildCreatePolicy(orgId, apiProperties, data, quoteReq, quoteResBody);
        GroupUnderwritingOutput output = createPolicy(input);
        log.info("渠道核保结果：{}", JSON.toJSONString(output));
        /**
         * 4.状态更新
         */
        txServiceManager.excute(() -> afterApply(orderId, premiumFunc, data, input, output));
        /**
         * 5.回包数据组装
         */
        GroupQuoteResponse response = new GroupQuoteResponse();
        response.setOrderId(orderId);
        response.setProposalNo(output.getProposalNo());
        response.setEffectiveDate(data.getStartTime());
        response.setExpireDate(data.getEndTime());
        return response;
    }

    private GroupUnderwritingOutput createPolicy(GroupUnderwritingInput input) {
        WhaleResp<GroupUnderwritingOutput> response = apiService.groupUnderwriting(input);
        if (!response.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), response.errorMsg());
        }
        return response.getData();
    }

    /**
     * 1.保存投保数据[sm_order_item]
     * 2.更新订单状态
     */
    public void afterApply(String orderId,
                           Function<String, BigDecimal> premiumFunc,
                           GroupUnderwriting data,
                           GroupUnderwritingInput input,
                           GroupUnderwritingOutput output) {

        List<GroupInsured> insuredList = data.getInsuredList();
        Integer productId = data.getProductId();
        Integer planId = data.getProduct().getPlanId();
        List<SmOrderItem> orderItems = new ArrayList<>();
        for (GroupInsured entry : insuredList) {
            SmOrderItem item = new SmOrderItem();
            item.setFhOrderId(orderId);
            item.setActiveBranchId(entry.getActiveBranchId());
            item.setAppStatus(SmConstants.ORDER_STATUS_TO_PAY);

            String planCode = WhaleMessageBuilder.genVirtualPlanCode(entry.getOccupationGroup());

            item.setIdNumber(entry.getIdNumber());
            item.setIdType(entry.getIdType());
            item.setType(0);
            item.setProductId(productId);
            item.setPlanId(planId);
            item.setPlanCode(planCode);
            item.setQty(1);
            item.setAppStatus(SmConstants.POLICY_STATUS_BLANK);
            BigDecimal unitPremium = premiumFunc.apply(planCode);
            item.setTotalAmount(unitPremium);
            item.setUnitPrice(unitPremium);
            orderItems.add(item);
        }
        int r0 = orderMapper.clearTempItem(orderId);
        int r1 = orderItemMapper.insertList(orderItems);
        log.info("被保人分单数据写入完成:{},{},{}", orderId, r0, r1);
        String appNo = output.getProposalNo();
        if (appNo != null) {
            OrderDTO param = new OrderDTO();
            param.setAppNo(appNo);
            param.setPayStatus(SmConstants.ORDER_STATUS_TO_PAY);
            param.setOrderState(SmConstants.ORDER_STATUS_TO_PAY);
            param.setPayUrl(output.getPayUrl());
            param.setApplyTime(new Date());
            param.addField("payNotifyUrl", output.getPaymentNoticeUrl());
            int rtn = orderMapper.updateOrder(orderId, param);
            log.info("[{}]更新投保订单状态完成,[{}]", orderId, rtn);
        }
    }

    @Override
    public GroupEndorResponse endorUnderwriting(GroupEndorsement req) {
        return endorCalPremium(req);
    }

    /**
     * 此方法不应该再此处做拆分
     * 理论上保险中台对外部提供的应该就是一个统一的批改试算保费的接口
     * 不应该有上游再细化成批增&批减的试算
     * 后续待优化......
     *
     * @param req
     * @return
     */
    @Override
    public GroupEndorResponse endorCalPremium(GroupEndorsement req) {
        List<GroupInsured> insureds = req.getInsuredList();
        Map<Integer, List<GroupInsured>> insuredMap = LambdaUtils.groupBy(insureds, GroupInsured::getOpType);
        List<GroupInsured> addList = insuredMap.get(1);
        List<GroupInsured> deductionList = insuredMap.get(2);
        int addSize = addList != null ? addList.size() : 0;
        int deductionSize = deductionList != null ? deductionList.size() : 0;
        if (addSize > 0 && deductionSize > 0) {
            throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "暂不支持同时批增批减");
        }
        if (addSize > 0) {
            return addCalPremium(req);
        }
        if (deductionSize > 0) {
            return dedutionCalPremium(req);
        }
        throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人列表不能为空");
    }

    private GroupEndorResponse addCalPremium(GroupEndorsement req) {
        GroupUnderwritingInput input = messageBuilder.buildAddQuoteRequest(apiProperties, req);
        WhaleResp<GroupQuoteOutput> quoteResponse = apiService.groupAddQuote(input);
        if (!quoteResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR);
        }
        GroupEndorResponse response = new GroupEndorResponse();
        response.setPolicyNo(req.getPolicyNo());

        GroupQuoteOutput quoteBody = quoteResponse.getData();
        BigDecimal premium = quoteBody.getPremium();
        response.setTotalPremium(premium);
        return response;
    }

    private GroupEndorResponse dedutionCalPremium(GroupEndorsement req) {
        GroupDeductionInput input = messageBuilder.buildDedutionQuoteRequest(apiProperties, req);
        WhaleResp<GroupDeductionOutput> response = apiService.groupDedutionQuote(input);
        if (!response.isSuccess()) {
            throw new MSException("-1", "渠道服务器异常");
        }
        GroupDeductionOutput data = response.getData();
        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());
        BigDecimal premium = new BigDecimal(0);
        for (CorrectPersonal entry : data.getInsuredList()) {
            BigDecimal refundPremium = entry.getTotalPremium();
            premium = premium.add(refundPremium);
        }
        premium = premium.multiply(new BigDecimal(-1));
        rtn.setTotalPremium(premium);
        return rtn;
    }

    @Override
    public GroupEndorResponse endorCommit(GroupEndorsement req) {
        boolean mutex = correctMutex(EnumChannel.TK_PAY.getCode(), req.getPolicyNo());
        if (!mutex) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前保单存在未完成的批单，无法提交新的批单");
        }

        List<GroupInsured> insureds = req.getInsuredList();
        Map<Integer, List<GroupInsured>> insuredMap = LambdaUtils.groupBy(insureds, GroupInsured::getOpType);
        List<GroupInsured> addList = insuredMap.get(1);
        List<GroupInsured> deductionList = insuredMap.get(2);
        int addSize = addList != null ? addList.size() : 0;
        int deductionSize = deductionList != null ? deductionList.size() : 0;
        if (addSize > 0 && deductionSize > 0) {
            throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "暂不支持同时批增批减");
        }
        if (addSize > 0) {
            return addCommit(req);
        }
        if (deductionSize > 0) {
            return dedutionCommit(req);
        }
        throw new MSException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人列表不能为空");
    }

    /**
     * 团险-批增
     *
     * @param req
     * @return
     */
    private GroupEndorResponse addCommit(GroupEndorsement req) {
        String endorId = IdGenerator.getNextNo(req.getChannel());
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(req.getOrderId());
        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSException("-1", "订单不存在");
        }
        req.setEndorId(endorId);
        /**
         * 1.保司试算保费
         */
        GroupUnderwritingInput input = messageBuilder.buildAddQuoteRequest(apiProperties, req);
        WhaleResp<GroupQuoteOutput> quoteResponse = apiService.groupAddQuote(input);
        if (!quoteResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), quoteResponse.errorMsg());
        }

        GroupQuoteOutput quoteResBody = quoteResponse.getData();

        List<WhalePlan> personalPolicyList = quoteResBody.getPlanList();
        final Map<String, BigDecimal> premiumMap = LambdaUtils.safeToMap(personalPolicyList, WhalePlan::getPlanCode, WhalePlan::getPremium);
        ZaGroupEndorsementRes res = buildMessageForAdd(req, premiumMap::get);

        GroupRuleAdaptor.addSubtractCheck(res.getResult());
        /**
         * 2.数据存档
         */
        Endor payment = new Endor();
        payment.setRawOrderId(req.getOrderId());
        payment.setChannel(EnumChannel.TK_PAY.getCode());
        payment.setEndorsementNo(IdGenerator.getNextNo(EnumChannel.TK_PAY.getCode()));
        payment.setPolicyNo(req.getPolicyNo());
        payment.setOperaor(HttpRequestUtil.getUserId());
        payment.setOrderId(endorId);
        payment.setStatus(EnumEndor.TO_COMMIT.getCode());
        payment.setOrderType(order.getOrderOutType());
        payment.setEffectiveTime(req.chooseEffectiveTime(1));
        payment.setOpType(1);
        payment.setRefund(req.getRefundInfo() != null ? JSON.toJSONString(req.getRefundInfo()) : null);
        payment.setCreateTime(new Date());
        payment.setPayWay(EnumChonghoPay.mapper(req.getPayType()));
        int notifyId = super.preDumpCorrectMsg(payment, req);

        /**
         * 3.提交批改
         * WhaleApiProperties properties,
         * GroupUnderwritingInput input,
         * GroupQuoteOutput output,
         * GroupEndorsement data,
         */
        GroupUnderwritingInput createInput = messageBuilder.buildAddCommitRequest(apiProperties, input, quoteResBody, req);
        WhaleResp<GroupUnderwritingOutput> commitResponse = apiService.groupAddCommit(createInput);

        if (commitResponse == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!commitResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), commitResponse.errorMsg());
        }
        BigDecimal premium = quoteResBody.getPremium();
        GroupUnderwritingOutput createPolicyResponse = commitResponse.getData();

        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());
        rtn.setTotalPremium(premium);
        rtn.setEndorStatus(0);
        rtn.setOrderId(req.getEndorId());
        rtn.setEndorId(req.getEndorId());

        GroupNotify.StatusEnum notifyCode = GroupNotify.StatusEnum.UN_INIT;
        payment.setProposalNo(createPolicyResponse.getProposalNo());
        payment.setAmount(premium);
        payment.setPayUrl(createPolicyResponse.getPayUrl());
        payment.setStatus(EnumEndor.COMMITED.getCode());
        txServiceManager.excute(() -> super.afterCorrectMsg(notifyId, notifyCode, null, payment, res));
        return rtn;
    }

    private ZaGroupEndorsementRes buildMessageForAdd(GroupEndorsement req, Function<String, BigDecimal> getPremium) {

        ZaGroupEndorsementRes res = new ZaGroupEndorsementRes();
        res.setCode("0");
        ZaGroupEndorsementInfo data = new ZaGroupEndorsementInfo();
        data.setValidateTime(req.getEffectiveTime());
        List<ZaGroupInsuredInfo> insureds = new ArrayList<>();
        List<GroupInsured> groupInsureds = req.getInsuredList();
        for (GroupInsured entry : groupInsureds) {
            ZaGroupInsuredInfo vo = new ZaGroupInsuredInfo();
            vo.setName(entry.getPersonName());
            vo.setGender(entry.getPersonGender());
            vo.setActiveBranchId(entry.getActiveBranchId());
            vo.setBranchId(entry.getBranchId());
            String idNumber = entry.getIdNumber();
            vo.setCertNo(idNumber);
            vo.setCertType(entry.getIdType());
            vo.setHandleType(String.valueOf(entry.getOpType()));

            String planNo = WhaleMessageBuilder.genVirtualPlanCode(entry.getOccupationGroup());

            BigDecimal premium = getPremium.apply(planNo);
            vo.setTotalPremium(premium == null ? new BigDecimal(0) : premium);
            vo.setIndividualStatus(entry.convertIndividualStatus());

            vo.setOccupationCode(entry.getOccupationCode());
            vo.setOccupationGroup(entry.getOccupationGroup());
            vo.setIsSecurity(entry.getIsSecurity());
            if (entry.getOpType() == 2) {
                vo.setTerminateTime(req.getReductionEffectiveTime());
            }
            if (StringUtils.isBlank(entry.getBirthday())) {
                vo.setBirthday(IdCardUtils.getBirthday(entry.getIdNumber(), "yyyy-MM-dd"));
            } else {
                vo.setBirthday(entry.getBirthday());
            }
            insureds.add(vo);
        }
        data.setValidateTime(req.getAdditionEffectiveTime());
        data.setReductionEffectiveTime(req.getReductionEffectiveTime());
        data.setInsuredList(insureds);
        res.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        res.setResult(data);
        return res;
    }

    private GroupEndorResponse dedutionCommit(GroupEndorsement req) {
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(req.getOrderId());
        OrderProductDTO orderProduct = orderMapper.queryOrderProduct(req.getOrderId());
        if (orderProduct == null) {
            throw new MSException("-1", "订单对应的产品信息不存在");
        }
        /**
         * 生成此次批改对应的订单id
         */
        String endorId = IdGenerator.getNextNo(req.getChannel());
        req.setEndorId(endorId);

        GroupDeductionInput input = messageBuilder.buildDedutionQuoteRequest(apiProperties, req);
        WhaleResp<GroupDeductionOutput> quoteResp = apiService.groupDedutionQuote(input);

        if (!quoteResp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), quoteResp.errorMsg());
        }
        GroupDeductionOutput quoteBody = quoteResp.getData();
        GroupDeductionInput commitInput = messageBuilder.buildDelCommitRequest(input, quoteBody, req);

        List<CorrectPersonal> personalPolicyList = quoteBody.getInsuredList();
        final Map<String, BigDecimal> premiumMap = LambdaUtils.safeToMap(personalPolicyList, entry -> entry.getInsuredCertiNo().toLowerCase(), CorrectPersonal::getTotalPremium);
        ZaGroupEndorsementRes res = buildMessageByDecrease(req, premiumMap::get);
        GroupRuleAdaptor.addSubtractCheck(res.getResult());
        /**
         * 预存信息
         */
        Endor payment = new Endor();
        payment.setRawOrderId(req.getOrderId());
        payment.setChannel(req.getChannel());
        payment.setPolicyNo(req.getPolicyNo());
        payment.setOperaor(HttpRequestUtil.getUserId());
        payment.setOrderId(req.getEndorId());
        payment.setStatus(EnumEndor.TO_COMMIT.getCode());
        payment.setOrderType(order.getOrderOutType());
        payment.setEffectiveTime(req.chooseEffectiveTime(2));
        payment.setOpType(2);
        payment.setRefund(req.getRefundInfo() != null ? JSON.toJSONString(req.getRefundInfo()) : null);
        payment.setCreateTime(new Date());
        payment.setAmount(quoteBody.summaryPremium(2));
        int notifyId = super.preDumpCorrectMsg(payment, req);

        /**
         * 提交批改
         */
        WhaleResp<GroupDeductionOutput> commitResponse = apiService.groupDedutionCommit(commitInput);

        GroupNotify.StatusEnum notifyCode = GroupNotify.StatusEnum.UN_INIT;
        if (commitResponse == null) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), ExcptEnum.ZA_BIZ_ERROR.getMsg());
        }
        if (!commitResponse.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), commitResponse.errorMsg());
        }
        BigDecimal premium = quoteBody.summaryPremium(2);

        GroupDeductionOutput respBody = commitResponse.getData();

        GroupEndorResponse rtn = new GroupEndorResponse();
        rtn.setPolicyNo(req.getPolicyNo());

        rtn.setEndorStatus(0);
        rtn.setOrderId(payment.getOrderId());
        rtn.setEndorId(payment.getOrderId());

        payment.setEndorsementNo(respBody.getApplyEndorsementNo());
        payment.setApplyEndorsementNo(respBody.getApplyEndorsementNo());
        payment.setStatus(EnumEndor.DOING.getCode());
        txServiceManager.excute(() -> super.afterCorrectMsg(notifyId, notifyCode, null, payment, res));
        return rtn;
    }

    /**
     * 构造一个批改消息
     *
     * @param req
     * @param getPremium
     * @return
     */
    private ZaGroupEndorsementRes buildMessageByDecrease(GroupEndorsement req, Function<String, BigDecimal> getPremium) {

        ZaGroupEndorsementRes res = new ZaGroupEndorsementRes();
        res.setCode("0");
        ZaGroupEndorsementInfo data = new ZaGroupEndorsementInfo();
        data.setValidateTime(req.getEffectiveTime());
        List<ZaGroupInsuredInfo> insureds = new ArrayList<>();
        List<GroupInsured> groupInsureds = req.getInsuredList();
        for (GroupInsured entry : groupInsureds) {
            ZaGroupInsuredInfo vo = new ZaGroupInsuredInfo();
            vo.setName(entry.getPersonName());
            vo.setGender(entry.getPersonGender());
            vo.setActiveBranchId(entry.getActiveBranchId());
            vo.setBranchId(entry.getBranchId());
            String idNumber = entry.getIdNumber();
            vo.setCertNo(idNumber);
            vo.setCertType(entry.getIdType());
            vo.setHandleType(String.valueOf(entry.getOpType()));


            BigDecimal premium = getPremium.apply(idNumber.toLowerCase());
            vo.setTotalPremium(premium == null ? new BigDecimal(0) : premium);
            vo.setIndividualStatus(entry.convertIndividualStatus());

            vo.setOccupationCode(entry.getOccupationCode());
            vo.setOccupationGroup(entry.getOccupationGroup());
            vo.setIsSecurity(entry.getIsSecurity());
            if (entry.getOpType() == 2) {
                vo.setTerminateTime(req.getReductionEffectiveTime());
            }
            if (StringUtils.isBlank(entry.getBirthday())) {
                vo.setBirthday(IdCardUtils.getBirthday(entry.getIdNumber(), "yyyy-MM-dd"));
            } else {
                vo.setBirthday(entry.getBirthday());
            }
            insureds.add(vo);
        }
        data.setValidateTime(req.getAdditionEffectiveTime());
        data.setReductionEffectiveTime(req.getReductionEffectiveTime());
        data.setInsuredList(insureds);
        res.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        res.setResult(data);
        return res;
    }

    @Override
    public InvoiceResponse openInvoice(InvoiceVo req) {
        log.info("[{}]-开始请求众安统一开票流程，Items:[{}]", req.getPolicyNo(), req.getPolicyItemList());

        GroupInvoiceInput input = WhaleMessageBuilder.buildInvoiceRequest(apiProperties, req);
        WhaleResp<GroupInvoiceOutput> resp = apiService.mergeInvoice(input);
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), resp.errorMsg());
        }
        GroupInvoiceOutput data = resp.getData();
        InvoiceResponse rtn = new InvoiceResponse();
        rtn.setInvoiceNo(data.getInvoiceNumber());
        rtn.setInvoiceCode(data.getInvoiceCode());
        rtn.setInvoiceUrl(data.getInvoiceUrl());
        return rtn;
    }

    @Override
    public String submitOfflinePay(OfflinePayDTO req) {
        log.info("开始提交线下支付资料-{}，Data:[{}]", req.getOrderId(), req);

        FastOrderDTO order = queryFastOrder(req);
        if (order == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT);
        }
        GroupNotifyPaymentInput request = WhaleMessageBuilder.cvtTransferAccountBean(order.getProposalNo(),
                "1",
                apiProperties,
                req.getPayInfo());
        WhaleResp<GroupNotifyPayOutput> resp = apiService.notifyPayinfo(request);
        log.warn("线下转账资料提交完成：{},{}", req.getOrderId(), resp);
        if (!resp.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), resp.errorMsg());
        }
        int rtn = submitSuccess(order.getEndorId(), req);

        log.info("{},线下支付资料提交成功，状态更新结果,{}", req.getOrderId(), rtn);
        return order.getOrderId();
    }

    /**
     * 线下转账资料提交成功后，状态更新
     *
     * @param endorId
     * @param req
     * @return
     */
    public int submitSuccess(String endorId, OfflinePayDTO req) {
        log.info("{},线下支付资料提交成功，更新订单状态为支付中", req.getOrderId());
        if (req.isCorrect()) {
            return endorMapper.updateStatusById(endorId, SmConstants.PAY_STATUS_DOING);
        }
        /**
         * 新契约状态更新
         */
        OrderDTO updateOrder = new OrderDTO();
        updateOrder.setFhOrderId(req.getOrderId());
        updateOrder.setPayStatus(SmConstants.PAY_STATUS_DOING);
        return orderMapper.updateOrder(req.getOrderId(), updateOrder);
    }

    private FastOrderDTO queryFastOrder(OfflinePayDTO req) {
        /**
         * 批改流程
         */
        if (req.isCorrect()) {
            if (StringUtils.isNotBlank(req.getEndorsementNo())) {
                return orderMapper.getFastOrderByEndorsementNo(req.getEndorsementNo());
            }
            if (StringUtils.isNotBlank(req.getEndorId())) {
                return orderMapper.getFastOrderByEndorId(req.getEndorId());
            }
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT);
        }
        /**
         * 新契约订单
         */
        return orderMapper.getFastOrderByOrderId(req.getOrderId());
    }

    @Autowired
    private EndorMapper endorMapper;

    @Override
    public MemberChange memberChange(GroupEndorsement data) {
        boolean mutex = correctMutex(EnumChannel.TK_PAY.getCode(), data.getPolicyNo());
        if (!mutex) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前保单存在未完成的批单，无法提交新的批单");
        }
        String orderId = data.getOrderId();
        String orgId = queryOrgId(orderId);

        OrderDetailQuery query = new OrderDetailQuery();
        query.setPolicyNo(data.getPolicyNo());
        List<InsuredListDTO> insuredList = policyMapper.queryInsureds(query);

        String endorId = IdGenerator.getNextNo(EnumChannel.TK_PAY.getCode());
        data.setEndorId(endorId);
        checkMemberChange(data, insuredList);

        data.fillBirthDay();
        /**
         * 0.校验数据
         */
        ZaGroupEndorsementRes correctBody = buildMessage4MemberChange(insuredList, data);
        GroupRuleAdaptor.addSubtractCheck(correctBody.getResult());
        /**
         * 1.预存数据
         */
        Endor endor = buildCorrectEntity(EnumGroupCorrectType.REPLACE_MEMBER, data);
        SmOrderGroupNotify notify = txServiceManager.excute(() -> super.preDumpReplaceMemberMsg(endor, data));

        /**
         * 2.请求保司流程
         */
        GroupMemberChangeInput request = messageBuilder.buildMemberChangeRequest(orgId, apiProperties, data);
        WhaleResp<GroupMemberChangeOutput> response = apiService.memberChange(request);
        if (!response.isSuccess()) {
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), response.errorMsg());
        }

        GroupMemberChangeOutput output = response.getData();
        String applyEndorsementNo = output.getApplyEndorsementNo();
        endor.setApplyEndorsementNo(applyEndorsementNo);
        endor.setStatus(EnumEndor.DOING.getCode());


        boolean correctFlag = txServiceManager.excute(() -> memberChangeFinish(data, endor, notify, correctBody));

        log.warn("[泰康]-替换人员-流程结束，数据更新结果：{}", correctFlag);
        MemberChange vo = new MemberChange();
        vo.setPolicyNo(data.getPolicyNo());
        vo.setOrderId(data.getOrderId());
        return vo;
    }

    /**
     * 人员替换完成-数据更新结果
     *
     * @param data
     * @param endor
     * @param notify
     * @return
     */
    public boolean memberChangeFinish(GroupEndorsement data, Endor endor, SmOrderGroupNotify notify, ZaGroupEndorsementRes correctBody) {
        SmOrderGroupNotify update = new SmOrderGroupNotify();
        update.setId(notify.getId());
        update.setStatus(GroupNotify.StatusEnum.UNDO.getCode());
        int notifyRet = smOrderGroupNotifyMapper.fullUpdateNotify(update);
        int endorRet = endorMapper.updateByPrimaryKeySelective(endor);

        SmOrderGroupNotifyMsg msg = new SmOrderGroupNotifyMsg();
        msg.setNotifyId(notify.getId());
        msg.setNotifyContent(JSON.toJSONString(correctBody));
        int notifyMsgRet = smOrderGroupNotifyMsgMapper.insertSelective(msg);
        log.warn("泰康-替换人员流程完成，数据更新结果:{},{},{}", notifyMsgRet, endorRet, notifyRet);
        if (notifyRet == 0 || endorRet == 0 || notifyMsgRet == 0) {
            throw new BizException("-1", "记录更新失败");
        }
        return true;
    }

    private ZaGroupEndorsementRes buildMessage4MemberChange(List<InsuredListDTO> insuredList, GroupEndorsement data) {

        ZaGroupEndorsementRes res = new ZaGroupEndorsementRes();
        res.setCode("0");
        ZaGroupEndorsementInfo correctBody = new ZaGroupEndorsementInfo();
        correctBody.setValidateTime(data.getEffectiveTime());
        correctBody.setEndorsementType(EnumGroupCorrectType.REPLACE_MEMBER.getCode());

        List<ZaGroupInsuredInfo> insureds = new ArrayList<>();
        List<GroupInsured> groupInsureds = data.getInsuredList();

        Map<String, InsuredListDTO> insuredMap = LambdaUtils.safeToMap(insuredList, InsuredListDTO::getBranchId);
        for (GroupInsured entry : groupInsureds) {
            InsuredListDTO entity = insuredMap.get(entry.getBranchId());
            if (entity == null) {
                continue;
            }
            ZaGroupInsuredInfo beforeMember = buildReplaceMember(entity);
            beforeMember.setIndividualStatus(ZaGroupPolicyStatusEnum.TERMINATED.getCode());
            beforeMember.setHandleType(String.valueOf(entry.getOpType()));

            ZaGroupInsuredInfo afterMember = buildReplaceMember(entity);
            afterMember.setIndividualStatus(ZaGroupPolicyStatusEnum.INFORCE.getCode());
            afterMember.setName(entry.getPersonName());
            afterMember.setCertType(entry.getIdType());
            afterMember.setCertNo(entry.getIdNumber());
            afterMember.setOccupationCode(entry.getOccupationCode());
            afterMember.setOccupationGroup(entry.getOccupationGroup());
            afterMember.setBirthday(entry.getBirthday());
            afterMember.setHandleType(String.valueOf(entry.getOpType()));

            insureds.add(beforeMember);
            insureds.add(afterMember);
        }
        correctBody.setValidateTime(data.getEffectiveTime());
        correctBody.setInsuredList(insureds);
        res.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        res.setResult(correctBody);
        return res;
    }

    public ZaGroupInsuredInfo buildReplaceMember(InsuredListDTO data) {
        ZaGroupInsuredInfo vo = new ZaGroupInsuredInfo();
        vo.setOrderId(data.getFhOrderId());
        vo.setName(data.getPersonName());
        vo.setGender(data.getPersonGender());
        vo.setActiveBranchId(data.getActiveBranchId());
        vo.setBranchId(data.getBranchId());

        String idNumber = data.getIdNumber();
        vo.setCertNo(idNumber);
        vo.setCertType(data.getIdType());
        vo.setHandleType(String.valueOf(data.getOpType()));

        vo.setOccupationCode(data.getOccupationCode());
        vo.setOccupationGroup(data.getOccupationGroup());
        vo.setIsSecurity(data.getIsSecurity());
        vo.setBirthday(data.getBirthday());
        vo.setEpolicyUrl(data.getDownloadURL());
        vo.setPlanCode(data.getPlanCode());

        vo.setTotalPremium(data.getTotalAmount());
        return vo;
    }

    @Autowired
    private PolicyMapper policyMapper;

    private void checkMemberChange(GroupEndorsement data, List<InsuredListDTO> dbEntityList) {

        if (CollectionUtils.isEmpty(dbEntityList)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("该保单的被保人列表为空，请检查参数:{}", data.getPolicyNo()));
        }
        ProductDTO productDTO = policyMapper.getProductDetailById(data.getProductId());
        if (productDTO == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "产品信息不存在或产品已下线");
        }

        Map<String, InsuredListDTO> entityMap = LambdaUtils.safeToMap(dbEntityList, entry -> entry.getIdNumber().toLowerCase());

        Set<String> businessLevel = dbEntityList.stream().map(InsuredListDTO::getOccupationGroup).collect(Collectors.toSet());
        List<GroupInsured> replaceInsuredList = data.getInsuredList();
        StringBuilder errorMsg = new StringBuilder();
        for (int i = 0; i < replaceInsuredList.size(); i++) {

            GroupInsured insured = replaceInsuredList.get(i);
            String idNumber = insured.getIdNumber();
            InsuredListDTO item = entityMap.get(idNumber.toLowerCase());
            if (item != null && Objects.equals(item.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                errorMsg.append(String.format("第%s行：替换后身份证号与在保人员重复", i + 1) + "<br>");
            }

            if (!businessLevel.contains(insured.getOccupationGroup())) {
                errorMsg.append(String.format("第%s行：替换人员时不能更改职业类别，如需更改请先批减后再批增", i + 1) + "<br>");
            }

            if (!StringTools.validIdCard(idNumber)) {
                errorMsg.append(String.format("第%s行：替换后身份证号校验失败", i + 1) + "<br>");
                continue;
            }
            /**
             * 被保人校验年龄
             */
            Integer age = StringTools.parseAge(insured.getIdNumber());
            String applyAgeFrom = productDTO.catAgeFrom();
            String applyAgeTo = productDTO.catAgeTo();
            if (StringUtils.isNotBlank(applyAgeFrom)) {
                Integer applyAgeFromNum = Integer.parseInt(applyAgeFrom);
                if (age < applyAgeFromNum) {
                    errorMsg.append(String.format("第%s行，身份证号年龄不在承保年龄范围内", i + 1) + "<br>");
                }
            }
            if (StringUtils.isNotBlank(applyAgeTo)) {
                Integer applyAgeToNum = Integer.parseInt(applyAgeTo);
                if (age > applyAgeToNum) {
                    errorMsg.append(String.format("第%s行，身份证号年龄不在承保年龄范围内", i + 1) + "<br>");
                }
            }
        }
        if (errorMsg != null && errorMsg.length() > 0) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), errorMsg.toString());
        }
    }

    @Autowired
    private CorrectLogMapper correctLogMapper;

    /**
     * 预存人员替换信息
     *
     * @param data
     */
    @Deprecated
    private Endor dumpMemberChangeMsg(GroupEndorsement data) {
        List<GroupInsured> insuredList = data.getInsuredList();
        Date now = new Date();
        List<OrderInsuredCorrectLog> logs = insuredList.stream()
                .map(entry -> {
                    OrderInsuredCorrectLog log = new OrderInsuredCorrectLog();
                    log.setOrderId(data.getEndorId());
                    log.setIdNumber(entry.getIdNumber());
                    log.setIdType(entry.getIdType());
                    log.setBranchId(entry.getBranchId());
                    log.setName(entry.getPersonName());
                    log.setCreateTime(now);
                    log.setOccupationCode(entry.getOccupationCode());
                    log.setOccupationGroup(entry.getOccupationGroup());
                    return log;
                })
                .collect(Collectors.toList());
        correctLogMapper.insertListAutoGenKey(logs);

        Endor endor = new Endor();
        endor.setOrderId(data.getEndorId());
        endor.setRawOrderId(data.getOrderId());
        endor.setAmount(new BigDecimal(0));
        endor.setOpType(4);
        endor.setChannel(data.getChannel());
        endor.setEffectiveTime(data.getEffectiveTime());
        endor.setOperaor(HttpRequestUtil.getUserId());
        endor.setPolicyNo(data.getPolicyNo());
        endor.setStatus(EnumEndor.DOING.getCode());
        endorMapper.insertSelective(endor);
        return endor;
    }

    public Endor buildCorrectEntity(EnumGroupCorrectType type, GroupEndorsement data) {
        Endor endor = new Endor();
        endor.setOrderId(data.getEndorId());
        endor.setRawOrderId(data.getOrderId());
        endor.setAmount(data.getTotalPremium());
        endor.setOpType(type.getCode());
        endor.setChannel(data.getChannel());
        endor.setEffectiveTime(data.getEffectiveTime());
        endor.setOperaor(HttpRequestUtil.getUserId());
        endor.setPolicyNo(data.getPolicyNo());
        endor.setStatus(EnumEndor.TO_COMMIT.getCode());
        return endor;
    }

    /**
     * 预存人员替换信息
     *
     * @param data
     */
    @Deprecated
    private Endor dumpMemberChangeMsgV2(GroupEndorsement data) {
        Endor endor = buildCorrectEntity(EnumGroupCorrectType.REPLACE_MEMBER, data);

        endorMapper.insertSelective(endor);
        List<GroupInsured> insuredList = data.getInsuredList();
        for (GroupInsured insured : insuredList) {
            insured.setEndorId(data.getEndorId());
            insured.setPolicyNo(data.getPolicyNo());
            int i = policyMapper.copyInsured(data.getOrderId(), insured);
            int j = policyMapper.copyInsuredItem(insured);
            log.info("被保人更新结果：{},{}", i, j);
        }
        return endor;
    }

    @Autowired
    private WxOrderMapper wxOrderMapper;

    /**
     * 查询机构Id
     *
     * @param orderId
     * @return
     */
    private String queryOrgId(String orderId) {
        ApplicantDTO applicant = wxOrderMapper.queryApplicant(orderId);
        if (applicant == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单不存在或投保人信息为空");
        }
        return applicant.queryField("orgId", String.class);
    }

    @Override
    public Boolean endorEffect(String orderId) {
        return true;
    }

    /**
     * 批改互斥
     *
     * @param policyNo
     * @return
     */
    public boolean correctMutex(String channel, String policyNo) {
        List<Endor> endorList = endorMapper.queryByPolicyNo(channel, policyNo);
        for (Endor endor : endorList) {
            if (endor.doing()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 订单[批改单]取消
     *
     * @param req
     * @return
     */
    @Override
    public GroupEndorResponse cancel(GroupRevokeVo req) {
        if (req.isEndor()) {
            return cancelEndor(req);
        }
        /**
         * 线下支付的单才需要通知保司撤单
         * 线上支付的单可直接操作订单状态
         */
        FastOrderDTO order = orderMapper.getFastOrderByOrderId(req.getOrderId());
        if (order == null) {
            throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), "订单信息不存在：" + req.getOrderId());
        }
        if (order.offlinepay()) {
            GroupNotifyPaymentInput request = WhaleMessageBuilder.cvtTransferAccountBean(order.getProposalNo(),
                    "2",
                    apiProperties,
                    null);
            WhaleResp<GroupNotifyPayOutput> resp = apiService.notifyPayinfo(request);
            if (!resp.isSuccess()) {
                throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), resp.getMsg());
            }
        }
        /**
         * 新契約直接關閉訂單
         * 不影響後續流程
         */
        OrderDTO entity = new OrderDTO();
        entity.setFhOrderId(req.getOrderId());
        entity.setPayStatus(SmConstants.ORDER_STATUS_CANCEL);
        entity.setOrderState(SmConstants.ORDER_STATUS_CANCEL);
        int i = orderMapper.updateOrder(req.getOrderId(), entity);

        log.info("投保单更新[撤销]状态：{}", i);
        GroupEndorResponse data = new GroupEndorResponse();
        data.setOrderId(req.getOrderId());
        return data;
    }

    /**
     * 撤销单-只有线下支付时才需要
     *
     * @param req
     * @return
     */
    public GroupEndorResponse cancelEndor(GroupRevokeVo req) {
        Endor endor = endorMapper.queryEndor(EnumChannel.TK_PAY.getCode(), req.getEndorId(), req.getEndorsementNo());
        if (endor == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "批改信息不存在");
        }
        if (endor.offlinepay()) {
            GroupNotifyPaymentInput request = WhaleMessageBuilder.cvtTransferAccountBean(endor.getProposalNo(),
                    "2",
                    apiProperties,
                    null);
            WhaleResp<GroupNotifyPayOutput> resp = apiService.notifyPayinfo(request);
            if (!resp.isSuccess()) {
                throw new MSBizNormalException(ExcptEnum.CHANNEL_ERROR.getCode(), resp.getMsg());
            }
        }

        endor.setStatus(EnumEndor.CANCEL.getCode());
        int i = endorMapper.updateByPrimaryKeySelective(endor);
        log.info("批改单更新[撤销]状态-{}", i);
        GroupEndorResponse data = new GroupEndorResponse();
        data.setOrderId(req.getOrderId());
        return data;
    }


}
