package com.cfpamf.ms.insur.admin.pojo.dto.order;

import com.alibaba.druid.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/10 1:42 下午
 * @Version 1.0
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class OrderCommissionInsuredInfoDTO {
    private String fhOrderId;

    private Integer insuredId;

    private Integer companyId;
    private Integer productId;

    Integer commissionId;

    String recommendId;
    /**
     * 投保产品名称
     */
    @ApiModelProperty(value = "投保产品名称")
    private String productName;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty("产品属性")
    private String productAttrCode;

    /**
     * 投保计划名称
     */
    @ApiModelProperty(value = "投保计划名称")
    private String planName;

    /**
     * 投保计划id
     */
    @ApiModelProperty(value = "投保计划id")
    private Integer planId;
    /**
     * 保司产品id
     */
    @ApiModelProperty(value = "保司产品id")
    private String fhProductId;




    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "item表订单金额",hidden = true)
    private BigDecimal itemTotalAmount;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 团险批减单原始保单号团险专用
     */
    @ApiModelProperty(value = "团险批减单原始保单号 团险专用")
    String originalPolicyNo;

    /**
     * 保单状态
     */
    private String appStatus;



    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredPersonName;

    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "被保人证件号")
    private String insuredIdNumber;

    /**
     * 被保人手机号
     */
    @ApiModelProperty(value = "被保人手机号")
    private String insuredCellPhone;

    /**
     * 被保人邮箱
     */
    @ApiModelProperty(value = "被保人邮箱")
    private String insuredEmail;


    @ApiModelProperty("渠道来源")
    private String subChannel;

    @ApiModelProperty(value = "渠道来源",hidden = true)
    private String channel;

    @ApiModelProperty("退保时间")
    private Date surrenderTime;

    @ApiModelProperty("支付时间")
    private Date paymentTime;
    @ApiModelProperty("起保日期")
    private LocalDateTime startTime;
    @ApiModelProperty("订单创建日期")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty("提交时间时间")
    private String submitTime;
    @ApiModelProperty("支付状态")
    private String payStatus;

    /**************长险相关参数******************
     /**
     * 字段名称 回访状态
     */
    @ApiModelProperty(value = "回访状态，未回访undo,回访成功 success,首访不成功 firstFailed,再访不成功 failedAgain")
    String visitStatus;
    /**
     * 字段名称 退保类型
     */
    @ApiModelProperty(value = "退保类型")
    String surrenderType;
    /**
     * 字段名称 是否长险 0:长险；1:短险
     */
    @ApiModelProperty(value = "是否长险 0:长险；1:短险")
    Integer longInsurance;



}
