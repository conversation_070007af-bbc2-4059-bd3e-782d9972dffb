package com.cfpamf.ms.insur.admin.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 保险公司
 * <AUTHOR> 2020/2/13 15:04
 */
@AllArgsConstructor
@Getter
public enum EnumCompanyCode {
    /**
     * id配置到这里
     */
    C4002(1, "泰康在线财产保险股份有限公司", "4002"),
    C2043(2, "华安财险", "2043"),
    C2060(3, "安心保险", "2060"),
    C2049(4, "安联财险", "2049"),
    C2007(5, "平安财险", "2007"),
    C2071(204, "史带财险", "2071"),
    C2034(205, "美亚保险", "2034"),
    C2019(206, "阳光保险", "2019"),
    C2056(207, "众安保险", "2056"),
    ZA(218, "众安保险", "ZA"),
    C1009(208, "平安健康", "1009"),
    C2058(209, "安诚财险", "2058"),
    C1010(210, "平安养老", "1010"),
    C2066(211, "亚太财产", "2066"),
    CCIC(212, "中华联合财产保险股份有限公司", "CIC"),
    C1111(213, "诚泰保险", "1111"),
    DJBX(217, "大家财产保险有限责任公司", "djbx"),
    GSCX(216, "中国人寿财产保险股份有限公司", "gscx"),
    FX(221, "复星联合健康保险", "fx"),
    GTCC(272, "国泰财产保险有限责任公司", "GTCC"),
    ;

    private Integer id;

    private String companyName;

    private String companyCode;


}
