package com.cfpamf.ms.insur.admin.pojo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 员工推广费记录表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user_promotion_fee_info")
@ApiModel(description = "员工推广费记录表")
public class UserPromotionFeeInfo extends BasePO {

    /**
     * 统计日期
     */
    @ApiModelProperty(value = "统计日期")
    @Column(name = "statistical_date")
    private LocalDate statisticalDate;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    @Column(name = "user_id", length = 60)
    private String userId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "user_name", length = 128)
    private String userName;

    /**
     * 数据类型 year年数据,month月数据
     */
    @ApiModelProperty(value = "数据类型 year年数据,month月数据")
    @Column(name = "data_type", length = 16)
    private String dataType;

    /**
     * 保费(计算保费)
     */
    @ApiModelProperty(value = "保费(计算保费)")
    @Column(name = "premium", precision = 16, scale = 4)
    private BigDecimal premium = BigDecimal.ZERO;

    /**
     * 推广费
     */
    @ApiModelProperty(value = "推广费")
    @Column(name = "amount", precision = 16, scale = 4)
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 同步时间
     */
    @ApiModelProperty(value = "同步时间")
    @Column(name = "pt", length = 16)
    private String pt;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @Column(name = "create_user", length = 32)
    private String createUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @Column(name = "update_user", length = 32)
    private String updateUser;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁")
    @Version
    @Column(name = "revision")
    private Integer revision = 1;
}
