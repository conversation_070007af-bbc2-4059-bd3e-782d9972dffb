package com.cfpamf.ms.insur.admin.service;

import com.beust.jcommander.internal.Maps;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.annotation.AdminAutoAuthQuery;
import com.cfpamf.ms.insur.admin.config.CancelProperties;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.EnumCancelFileType;
import com.cfpamf.ms.insur.admin.enums.EnumCancelState;
import com.cfpamf.ms.insur.admin.enums.EnumCancelVariable;
import com.cfpamf.ms.insur.admin.enums.EnumFlowInsCancel;
import com.cfpamf.ms.insur.admin.enums.order.EnumRecommendChannel;
import com.cfpamf.ms.insur.admin.event.SmCancelSuccessEvent;
import com.cfpamf.ms.insur.admin.event.WxCancelNotifyEvent;
import com.cfpamf.ms.insur.admin.external.whale.client.WhaleInsuranceClient;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.external.whale.utils.WhaleRequestMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCompanySettingDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.ActJumpDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.CancelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.cancel.SmCancelCompanyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancel;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancelBankDeposit;
import com.cfpamf.ms.insur.admin.pojo.po.SmCancelFile;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.query.SmCancelQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.CompanyVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmCompanySettingVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO;
import com.cfpamf.ms.insur.admin.pojo.vo.cancel.*;
import com.cfpamf.ms.insur.base.activiti.ActivitiCommonService;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.constant.WorkFlowConstants;
import com.cfpamf.ms.insur.base.enums.AuditResultEnum;
import com.cfpamf.ms.insur.base.enums.WorkFlowEnum;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.pojo.dto.SmAuditDTO;
import com.cfpamf.ms.insur.base.pojo.dto.SmAuditEmailDTO;
import com.cfpamf.ms.insur.base.pojo.vo.ActTaskEndTime;
import com.cfpamf.ms.insur.base.pojo.vo.ActivitiActVO;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxCancelDTO;
import com.cfpamf.ms.insur.weixin.pojo.enums.whale.SourceSystemEnum;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCancelQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.base.WhalePlatformData;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.order.request.ApplyCancelInput;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.order.request.WhaleApplyCancel;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.order.request.WhaleApplyCancelBackInfo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.order.response.ApplyCancerOutput;
import com.cfpamf.ms.insur.weixin.pojo.vo.cancel.WxCancelPolicyVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 退保服务
 *
 * <AUTHOR> 2020/1/17 14:00
 */
@Slf4j
@Service("smCancelService")
@Transactional(rollbackFor = Exception.class)
@EnableConfigurationProperties(CancelProperties.class)
public class SmCancelService {

    /**
     *
     * 用于区域编码和区域名称字段的分隔符
     *
     */
    private static final String CANCEL_BANK_INFO_SPLIT = ",";
    /**
     *
     * 用于抽取出银行名称的正则匹配
     *
     */
    private static final Pattern CANCEL_BANK_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]+银行");


    @Autowired
    private SmCancelEmailService smCancelEmailService;
    @Autowired
    private SmCancelMapper cancelMapper;

    @Autowired
    private OrgPicExtraMapper orgPicExtraMapper;

    @Autowired
    private ActivitiCommonService activitiCommonService;

    @Autowired
    private UserService userService;

    @Autowired
    private RedisUtil<String, Integer> redisUtil;

    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private SmCancelFileMapper fileMapper;

    @Autowired
    private SmCancelBankDepositMapper bankDepositMapper;
    @Autowired
    private SmOrderCoreService orderCoreService;

    @Autowired
    private SmCmpySettingService cmpySettingService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private CompanyMapper companyMapper;

    @Resource
    private WhaleInsuranceClient whaleInsuranceClient;

    @Resource
    private WhaleRequestMapper whaleRequestMapper;

    @Autowired
    private CancelProperties cancelProperties;

    @Autowired
    private SmOrderInsuredMapper smOrderInsuredMapper;


    /**
     * 退保申请 没有审批流 （泰康 众安等渠道）
     */
    public SmCancel applyNoProcess(WxCancelDTO dto, String userId) {
        validData(dto);

        SmCancel smCancel = new SmCancel();

        BeanUtils.copyProperties(dto, smCancel);
        smCancel.setCreateBy(userId);
        smCancel.setState(EnumCancelState.CANCELING.getKey());
        smCancel.setCancelNo(getNextCancelNo());
        smCancel.setTaskKey(EnumFlowInsCancel.COMPANY_DOING.getEventId());
        cancelMapper.insertSelective(smCancel);
        return smCancel;
    }

    /**
     * 退保申请
     */
    public SmCancel apply(WxCancelDTO dto) {
        validData(dto);
        JwtUserInfo user = HttpRequestUtil.getUserOrThrowExp();
        SmCancel smCancel = new SmCancel();
        smCancel.setCreateBy(user.getJobNumber());
        smCancel.setState(EnumCancelState.CANCELING.getKey());
        smCancel.setCancelNo(getNextCancelNo());
        BeanUtils.copyProperties(dto, smCancel);

//        ThreadUserUtil.USER_DETAIL_TL.get()

        AuthUserVO currUser = userService.getAuthUserByUserId(user.getJobNumber());
        //当前登录用户不存在
        if (Objects.isNull(currUser)) {
            throw new BizException(ExcptEnum.DATA_ERROR_801304);
        }
        //该机构暂未渠道pco，流程直接跳转至总部审核阶段
        AuthUserVO pcoUser = userService.getChannelPCOByHrOrgId(currUser.getHrOrgId());
        //查询机构对接人
        // 退保流程的全局变量
        Map<String, Object> map = EnumCancelVariable.initVars();
        //所有的都没有机构审核
        map.put("creatorIsHead", true);
        //机构对接人
        if (Objects.nonNull(pcoUser)) {
            map.put("orgAdmin", pcoUser.getUserId());
        }
        //启动流程
        ProcessInstance processInstance = activitiCommonService.startProcessInstanceByKey(
                WorkFlowEnum.INSURANCE_CANCEL.getProDefKey(), smCancel.getCancelNo(), map);
        smCancel.setProcessInstanceId(processInstance.getId());
        cancelMapper.insertSelective(smCancel);
        activitiCommonService.completeTask(processInstance.getId(),
                smCancel.getCreateBy(), null, new HashMap<>(2));
        saveCancelFile(smCancel.getId(), smCancel.getCreateBy(), dto.getFileGroupVOS());
        saveBankDeposit(smCancel.getId(), dto.getBankDeposit());
        return smCancel;
    }


    /**
     * 重新推送事件[第一次推送未保存退保数据 会返回空]
     *
     * @param smCancel
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void rePushMessage(SmCancel smCancel) {
        SpringFactoryUtil.getBean(EventBusEngine.class)
                .publish(new WxCancelNotifyEvent(smCancel.getProcessInstanceId(), smCancel.getTaskKey(), false));
    }

    /**
     * 重新提交 【补充资料】
     */
    public void reApply(WxCancelDTO dto) {
        String jobNumber = HttpRequestUtil.getUserOrThrowExp().getJobNumber();

        SmCancel update = new SmCancel();
        update.setCancelReasonType(dto.getCancelReasonType());
        update.setCancelReason(dto.getCancelReason());
        update.setId(dto.getCancelId());
        cancelMapper.updateByPrimaryKeySelective(update);
        SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(dto.getCancelId());
        saveCancelFile(dto.getCancelId(), jobNumber, dto.getFileGroupVOS());
        saveBankDeposit(dto.getCancelId(), dto.getBankDeposit());
        //只有在提交资料的环境才跑到下一个环节
        if (EnumFlowInsCancel.UT_APPLY.getEventId().equals(smCancel.getTaskKey())) {
            log.info("客户经理补充资料 并提交到下一个环节:{}", dto);
            activitiCommonService.completeTask(smCancel.getProcessInstanceId(), jobNumber, null, new HashMap<>(2));
        } else {
            log.info("客户经理修改资料资料当前环节{}:{}", smCancel.getTaskKey(), dto);
        }
    }

    private void saveBankDeposit(Integer cancelId, SmCancelBankDeposit bankDeposit) {

        bankDeposit.setCancelId(cancelId);
        SmCancelBankDeposit dbData = bankDepositMapper.selectByCancelId(cancelId);
        if (dbData == null) {
            bankDepositMapper.insertUseGeneratedKeys(bankDeposit);
        } else {
            bankDeposit.setId(dbData.getId());
            bankDepositMapper.updateByPrimaryKeySelective(bankDeposit);
        }
    }

    /**
     * 保存文件
     */
    public void saveCancelFile(Integer cancelId, String operator, List<SmCancelFileGroupVO> files) {

        log.info("开始保存文件：{},{}", cancelId, operator);
        int i = fileMapper.logicDeleteByCancelId(cancelId);
        log.info("{}删除之前数据成功{}==>{}", operator, cancelId, i);
        List<SmCancelFile> smCancelFiles = LambdaUtils.one2More(files, fileGroupVO ->
                fileGroupVO.getFileUrls().stream().map(fileUrl -> {
                    SmCancelFile smCancelFile = new SmCancelFile();
                    BeanUtils.copyProperties(fileGroupVO, smCancelFile);
                    if (UrlUtils.isAbsoluteUrl(fileUrl)) {
                        //去掉path 中的 第一个/
                        String substring = URI.create(fileUrl).getPath().substring(1);
                        smCancelFile.setFileUrl(substring);
                    } else {
                        smCancelFile.setFileUrl(fileUrl);
                    }
                    smCancelFile.setCancelId(cancelId);
                    smCancelFile.setCreateBy(operator);
                    return smCancelFile;
                }).collect(Collectors.toList()));
        //保存文件
        if (!CollectionUtils.isEmpty(smCancelFiles)) {
            fileMapper.insertList(smCancelFiles);
        }
    }


    /**
     * 获取需要上传的文件 或已上传的文件
     *
     * @param cancelId
     * @param insuredId
     * @return
     */
    public List<SmCancelFileGroupVO> getCancelFile(Integer cancelId, Integer insuredId) {

        Map<String, List<SmCancelFile>> fileGroup =
                Objects.isNull(cancelId) ? Collections.emptyMap() :
                        LambdaUtils.groupBy(fileMapper.selectByCancelId(cancelId), SmCancelFile::getFileTypeCode);
        //获取需要上传 文件
        CancelOrderDetail cancelOrderDetail = orderDetail(insuredId);
        List<EnumCancelFileType> enumCancelFileTypes = fileTypes(insuredId);

        return enumCancelFileTypes.stream()
                .map(fileEnum -> {
                    SmCancelFileGroupVO smCancelFileGroupVO = fileEnum.toGroupVO(cancelOrderDetail);
                    smCancelFileGroupVO.setCancelId(cancelId);
                    List<SmCancelFile> orDefault = fileGroup.getOrDefault(fileEnum.getCode(), Collections.emptyList());
                    smCancelFileGroupVO.setFileUrls(orDefault.stream().map(SmCancelFile::getFileUrl).collect(Collectors.toList()));

                    return smCancelFileGroupVO;
                }).collect(Collectors.toList());
    }


    /**
     * 获取获取需要上传的文件类型
     *
     * @param insuredId
     */
    public List<EnumCancelFileType> fileTypes(Integer insuredId) {
        return EnumCancelFileType.filterCancelType(orderDetail(insuredId));
    }

    /**
     * 审核 当前环节必须处于审批节点 ut_xxx_audit
     * 退保：客户经理提交-【机构】渠道pco & 总部审核-【总部】总部审核
     *
     * @param cancelId
     * @param insuredId
     * @param auditDTO
     */
    public void audit(Integer cancelId, Integer insuredId, SmAuditDTO auditDTO) {
        String resultCode = auditDTO.getResultCode();
        SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(cancelId);
        log.info("审批信息{},{}", smCancel, auditDTO);
        //ut_代表是用户任务 _audit 代表审核节点
        if (StringUtils.startsWith(smCancel.getTaskKey(), ActivitiCommonService.AUDIT_PREFIX)
                && StringUtils.endsWith(smCancel.getTaskKey(), ActivitiCommonService.AUDIT_SUFFIX)) {
            JwtUserInfo user = HttpRequestUtil.getUserOrThrowExp();
            if (EnumFlowInsCancel.SAFE_CENTER.isCurrent(smCancel.getTaskKey())) {
                try {
                    UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
                    //【总部】业务支持审核
                    if (!permissionUtil.isHeadRole(userDetailVO)) {
                        throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "流程已至总部审核当前用户无权限");
                    }

                } finally {
                    ThreadUserUtil.USER_DETAIL_TL.remove();
                }
            }
            //机构审核 权限-渠道pco 总部
            if (EnumFlowInsCancel.ORG_ADMIN_AUDIT.isCurrent(smCancel.getTaskKey())) {
                try {
                    UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
                    if (!permissionUtil.isChannelPCO(userDetailVO) && !permissionUtil.isHeadRole(userDetailVO)) {
                        throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "当前用户无权限");
                    }

                } finally {
                    ThreadUserUtil.USER_DETAIL_TL.remove();
                }
            }
            // 如果是同意就调用保司的申请退保接口
            if (AuditResultEnum.PASS.getResult().equals(resultCode)){
                handlerApplyCancel(cancelId, insuredId);
            }

            Map<String, Object> map = Maps.newHashMap();
            map.put(WorkFlowConstants.VARS_AUDIT_RESULT, resultCode);
            activitiCommonService.completeTask(smCancel.getProcessInstanceId(), user.getJobNumber(), auditDTO.getMessage(), map);
        } else {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "当前流程不处于审批节点:" + smCancel.getTaskKey());
        }

    }

    private void handlerApplyCancel(Integer cancelId, Integer insuredId) {
        // CancelOrderDetail cancelOrderDetail = getCancelOrderDetail(cancelId);
        CancelOrderDetail cancelOrderDetail = getCancelDetailByCancelIdAndInsuredId(cancelId, insuredId);
        if(Objects.isNull(cancelOrderDetail)){
            String errMsg = String.format("查询退保详情信息异常，请检查参数: cancelId - %s, insuredId - %s", cancelId, insuredId);
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), errMsg);
        }
        String companyIdentifier = cancelOrderDetail.getCompanyIdentifier();
        List<String> companyCodes = cancelProperties.getCompanyCodes();
        // 如果配置了就调用中台的接口进行退保申请。
        if (Objects.nonNull(companyCodes) && companyCodes.contains(companyIdentifier)){
            applyInsuranceCancel(cancelOrderDetail);
        }
    }

    /**
     *
     * 进行调用保司侧的退保接口
     * 本质是调用小鲸的对接中台服务的退保申请
     *
     *
     * @param cancelOrderDetail
     *
     * 退保订单详情
     */
    private void applyInsuranceCancel(CancelOrderDetail cancelOrderDetail) {
        ApplyCancelInput applyCancelInput = whaleRequestMapper.buildApplyCancelInput(cancelOrderDetail);
        WhalePlatformData platformData = applyCancelInput.getPlatformData();
        platformData.setSourceCode(cancelOrderDetail.getFhOrderId());
        platformData.setSourceSystemEnum(SourceSystemEnum.Zhnx_Order_Source);
        WhaleApplyCancel cancel = buildWhalePolicyForm(cancelOrderDetail);
        applyCancelInput.setApplyCancel(cancel);
        WhaleResp<ApplyCancerOutput> applyCancerOutputWhaleResp = whaleInsuranceClient.applyCancel(applyCancelInput);
        if (!applyCancerOutputWhaleResp.isSuccess()){
            throw new BizException(ExcptEnum.WHALE_APPLY_CANCEL_FAILED.getCode(), applyCancerOutputWhaleResp.getMsg());
        }
    }

    /**
     *
     * 构建小鲸的申请退保的参数
     *
     * @param cancelOrderDetail 退保的订单的详情
     *
     * @return
     *
     * 小鲸的申请退保的参数
     *
     */
    private WhaleApplyCancel buildWhalePolicyForm(CancelOrderDetail cancelOrderDetail) {
        WhaleApplyCancel cancel = whaleRequestMapper.buildWhalePolicyForm(cancelOrderDetail);
        WhaleApplyCancelBackInfo whaleApplyCancelBackInfo = whaleRequestMapper.buildWhaleApplyCancelBackInfo(cancelOrderDetail);

        //由于原设计省市名称和区域编码耦分别耦合成一个字段，需要将他们隔开处理。
        //例子，区域编码字段11,1101。区域名称字段北京,北京市
        String bankAreaCode = cancelOrderDetail.getBankAreaCode();
        List<String> bankAreaCodeList = splitStringToList(bankAreaCode);
        String provinceCode = bankAreaCodeList.get(0);
        String cityCode = bankAreaCodeList.get(1);

        String bankAreaName = cancelOrderDetail.getBankAreaName();
        List<String> bankAreaNameList = splitStringToList(bankAreaName);
        String provinceName = bankAreaNameList.get(0);
        String cityName = bankAreaNameList.get(1);

        //从支行名称里抽取出，改银行的名称
        String bankOfDeposit = cancelOrderDetail.getBankOfDeposit();
        String bankName = cancelOrderDetail.getBankName();
        String bankCode = cancelOrderDetail.getBankCode();
        whaleApplyCancelBackInfo.setProvinceName(provinceName);
        whaleApplyCancelBackInfo.setProvinceCode(provinceCode);
        whaleApplyCancelBackInfo.setCityName(cityName);
        whaleApplyCancelBackInfo.setCityCode(cityCode);
        whaleApplyCancelBackInfo.setBankName(bankName);
        whaleApplyCancelBackInfo.setBankCode(bankCode);
        whaleApplyCancelBackInfo.setBranchName(bankOfDeposit);
        cancel.setWhaleApplyCancelBackInfo(whaleApplyCancelBackInfo);

        return cancel;
    }

    /**
     *
     * 分割退保银行信息字段专用，
     * 用于分割area_id和area_name
     * 功能是将一个字符串，使用","分割成两个字符串
     * 如果不是固定的两个字符串集合将会报错
     *
     * @param bankString
     * @return
     */
    private List<String> splitStringToList(String bankString) {
        String[] split = bankString.split(CANCEL_BANK_INFO_SPLIT);
        List<String> bankAreaCodeList = Arrays.asList(split);
        int size = bankAreaCodeList.size();
        if (size != 2){
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "退保银行信息有误");
        }
        return bankAreaCodeList;
    }

    /**
     * 获取默认流程图
     *
     * @param processInstanceId
     * @return
     */
    public List<ActivitiActVO> getDefaultPath(String processInstanceId) {
        if (StringUtils.isBlank(processInstanceId)) {
            return Collections.emptyList();
        }

        SmCancel query = new SmCancel();
        query.setProcessInstanceId(processInstanceId);
        SmCancel cancel = cancelMapper.selectOne(query);

        boolean isCancel = Objects.equals(cancel.getState(), EnumCancelState.CANCELED.getKey());
        List<ActivitiActVO> processPath = activitiCommonService.findProcessPath(processInstanceId, isCancel);
        //如果是取消退保
        if (isCancel) {
            if (CollectionUtils.isEmpty(processPath)
                    || processPath.size() < 2) {
                return processPath;
            }
            ActivitiActVO activitiAct = new ActivitiActVO();
            activitiAct.setActId("ut_cancel_cancel");
            activitiAct.setStartTime(cancel.getEndTime());
            activitiAct.setActName("取消退保");
            activitiAct.setMessage(activitiCommonService.getDeleteReason(processInstanceId));
            activitiAct.setEndTime(cancel.getEndTime());
            if (StringUtils.isNotBlank(cancel.getCanceler())) {
                activitiAct.setAssignee(cancel.getCanceler());
                AuthUserVO authUserById = userService.getAuthUserByUserId(cancel.getCanceler());
                activitiAct.setAssigneeName(authUserById.getUserName());
            }
            processPath.add(activitiAct);

            activitiAct = new ActivitiActVO();
            activitiAct.setActId("cancel_end");
            activitiAct.setStartTime(cancel.getEndTime());
            activitiAct.setActName("结束");
            activitiAct.setEndTime(cancel.getEndTime());
            processPath.add(activitiAct);
        }
        return processPath;
    }

    /**
     * 当保险业务中心审核驳回时
     */
    public void safeCenterReject(DelegateExecution execution) {

        log.info("safeCenterReject {}", execution);
        Integer variable = execution.getVariable("centerRejectCount", Integer.class);
        if (Objects.isNull(variable)) {
            variable = 0;
        }
        execution.setVariable("centerRejectCount", ++variable);

    }

    /**
     * 提交到保险公司
     *
     * @param execution
     */
    public void commit2Company(DelegateExecution execution) {

        // 如果以前没有被保险业务中心拒绝过 会导致 后面 强制跳转走到机构对接人 所以在这里+1
        log.info("safeCenterReject {}", execution);
        Integer variable = execution.getVariable("centerRejectCount", Integer.class);
        if (Objects.isNull(variable)) {
            variable = 0;
        }
        execution.setVariable("centerRejectCount", ++variable);

        log.info("refCancelState 提交到保险公司{}:{}", execution.getProcessInstanceBusinessKey(), execution.getCurrentFlowElement());
        String cancelNo = execution.getProcessInstanceBusinessKey();
        SmCancel smCancel = cancelMapper.selectByCancelNo(cancelNo);
        WxCancelPolicyVO canCancelDetailById = getCanCancelDetailById(smCancel.getId());
        long between = ChronoUnit.DAYS.between(LocalDate.now(), canCancelDetailById.getStartTime());
        SmCancel update = new SmCancel();
        update.setId(smCancel.getId());
        update.setToCompanyPolicyState(between < 0 ? 1 : 0);
        cancelMapper.updateByPrimaryKeySelective(update);

    }

    public void actCancelCancel(DelegateExecution execution) {

        log.info("actRefState 取消退保{}:{}", execution.getProcessInstanceBusinessKey(), execution.getCurrentFlowElement());
        String cancelNo = execution.getProcessInstanceBusinessKey();
    }


    /**
     * 后台权限内查询
     *
     * @param query
     * @return
     */
    @AdminAutoAuthQuery
    public PageInfo<WxCancelPolicyVO> adminAuthPageQuery(SmCancelQuery query) {
        PageInfo<WxCancelPolicyVO> objectPageInfo = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> adminAuthQuery(query));

        dictSex(objectPageInfo.getList());
        return objectPageInfo;
    }

    @AdminAutoAuthQuery
    public List<WxCancelPolicyVO> adminAuthQuery(SmCancelQuery query) {

        //查询时间需要包括查询日期 将日期推后一天
        if (Objects.nonNull(query.getEndEnd())) {
            query.setEndEnd(query.getEndEnd().plusDays(1L));
        }

        if (Objects.nonNull(query.getApplyEnd())) {
            query.setApplyEnd(query.getApplyEnd().plusDays(1L));
        }
        return cancelMapper.listAdminQuery(query);
    }

    public List<SmCancel> listSimpleByFhOrderId(String fhOrderId) {
        return cancelMapper.selectByOrderId(fhOrderId);
    }

    public PageInfo<WxCancelPolicyVO> pageCancel(WxCancelQuery query) {

        PageInfo<WxCancelPolicyVO> objectPageInfo = PageHelper.startPage(query.getPage(), query.getSize())
                .doSelectPageInfo(() -> listCanCancelPolicy(query));
        dictSex(objectPageInfo.getList());
        dictCompany(objectPageInfo.getList());
        return objectPageInfo;
    }

    public List<WxCancelPolicyVO> listCanCancelPolicy(WxCancelQuery query) {

        //简单判断是在搜索什么字段
        if (StringUtils.isNotBlank(query.getKeyword())) {
            if (Pattern.matches("[\\d]+[xX]?", query.getKeyword())) {
                query.setKeywordLikeIdCard(Boolean.TRUE);
                query.setKeywordLikePolicyNo(Boolean.TRUE);
            } else if (Pattern.matches("[\\w]+", query.getKeyword())) {
                query.setKeywordLikePolicyNo(Boolean.TRUE);
            } else {
                query.setKeywordLikeName(Boolean.TRUE);
            }
        }
        return cancelMapper.listCanCancelPolicy(query);
    }

    /**
     * 获取保单详情 渠道 被保人 投保人信息等
     *
     * @param insuredId
     * @return
     */
    private CancelOrderDetail orderDetail(Integer insuredId) {
        return cancelMapper.getCancelOrderDetailByInsuredId(insuredId);
    }

    /**
     * 获取保单详情 渠道 被保人 投保人信息等
     *
     * @return
     */
    public CancelOrderDetail getCancelOrderDetail(Integer cancelId) {
        return cancelMapper.getCancelOrderDetail(cancelId);
    }

    /**
     * 退保相关信息
     * @param cancelId  退保ID
     * @param insuredId 被保人ID
     * @return  返回结果
     */
    public CancelOrderDetail getCancelDetailByCancelIdAndInsuredId(Integer cancelId, Integer insuredId) {
        return cancelMapper.getCancelDetailByCancelIdAndInsuredId(cancelId, insuredId);
    }

    public CancelEmailTemplateVO getCancelEmailTemplate(Integer cancelId, Integer insuredId) {
        return smCancelEmailService.getEmailTemplate(cancelId, insuredId);
    }

    /**
     * 取消退保
     *
     * @param dto
     */
    public void cancelCancel(CancelDTO dto) {
        SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(dto.getId());
        if (StringUtils.isNotBlank(smCancel.getProcessInstanceId())) {
            activitiCommonService.deleteProcessInstance(smCancel.getProcessInstanceId(), dto.getReason());
        }
        SmCancel update = new SmCancel();
        update.setId(smCancel.getId());
        update.setEndTime(LocalDateTime.now());
        update.setTaskKey(EnumFlowInsCancel.CANCEL_END.getEventId());
        update.setState(EnumCancelState.CANCELED.getKey());
        update.setCanceler(HttpRequestUtil.getUserId());
        cancelMapper.updateByPrimaryKeySelective(update);
        //发送推送
        SpringFactoryUtil.getBean(EventBusEngine.class).publishSync(new WxCancelNotifyEvent(smCancel.getProcessInstanceId(),
                EnumFlowInsCancel.CANCEL_END.getEventId(), false));
    }


    /**
     * 查询退保信息详情【】
     * 有退保数据
     * 无退保中数据（） return null
     * 有退保中数据 return 退保中数据
     * 无退保数据源
     * 返回 订单信息
     *
     * @param cancelId 根据退保id查询
     * @return
     */
    public WxCancelPolicyVO getCanCancelDetailById(Integer cancelId) {

        WxCancelQuery wxCancelQuery = new WxCancelQuery();
        wxCancelQuery.setCancelId(cancelId);
        List<WxCancelPolicyVO> dbs = listCanCancelPolicy(wxCancelQuery);
        if (CollectionUtils.isEmpty(dbs)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "退保信息不存在!");
        }
        dictSex(dbs);
        return dbs.get(0);
    }

    /**
     * 查询退保信息详情【】
     * 有退保数据
     * 无退保中数据（） return null
     * 有退保中数据 return 退保中数据
     * 无退保数据源
     * 返回 订单信息
     *
     * @param insuredId
     * @return
     */
    public WxCancelPolicyVO getCanCancelDetail(Integer insuredId) {

        WxCancelQuery wxCancelQuery = new WxCancelQuery();
        wxCancelQuery.setInsuredId(insuredId);
        wxCancelQuery.setAdd(Boolean.TRUE);
        List<WxCancelPolicyVO> dbs = listCanCancelPolicy(wxCancelQuery);
        dictSex(dbs);
        if (dbs.size() == 1) {
            WxCancelPolicyVO wxCancelPolicyVO = dbs.get(0);
            if (wxCancelPolicyVO.isCancel()) {
                wxCancelPolicyVO.setCancelId(null);
                wxCancelPolicyVO.setCancelState(null);
                wxCancelPolicyVO.setTaskKey(null);
                return wxCancelPolicyVO;
            }
        }
        List<WxCancelPolicyVO> sta = dbs.stream().filter(vo ->
                !Objects.equals(EnumCancelState.CANCELED.getKey(),
                        vo.getCancelState()))
                .collect(Collectors.toList());
        //查询出来的数据大于1 但是非取消数据为空
        if (dbs.size() > 1 && CollectionUtils.isEmpty(sta)) {
            WxCancelPolicyVO wxCancelPolicyVO = dbs.get(0);
            wxCancelPolicyVO.setCancelId(null);
            wxCancelPolicyVO.setCancelState(null);
            wxCancelPolicyVO.setTaskKey(null);
            return wxCancelPolicyVO;
        }
        if (!CollectionUtils.isEmpty(sta)) {
            return sta.get(0);
        }
        return null;

    }

    /**
     * 获取退保详情
     *
     * @return
     */
    public CancelDetailVO getCancelDetailVO(Integer insuredId, Integer cancelId) {
        CancelDetailVO cancel = new CancelDetailVO();
        WxCancelPolicyVO canCancelDetail =
                Objects.nonNull(cancelId) ? getCanCancelDetailById(cancelId) : getCanCancelDetail(insuredId);

        if (canCancelDetail == null) {
            return null;
        }
        Integer companyId = canCancelDetail.getCompanyId();
        if (companyId != null) {
            CompanyVO companyVO = companyMapper.getCompanyById(companyId);
            if (companyVO != null) {
                canCancelDetail.setCompanyCode(companyVO.getCompanyIdentifier());
            }
        }

        String orderId = canCancelDetail.getFhOrderId();
        if (orderId != null) {
            String pattern = "^((xj){1}|(XJ){1})\\S+";
            if (orderId.matches(pattern)) {
                canCancelDetail.setSource(EnumRecommendChannel.xjxh.name());
            } else {
                canCancelDetail.setSource(EnumRecommendChannel.zhnx.name());
            }
        }


        if (canCancelDetail.isApply()) {
            cancel.setBankDeposit(bankDepositMapper.selectByCancelId(canCancelDetail.getCancelId()));
            cancel.setCancel(cancelMapper.selectByPrimaryKeyMustExists(canCancelDetail.getCancelId()));
            cancel.setWxCancelPolicyVO(canCancelDetail);
            cancel.setActivities(getDefaultPath(cancel.getCancel().getProcessInstanceId()));
        }
        cancel.setWxCancelPolicyVO(canCancelDetail);
        cancel.setFileGroupVOS(getCancelFile(canCancelDetail.getCancelId(), insuredId));

        //去除未来机构审核节点
        if (!CollectionUtils.isEmpty(cancel.getActivities())) {
            cancel.setActivities(cancel.getActivities()
                    .stream()
                    .filter(e -> e.getProcInstId() != null
                            || !EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId().equals(e.getActId()))
                    .collect(Collectors.toList()));
        }
        return cancel;
    }

    /**
     * 审核通过并发送邮件
     *
     * @param cancelId
     * @param insuredId
     * @param emailDTO
     */
    public void sendEmailAndAudit(Integer cancelId, Integer insuredId, SmAuditEmailDTO emailDTO) {

        SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(cancelId);
        log.info("发送邮件给办保险公司{}", emailDTO);
        //ut_代表是用户任务 _audit 代表审核节点
        if (StringUtils.equals(smCancel.getTaskKey(), EnumFlowInsCancel.SAFE_CENTER.getEventId())) {
            handlerApplyCancel(cancelId, insuredId);
            JwtUserInfo user = HttpRequestUtil.getUserOrThrowExp();
            Map<String, Object> map = Maps.newHashMap();
            map.put(WorkFlowConstants.VARS_AUDIT_RESULT, AuditResultEnum.PASS.getResult());
            activitiCommonService.completeTask(smCancel.getProcessInstanceId(), user.getJobNumber(), null, map);
        } else {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "当前流程不处于保险业务中心审核:" + smCancel.getTaskKey());
        }
        CancelEmailTemplateVO emailTemplateVO = emailDTO.getEmailTemplateVO();

        List<SmCancelFileGroupVO> cancelFile = getCancelFile(smCancel.getId(), smCancel.getInsuredId());
        if (emailTemplateVO != null) {
            smCancelEmailService.sendEmail(emailTemplateVO, cancelFile);
        }
    }

    public void jump2Center(Integer cancelId) {
        //            //如果当前是机构对接人审核 但是审核人是保险业务中心 先默认通过机构对接人的流程
        SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(cancelId);
        if (StringUtils.equals(smCancel.getTaskKey(), EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId())) {
            JwtUserInfo user = HttpRequestUtil.getUserOrThrowExp();
            Map<String, Object> map = Maps.newHashMap();
            map.put(WorkFlowConstants.VARS_AUDIT_RESULT, AuditResultEnum.PASS.getResult());
            activitiCommonService.completeTask(smCancel.getProcessInstanceId(), user.getJobNumber(),
                    "保险业务中心代操作", map);
        } else {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "当前环节不是机构对接人审核");
        }

    }

    @AdminAutoAuthQuery
    public void download(SmCancelQuery query, HttpServletResponse resp) {
        List<WxCancelPolicyVO> wxCancelPolicy = adminAuthQuery(query);
        if (CollectionUtils.isEmpty(wxCancelPolicy)) {
            return;
        }

        dictSex(wxCancelPolicy);

        List<String> proInstIds = wxCancelPolicy.stream().map(WxCancelPolicyVO::getProcessInstanceId).collect(Collectors.toList());
        // 获取流程实例各个节点的最后一次处理时间
        Map<String, ActTaskEndTime> taskTimeMap = LambdaUtils.toMap(activitiCommonService.listTaskEndTime(proInstIds),
                tp -> tp.getProcInstId() + "-" + tp.getTaskDefKey());
        ActTaskEndTime nullable = new ActTaskEndTime();
        List<SmCancelExcelVO> collect = wxCancelPolicy.stream()
                .map(po -> {
                    po.setTaskKey(EnumFlowInsCancel.dict(po.getTaskKey()));
                    SmCancelExcelVO smCancelExcelVO = new SmCancelExcelVO();
                    BeanUtils.copyProperties(po, smCancelExcelVO);
                    //翻译退保状态
                    smCancelExcelVO.setCancelStateDesc(EnumCancelState.dict(po.getCancelState()));
                    //获取各个环节的最后处理时间
                    smCancelExcelVO.setOrgAuditTime(
                            taskTimeMap.getOrDefault(po.getProcessInstanceId() + "-" + EnumFlowInsCancel.ORG_ADMIN_AUDIT.getEventId(), nullable).getEndTime());
                    smCancelExcelVO.setCenterAuditTime(
                            taskTimeMap.getOrDefault(po.getProcessInstanceId() + "-" + EnumFlowInsCancel.SAFE_CENTER.getEventId(), nullable).getEndTime());
                    //修改产品名称为 产品+计划
                    smCancelExcelVO.setProductName(po.getProductName() + "-" + po.getPlanName());
                    return smCancelExcelVO;
                }).collect(Collectors.toList());
        List<String> ex = new ArrayList<>();
        if (Objects.nonNull(query.getTaskKey()) &&
                !Objects.equals(query.getTaskKey(), EnumFlowInsCancel.ST_SEND_FILE.getEventId())) {
            ex.add("toCompanyTime");
        }
        if (Boolean.TRUE.equals(query.getNoEnd())) {
            ex.add("cancelEndTime");
        }
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition",
                    "attachment; filename=" + URLEncoder.encode("退保列表", StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
            resp.setContentType("application/octet-stream");
            ExcelBuilderUtil.newInstance()
                    .createSheet("退保列表")
                    .buildSheetHead(SmCancelExcelVO.class, ex)
                    .addSheetData(collect)
                    .write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }


    /**
     * 打包下载退保文件
     *
     * @param cancelId
     */
    public void downloadCancelFile(Integer cancelId, HttpServletResponse resp) {

        SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(cancelId);
        List<SmCancelFile> smCancelFiles = fileMapper.selectByCancelId(cancelId);
        try {
            resp.reset();
            resp.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(smCancel.getCancelNo() + ".zip", StandardCharsets.UTF_8.name()));
            resp.setContentType("multipart/form-data");
            @Cleanup ZipOutputStream zos = new ZipOutputStream(resp.getOutputStream());
            smCancelFiles.stream().collect(Collectors.groupingBy(SmCancelFile::getFileTypeName))
                    .entrySet().forEach(dc -> {
                try {
                    List<SmCancelFile> value = dc.getValue();
                    for (int i = 0; i < value.size(); i++) {
                        SmCancelFile smCancelFile = value.get(i);
                        zos.putNextEntry(new ZipEntry(smCancelFile.getFileTypeName() + "_" + i + ".jpg"));
                        zos.write(CommonUtil.getBytesFromUrl(smCancelFile.getFileUrl()));
                        zos.closeEntry();
                    }
                } catch (Exception e) {
                    log.error("下载退保文件失败", e);
                }
            });
        } catch (Exception e) {
            log.warn("下载退保文件失败", e);
        }
    }

    /**
     * 强制跳转到补充资料
     *
     * @param cancelId
     * @param jumpReason
     */
    public void jumpUtApply(Integer cancelId, ActJumpDTO jumpReason, String userJobNumber) {

        activitiCommonService.jump(jumpReason.getProcessInstanceId(),
                EnumFlowInsCancel.UT_APPLY.getEventId(),
                jumpReason.getJumpReason(), userJobNumber);
    }

    /**
     * 刷新状态
     *
     * @param cancelNo
     * @return
     */
    public SmCancel refState(String cancelNo, String userJobNumber) {

        log.info("开始刷新退保状态！！！！！{}", cancelNo);
        SmCancel smCancel = cancelMapper.selectByCancelNo(cancelNo);

        Map<String, String> status = orderCoreService.updateOrderPolicyInfo(smCancel.getFhOrderId());
        log.info("开始刷新退保完成！！！！！{}", status);
        if (CollectionUtils.isEmpty(status)) {
            return smCancel;
        }
        SmOrderInsuredVO orderInsuredByInsId = orderMapper.getOrderInsuredByInsId(smCancel.getInsuredId());
        String stat = status.get(orderInsuredByInsId.getIdNumber());
        //当前保单退保成功
        SmCancel update = new SmCancel();
        update.setId(smCancel.getId());

        Map<String, Object> pas = new HashMap<>(2);
        if (Objects.equals(stat, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            log.info("退保成功{}", cancelNo);
            update.setState(EnumCancelState.SUCCESS.getKey());
            update.setEndTime(LocalDateTime.now());
            //如果没有流程id 不需要执行流程引擎
            if (StringUtils.isNotBlank(smCancel.getProcessInstanceId())) {
                pas.put("cancelState", "success");
                activitiCommonService.completeTask(smCancel.getProcessInstanceId(), userJobNumber,
                        "退保成功", pas);
            } else {
                update.setTaskKey(EnumFlowInsCancel.CANCEL_END.getEventId());
            }
            smCancel.setState(update.getState());
            smCancel.setEndTime(LocalDateTime.now());
            smCancel.setTaskKey(EnumFlowInsCancel.CANCEL_END.getEventId());

            cancelMapper.updateByPrimaryKeySelective(update);
            SpringFactoryUtil.getBean(EventBusEngine.class)
                    .publish(new SmCancelSuccessEvent(smCancel.getInsuredId()));
        } else if (Objects.equals(stat, SmConstants.POLICY_STATUS_CANCEL_FAIL)) {
            // 退保失败
            if (Objects.equals(smCancel.getState(), EnumCancelState.FAIL.getKey())) {
                return smCancel;
            }
            if (StringUtils.isNotBlank(smCancel.getProcessInstanceId())) {
                pas.put("cancelState", "fail");
                pas.put("failCount", 1);
                activitiCommonService.completeTask(smCancel.getProcessInstanceId(), userJobNumber, "退保失败", pas);
            }
            update.setState(EnumCancelState.FAIL.getKey());
            smCancel.setState(update.getState());
            cancelMapper.updateByPrimaryKeySelective(update);
        }
        return smCancel;

    }

    /**
     * 上传批单
     *
     * @param dto
     * @return
     */
    public SmCancel uploadCompanyCn(SmCancelCompanyDTO dto) {
        SmCancel update = new SmCancel();
        update.setId(dto.getCancelId());
        update.setCompanyCn(dto.getCompanyCn());
        update.setCompanyCnDesc(dto.getCompanyCnDesc());
        update.setCompanyCnCreateTime(LocalDateTime.now());
        update.setCompanyCnCreator(HttpRequestUtil.getUserId());
        if (cancelMapper.updateByPrimaryKeySelective(update) > 0) {
            SmCancel smCancel = cancelMapper.selectByPrimaryKeyMustExists(dto.getCancelId());
            //推送上传批单事件
            SpringFactoryUtil.getBean(EventBusEngine.class)
                    .publishSync(new WxCancelNotifyEvent(smCancel.getProcessInstanceId(),
                            EnumFlowInsCancel.COMPANY_CN_UPLOAD.getEventId(), true));
            return smCancel;
        }
        throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "退保流程不存在:" + dto.getCancelId());
    }


//    @SentinelResource(value = "cancel.testSentinel", blockHandler = "testSentinelExp")
    public SmCancel testSentinel(Integer id) {

        try {
            Thread.sleep(RandomUtils.nextLong(1, 10000));
            return cancelMapper.selectByPrimaryKeyMustExists(id);
        } catch (InterruptedException e) {
            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
        }
    }

//    public SmCancel testSentinelExp(Integer id, BlockException ex) {
//        log.warn(" testSentinelBlockHandlerInteger {}", id);
//        log.warn("testSentinelBlockHandlerInteger", ex);
//        throw new BizException(ExcptEnum.SENTINEL_ERROR);
//    }

    private void dictCompany(List<WxCancelPolicyVO> list) {

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<CompanyVO> companyList = companyMapper.listCompanys(null);
        if (CollectionUtils.isEmpty(companyList)) {
            return;
        }
        Map<Integer, CompanyVO> companyMap = LambdaUtils.safeToMap(companyList, CompanyVO::getId);
        for (WxCancelPolicyVO entry : list) {
            Integer companyId = entry.getCompanyId();
            CompanyVO company = companyMap.get(companyId);
            if (company != null) {
                entry.setCompanyCode(company.getCompanyIdentifier());
            }
        }

    }

    private void dictSex(List<WxCancelPolicyVO> list) {

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, SmCompanySettingVO> setting = cmpySettingService.dictQuery(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);

        list.forEach(vo -> {
            //翻译
            vo.setApplicantPersonGender(
                    Optional.ofNullable(setting.get(vo.getCompanyId() + "-" + vo.getApplicantPersonGender()
                            + "-" + vo.getChannel()))
                            .map(SmCompanySettingDTO::getOptionName)
                            .orElse(""));

            vo.setInsuredPersonGender(
                    Optional.ofNullable(setting.get(vo.getCompanyId() + "-" + vo.getInsuredPersonGender() + "-" + vo.getChannel()))
                            .map(SmCompanySettingDTO::getOptionName).orElse(""));

        });
    }


    /**
     * 获取下个退保单号
     *
     * @return
     */
    private String getNextCancelNo() {
        String tds = DateUtil.format(new Date(), BaseConstants.DATE_FORMAT_YYYYMMDD);
        String redisClaimKey = SmConstants.REDIS_KEY_CANCEL_NO + tds;
        Integer oldNo = redisUtil.get(redisClaimKey);
        if (oldNo == null) {
            DLockTemplate lockTemplate = SpringFactoryUtil.getBean(DLockTemplate.class);
            String dLockKey = "cancel:no" + tds;
            try {
                lockTemplate.lock(dLockKey, 2);
                Integer maxNo = cancelMapper.getMaxNo();
                maxNo = Objects.isNull(maxNo) ? 0 : maxNo;
                redisUtil.setnx(redisClaimKey, maxNo);
                redisUtil.expire(redisClaimKey, CommonUtil.getTodayNextSeconds());
            } finally {
                lockTemplate.unLock(dLockKey);
            }
        }
        long newNo = redisUtil.increment(redisClaimKey, 1);
        return String.format(SmConstants.CANCEL_NO_PREFIX + tds + "%03d", newNo);
    }


    private void validData(WxCancelDTO dto) {
        SmCancel query = new SmCancel();
        query.setInsuredId(dto.getInsuredId());
        List<SmCancel> select = cancelMapper.select(query);

        if (select.stream().anyMatch(can -> !Objects.equals(can.getState(), EnumCancelState.CANCELED.getKey()))) {
            log.warn("重复申请退保数据:SmCancel.apply.dup {}", dto);
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该订单已申请退保");
        }

    }
    public void applyCancelValid(Integer insuredId) {
        List<SmOrderInsured> smOrderInsureds = smOrderInsuredMapper.getAllByInsuredId(insuredId);
        List<SmCancel> smCancels = Lists.newArrayList();
        for (SmOrderInsured smOrderInsured : smOrderInsureds) {
            SmCancel query = new SmCancel();
            query.setInsuredId(smOrderInsured.getId());
            smCancels.addAll(cancelMapper.select(query));
        }

        if (smCancels.stream().anyMatch(can -> !Objects.equals(can.getState(), EnumCancelState.CANCELED.getKey()))) {
            log.warn("重复申请退保数据:smOrderInsureds = {}", smOrderInsureds);
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该保单已发起退保申请，请勿重复申请");
        }
    }
}
