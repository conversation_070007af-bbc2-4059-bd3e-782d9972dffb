package com.cfpamf.ms.insur.admin.validation;

import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.TmpOrderValidPolicyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SmOrderCommissionDetailMapper;
import com.cfpamf.ms.insur.admin.enums.EnumInsuredAppStatus;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.po.commission.SmCommissionDetailPO;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmCommonSettingService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.validation.order.SexAndBirthdayValidation;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.constant.SmConstants.ORDER_PREFIX_IMPORT;

/**
 * <AUTHOR>
 * @description
 * @date 2021/5/20 10:59 下午
 * @Version 1.0
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Slf4j
public class SmCarOrderValidation extends OrderCommonValidator {
    @Autowired
    Validator validator;
    @Autowired
    SmProductMapper productMapper;
    @Autowired
    AuthUserMapper authUserMapper;
    @Autowired
    SmCommonSettingService commonSettingService;
    @Autowired
    TmpOrderValidPolicyMapper tmpOrderValidPolicyMapper;
    @Autowired
    SmOrderCommissionDetailMapper smOrderCommissionDetailMapper;
    @Autowired
    OrderNoGenerator orderNoGenerator;
    @Autowired
    UserService userService;
    @Autowired
    private SexAndBirthdayValidation sexAndBirthdayValidation;

    private static String PERCENT = "100";

    /**
     * 车险订单导入规则校验逻辑
     * 校验导入数据 以及翻译中文成码值
     *
     * @param request
     * @param data
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ValidationResult<SmCarOrderExcelDTO>> validCarOrderAndMapper(SmOrderImportDTO request, List<SmCarOrderExcelDTO> data) {
        /**
         * 1.基础校验逻辑，包括一些基础数据的格式，判空等等
         */
        List<ValidationResult<SmCarOrderExcelDTO>> validResultList = data.stream()
                .map(order -> {
                    return validateBaseData(request, order);
                })
                .collect(Collectors.toList());

        Map<Boolean, List<ValidationResult<SmCarOrderExcelDTO>>> baseResMap = validResultList.stream()
                .collect(Collectors.partitioningBy(ValidationResult::isSuccess));


        List<ValidationResult<SmCarOrderExcelDTO>> success = baseResMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
        List<SmCarOrderExcelDTO> successData = success.stream()
                .map(ValidationResult::getSource)
                .collect(Collectors.toList());
        //校验本人信息
        validateRelation(success);
        //验证计划
        validatePlan(request.getChannel(), baseResMap, success, successData);
        //校验上下文
        ValidContext<SmCarOrderExcelDTO> context = new ValidContext(request.getChannel(), validResultList);
        //校验保费
        validProductBaseConfig4Car(context);
        //验证推荐人
        validateRecommender(success);
        //校验性别生日
        sexAndBirthdayValidation.validSexAndBirthdayCar(validResultList);
        //码值校验
        validateCodeValue(success);
        //保单重复性校验
        validatePolicy(request, success);
        //公共信息校验
        validPublic(context.getValidationData());
        return validResultList;
    }

    /**
     * 校验关系
     *
     * @param success
     */
    private void validateRelation(List<ValidationResult<SmCarOrderExcelDTO>> success) {
        if (CollectionUtils.isEmpty(success)) {
            return;
        }
        //选择关系为“本人”时，校验：投保人姓名+证件号与被保人姓名+证件必须一致；
        //选择关系不为“本人”时，校验：投保人证件号与被保人证件号不能一致；
        success.forEach(
                validation -> {
                    SmCarOrderExcelDTO dto = validation.getSource();
                    if (StringUtils.isEmpty(dto.getInsuredRelationship()) || StringUtils.isEmpty(dto.getApplicantName()) || StringUtils.isEmpty(dto.getApplicantIdNumber())) {
                        return;
                    }
                    if (Objects.equals(dto.getInsuredRelationship(), "本人")) {
                        if (!Objects.equals(dto.getApplicantName(), dto.getInsuredName()) || !Objects.equals(dto.getInsuredIdNumber(), dto.getApplicantIdNumber())) {
                            validation.addMessage("关系为本人，投被保人必须一致");
                        }
                    } else {
                        if (Objects.equals(dto.getInsuredIdNumber(), dto.getApplicantIdNumber())) {
                            validation.addMessage("关系非本人" + "投被保人证件号不能一致");
                        }
                    }
                }
        );
    }

    /**
     *
     * @param dto
     * @param success
     * @张健@申佳 [2023-01-01]车险导入规则变更：历史逻辑是同一个保单的被保人不能重复，现在改成保单号不能重复
     */
    public void validatePolicy(SmOrderImportDTO dto, List<ValidationResult<SmCarOrderExcelDTO>> success) {
        if (CollectionUtils.isEmpty(success)) {
            return;
        }
        int size = success.size();
        Map<String, TmpOrderValidPolicy> policyMap = new HashMap<>(size);
        Map<String, Integer> policyNumberMap = new HashMap<>(size);

        /**
         * 1. 导入数据重复性校验，如果某个保单号有重复记录，则该保单号不导入
         */
        success.stream().forEach(order -> {
            SmCarOrderExcelDTO source = order.getSource();
            String policyNo = source.getPolicyNo();
            Integer number = policyNumberMap.get(policyNo);
            if (number == null) {
                policyNumberMap.put(policyNo, 1);
            } else {
                policyNumberMap.put(policyNo, number + 1);
            }
        });
        String batchNo = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("生成导入的批次号：{}", batchNo);
        /**
         * 1. 判断当前excel中是否有保单重复的数据；
         */
        success.forEach(order -> {
            SmCarOrderExcelDTO source = order.getSource();
            String policyNo = source.getPolicyNo();
            Integer number = policyNumberMap.get(policyNo);
            if (number != null && number > 1) {
                String errorMsg = "保单号重复：重复序号[%s],重复的保单号：[%s]";
                order.addMessage(String.format(errorMsg, source.getNo(), source.getPolicyNo()));
            } else {
                TmpOrderValidPolicy validPolicy = new TmpOrderValidPolicy();
                validPolicy.setBatch(batchNo);
                validPolicy.setFhOrderId(source.getOrderNo());
                validPolicy.setInsuredIdNumber(source.getInsuredIdNumber());
                validPolicy.setPolicyNo(source.getPolicyNo());
                validPolicy.setNo(source.getNo());
                policyMap.put(policyNo, validPolicy);
            }
        });

        List<String> policyNoList = success.stream()
                .map(ValidationResult::getSource)
                .map(SmCarOrderExcelDTO::getPolicyNo)
                .distinct()
                .collect(Collectors.toList());
        /**
         * 2. 与系统已有的保单对比判重
         */
        if (!CollectionUtils.isEmpty(policyMap)) {
            //泰康套交的需要根据子保单号查
            List<TmpOrderValidPolicy> policy = tmpOrderValidPolicyMapper.selectExistsOnlyDb(policyNoList);
            Map<String, TmpOrderValidPolicy> dbPolicyMap = LambdaUtils.safeToMap(policy, TmpOrderValidPolicy::getPolicyNo);
            log.info("有与数据库重复的保单:{}", policy);
            success.stream().forEach(order -> {
                /**
                 * 如果有保单号一样的就为他赋值对应数据库的订单号
                 * 后续逻辑需要验证已经存在的保单号，与当前导入的车牌号是否一直，而不能直接更新保单数据
                 * 2023-01-16:导入的数据和数据库的保单数据做对比，判断是否有重复的保单号
                 * @申佳 同一个保单号只能导入一次，多次导入，系统直接返回错误。
                 */
                SmCarOrderExcelDTO excelVo = order.getSource();
                String policyNo = excelVo.getPolicyNo();
                String appStatus = excelVo.getAppStatus();
                TmpOrderValidPolicy dbPolicy = dbPolicyMap.get(policyNo);

                /**
                 * 承保成功数据导入校验规则
                 */
                if (Objects.equals(SmConstants.POLICY_STATUS_SUCCESS, appStatus)) {
                    if (dbPolicy != null) {
                        String msg = "系统已存在该保单，请勿重复导入，重复的保单号：[%s]";
                        order.addMessage(String.format(msg, policyNo));
                        if(Objects.equals(SmConstants.POLICY_STATUS_SUCCESS, dbPolicy.getAppStatus())){
                            order.addMessage("新契约保单，不允许分多次导入。");
                        }
                    }
                }
                /**
                 * 退保成功数据导入校验规则
                 */
                if (Objects.equals(SmConstants.POLICY_STATUS_CANCEL_SUCCESS, appStatus)) {
                    if (dbPolicy == null) {
                        String msg = "系统不存在该保单的承保记录，不能导入退保保单，保单号：[%s]";
                        order.addMessage(String.format(msg, policyNo));
                    }else if(!Objects.equals(SmConstants.POLICY_STATUS_SUCCESS, dbPolicy.getAppStatus())){
                        String msg = "系统已存在该保单的退保记录，不能重复导入，保单号：[%s]";
                        order.addMessage(String.format(msg, policyNo));
                    }
                }
                if (dbPolicy != null) {
                    excelVo.setOrderNo(dbPolicy.getDbFhOrderId());
                    excelVo.setPolicyDbExists(true);
                }
            });
        }

        /**
         * 校验成功的数据，去数据库做一次判断
         */
        if (dto.isAutoOrder() && !CollectionUtils.isEmpty(success)) {
            log.info("开始自动为保单号生成一个订单号");

            List<String> batchOrderNo = orderNoGenerator.getBatchNo(dto.getChannel(), policyNoList.size());
            HashMap<String, String> maps = Maps.newHashMap();
            for (int i = 0; i < policyNoList.size(); i++) {
                //加个前缀标记下
                maps.put(policyNoList.get(i), ORDER_PREFIX_IMPORT + batchOrderNo.get(i));
            }
            log.info("开始自动为没有订单号的数据设置一个订单号");
            success.stream().map(ValidationResult::getSource)
                    .filter(excelDTO -> Objects.isNull(excelDTO.getOrderNo()))
                    .forEach(excel -> excel.setOrderNo(maps.get(excel.getPolicyNo())));
        }
    }


    /**
     * 验证推荐人
     *
     * @param success
     */
    public void validateRecommender(List<ValidationResult<SmCarOrderExcelDTO>> success) {
        List<String> recommendUserIds = success.stream()
                .map(ValidationResult::getSource)
                .map(SmCarOrderExcelDTO::getRecommendUserId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        log.info("开始校验推荐人工号:{}", recommendUserIds.size());
        if (!CollectionUtils.isEmpty(recommendUserIds)) {
            List<UserVO> userVOS = authUserMapper.listUsersByPageWithJobNumbers(recommendUserIds);
            Map<String, UserVO> userMap = LambdaUtils.safeToMap(userVOS, UserVO::getUserId);
            //数量不匹配
            AtomicInteger errCount = new AtomicInteger();
            success.stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getSource().getRecommendUserId()))
                    .forEach(
                            entry -> {
                                SmCarOrderExcelDTO order = entry.getSource();
                                String id = order.getRecommendUserId();
                                String name = order.getRecommendName();
                                UserVO user = userMap.get(id);
                                if (user == null) {
                                    entry.addMessage("推荐人工号填写错误，匹配不到:" + order.getRecommendUserId());
                                    errCount.incrementAndGet();
                                    return;
                                }
                                if (!Objects.equals(user.getUserName(), name)) {
                                    entry.addMessage("推荐人工号与名字不匹配");
                                    errCount.incrementAndGet();
                                    return;
                                }
                            }
                    );
            log.info("工号不存在数据{}", errCount.get());
        }
    }

    /**
     * 验证计划id
     *
     * @param queryChannel
     * @param baseResMap
     * @param success
     * @param successData
     */
    public void validatePlan(String queryChannel, Map<Boolean, List<ValidationResult<SmCarOrderExcelDTO>>> baseResMap, List<ValidationResult<SmCarOrderExcelDTO>> success, List<SmCarOrderExcelDTO> successData) {

        // 所有计划
        List<String> fhProductIds = successData.stream()
                .map(SmCarOrderExcelDTO::getFhProductId)
                .distinct()
                .collect(Collectors.toList());

        log.info("开始校验计划id是配置:{}", fhProductIds);
        //校验计划id
        if (CollectionUtils.isEmpty(fhProductIds)) {
            return;
        }
        List<SmPlanVO> planList = productMapper.listPlanByFhProductIds(queryChannel, fhProductIds);
        Map<String, SmPlanVO> planMap = LambdaUtils.safeToMap(planList, SmPlanVO::getFhProductId);
        //校验计划id
        Map<Boolean, List<ValidationResult<SmCarOrderExcelDTO>>> planValidMap = success
                .stream()
                .collect(Collectors.partitioningBy(order -> {
                    SmPlanVO smPlanVO = planMap.get(order.getSource().getFhProductId());
                    if (Objects.nonNull(smPlanVO)) {
                        order.getSource().setCompanyId(smPlanVO.getCompanyId());
                        order.getSource().setProductId(smPlanVO.getProductId());
                        order.getSource().setPlanId(smPlanVO.getPlanId());
                        return true;
                    }
                    return false;
                }));
        success = planValidMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
        List<ValidationResult<SmCarOrderExcelDTO>> planIdNotExists = planValidMap.get(Boolean.FALSE);
        planIdNotExists.forEach(s -> s.addMessage("系统中计划id未配置:" + s.getSource().getFhProductId()));
        baseResMap.get(Boolean.FALSE).addAll(planIdNotExists);
    }

    /**
     * excel表格数据验证
     *
     * @param dto
     * @param order
     * @return
     */
    public ValidationResult<SmCarOrderExcelDTO> validateBaseData(SmOrderImportDTO dto, SmCarOrderExcelDTO order) {
        //订单号全部设置成空
        order.setOrderNo(null);
        //如果时间不带时分秒的时候
        if (Objects.nonNull(order.getStartTime()) && order.getStartTime().length() == 10) {
            order.setStartTime(order.getStartTime() + " 00:00:00");
        }
        if (Objects.nonNull(order.getEndTime()) && order.getEndTime().length() == 10) {
            order.setEndTime(order.getEndTime() + " 23:59:59");
        }
        //第一次校验
        Set<ConstraintViolation<SmCarOrderExcelDTO>> validate = validator.validate(order);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String collect = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            return new ValidationResult<>(collect, order);
        }
        ValidationResult<SmCarOrderExcelDTO> success = ValidationResult.success(order);

        //产品渠道不匹配
        if (!Objects.equals(order.getChannel(), dto.getChannelName())) {
            success.addMessage("选择的产品渠道和导入的渠道不一致");
        }
        //验证订单金额
        validateOrderAmt(order, success);

        //验证身份
        validateIdNumber(order, success);

        //校验保单状态
        if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.SUCCESS.getDesc())) {
            order.setAppStatus(EnumInsuredAppStatus.SUCCESS.getCode());
        } else if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.CANCEL_SUCCESS.getDesc())) {
            order.setAppStatus(EnumInsuredAppStatus.CANCEL_SUCCESS.getCode());
        } else {
            success.addMessage("只能导入退保成功和承包成功的订单");
        }
        //校验子渠道信息
        if (Objects.equals(order.getSubChannel(), EnumOrderSubChannel.XIANGZHU.getDesc())) {
            order.setSubChannel(EnumOrderSubChannel.XIANGZHU.getCode());
        } else if (Objects.equals(order.getSubChannel(), EnumOrderSubChannel.CAPP.getDesc())) {
            order.setSubChannel(EnumOrderSubChannel.CAPP.getCode());
        } else {
            order.setSubChannel(EnumOrderSubChannel.IMPORT.getCode());
        }
        //校验失效时间与保单生效时间
        if (order.getEndTime().compareTo(order.getStartTime()) < 1) {
            success.addMessage("保单失效时间不能早于保单生效时间");
        }
        //校验时间格式
        validate = validator.validate(order, SmCarOrderExcelDTO.InitAfter.class);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String collect = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            success.addMessage(collect);
        }
        return success;
    }

    /**
     * 验证订单相关金额计算
     *
     * @param order
     * @param success
     */
    public void validateOrderAmt(SmCarOrderExcelDTO order, ValidationResult<SmCarOrderExcelDTO> success) {
        if (order.getAmount() != null && BigDecimal.ZERO.compareTo(order.getAmount()) >= 0) {
            success.addMessage("订单金额不正确");
            return;
        }

        BigDecimal carBoatTax = BigDecimal.ZERO;
        if (order.getCarBoatTax() != null) {
            carBoatTax = order.getCarBoatTax();
        }
        BigDecimal premium = BigDecimal.ZERO;
        if (order.getPremium() != null) {
            premium = order.getPremium();
        }
        if (order.getAmount().compareTo(premium.add(carBoatTax)) != 0) {
            success.addMessage("订单金额不等于保费+车船税");
        }
        //支付佣金
        BigDecimal paymentAmount = BigDecimal.ZERO;
        BigDecimal paymentRate = BigDecimal.ZERO;
//        if (order.getPaymentAmount() != null) {
//            paymentAmount = order.getPaymentAmount();
//        }
        if (order.getPaymentRate() != null) {
            paymentRate = order.getPaymentRate();
            paymentAmount = calcAmt(premium,paymentRate);
        } else {
            order.setPaymentRate(BigDecimal.ZERO);
        }
        order.setPaymentAmount(paymentAmount);
//        if (paymentAmount.compareTo(calcAmt(premium, paymentRate)) != 0) {
//            success.addMessage("支付佣金计算错误");
//        }
        //结算佣金
        BigDecimal settleAmount = BigDecimal.ZERO;
        BigDecimal settleRate = BigDecimal.ZERO;
//        if (order.getSettlementAmount() != null) {
//            settleAmount = order.getSettlementAmount();
//        }
        if (order.getSettlementRate() != null) {
            settleRate = order.getSettlementRate();
            settleAmount = calcAmt(premium, settleRate);
        } else {
            order.setSettlementRate(BigDecimal.ZERO);
        }
        order.setSettlementAmount(settleAmount);
//        if (settleAmount.compareTo(calcAmt(premium, settleRate)) != 0) {
//            success.addMessage("结算佣金计算错误");
//        }

        //加佣佣金
        BigDecimal addAmount = BigDecimal.ZERO;
        BigDecimal addRate = BigDecimal.ZERO;
//        if (order.getAddAmount() != null) {
//            addAmount = order.getAddAmount();
//        }
        if (order.getAddRate() != null) {
            addRate = order.getAddRate();
            addAmount = calcAmt(premium, addRate);
        }
        order.setAddAmount(addAmount);
//        if (addAmount.compareTo(calcAmt(premium, addRate)) != 0) {
//            success.addMessage("加佣佣金计算错误");
//        }


    }

    private BigDecimal calcAmt(BigDecimal amount, BigDecimal rate) {
        return amount.multiply(rate).divide(new BigDecimal(PERCENT)).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 验证身份证
     *
     * @param order
     */
    public void validateIdNumber(SmCarOrderExcelDTO order, ValidationResult<SmCarOrderExcelDTO> success) {
        //证件类型 默认为身份证
        if (StringUtils.isBlank(order.getApplicantIdType())) {
            order.setApplicantIdType(SmOrderExcelDTO.ID_CARD_TYPE);
        }
        if (StringUtils.isBlank(order.getInsuredIdType())) {
            order.setInsuredIdType(SmOrderExcelDTO.ID_CARD_TYPE);
        }
        if (StringUtils.isBlank(order.getOwnerIdType())) {
            order.setOwnerIdType(SmOrderExcelDTO.ID_CARD_TYPE);
        }

        // 如果是身份证校验证件号码 然后设置性别年龄
        if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getApplicantIdNumber();
            if (StringUtils.isNotBlank(idNumber) && !IdcardUtils.validateCard(idNumber)) {
                success.addMessage("投保人证件号码有误");
            }
        }
        /**
         * S59迭代：去除车险被保人证件号为空的校验
         * author：zenghuaguang
         * 修订时间：2022-04-11
         */
        if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getInsuredIdNumber();
            if (StringUtils.isNotBlank(idNumber) && !IdcardUtils.validateCard(idNumber)) {
                success.addMessage("被保人证件号码有误");
            }
        }
        if (Objects.equals(order.getOwnerIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getOwnerIdNumber();
            if (StringUtils.isNotBlank(idNumber) && !IdcardUtils.validateCard(idNumber)) {
                success.addMessage("车主证件号码有误");
            }
        }

        //证件类型为其他时，校验证件号码是否以“XN”开头
        String reg = "^[Xx][Nn].*$";
        if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
            String idNumber = order.getApplicantIdNumber();
            if (StringUtils.isNotBlank(idNumber) && !idNumber.matches(reg)) {
                success.addMessage("投保人证件号码不是“XN”开头");
            }
        }
        if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
            String idNumber = order.getInsuredIdNumber();
            if (StringUtils.isNotBlank(idNumber) && !idNumber.matches(reg)) {
                success.addMessage("被保人证件号码不是“XN”开头");
            }
        }
    }


    /****************************************************************/
    /******************* begin 验证车险佣金导入*************************/
    /****************************************************************/
    /**
     * 校验导入数据 以及翻译中文成码值
     *
     * @param dto
     * @param extractOrder
     */

    @Transactional(rollbackFor = Exception.class)
    public List<ValidationResult<SmCarOrderCommissionExcelDTO>> validCarOrderCommissionAndMapper(SmOrderImportDTO dto, List<SmCarOrderCommissionExcelDTO> extractOrder) {


        //基本校验  true --> success
        List<ValidationResult<SmCarOrderCommissionExcelDTO>> allRes = extractOrder.stream()
                .map(order -> {
                    //excel数据基础校验
                    return validateCommissionBaseData(dto, order);
                }).collect(Collectors.toList());
        Map<Boolean, List<ValidationResult<SmCarOrderCommissionExcelDTO>>> baseResMap = allRes.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));
        //成功记录
        List<ValidationResult<SmCarOrderCommissionExcelDTO>> success = baseResMap.getOrDefault(Boolean.TRUE, Collections.emptyList());

        //验证推荐人信息
        validateCommissionRecommender(success);
        //验证保单信息
        validateCommissionPolicy(success);

        return allRes;
    }
    /****************************************************************/
    /******************* begin 验证团险佣金导入*************************/
    /****************************************************************/
    /**
     * 校验导入数据 以及翻译中文成码值
     *
     * @param dto
     * @param extractOrder
     */

    @Transactional(rollbackFor = Exception.class)
    public List<ValidationResult<SmGroupOrderCommissionExcelDTO>> validGroupOrderCommissionAndMapper(SmOrderImportDTO dto, List<SmGroupOrderCommissionExcelDTO> extractOrder) {

        //基本校验  true --> success
        List<ValidationResult<SmGroupOrderCommissionExcelDTO>> allRes = extractOrder.stream()
                .map(order -> {
                    //excel数据基础校验
                    return validateGroupCommissionBaseData(dto, order);
                }).collect(Collectors.toList());
        Map<Boolean, List<ValidationResult<SmGroupOrderCommissionExcelDTO>>> baseResMap = allRes.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));
        //成功记录
        List<ValidationResult<SmGroupOrderCommissionExcelDTO>> success = baseResMap.getOrDefault(Boolean.TRUE, Collections.emptyList());

        //验证保单信息
        validateGroupCommissionPolicy(success);

        return allRes;
    }


    /**
     * excel表格数据验证
     *
     * @param dto
     * @param order
     * @return
     */
    public ValidationResult<SmCarOrderCommissionExcelDTO> validateCommissionBaseData(SmOrderImportDTO dto, SmCarOrderCommissionExcelDTO order) {
        //订单号全部设置成空
        order.setOrderNo(null);

        //第一次校验
        Set<ConstraintViolation<SmCarOrderCommissionExcelDTO>> validate = validator.validate(order);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String collect = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            return new ValidationResult<>(collect, order);
        }
        ValidationResult<SmCarOrderCommissionExcelDTO> success = ValidationResult.success(order);

        return success;
    }
    /**
     * excel表格数据验证
     *
     * @param dto
     * @param order
     * @return
     */
    public ValidationResult<SmGroupOrderCommissionExcelDTO> validateGroupCommissionBaseData(SmOrderImportDTO dto, SmGroupOrderCommissionExcelDTO order) {
        //订单号全部设置成空
        order.setOrderNo(null);

        //第一次校验
        Set<ConstraintViolation<SmGroupOrderCommissionExcelDTO>> validate = validator.validate(order);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String collect = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            return new ValidationResult<>(collect, order);
        }
        ValidationResult<SmGroupOrderCommissionExcelDTO> success = ValidationResult.success(order);

        return success;
    }


    /**
     * 车险佣金导入规则
     *
     * @param success
     */
    public void validateCommissionPolicy(List<ValidationResult<SmCarOrderCommissionExcelDTO>> success) {
        if (CollectionUtils.isEmpty(success)) {
            return;
        }

        //判断当前excel中是否有同保单重复被保人的数据
        List<TmpOrderValidPolicy> policyValid = new ArrayList<>();
        success.forEach(val -> {
            SmCarOrderCommissionExcelDTO source = val.getSource();
            Optional<TmpOrderValidPolicy> first = policyValid.stream()
                    .filter(s -> Objects.equals(s.getPolicyNo(), source.getPolicyNo()))
                    .findFirst();

            if (first.isPresent()) {
                String errorMsg = "保单号数据重复，重复序号:%s，重复的保单号：%s";
                val.addMessage(String.format(errorMsg, source.getNo(), source.getPolicyNo()));

            } else {
                TmpOrderValidPolicy validPolicy = new TmpOrderValidPolicy();
                validPolicy.setPolicyNo(source.getPolicyNo());
                validPolicy.setNo(source.getNo());
                policyValid.add(validPolicy);
            }
        });

        if (!CollectionUtils.isEmpty(policyValid)) {
            List<String> policyNos = success.stream()
                    .map(ValidationResult::getSource)
                    .map(SmCarOrderCommissionExcelDTO::getPolicyNo)
                    .distinct().collect(Collectors.toList());
            //泰康套交的需要根据子保单号查
            List<TmpOrderValidPolicy> policy = tmpOrderValidPolicyMapper.listSmOrderInsuredByPolicyNos(policyNos);
            List<SmCommissionDetailPO> detailPos = smOrderCommissionDetailMapper.getByPolicyNos(policyNos);
            if (!CollectionUtils.isEmpty(policy)) {
                log.info("保单存在:{}", policy);
                success.forEach(order -> {
                    //如果有保单号一样的就为他赋值对应数据库的订单号
                    //后续逻辑需要验证已经存在的保单号，与当前导入的车牌号是否一直，而不能直接更新保单数据
                    Optional<TmpOrderValidPolicy> opt = policy.stream().filter(
                            va -> Objects.equals(va.getPolicyNo(), order.getSource().getPolicyNo())
                    ).findFirst();
                    if (opt.isPresent()) {
                        order.getSource().setOrderNo(opt.get().getDbFhOrderId());
                        order.getSource().setProductId(opt.get().getProductId());
                        order.getSource().setPlanId(opt.get().getPlanId());
                        order.getSource().setPaymentTime(opt.get().getPaymentTime());
                        order.getSource().setInsuredIdNumber(opt.get().getInsuredIdNumber());

                        if (StringUtils.isBlank(order.getSource().getRecommendUserId())) {
                            order.getSource().setRecommendUserId(opt.get().getRecommendId());
                        }

                        if (opt.get().getTotalAmount().compareTo(order.getSource().getPremium()) == 0) {
                            order.getSource().setTotalAmount(opt.get().getTotalAmount());
                        } else {
                            order.addMessage("保费与系统不一致");
                        }
                    }

                    Optional<SmCommissionDetailPO> detailOpt = detailPos.stream().filter(
                            va -> Objects.equals(va.getPolicyNo(), order.getSource().getPolicyNo())
                    ).findFirst();
                    if (detailOpt.isPresent()){
                        if (Objects.isNull(detailOpt.get().getAccountTime())
                                || !DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT).equals(DateUtil.format(detailOpt.get().getAccountTime(), DateUtil.CN_YEAR_MONTH_FORMAT))) {
                            order.addMessage("跨月订单不能导入佣金");
                        }
                    }
                });
            }

            success.stream().forEach(order -> {
                if (StringUtils.isBlank(order.getSource().getOrderNo())) {
                    order.addMessage("保单号不存在");
                }
            });
        }
    }

    /**
     * 车险佣金导入规则
     *
     * @param success
     */
    public void validateGroupCommissionPolicy(List<ValidationResult<SmGroupOrderCommissionExcelDTO>> success) {
        if (CollectionUtils.isEmpty(success)) {
            return;
        }

        //判断当前excel中是否有同保单重复被保人的数据
        List<TmpOrderValidPolicy> policyValid = new ArrayList<>();
        success.forEach(val -> {
            SmGroupOrderCommissionExcelDTO source = val.getSource();
            Optional<TmpOrderValidPolicy> first = policyValid.stream()
                    .filter(s -> Objects.equals(s.getPolicyNo(), source.getOrderId()))
                    .findFirst();

            if (first.isPresent()) {
                String errorMsg = "订单号数据重复，重复序号:%s，重复的订单号：%s";
                val.addMessage(String.format(errorMsg, source.getNo(), source.getOrderId()));

            } else {
                TmpOrderValidPolicy validPolicy = new TmpOrderValidPolicy();
                validPolicy.setFhOrderId(source.getOrderId());
                validPolicy.setNo(source.getNo());
                policyValid.add(validPolicy);
            }
        });

        if (!CollectionUtils.isEmpty(policyValid)) {
            List<String> orderIds = success.stream()
                    .map(ValidationResult::getSource)
                    .map(SmGroupOrderCommissionExcelDTO::getOrderId)
                    .distinct().collect(Collectors.toList());
            //泰康套交的需要根据子保单号查
            List<TmpOrderValidPolicy> policy = tmpOrderValidPolicyMapper.listSmOrderInsuredByOrderIds(orderIds);
            List<SmCommissionDetailPO> detailPos = smOrderCommissionDetailMapper.getByOrderIds(orderIds);
            if (!CollectionUtils.isEmpty(policy)) {
                log.info("保单存在:{}", policy);
                success.forEach(order -> {
                    //如果有保单号一样的就为他赋值对应数据库的订单号
                    //后续逻辑需要验证已经存在的保单号，与当前导入的车牌号是否一直，而不能直接更新保单数据
                    Optional<TmpOrderValidPolicy> opt = policy.stream().filter(
                            va -> Objects.equals(va.getFhOrderId(), order.getSource().getOrderId())
                    ).findFirst();
                    if (opt.isPresent()) {
                        order.getSource().setOrderNo(opt.get().getDbFhOrderId());
                        order.getSource().setPolicyNo(opt.get().getPolicyNo());
                        order.getSource().setProductId(opt.get().getProductId());
                        order.getSource().setPlanId(opt.get().getPlanId());
                        order.getSource().setPaymentTime(opt.get().getPaymentTime());
                        order.getSource().setInsuredIdNumber(opt.get().getInsuredIdNumber());

                        if (StringUtils.isBlank(order.getSource().getRecommendUserId())) {
                            order.getSource().setRecommendUserId(opt.get().getRecommendId());
                        }

                        if (opt.get().getTotalAmount().compareTo(order.getSource().getPremium()) == 0) {
                            order.getSource().setTotalAmount(opt.get().getTotalAmount());
                        } else {
                            order.addMessage("保费与系统不一致");
                        }
                    }

                    Optional<SmCommissionDetailPO> detailOpt = detailPos.stream().filter(
                            va -> Objects.equals(va.getOrderId(), order.getSource().getOrderId())
                    ).findFirst();
                    if (detailOpt.isPresent()){
                        if (Objects.isNull(detailOpt.get().getAccountTime())
                                || !DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT).equals(DateUtil.format(detailOpt.get().getAccountTime(), DateUtil.CN_YEAR_MONTH_FORMAT))) {
                            order.addMessage("跨月订单不能导入佣金");
                        }
                    }
                });
            }

            success.stream().forEach(order -> {
                if (StringUtils.isBlank(order.getSource().getOrderNo())) {
                    order.addMessage("订单号不存在");
                }
            });
        }
    }

    public static void addRecommondUser(ValidationResult<SmCarOrderCommissionExcelDTO> valid, List<UserPost> userPostList, String recommendId) {
        if (!com.alibaba.druid.util.StringUtils.isEmpty(recommendId)) {
            Optional<UserPost> userPostOpt = userPostList.stream().filter(p -> Objects.equals(p.getJobNumber(), recommendId)).findFirst();
            if (userPostOpt.isPresent()) {
                UserPost userPost = userPostOpt.get();
                valid.getSource().setRecommendJobCode(userPost.getJobCode());
                valid.getSource().setRecommendMainJobNumber(userPost.getMainJobNumber());
                valid.getSource().setRecommendOrgCode(userPost.getOrgCode());

            }
        }
    }

    /**
     * 验证推荐人
     *
     * @param success
     */
    public void validateCommissionRecommender(List<ValidationResult<SmCarOrderCommissionExcelDTO>> success) {
        List<String> recommendUserIds = success.stream().map(ValidationResult::getSource)
                .map(SmCarOrderCommissionExcelDTO::getRecommendUserId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        log.debug("开始校验推荐人工号{}", recommendUserIds.size());
        if (!CollectionUtils.isEmpty(recommendUserIds)) {
            List<UserVO> userVOS = authUserMapper.listUsersByPageWithJobNumbers(recommendUserIds);
            Map<String, UserVO> userMap = LambdaUtils.safeToMap(userVOS, UserVO::getUserId);
            //数量不匹配
            AtomicInteger errCount = new AtomicInteger();

            //数量不匹配
            success.stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getSource().getRecommendUserId()))
                    .forEach(entry -> {
                        SmCarOrderCommissionExcelDTO order = entry.getSource();
                        String id = order.getRecommendUserId();
                        String name = order.getRecommendName();
                        UserVO user = userMap.get(id);
                        if (user == null) {
                            entry.addMessage("推荐人工号填写错误，匹配不到:" + order.getRecommendUserId());
                            errCount.incrementAndGet();
                            return;
                        }
                        if (!Objects.equals(user.getUserName(), name)) {
                            entry.addMessage("推荐人工号与名字不匹配");
                            errCount.incrementAndGet();
                            return;
                        }
                        order.setRecommendJobCode(user.getJobCode());
                        order.setRecommendOrgCode(user.getOrgCode());
                        order.setRecommendMainJobNumber(user.getMainJobNumber());
                    });
            log.debug("工号不存在数据{}", errCount.get());
        }
    }
}