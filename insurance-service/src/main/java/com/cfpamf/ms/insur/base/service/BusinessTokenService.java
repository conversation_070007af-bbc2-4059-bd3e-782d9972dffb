package com.cfpamf.ms.insur.base.service;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

/**
 * 业务token service
 * <p>
 * 重要的业务流程（保险下单）需要获取token进行api调用
 *
 * <AUTHOR>
 */
@Service
public class BusinessTokenService {

    /**
     * token有效时间
     */
    @Value("${author.token.expireTime}")
    private long tokenExpireTime;

    /**
     * Redis工具类
     */
    @Autowired
    private RedisUtil<String, String> redisUtil;

    /**
     * 分布式锁
     */
    @Autowired
    private DLockTemplate lockTemplate;

    @Autowired
    RedissonClient redissonClient;

    public RLock getLock(String key){
        return redissonClient.getLock(key);

    }

    /**
     * 获取业务token
     *
     * @param userId
     * @param businessCode
     * @return
     */
    public String getBusinessToken(String userId, String businessCode) {
        String cacheKey = getTokenKey(userId, businessCode);
        String token = redisUtil.get(cacheKey);
        if (!StringUtils.isEmpty(token)) {
            redisUtil.remove(cacheKey);
        }
        token = UUID.randomUUID().toString();
        redisUtil.set(cacheKey, token, tokenExpireTime);
        return redisUtil.get(cacheKey);
    }

    /**
     * 验证业务token
     *
     * @param userId
     * @param businessCode
     * @return
     */
    public boolean validateBusinessToken(String userId, String businessCode, String token) {
        return Objects.equals(token, redisUtil.get(getTokenKey(userId, businessCode)));
    }

    /**
     * 删除业务token
     *
     * @param userId
     * @param businessCode
     * @return
     */
    public void deleteBusinessToken(String userId, String businessCode, String token) {
        String cacheKey = getTokenKey(userId, businessCode);
        String cacheToken = redisUtil.get(cacheKey);
        if (Objects.equals(cacheToken, token)) {
            redisUtil.remove(cacheKey);
        }
    }

    /**
     * 锁定token
     *
     * @param token
     * @return
     */
    public boolean lockBusinessToken(String token, Long tokenTime) {
        return lockTemplate.tryLock(token, tokenTime);
    }

    /**
     * 移除token
     *
     * @param token
     */
    public void unlockBusinessToken(String token) {
        lockTemplate.unLock(token);
    }

    /**
     * redis token key
     *
     * @param userId
     * @param businessCode
     * @return
     */
    private String getTokenKey(String userId, String businessCode) {
        return userId + "_" + businessCode;
    }
}