package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductPosterMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.PosterGenDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductPosterDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductPosterVO;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.QrcodeUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.PRODUCT_DETAIL;

/**
 * 小额保险产品海报service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class SmProductPosterService {

    /**
     * 小额保险产品mapper
     */
    @Autowired
    private SmProductPosterMapper mapper;

    /**
     * 查询管海报列表
     *
     * @param keyword
     * @return
     */
    public PageInfo<SmProductPosterVO> getProductPosterByPage(String keyword, Pageable pageable) {
        PageHelper.startPage(pageable.getPage(), pageable.getSize());
        return new PageInfo<>(mapper.listProductPosters(keyword));
    }

    /**
     * 保存产品海报
     *
     * @param dto
     * @return
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void saveProductPoster(SmProductPosterDTO dto) {
        List<SmProductPosterVO> posterVOs = mapper.getProductPostersByProductId(dto.getProductId());
        if (!posterVOs.isEmpty() && posterVOs.stream().anyMatch(p -> !Objects.equals(p.getId(), dto.getId()))) {
            throw new BizException(ExcptEnum.POSTER_ADD_ERROR);
        }
        if (dto.getId() == null || dto.getId() <= 0) {
            mapper.insertProductPoster(dto);
        } else {
            mapper.updateProductPoster(dto);
        }
    }

    /**
     * 查询产品海报详情
     *
     * @param id
     * @return
     */
    public SmProductPosterVO getProductPosterById(int id) {
        return mapper.getProductPosterById(id);
    }

    /**
     * 查询产品海报详情
     *
     * @param id
     * @return
     */
    public SmProductPosterVO getProductPosterByProductId(int id) {
        return mapper.getProductPosterByProductId(id);
    }

    /**
     * 删除产品海报
     *
     * @param id
     * @return
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void deleteProductPoster(int id) {
        mapper.deleteProductPoster(id);
    }

    /**
     * 合成海报
     *
     * @param dto
     * @return
     */
    public String genPoster(PosterGenDTO dto) {
        if (dto == null || StringUtils.isEmpty(dto.getPosterUrl())) {
            throw new BizException(ExcptEnum.POSTER_SETTING_ERROR);
        }
        try (InputStream is = new ByteArrayInputStream(CommonUtil.getBytesFromUrl(dto.getPosterUrl()))) {
            BufferedImage buffImg = ImageIO.read(is);
            buffImg = drawImage(buffImg, dto.getWxImageBytes(), 250, 761, 100, 100);
            buffImg = drawImage(buffImg, dto.getQrBytes(), 60, 710, 154, 154);
            buffImg = drawWords(buffImg, "投保前请联系 " + dto.getUserName(), 360, 790, 19, true);
            buffImg = drawWords(buffImg, dto.getUserMobile(), 360, 828, 32, true);
            buffImg = drawWords(buffImg, "咨询详细产品方案及免责条款！", 360, 855, 16, false);
            return QrcodeUtil.bufferedImage2Base64(buffImg);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.POSTER_GEN_ERROR, e);
        }
    }


    /***
     * 在一张大图张添加小图和文字
     * @param imgBytes
     * @param sx    小图在大图上x抽位置
     * @param sy    小图在大图上y抽位置
     * @param wd    小图宽度
     * @param ht    小图高度
     */
    private BufferedImage drawImage(BufferedImage buffImg, byte[] imgBytes, int sx, int sy, int wd, int ht) {
        Graphics g = buffImg.getGraphics();
        ImageIcon imgIcon = new ImageIcon(imgBytes);
        Image img = imgIcon.getImage();
        g.drawImage(img, sx, sy, wd, ht, null);
        g.setColor(Color.WHITE);
        g.dispose();
        return buffImg;
    }

    /***
     * 在一张大图张添加小图和文字
     * @param sx    小图在大图上x抽位置
     * @param sy    小图在大图上y抽位置
     * @param content   文字内容
     * @param fontSize  文字大小
     */
    private BufferedImage drawWords(BufferedImage buffImg, String content, int sx, int sy, int fontSize, boolean bold) {
        Graphics g = buffImg.getGraphics();
        int style = Font.PLAIN;
        if (bold) {
            style = Font.BOLD;
        }
        Font f = new Font("宋体", style, fontSize);
        g.setColor(new Color(47, 51, 54));
        g.setFont(f);
        g.drawString(content, sx, sy);
        g.dispose();
        return buffImg;
    }
}
