package com.cfpamf.ms.insur.admin.renewal.service;

import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.po.renewal.InsuranceRenewPo;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.form.WxOrderSearchForm;
import com.cfpamf.ms.insur.admin.renewal.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.renewal.RenewalBasicQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface InsuranceRenewService {

    /**
     * 根据数仓表处理续保保单信息
     */
    void initRenewPolicy();

    /**
     * 续保列表分页查询
     * @param wxOrderSearchForm
     * @param wxSessionVO
     * @return
     */
    PageInfo<InsuranceRenewedOrderVo> searchWxRenewalOrderVo(WxOrderSearchForm wxOrderSearchForm, WxSessionVO wxSessionVO);

    /**
     * 搜索微信端待续保订单条数
     * @param wxSessionVO
     * @return
     */
    Integer searchWxWaitRenewalOrderVoCount(WxSessionVO wxSessionVO);

    /**
     * 查看订单详情
     * @param query
     * @return
     */
    OrderDetailVo findOrderByPolicyNo(OrderDetailQuery query);

    /**
     * 增加断保原因
     * @param policyNo
     * @param productAttrCode
     * @param overRenewalReason
     */
    void addOverRenewalReason(String policyNo,String productAttrCode, String overRenewalReason);

    /**
     * 续保列表统计
     * @param query
     * @return
     */
    RenewalStatisticsVo renewalStatistics(RenewalBasicQuery query);

    /**
     * 搜索续保订单列表（后端）
     * @param renewalOrderSearchForm
     * @return
     */
    PageInfo<InsuranceRenewedOrderVo> searchRenewalOrderVo(RenewalOrderSearchForm renewalOrderSearchForm);

    /**
     * 续保列表导出
     * @param renewalOrderSearchForm
     * @param reportType
     * @return
     */
    List getReportRenewalOrderList(RenewalOrderSearchForm renewalOrderSearchForm, String reportType);

    List<PersonVo> initInsuredInfo(String policyNo, String productAttrCode);

    void addRenewedPolicy(SmBaseOrderVO sourcePolicyOrder, List<PersonVo> sourceInsuredList, SmCreateOrderSubmitRequest createOrder);

    /**
     * 获取转投保单
     * @param fhOrderId
     * @param productAttrCode
     * @return
     */
    List<TransferPolicyVo> getTransferPolicyList(String fhOrderId, String productAttrCode);

    /**
     * 获取首页待续保记录
     * @param userInfo
     * @return
     */
    List<HomePageRenewedOrderVo> listHomePageRenewedOrder(JwtUserInfo userInfo);

    /**
     * 注意，该方法已注销
     * 待续列表数据由insurance_renewal表改成policy_renewal_base_info表
     * @param size
     * @return
     */
    List<InsuranceRenewPo> listWaitInsuranceRenewal(Integer size);

    /**
     *
     * @param size
     * @return
     */
    List<InsuranceRenewPo> listWaitInsuranceRenewalV2(Integer size);

    /**
     *
     * @param insuranceRenewPo
     * @return
     */
    List<TransferPolicyVo> listAutoQueryTransferPolicyList(InsuranceRenewPo insuranceRenewPo);

    int updateUpdateTime(Integer id);

    int modifyUpdateTimeByOldPolicyNo(String oldPolicyNo);

}
