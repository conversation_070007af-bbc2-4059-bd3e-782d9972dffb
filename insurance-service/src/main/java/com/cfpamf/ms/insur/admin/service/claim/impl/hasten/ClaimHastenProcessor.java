package com.cfpamf.ms.insur.admin.service.claim.impl.hasten;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimProgressExpectTimeMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimProgressHastenRecordMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmWeekdayMapper;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressExpectTime;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressHastenRecord;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmWeekday;
import com.cfpamf.ms.insur.admin.pojo.vo.ClaimExpectWaitTimeProgressStepVO;
import com.cfpamf.ms.insur.admin.pojo.vo.ProgressStepVO;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.claim.base.HastenService;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/5/30 19:59
 * @Version 1.0
 */
@Service
@Slf4j
public class ClaimHastenProcessor {

    private Map<String, HastenService> hastenMap;

    @Autowired
    private SmClaimMapper claimMapper;

    @Autowired
    private ClaimWorkflow claimWorkflow;

    @Autowired
    protected SmWeekdayMapper weekdayMapper;

    @Autowired
    private SmClaimProgressExpectTimeMapper progressExpectTimeMapper;

    @Autowired
    private SmClaimProgressHastenRecordMapper hastenRecordMapper;


    @Autowired
    public void init(@Autowired List<HastenService> hanstenServiceList) {
        this.hastenMap = hanstenServiceList.stream().collect(
                HashMap::new
                , (map, t) -> map.putAll(t.node().stream().collect(Collectors.toMap(Function.identity(), key -> t)))
                , HashMap::putAll
        );
    }


    public LocalDateTime calHastenTime(String node, Integer claimId) {
        if (hastenMap.containsKey(node)) {
            return hastenMap.get(node).calFirstTimeDelay(claimId);
        }
        return null;
    }

    public void hastenDetail(String node, Integer claimId) {
        if (hastenMap.containsKey(node)) {
            hastenMap.get(node).readDetail(claimId, node);
        }
    }


    public void hasten(String type) {

        long count = PageHelper.count(
                () -> claimMapper.listHastenList(null, null)
        );

        for (int i = 0; i < count; ) {

            List<SmClaim> claimHastenList = claimMapper.listHastenList(i, 100);

            if (CollectionUtil.isEmpty(claimHastenList)) {
                break;
            }

            for (SmClaim claim : claimHastenList) {
                try{
                    if (hastenMap.containsKey(claim.getClaimState())) {
                        hastenMap.get(claim.getClaimState()).doHasten(claim.getId(), type);
                    }
                } catch(Exception e) {
                    log.warn("催办失败-{}-{}", claim.getId(), claim.getClaimState());
                }
            }
            i = i + claimHastenList.size();
        }

    }

    /**
     * 查询出险时间
     *
     * @param claimId
     * @return
     */
    public List<ClaimExpectWaitTimeProgressStepVO> mapWaitTimeList(List<ProgressStepVO> allSteps, int claimId) {

        if (CollectionUtil.isEmpty(allSteps)) {
            return Collections.emptyList();
        }

        SmClaim claim = claimMapper.getByClaimId(claimId);

        List<ProgressStepVO> progressStepVOList = claimWorkflow.getAllSteps(claim.getClaimState());
        if (Objects.isNull(progressStepVOList)) {
            return null;
        }

        return progressStepVOList.stream().map(
                x -> {
                    ClaimExpectWaitTimeProgressStepVO expectWaitTime = new ClaimExpectWaitTimeProgressStepVO();
                    expectWaitTime.setSCode(x.getSCode());
                    expectWaitTime.setSName(x.getSName());
                    if (hastenMap.containsKey(x.getSCode())) {
                        expectWaitTime.setExpectWaitTime(hastenMap.get(x.getSCode()).expectProcessDay(claimId));
//                        calExpectWaitDay(x.getSCode(), claimId);
                    }
                    return expectWaitTime;
                }
        ).collect(Collectors.toList());

    }

    public List<ClaimExpectWaitTimeProgressStepVO> mapWaitTimeList(String sCode, int claimId) {

        List<ProgressStepVO> allSteps = claimWorkflow.getAllSteps(sCode);

        if (CollectionUtil.isEmpty(allSteps)) {
            return Collections.emptyList();
        }

        SmClaim claim = claimMapper.getByClaimId(claimId);

        List<ProgressStepVO> progressStepVOList = claimWorkflow.getAllSteps(claim.getClaimState());
        if (Objects.isNull(progressStepVOList)) {
            return null;
        }

        return progressStepVOList.stream().map(
                x -> {
                    ClaimExpectWaitTimeProgressStepVO expectWaitTime = new ClaimExpectWaitTimeProgressStepVO();
                    expectWaitTime.setSCode(x.getSCode());
                    expectWaitTime.setSName(x.getSName());
                    if (hastenMap.containsKey(x.getSCode())) {
                        expectWaitTime.setExpectWaitTime(hastenMap.get(x.getSCode()).expectProcessDay(claimId));
                    }
                    return expectWaitTime;
                }
        ).collect(Collectors.toList());

    }

    public Integer calExpectWaitDay(String sCode, int claimId) {
        List<ClaimExpectWaitTimeProgressStepVO> list = mapWaitTimeList(sCode, claimId);
        boolean next = false;
        if (!CollectionUtils.isEmpty(list)) {
            int day = 0;
            for (ClaimExpectWaitTimeProgressStepVO waitTimeProgressStepVO : list) {
                if (Objects.equals(waitTimeProgressStepVO.getSCode(), sCode)) {
                    next = true;
                }
                if (next && contains(waitTimeProgressStepVO.getSCode())) {
                    day = day + waitTimeProgressStepVO.getExpectWaitTime();
                }
            }

            //计算节假日
            LocalDate localDate = LocalDate.now();
            List<SmWeekday> weekdayList = weekdayMapper.queryIntervalExceptHoliday(localDate, day);
            if (CollectionUtil.isEmpty(weekdayList)) {
                return day;
            }

            SmWeekday weekday = weekdayList.get(weekdayList.size() - 1);
            int interval = Period.between(localDate, weekday.getWorkDay()).getDays();

            //计算节假日
            return interval;
        }
        return null;
    }

    @Autowired
    private BmsConfig bmsConfig;


    public void hastenByClaimId(Integer claimId) {
        WxClaimDetailVO claim = claimMapper.getSmClaimById(claimId);

        if (Objects.isNull(claim)) {
            throw new BizException("", "理赔信息不存在");
        }

        if (!hastenMap.containsKey(claim.getClaimState())) {
            throw new BizException("", "该节点不可催办");
        }

        // 判断催办权限
        if (Objects.equals(claim.getClaimState(), ClaimWorkflow.STEP_TO_PAY)) {
            throw new BizException("", "该节点不可催办");
        }

        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        if (
                Objects.isNull(userDetailVO) || CollectionUtil.isEmpty(userDetailVO.getRoleList())
                        || userDetailVO.getRoleList().stream().noneMatch(x -> Objects.equals(x.getRoleCode(), bmsConfig.getClaimClerkCode()))
        ) {
            throw new BizException("", "仅理赔专员可以催办");
        }

        //查询上次成功催办时间
        SmClaimProgressHastenRecord hastenRecord = hastenRecordMapper.selectByClaimIdAndStateSuc(claim.getId(), claim.getClaimState());

        if (Objects.nonNull(hastenRecord) && Objects.nonNull(hastenRecord.getActualHastenTime())) {
            long interval = ChronoUnit.DAYS.between(hastenRecord.getActualHastenTime(), LocalDateTime.now());

            if (interval < hastenMap.get(claim.getClaimState()).manualHastenInterval(claimId)) {
                throw new BizException("", StrUtil.format("距离上次催办提醒超过{}天才可催办", hastenMap.get(claim.getClaimState()).manualHastenInterval(claimId)));
            }
        }


        SmClaimProgressHastenRecord progressHastenRecord = hastenMap.get(claim.getClaimState()).doHasten(claim.getId(), "manual");
        if (Objects.nonNull(progressHastenRecord) && Objects.equals(progressHastenRecord.getState(), "2")) {
            throw new BizException("", progressHastenRecord.getHastenResult());
        }
    }

    public boolean contains(String code) {
        return hastenMap.containsKey(code);
    }


    public void updateExpectDays(String sCode, int claimId) {
        List<ClaimExpectWaitTimeProgressStepVO> expectWaitTimeProgressStepVOS = mapWaitTimeList(sCode, claimId);
        boolean next = false;
        if (!CollectionUtils.isEmpty(expectWaitTimeProgressStepVOS)) {
            int day = 0;
            for (ClaimExpectWaitTimeProgressStepVO waitTimeProgressStepVO : expectWaitTimeProgressStepVOS) {
                if (Objects.equals(waitTimeProgressStepVO.getSCode(), sCode)) {
                    next = true;
                }
                if (next && contains(waitTimeProgressStepVO.getSCode())) {
                    day = day + waitTimeProgressStepVO.getExpectWaitTime();
                }
            }

//            //计算节假日
//            LocalDate localDate = LocalDate.now();
//            List<SmWeekday> weekdayList = weekdayMapper.queryIntervalExceptHoliday(localDate, day);
//            if (CollectionUtil.isEmpty(weekdayList)) {
//                //不阻断业务主要流程
//                log.error("理赔催办日期配置不存在！");
//            }

            progressExpectTimeMapper.logicDelByClaimId(claimId);
            progressExpectTimeMapper.insertList(
                    expectWaitTimeProgressStepVOS.stream().map(
                            x -> {
                                SmClaimProgressExpectTime progressExpectTime = new SmClaimProgressExpectTime();
                                progressExpectTime.setExpectWaitDay(x.getExpectWaitTime());
                                progressExpectTime.setScode(x.getSCode());
                                progressExpectTime.setSname(x.getSName());
                                progressExpectTime.setCreateBy(HttpRequestUtil.getUserId());
                                progressExpectTime.setClaimId(claimId);
                                return progressExpectTime;
                            }
                    ).collect(Collectors.toList())
            );
            //更新数据库
            claimMapper.updateExpectCloseDay(day, claimId);


        }

    }

}
