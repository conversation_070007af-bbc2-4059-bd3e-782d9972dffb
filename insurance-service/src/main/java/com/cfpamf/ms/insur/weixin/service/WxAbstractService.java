package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRenewBindInfo;
import com.cfpamf.ms.insur.admin.service.SmOrderRenewBindService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.pojo.dto.BmsOrgDTO;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.BankVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEndorResponse;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEndorsement;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupRevokeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.EndorVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.PolicyItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import static com.cfpamf.ms.insur.base.constant.BaseConstants.USER_TOKEN;
import static com.cfpamf.ms.insur.base.util.HttpRequestUtil.*;

/**
 * : 微信公共类
 *
 * @author: zhangnayi
 **/
@Slf4j
public abstract class WxAbstractService {

    /**
     * redis 缓存
     */
    @Autowired
    protected RedisUtil<String, String> redisUtil;

    @Autowired
    protected BusinessTokenService tokenService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private BmsService bmsService;
    @Autowired
    protected SmOrderRenewBindService smOrderRenewBindService;

    /**
     * 如果是区域办公室，则获取分支机构树对应的区域名称
     *
     * @param orgCode
     * @return
     */
    public String getBranchOrgName(String orgCode) {
        BmsOrgDTO dto = permissionUtil.getBranchOrgByOrgCode(orgCode);
        return dto != null ? dto.getOrgName() : null;
    }

    /**
     * 如果是区域办公室，则获取分支机构树对应的区域code
     *
     * @param orgCode
     * @return
     */
    public String getBranchOrgCode(String orgCode) {
        BmsOrgDTO dto = permissionUtil.getBranchOrgByOrgCode(orgCode);
        return dto != null ? dto.getOrgCode() : null;
    }

    public Boolean isAreaOffice(String orgId) {
        return bmsService.checkOrgIsAreaOffice(orgId);
    }

    public OrganizationBaseVO getBmsOrgInfo(String orgCode) {
        return bmsService.getBizOrg(orgCode);

    }

    /**
     * 验证微信用户权限是从微信菜单过来
     */
    public WxSessionVO checkAuthority() {

        return checkAuthority(getWxOpenId(), getToken());
    }

    /**
     * 验证微信用户权限是从微信菜单过来
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthority(String wxOpenId, String authorization) {
        if (StringUtils.isEmpty(wxOpenId)&&StringUtils.isEmpty(authorization)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}", wxOpenId, authorization);
            log.warn("微信用户token失效 user-agent={}, referer={}", getUserAgent(), getReferer());
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        WxSessionVO session = null;
        if(!StringUtils.isEmpty(wxOpenId)){
            session = getWxSession(wxOpenId);
        }
        if(session == null && !StringUtils.isEmpty(authorization)){
            session = getWxSession(USER_TOKEN+authorization); //新老模式token 缓存兼容
        }
        if (session == null || !Objects.equals(session.getAuthorization(), authorization)) {
            log.warn("微信用户token失效 wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            log.warn("微信用户token失效 user-agent= {}, referer={}", getUserAgent(), getReferer());
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        log.info("获取session用户信息|{}", JSON.toJSONString(session));
        return session;
    }

    /**
     * 验证微信用户权限是从微信菜单过来
     */
    public WxSessionVO getSession() {
        String openId = getWxOpenId();
        WxSessionVO wxSession = null;
        if(!StringUtils.isEmpty(openId)){
            wxSession = getWxSession(openId);
        }
        if (wxSession == null) {
            String authToken = getAuthToken();
            if(!StringUtils.isEmpty(authToken)){
                wxSession = getWxSession(USER_TOKEN+authToken); //新老模式token 缓存兼容
            }
        }
        return wxSession;
    }

    public OrderDetailQuery buildQuery() {
        OrderDetailQuery query = new OrderDetailQuery();
        query.setUserId(HttpRequestUtil.getUserId());
        WxSessionVO session = getSession();
        if (session != null) {
            query.setChannel(session.getChannel());
            if (session.isBindEmployee()) {
                query.setUserId(session.getUserId());
            }
        }
        return query;
    }

    public WxSessionVO checkAuthorityEpAg() {
        return checkAuthorityEpAg(getWxOpenId(), getToken());
    }

    /**
     * 验证微信用户权限(公司员工+代理人)
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthorityEpAg(String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthority(wxOpenId, authorization);
        if (!Objects.equals(session.getUserType(), SmConstants.USER_TYPE_EMPLOYEE)
                && !Objects.equals(session.getUserType(), SmConstants.USER_TYPE_AGENT)) {
            log.warn("微信用户没有公司员工+代理人模块权限wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448);
        }
        return session;
    }

    /**
     * 验证微信用户权限(公司员工)
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthorityEmployee(String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthority(wxOpenId, authorization);
        if (!Objects.equals(session.getUserType(), SmConstants.USER_TYPE_EMPLOYEE)) {
            log.warn("微信用户没有公司员工模块权限wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448);
        }
        return session;
    }

    /**
     * 验证微信用户权限(代理人)
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthorityAgent(String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthority(wxOpenId, authorization);
        if (!Objects.equals(session.getUserType(), SmConstants.USER_TYPE_AGENT)) {
            log.warn("微信用户没有代理人模块权限wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448);
        }
        return session;
    }


    /**
     * 验证微信用户权限(代理人)
     *
     * @param wxOpenId
     * @param authorization
     */
    public WxSessionVO checkAuthorityWeixin(String wxOpenId, String authorization) {
        WxSessionVO session = checkAuthority(wxOpenId, authorization);
        if (!Objects.equals(session.getUserType(), SmConstants.USER_TYPE_WEIXIN)) {
            log.warn("微信用户没有代理人模块权限wxOpenId={}, authorization={}, session={}", wxOpenId, authorization, JSON.toJSONString(session));
            throw new BizException(ExcptEnum.INVALID_USER_AUTH_844448);
        }
        return session;
    }

    /**
     * 获取微信session
     *
     * @param wxOpenId
     * @return
     */
    public WxSessionVO getWxSession(String wxOpenId) {
        if(org.apache.commons.lang.StringUtils.isBlank(wxOpenId)){
            return null;
        }
        String json = redisUtil.get(wxOpenId);
        if (!StringUtils.isEmpty(json)) {
            return JSON.parseObject(json, WxSessionVO.class);
        }
        return null;
    }

    /**
     * 清除微信session
     *
     * @param wxOpenId
     * @return
     */
    public void deleteWxSession(String wxOpenId) {
        String json = redisUtil.get(wxOpenId);
        if (!StringUtils.isEmpty(json)) {
            redisUtil.remove(wxOpenId);
        }
    }

}
