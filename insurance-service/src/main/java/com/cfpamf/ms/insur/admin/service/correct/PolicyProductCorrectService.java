package com.cfpamf.ms.insur.admin.service.correct;

import cn.hutool.core.stream.CollectorUtil;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.whale.WhaleDataRevisionMapper;
import com.cfpamf.ms.insur.admin.enums.commission.CommissionRedoTypeEnum;
import com.cfpamf.ms.insur.admin.enums.correct.EnumWhaleCorrectProject;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.channel.WhaleOrderChannelService;
import com.cfpamf.ms.insur.admin.external.whale.enums.EnumChannelBusinessScenario;
import com.cfpamf.ms.insur.admin.external.whale.model.*;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionRedoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.order.whale.PolicyCorrectCheck;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.whale.tool.RemovePolicyData;
import com.cfpamf.ms.insur.admin.service.SmCommissionDetailService;
import com.cfpamf.ms.insur.admin.service.order.CommonOrderService;
import com.cfpamf.ms.insur.admin.service.order.syn.SynVehicleInfoService;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.product.PolicyRiskChangeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.PreservationCheckResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 保单险种变更保全
 */
@Service
@Slf4j
public class PolicyProductCorrectService extends BaseCorrectService<PreservationDetail> {
    @Autowired
    private SmOrderInsuredMapper insuredMapper;
    @Autowired
    private WhaleDataRevisionMapper whaleDataRevisionMapper;
    @Autowired
    private CommonOrderService commonOrderService;
    @Autowired
    private SynVehicleInfoService synVehicleInfoService;
    @Autowired
    private WhaleOrderChannelService whaleOrderChannelService;
    @Autowired
    private SmCommissionDetailService smCommissionDetailService;
    @Autowired
    private WhaleOrderServiceAdapterImpl adapter;
    @Autowired
    private TxServiceManager txServiceManager;
    @Override
    public PreservationDetail queryCorrectContextParams(String preservationCode) {
        return super.callXjPreservationDetail(preservationCode);
    }

    @Override
    public void doCorrect(PreservationDetail preservationDetail) {
        log.info("保单险种变更：{}",preservationDetail);
        String policyCode = preservationDetail.getPolicyCode();
        String contractCode = preservationDetail.getContractCode();
        if (StringUtils.isBlank(policyCode)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "保单号为空");
        }
        WhaleResp<WhaleContract> policy = adapter.getPolicyNoByBusinessScenario(contractCode, EnumChannelBusinessScenario.SYN_WHALE_POLICY);
        WhaleContract contract = policy.getData();
        SmPlanVO planVO = adapter.convertPlan(contract);
        List<SmOrderInsured> insureds = insuredMapper.selectByPolicyNoList(Collections.singletonList(policyCode));
        if (CollectionUtils.isEmpty(insureds)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("被保人信息查询不存在-%s", policyCode));
        }
        String orderId = insureds.get(0).getFhOrderId();
        SmCreateOrderSubmitRequest submitRequest = adapter.cvtByNotify(planVO, contract, orderId);
        synVehicleInfoService.whaleSynInsured(contract);
        whaleOrderChannelService.setWhaleImportChannel(planVO,contract,submitRequest);
        RemovePolicyData dataRevise = new RemovePolicyData();
        dataRevise.setFhOrderId(orderId);
        dataRevise.setPolicyCode(policyCode);
        
        txServiceManager.excute(()->{
            whaleDataRevisionMapper.removePolicyPhysics(dataRevise);
            commonOrderService.saveOrder(planVO, contract, submitRequest);
        });
        CommissionRedoDTO redoVo = new CommissionRedoDTO();
        redoVo.setPolicyNo(policyCode);
        redoVo.setOrderId(orderId);
        redoVo.setOperationType(CommissionRedoTypeEnum.REDO_COMMISSION);
        smCommissionDetailService.redoCommission(redoVo);
    }



    @Override
    public String correctProject() {
        return EnumWhaleCorrectProject.CHANGE_PRODUCT.getCode();
    }

    @Override
    public PreservationCheckResultVo preCheck(PolicyCorrectCheck param){
        String contractCode = param.getContractCode();
        WhaleResp<WhaleContract> policy = adapter.getPolicyNoByBusinessScenario(contractCode,EnumChannelBusinessScenario.SYN_WHALE_POLICY);
        WhaleContract contract = policy.getData();

        List<PolicyRiskChangeVo> correctedPolicyProductList = param.getPolicyRiskChangeForm().getCorrectedPolicyProductList();
        List<String> productCodeList = convertProductList(correctedPolicyProductList);
        if(CollectionUtils.isEmpty(productCodeList)){
            return PreservationCheckResultVo.err("保单无主险种");
        }
        LocalDateTime approvedTime = contract.getContractExtendInfo().getApprovedTime();
        String paymentPeriodType = correctedPolicyProductList.get(0).getPaymentPeriodType();
        Integer paymentPeriod = correctedPolicyProductList.get(0).getPaymentPeriod();
        InsuredInfoList insured = null;
        if(CollectionUtils.isNotEmpty(contract.getInsuredInfoList())) {
            insured = contract.getInsuredInfoList().get(0);
        }

        SmPlanVO planVO = adapter.convertPlanV2(approvedTime,productCodeList,paymentPeriodType,paymentPeriod,insured);
        if(planVO==null){
            return PreservationCheckResultVo.err("农保不存在变更后的险种/计划，请先配置");
        }
        return PreservationCheckResultVo.success();
    }

    private List<String> convertProductList(List<PolicyRiskChangeVo> productList){
        List<String> productLict = productList.stream().filter(product->Objects.equals(product.getMainInsurance(),1)).map(PolicyRiskChangeVo::getProductCode).collect(Collectors.toList());

        List<WhaleProductMappingVo> whaleProductMappingVoList = adapter.getWhaleProductMapping(productLict);
        if(CollectionUtils.isEmpty(whaleProductMappingVoList)){
            return Collections.emptyList();
        }
        return whaleProductMappingVoList.stream().map(product->{
            String rualPlanCode = product.getRuralPlanCode();
            String productCode = product.getProductCode();
            return StringUtils.isNotBlank(rualPlanCode)?rualPlanCode:productCode;
        }).collect(Collectors.toList());
    }
}
