package com.cfpamf.ms.insur.admin.validation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.order.EnumImportOrder;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCommissionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.TmpOrderValidPolicyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumInsuredAppStatus;
import com.cfpamf.ms.insur.admin.enums.OrderImportEndorsementTypeEnum;
import com.cfpamf.ms.insur.admin.enums.product.EnumRiskPayWay;
import com.cfpamf.ms.insur.admin.external.whale.enums.EnumWhaleInsuredPeriodType;
import com.cfpamf.ms.insur.admin.external.whale.enums.EnumWhalePayPeriodType;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCommonSettingVO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderExcelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOrderImportDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.excelorder.ExcelLongTermOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.excelorder.ExcelOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.excelorder.ExcelShortTermOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmPolicyRegister;
import com.cfpamf.ms.insur.admin.pojo.po.order.impor.TmpOrderValidPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRisk;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmPolicyRegisterService;
import com.cfpamf.ms.insur.admin.util.CompareTools;
import com.cfpamf.ms.insur.admin.validation.order.SexAndBirthdayValidation;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.util.IdCardUtils;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.constant.SmConstants.ORDER_PREFIX_IMPORT;
import static com.cfpamf.ms.insur.admin.constant.SmConstants.POLICY_STATUS_SUCCESS;

/**
 * <AUTHOR> 2020/6/24 10:57
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Component
@Slf4j
public class SmOfflineOrderValidation extends OrderCommonValidator {

    @Autowired
    private Validator validator;

    @Autowired
    private SmProductMapper productMapper;

    @Autowired
    private AuthUserMapper authUserMapper;

    @Autowired
    private OrderNoGenerator orderNoGenerator;

    @Autowired
    private SmPolicyRegisterService registerService;

    @Autowired
    private TmpOrderValidPolicyMapper tmpOrderValidPolicyMapper;

    @Autowired
    private SysRiskMapper riskMapper;

    @Autowired
    private SmCommissionMapper commissionMapper;

    @Autowired
    private SystemCommissionConfigDetailMapper sysCommissionConfigDetailMapper;

    @Autowired
    private SexAndBirthdayValidation sexAndBirthdayValidation;

    public SmOfflineOrderValidation() {
    }

    private Class getValidRule(String importType) {
        if (Objects.equals(importType, EnumImportOrder.longTermPersonalIns.name())) {
            return SmOrderExcelDTO.LongTermInsRule.class;
        }

        if (Objects.equals(importType, EnumImportOrder.shortTermPersonalIns.name())) {
            return SmOrderExcelDTO.ShortTermInsRule.class;
        }

        if (Objects.equals(importType, EnumImportOrder.groupIns.name())) {
            return SmOrderExcelDTO.ShortTermInsRule.class;
        }
        return null;
    }

    // TODO 这个地方带确认
    static BiConsumer<Collection<SmCommonSettingVO>, ValidationResult<SmOrderExcelDTO>> DEFAULT_RELATIONSHIP =
            (dbMapper, validRes) -> {
                if (CollectionUtils.isEmpty(dbMapper)) {
                    validRes.addMessage("翻译投被保人关系失败:" + validRes.getSource().getInsuredRelationship());
                } else {
                    validRes.getSource().setInsuredRelationship(dbMapper.iterator().next().getFieldCode());
                }
            };

    static BiConsumer<Collection<SmCommonSettingVO>, ValidationResult<SmOrderExcelDTO>> CIC_RELATIONSHIP =
            (dbMapper, validRes) -> {
                if (CollectionUtils.isEmpty(dbMapper)) {
                    validRes.addMessage("翻译投被保人关系失败:" + validRes.getSource().getInsuredRelationship());
                    return;
                }
                //中华联合性别为男
                if (Objects.equals(validRes.getSource().getInsuredSex(), "1")) {
                    //设置成儿子
                    validRes.getSource().setInsuredRelationship("");
                } else {
                    //设置成女儿
                    validRes.getSource().setInsuredRelationship("");
                }

            };

    private static String getSex(String idNumber) {
        if (Objects.equals(IdcardUtils.getGenderByIdCard(idNumber), "F")) {
            return "女";
        }
        return "男";
    }

    public static String getBirthdayByIdCard(String idcard) {

        String str = IdcardUtils.getBirthByIdCard(idcard);
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6);
    }

    @Autowired
    ObjectMapper objectMapper;

    /**
     * 校验导入数据 以及翻译中文成码值
     * 团险单导入校验逻辑
     *
     * @param dto
     * @param extractOrder
     */
    @Deprecated
    public List<ValidationResult<SmOrderExcelDTO>> validAndMapper4Group(SmOrderImportDTO dto, List<SmOrderExcelDTO> extractOrder) {
        //基本校验
        List<ValidationResult<SmOrderExcelDTO>> validList = wrapper(extractOrder);
        //校验性别生日
        sexAndBirthdayValidation.validSexAndBirthdayGroup(validList);
        //公共信息校验
        validPublic(validList);
        //真实的channel
        String queryChannel = dto.getChannel();
        if (ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(queryChannel)) {
            queryChannel = EnumChannel.ZA.getCode();
        }

        Map<Boolean, List<ValidationResult<SmOrderExcelDTO>>> baseResMap = validList.stream().collect(Collectors.partitioningBy(ValidationResult::isSuccess));
        List<ValidationResult<SmOrderExcelDTO>> success = baseResMap.getOrDefault(Boolean.TRUE, Collections.emptyList());

        List<SmOrderExcelDTO> successData = success.stream().map(ValidationResult::getSource)
                .collect(Collectors.toList());
        /**
         * 匹配产品管理系统中已配置的计划
         */
        List<String> fhProductIds = successData.stream()
                .map(SmOrderExcelDTO::getFhProductId)
                .distinct()
                .collect(Collectors.toList());

        log.info("开始校验计划id是配置:{}", fhProductIds);
        Set<Integer> planIdList = new HashSet<>();

        if (!CollectionUtils.isEmpty(fhProductIds)) {
            List<SmPlanVO> planList = productMapper.listPlanByFhProductIds(queryChannel, fhProductIds);
            Map<String, SmPlanVO> planMap = LambdaUtils.safeToMap(planList, SmPlanVO::getFhProductId);
            //校验计划id
            Map<Boolean, List<ValidationResult<SmOrderExcelDTO>>> planValidMap = success
                    .stream()
                    .collect(Collectors.partitioningBy(order -> {
                        SmPlanVO smPlanVO = planMap.get(order.getSource().getFhProductId());
                        if (Objects.isNull(smPlanVO)) {
                            order.addMessage("计划id有误:" + order.getSource().getFhProductId());
                            return false;
                        }
                        String productName = smPlanVO.getProductName();
                        String importProductName = order.getSource().getProductName();
                        if (!Objects.equals(productName, importProductName)) {
                            order.addMessage("系统配置的产品名称:[" + productName + "]与导入的产品名称:[" + importProductName + "]不一致,请仔细核对");
                            return false;
                        }

                        if (EnumProductAttr.PERSON.getCode().equals(smPlanVO.getProductAttrCode())) {
                            order.addMessage("个险产品不能使用团险模板导入，请仔细确认导入类型和导入模版是否正确");
                            return false;
                        }

                        order.getSource().setCompanyId(smPlanVO.getCompanyId());
                        order.getSource().setProductId(smPlanVO.getProductId());
                        order.getSource().setPlanId(smPlanVO.getPlanId());
                        planIdList.add(smPlanVO.getPlanId());
                        return true;

                    }));
            success = planValidMap.getOrDefault(Boolean.TRUE, Collections.emptyList());
            List<ValidationResult<SmOrderExcelDTO>> planIdNotExists = planValidMap.get(Boolean.FALSE);
            baseResMap.get(Boolean.FALSE).addAll(planIdNotExists);
        }
        validProductBaseConfig(planIdList, success);
        //校验推荐人工号
        List<String> recommendUserIds = success.stream()
                .map(ValidationResult::getSource)
                .map(SmOrderExcelDTO::getRecommendUserId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        log.info("开始校验推荐人工号{}", recommendUserIds.size());
        if (!CollectionUtils.isEmpty(recommendUserIds)) {
            List<UserVO> userVOS = authUserMapper.listUsersByPageWithJobNumbers(recommendUserIds);
            Map<String, UserVO> userMap = LambdaUtils.safeToMap(userVOS, UserVO::getUserId);
            //数量不匹配
            success.stream()
                    .filter(entry -> StringUtils.isNotBlank(entry.getSource().getRecommendUserId()))
                    .forEach(entry -> {
                        SmOrderExcelDTO order = entry.getSource();
                        UserVO user = userMap.get(order.getRecommendUserId());
                        if (user == null) {
                            entry.addMessage("推荐人不存在");
                            return;
                        }
                        if (!Objects.equals(user.getUserName(), order.getRecommendName())) {
                            entry.addMessage("推荐人工号和名字不匹配");
                            return;
                        }
                    });
        }
        // 码值判断
        codeMap(success);
        //校验保单
        validGroupPolicyV2(queryChannel, dto, success);

        if (!CollectionUtils.isEmpty(success) && ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(dto.getChannel())) {
            zaIyb(success);
        }
        //验证团险金额
        if (!CollectionUtils.isEmpty(success)) {
            validatorGroupAmt(success);
        }
        return validList;
    }

    private List<ValidationResult<SmOrderExcelDTO>> wrapper(List<SmOrderExcelDTO> excelDataList) {
        List<ValidationResult<SmOrderExcelDTO>> allRes = excelDataList.stream()
                .map(order -> {
                    //订单号全部设置成空
                    order.setOrderNo(null);

                    //如果时间不带时分秒的时候
                    if (Objects.nonNull(order.getStartTime()) && order.getStartTime().length() == 10) {
                        order.setStartTime(order.getStartTime() + " 00:00:00");
                    }
                    if (Objects.nonNull(order.getEndTime()) && order.getEndTime().length() == 10) {
                        order.setEndTime(order.getEndTime() + " 23:59:59");
                    }

                    //第一次校验
                    Set<ConstraintViolation<SmOrderExcelDTO>> validate = validator.validate(order);

                    //基础校验 非空 简单正则等
                    if (!CollectionUtils.isEmpty(validate)) {
                        String collect = validate.stream()
                                .map(ConstraintViolation::getMessage)
                                .collect(Collectors.joining(","));
                        return new ValidationResult<>(collect, order);
                    }
                    //证件类型 默认为身份证
                    if (StringUtils.isBlank(order.getApplicantIdType())) {
                        order.setApplicantIdType(SmOrderExcelDTO.ID_CARD_TYPE);
                    }
                    if (StringUtils.isBlank(order.getInsuredIdType())) {
                        order.setInsuredIdType(SmOrderExcelDTO.ID_CARD_TYPE);
                    }
                    ValidationResult<SmOrderExcelDTO> success = ValidationResult.success(order);
                    if (Objects.equals(order.getInsuredRelationship(), "本人")) {
                        if (!Objects.equals(order.getApplicantName(), order.getInsuredName()) || !Objects.equals(order.getInsuredIdNumber(), order.getApplicantIdNumber())) {
                            success.addMessage("关系为本人，投被保人必须一致");
                        }
                    } else if (Objects.equals(order.getInsuredIdNumber(), order.getApplicantIdNumber())) {
                        success.addMessage("关系非本人" + "投被保人证件号不能一致");
                    }
                    // 如果是身份证校验证件号码 然后设置性别年龄
                    if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
                        String idNumber = order.getApplicantIdNumber();
                        if (!IdCardUtils.validate(idNumber)) {
                            success.addMessage("投保人证件号码错误");
                        } else {
                            order.setApplicantBirthday(getBirthdayByIdCard(idNumber));
                            order.setApplicantSex(getSex(idNumber));
                        }
                    }
                    if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
                        String idNumber = order.getInsuredIdNumber();
                        if (!IdCardUtils.validate(idNumber)) {
                            success.addMessage("被保人证件号码错误");
                        } else {
                            //根据证件号码生成生日
                            order.setInsuredBirthday(getBirthdayByIdCard(idNumber));
                            order.setInsuredSex(getSex(idNumber));
                        }
                    }
                    //证件类型为其他时，校验证件号码是否以“XN”开头
                    String reg = "^[Xx][Nn].*$";
                    if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
                        String idNumber = order.getApplicantIdNumber();
                        if (!idNumber.matches(reg)) {
                            success.addMessage("投保人证件号码不是“XN”开头");
                        }
                    }
                    if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
                        String idNumber = order.getInsuredIdNumber();
                        if (!idNumber.matches(reg)) {
                            success.addMessage("被保人证件号码不是“XN”开头");
                        }
                    }

                    //校验保单状态
                    if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.SUCCESS.getDesc())) {
                        order.setAppStatus(EnumInsuredAppStatus.SUCCESS.getCode());
                    } else if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.CANCEL_SUCCESS.getDesc())) {
                        order.setAppStatus(EnumInsuredAppStatus.CANCEL_SUCCESS.getCode());
                    } else {
                        success.addMessage("只能导入退保成功和承保成功的订单");
                    }
                    if (order.getEndTime().compareTo(order.getStartTime()) < 1) {
                        success.addMessage("保单失效时间不能早于保单起保时间");
                    }
                    //验证团险参数  2021-04-24 zhangjian
                    validateGroupOrder(order, success);
                    validate = validator.validate(order, SmOrderExcelDTO.InitAfter.class);

                    //基础校验 非空 简单正则等
                    if (!CollectionUtils.isEmpty(validate)) {
                        String collect = validate.stream()
                                .map(ConstraintViolation::getMessage)
                                .collect(Collectors.joining(","));
                        //校验出问题
                        success.addMessage(collect);
                    }
                    return success;
                }).collect(Collectors.toList());
        return allRes;
    }

    /**
     * 个险(长险)导入-数据校验
     * TODO
     *
     * @param dto
     * @param orders
     */
    public List<ValidationResult<ExcelLongTermOrderDTO>> valid4LongTermPersonal(SmOrderImportDTO dto,
                                                                                List<ExcelLongTermOrderDTO> orders) {
        String channel = dto.getChannel();
        if (ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(channel)) {
            channel = EnumChannel.ZA.getCode();
        }
        String importType = dto.getProductAttrCode();
        /**
         * 生成订单id
         */
        genId4LongTermIns(channel, orders);
        /**
         * 校验Excel数据的合法性
         */
        List<ValidationResult<ExcelLongTermOrderDTO>> allRes = orders.stream().map(a -> validLongTermOrder(importType, a)).collect(Collectors.toList());

        ValidContext<ExcelLongTermOrderDTO> context = new ValidContext(channel, allRes);
        //校验本人信息
        validRelation4LongTerm(context);
        //校验产品计划
        validPlan4LongTerm(context);
        //校验产品基础配置信息
        validProductBaseConfig4LongTerm(context);
        //校验推荐人信息是否正确
        validRecommend4LongTerm(context);
        //校验性别生日
        sexAndBirthdayValidation.validSexAndBirthdayLong(context);
        //码值转换
        convertDict4LongTerm(context);
        //校验险种
        validRisk4LongTerm(context);
        //校验去重
        validDuplicate4LongTerm(dto, context);
        //校验保费
        validPremium4LongTerm(context);
        //公共信息校验
        validPublic(context.getValidationData());
        return context.getValidationData();
    }

    private void validRelation4LongTerm(ValidContext<ExcelLongTermOrderDTO> context) {
        List<ValidationResult<ExcelLongTermOrderDTO>> validationDataList = context.getValidationData();
        if (CollectionUtils.isEmpty(validationDataList)) {
            return;
        }
        //选择关系为“本人”时，校验：投保人姓名+证件号与被保人姓名+证件必须一致；
        //选择关系不为“本人”时，校验：投保人证件号与被保人证件号不能一致；
        validationDataList.forEach(
                validation -> {
                    ExcelLongTermOrderDTO longTermOrderDTO = validation.getSource();
                    if (Objects.equals(longTermOrderDTO.getInsuredRelationship(), "本人")) {
                        if (!Objects.equals(longTermOrderDTO.getApplicantName(), longTermOrderDTO.getInsuredName()) || !Objects.equals(longTermOrderDTO.getInsuredIdNumber(), longTermOrderDTO.getApplicantIdNumber())) {
                            validation.addMessage("关系为本人，投被保人必须一致");
                        }
                    } else if (Objects.equals(longTermOrderDTO.getInsuredIdNumber(), longTermOrderDTO.getApplicantIdNumber())) {
                        validation.addMessage("关系非本人" + "投被保人证件号不能一致");
                    }
                }
        );
    }

    /**
     * 个险(短险)导入-数据校验
     *
     * @param dto
     * @param orders
     */
    public List<ValidationResult<ExcelShortTermOrderDTO>> valid4ShortTermPersonal(SmOrderImportDTO dto,
                                                                                  List<ExcelShortTermOrderDTO> orders) {
        String channel = dto.getChannel();
        if (ChannelConstant.ORDER_CHANNEL_ZA_IYB.equals(channel)) {
            channel = EnumChannel.ZA.getCode();
        }
        /**
         * 优先生成订单id
         */
        genId4ShortTermIns(channel, orders);
        List<ValidationResult<ExcelShortTermOrderDTO>> validateWrapperList = orders.stream()
                .map(this::validShortTermOrder)
                .collect(Collectors.toList());

        ValidContext<ExcelShortTermOrderDTO> context = new ValidContext(channel, validateWrapperList);
        //校验本人信息
        validateRelation(context);
        //校验产品计划
        validPlan4ShortTerm(context);
        //校验产品基础配置信息
        validProductBaseConfig4ShortTerm(context);
        //校验推荐人信息是否正确
        validRecommend(context);
        //校验性别生日
        sexAndBirthdayValidation.validSexAndBirthdayShort(context);
        //码值转换
        convertDict4ShortTerm(context);
        //校验状态一致性
        validApplyUniformity4ShortTerm(context);
        //校验保费
        validPremium4ShortTerm(context);
        //校验去重
        validDuplicate4ShortTerm(context);
        //公共信息校验
        validPublic(context.getValidationData());
        return validateWrapperList;
    }

    private void validateRelation(ValidContext<ExcelShortTermOrderDTO> context) {
        List<ValidationResult<ExcelShortTermOrderDTO>> validationDataList = context.getValidationData();
        if (CollectionUtils.isEmpty(validationDataList)) {
            return;
        }
        //选择关系为“本人”时，校验：投保人姓名+证件号与被保人姓名+证件必须一致；
        //选择关系不为“本人”时，校验：投保人证件号与被保人证件号不能一致；
        validationDataList.forEach(
                validation -> {
                    ExcelShortTermOrderDTO shortTermOrderDTO = validation.getSource();
                    if (Objects.equals(shortTermOrderDTO.getInsuredRelationship(), "本人")) {
                        if (!Objects.equals(shortTermOrderDTO.getApplicantName(), shortTermOrderDTO.getInsuredName()) || !Objects.equals(shortTermOrderDTO.getInsuredIdNumber(), shortTermOrderDTO.getApplicantIdNumber())) {
                            validation.addMessage("关系为本人，投被保人必须一致");
                        }
                    } else if (Objects.equals(shortTermOrderDTO.getInsuredIdNumber(), shortTermOrderDTO.getApplicantIdNumber())) {
                        validation.addMessage("关系非本人" + "投被保人证件号不能一致");
                    }
                }
        );

    }

    /**
     * 长期险.生成订单Id
     * 同一个保单号对应同一个订单号
     *
     * @param extractOrder
     */
    private void genId4LongTermIns(String channel, List<ExcelLongTermOrderDTO> extractOrder) {
        Map<String, String> orderMap = new HashMap<>();
        extractOrder.stream()
                .forEach(
                        order -> {
                            String policyNo = order.getPolicyNo();
                            String orderId = orderMap.get(policyNo);
                            if (StringUtils.isBlank(orderId)) {
                                orderId = ORDER_PREFIX_IMPORT + IdGenerator.getNextNo(channel);
                                orderMap.put(policyNo, orderId);
                            }
                            order.setOrderNo(orderId);
                        }
                );
        Map<String, List<ExcelLongTermOrderDTO>> data = LambdaUtils.groupBy(extractOrder, ExcelLongTermOrderDTO::getOrderNo);
        for (Map.Entry<String, List<ExcelLongTermOrderDTO>> entry : data.entrySet()) {
            List<ExcelLongTermOrderDTO> orders = entry.getValue();
            int qty = orders.size();
            orders.stream().forEach(a -> a.setItemQty(qty));
        }
    }

    /**
     * 短期险生成订单Id
     * 多人投保需要共用一个订单
     *
     * @param extractOrder
     */
    private void genId4ShortTermIns(String channel, List<ExcelShortTermOrderDTO> extractOrder) {
        Map<String, String> orderMap = new HashMap<>();
        extractOrder.stream().forEach(
                order -> {
                    String policyNo = order.getPolicyNo();
                    String orderId = orderMap.get(policyNo);
                    if (StringUtils.isBlank(orderId)) {
                        orderId = ORDER_PREFIX_IMPORT + IdGenerator.getNextNo(channel);
                    }

                    String policys = order.getPolicys();
                    if (StringUtils.isNotBlank(policys)) {
                        String[] policyList = policys.split(",|，");
                        Set<String> policySet = new HashSet<>(Arrays.asList(policyList));
                        final String finalOrderId = orderId;
                        if (policySet.contains(policyNo)) {
                            Arrays.stream(policyList)
                                    .forEach(a -> orderMap.put(a, finalOrderId));
                        }
                    }
                    order.setOrderNo(orderId);
                }
        );
    }

    /**
     * 个险.去重逻辑：excel内部是否重复，数据库是否已存在该记录
     *
     * @param dto
     * @param context
     */
    public void validDuplicate4LongTerm(SmOrderImportDTO dto, ValidContext<ExcelLongTermOrderDTO> context) {
        List<ValidationResult<ExcelLongTermOrderDTO>> success = context.getValidationData();
        if (CollectionUtils.isEmpty(success)) {
            return;
        }
        Map<String, ValidationResult<ExcelLongTermOrderDTO>> duplicateMap = new HashMap<>();
        List<String> policys = new ArrayList<>();
        success.forEach(val -> {
            ExcelLongTermOrderDTO source = val.getSource();
            policys.add(source.getPolicyNo());
            String key = source.getPolicyNo() + source.getRiskCode();
            if (duplicateMap.containsKey(key)) {
                String errorMsg = "重复数据：" + source.getPolicyNo();
                val.addMessage(errorMsg);
                duplicateMap.get(key).addMessage(errorMsg);
            } else {
                duplicateMap.put(key, val);
            }
        });
        List<TmpOrderValidPolicy> stockPolicys = tmpOrderValidPolicyMapper.selectExistsOnlyDb(policys);

        if (CollectionUtils.isEmpty(stockPolicys)) {
            return;
        }
        log.info("有与数据库重复的保单:{}", stockPolicys.size());
        /**
         * 目前长险同一个保单对应一个被保人(会有多个险种)
         * 所以此处只需要跟数据库的sm_order_insured表的保单对比即可去重
         * 后续如果有其他场景需要扩展
         */
        Map<String, TmpOrderValidPolicy> sockPolicyMap = LambdaUtils.safeToMap(stockPolicys, TmpOrderValidPolicy::getPolicyNo);
        success.forEach(a -> {
            ExcelLongTermOrderDTO source = a.getSource();
            String policyNo = source.getPolicyNo();
            TmpOrderValidPolicy stockPolicy = sockPolicyMap.get(policyNo);
            if (stockPolicy != null) {
                source.setOrderNo(stockPolicy.getDbFhOrderId());
                source.setPolicyDbExists(Boolean.TRUE);

                if (Objects.equals(stockPolicy.getAppStatus(), source.getAppStatus())) {
                    a.addMessage("该保单入库，请仔细核查：" + policyNo);
                }
                if (Objects.nonNull(stockPolicy)
                        && Objects.equals(stockPolicy.getAppStatus(), POLICY_STATUS_SUCCESS)
                        && Objects.equals(source.getAppStatus(), POLICY_STATUS_SUCCESS)) {
                    a.addMessage("新契约保单，不允许分多次导入" + policyNo);
                }
            }
        });
    }

    /**
     * 个险.短期险
     * 去重逻辑：excel内部是否重复，数据库是否已存在该记录
     *
     * @param context
     */
    private void validDuplicate4ShortTerm(ValidContext<ExcelShortTermOrderDTO> context) {
        List<ValidationResult<ExcelShortTermOrderDTO>> validData = context.getValidationData();
        if (CollectionUtils.isEmpty(validData)) {
            return;
        }
        Map<String, ValidationResult<ExcelShortTermOrderDTO>> duplicateMap = new HashMap<>();
        List<String> policys = new ArrayList<>();
        validData.forEach(val -> {
            ExcelShortTermOrderDTO source = val.getSource();
            String dupKey = source.getPolicyNo() + source.getInsuredIdNumber();
            policys.add(source.getPolicyNo());

            if (duplicateMap.containsKey(dupKey)) {
                String errorMsg = "重复数据：" + source.getPolicyNo();
                val.addMessage(errorMsg);
                duplicateMap.get(dupKey).addMessage(errorMsg);
            } else {
                duplicateMap.put(dupKey, val);
            }
        });
        Map<String, Map<String, TmpOrderValidPolicy>> policyMap = mapStockPolicy(policys);
        Map<String, String> orderIdMap = new HashMap<>();
        validData.forEach(entry -> {
            ExcelShortTermOrderDTO excelOrder = entry.getSource();
            String policyNo = excelOrder.getPolicyNo();
            Map<String, TmpOrderValidPolicy> stockInsuredMap = policyMap.get(policyNo);
            excelOrder.setPolicyDbExists(Boolean.FALSE);
            if (stockInsuredMap != null) {
                TmpOrderValidPolicy policyInsured = stockInsuredMap.get(excelOrder.getInsuredIdNumber());
                if (policyInsured != null) {
                    String currentOrderId = policyInsured.getDbFhOrderId();
                    setMap(currentOrderId, excelOrder, orderIdMap);

                    excelOrder.setOrderNo(currentOrderId);
                    excelOrder.setPolicyDbExists(Boolean.TRUE);
                    excelOrder.setIdNumberDbExists(Boolean.TRUE);

                    if (Objects.equals(policyInsured.getAppStatus(), "4")) {
                        entry.addMessage("该保单的被保人已有退保，不能再操作：" + policyNo);
                    } else if (Objects.equals(policyInsured.getAppStatus(), "1") && Objects.equals(excelOrder.getAppStatus(), "1")) {
                        entry.addMessage("该保单的被保人已承保，不能再导入承保记录：" + policyNo);
                    }
                } else {
                    excelOrder.setIdNumberDbExists(Boolean.FALSE);
                    TmpOrderValidPolicy policy = pickOne(stockInsuredMap);
                    if (policy != null) {
                        String currentOrderId = policy.getDbFhOrderId();
                        excelOrder.setOrderNo(currentOrderId);
                        excelOrder.setPolicyDbExists(Boolean.TRUE);
                        setMap(currentOrderId, excelOrder, orderIdMap);
                    }
                }
                //存在承保的不允许新建
                if (!CollectionUtils.isEmpty(stockInsuredMap.values())
                        && Objects.equals(excelOrder.getAppStatus(), POLICY_STATUS_SUCCESS)) {
                    long underwritingQuantity = stockInsuredMap.values()
                            .stream()
                            .filter(e -> Objects.nonNull(e) && Objects.equals(e.getAppStatus(), POLICY_STATUS_SUCCESS))
                            .count();
                    if (underwritingQuantity > 0) {
                        entry.addMessage("新契约保单，不允许分多次导入" + policyNo);
                    }
                }
            }
            String orderId = orderIdMap.get(policyNo);
            if (StringUtils.isNotBlank(orderId)) {
                excelOrder.setOrderNo(orderId);
            }

        });
    }

    private void setMap(String orderId, ExcelShortTermOrderDTO vo, Map<String, String> idMap) {
        String policyNos = vo.getPolicys();
        if (StringUtils.isNotBlank(policyNos)) {
            String[] policyNoArr = policyNos.split(",");
            for (String no : policyNoArr) {
                idMap.put(no, orderId);
            }
        }
        String policyNo = vo.getPolicyNo();
        idMap.put(policyNo, orderId);

    }

    private TmpOrderValidPolicy pickOne(Map<String, TmpOrderValidPolicy> policyMap) {
        if (policyMap == null) {
            return null;
        }
        Collection<TmpOrderValidPolicy> collection = policyMap.values();
        if (!CollectionUtils.isEmpty(collection)) {
            return collection.iterator().next();
        }
        return null;
    }

    public Map<String, Map<String, TmpOrderValidPolicy>> mapStockPolicy(List<String> policyNoList) {
        List<TmpOrderValidPolicy> stockPolicys = tmpOrderValidPolicyMapper.selectExistsOnlyDb(policyNoList);
        Map<String, Map<String, TmpOrderValidPolicy>> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(stockPolicys)) {
            return resultMap;
        }
        log.info("存量的保单信息:{}", stockPolicys);
        Map<String, List<TmpOrderValidPolicy>> policyListMap = LambdaUtils.groupBy(stockPolicys, TmpOrderValidPolicy::getPolicyNo);
        for (Map.Entry<String, List<TmpOrderValidPolicy>> entry : policyListMap.entrySet()) {
            List<TmpOrderValidPolicy> policyList = entry.getValue();
            Map<String, TmpOrderValidPolicy> policyMap = LambdaUtils.safeToMap(policyList, TmpOrderValidPolicy::getInsuredIdNumber);
            resultMap.put(entry.getKey(), policyMap);
        }
        return resultMap;
    }

    /**
     * 验证推荐人信息是否正确.
     *
     * @param context
     */
    public void validRecommend(ValidContext<ExcelShortTermOrderDTO> context) {
        List<ValidationResult<ExcelShortTermOrderDTO>> validData = context.getValidationData();
        List<String> recommendUserIds = validData.stream()
                .map(ValidationResult::getSource)
                .map(ExcelShortTermOrderDTO::getRecommendUserId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        log.info("开始校验推荐人工号:{}", recommendUserIds);
        if (CollectionUtils.isEmpty(recommendUserIds)) {
            return;
        }
        List<UserVO> userVOS = authUserMapper.listUsersByPageWithJobNumbers(recommendUserIds);
        Map<String, UserVO> userMap = LambdaUtils.safeToMap(userVOS, UserVO::getUserId);
        validData.stream()
                .forEach(order -> {
                    ExcelShortTermOrderDTO data = order.getSource();
                    String recommendUid = data.getRecommendUserId();
                    if (StringUtils.isBlank(recommendUid)) {
                        return;
                    }
                    UserVO user = userMap.get(recommendUid);
                    if (user == null) {
                        order.addMessage("推荐人不存在:" + recommendUid);
                        return;
                    }
                    if (!Objects.equals(user.getUserName(), data.getRecommendName())) {
                        log.warn("推荐人的名字和工号不匹配：{},{}", user.getUserName(), data.getRecommendName());
                        order.addMessage("推荐人的名字和工号不匹配:" + recommendUid);
                        return;
                    }
                });
    }

    /**
     * 验证推荐人信息是否正确.
     *
     * @param context
     */
    public void validRecommend4LongTerm(ValidContext<ExcelLongTermOrderDTO> context) {
        List<ValidationResult<ExcelLongTermOrderDTO>> successPart = context.getValidationData();
        List<String> recommendUserIds = successPart.stream()
                .map(ValidationResult::getSource)
                .map(ExcelLongTermOrderDTO::getRecommendUserId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        log.info("开始校验推荐人工号{}", recommendUserIds.size());
        if (CollectionUtils.isEmpty(recommendUserIds)) {
            return;
        }
        List<UserVO> userVOS = authUserMapper.listUsersByPageWithJobNumbers(recommendUserIds);
        Map<String, UserVO> userMap = LambdaUtils.safeToMap(userVOS, UserVO::getUserId);
        successPart.stream()
                .forEach(order -> {
                    ExcelLongTermOrderDTO data = order.getSource();
                    String recommendUid = data.getRecommendUserId();
                    if (StringUtils.isBlank(recommendUid)) {
                        return;
                    }
                    UserVO user = userMap.get(recommendUid);
                    if (user == null) {
                        order.addMessage("推荐人不存在:" + recommendUid);
                        return;
                    }
                    if (!Objects.equals(user.getUserName(), data.getRecommendName())) {
                        order.addMessage("推荐人的名字和工号不匹配:" + recommendUid);
                        return;
                    }
                });
    }

    /**
     * 险种校验
     * 险种起保时间必须大于订单创建时间
     * TODO 未添加时间校验
     *
     * @param context
     */
    public void validRisk4LongTerm(ValidContext<ExcelLongTermOrderDTO> context) {
        List<ValidationResult<ExcelLongTermOrderDTO>> successPart = context.getValidationData();
        List<ExcelLongTermOrderDTO> excelOrders = successPart.stream().map(ValidationResult::getSource).collect(Collectors.toList());
        List<String> riskCodes = excelOrders.stream().map(ExcelLongTermOrderDTO::getRiskCode).distinct().collect(Collectors.toList());

        List<SysRisk> risks = riskMapper.selectByCodes(riskCodes, null);

        Map<String, SysRisk> riskMap = LambdaUtils.safeToMap(risks, SysRisk::getRiskCode);
        successPart.stream()
                .forEach(item -> {
                    ExcelLongTermOrderDTO order = item.getSource();
                    SysRisk risk = riskMap.get(order.getRiskCode());
                    if (risk == null) {
                        item.addMessage("险种编码不存在");
                        return;
                    }
                    if (!Objects.equals(risk.getRiskName(), order.getRiskName())) {
                        item.addMessage("险种名称和险种编码不匹配");
                    }
                    if (!validPayPeriod(order.getPayingType(), order.getPayPeriodType(), order.getPayPeriod(), risk)) {
                        item.addMessage("缴费期限配置不存在");
                    }
                    if (!validSafePeriod(order.getSafePeriodType(), order.getSafePeriod(), risk)) {
                        item.addMessage("保险期限配置不存在");
                    }
                    order.setRiskId(Long.valueOf(risk.getId()));
                });
    }

    /**
     * TODO 验证缴费期间
     *
     * @param payPeriodType
     * @param payPeriod
     * @param risk
     * @return
     */
    private boolean validPayPeriod(String payingType, String payPeriodType, Integer payPeriod, SysRisk risk) {
        String paywayRule = risk.getPayWay();
        Set<String> paywayRules = new HashSet<>();
        if (StringUtils.isNotBlank(paywayRule)) {
            TypeReference<Set<String>> tr = new TypeReference<Set<String>>() {
            };
            paywayRules = JSON.parseObject(paywayRule, tr);
        }
        if (!paywayRules.contains(payingType)) {
            return false;
        }
        if (EnumRiskPayWay.ALL.getCode().equals(payingType)) {
            return EnumWhalePayPeriodType.PAYMENT_PERIOD_TYPE_1.getInsCode().equals(payPeriodType) && 1 == payPeriod;
        }
        String pattern = "{0}";

        if (EnumWhalePayPeriodType.PAYMENT_PERIOD_TYPE_1.getInsCode().equals(payPeriodType)) {
            pattern = "{0}Y";
        }
        if (EnumWhalePayPeriodType.PAYMENT_PERIOD_TYPE_4.getInsCode().equals(payPeriodType)) {
            pattern = "<{0}Y";
        }
        String coveredYear = risk.getCoveredYears();
        Set<String> coveredYears = new HashSet<>();
        if (StringUtils.isNotBlank(coveredYear)) {
            coveredYears = JSON.parseObject(coveredYear, Set.class);
        }
        String period = MessageFormat.format(pattern, payPeriod);
        return coveredYears.contains(period);
    }


    /**
     * TODO 验证保障期间
     *
     * @param safePeriod
     * @return
     */
    private boolean validSafePeriod(String safePeriodType, Integer safePeriod, SysRisk risk) {
        String rule = risk.getValidPeriod();
        Set<String> periodSet = new HashSet<>();
        if (rule != null) {
            periodSet = JSON.parseObject(rule, Set.class);
        }
        if (EnumWhaleInsuredPeriodType.FULL.getInsCode().equals(safePeriodType)) {
            return periodSet.contains("ALL");
        }
        if (EnumWhaleInsuredPeriodType.ONLY_AGE.getInsCode().equals(safePeriodType)) {
            String pattern = "<{0}Y";
            String period = MessageFormat.format(pattern, safePeriod);
            return periodSet.contains(period);
        }
        String pattern = "{0}";
        if (EnumWhaleInsuredPeriodType.ONLY_YEAR.getInsCode().equals(safePeriodType)) {
            pattern = "{0}Y";
        }
        if (EnumWhaleInsuredPeriodType.ONLY_MONTH.getInsCode().equals(safePeriodType)) {
            pattern = "{0}M";
        }
        if (EnumWhaleInsuredPeriodType.ONLY_DAY.getInsCode().equals(safePeriodType)) {
            pattern = "{0}D";
        }
        String period = MessageFormat.format(pattern, safePeriod);
        return periodSet.contains(period);
    }

    /**
     * 产品计划验证.
     * 同时关联出产品Id
     *
     * @param context
     */
    public void validPlan4ShortTerm(ValidContext<ExcelShortTermOrderDTO> context) {
        String channel = context.getChannel();
        List<ValidationResult<ExcelShortTermOrderDTO>> validData = context.getValidationData();
        List<ExcelShortTermOrderDTO> excelOrders = validData.stream().map(ValidationResult::getSource).collect(Collectors.toList());
        List<String> fhProductIds = excelOrders.stream().map(ExcelShortTermOrderDTO::getFhProductId).distinct().collect(Collectors.toList());
        log.info("[短险数据导入]投保计划：{},{}", channel, fhProductIds);
        if (CollectionUtils.isEmpty(fhProductIds)) {
            return;
        }
        List<SmPlanVO> planList = productMapper.listPlanByFhProductIds(channel, fhProductIds);
        Map<String, SmPlanVO> planMap = LambdaUtils.safeToMap(planList, SmPlanVO::getFhProductId);
        log.info("[短险数据导入]产品计划配置库查询：{},{}", channel, planMap);
        validData.stream()
                .collect(Collectors.partitioningBy(order -> {
                    String thirdPlanId = order.getSource().getFhProductId();
                    SmPlanVO smPlanVO = planMap.get(thirdPlanId);
                    if (Objects.isNull(smPlanVO)) {
                        order.addMessage("当前渠道无法关联到计划id,请仔细检查渠道和计划配置：" + thirdPlanId);
                        return Boolean.FALSE;
                    }

                    String productName = smPlanVO.getProductName();
                    String importProductName = order.getSource().getProductName();
                    if (!Objects.equals(productName, order.getSource().getProductName())) {
                        order.addMessage("系统配置的产品名称:[" + productName + "]与导入的产品名称:[" + importProductName + "]不一致,请仔细核对");
                        return Boolean.FALSE;
                    }

                    if (!EnumProductAttr.PERSON.getCode().equals(smPlanVO.getProductAttrCode())) {
                        order.addMessage("该产品不是个险产品，不能选择个险模板导入方式");
                        return Boolean.FALSE;
                    }

                    order.getSource().setCompanyId(smPlanVO.getCompanyId());
                    order.getSource().setProductId(smPlanVO.getProductId());
                    order.getSource().setPlanId(smPlanVO.getPlanId());
                    return Boolean.TRUE;
                }));
    }

    /**
     * 产品计划验证.
     *
     * @param context
     */
    public void validPlan4LongTerm(ValidContext<ExcelLongTermOrderDTO> context) {
        String channel = context.getChannel();
        List<ValidationResult<ExcelLongTermOrderDTO>> successPart = context.getValidationData();
        List<ExcelLongTermOrderDTO> excelOrders = successPart.stream().map(ValidationResult::getSource).collect(Collectors.toList());
        List<String> fhProductIds = excelOrders.stream().map(ExcelLongTermOrderDTO::getFhProductId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fhProductIds)) {
            return;
        }
        Map<String, SmPlanVO> planMap = LambdaUtils.safeToMap(productMapper.listPlanByFhProductIds(channel, fhProductIds),
                SmPlanVO::getFhProductId);
        successPart
                .stream()
                .forEach(order -> {
                    SmPlanVO smPlanVO = planMap.get(order.getSource().getFhProductId());
                    if (Objects.isNull(smPlanVO)) {
                        order.addMessage("计划id有误,当前渠道无法关联到该计划id:" + order.getSource().getFhProductId());
                        return;
                    }
                    String productName = smPlanVO.getProductName();
                    String importProductName = order.getSource().getProductName();
                    if (!Objects.equals(productName, importProductName)) {
                        order.addMessage("系统配置的产品名称:[" + productName + "]与导入的产品名称:[" + importProductName + "]不一致,请仔细核对");
                        return;
                    }
                    if (!EnumProductAttr.PERSON.getCode().equals(smPlanVO.getProductAttrCode())) {
                        order.addMessage("该产品不是个险产品，不能选择个险模板导入方式");
                        return;
                    }
                    order.getSource().setCompanyId(smPlanVO.getCompanyId());
                    order.getSource().setProductId(smPlanVO.getProductId());
                    order.getSource().setPlanId(smPlanVO.getPlanId());
                    return;
                });
    }

    /**
     * 校验数据合法性
     *
     * @param order
     * @return
     */
    private ValidationResult<ExcelLongTermOrderDTO> validLongTermOrder(String type, ExcelLongTermOrderDTO order) {
        order.fixTime();
        /**
         * 数据校验，包含多个group
         */
        Class validRule = getValidRule(type);
        Set<ConstraintViolation<ExcelLongTermOrderDTO>> validate = validator.validate(order, Default.class, validRule);

        /**
         * 基础校验 非空 简单正则等
         */
        if (!CollectionUtils.isEmpty(validate)) {
            String errorMsg = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            return new ValidationResult<>(errorMsg, order);
        }
        order.initDefaultField();
        ValidationResult<ExcelLongTermOrderDTO> success = ValidationResult.success(order);
        // 如果是身份证校验证件号码 然后设置性别年龄
        if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getApplicantIdNumber();
            if (!IdCardUtils.validate(idNumber)) {
                success.addMessage("投保人证件号码错误");
            } else {
                order.setApplicantBirthday(getBirthdayByIdCard(idNumber));
                order.setApplicantSex(getSex(idNumber));
            }
        }
        if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getInsuredIdNumber();
            if (!IdCardUtils.validate(idNumber)) {
                success.addMessage("被保人证件号码错误");
            } else {
                //根据证件号码生成生日
                order.setInsuredBirthday(getBirthdayByIdCard(idNumber));
                order.setInsuredSex(getSex(idNumber));
            }
        }

        //如果证件类型为其他，校验证件号码
        String reg = "^[Xx][Nn].*$";
        if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
            String idNumber = order.getApplicantIdNumber();
            if (!idNumber.matches(reg)) {
                success.addMessage("投保人证件号码不是“XN”开头");
            }
        }
        if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
            String idNumber = order.getInsuredIdNumber();
            if (!idNumber.matches(reg)) {
                success.addMessage("被保人证件号码不是“XN”开头");
            }
        }

        if (StringUtils.isNotBlank(order.getApplicantMobile())) {
            if (!StringTools.validMobile(order.getApplicantMobile())) {
                success.addMessage("投保人手机号格式不正确:"+order.getApplicantMobile());
            }
        }

        if (StringUtils.isNotBlank(order.getInsuredMobile())) {
            if (!StringTools.validMobile(order.getInsuredMobile())) {
                success.addMessage("被保人手机号格式不正确:"+order.getInsuredMobile());
            }
        }

        //校验保单状态
        if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.SUCCESS.getDesc())) {
            order.setAppStatus(EnumInsuredAppStatus.SUCCESS.getCode());
        } else if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.CANCEL_SUCCESS.getDesc())) {
            order.setAppStatus(EnumInsuredAppStatus.CANCEL_SUCCESS.getCode());
        } else {
            success.addMessage("只能导入退保成功和承保成功的订单");
        }
        if (order.getEndTime().compareTo(order.getStartTime()) < 1) {
            success.addMessage("保单失效时间不能早于保单起保时间");
        }
        validate = validator.validate(order, SmOrderExcelDTO.InitAfter.class);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String errorMsg = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            success.addMessage(errorMsg);
        }
        return success;
    }

    /**
     * 校验数据合法性
     *
     * @param order
     * @return
     */
    private ValidationResult<ExcelShortTermOrderDTO> validShortTermOrder(ExcelShortTermOrderDTO order) {
        order.fixTime();
        /**
         * 数据校验，包含多个group
         */
        Set<ConstraintViolation<ExcelShortTermOrderDTO>> validate = validator.validate(order, Default.class, SmOrderExcelDTO.ShortTermInsRule.class);

        /**
         * 基础校验 非空 简单正则等
         */
        if (!CollectionUtils.isEmpty(validate)) {
            String errorMsg = validate.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
            return new ValidationResult<>(errorMsg, order);
        }
        order.initDefaultField();
        ValidationResult<ExcelShortTermOrderDTO> success = ValidationResult.success(order);
        // 如果是身份证校验证件号码 然后设置性别年龄
        if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getApplicantIdNumber();
            if (!IdCardUtils.validate(idNumber)) {
                success.addMessage("投保人证件号码错误");
            } else {
                order.setApplicantBirthday(getBirthdayByIdCard(idNumber));
                order.setApplicantSex(getSex(idNumber));
            }
        }
        if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
            String idNumber = order.getInsuredIdNumber();
            if (!IdCardUtils.validate(idNumber)) {
                success.addMessage("被保人证件号码错误");
            } else {
                //根据证件号码生成生日
                order.setInsuredBirthday(getBirthdayByIdCard(idNumber));
                order.setInsuredSex(getSex(idNumber));
            }
        }

        //如果证件类型为其他，校验证件号码
        String reg = "^[Xx][Nn].*$";
        if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
            String idNumber = order.getApplicantIdNumber();
            if (!idNumber.matches(reg)) {
                success.addMessage("投保人证件号码不是“XN”开头");
            }
        }
        if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE_OTHER)) {
            String idNumber = order.getInsuredIdNumber();
            if (!idNumber.matches(reg)) {
                success.addMessage("被保人证件号码不是“XN”开头");
            }
        }

        if (StringUtils.isNotBlank(order.getApplicantMobile())) {
            if (!StringTools.validMobile(order.getApplicantMobile())) {
                success.addMessage("投保人手机号格式不正确:"+order.getApplicantMobile());
            }
        }

        if (StringUtils.isNotBlank(order.getInsuredMobile())) {
            if (!StringTools.validMobile(order.getInsuredMobile())) {
                success.addMessage("被保人手机号格式不正确:"+order.getInsuredMobile());
            }
        }

        //校验保单状态
        if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.SUCCESS.getDesc())) {
            order.setAppStatus(EnumInsuredAppStatus.SUCCESS.getCode());
        } else if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.CANCEL_SUCCESS.getDesc())) {
            order.setAppStatus(EnumInsuredAppStatus.CANCEL_SUCCESS.getCode());
        } else {
            success.addMessage("只能导入退保成功和承保成功的订单");
        }
        if (order.getEndTime().compareTo(order.getStartTime()) < 1) {
            success.addMessage("保单失效时间不能早于保单起保时间");
        }
        validate = validator.validate(order, SmOrderExcelDTO.InitAfter.class);
        //基础校验 非空 简单正则等
        if (!CollectionUtils.isEmpty(validate)) {
            String errorMsg = validate.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            //校验出问题
            success.addMessage(errorMsg);
        }
        return success;
    }

    /**
     * 修正时间
     *
     * @param order
     */
    private void fixTime(SmOrderExcelDTO order) {
        if (Objects.nonNull(order.getStartTime()) && order.getStartTime().length() == 10) {
            order.setStartTime(order.getStartTime() + " 00:00:00");
        }
        if (Objects.nonNull(order.getEndTime()) && order.getEndTime().length() == 10) {
            order.setEndTime(order.getEndTime() + " 23:59:59");
        }
    }

    /**
     * 验证保单时间是否合法
     *
     * @param type
     * @param order
     * @return
     */
    private String validPolicyTime(String type, SmOrderExcelDTO order) {
        Date submitTime = order.parseCreateTime();
        Date startTime = order.parseStartTime();
        Date endTime = order.parseEndTime();
        if (submitTime == null) {
            return "订单创建时间格式不正确";
        }
        if (startTime == null) {
            return "保险起期格式不正确";
        }
        if (endTime == null) {
            return "保险止期格式不正确";
        }
        if (endTime.before(startTime)) {
            return "失效日期须大于起保日期";
        }
        if (startTime.before(submitTime)) {
            return "起保日期须大于订单创建时间";
        }
        return null;
    }

    /**
     * 校验数据合法性
     *
     * @param orders
     * @return
     */
    private List<ValidationResult<SmOrderExcelDTO>> validExcelData(String importType, List<SmOrderExcelDTO> orders) {
        return orders.stream()
                .map(order -> {
                    fixTime(order);
                    /**
                     * 数据校验，包含多个group
                     */
                    Class validRule = getValidRule(importType);
                    Set<ConstraintViolation<SmOrderExcelDTO>> validate = validator.validate(order, Default.class, validRule);

                    /**
                     * 基础校验 非空 简单正则等
                     */
                    if (!CollectionUtils.isEmpty(validate)) {
                        String errorMsg = validate.stream()
                                .map(ConstraintViolation::getMessage)
                                .collect(Collectors.joining(","));
                        return new ValidationResult<>(errorMsg, order);
                    }
                    //证件类型 默认为身份证
                    if (StringUtils.isBlank(order.getApplicantIdType())) {
                        order.setApplicantIdType(SmOrderExcelDTO.ID_CARD_TYPE);
                    }
                    if (StringUtils.isBlank(order.getInsuredIdType())) {
                        order.setInsuredIdType(SmOrderExcelDTO.ID_CARD_TYPE);
                    }
                    ValidationResult<SmOrderExcelDTO> success = ValidationResult.success(order);
                    // 如果是身份证校验证件号码 然后设置性别年龄
                    if (Objects.equals(order.getApplicantIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
                        String idNumber = order.getApplicantIdNumber();
                        if (!IdCardUtils.validate(idNumber)) {
                            success.addMessage("投保人证件号码错误");
                        } else {
                            order.setApplicantBirthday(getBirthdayByIdCard(idNumber));
                            order.setApplicantSex(getSex(idNumber));
                        }
                    }
                    if (Objects.equals(order.getInsuredIdType(), SmOrderExcelDTO.ID_CARD_TYPE)) {
                        String idNumber = order.getInsuredIdNumber();
                        if (!IdCardUtils.validate(idNumber)) {
                            success.addMessage("被保人证件号码错误");
                        } else {
                            //根据证件号码生成生日
                            order.setInsuredBirthday(getBirthdayByIdCard(idNumber));
                            order.setInsuredSex(getSex(idNumber));
                        }
                    }

                    //校验保单状态
                    if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.SUCCESS.getDesc())) {
                        order.setAppStatus(EnumInsuredAppStatus.SUCCESS.getCode());
                    } else if (Objects.equals(order.getAppStatus(), EnumInsuredAppStatus.CANCEL_SUCCESS.getDesc())) {
                        order.setAppStatus(EnumInsuredAppStatus.CANCEL_SUCCESS.getCode());
                    } else {
                        success.addMessage("只能导入退保成功和承保成功的订单");
                    }

                    String timeErrorMsg = validPolicyTime(importType, order);
                    if (StringUtils.isNotBlank(timeErrorMsg)) {
                        success.addMessage(timeErrorMsg);
                    }

                    validate = validator.validate(order, SmOrderExcelDTO.InitAfter.class);
                    //基础校验 非空 简单正则等
                    if (!CollectionUtils.isEmpty(validate)) {
                        String errorMsg = validate.stream()
                                .map(ConstraintViolation::getMessage)
                                .collect(Collectors.joining(","));
                        //校验出问题
                        success.addMessage(errorMsg);
                    }
                    return success;
                }).collect(Collectors.toList());
    }


    private void validateGroupOrder(SmOrderExcelDTO order, ValidationResult<SmOrderExcelDTO> success) {

        if (order.isGroupOrder()) {
            if (order.getItemAmount() == null || order.getItemAmount().compareTo(BigDecimal.ZERO) < 0) {
                success.addMessage("团单的分单保费不能为空或小于0");
            }
            if (StringUtils.isBlank(order.getEndorsementNo())) {
                success.addMessage("团险订单的批单号必填");
            }
            if (StringUtils.isBlank(order.getEndorsementType())) {
                success.addMessage("团险订单的批改类型必填");
            } else if (!OrderImportEndorsementTypeEnum.INCREASE.isMe(order.getEndorsementType())
                    && !OrderImportEndorsementTypeEnum.DECREASE.isMe(order.getEndorsementType())
                    && !OrderImportEndorsementTypeEnum.NEW.isMe(order.getEndorsementType())) {
                success.addMessage("团险订单的批改类型必填");
            }
            if (OrderImportEndorsementTypeEnum.INCREASE.isMe(order.getEndorsementType()) && Objects.isNull(order.getValidateTime())) {
                success.addMessage("团险批增订单的批改生效日期必填");
            }

            if (OrderImportEndorsementTypeEnum.DECREASE.isMe(order.getEndorsementType())) {
                if (Objects.isNull(order.getValidateTime())) {
                    success.addMessage("团险批减订单的批改生效日期必填");
                }
                if (Objects.isNull(order.getTerminateTime())) {
                    success.addMessage("团险批减订单的批改失效日期必填");
                }
                if (Objects.nonNull(order.getTerminateTime()) && order.getTerminateTime().compareTo(order.getValidateTime()) < 1) {
                    success.addMessage("批改失效日期不能早于批改生效日期");
                }
            }

            if (Objects.nonNull(order.getValidateTime()) && order.getValidateTime().length() == 10) {
                order.setValidateTime(order.getValidateTime() + " 00:00:00");
            }
            if (Objects.nonNull(order.getTerminateTime()) && order.getTerminateTime().length() == 10) {
                order.setTerminateTime(order.getTerminateTime() + " 23:59:59");
            }

            if (OrderImportEndorsementTypeEnum.DECREASE.isMe(order.getEndorsementType()) && Objects.equals(order.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                success.addMessage("团单批减类型只能为退保成功");
            } else if (OrderImportEndorsementTypeEnum.INCREASE.isMe(order.getEndorsementType()) && Objects.equals(order.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                success.addMessage("团单批增类型只能为承保成功");
            }
        }
    }

    /**
     * 验证状态一致性
     * 同一个订单，要么全部承保成功，要么全部退保成功，主要考虑到订单金额的计算问题
     *
     * @param context
     */
    private void validApplyUniformity4ShortTerm(ValidContext<ExcelShortTermOrderDTO> context) {
        List<ValidationResult<ExcelShortTermOrderDTO>> data = context.getValidationData();
        Map<String, List<ValidationResult<ExcelShortTermOrderDTO>>> orderGroup = data.stream()
                .filter(entry -> entry.isSuccess())
                .collect(
                        Collectors.groupingBy(entry -> {
                            ExcelShortTermOrderDTO order = entry.getSource();
                            return order.getOrderNo();
                        })
                );
        for (Map.Entry<String, List<ValidationResult<ExcelShortTermOrderDTO>>> entry : orderGroup.entrySet()) {
            List<ValidationResult<ExcelShortTermOrderDTO>> orderList = entry.getValue();
            if (orderList == null) {
                continue;
            }
            String lastAppStatus = null;
            boolean allFail = false;
            for (ValidationResult<ExcelShortTermOrderDTO> valid : orderList) {
                ExcelShortTermOrderDTO order = valid.getSource();
                if (lastAppStatus == null) {
                    lastAppStatus = order.getAppStatus();
                } else if (!Objects.equals(lastAppStatus, order.getAppStatus())) {
                    allFail = true;
                    break;
                }
            }
            if (allFail) {
                orderList.stream().forEach(valid -> valid.addMessage("同一个订单的分单状态必须相同，否则请分批导入"));
            }
        }
    }

    /**
     * 个险.保费校验逻辑
     * 导入场景.不存在同一个订单同时存在批增批减的情况
     *
     * @param context
     */
    private void validPremium4ShortTerm(ValidContext<ExcelShortTermOrderDTO> context) {
        List<ValidationResult<ExcelShortTermOrderDTO>> successPart = context.getValidationData();
        if (CollectionUtils.isEmpty(successPart)) {
            return;
        }
        Map<String, List<ValidationResult<ExcelShortTermOrderDTO>>> orderGroup = successPart.stream()
                .filter(entry -> entry.isSuccess())
                .collect(
                        Collectors.groupingBy(entry -> {
                            ExcelShortTermOrderDTO data = entry.getSource();
                            return data.getOrderNo();
                        })
                );
        for (Map.Entry<String, List<ValidationResult<ExcelShortTermOrderDTO>>> entry : orderGroup.entrySet()) {
            List<ValidationResult<ExcelShortTermOrderDTO>> orderList = entry.getValue();
            BigDecimal orderAmt = orderList.get(0)
                    .getSource()
                    .getAmount();

            BigDecimal totalPremium = orderList.stream()
                    .map(ValidationResult::getSource)
                    .filter(a -> a.getItemAmount() != null)
                    .map(ExcelShortTermOrderDTO::getItemAmount)
                    .reduce((a, b) -> a.add(b))
                    .orElseGet(() -> new BigDecimal(0));
            if (CompareTools.safeCompare(orderAmt, totalPremium) != 0) {
                orderList.stream()
                        .forEach(a -> {
                            a.addMessage("订单金额跟分单保费之和不对，请仔细核对保费或者可能当前订单存在错误的分单信息");
                        });
            }
        }
    }

    /**
     * 长险.保费校验逻辑
     * 订单保费等于险种保费之和
     *
     * @param context
     */
    public void validPremium4LongTerm(ValidContext<ExcelLongTermOrderDTO> context) {
        List<ValidationResult<ExcelLongTermOrderDTO>> successPart = context.getValidationData();
        if (CollectionUtils.isEmpty(successPart)) {
            return;
        }
        Map<String, List<ValidationResult<ExcelLongTermOrderDTO>>> orderGroup = successPart.stream()
                .collect(Collectors.groupingBy(a -> {
                    ExcelLongTermOrderDTO data = a.getSource();
                    return data.getOrderNo();
                }));
        for (Map.Entry<String, List<ValidationResult<ExcelLongTermOrderDTO>>> entry : orderGroup.entrySet()) {
            List<ValidationResult<ExcelLongTermOrderDTO>> orderList = entry.getValue();
            int qty = orderList.size();
            BigDecimal orderAmt = orderList.get(0).getSource().getAmount();
            BigDecimal totalPremium = orderList.stream()
                    .map(a -> {
                        ExcelLongTermOrderDTO order = a.getSource();
                        if (order.getItemQty() != qty) {
                            a.addMessage("当前保单存在错误的分单");
                        }
                        BigDecimal im = order.getItemAmount();
                        im = im == null ? new BigDecimal(0) : im;
                        return im;
                    })
                    .reduce((a, b) -> a.add(b))
                    .orElseGet(() -> new BigDecimal(0));

            if (orderAmt == null || orderAmt.compareTo(totalPremium) != 0) {
                orderList.stream()
                        .forEach(a -> {
                            a.addMessage("保单保费不等于险种保费之和");
                        });
            }
        }
    }

    //验证团单订单总与分单保费金额是否一直
    @Deprecated
    private void validatorGroupAmt(List<ValidationResult<SmOrderExcelDTO>> success) {
        if (CollectionUtils.isEmpty(success)) {
            return;
        }
        //团单需要校验保费总额与分单保费是否一直，且必须等所有其他条件验证通过以后才能验证，保费总额!=分单保费之和则整个保单不能导入
        List<SmOrderExcelDTO> successList = success.stream()
                .filter(entry -> entry.isSuccess())
                .map(ValidationResult::getSource)
                .collect(Collectors.toList());
        //根据保单号分组
        Map<String, List<SmOrderExcelDTO>> orderGroup = LambdaUtils.groupBy(successList, SmOrderExcelDTO::getPolicyNo);
        Map<String, String> errorMsgMap = new HashMap<>();
        for (String key : orderGroup.keySet()) {
            List<SmOrderExcelDTO> dtoLst = orderGroup.get(key);
            SmOrderExcelDTO dto = dtoLst.get(0);

            if (dto.isGroupOrder()) {
                BigDecimal amt = dtoLst.get(0).getAmount();
                BigDecimal totalAmt = dtoLst.stream()
                        .map(SmOrderExcelDTO::getItemAmount)
                        .reduce(BigDecimal.ZERO, (a, b) -> a.add(b));

                //订单总金额和分单保费总额不一致
                if (amt.compareTo(totalAmt) != 0) {
                    errorMsgMap.put(key, "订单总金额和分单保费总额不一致");
                }
            }
        }

        for (ValidationResult<SmOrderExcelDTO> result : success) {
            SmOrderExcelDTO source = result.getSource();
            String err = errorMsgMap.get(source.getPolicyNo());
            if (StringUtils.isNotBlank(err)) {
                result.addMessage(err);
            }
        }
    }

    /**
     * 众安I云宝校验：只是同步了推荐人信息
     */
    private void iybMapper(List<ValidationResult<ExcelOrderDTO>> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<String> policyNos = data.stream()
                .map(ValidationResult::getSource)
                .map(ExcelOrderDTO::getPolicyNo)
                .distinct().collect(Collectors.toList());

        Map<String, SmPolicyRegister> mapByPolicyNos = registerService.getMapByPolicyNos(policyNos);

        for (ValidationResult<? extends ExcelOrderDTO> result : data) {
            ExcelOrderDTO source = result.getSource();
            SmPolicyRegister register = mapByPolicyNos.get(source.getPolicyNo());
            if (Objects.isNull(register)) {
                result.addMessage("匹配不上推荐人");
            } else {
                source.setRecommendUserId(register.getUserId());
                source.setRecommendName(register.getUserName());
            }
        }
    }

    /**
     * 众安I云宝校验：只是同步了推荐人信息
     */
    private void zaIyb(List<ValidationResult<SmOrderExcelDTO>> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<String> policyNos = data.stream()
                .map(ValidationResult::getSource)
                .map(SmOrderExcelDTO::getPolicyNo)
                .distinct().collect(Collectors.toList());

        Map<String, SmPolicyRegister> mapByPolicyNos = registerService.getMapByPolicyNos(policyNos);

        for (ValidationResult<SmOrderExcelDTO> result : data) {
            SmOrderExcelDTO source = result.getSource();
            SmPolicyRegister register = mapByPolicyNos.get(source.getPolicyNo());
            if (Objects.isNull(register)) {
                result.addMessage("匹配不上推荐人");
            } else {
                source.setRecommendUserId(register.getUserId());
                source.setRecommendName(register.getUserName());
            }
        }
    }

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class CodeMapperHelper {
        String companyId;

        Set<String> fieldCode;

        Set<String> optionName;
    }

}
