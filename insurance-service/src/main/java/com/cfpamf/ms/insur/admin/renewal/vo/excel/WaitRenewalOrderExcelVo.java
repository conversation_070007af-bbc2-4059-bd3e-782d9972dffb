package com.cfpamf.ms.insur.admin.renewal.vo.excel;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumRenewalIntention;
import com.cfpamf.ms.insur.admin.renewal.vo.BaseOrderVo;
import com.cfpamf.ms.insur.base.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

/**
 * 待续保订单视图excel对象
 *
 * <AUTHOR>
 * @date 2021/5/13 10:26
 */
@Getter
@Setter
public class WaitRenewalOrderExcelVo {

    private static final String PRODUCT_ATTR_CODE_PERSON = "person";
    private static final String PRODUCT_ATTR_CODE_GROUP = "group";
    private static final String ORDER_TYPE_PERSON = "个险";
    private static final String ORDER_TYPE_GROUP = "团险";
    private static final String ORDER_TYPE_DEFAULT = "无";
    private static final String YYYY_MM_DD = "yyyy/MM/dd";
    @ExportField(name = "订单类别", order = 0)
    private String orderType;

    @ExportField(name = "投保产品", order = 1)
    private String productName;

    @ExportField(name = "投保人姓名", order = 2)
    private String applicantPersonName;

    @ExportField(name = "被保人姓名", order = 3)
    private String insuredPersonName;

    @ExportField(name = "保费", order = 4, type = "money")
    private BigDecimal insuredAmount;

    @ExportField(name = "保单号", order = 5)
    private String policyNo;

    @ExportField(name = "失效时间", order = 6)
    private String invalidTime;

    @ExportField(name = "状态", order = 7)
    private String status;

    @ExportField(name = "推荐人姓名", order = 8)
    private String recommendUserName;


    @ExportField(name = "管护客户经理", order = 9)
    private String customerManager;

    @ExportField(name = "管护客户经理区域", order = 10)
    private String customerManagerRegion;

    @ExportField(name = "管护客户经理组织", order = 11)
    private String customerManagerOrganization;

    @ExportField(name = "跟进情况", order = 12)
    private String intention;

    @ExportField(name = "自保件", order = 13)
    private String selfInsuredName;

    @ExportField(name = "是否异业客户", order = 14)
    private String judgeCustomerLoan;


    public WaitRenewalOrderExcelVo() {

    }

    public WaitRenewalOrderExcelVo(BaseOrderVo baseOrderVo) {
        this.orderType = getOrderTypeByProductAttrCode(baseOrderVo.getProductAttrCode());
        this.productName = baseOrderVo.getProductName();
        this.applicantPersonName = baseOrderVo.getApplicantPersonName();
        this.insuredPersonName = baseOrderVo.getInsuredPersonName();
        this.insuredAmount = baseOrderVo.getInsuredAmount();
        this.policyNo = baseOrderVo.getPolicyNo();
        this.invalidTime = baseOrderVo.getInvalidTime().format(DateTimeFormatter.ofPattern(YYYY_MM_DD));
        this.status = getStatusBySurplusDay(baseOrderVo.getSurplusDay());
        this.recommendUserName = baseOrderVo.getRecommendUserName();
        this.customerManager = baseOrderVo.getCustomerManager();
        this.customerManagerOrganization = baseOrderVo.getCustomerManagerOrganization();
        this.customerManagerRegion = baseOrderVo.getCustomerManagerRegion();
        this.intention = baseOrderVo.getIntention();
        this.selfInsuredName = baseOrderVo.getSelfInsuredName();
        this.judgeCustomerLoan = baseOrderVo.getJudgeCustomerLoan();
    }

    private String getOrderTypeByProductAttrCode(String productAttrCode) {

        if (PRODUCT_ATTR_CODE_PERSON.equals(productAttrCode)) {
            return ORDER_TYPE_PERSON;
        }

        if (PRODUCT_ATTR_CODE_GROUP.equals(productAttrCode)) {
            return ORDER_TYPE_GROUP;
        }
        return ORDER_TYPE_DEFAULT;
    }

    private String getStatusBySurplusDay(Integer surplusDay) {
        if (surplusDay > 0) {
            return "宽限期" + surplusDay + "天到期";
        } else {
            return "续保" + (surplusDay * -1) + "天到期";
        }
    }
}
