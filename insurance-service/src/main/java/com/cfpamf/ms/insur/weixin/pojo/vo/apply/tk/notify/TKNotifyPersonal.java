package com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TKNotifyPersonal {

    @ApiModelProperty(value = "渠道个单号", required = true)
    private String channelPolicyNo;

    @ApiModelProperty(value = "泰康分单号", required = true)
    private String personalPolicyNo;

    @ApiModelProperty(value = "泰康个人投保单号")
    private String effectiveTime;

    @ApiModelProperty(value = "个单子方案")
    private String expiredTime;

    @ApiModelProperty(value = "被保人姓名", required = true)
    private String name;

    @ApiModelProperty(value = "被保人证件类型", required = true)
    private String credentialType;

    @ApiModelProperty(value = "被保人证件号码", required = true)
    private String credentialNo;

    @ApiModelProperty(value = "电子保单地址，只有status为1的时候会传", required = true)
    private String epolicyUrl;

    @ApiModelProperty(value = "保单状态：1：有效（表示已出单)", required = true)
    private String status;

    @ApiModelProperty(value = "批减退费金额，单位：分", required = true)
    private Long refundPremium;

    @ApiModelProperty(value = "渠道openid 非必传", required = true)
    private String channelOpenid;

    @ApiModelProperty(value = "泰康openid 非必传", required = true)
    private String taikangOpenid;

    @ApiModelProperty(value = "来源 非必传 医药网=1，线下药房=2", required = true)
    private String pharmacySource;

    @ApiModelProperty(value = "药品盒数（医路）")
    private String boxNum;

    @ApiModelProperty(value = "个单申请单号（医路）")
    private String personalAppEndorNo;

}
