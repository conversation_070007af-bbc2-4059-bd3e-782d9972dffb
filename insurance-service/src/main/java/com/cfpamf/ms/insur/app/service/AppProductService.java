package com.cfpamf.ms.insur.app.service;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.ProductMinAmtDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductFormFieldCombDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductFormFieldDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.OccupationService;
import com.cfpamf.ms.insur.admin.service.SmCmpySettingService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.app.dao.AppProductMapper;
import com.cfpamf.ms.insur.app.pojo.query.AppProductQuery;
import com.cfpamf.ms.insur.app.pojo.vo.*;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxFormFieldsVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.cfpamf.ms.insur.weixin.service.WxHomeProductService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AppProductService {

    /**
     * 通过p标签切分html正则表达式
     */
    private final Pattern pattern = Pattern.compile("<p.*?>(.*?)</p>");
    /**
     * redis 缓存
     */
    @Autowired
    protected RedisUtil<String, String> redisUtil;
    /**
     * 产品service
     */
    @Autowired
    private SmProductService smProductService;
    /**
     * 产品mapper
     */
    @Autowired
    private SmProductMapper smProductMapper;
    /**
     * app产品dao
     */
    @Autowired
    private AppProductMapper appProductMapper;
    /**
     * 保险公司职业service
     */
    @Autowired
    private OccupationService occupationService;
    /**
     * 产品配置service
     */
    @Autowired
    private SmCmpySettingService smCmpySettingService;

    /**
     * 和微信一样的地方直接使用微信的service
     */
    @Autowired
    private WxHomeProductService homeProductService;

    /**
     * 查询产品分类
     *
     * @return
     */
    public List<AppCategoryVO> getProductCategory() {
        List<AppCategoryVO> categorys = appProductMapper.listProductCategory();
        categorys.stream().filter(c -> Objects.equals(c.getCategoryCode(), "RX")).findFirst()
                .ifPresent(c -> c.setCategoryId("hot"));
        AppCategoryVO all = new AppCategoryVO();
        all.setCategoryId(null);
        all.setCategoryName("全部");
        categorys.add(all);
        return categorys;
    }

    private void resetMinAmount() {

    }

    /**
     * 查询app产品列表
     */
    /**
     * 查询微信产品列表
     *
     * @param query
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_LIST,
            key = "'_app_categoryId'+#query.categoryId  +'page' + #query.page  + 'size'+#query.size + 'regionName'+#query.regionName+'q'+#query.productName"
    )
    public PageInfo<AppProductListVO> getProductList(AppProductQuery query) {
        if (Objects.equals(query.getCategoryId(), "hot")) {
            query.setPage(1);
            query.setSize(5);
            query.setActive(null);
            PageHelper.startPage(query.getPage(), query.getSize(), false);
            List<AppProductListVO> hots = appProductMapper.listAppHotProducts(query);

            // 查询推荐产品
            query.setActive(1);
            List<AppProductListVO> actives = appProductMapper.listAppHotProducts(query);
            hots.forEach(m -> {
                if (actives.stream().noneMatch(x -> x.getProductId().equals(m.getProductId()))) {
                    actives.add(m);
                }
            });
            PageInfo<AppProductListVO> pageInfo = new PageInfo<>(actives);
            resetMinAmt(query.getRegionName(), actives);
            pageInfo.setHasNextPage(false);
            pageInfo.setPages(1);
            pageInfo.setSize(actives.size());
            pageInfo.setTotal(actives.size());
            return pageInfo;
        }
        PageHelper.startPage(query.getPage(), query.getSize());
        List<AppProductListVO> appProductListVOS = appProductMapper.listAppProducts(query);
        resetMinAmt(query.getRegionName(), appProductListVOS);
        return new PageInfo<>(appProductListVOS);
    }

    public WxProductDetailVO getDetailForWx(int productId, String regionName) {
        return homeProductService.getWxProductDetailByIdAndRegionName(productId, regionName, null);
    }

    /**
     * 查询app产品详情
     */
    @Cacheable(cacheNames = PRODUCT_DETAIL, key = "'_app_:'+#productId")
    public AppProductDetailVO getProductDetailByProductId(int productId) {
        AppProductDetailVO productDetailVO = appProductMapper.getProductDetailById(productId);
        if (productDetailVO == null) {
            throw new BizException(ExcptEnum.PRODUCT_NOT_ONLINE_201001);
        }
        productDetailVO.setClauses(appProductMapper.listProductClausesByProductId(productId));
        if (productDetailVO.getAttentions() != null) {
            List<String> textList = splitTextWithLabel(productDetailVO.getAttentions());
            List<AppProductDetailVO.QuestionAnswer> questions = new ArrayList<>();
            for (int i = 0, len = textList.size(); i + 1 < len; i = i + 2) {
                questions.add(AppProductDetailVO.QuestionAnswer.builder().question(textList.get(i))
                        .answer(textList.get(i + 1)).build());
            }
            productDetailVO.setQuestions(questions);
        }
        if (productDetailVO.getHealthNotification() != null) {
            List<String> textList = splitTextWithLabel(productDetailVO.getHealthNotification());
            productDetailVO.setHealthNotifications(textList);
        }
        productDetailVO.setCoveragePlan(buildAppCoveragePlanVO(productId));
        return productDetailVO;
    }

    /**
     * 创建微信产品详情保障项目计划价格表
     *
     * @param productId
     * @return
     */
    private AppCoveragePlanVO buildAppCoveragePlanVO(int productId) {
        List<SmProductCoverageAmountVO> coverageAmounts = smProductMapper.listProductCoverageAmounts(productId);
        List<SmPlanVO> plans = smProductMapper.listProductPlansById(String.valueOf(productId), true);
        List<SmProductCoverageVO> coverages = smProductMapper.listProductCoverages(productId);

        AppCoveragePlanVO coveragePlan = new AppCoveragePlanVO();
        List<AppCoveragePlanVO.CoverageAmount> planCoverageAmounts = new ArrayList<>();
        coveragePlan.setPlanNames(plans.stream().map(p -> {
            AppCoveragePlanVO.Plan plan = new AppCoveragePlanVO.Plan();
            plan.setPlanId(p.getPlanId());
            plan.setPlanName(p.getPlanName());
            plan.setCoverages(appProductMapper.listAppProductPlanCoverages(p.getPlanId()));
            return plan;
        }).collect(Collectors.toList()));
        coverages.forEach(cvg -> {
            AppCoveragePlanVO.CoverageAmount planCoverageAmount = new AppCoveragePlanVO.CoverageAmount();
            planCoverageAmount.setCoverageName(cvg.getCvgItemName());
            List<String> cvgNotices = new ArrayList<>();
            plans.forEach(plan -> {
                Optional<SmProductCoverageAmountVO> optional = coverageAmounts.stream().filter(ca -> Objects.equals(ca.getPlanId(), plan.getId())
                        && Objects.equals(cvg.getSpcId(), ca.getSpcId())).findFirst();
                if (optional.isPresent()) {
                    cvgNotices.add(optional.get().getCvgNotice());
                } else {
                    cvgNotices.add("");
                }
            });
            planCoverageAmount.setCvgNotices(cvgNotices);
            planCoverageAmounts.add(planCoverageAmount);
        });
        coveragePlan.setCoverageAmounts(planCoverageAmounts);

        List<SmPlanFactorPriceDT> planPrices = smProductMapper.listPlanFactorPrices(productId, null);
        List<BigDecimal> planMinPrices = new ArrayList<>();
        plans.forEach(plan -> {
            BigDecimal minPrice = planPrices.stream()
                    .filter(pp -> Objects.equals(pp.getPlanId(), plan.getId()) && !StringUtils.isEmpty(pp.getPrice()))
                    .map(pp -> new BigDecimal(pp.getPrice()))
                    .filter(p -> p.compareTo(BigDecimal.ZERO) > 0)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
            planMinPrices.add(minPrice);
        });
        coveragePlan.setPlanMinPrices(planMinPrices);
        return coveragePlan;
    }

    /**
     * 字符串通过p切分成字符串列表
     *
     * @param origin
     * @return
     */
    private List<String> splitTextWithLabel(String origin) {
        Matcher matcher = pattern.matcher(origin);
        List<String> textList = new ArrayList<>();
        while (matcher.find()) {
            String res = matcher.group();
            String text = Jsoup.parse(res).text();
            if (!StringUtils.isEmpty(text)) {
                textList.add(text);
            }
        }
        return textList;
    }

    /**
     * 查询产品计划价格
     */
    @Cacheable(cacheNames = PRODUCT_PLAN, key = "'_app_'+#productId+':'+#regionName")
    public List<SmPlanVO> getProductPlans(int productId, String regionName) {
        return smProductService.getProductPlansByOrgLimit(productId, regionName);
    }

    /**
     * 查询产品计划价格详情
     */
    @Cacheable(cacheNames = PRODUCT_PLAN_PRICE, key = "'_app_productId'+#productId+'planId'+#planId")
    public AppPlanFactorPriceVO getProductPlanFactorPrice(int productId, int planId) {
        AppPlanFactorPriceVO planFactor = new AppPlanFactorPriceVO();
        planFactor.setFactorOptionals(smProductService.getPlanPriceFactorOptions(planId));
        planFactor.setPlanFactorPrices(smProductService.getPlanFactorPrice(productId).stream().filter(f -> f.getPlanId() == planId).collect(Collectors.toList()));
        return planFactor;
    }

    /**
     * 查询产品详情基本信息
     *
     * @param companyId
     * @return
     */
    public int getProductCompanyId(int companyId) {
        return smProductService.getProductById(companyId).getCompanyId();
    }

    /**
     * 查询产品投保信息录入列表
     *
     * @param productId
     * @return
     */
    @Cacheable(cacheNames = PRODUCT_FORM_FIELD, key = "'_app_'+#productId")
    public WxProductFormFieldCombVO getProductFormFields(int productId) {
        WxProductFormFieldCombVO comb = new WxProductFormFieldCombVO();
        List<SmProductFormFieldCombDTO> formFieldCombs = smProductService.getProductFormFields(productId);
        comb.setFieldCombs(formFieldCombs);
        formFieldCombs.forEach(ff -> {
            if (ff.getGroupCode() != null
                    && (ff.getGroupCode().contains("vehicleInfo")
                    || ff.getGroupCode().contains("houseInfo"))) {
                if (ff.getList().stream().anyMatch(SmProductFormFieldDTO::getDisplay)) {
                    comb.setSingleInsure(true);
                }
            }
        });
        AppProductDetailVO productDetail = getProductDetailByProductId(productId);
        if (productDetail != null
                && (Objects.equals(productDetail.getChannel(), SmConstants.CHANNEL_CIC) ||
                Objects.equals(productDetail.getChannel(), SmConstants.CHANNEL_DJ) ||
                Objects.equals(productDetail.getChannel(), SmConstants.CHANNEL_GSC)
        )) {
            comb.setSingleInsure(true);
        }
        return comb;
    }

    /**
     * 查询产品职业列表top排行
     *
     * @param productId
     * @param size
     * @return
     */
    public List<WxTreeVO> getOccupationHotList(int productId, int size) {
        return Collections.emptyList();
//        return occupationService.getOccupationHotListInCache(productId, size);
    }

    /**
     * 查询保险页面基本的保险公司配置参数
     *
     * @param planId
     * @return
     */
    public WxFormFieldsVO getWxFormFieldSettings(int planId) {
        return smCmpySettingService.getWxFormFieldSettings(planId);
    }

    /**
     * 重新设置最小金额
     *
     * @param orgName
     * @param vos
     */
    private void resetMinAmt(String orgName, List<AppProductListVO> vos) {
        if (!CollectionUtils.isEmpty(vos) && org.apache.commons.lang3.StringUtils.isNotBlank(orgName)) {
            List<Integer> voIds = vos.stream().map(AppProductListVO::getProductId).collect(Collectors.toList());
            List<ProductMinAmtDTO> wxProductMinAmtDTOS = smProductMapper.listProductMinAmtByOrgName(voIds, orgName)
                    .stream().filter(Objects::nonNull).collect(Collectors.toList());
            Map<Integer, List<ProductMinAmtDTO>> minAmtDTOMap = LambdaUtils.groupBy(wxProductMinAmtDTOS, ProductMinAmtDTO::getProductId);
            vos.forEach(pro -> {
                List<ProductMinAmtDTO> amts = minAmtDTOMap.get(pro.getProductId());
                if (!CollectionUtils.isEmpty(amts)) {
                    pro.setMinAmount(amts.stream().map(ProductMinAmtDTO::getAmount)
                            .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO));
                }
            });
        }
    }
}
