package com.cfpamf.ms.insur.admin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.enums.pco.EnumTalkInviteType;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.RiskCommissionInfoDTO;
import com.cfpamf.ms.insur.base.annotation.ExportField;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveDefault;
import com.cfpamf.ms.insur.base.util.reflect.JsonRemove;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 小额保险提成 vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmPgCommissionVO {

    /**
     * 记账时间
     */
    @ExportField(name = "记账时间", order = 0, type = "dateTime")
    @ApiModelProperty(value = "记账时间")
    private Date accountTime;

    /**
     * 订单创建日期
     */
    @ExportField(name = "订单创建日期", order = 1, type = "dateTime")
    @ApiModelProperty(value = "订单创建日期")
    private Date createTime;

    /**
     * 支付时间
     */
    @ExportField(name = "支付时间", order = 1, type = "dateTime")
    @ApiModelProperty(value = "支付时间")
    private Date paymentTime;

    /**
     * 订单号
     */
    @ExportField(name = "订单号", order = 2)
    @ApiModelProperty(value = "订单号")
    private String fhOrderId;

    /**
     * 所属区域
     */
    @ExportField(name = "所属区域", order = 2)
    @ApiModelProperty(value = "所属区域")
    private String recommendRegionName;

    /**
     * 所属机构
     */
    @ExportField(name = "所属机构", order = 3)
    @ApiModelProperty(value = "所属机构")
    private String recommendOrganizationName;

    /**
     * 推荐人姓名
     */
    @ExportField(name = "推荐人姓名", order = 4)
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendUserName;

    /**
     * 推荐人员工编号
     */
    @ExportField(name = "推荐人手机号", order = 5)
    @ApiModelProperty(value = "推荐人手机号")
    @JsonRemove
    private String recommendUserMobile;

    /**
     * 推荐人手机号
     */
    @ExportField(name = "推荐人员工编号", order = 6)
    @ApiModelProperty(value = "推荐人员工编号")
    private String recommendUserId;

    /**
     * 业务上级
     */
    @ExportField(name = "业务上级", order = 7)
    @ApiModelProperty("业务上级")
    private String userMasterName;

    /**
     * 直线经理
     */
    @ExportField(name = "直线经理", order = 7)
    @ApiModelProperty("直线经理")
    private String userAdminName;

    /**
     * 入职时间
     */
    @ExportField(name = "入职时间", order = 7)
    @ApiModelProperty("入职时间")
    private Date entryDate;

    /**
     * 推荐人所属机构
     */
    @ExportField(name = "所属岗位", order = 7)
    @ApiModelProperty("所属岗位")
    private String postName;

    /**
     *
     */
    @ExportField(name = "投保人姓名", order = 7)
    @ApiModelProperty(value = "投保人姓名")
    private String applicantPersonName;

    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "投保人证件号")
    @JsonRemove
    private String aplicantIdNumber;

    /**
     * 投保人性别
     */
    @ApiModelProperty(value = "投保人性别")
    @JsonRemove
    private String applicantPersonGender;

    /**
     * 投保人手机号
     */
    @ExportField(name = "投保人手机号", order = 8)
    @ApiModelProperty(value = "投保人手机号")
    @JsonRemove
    private String applicantCellPhone;

    /**
     * 投保人邮箱
     */
    @ApiModelProperty(value = "投保人邮箱")
    @JsonRemove
    private String applicantEmail;

    /**
     * 投被保人关系
     */
    @ApiModelProperty(value = "投被保人关系")
    private String insuredRelationship;

    /**
     *
     */
    @ExportField(name = "被保人姓名", order = 9)
    @ApiModelProperty(value = "被保人姓名")
    private String insuredPersonName;

    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "被保人证件号")
    @JsonRemove
    private String insuredIdNumber;

    /**
     * 被保人性别
     */
    @ApiModelProperty(value = "被保人性别")
    private String insuredPersonGender;

    /**
     * 被保人手机号
     */
    @ExportField(name = "被保人手机号", order = 10)
    @ApiModelProperty(value = "被保人手机号")
    @JsonRemove
    private String insuredCellPhone;

    /**
     * 被保人邮箱
     */
    @ApiModelProperty(value = "被保人邮箱")
    private String insuredEmail;

    /**
     * 投保产品名称
     */
    @ExportField(name = "投保产品名称", order = 11)
    @ApiModelProperty(value = "投保产品名称")
    private String productName;


    @ExportField(name = "是否含主险", order = 11.1)
    @ApiModelProperty("是否含有主险-中文")
    private String mainInsurance;

    /**
     * 保单号
     */
    @ExportField(name = "保单号", order = 12)
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "批单号")
    @ExportField(name = "批单号", order = 12)
    private String endorsementNo;

    /**
     * 保单起保日期
     */
    @ExportField(name = "保单起保日期", order = 13)
    @ApiModelProperty(value = "保单起保日期")
    private Date startTime;

    /**
     * 保单失效日期
     */
    @ExportField(name = "保单失效日期", order = 14)
    @ApiModelProperty(value = "保单失效日期")
    private Date endTime;

    /**
     * 保险公司名称
     */
    @ExportField(name = "保险公司名称", order = 15)
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 渠道
     */
    @ExportField(name = "产品渠道", order = 16)
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /**
     * 保单下载地址
     */
    @ExportField(name = "保单下载地址", order = 17)
    @ApiModelProperty(value = "保单下载地址")
    @JsonRemove
    private String downloadURL;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderState;

    /**
     * 投保计划名称
     */
    @ApiModelProperty(value = "投保计划名称")
    private String planName;

    /**
     * 订单金额
     */
    @ExportField(name = "订单金额", order = 18, type = "money")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String payStatus;

    /**
     * 保单状态
     */
    private String appStatus;

    /**
     * 保单状态
     */
    @ExportField(name = "保单状态", order = 19)
    @ApiModelProperty(value = "保单状态")
    private String appStatusName;

    /**
     * 保险期限
     */
    @ApiModelProperty(value = "保险期限")
    private String validPeriod;

    /**
     * 支付佣金比例
     */
    @ExportField(name = "基础佣金比例", order = 20, type = "percentage")
    @ApiModelProperty(value = "支付佣金比例")
    private BigDecimal paymentProportion;

    /**
     * 支付佣金
     */
    @ExportField(name = "基础佣金", order = 21, type = "money")
    @ApiModelProperty(value = "支付佣金")
    private BigDecimal paymenyCommission;

    @ApiModelProperty("支付佣金险种信息")
    private String paymentRiskJson;

    /**
     * 结算佣金比例
     */
    @ExportField(name = "结算佣金比例", order = 22, type = "percentage")
    @ApiModelProperty(value = "结算佣金比例")
    @JsonRemove(classify = NormalRoleCommission.class)
    private BigDecimal settlementProportion;


    /**
     * 结算佣金
     */
    @ExportField(name = "结算佣金", order = 23, type = "money")
    @ApiModelProperty(value = "结算佣金")
    @JsonRemove(classify = NormalRoleCommission.class)
    private BigDecimal settlementCommission;

    @ApiModelProperty("结算佣金险种信息")
    @JsonRemove(classify = NormalRoleCommission.class)
    private String settlementRiskJson;

    /**
     * 结算佣金比例
     */
    @ApiModelProperty(value = "折算保费比例")
    @ExportField(name = "折算保费比例", order = 24)
    private BigDecimal convertedProportion;

    /**
     * 结算佣金
     */
    @ApiModelProperty(value = "折算保费")
    @ExportField(name = "折算保费", order = 25)
    private BigDecimal convertedAmount;

    @ApiModelProperty("折算保费险种信息")
    private String conversionRiskJson;

    @ApiModelProperty("任务保费")
    @ExportField(name = "任务保费", order = 26)
    private BigDecimal taskPremium;

    @ApiModelProperty("是否宣讲会订单")
    private Boolean talkOrder;


    @ApiModelProperty("是否宣讲会订单翻译")
    private String talkOrderName;

    @ExportField(name = "邀约人姓名", order = 28)
    @ApiModelProperty("邀约人姓名")
    private String inviteName;

    @ApiModelProperty("宣讲会邀约人类型")
    private Integer inviteType;

    @ExportField(name = "邀约人类型", order = 29)
    @ApiModelProperty("宣讲会邀约人类型翻译")
    private String inviteTypeName;


    /**
     * 加佣比例
     */
    @ApiModelProperty(value = "加佣比例")
    @ExportField(name = "加佣比例", order = 30)
    private BigDecimal addCommissionProportion;

    /**
     * 加佣金额
     */
    @ApiModelProperty(value = "加佣金额")
    @ExportField(name = "加佣金额", order = 31)
    private BigDecimal addCommissionAmount;



    /**
     * 支付佣金
     */
    @ExportField(name = "支付总佣金", order = 32, type = "money")
    @ApiModelProperty(value = "支付总佣金")
    private BigDecimal totalCommissionAmount;


    @ApiModelProperty("产品类型")
    @ExportField(name = "产品类型", order = 33)
    private String productTypeName;

    @ApiModelProperty(value = "活动编码")
    @ExportField(name = "活动编码", order = 34)
    private String activityCode;

    @ApiModelProperty(value = "是否整村推进")
    @ExportField(name = "是否整村推进", order = 35)
    private String villageActivity;
    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "当月异业相关  1 是，0否")
    @ExportField(name = "当月异业相关", order = 38)
    private String loanMonth;

    @ApiModelProperty(value = "当年异业相关  1 是，0否")
    @ExportField(name = "当年异业相关", order = 39)
    private String loanYear;

    @ApiModelProperty(value = "3天复购  1 是，0否")
    @ExportField(name = "3天复购", order = 40)
    private String buyback3d;

    @ApiModelProperty(value = "是否自保件  1 是，0否")
    @ExportField(name = "是否自保件", order = 40)
    private String isLabel;
    @ApiModelProperty(value = "是否分销单  1 是，0否")
    private Integer orderType;

    @ApiModelProperty(value = "是否分销单  1 是，0否")
    @ExportField(name = "订单类型", order = 41)
    private String orderTypeName;

    @ExportField(name = "主营相关保单", order = 42)
    @ApiModelProperty("是否主营保单-中文")
    private String loanFlag;

    @ExportField(name = "是否在贷", order = 42)
    @ApiModelProperty("是否在贷-中文")
    private String validLoan;

    public String getOrderTypeName() {
        if(Objects.equals(orderType,0)){
            return "普通订单";
        }else {
            return "分销订单";
        }
    }
    public String getVillageActivity() {
        if(villageActivity != null && !villageActivity.isEmpty() && type != null && type == 100){
            return "是";
        }else {
            return "否";
        }
    }
    public String getInviteTypeName() {
        if (Objects.isNull(inviteType)) {
            return null;
        }
        return EnumTalkInviteType.USER.getCode().equals(inviteType) ?
                EnumTalkInviteType.USER.getDesc() : EnumTalkInviteType.WHALE_AGENT.getDesc();
    }

    public String getTalkOrderName() {

        return Boolean.TRUE.equals(talkOrder) ? "是" : "否";
    }

    public BigDecimal getPaymenyCommission() {
        if(paymentProportion == null){
            if(paymenyCommission!=null) {
                return paymenyCommission;
            }
        }else{
            return paymentProportion.multiply(totalAmount);
        }
        return BigDecimal.ZERO;
        //return paymentProportion == null ? new BigDecimal(0) : paymentProportion.multiply(totalAmount);
    }

    public BigDecimal getSettlementCommission() {
        if(settlementProportion == null){
            if(settlementCommission!=null) {
                return settlementCommission;
            }
        }else{
            return settlementProportion.multiply(totalAmount);
        }
        return BigDecimal.ZERO;
        //return settlementProportion == null ? new BigDecimal(0) : settlementProportion.multiply(totalAmount);
    }

    public interface Commission extends FieldRemoveDefault {
    }

    public interface NormalRoleCommission extends Commission {
    }

    public String getPaymentRiskJson() {
        if (StringUtils.isEmpty(paymentRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(paymentRiskJson);
    }

    public String getSettlementRiskJson() {
        if (StringUtils.isEmpty(settlementRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(settlementRiskJson);
    }
    public String getConversionRiskJson(){
        if(StringUtils.isEmpty(conversionRiskJson)){
            return null;
        }
        return riskCommissionJsonToStr(conversionRiskJson);
    }
    public static String riskCommissionJsonToStr(String riskJson){
        List<RiskCommissionInfoDTO> list=  JSONArray.parseArray(riskJson, RiskCommissionInfoDTO.class);
        String str ="";
        for(RiskCommissionInfoDTO dto : list ){
            str += dto.toShowString()+"\r\n";
        }
        return str;
    }
}
