package com.cfpamf.ms.insur.admin.pojo.dto;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.base.util.Base64Util;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 创建用户DTO
 *
 * <AUTHOR>
 */
@Data
public class UserDTO {

    /**
     * Id
     */
    private Integer id;

    /**
     * 区域
     */
    private String regionCode;

    /**
     * 分支
     */
    private String regionName;

    /**
     * 分支
     */
    private String orgCode;

    /**
     * 机构
     */
    private String organizationName;

    /**
     * 机构
     */
    private String organizationFullName;

    /**
     * 北森OrgId
     */
    private Integer hrOrgId;

    /**
     * 组织树结构
     */
    private String orgPath;

    /**
     * 授权用户Id
     */
    private String userId;

    /**
     * 授权用户名
     */
    private String userName;

    /**
     * 员工身份证
     */
    private String userIdCard;

    /**
     * 授权用户名手机号
     */
    private String userMobile;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 1 可以登录后台管理系统  0 不可以
     */
    private String userType;

    /**
     * 1 可以登录后台管理系统  0 不可以
     */
    private String wxOpenId;

    /**
     * 1 可以登录后台管理系统  0 不可以
     */
    private String wxNickName;

    /**
     * 1 可以登录后台管理系统  0 不可以
     */
    private String wxImgUrl;

    /**
     * 同步批次号
     */
    private String batchNo;

    /**
     * 岗位code
     */
    private String postCode;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 人员状态
     */
    private String status;
    /**
     * 权限
     */
    private List<UserAuthorityDTO> permissions;

    /**
     *  任职类型（0:正职、1:兼职）
     */
    private String serviceType;

    private String jobCode;

    private Integer hrUserId;

    private Integer bmsUserId;

    private String mainJobNumber;

    private String bizCode;

    /**
     * get wxNickName
     */
    public String getWxNickName() {
        if (!StringUtils.isEmpty(wxNickName)) {
            return Base64Util.encryptBASE64(wxNickName.getBytes(StandardCharsets.UTF_8));
        }
        return null;
    }
}
