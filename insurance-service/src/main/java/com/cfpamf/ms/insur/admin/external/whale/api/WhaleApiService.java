package com.cfpamf.ms.insur.admin.external.whale.api;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.external.whale.WhaleApiProperties;
import com.cfpamf.ms.insur.admin.external.whale.client.*;
import com.cfpamf.ms.insur.admin.external.whale.model.*;
import com.cfpamf.ms.insur.admin.external.whale.model.policy.WhalePolicyInfoVo;
import com.cfpamf.ms.insur.admin.external.whale.model.request.BaseQuery;
import com.cfpamf.ms.insur.admin.external.whale.model.request.WhalePolicyQuery;
import com.cfpamf.ms.insur.admin.external.whale.utils.PageUtils;
import com.cfpamf.ms.insur.admin.pojo.vo.order.activitie.DistributionExemptionVo;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.request.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.whale.group.response.*;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2022/3/16 10:12
 */
@Service
@Slf4j
public class WhaleApiService {

    @Autowired
    WhaleOrderClient orderClient;

    @Autowired
    WhaleGroupApi groupApi;

    @Autowired
    WhaleApiProperties properties;

    @Autowired
    WhaleDirectSignProxyClient whaleDirectSignProxyClient;

    @Autowired
    WhalePolicyLostClient whalePolicyLostClient;

    @Autowired
    WhalePolicyClient whalePolicyClient;


    /**
     * 客户经理经理是否在小程序做了实名认证
     *
     * @param jobNumber
     * @return
     */
    public Boolean isAuth(String jobNumber) {
        WhaleResp<Boolean> resp = orderClient.authStatus(properties.getAid(), "v1.0", jobNumber);
        if (resp.isSuccess()) {
            return resp.getData();
        }
        log.warn("小鲸客户经理是否实名认证调用失败:{}", resp);
        return Boolean.FALSE;
    }

    public WhaleResp<WhaleContract> getPolicyNo(String contractCode) {
        WhaleResp<WhaleContract> policy = orderClient.getPolicy(properties.getAid(), "v1.0", contractCode,null);
        if (policy.isSuccess()) {
            return policy;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸]" + policy.getMsg());
    }

    /**
     * 根据业务场景获取保单信息
     *
     * @param contractCode     合约编码
     * @param businessScenario 业务场景
     * @return 业务场景策略
     * @throws MSBizNormalException 业务异常
     */
    public WhaleResp<WhaleContract> getPolicyNo(String contractCode, String businessScenario) {
        // 调用orderClient的getPolicy方法获取业务场景策略
        log.info("获取保单信息，入参：{},{}", contractCode, businessScenario);
        WhaleResp<WhaleContract> policy = orderClient.getPolicy(properties.getAid(), "v1.0", contractCode, businessScenario);
        log.info("获取保单信息结果：{}", JSON.toJSONString(policy));
        if (policy.isSuccess()) {
            return policy;
        }
        // 如果获取策略失败，则抛出异常
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸]" + policy.getMsg());
    }

    /**
     * 根据业务场景获取保单信息
     *
     * @return 业务场景策略
     * @throws MSBizNormalException 业务异常
     */
    public List<WhaleProductMappingVo> getProductMapping(List<String> productCodeList) {
        // 调用orderClient的getPolicy方法获取业务场景策略
        log.info("获取小鲸险种信息结果，入参：{},{}", productCodeList);
        WhaleResp<List<WhaleProductMappingVo>> data = orderClient.productMapping(properties.getAid(), "v1.0", productCodeList);
        log.info("获取小鲸险种信息结果：{}", JSON.toJSONString(data));
        if (data.isSuccess()) {
            return data.getData();
        }
        // 如果获取策略失败，则抛出异常
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸]" + data.getMsg());
    }


    public WhaleResp<WhaleContract> getGroupPolicy(String contractCode) {
        WhaleResp<WhaleContract> policy = orderClient.getGroupPolicy(properties.getAid(), "v1.0",
                contractCode, WhaleApiProperties.CHANNEL_CODE,null);
        if (policy.isSuccess()) {
            return policy;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸团险原单]" + policy.getMsg());
    }

    /**
     * 获取保单信息
     * @param contractCode 合同代码
     * @param businessScenario 业务场景
     * @return 保单信息
     */
    public WhaleResp<WhaleContract> getGroupPolicy(String contractCode, String businessScenario) {
        log.info("获取团险保单信息，入参：{},{}", contractCode, businessScenario);
        // 调用订单客户端获取保单信息
        WhaleResp<WhaleContract> policy = orderClient.getGroupPolicy(properties.getAid(),
                "v1.0",
                contractCode, WhaleApiProperties.CHANNEL_CODE,businessScenario);
        log.info("获取团险保单信息：{}", JSON.toJSONString(policy));
        if (policy.isSuccess()) {
            return policy;
        }
        // 如果获取保单信息失败，则抛出异常
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸团险原单]" + policy.getMsg());
    }

    public WhaleResp<PreservationDetail> getPreservationDetail(String preservationCode) {
        WhaleResp<PreservationDetail> policy = orderClient.getPreservationDetail(properties.getAid(), "v1.0", preservationCode, WhaleApiProperties.CHANNEL_CODE);
        if (policy.isSuccess()) {
            return policy;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸团险批改单]" + policy.getMsg());
    }

    /**
     * 四级分销豁免
     *
     * @param req
     * @return
     */
    public Boolean activityFourExemption(DistributionExemptionVo req) {
        final WhaleResp<String> resp = activityFourExemptionRes(req);
        if (Boolean.TRUE.equals(resp.getSuccess())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    public Boolean disposeHandover(HandoverVO handoverVO){
        log.info("开始请求小鲸-查询客户经理是否实名认证:{}", JSON.toJSONString(handoverVO));
        WhaleResp<String> resp = orderClient.disposeHandover(properties.getAid(),"v1.0",handoverVO);
        log.info("查询客户经理是否实名认证完成:{}", JSON.toJSONString(resp));
        if (resp.getSuccess()) {
            return Boolean.TRUE;
        }
        log.warn("小鲸客户经理是否实名认证调用失败");
        return Boolean.FALSE;
    }

    /**
     * 四级分销豁免
     *
     * @param req
     * @return
     */
    public WhaleResp<String> activityFourExemptionRes(DistributionExemptionVo req) {
        log.info("开始请求小鲸-四级分销豁免:{}", JSON.toJSONString(req));
        final WhaleResp<String> resp = orderClient.activityFourExemption(properties.getAid(), "v1.0", req);
        log.info("小鲸-四级分销豁免完成:{}", JSON.toJSONString(resp));
        return resp;
    }

    public WhaleResp<GroupQuoteOutput> groupQuote(GroupUnderwritingInput input) {
        return groupApi.quote(input);
    }

    public WhaleResp<RegisterApplicantOutput> registApplicant(RegisterApplicantInput input) {
        return groupApi.registApplicant(input);
    }

    public WhaleResp<GroupUnderwritingOutput> groupUnderwriting(GroupUnderwritingInput input) {
        return groupApi.underwriting(input);
    }

    public WhaleResp<GroupQuoteOutput> groupAddQuote(GroupUnderwritingInput input) {
        return groupApi.addQuote(input);
    }

    public WhaleResp<GroupUnderwritingOutput> groupAddCommit(GroupUnderwritingInput input) {
        return groupApi.addCommit(input);
    }

    public WhaleResp<GroupDeductionOutput> groupDedutionQuote(GroupDeductionInput input) {
        return groupApi.dedutionQuote(input);
    }

    public WhaleResp<GroupDeductionOutput> groupDedutionCommit(GroupDeductionInput input) {
        return groupApi.dedutionCommit(input);
    }

    public WhaleResp<PageUtils<PolicyLostRegisterList>> list(@RequestBody PolicyLostRegisterQuery query) {
        WhaleResp<PageUtils<PolicyLostRegisterList>>  list = whalePolicyLostClient.list(query);
        if (list.isSuccess()) {
            return list;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸丢单补偿]" + list.getMsg());
    }

    public WhaleResp<PolicyLostRegisterList> detail(@RequestParam("id") @ApiParam(name = "id", value = "丢单登记主键") Integer id) {
        WhaleResp<PolicyLostRegisterList>  list = whalePolicyLostClient.detail(id);
        if (list.isSuccess()) {
            return list;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸丢单补偿-丢单详情]" + list.getMsg());
    }

    public WhaleResp<String> save(@RequestBody @ApiParam(name = "dataInput", value = "提交信息") PolicyLostRegisterDataInput dataInput){
        WhaleResp<String>  policy = whalePolicyLostClient.save(dataInput);
        if (policy.isSuccess()) {
            return policy;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸丢单补偿登记]" + policy.getMsg());
    }

    public WhaleResp<String> updatePolicyNo(@RequestBody @ApiParam(name = "dataInput", value = "提交信息") PolicyLostRegisterDataInput dataInput){
        WhaleResp<String>  policy = whalePolicyLostClient.updatePolicyNo(dataInput);
        if (policy.isSuccess()) {
            return policy;
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), policy.getMsg());
    }

    public WhaleResp<GroupInvoiceOutput> mergeInvoice(GroupInvoiceInput input) {
        return groupApi.mergeInvoice(input);
    }

    public WhaleResp<GroupNotifyPayOutput> notifyPayinfo(GroupNotifyPaymentInput input) {
        return groupApi.notifyPayinfo(input);
    }

    public WhaleResp<GroupMemberChangeOutput> memberChange(GroupMemberChangeInput input) {
        return groupApi.memberChange(input);
    }

    public WhaleApiProperties getProperties() {
        return properties;
    }

    public Map<String, Object> quote(Map<String, Object> objectMap) {
        return whaleDirectSignProxyClient.quote(objectMap);
    }


    public Map<String, Object> registApplicant(String commodityCode) {
        return whaleDirectSignProxyClient.registApplicant(commodityCode);
    }

    public Map<String, Object> underwriting(Map<String, Object> objectMap) {
        Map<String, Object> result = whaleDirectSignProxyClient.underwriting(objectMap);
        return result;
    }

    public Map<String, Object> notifyPayinfo(String policyNo) {
        return whaleDirectSignProxyClient.notifyPayinfo(policyNo);
    }


    public Map<String, Object> referrerCreateSign(Map<String, Object> objectMap) {
        return whaleDirectSignProxyClient.referrerCreateSign(objectMap);
    }

    /**
     * 获取小程序token
     * @param getAppletTokenInput
     * @return
     */
    public String getAppletToken(GetAppletTokenInput getAppletTokenInput) {
        WhaleResp<String> tokenResult = orderClient.getAppletToken(properties.getAid(), "v1.0",getAppletTokenInput);
        if (tokenResult.isSuccess()) {
            return tokenResult.getData();
        }
        throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "[小鲸]" + tokenResult.getMsg());
    }

    public WhalePolicyInfoVo getPolicyInfo(String policyNo) {
        WhalePolicyQuery query = new WhalePolicyQuery();
        initParam(query);
        query.setPolicyNo(policyNo);
        WhaleResp<WhalePolicyInfoVo> data = whalePolicyClient.getPolicyInfo(properties.getAid(), "v1.0",query);
        if(!data.isSuccess()){
            throw new MSBizNormalException("-1",data.getMsg());
        }
        return data.getData();
    }

    public void initParam(BaseQuery query){
        query.setAppKey("12345");
        query.setChannelCode("zhnx");
        query.setRequestTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
    }
}
