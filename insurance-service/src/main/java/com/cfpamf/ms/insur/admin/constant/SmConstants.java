package com.cfpamf.ms.insur.admin.constant;

/**
 * 小额保险自定义常量
 *
 * <AUTHOR>
 */
public interface SmConstants {

    /**
     * 承保年龄
     */
    String UNDER_WRITING_AGE = "underWritingAge";
    /**
     * 保险期限
     */
    String VALID_PERIOD = "validPeriod";
    /**
     * 性别
     */
    String SEX = "sex";
    /**
     * 社保
     */
    String SOCIAL_SECURITY = "socialSecurity";
    /**
     * 职业类别
     */
    String OCCUPL_CATEGORY = "occuplCategory";

    /**
     * 质重医疗
     */
    String PHI_MEDICAINE = "protonHeavyIonMedicine";

    /**
     * 特需医疗
     */
    String SDS_CARE = "specifiedDiseaseSpecificCare";

    /**
     * 赴日医疗
     */
    String JP_MEDICAL = "japanMedicalTreatment";

    String OPTIONS_SMOKE = "smoke";

    /**
     * 车辆座位数
     */
    String VEHICLE_SEAT_NUMBER = "vehicleSeatNumber";
    /**
     * 字典编码-产品类型
     */
    String DICTIONARY_PRODUCT_TYPE = "productType";

    /**
     * 字典编码-产品分类
     */
    String DICTIONARY_PRODUCT_GROUP = "productGroup";
    /**
     * 字典编码-订单
     */
    String DICTIONARY_OODER_FIELD_SETTING = "orderField";
    /**
     * 泛华小额保险调用成功code
     */
    String SM_ORDER_API_INVOKE_SUCCESS = "0";
    /**
     * 订单业务code
     */
    String SM_ORDER_BUSINESS_CODE = "order";
    /**
     * 理赔业务code
     */
    String SM_CLAIM_BUSINESS_CODE = "claim";

    /**
     * 退保业务code
     */
    String SM_CANCEL_BUSINESS_CODE = "cancel ";
    /**
     * 用户种类-微信
     */
    String USER_TYPE_WEIXIN = "weixin";
    /**
     * 用户种类-员工
     */
    String USER_TYPE_EMPLOYEE = "employee";
    /**
     * 用户种类-代理人
     */
    String USER_TYPE_AGENT = "agent";
    /**
     * 公司参数配置字段-证件类型
     */
    String PRODUCT_FORM_FIELD_TYPE_IDTYPE = "idType";
    /**
     * 公司参数配置字段-性别
     */
    String PRODUCT_FORM_FIELD_TYPE_SEX = "sex";
    /**
     * 公司参数配置字段-投保人与被保人关
     */
    String PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP = "apiRelationship";
    /**
     * 公司参数配置字段-财产与被保人关系
     */
    String PRODUCT_FORM_FIELD_TYPE_PTY_RELATIONSHIP = "ptyRelationship";
    /**
     * 公司参数配置字段-单位证件类型
     */
    String PRODUCT_FORM_FIELD_TYPE_COMPANY_IDTYPE = "companyIdType";
    /**
     * 公司参数配置字段-车辆座位数
     */
    String PRODUCT_FORM_FIELD_TYPE_VEHICLE_SEAT_NUMBER = "vehicleSeatNumber";
    /**
     * 公司参数配置字段-省份简称
     */
    String PRODUCT_FORM_FIELD_TYPE_PROVINCE_ABBR = "provinceAbbr";
    /**
     * 公司参数配置字段-房屋类型
     */
    String PRODUCT_FORM_FIELD_TYPE_HOUSE_TYPE = "houseType";
    /**
     * 公司参数配置字段-车辆使用性质
     */
    String PRODUCT_FORM_FIELD_TYPE_CAR_NATURE = "carNature";
    /**
     * 公司参数配置字段-学生类型
     */
    String PRODUCT_FORM_FIELD_TYPE_STUDENT_TYPE = "studentType";
    /**
     * 公司参数配置字段-学校类型
     */
    String PRODUCT_FORM_FIELD_TYPE_SCHOOL_TYPE = "schoolType";
    /**
     * 公司参数配置字段-学校属性
     */
    String PRODUCT_FORM_FIELD_TYPE_SCHOOL_NATURE = "schoolNature";
    /**
     * 公司参数配置字段-户籍类型
     */
    String PRODUCT_FORM_FIELD_HHR_TYPE = "householdRegisterType";

    /***** s54 佣金管理 ******/
    /**
     * 公司参数配置字段-支付方式
     */
    String PRODUCT_FORM_FIELD_PAY_WAY = "payWay";
    /**
     * 公司参数配置字段-缴费年限
     */
    String PRODUCT_FORM_FIELD_COVERED_YEARS = "coveredYears";

    /**
     * 公司参数配置字段-保障期限
     */
    String PRODUCT_FORM_FIELD_VALID_PERIOD = "validPeriod";

    /***** s54 佣金管理 ******/

    /**
     * 未下单-还在智能核保的阶段
     */
    String ORDER_STATE = "10";
    /**
     * 订单状态-订单过期
     */
    String ORDER_STATUS_EXPIRE = "0";


//    /**
//     * 订单状态-已拒保
//     */
//    String ORDER_STATUS_TO_REJECT = "-10";

    /**
     * 订单状态-已智能核保（完成了问卷调查）
     */
    String ORDER_STATUS_CHECKED = "-10";
    /**
     * 订单状态-进行中（待核保）
     */
    String ORDER_STATUS_TO_ORDER = "-1";
    /**
     * 订单状态-已核保（待支付）
     */
    String ORDER_STATUS_TO_PAY = "1";

    /**
     * ★支付状态：支付中
     */
    String PAY_STATUS_DOING = "11";
    /**
     * 订单状态-支付成功
     */
    String ORDER_STATUS_PAYED = "2";
    /**
     * 订单状态-订单取消
     */
    String ORDER_STATUS_CANCEL = "3";

    /**
     * 订单状态-暂存
     */
    String ORDER_STATUS_TEMP_STORE = "10";
    /**
     * 订单状态-订单异常
     */
    String ORDER_STATUS_ERROR = "99";

    /**
     * 未知的状态
     */
    String ORDER_STATUS_UNKNOW = "98";
    /**
     * 保单状态-无
     */
    String POLICY_STATUS_BLANK = "-2";
    /**
     * 保单状态-失效
     */
    String POLICY_STATUS_INVALID = "-1";
    /**
     * 保单状态-承保失败
     */
    String POLICY_STATUS_FAIL = "0";
    /**
     * 保单状态-承保成功
     */
    String POLICY_STATUS_SUCCESS = "1";
    /**
     * 保单状态-处理中
     */
    String POLICY_STATUS_PROCESS = "2";
    /**
     * 保单状态-退保失败
     */
    String POLICY_STATUS_CANCEL_FAIL = "3";
    /**
     * 保单状态-退保成功
     */
    String POLICY_STATUS_CANCEL_SUCCESS = "4";
    /**
     * 保单状态-部分承保失败
     */
    String POLICY_STATUS_PART_FAIL = "5";
    /**
     * 身份证证件类型名称
     */
    String ID_TYPE_NAME_SFZ = "身份证";
    /**
     * excel2003后缀
     */
    String SUFFIX_2003 = ".xls";
    /**
     * excel2007后缀
     */
    String SUFFIX_2007 = ".xlsx";
    /**
     * 时间格式yyyy-MM-dd HH:mm:ss
     */
    String DATE_FORMAT_1 = "yyyy-MM-dd HH:mm:ss";
    /**
     * 全局商品配置使用的产品Id 默认未0
     */
    int DEFAULT_PRODUCT_ID = 0;

    int DEFAULT_FIELD_GROUP_PRODUCT_ID = -1;
    /**
     * 判定刷单数量
     */
    int EVIL_ORDER_NUMBER = 100;
    /**
     * 微信用户产品显示提成配置code
     */
    String WX_USER_SETTING_CODE_CMS = "showCms";
    /**
     * 小额保险报表分组-年
     */
    String REPORT_GROUP_YEAR = "year";
    /**
     * 小额保险报表分组-月
     */
    String REPORT_GROUP_MONTH = "month";
    /**
     * 小额保险报表分组-周
     */
    String REPORT_GROUP_WEEK = "week";
    /**
     * 小额保险报表分组-日
     */
    String REPORT_GROUP_DAY = "day";
    /**
     * 小额保险按区域统计报表文件名
     */
    String SM_TEMPLATE_FILE_PATH = "template";
    /**
     * 小额保险按区域统计报表文件名
     */
    String SM_TEMPLATE_FILE_NAME_REGION_REPORT = "按区域统计报表.xlsx";
    /**
     * 小额保险按分支统计报表文件名
     */
    String SM_TEMPLATE_FILE_NAME_BRANCH_REPORT = "按机构统计报表.xlsx";
    /**
     * 小额保险按人员统计报表文件名
     */
    String SM_TEMPLATE_FILE_NAME_EMPLOYEE_REPORT = "按人员统计报表.xlsx";
    /**
     * 小额保险按产品统计报表文件名
     */
    String SM_TEMPLATE_FILE_NAME_PRODUCT_REPORT = "按产品统计报表.xlsx";
    /**
     * 保险客户区域转化统计模板名
     */
    String SM_TEMPLATE_FILE_NAME_CUST_TRANSFER_REGION_REPORT = "保险客户区域转化统计报表.xlsx";
    /**
     * 保险客户分支转化统计模板名
     */
    String SM_TEMPLATE_FILE_NAME_CUST_TRANSFER_ORG_REPORT = "保险客户分支转化统计报表.xlsx";
    /**
     * 保险信贷转化数据件名
     */
    String SM_TEMPLATE_FILE_NAME__CUST_TRANSFER_LIST_REPORT = "保险信贷转化数据报表.xlsx";
    /**
     * 保险个人业务明细报表
     */
    String SM_TEMPLATE_FILE_NAME_PERSONAL_BUSINESS_DETAILS_REPORT = "保险个人业务明细报表.xlsx";
    /**
     * 保险机构业务明细报表
     */
    String SM_TEMPLATE_FILE_NAME_ORG_BUSINESS_DETAILS_REPORT = "保险机构业务明细报表.xlsx";
    /**
     * 保险区域业务明细报表
     */
    String SM_TEMPLATE_FILE_NAME_REGION_BUSINESS_DETAILS_REPORT = "保险区域业务明细报表.xlsx";
    /**
     * 保险事业部业务明细报表
     */
    String SM_TEMPLATE_FILE_NAME_BRANCHBU_BUSINESS_DETAILS_REPORT = "保险事业部业务明细报表.xlsx";
    /**
     * 个人佣金报表
     */
    String SM_TEMPLATE_FILE_NAME_COMMISSION_REPORT = "个人佣金报表.xlsx";
    /**
     * 机构佣金报表
     */
    String SM_TEMPLATE_FILE_NAME_ORG_COMMISSION_REPORT = "机构佣金报表.xlsx";
    /**
     * redis类别-ORC
     */
    String REDIS_KEY_TYPE_ORC = "orc";
    /**
     * 渠道-泛华
     */
    String CHANNEL_FH = "fh";
    /**
     * 渠道-中户保险
     */
    String CHANNEL_CIC = "cic";
    String CHANNEL_GSC = "gsc";
    String CHANNEL_DJ = "dj";
    String CHANNEL_TK = "tk";
    String CHANNEL_ZA = "za";

    /**
     * 产品类别-团队险
     */
    String PRODUCT_ATTR_GROUP = "group";

    /**
     * 产品类别-个人险
     */
    String PRODUCT_ATTR_PERSON = "person";

    /**
     * 产品类别-雇主责任险编码
     */
    String PRODUCT_ATTR_EMPLOYER = "employer";

    /**
     * 订单top报表缓存-key
     */
    String REDIS_CAHE_KEY_SALES_ORDER_TOP = "saleTop";

    /**
     * 微信代理人岗位code
     */
    String USER_AGENT_POST_CODE = "DL001";

    /**
     * 订单top报表缓存-key
     */
    String USER_AGENT_POST_NAME = "代理人";

    /**
     * 代理人状态-未实名认证
     */
    Integer AGENT_STATUS_NOT_REAL = -1;
    /**
     * 车险权限
     */
    String PERMISSION_AM = "am";
    /**
     * 小额保险权限
     */
    String PERMISSION_SM = "sm";
    /**
     * 管理系统权限
     */
    String PERMISSION_BM = "bm";

    /**
     * 员工状态-离职
     */
    String EMPLOYEE_STATUS_LEASE = "8";

    /**
     * 推广精英排行版用户设置缓存key
     */
    String WX_SALES_TOP_EMPLOYEE_SETTING_CACHE_KEY = "wx_sales_top_employee_setting";

    /**
     * 理赔编号 Redis key
     */
    String REDIS_KEY_CLAIM_NO = "redis_key_claim_no";
    /**
     * 退保 自增序列
     */
    String REDIS_KEY_CANCEL_NO = "insurance:no:cancel_no";
    /**
     * 退保 自增序列
     */
    String REDIS_KEY_ORDER_NO = "insurance:no:order_no:";

    String REDIS_KEY_JOB_NUMBER = "insurance:no:job_number:";

    /**
     * 自增序列
     */
    String REDIS_KEY_FX_ONLY_CODE = "insurance:fx:only_code";

    /**
     * 报价编号 Redis key
     */
    String REDIS_KEY_QUOTE_NO = "redis_key_quote_no";

    /**
     * 理赔编号前缀
     */
    String CLAIM_NO_PREFIX = "LP";
    /**
     * 退保编号前缀
     */
    String CANCEL_NO_PREFIX = "TP";

    /**
     * 报价编号前缀
     */
    String QUOTE_NO_PREFIX = "XJ";


    Integer MODEL_ENABLE = 0;

    Integer MODEL_DISABLE = 1;

    /**
     * 线下导入订单号的前缀
     */
    String ORDER_PREFIX_IMPORT = "IM";

    /**
     * 产品计划出单方式-见费出单
     */
    String PLAN_ORDER_OUT_TYPE_SEE_FEE = "seeFee";

    /**
     * 产品计划出单方式-非见费出单
     */
    String PLAN_ORDER_OUT_TYPE_NON_SEE_FEE = "nonSeefee";
    /**
     * 横线分隔符
     */
    String AK_QUESTIONNAOIRE_HORIZONTAL_SEPARATOR = "-";
    /**
     * 竖线分隔符
     */
    String AK_QUESTIONNAIRE_VERTICAL_SEPARATOR = "|";

    /**
     * 泰康拒保编码
     */
    String TK_REFUSE_CODE = "A001";
    /**
     * 泰康可投保编码
     */
    String TK_SUCCESS_CODE = "A000";

    /**
     * 泰康可投保编码
     */
    String TK_CODE_A044 = "A044";
    /**
     * 对接小鲸向海火球工号字段名
     */
    String XJXH_JOB_NUMBER = "job_number";

    /**
     * 机构主任的职位id
     */
    Integer BRANCH_MANAGER_POST_ID = 82;
    /**
     * 团险批增后缀
     */
    String ENDTOSEMENT_INCRE = "_I";
    /**
     * 团险批减后缀
     */
    String ENDTOSEMENT_DECRE = "_D";

    /**
     * 产品上线状态
     */
    Long PRODUCT_STATUS_ONLINE = 1L;

    /**
     * 雇主责任险编码
     */
    String PRODUCT_ATTR_CODE_EMPLOYER = "employer";

    /** 
     * bms字典配置管理的类型编码
     */
    String BMS_PARAM_VISIT_PRODUCT = "VISIT_PRODUCT";
    /**
     * bms字典数据权限角色配置的类型编码
     */
    String BMS_PARAM_DATA_AUTH_ROLES = "DATA_AUTH_ROLES";

    /**
     * bms字典数据权限角色配置的类型编码
     */
    String BMS_PARAM_DATA_AUTH_HEAD_EMP_ROLES = "DATA_AUTH_HEAD_EMP_ROLE";
 
    /**
     * bms字典数据权限角色开关配置的类型编码
     */
    String BMS_PARAM_DATA_AUTH_SWITCH = "DATA_AUTH_SWITCH";

    /**
     * bms字典数据权限角色开关配置的类型编码
     */
    String BMS_PARAM_DATA_AUTH_SWITCH_NEW = "new";

    /**
     * 总部角色
     */
    String DATA_AUTH_HEAD_ROLE = "HEAD_ROLE";
    /**
     * 事业部负责人角色
     */
    String DATA_AUTH_BRANCH_BU_ROLE = "BRANCH_BU_ROLE";

    /**
     * 区域角色
     */
    String DATA_AUTH_REGION_ROLE = "REGION_ROLE";

    /**
     * 片区角色
     */
    String DATA_AUTH_ZONE_ROLE = "ZONE_ROLE";

    /**
     * 机构负责人角色
     */
    String DATA_AUTH_BRANCH_ROLE = "BRANCH_ROLE";
    /**
     * 创新业务对接人角色
     */
    String DATA_AUTH_INS_BIZ_ROLE = "INS_BIZ_ROLE";
    /**
     * 是否机构主任
     */
    String DATA_AUTH_ORG_HEAD_ROLE = "ORG_HEAD_ROLE";
    /**
     * 客户经理
     */
    String DATA_AUTH_CUSTOMER_MANAGER_ROLE = "CUSTOMER_MANAGER_ROLE";

    /**
     * 渠道保险督导
     */
    String DATA_AUTH_CHANNEL_SUPER_ROLE = "CHANNEL_SUPER_ROLE";





    /**
     * bms字典配置管理的类型编码
     */
    String BMS_PARAM_RENEW_BIND_PRODUCT = "RENEW_BIND_PRODUCT";

    /**
     * bms字典配置管理的类型编码
     */
    String BMS_PARAM_SYSTEM_OPERATION_USER = "SYSTEM_OPERATION_USER";

    /**
     * 产品API对接方式-api对接方式
     */
    Integer PRODUCT_API_TYPE_API = 1;

    /**
     * 产品API对接方式-H5对接方式
     */
    Integer PRODUCT_API_TYPE_H5 = 2;

    /**
     * 险种类型 1-主险
     */
    Integer RISK_TYPE_MASTER = 1;
    /**
     *  2-附加险
     */
    Integer RISK_TYPE_ADDITIONAL = 2;

    /**
     * 已发布
     */
    Integer RELEASE_FLAG_Y = 1;
    /**
     * 未发布
     */
    Integer RELEASE_FLAG_N = 0;

    String PERCENT = "100";

    /**
     *
     */
    String SEPARATOR_1 = "|";

    /**
     * 是的字母标签
     */
    String LABEL_Y= "Y";
    /**
     * 否的字母标签
     */
    String LABEL_N= "N";

    String SYSTEM_USERID = "system";

    String CLAIM_MAIL_SCRIPT = "claim";

}
