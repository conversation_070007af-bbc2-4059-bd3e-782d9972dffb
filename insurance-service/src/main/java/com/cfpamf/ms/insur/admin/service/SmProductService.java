package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.EnumProductAttr;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.product.EnumProductConfig;
import com.cfpamf.ms.insur.admin.constant.product.ProductConfig;
import com.cfpamf.ms.insur.admin.convertedpremium.dao.ConvertedPremiumConfigMapper;
import com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig;
import com.cfpamf.ms.insur.admin.convertedpremium.service.ConvertedPremiumConfigService;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.commission.SystemCommissionConfigMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.*;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.product.EnumInformObject;
import com.cfpamf.ms.insur.admin.enums.product.EnumPremiumFlow;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductLabelType;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.dto.product.*;
import com.cfpamf.ms.insur.admin.pojo.form.product.*;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmPlanSalesOrg;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductNotify;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductVersion;
import com.cfpamf.ms.insur.admin.pojo.po.commission.FastSysCommissionConfigPO;
import com.cfpamf.ms.insur.admin.pojo.po.product.*;
import com.cfpamf.ms.insur.admin.pojo.query.ActivityProductQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmPlanFactorQuery;
import com.cfpamf.ms.insur.admin.pojo.query.SmProductQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductCoveragePremiumDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.product.*;
import com.cfpamf.ms.insur.admin.service.aicheck.AkProductQuestionnaireService;
import com.cfpamf.ms.insur.admin.service.product.ProductQueryService;
import com.cfpamf.ms.insur.admin.service.product.SmLongInsurancePlanService;
import com.cfpamf.ms.insur.admin.service.product.SmProductHealthInformService;
import com.cfpamf.ms.insur.admin.service.product.SmProductRiskService;
import com.cfpamf.ms.insur.admin.service.product.config.AttributeProcessorFactory;
import com.cfpamf.ms.insur.base.advice.DistributedLock;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.constant.RedisCache;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.pojo.query.SmGlProductQuoteQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxProductRenewVO;
import com.cfpamf.ms.mp.facade.api.MpNotifyFacade;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.*;
import static java.util.stream.Collectors.*;

/**
 * 小额保险产品service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SmProductService {

    /**
     * 小额保险产品mapper
     */
    @Autowired
    private SmProductMapper mapper;

    @Autowired
    private SmProductAttrMapper productAttrMapper;

    @Autowired
    private SmProductAttrHistoryMapper productAttrHistoryMapper;

    @Autowired
    SmProductVersionMapper productVersionMapper;

    @Autowired
    private SmProductHistoryMapper historyMapper;

    @Autowired
    private SmPlanSaleOrgMapper planSaleOrgMapper;

    /**
     * 字典service
     */
    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 职业mapper
     */
    @Autowired
    private SmOccupationMapper occupationMapper;

    /**
     * 提成mapper
     */
    @Autowired
    private SmCommissionMapper commissionMapper;

    /**
     * 客户告知书Mapper
     */
    @Autowired
    private SmProductNotifyMapper notifyMapper;

    @Autowired
    private AkProductQuestionnaireService akProductQuestionnaireService;

    /**
     * RedisUtil
     */
    @Autowired
    private RedisUtil<String, String> redisUtil;

    @Autowired
    private ObjectMapper jsonMapper;

    /**
     * 快照服务
     */
    @Autowired
    private SmProductHistoryService historyService;

    @Autowired
    private SmProductConfirmMapper confirmMapper;

    @Autowired
    ConvertedPremiumConfigService convertedPremiumConfigService;

    @Autowired
    SmLongInsurancePlanService smLongInsurancePlanService;

    @Autowired
    SmProductHealthInformService smProductHealthInformService;

    @Autowired
    SmProductRiskService smProductRiskService;

    @Autowired
    private SmProductEndorMapper endorMapper;

    @Autowired
    private SmProductLabelMapper smProductLabelMapper;

    @Autowired
    SmOrderDDDMapper orderDDDMapper;

    @Autowired
    DictionaryMapper dictionaryMapper;
    @Autowired
    private ProductQueryService productQueryService;

    @Autowired
    SmProductRiskService riskService;

    @Autowired
    SmLongInsurancePlanService longInsurancePlanService;

//    @Autowired
//    SmProductHealthInformService healthInformService;

    @Autowired
    SystemCommissionConfigMapper systemCommissionConfigMapper;

    /**
     * 查询管理后端产品列表
     *
     * @param query
     * @return
     */
    public PageInfo<SmProductListVO> getBackendProductByPage(SmProductQuery query) {
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<SmProductListVO> productListVos = getSmProductListVOS(query);
        return new PageInfo<>(productListVos);
    }

    /**
     * 查询管理后端最新产品列表
     *
     * @param query
     * @return
     */
    public PageInfo<SmProductListVO> getNewestProductList(SmProductQuery query) {
        if (query.getPage() > 0) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<SmProductListVO> productListVos = getNewestSmProductListVOS(query);
        return new PageInfo<>(productListVos);
    }

    /**
     * 获取最新的产品列表（编辑但未上线时最新内容）
     *
     * @param query
     * @return
     */
    private List<SmProductListVO> getNewestSmProductListVOS(SmProductQuery query) {
        if (StringUtils.isEmpty(query.getProductAttrCode())) {
            query.setProductAttrCode(null);
        }

        List<SmProductListVO> maxVersion= historyMapper.getMaxVersionProductData();

        List<SmProductListVO> productListVos = mapper.listNewestBackendProducts(query);

        productListVos = productListVos.stream().filter(x->
                maxVersion.stream().anyMatch(y->y.getId().equals(x.getId()) && y.getVersion().equals(x.getVersion()))).collect(toList());

        return productListVos.stream().distinct().collect(toList());
    }

    @Autowired
    private ProductConfigMapper configMapper;

    private List<SmProductListVO> getSmProductListVOS(SmProductQuery query) {
        if (StringUtils.isEmpty(query.getProductAttrCode())) {
            query.setProductAttrCode(null);
        }

        if (StringUtils.isNotEmpty(query.getProductTypeJoin())) {
            query.setProductTypeList(Arrays.asList(query.getProductTypeJoin().split(",")));
        }
        List<SmProductListVO> productListVos = mapper.listBackendProducts(query);

        return initSmProductListVOS(productListVos);
    }

    private List<SmProductListVO> initSmProductListVOS(List<SmProductListVO> productListVos) {
        List<Integer> pids = productListVos.stream()
                .map(SmProductListVO::getId)
                .collect(toList());
        List<SmProductConfig> configs = null;
        if (!pids.isEmpty()) {
            historyMapper.listProductPlansByIds(pids)
                    .forEach(v -> {
                        Optional<SmProductListVO> optional = productListVos.stream()
                                .filter(p -> Objects.equals(p.getId(), v.getProductId()))
                                .findFirst();
                        if (optional.isPresent()) {
                            SmProductListVO vo = optional.get();
                            if (vo.getPlans() == null) {
                                vo.setPlans(new ArrayList<>());
                            }
                            vo.getPlans().add(v);
                        }
                    });
            configs = configMapper.queryList(pids, ProductConfig.GROUP_ONOFF);
        }
        Map<Integer, List<SmProductConfig>> configMap = LambdaUtils.groupBy(configs, SmProductConfig::getProductId);
        // 多个分类处理
        Map<String, String> categoryMap = dictionaryService.getDictionarysByPage(SmConstants.DICTIONARY_PRODUCT_TYPE, null, null, false)
                .getList().stream()
                .collect(toMap(d -> d.getId() + "", DictionaryVO::getName));
        productListVos.stream().forEach(p -> {
            String categoryId = p.getProductCategoryId();
            if (!StringUtils.isEmpty(categoryId)) {
                String[] categoryIds = categoryId.split(",");
                StringBuilder sb = new StringBuilder();
                for (String s : categoryIds) {
                    sb.append(categoryMap.get(s)).append(" / ");
                }
                p.setProductCategoryName(sb.toString().substring(0, sb.length() - 2));
            }

            String onlineChannelCodes = p.getOnlineChannel();
            List<String> onlineChannelNames = new ArrayList<>();
            if (!StringUtils.isEmpty(onlineChannelCodes)) {
                Stream.of(onlineChannelCodes.split(","))
                        .forEach(code -> {
                            if (!StringUtils.isEmpty(code)) {
                                String onlineChannelName = Objects.equals(code, "xiangzhu") ? EnumOrderSubChannel.XIANGZHU.getDesc() :
                                        EnumOrderSubChannel.CAPP.getDesc();
                                onlineChannelNames.add(onlineChannelName);
                            }
                        });
            }
            p.setOnlineChannel(String.join(",", onlineChannelNames));
            /**
             * 设置产品配置信息
             */
            List<SmProductConfig> configList = configMap.get(p.getId());
            if (configList != null) {
                ProductConfigVO vo = new ProductConfigVO();
                configList.forEach(a -> {
                    if (EnumProductConfig.regular_offline.getCode().equals(a.getDictCode())) {
                        vo.setOffline(a.getDictValue());
                    } else if (EnumProductConfig.regular_online.getCode().equals(a.getDictCode())) {
                        vo.setOnline(a.getDictValue());
                    }
                });
                p.setConfig(vo);
            }
        });
        return productListVos;
    }

    public List<SmProductExcelVO> getSmProductExcelVOList(SmProductQuery query) {
        List<SmProductListVO> smProductListVOS = getSmProductListVOS(query);
        smProductListVOS.forEach(x -> {
            String syncWhale = "否";
            String syncAssigned = "否";
            DictionaryVO dictionaryVO = dictionaryMapper.getByTypeAndName(DictionaryVO.TYPE_CHANNEL, x.getChannel());
            if (dictionaryVO != null) {
                x.setChannel(dictionaryVO.getName());
            }
            SmProductLabel smProductLabel = smProductLabelMapper.getByProductIdAndType(x.getId(), EnumProductLabelType.SYNC_WHALE.getCode());
            if (smProductLabel != null
                    && !StringUtils.isBlank(smProductLabel.getLabelValue())
                    && "Y".equals(smProductLabel.getLabelValue())) {
                syncWhale = "是";
            }
            x.setSyncWhale(syncWhale);
            SmProductLabel smProductLabel1 = smProductLabelMapper.getByProductIdAndType(x.getId(), EnumProductLabelType.JOIN_PERFORMANCE.getCode());
            if (smProductLabel1 != null
                    && !StringUtils.isBlank(smProductLabel1.getLabelValue())
                    && "Y".equals(smProductLabel1.getLabelValue())) {
                syncAssigned = "是";
            }
            x.setSyncAssigned(syncAssigned);
        });
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(smProductListVOS)) {
            return Lists.newArrayList();
        }

        return smProductListVOS.stream()
                .map(SmProductExcelVO::new)
                .collect(Collectors.toList());
    }

    /**
     * 查询所有产品计划列表
     *
     * @return
     */
    public List<SmPlanVO> getProductPlanList() {
        return mapper.listProductPlansById(null, true);
    }

    /**
     * 通过计划id列表查询产品计划列表
     *
     * @param planIdList
     * @return
     */
    public List<SmPlanVO> getPlanListByPlanIdList(List<Integer> planIdList) {
        return getPlanListByPlanIdList(planIdList, false);
    }

    /**
     * 通过计划id列表查询产品计划列表
     *
     * @param planIdList
     * @return
     */
    public List<SmPlanVO> getPlanListByPlanIdList(List<Integer> planIdList, boolean getMinPrice) {
        List<SmPlanVO> planListByPlanIdList = mapper.getPlanListByPlanIdList(planIdList);
        if (getMinPrice) {
            List<SmPlanFactorPriceDT> planFactorPrices = mapper.listPlanFactorPrices(null, planIdList);
            planListByPlanIdList.forEach(plan -> {
                BigDecimal minPrice = planFactorPrices.stream()
                        .filter(pp -> Objects.equals(pp.getPlanId(), plan.getId()) && !StringUtils.isEmpty(pp.getPrice()))
                        .map(pp -> new BigDecimal(pp.getPrice()))
                        .filter(p -> p.compareTo(BigDecimal.ZERO) > 0)
                        .min(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);
                plan.setMinPrice(minPrice);
            });
        }
        return planListByPlanIdList;
    }

    /**
     * 查询产品计划
     *
     * @param productId
     * @return
     */
    public List<SmPlanVO> getProductPlans(int productId) {
        return mapper.listProductPlansById(String.valueOf(productId), false);
    }

    /**
     * 查询产品计划
     *
     * @param productId
     * @return
     */
    public List<SmPlanVO> queryPlans(int productId, int version) {
        return historyMapper.listProductPlansById(String.valueOf(productId), true, version);
    }

    /**
     * 根据区域获取有权限的产品计划
     *
     * @param productId
     * @param regionName
     * @return
     */
    public List<SmPlanVO> getProductPlansByOrgLimit(int productId, String regionName) {

        List<SmPlanVO> smPlanVOS = planSaleOrgMapper.listProductPlansById(productId, regionName);
        //如果限制内没有计划那么进行无限制查询
        return CollectionUtils.isEmpty(smPlanVOS) ? getProductPlans(productId) : smPlanVOS;
    }

    /**
     * 查询产品详情基本信息
     * 此接口谨慎使用 productCategoryName ，只映射了一个类型名称
     *
     * @param id
     * @return
     */
    public SmProductDetailVO getProductById(int id) {
        SmProductDetailVO detailVO = mapper.getProductById(id);
        if (Objects.nonNull(detailVO)) {
            detailVO.setId(id);
            SmProductAttr attrQuery = new SmProductAttr();
            attrQuery.setProductId(id);
            attrQuery.setEnabledFlag(0);
            List<SmProductAttr> select = productAttrMapper.select(attrQuery);
            Map<String, String> attrMap = LambdaUtils.safeToMap(select, SmProductAttr::getAttrCode, SmProductAttr::getAttrVal);
            detailVO.setAttrs(attrMap);
        }
        return detailVO;
    }


    /**
     * 修改产品推荐标记（1推荐 0不推荐）
     *
     * @param id
     * @param activeFlag
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_FORM_FIELD, PRODUCT_LIST, PRODUCT_FORM_OPTIONAL}, allEntries = true)
    public void updateProductActiveFlag(int id, int activeFlag) {
        // 清空热门产品缓存
        Set<String> hotPageKeys = redisUtil.keys(PRODUCT_LIST + "*");
        for (String key : hotPageKeys) {
            redisUtil.remove(key);
        }

        mapper.updateProductActiveFlag(id, activeFlag);
    }

    /**
     * 保存所有产品上线平台
     *
     * @param productId
     * @param onlineChannel
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_FORM_FIELD, PRODUCT_LIST}, allEntries = true)
    public void saveProductOnlinePlatform(int productId, String onlineChannel) {
        mapper.updateProductOnlinePlatform(productId, onlineChannel);
        mpNotifyFacade.productChange(String.valueOf(productId));
    }

    /**
     * 查询所有产品上线平台
     *
     * @param productId
     */
    public List<String> getProductOnlinePlatform(int productId) {
        SmProductDetailVO productDetail = mapper.getProductById(productId);
        String onlineChannel = productDetail.getOnlineChannel();
        if (!StringUtils.isEmpty(onlineChannel)) {
            return Arrays.asList(onlineChannel.split(","));
        }
        return Collections.emptyList();
    }

    /**
     * 修改保险产品状态
     * 个险-长险 ：API对接上线：
     * （1）产品配置-基础信息、险种信息、产品计划、产品价格、投保须知、保险条款、投保信息录入，均需要完成配置；
     * （2）佣金方案配置（3）折算保费配置
     * 个险-长险 ：h5上线要求：
     * （1）产品配置-基础信息、产品计划；
     * （2）佣金方案配置（3）折算保费配置
     * 修订时间:2022-04-22
     * 基于版本验证数据完整性
     *
     * @param id
     * @param status
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_LIST, PRODUCT_FORM_OPTIONAL,
            PRODUCT_PLAN_PRICE, PRODUCT_FORM_FIELD, WX_LBT_IMAGE}, allEntries = true)
    public void updateProductStatus(int id, int status, String operator) {

        // 清空热门产品缓存
        Set<String> hotPageKeys = redisUtil.keys(PRODUCT_LIST + "*");
        for (String key : hotPageKeys) {
            redisUtil.remove(key);
        }

        //判断产品是否配置了折算保费配置
        if (status == 1) {
            List<SmPlanVO> smPlanList = historyMapper.listProductPlansById(String.valueOf(id), true, null);
            List<Long> configConvertedPremiumPlanIdList = convertedPremiumConfigService.getConvertedPremiumConfigListByProductIdAndNow(Long.valueOf(id))
                    .stream()
                    .map(ConvertedPremiumConfig::getPlanId)
                    .collect(toList());
            smPlanList.forEach(smPlanVO -> {
                if (!configConvertedPremiumPlanIdList.contains(Long.valueOf(smPlanVO.getId()))) {
                    throw new BizException("product.converted.premium.config.not.exist", "没有配置折算保费，请配置好后再进行上线操作");
                }
            });
        }

        // 个险产品上线校验价格因素配置
        if (status == 1) {
            SmProductService productService = (SmProductService)AopContext.currentProxy();
            productService.pushNewVersion(id, operator);
        }
        SmProductDetailVO productDetail = getProductById(id);
        if (Objects.equals(productDetail.getProductAttrCode(), SmConstants.PRODUCT_ATTR_PERSON) && status == 1) {
            //校验产品计划
            List<SmPlanVO> plans = getProductPlans(id);
            if (plans == null || plans.isEmpty()) {
                throw new BizException(ExcptEnum.PRODUCT_PLAN_ERROR_201003);
            }
            String createType = productDetail.getCreateType();
            Integer apiType = productDetail.getApiType();
            //如果是个险-长期险
            if (Objects.equals(createType, EnumProductCreateType.PERSON_LONG_INSURANCE.name())) {
                checkLongInsuranceConfig(id, productDetail, plans, apiType);
                mapper.updateProductStatus(id, status, HttpRequestUtil.getUserId());
                return;
            }
            //校验保险条款
            checkProductClause(id);
            List<SmProductCoverageAmountVO> coverageAmounts = mapper.listProductCoverageAmounts(id);
            List<SmProductCoverageVO> coverages = mapper.listProductCoverages(id);
            // 产品介绍图片和保障项目金额配置不能都为空
            if (StringUtils.isEmpty(productDetail.getIntroduceImageUrl())
                    && (coverageAmounts.isEmpty() || coverages.isEmpty())) {
                throw new BizException(ExcptEnum.PRODUCT_ERROR_IMAGE);
            }
            getProductCoverageAmountList(id).forEach(cv -> {
                if (cv.getCoverageAmounts().isEmpty()) {
                    throw new BizException(ExcptEnum.PRODUCT_CVG_ERROR);
                }
                cv.getCoverageAmounts().forEach(ca -> {
                    if (ca.getCvgAmount() == null) {
                        throw new BizException(ExcptEnum.PRODUCT_CVG_ERROR);
                    }
                });
            });

            if (mapper.countPlanSalesOrg(id) <= 0) {
                throw new BizException(ExcptEnum.PRODUCT_PLAN_ORG_ERROR_201006);
            }

            List<SmPlanFactorPriceDT> pfpvs = getPlanFactorPrice(id);
            boolean isPriceZero = pfpvs == null || pfpvs.isEmpty()
                    || (pfpvs.stream().anyMatch(p -> (p.getAvailable() != null && p.getAvailable() && (StringUtils.isEmpty(p.getPrice())
                    || new BigDecimal(p.getPrice()).compareTo(new BigDecimal(0)) == 0))));
            //是否 赠险
            Map<String, String> attrs = productDetail.getAttrs();
            boolean isFree = "1".equals(Objects.nonNull(attrs) ? attrs.getOrDefault(EnumProductAttr.IS_FREE.getCode(), "0") : "0");
            if (isPriceZero && !isFree) {
                throw new BizException(ExcptEnum.PRODUCT_PLAN_PRICE_ERROR_201004);
            }


            if (!isFree) {
                checkCommissionConfig(plans);
            }
        }
        mapper.updateProductStatus(id, status, HttpRequestUtil.getUserId());
    }

    /**
     * 修改保险产品状态
     * 个险-长险 ：API对接上线：
     * （1）产品配置-基础信息、险种信息、产品计划、产品价格、投保须知、保险条款、投保信息录入，均需要完成配置；
     * （2）佣金方案配置（3）折算保费配置
     * 个险-长险 ：h5上线要求：
     * （1）产品配置-基础信息、产品计划；
     * （2）佣金方案配置（3）折算保费配置
     * 修订时间:2022-04-22
     * 基于版本验证数据完整性
     *
     * @param id
     * @param status
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_LIST, PRODUCT_FORM_OPTIONAL,
            PRODUCT_PLAN_PRICE, PRODUCT_FORM_FIELD, WX_LBT_IMAGE}, allEntries = true)
    public void onoff(int id, int status, String applyTime, String operator) {
        // 清空热门产品缓存
        Set<String> hotPageKeys = redisUtil.keys(PRODUCT_LIST + "*");
        for (String key : hotPageKeys) {
            redisUtil.remove(key);
        }

        //判断产品是否配置了折算保费配置
        if (status == 1) {
            validProduct(id);
        }
        SmProductService productService = (SmProductService) AopContext.currentProxy();
        if (StringUtils.isNotBlank(applyTime)) {
            DateUtil.parseDate(applyTime);
            productService.initJob(id, status, applyTime);
            return;
        }
        if (status == 1) {
            productService.pushNewVersion(id, operator);
        }
        mapper.updateProductStatus(id, status, operator);
    }

    public void offline(Integer productId, String operator) {
        mapper.updateProductStatus(productId, 2, operator);
    }

    public void cancelOnoff(int id, int status) {
        EnumProductConfig config = ProductConfig.mapperProductStatus(status);
        configMapper.initJob(id, ProductConfig.GROUP_ONOFF, config.getCode());
    }

    /**
     * 初始化定时任务
     * 将同一个产品的之前的定时任务给停掉
     *
     * @param id
     * @param status
     * @param applyTime
     */
    @Transactional
    public void initJob(int id, int status, String applyTime) {
        String operator = HttpRequestUtil.getUserId();
        SmProductConfig jobConfig = new SmProductConfig();
        jobConfig.setProductId(id);
        jobConfig.setColony(ProductConfig.GROUP_ONOFF);
        EnumProductConfig config = ProductConfig.mapperProductStatus(status);
        jobConfig.setDictCode(config.getCode());
        jobConfig.setDictValue(applyTime);
        jobConfig.setRemark(config.getRemark());
        jobConfig.setEnabledFlag(0);
        jobConfig.setOperator(operator);
        configMapper.initJob(id, ProductConfig.GROUP_ONOFF, config.getCode());
        configMapper.insertSelective(jobConfig);

    }

    /**
     * 验证产品配置是否完整
     *
     * @return
     */
    public boolean validProduct(int id) {
        int version = getProductUpdateVersion(id);
        /**
         * 查询当前版本的产品详情信息
         */
        SmProductDetailVO product = historyMapper.getProductById(id, version);
        if (product == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("产品配置找不到版本:%s", version));
        }
        SmProductAttr attrQuery = new SmProductAttr();
        attrQuery.setProductId(id);
        attrQuery.setEnabledFlag(0);
        List<SmProductAttr> select = productAttrMapper.select(attrQuery);
        Map<String, String> attrMap = LambdaUtils.safeToMap(select, SmProductAttr::getAttrCode, SmProductAttr::getAttrVal);
        product.setAttrs(attrMap);
        /**
         * 校验折算保费是否配置
         */
        //校验产品计划
        List<SmPlanVO> plans = queryPlans(id, version);
        if (plans == null || plans.isEmpty()) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_PLAN_ERROR_201003);
        }
        validConvertedPremium(plans);
        checkConfig(id, version, product, plans);
        return true;
    }

    /**
     * 检查产品配置是否完整
     *
     * @param id
     * @param version
     * @param product
     */
    private void checkConfig(int id, int version, SmProductDetailVO product, List<SmPlanVO> planList) {
        String createType = product.getCreateType();
        //如果是个险-长期险
        if (Objects.equals(createType, EnumProductCreateType.PERSON_LONG_INSURANCE.name())) {
            checkLongInsConfig(id, version, planList, product);
            return;
        }
        validShortTermConfig(id, version, planList, product);
    }

    private void validShortTermConfig(int id, int version, List<SmPlanVO> plans, SmProductDetailVO product) {
        //校验保险条款
        checkProductClauseByVersion(id, version);
        List<SmProductCoverageAmountVO> coverageAmounts = historyMapper.listProductCoverageAmounts(id, version);
        List<SmProductCoverageVO> coverages = historyMapper.listProductCoverages(id, version);
        // 产品介绍图片和保障项目金额配置不能都为空
        if (StringUtils.isEmpty(product.getIntroduceImageUrl())
                && (coverageAmounts.isEmpty() || coverages.isEmpty())) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_IMAGE);
        }
        historyService.getProductCoverageAmountList(id, version).forEach(cv -> {
            if (cv.getCoverageAmounts().isEmpty()) {
                throw new MSBizNormalException(ExcptEnum.PRODUCT_CVG_ERROR);
            }
            cv.getCoverageAmounts().forEach(ca -> {
                if (ca.getCvgAmount() == null) {
                    throw new MSBizNormalException(ExcptEnum.PRODUCT_CVG_ERROR);
                }
            });
        });

        if (mapper.countPlanSalesOrg(id) <= 0) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_PLAN_ORG_ERROR_201006);
        }

        //是否 赠险
        Map<String, String> attrs = product.getAttrs();
        boolean isFree = "1".equals(Objects.nonNull(attrs) ? attrs.getOrDefault(EnumProductAttr.IS_FREE.getCode(), "0") : "0");

        String productAttr = product.getProductAttrCode();
        if (EnumProductAttr.GROUP.getCode().equalsIgnoreCase(productAttr)) {
            validPlanPremium4Group(plans, product);
        } else {
            validPlanPremium4ShortTerm(id, version, product);
        }
        if (!isFree) {
            validSysCommission(plans);
        }
    }

    /**
     * 2022-07-19
     * 发现很多情况不需要去校验保费跟赠险的关系
     * 或者以下代码(历史代码)是否已过期，譬如保险计划的保费不是在[sm_plan_factor_price]表中校验???
     * 所以此处先屏蔽掉以下逻辑!
     *
     * @param productId
     * @param version
     * @param product
     */
    private void validPlanPremium4ShortTerm(int productId, int version, SmProductDetailVO product) {
//        List<SmPlanFactorPriceDT> pfpvs = historyService.getPlanFactorPrice(productId, version);
//        boolean isPriceZero = pfpvs == null
//                || pfpvs.isEmpty()
//                || (pfpvs.stream().anyMatch(p -> (p.getAvailable() != null && p.getAvailable() && (StringUtils.isEmpty(p.getPrice())
//                || new BigDecimal(p.getPrice()).compareTo(new BigDecimal(0)) == 0))));
//
//        //是否 赠险
//        Map<String, String> attrs = product.getAttrs();
//        boolean isFree = "1".equals(Objects.nonNull(attrs) ? attrs.getOrDefault(EnumProductAttr.IS_FREE.getCode(), "0") : "0");
//        if (isPriceZero && !isFree) {
//            throw new BizException(ExcptEnum.PRODUCT_PLAN_PRICE_ERROR_201004);
//        }
    }

    private void validPlanPremium4Group(List<SmPlanVO> plans, SmProductDetailVO product) {

        boolean freePlan = plans.stream()
                .anyMatch(p -> p.getMinPrice() != null && p.getMinPrice().compareTo(new BigDecimal(0)) == 0);
        //是否 赠险
        Map<String, String> attrs = product.getAttrs();
        boolean isFree = "1".equals(Objects.nonNull(attrs) ? attrs.getOrDefault(EnumProductAttr.IS_FREE.getCode(), "0") : "0");
        if (freePlan && !isFree) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_PLAN_PRICE_ERROR_201004);
        }
    }

    @Autowired
    ConvertedPremiumConfigMapper convertedPremiumConfigMapper;

    @Autowired
    private SystemCommissionConfigDetailMapper sysCommissionConfigDetailMapper;

    /**
     * 折算保费配置检查
     *
     * @param plans
     */
    public void validConvertedPremium(List<SmPlanVO> plans) {
        /**
         * 折算保费校验
         */
        List<Integer> planIdList = plans.stream().map(SmPlanVO::getPlanId).collect(Collectors.toList());
        List<FastSysCommissionConfigPO> commissionConfigs = sysCommissionConfigDetailMapper.queryEffectiveConfigByPlans(1, planIdList);
        Map<Integer, List<FastSysCommissionConfigPO>> commissionMap = new HashMap<>();
        commissionConfigs.stream()
                .filter(a -> a.getType() == 3)
                .forEach(a -> {
                    safePut(a.getPlanId(), a, commissionMap);
                });
        plans.forEach(smPlanVO -> {
            if (!commissionMap.containsKey(smPlanVO.getId())) {
                throw new MSBizNormalException("product.converted.premium.config.not.exist", "折算保费未配置，请检查并配置好再操作上线");
            }
        });
    }

    private <T, V> void safePut(T key, V value, Map<T, List<V>> map) {
        List<V> list = map.get(key);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(value);
        map.put(key, list);
    }

    /**
     * 佣金配置检查
     *
     * @param smPlanList
     */
    public void validSysCommission(List<SmPlanVO> smPlanList) {
        List<Integer> planIdList = smPlanList.stream().map(SmPlanVO::getPlanId).collect(Collectors.toList());
        List<FastSysCommissionConfigPO> commissionConfigs = sysCommissionConfigDetailMapper.queryEffectiveConfigByPlans(1, planIdList);
        Map<Integer, List<FastSysCommissionConfigPO>> upCommissionMap = new HashMap<>();
        Map<Integer, List<FastSysCommissionConfigPO>> downCommissionMap = new HashMap<>();
        commissionConfigs.stream()
                .forEach(a -> {
                    Integer planId = a.getPlanId();
                    if (a.getType() == 1) {
                        safePut(planId, a, upCommissionMap);
                    } else if (a.getType() == 2) {
                        safePut(planId, a, downCommissionMap);
                    }
                });
        smPlanList.forEach(smPlanVO -> {
            if (!upCommissionMap.containsKey(smPlanVO.getId())) {
                throw new MSBizNormalException(ExcptEnum.PRODUCT_PLAN_ERROR_201003.getCode(), "上游结算佣金未配置");
            }
            if (!downCommissionMap.containsKey(smPlanVO.getId())) {
                throw new MSBizNormalException(ExcptEnum.PRODUCT_PLAN_ERROR_201003.getCode(), "下游结算佣金未配置");
            }
        });
    }

    /**
     * 校验长险配置
     *
     * @param id
     * @param productDetail
     * @param plans
     * @param apiType
     */
    public void checkLongInsuranceConfig(int id, SmProductDetailVO productDetail, List<SmPlanVO> plans, Integer apiType) {
        //校验提成
        checkCommissionConfig(plans);
        //H5对接
        if (Objects.equals(apiType, SmConstants.PRODUCT_API_TYPE_H5)) {
            return;
        }
        //险种配置
        List<SmProductRiskVO> productRisk = smProductRiskService.getProductRisk(id);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(productRisk)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，需要配置产品险种");
        }
        //产品价格和产品计划 warning:个险-长期险产品价格由产品计划带出
        List<SmLongInsurancePlanVo> planList = smLongInsurancePlanService.getPlanList(id);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(planList)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，需要配置产品价格");
        }
        //投保须知
        if (org.apache.commons.lang3.StringUtils.isBlank(productDetail.getGlProductNotice())) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，需要配置投保须知内容");
        }
        //校验保险条款
        checkProductClause(id);
        //投保信息录入 因为又默认的投保信息录入可以不检验
        return;
    }

    /**
     * 校验长险配置
     *
     * @param id
     * @param product
     * @param planList
     */
    private void checkLongInsConfig(int id,
                                    int version,
                                    List<SmPlanVO> planList,
                                    SmProductDetailVO product) {
        //校验提成
        validSysCommission(planList);
        //H5对接
        Integer apiType = product.getApiType();
        if (Objects.equals(apiType, SmConstants.PRODUCT_API_TYPE_H5)) {
            return;
        }
        //险种配置
        List<SmProductRiskVO> productRisk = smProductRiskService.getProductRisk(id);
        if (CollectionUtils.isEmpty(productRisk)) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，需要配置产品险种");
        }
        //产品价格和产品计划 warning:个险-长期险产品价格由产品计划带出
        if (CollectionUtils.isEmpty(planList)) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，需要配置产品价格");
        }
        //投保须知
        if (StringUtils.isBlank(product.getGlProductNotice())) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，需要配置投保须知内容");
        }
        //校验保险条款
        checkProductClauseByVersion(id, version);
        //投保信息录入 因为又默认的投保信息录入可以不检验
    }

    /**
     * 校验保险条款
     *
     * @param productId
     */
    private void checkProductClause(int productId) {
        List<SmProductClauseVO> clauses = getProductClause(productId);
        if (clauses == null || clauses.isEmpty()) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201007);
        }
    }

    private void checkProductClauseByVersion(int productId, int version) {
        List<SmProductClauseVO> clauses = historyMapper.listProductClausesByProductId(productId, version);
        if (clauses == null || clauses.isEmpty()) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_ERROR_201007);
        }
    }

    /**
     * 校验提成配置
     *
     * @param plans
     */
    private void checkCommissionConfig(List<SmPlanVO> plans) {
        Date today = new Date();
        plans.forEach(p -> {
            SmCommissionSettingVO cms = commissionMapper.getCommissionSettingByPlanId(p.getId(), today);
            if (null == cms) {
                throw new BizException(ExcptEnum.COMMISSION_ERROR_201025);
            }
        });
    }

    /**
     * 更新排序
     * 调整排序：
     * ①由N调整为M（M＞N）,排序为 N+1,N+2...M-1,M号 的产品，排序均减一
     * 比如某个产品由3调整为5，原来的4号产品变为3，原来的5号产品变为4；
     * ②由M调整为N（M＞N）,排序为 N,N+1...M-1 的产品，排序均+1
     * 比如某个产品由5调整为3，原来的3号产品变为4，原来的4号产品变为5；
     *
     * @param id
     * @param newSortNum
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_FORM_FIELD, PRODUCT_LIST, PRODUCT_FORM_OPTIONAL}, allEntries = true)
    public int updateSortNum(int id, int newSortNum) {
        return mapper.updateSortNum(id, newSortNum);
    }

    /**
     * 保存产品基本信息
     *
     * @param vo
     * @return
     */
    @DistributedLock(prefix = "safes:product", params = "channel")
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_FORM_FIELD, PRODUCT_LIST, PRODUCT_FORM_OPTIONAL}, allEntries = true)
    public int saveProductBaseInfo(BasicProductVo vo) {
        SmProductDTO dto = vo.copyFiled2DTO();
        dto.setModifyBy(HttpRequestUtil.getUserId());
        Integer version = 0;
        //如果是个险-长险类型产品编码不能为空
        if (EnumProductCreateType.PERSON_LONG_INSURANCE.equals(vo.getCreateType())) {
            if (StringUtils.isBlank(vo.getProductCode())) {
                throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品编码不能为空");
            }
        }

        if (EnumProductCreateType.PERSON_LONG_INSURANCE.equals(vo.getCreateType())
                || EnumProductCreateType.PERSON_SHORT_INSURANCE.equals(vo.getCreateType())) {
            if (vo.getLongInsurance() == null) {
                throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "是否为长险不能为空");
            }
        }

        if (StringUtils.isBlank(vo.getProductShortName())){
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品简称不能为空");
        }

        // 产品简称长度不能超过8
        if (vo.getProductShortName().length() > 8 || !vo.validateString(vo.getProductShortName())) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品简称不能超过8个字或者包含特殊字符");
        }

        /**
         * 默认为短险(团险&雇主责任险)
         */
        if (dto.getLongInsurance() == null) {
            dto.setLongInsurance(1);
        }
        if (dto.getId() != null && dto.getId() > 0) {
            dto.setProductId(dto.getId());
            version = productVersionMapper.getMaxVersion(dto.getId()) + 1;
            dto.setVersion(version);

            historyMapper.updateProduct(dto);
        } else {
            Integer sortNum = mapper.getMaxSortNum();
            dto.setSortNum(sortNum + 1);
            dto.setVersion(version);
            mapper.insertProduct(dto);
            dto.setProductId(dto.getId());
            historyMapper.insertProduct(dto);
            //设置默认的产品标签，是否同步至小鲸向海
            if(Objects.isNull(vo.getProductLabelDTO())){
                saveDefaultProductLabel(dto.getProductId());
            }
        }
        // 团险产品职业类别限制
        if (!StringUtils.isEmpty(dto.getGlOcpnGroup()) && Objects.equals(dto.getProductAttrCode(), SmConstants.PRODUCT_ATTR_GROUP)) {
            List<SmOccupationLimitDTO> occupationLimits = new ArrayList<>();
            Stream.of(dto.getGlOcpnGroup().split(",")).forEach(o -> {
                SmOccupationLimitDTO limit = new SmOccupationLimitDTO();
                limit.setProductId(dto.getId());
                limit.setFieldCode(SmConstants.OCCUPL_CATEGORY);
                limit.setLimitCode(o);
                limit.setModifyBy(HttpRequestUtil.getUserId());
                occupationLimits.add(limit);
            });
            occupationMapper.deleteProductOccupationLimitsHistory(dto.getId(), SmConstants.OCCUPL_CATEGORY, version);
            occupationMapper.insertProductOccupationLimitsHistory(occupationLimits, version);
        }

        saveProductAttrs(dto.getAttrs(), dto.getProductId(), version);

        //更新上下游比例、折算保费配置
        systemCommissionConfigMapper.updateCompanyIdByProductId(vo.getId(), vo.getCompanyId());
        ProductLabelDTO productLabelDTO = vo.getProductLabelDTO();
        if(Objects.nonNull(productLabelDTO)){
            productLabelDTO.setProductId(dto.getProductId());
            saveProductLabel(productLabelDTO);
        }

        return dto.getProductId();
    }

    private void saveDefaultProductLabel(Integer productId) {
        ProductLabelDTO productLabelDTO = new ProductLabelDTO();
        productLabelDTO.setProductId(productId);
        productLabelDTO.setLabelList(
                Lists.newArrayList(
                        EnumProductLabelType.SYNC_WHALE.constructDefaultLabelType()
                        , EnumProductLabelType.JOIN_PERFORMANCE.constructDefaultLabelType()
                        , EnumProductLabelType.OFFLINE_CLAIM.constructDefaultLabelType()
                )
        );
        saveProductLabel(productLabelDTO);
    }

    @Autowired
    private ProductOccupationMapper productOccupationMapper;


    @Autowired
    private TxServiceManager txService;


    /**
     * 产品扩展属性维护
     *
     * @param attrMap
     * @param productId
     * @param version
     */


    public void saveProductAttrs(Map<String, String> attrMap, Integer productId, Integer version) {
        //数组为空 不会删除以前的数据???
        if (CollectionUtils.isEmpty(attrMap)) {
            productAttrMapper.deleteHistory(productId, version);
            return;
        }
        //如果第一次保存版本  版本号从1开始
        if (version == 0) {
            version = 1;
        }
        List<SmProductAttr> attrModels = attrMap.entrySet().stream().map(en -> {
            SmProductAttr productAttr = new SmProductAttr();
            productAttr.setProductId(productId);
            productAttr.setAttrCode(en.getKey());
            productAttr.setAttrVal(en.getValue());
            return productAttr;
        }).collect(toList());

        log.info("开始保存产品扩展属性:{}", attrMap);
        productAttrMapper.deleteHistory(productId, version);
        productAttrMapper.insertHisList(attrModels, version);
    }

    /**
     * 获取产品修改版本
     *
     * @param productId
     * @return
     */
    public int getProductUpdateVersion(int productId) {
        return productVersionMapper.getMaxVersion(productId) + 1;
    }

    /**
     * 保存产品计划
     *
     * @param id
     * @param plans
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_PLAN}, allEntries = true)
    public void saveProductPlan(int id, List<SmPlanDTO> plans) {

        if (CommonUtil.isCollectionEmpty(plans)) {
            throw new BizException(ExcptEnum.PRODUCT_PLAN_ERROR_201003);
        }
        //校验计划编码是否重复
        longInsurancePlanService.checkFhProductId(plans
                .stream()
                .filter(Objects::nonNull)
                .map(SmPlanDTO::getFhProductId)
                .collect(Collectors.toList()), Collections.singletonList(id));
        int productUpdateVersion = getProductUpdateVersion(id);

        List<SmPlanVO> extPlans = historyMapper.listProductPlansById("" + id, true, productUpdateVersion);
        for (int i = 0, len = plans.size(); i < len; i++) {
            SmPlanDTO dto = plans.get(i);
            String fhProductId = dto.getFhProductId();
            String planName = dto.getPlanName();
            if (extPlans.stream().anyMatch(ep -> Objects.equals(ep.getFhProductId(), fhProductId) && !Objects.equals(dto.getId(), ep.getId()))) {
                throw new BizException(ExcptEnum.PRODUCT_ID_UNIQUE_201015);
            }
            if (extPlans.stream().anyMatch(ep -> Objects.equals(ep.getPlanName(), planName) && !Objects.equals(dto.getId(), ep.getId()))) {
                throw new BizException(ExcptEnum.PRODUCT_ID_UNIQUE_201005);
            }
            dto.setModifyBy(HttpRequestUtil.getUserId());
            if (StringUtils.isEmpty(dto.getFhProductId()) || StringUtils.isEmpty(dto.getPlanName())) {
                throw new BizException(ExcptEnum.PLAN_FIELD_NOT_EMPYT_701001);
            }
            if (dto.getId() == null) {

                mapper.insertProductPlansDisable(Collections.singletonList(dto));
                historyMapper.insertProductPlans(Collections.singletonList(dto), productUpdateVersion);

            } else {
                int row = historyMapper.updateProductPlan(dto, productUpdateVersion);
//                mapper.updateProductPlan(dto);
            }
        }

        List<SmPlanFactorPriceDT> priceVos = historyService.getPlanFactorPrice(id, productUpdateVersion);
        List<SmOptionalFieldPriceDTO> newPriceDtos = priceVos.stream().map(p -> {
            SmOptionalFieldPriceDTO pfpd = new SmOptionalFieldPriceDTO();
            BeanUtils.copyProperties(p, pfpd);
            pfpd.setAvailable(Boolean.TRUE);
            pfpd.setPrice("0.00");
            return pfpd;
        }).collect(toList());
        savePlanFactorPrice(newPriceDtos);
    }

    /**
     * 保存产品计划
     *
     * @param planId
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_PLAN}, allEntries = true)
    public void deleteProductPlan(int planId) {
//        mapper.deleteProductPlan(planId, HttpRequestUtil.getUserId());
//        historyMapper.deleteProductPlan(planId, HttpRequestUtil.getUserId());
        throw new RuntimeException("暂不支持删除计划");
    }

    /**
     * 保存产品常见问题
     *
     * @param dto
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_LIST}, allEntries = true)
    public void saveProductAttention(SmProductAttentionDTO dto) {
        dto.setModifyBy(HttpRequestUtil.getUserId());
        int version = getProductUpdateVersion(dto.getProductId());
        int i = historyMapper.updateProductAttention(dto, version);
        if (i == 0) {
            throw new BizException(ExcptEnum.PRODUCT_IS_ONLINE_201002);
        }

    }

    /**
     * 保存产品保险条款
     *
     * @param productId
     * @param masterDTO
     */
    @CacheEvict(value = {PRODUCT_DETAIL}, allEntries = true)
    public void saveProductClause(int productId, SmProductClauseMasterDTO masterDTO, String userId) {

        int version = getProductUpdateVersion(productId);
        historyMapper.deleteProductClause(productId, version);
//        mapper.deleteProductClause(productId, HttpRequestUtil.getUserId());
        if (CommonUtil.isCollectionNotEmpty(masterDTO.getClauses())) {
            masterDTO.getClauses().forEach(cl -> cl.setModifyBy(userId));
//            mapper.insertProductClause(clauses);
            historyMapper.insertProductClause(masterDTO.getClauses(), version);
        }
        historyMapper.deleteProductClauseContent(productId, version);
        historyMapper.insertProductClauseContent(productId, masterDTO.getContent(), version);

    }

    /**
     * 保存产品健康告知
     *
     * @param dto
     */
    @CacheEvict(value = {PRODUCT_DETAIL}, allEntries = true)
    public void saveProductHealthNotification(SmProductNotificationDTO dto) throws Exception {
        dto.setModifyBy(HttpRequestUtil.getUserId());
        int version = getProductUpdateVersion(dto.getProductId());
        int i = historyMapper.updateProductHealthNotification(dto, version);

        if (Objects.equals(dto.getAiCheckWay(), 2)) {
            if (org.apache.commons.lang.StringUtils.isNotBlank(dto.getFileUrl())) {
                akProductQuestionnaireService.importQuestionnaire(dto, HttpRequestUtil.getUserId());
            }
        }
        akProductQuestionnaireService.deleteQuestionHistory(dto.getProductId(), version);
        if (dto.getQuestions() != null) {
            dto.getQuestions().forEach(q -> {
                akProductQuestionnaireService.addQuestionHistory(q, version);
            });
        }
//        if (i == 0) {
//            historyMapper.insertHistoryByMain(dto.getProductId(), null, version);
//            historyMapper.updateProductHealthNotification(dto, version);
//        }
    }

    /**
     * 保存产品健康告知
     *
     * @param dto
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_LIST}, allEntries = true)
    public void saveProductIntroduce(SmProductDTO dto) {
        int version = getProductUpdateVersion(dto.getProductId());
        dto.setModifyBy(HttpRequestUtil.getUserId());
        int i = historyMapper.updateProductIntroduce(dto, version);
//        if (i == 0) {
//            historyMapper.insertHistoryByMain(dto.getProductId(), null, version);
//            historyMapper.updateProductIntroduce(dto, version);
//        }
    }

    /**
     * 保存产品健康告知
     *
     * @param dto
     */
    @CacheEvict(value = {PRODUCT_DETAIL, PRODUCT_LIST}, allEntries = true)
    public void saveProductNotice(SmProductDTO dto) {
        int version = getProductUpdateVersion(dto.getProductId());

        dto.setModifyBy(HttpRequestUtil.getUserId());
//        mapper.updateProductNotice(dto);
        dto.setVersion(version);
        historyMapper.updateProductNotice(dto);
    }

    /**
     * 保存产品价格因素code
     *
     * @param id
     * @param selects
     */
    public void saveProductPriceFactor(int id, List<String> selects) {
        SmProductDTO dto = new SmProductDTO();
        int version = getProductUpdateVersion(id);
        dto.setVersion(version);
        dto.setProductId(id);
        if (CommonUtil.isCollectionNotEmpty(selects)) {
            setModifyBy(dto);
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.UNDER_WRITING_AGE))) {
                dto.setUnderWritingAgeSelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.VALID_PERIOD))) {
                dto.setValidPeriodSelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.SOCIAL_SECURITY))) {
                dto.setSocialSecuritySelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.VEHICLE_SEAT_NUMBER))) {
                dto.setVehicleSeatNumberSelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.SEX))) {
                dto.setSexSelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.OCCUPL_CATEGORY))) {
                dto.setOccuplCategorySelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.PHI_MEDICAINE))) {
                dto.setProtonHeavyIonMedicineSelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.SDS_CARE))) {
                dto.setSpecifiedDiseaseSpecificCareSelect(Boolean.TRUE);
            }
            if (selects.stream().anyMatch(s -> Objects.equals(s, SmConstants.JP_MEDICAL))) {
                dto.setJapanMedicalTreatmentSelect(Boolean.TRUE);
            }
            dto.setPriceCode(JSON.toJSONString(selects));
            historyMapper.updateProductPriceFactor(dto);
        }
    }

    /**
     * 查询产品价格因素字段code
     *
     * @param id
     * @return
     */
    public List<String> getProductPriceFactor(int id) {
        SmProductDetailVO detailVo = getProductById(id);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(detailVo.getPriceCode())) {
            return JSONArray.parseArray(detailVo.getPriceCode(), String.class);
        }
        List<String> factors = new ArrayList<>();
        if (detailVo.getUnderWritingAgeSelect() != null && detailVo.getUnderWritingAgeSelect()) {
            factors.add(SmConstants.UNDER_WRITING_AGE);
        }
        if (detailVo.getValidPeriodSelect() != null && detailVo.getValidPeriodSelect()) {
            factors.add(SmConstants.VALID_PERIOD);
        }
        if (detailVo.getSocialSecuritySelect() != null && detailVo.getSocialSecuritySelect()) {
            factors.add(SmConstants.SOCIAL_SECURITY);
        }
        if (detailVo.getVehicleSeatNumberSelect() != null && detailVo.getVehicleSeatNumberSelect()) {
            factors.add(SmConstants.VEHICLE_SEAT_NUMBER);
        }
        if (detailVo.getSexSelect() != null && detailVo.getSexSelect()) {
            factors.add(SmConstants.SEX);
        }
        if (detailVo.getOccuplCategorySelect() != null && detailVo.getOccuplCategorySelect()) {
            factors.add(SmConstants.OCCUPL_CATEGORY);
        }
        if (detailVo.getProtonHeavyIonMedicineSelect() != null && detailVo.getProtonHeavyIonMedicineSelect()) {
            factors.add(SmConstants.PHI_MEDICAINE);
        }
        if (detailVo.getSpecifiedDiseaseSpecificCareSelect() != null && detailVo.getSpecifiedDiseaseSpecificCareSelect()) {
            factors.add(SmConstants.SDS_CARE);
        }
        if (detailVo.getJapanMedicalTreatmentSelect() != null && detailVo.getJapanMedicalTreatmentSelect()) {
            factors.add(SmConstants.JP_MEDICAL);
        }

        return factors;
    }

    /**
     * 保存产品价格因素可选值
     *
     * @param optionals
     */
    @CacheEvict(value = {PRODUCT_PLAN_FACTOR, PRODUCT_PLAN_PRICE}, allEntries = true)
    public void saveProductFactorOptionals(List<SmFactorOptionalDTO> optionals) {
        if (optionals != null && !optionals.isEmpty()) {
            Integer productId = optionals.get(0).getProductId();
            int version = getProductUpdateVersion(productId);
            historyMapper.deleteProductFactorOptionals(productId, version);

            mapper.insertProductFactorOptionalsDisable(optionals);
            historyMapper.insertProductFactorOptionals(optionals, version);
            Optional<SmFactorOptionalDTO> optional = optionals.stream().filter(o ->
                    Objects.equals(o.getFieldCode(), SmConstants.OCCUPL_CATEGORY)).findFirst();
            if (optional.isPresent()) {
                String occuplCategoryLimitStr = optional.get().getValue1();
                String[] occuplGroups = occuplCategoryLimitStr.split(",");
                List<SmOccupationLimitDTO> occupationLimits = new ArrayList<>(occuplGroups.length);
                for (String o : occuplGroups) {
                    SmOccupationLimitDTO limit = new SmOccupationLimitDTO();
                    limit.setProductId(optionals.get(0).getProductId());
                    limit.setFieldCode(SmConstants.OCCUPL_CATEGORY);
                    limit.setLimitCode(o);
                    limit.setModifyBy(HttpRequestUtil.getUserId());
                    occupationLimits.add(limit);
                }
                occupationMapper.deleteProductOccupationLimitsHistory(productId, SmConstants.OCCUPL_CATEGORY, version);
                occupationMapper.insertProductOccupationLimitsHistory(occupationLimits, version);
            }
        }
    }

    /**
     * 保存产品计划价格费率表
     *
     * @param prices
     */
    @CacheEvict(value = {PRODUCT_LIST, PRODUCT_DETAIL, PRODUCT_PLAN, PRODUCT_PLAN_PRICE, PRODUCT_PLAN_FACTOR}, allEntries = true)
    public void savePlanFactorPrice(List<SmOptionalFieldPriceDTO> prices) {

        if (prices != null && !prices.isEmpty()) {
            int productUpdateVersion = getProductUpdateVersion(prices.get(0).getProductId());
//            mapper.deletePlanFactorPrice(prices.get(0).getProductId());
            mapper.insertPlanFactorPriceDisable(prices);
            historyMapper.deletePlanFactorPrice(prices.get(0).getProductId(), productUpdateVersion);
            historyMapper.insertPlanFactorPrice(prices, productUpdateVersion);
            BigDecimal minAmount = new BigDecimal(100000);
            for (int i = 0, len = prices.size(); i < len; i++) {
                SmOptionalFieldPriceDTO price = prices.get(i);
                if (price.getAvailable() != null && price.getAvailable() && !StringUtils.isEmpty(price.getPrice()) && new BigDecimal(price.getPrice()).compareTo(minAmount) < 0) {
                    minAmount = new BigDecimal(price.getPrice());
                }
            }
            int row = historyMapper.updateProductMinAmount(prices.get(0).getProductId(),
                    minAmount,
                    HttpRequestUtil.getUserId(), productUpdateVersion);
            if (row == 0) { // 如果快照表不存在 就插入
                historyMapper.insertHistoryByMain(prices.get(0).getProductId(), minAmount, productUpdateVersion);
            }
        }
    }

    public void insertPlanFactorPriceRef(int productId, List<SmOptionalFieldPriceDTO> prices) {

        historyMapper.insertPlanFactorPriceRef(productId, prices, getProductUpdateVersion(productId));
    }

    public void deletePlanFactorPriceRef(int productId) {

        historyMapper.deletePlanFactorPriceRef(productId, getProductUpdateVersion(productId));
    }

    /**
     * 查询产品保险价格选项
     *
     * @param id
     * @return
     */
    public SmPriceFactorOptionalsVO getProductPriceFactorOptionals(int id) {
        List<SmFactorOptionalDTO> optionals = mapper.listFieldOptionals(id);
        SmPriceFactorOptionalsVO optionalsVo = new SmPriceFactorOptionalsVO();
        optionalsVo.setUnderWritingAge(getOptionalFieldDto(optionals, SmConstants.UNDER_WRITING_AGE));
        optionalsVo.setValidPeriod(getOptionalFieldDto(optionals, SmConstants.VALID_PERIOD));
        optionalsVo.setSex(getOptionalFieldDto(optionals, SmConstants.SEX));
        optionalsVo.setSocialSecurity(getOptionalFieldDto(optionals, SmConstants.SOCIAL_SECURITY));
        optionalsVo.setVehicleSeatNumber(getOptionalFieldDto(optionals, SmConstants.VEHICLE_SEAT_NUMBER));
        optionalsVo.setOccuplCategory(getOptionalFieldDto(optionals, SmConstants.OCCUPL_CATEGORY));
        optionalsVo.setProtonHeavyIonMedicine(getOptionalFieldDto(optionals, SmConstants.PHI_MEDICAINE));
        optionalsVo.setSpecifiedDiseaseSpecificCare(getOptionalFieldDto(optionals, SmConstants.SDS_CARE));
        optionalsVo.setJapanMedicalTreatment(getOptionalFieldDto(optionals, SmConstants.JP_MEDICAL));
        optionalsVo.setSmoke(getOptionalFieldDto(optionals, SmConstants.OPTIONS_SMOKE));
        return optionalsVo;
    }

    /**
     * 查询产品投保信息录入列表
     *
     * @param productId
     * @return
     */
    public List<SmProductFormFieldCombDTO> getProductFormFields(int productId) {
        List<SmProductFormFieldVO> formFieldVos = mapper.listProductFormFields(productId);
        if (formFieldVos.isEmpty()) {
            formFieldVos = mapper.listProductFormFields(SmConstants.DEFAULT_PRODUCT_ID);
            if (!formFieldVos.isEmpty()) {
                List<SmProductFormFieldDTO> dtos = formFieldVos.stream().map(f -> {
                    f.setProductId(productId);
                    f.setModifyBy(HttpRequestUtil.getUserId());
                    return (SmProductFormFieldDTO) f;
                }).collect(toList());
                mapper.insertProductFormField(dtos);
            }
        }
        Map<String, SmProductFormFieldCombDTO> combMap = new LinkedHashMap<>();

        formFieldVos.stream().forEach(vo -> {
            SmProductFormFieldCombDTO group = combMap.get(vo.getGroupCode());
            if (group == null) {
                group = new SmProductFormFieldCombDTO();
                group.setGroupCode(vo.getGroupCode());
                group.setGroupName(vo.getGroupName());
                group.setList(new ArrayList<>());
            }
            group.getList().add(vo);
            combMap.put(vo.getGroupCode(), group);
        });
        return new ArrayList<>(combMap.values());
    }

    /**
     * 保存产品投保信息录入列表
     *
     * @param fields
     */
    @CacheEvict(value = PRODUCT_FORM_FIELD, allEntries = true)
    public void saveProductInsureForm(List<SmProductFormFieldCombDTO> fields) {
        if (fields != null && !fields.isEmpty() && fields.get(0).getList() != null && !fields.get(0).getList().isEmpty()) {
            Integer productId = fields.get(0).getList().get(0).getProductId();
            int version = getProductUpdateVersion(productId);
//            mapper.deleteProductFormField(version);
            historyMapper.deleteProductFormField(productId, version);
            fields.stream().forEach(fs -> historyMapper.insertProductFormField(fs.getList(), version));
        }
    }


    /**
     * 查询产品保险条款
     *
     * @param productId
     * @return
     */
    public List<SmProductClauseVO> getProductClause(int productId) {
        return mapper.listProductClausesByProductId(productId);
    }

    /**
     * 查询产品计划价格费率表
     *
     * @param id
     * @return
     */
    public List<SmPlanFactorPriceDT> getPlanFactorPrice(int id) {
        List<SmPlanFactorPriceDT> oldPriceVos = mapper.listPlanFactorPrices(id, null);

        List<SmPlanVO> plans = getProductPlans(id);
        if (plans.isEmpty()) {
            return Collections.emptyList();
        }
        SmPriceFactorOptionalsVO vo = getProductPriceFactorOptionals(id);

        List<SmPlanFactorPriceDT> priceVos = new ArrayList<>();
        List<SmPlanFactorPriceDT> iterationVos = new ArrayList<>();
        priceVos.add(new SmPlanFactorPriceDT());

        List<SmFactorOptionalDTO> occuplCategorys = vo.getOccuplCategory();
        if (CommonUtil.isCollectionNotEmpty(occuplCategorys)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = occuplCategorys.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = occuplCategorys.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setOccuplCategoryName(optionalDto.getOptionalName());
                    copy.setOccuplCategoryOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> vehicleSeatNumbers = vo.getVehicleSeatNumber();
        if (CommonUtil.isCollectionNotEmpty(vehicleSeatNumbers)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = vehicleSeatNumbers.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = vehicleSeatNumbers.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setVehicleSeatNumberName(optionalDto.getOptionalName());
                    copy.setVehicleSeatNumberOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> socialSecuritys = vo.getSocialSecurity();
        if (CommonUtil.isCollectionNotEmpty(socialSecuritys)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = socialSecuritys.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = socialSecuritys.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSocialSecurityName(optionalDto.getOptionalName());
                    copy.setSocialSecurityOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> sexs = vo.getSex();
        if (CommonUtil.isCollectionNotEmpty(sexs)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = sexs.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = sexs.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSexName(optionalDto.getOptionalName());
                    copy.setSexOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> underWritingAges = vo.getUnderWritingAge();
        if (CommonUtil.isCollectionNotEmpty(underWritingAges)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = underWritingAges.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = underWritingAges.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setUnderWritingAgeName(optionalDto.getOptionalName());
                    copy.setUnderWritingAgeOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> validPeriods = vo.getValidPeriod();
        if (CommonUtil.isCollectionNotEmpty(validPeriods)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = validPeriods.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = validPeriods.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setValidPeriodName(optionalDto.getOptionalName());
                    copy.setValidPeriodOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> protonHeavyIonMedicines = vo.getProtonHeavyIonMedicine();
        if (CommonUtil.isCollectionNotEmpty(protonHeavyIonMedicines)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = protonHeavyIonMedicines.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = protonHeavyIonMedicines.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setProtonHeavyIonMedicineName(optionalDto.getOptionalName());
                    copy.setProtonHeavyIonMedicineOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> specifiedDiseaseSpecificCares = vo.getSpecifiedDiseaseSpecificCare();
        if (CommonUtil.isCollectionNotEmpty(specifiedDiseaseSpecificCares)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = specifiedDiseaseSpecificCares.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = specifiedDiseaseSpecificCares.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSpecifiedDiseaseSpecificCareName(optionalDto.getOptionalName());
                    copy.setSpecifiedDiseaseSpecificCareOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> japanMedicalTreatments = vo.getJapanMedicalTreatment();
        if (CommonUtil.isCollectionNotEmpty(japanMedicalTreatments)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = japanMedicalTreatments.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = japanMedicalTreatments.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setJapanMedicalTreatmentName(optionalDto.getOptionalName());
                    copy.setJapanMedicalTreatmentOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        List<SmFactorOptionalDTO> smoke = vo.getSmoke();
        if (CommonUtil.isCollectionNotEmpty(smoke)) {
            iterationVos.clear();
            iterationVos.addAll(priceVos);
            priceVos.clear();
            for (int i = 0, len = smoke.size(); i < len; i++) {
                SmFactorOptionalDTO optionalDto = smoke.get(i);
                for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                    SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                    copy.setSmokeOptionalName(optionalDto.getOptionalName());
                    copy.setSmokeOptional(optionalDto.getId());
                    priceVos.add(copy);
                }
            }
        }

        iterationVos.clear();
        iterationVos.addAll(priceVos);
        priceVos.clear();
        for (int i = 0, len = plans.size(); i < len; i++) {
            SmPlanVO planVo = plans.get(i);
            for (int j = 0, jlen = iterationVos.size(); j < jlen; j++) {
                SmPlanFactorPriceDT copy = iterationVos.get(j).copy();
                copy.setPlanId(planVo.getId());
                copy.setPlanName(planVo.getPlanName());
                copy.setProductName(planVo.getProductName());
                copy.setProductId(planVo.getProductId());
                copy.setPrice("0.00");
                copy.setAvailable(Boolean.TRUE);
                priceVos.add(copy);
            }
        }

        oldPriceVos.stream().forEach(op -> {
            Optional<SmPlanFactorPriceDT> optional = priceVos.stream().filter(op::isSameGroup).findFirst();
            if (optional.isPresent()) {
                optional.get().setPrice(op.getPrice());
                optional.get().setAvailable(op.getAvailable());
            }
        });
        return priceVos;
    }

    /**
     * 微信查询产品所有计划选项
     *
     * @param id
     * @return
     */
    public SmPriceFactorOptionalsVO getPlanPriceFactorOptions(int id) {
        SmPlanFactorQuery query = new SmPlanFactorQuery();
        query.setPlanId(id + "");
        query.setAvailable(Boolean.TRUE);
        List<SmPlanFactorPriceDT> planSpecs = mapper.getPlanSpecPrice(query);
        List<Integer> underWritingAgeOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getUnderWritingAgeOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> occuplCategoryOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getOccuplCategoryOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> socialSecurityOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getSocialSecurityOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> sexOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getSexOptional).filter(Objects::nonNull)
                .collect(toList());
        List<Integer> validPeriodOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getValidPeriodOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> vehicleSeatNumberOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getVehicleSeatNumberOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> protonHeavyIonMedicineOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getProtonHeavyIonMedicineOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> specifiedDiseaseSpecificCareOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getSpecifiedDiseaseSpecificCareOptional)
                .filter(Objects::nonNull).collect(toList());
        List<Integer> japanMedicalTreatmentOptionals = planSpecs.stream().map(SmPlanFactorPriceDT::getJapanMedicalTreatmentOptional)
                .filter(Objects::nonNull).collect(toList());

        List<Integer> smokeOptions = planSpecs.stream().map(SmPlanFactorPriceDT::getSmokeOptional)
                .filter(Objects::nonNull).collect(toList());

        SmPlanVO planVo = getPlanById(id);
        List<SmFactorOptionalDTO> optionals = mapper.listFieldOptionals(planVo.getProductId());
        SmPriceFactorOptionalsVO optionalsVo = new SmPriceFactorOptionalsVO();
        List<SmFactorOptionalDTO> underWritingAgeDtos = getOptionalFieldDto(optionals, SmConstants.UNDER_WRITING_AGE).stream()
                .filter(u -> underWritingAgeOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!underWritingAgeDtos.isEmpty()) {
            optionalsVo.setUnderWritingAge(underWritingAgeDtos);
        }
        List<SmFactorOptionalDTO> validPeriodDtos = getOptionalFieldDto(optionals, SmConstants.VALID_PERIOD).stream()
                .filter(u -> validPeriodOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!validPeriodDtos.isEmpty()) {
            optionalsVo.setValidPeriod(validPeriodDtos);
        }
        List<SmFactorOptionalDTO> sexDtos = getOptionalFieldDto(optionals, SmConstants.SEX).stream()
                .filter(u -> sexOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!sexDtos.isEmpty()) {
            optionalsVo.setSex(sexDtos);
        }
        List<SmFactorOptionalDTO> socialSecurityDtos = getOptionalFieldDto(optionals, SmConstants.SOCIAL_SECURITY).stream()
                .filter(u -> socialSecurityOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!socialSecurityDtos.isEmpty()) {
            optionalsVo.setSocialSecurity(socialSecurityDtos);
        }
        List<SmFactorOptionalDTO> vehicleSeatNumberDtos = getOptionalFieldDto(optionals, SmConstants.VEHICLE_SEAT_NUMBER).stream()
                .filter(u -> vehicleSeatNumberOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!vehicleSeatNumberDtos.isEmpty()) {
            optionalsVo.setVehicleSeatNumber(vehicleSeatNumberDtos);
        }
        List<SmFactorOptionalDTO> occupationDtos = getOptionalFieldDto(optionals, SmConstants.OCCUPL_CATEGORY).stream()
                .filter(u -> occuplCategoryOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!occupationDtos.isEmpty()) {
            optionalsVo.setOccuplCategory(occupationDtos);
        }
        List<SmFactorOptionalDTO> protonHeavyIonMedicines = getOptionalFieldDto(optionals, SmConstants.PHI_MEDICAINE).stream()
                .filter(u -> protonHeavyIonMedicineOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!protonHeavyIonMedicines.isEmpty()) {
            optionalsVo.setProtonHeavyIonMedicine(protonHeavyIonMedicines);
        }
        List<SmFactorOptionalDTO> specifiedDiseaseSpecificCares = getOptionalFieldDto(optionals, SmConstants.SDS_CARE).stream()
                .filter(u -> specifiedDiseaseSpecificCareOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!specifiedDiseaseSpecificCares.isEmpty()) {
            optionalsVo.setSpecifiedDiseaseSpecificCare(specifiedDiseaseSpecificCares);
        }
        List<SmFactorOptionalDTO> japanMedicalTreatments = getOptionalFieldDto(optionals, SmConstants.JP_MEDICAL).stream()
                .filter(u -> japanMedicalTreatmentOptionals.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());
        if (!japanMedicalTreatments.isEmpty()) {
            optionalsVo.setJapanMedicalTreatment(japanMedicalTreatments);
        }

        List<SmFactorOptionalDTO> smokes = getOptionalFieldDto(optionals, SmConstants.OPTIONS_SMOKE).stream()
                .filter(u -> smokeOptions.stream().anyMatch(j -> Objects.equals(j, u.getId()))).collect(toList());

        if (!CollectionUtils.isEmpty(smokeOptions)) {
            optionalsVo.setSmoke(smokes);
        }
        return optionalsVo;
    }

    /**
     * 查询产品保障项目
     *
     * @param productId
     * @return
     */
    public List<SmProductCoverageVO> getProductCoverageList(int productId) {
        return mapper.listProductCoverages(productId);
    }

    /**
     * 保存产品保障项目
     *
     * @param productId
     * @param dtos
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void saveProductCoverage(int productId, List<SmProductCoverageDTO> dtos) {
        final int productUpdateVersion = getProductUpdateVersion(productId);
        historyMapper.deleteAllCoverage(productId, productUpdateVersion);
        String userId = HttpRequestUtil.getUserId();
        dtos.forEach(dto -> {
            dto.setProductId(productId);
            dto.setOperator(userId);
            if (dto.getSpcId() == null) {
                mapper.insertProductCoverageDisable(dto);
            }
            historyMapper.insertProductCoverage(dto, productUpdateVersion);
        });
    }

    @Autowired
    protected BusinessTokenService tokenService;

    /**
     * 更新版本
     *
     * @param productId
     */
    @DistributedLock(params = "productId", prefix = "product:update:")
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {PRODUCT_LIST, PRODUCT_PLAN_PRICE, PRODUCT_FORM_FIELD, PRODUCT_DETAIL, WX_LBT_IMAGE}, allEntries = true)
    public void pushNewVersion(int productId, String operator) {

        int updateVersion = getProductUpdateVersion(productId);
        SmProductVersion version = new SmProductVersion();
        version.setProductId(productId);
        version.setCreateBy(operator);
        version.setVersion(updateVersion);

        productVersionMapper.insertUseGeneratedKeys(version);
        //需要更新所有东西

        // 更新基础信息
        historyMapper.pushMainByHistory(productId, updateVersion);

        pushProductAttr(productId, updateVersion);
        // 职业限制数据
        occupationMapper.deleteProductOccupationLimits(productId, SmConstants.OCCUPL_CATEGORY);
        occupationMapper.insertOccupationsByHistory(productId, updateVersion);

        //更新产品计划
        historyMapper.pushPlanByHistory(productId, updateVersion);
        //更新计费因子
        mapper.deletePlanFactorPrice(productId);
        historyMapper.pushPlanPriceByHistory(productId, updateVersion);

        //计费因子
        mapper.deleteProductFactorOptionals(productId);
        historyMapper.pushProductFactorOptionals(productId, updateVersion);
        //保障项目
        historyMapper.pushProductCoverage(productId, updateVersion);
        //团险产品保费折扣比例表
        historyMapper.pushProductCoverageDiscount(productId, updateVersion);
        /**
         * 责任因子表更新
         */
        mapper.deleteDutyFactor(productId);
        historyMapper.pushDutyFactor(productId, updateVersion);

        /**
         * 费率-时间因子
         */
        mapper.deleteTimeFactor(productId);
        historyMapper.pushTimeFactor(productId, updateVersion);
        //团险产品保障计划金额表
        historyMapper.pushProductCoverageAmount(productId, updateVersion);
        historyMapper.pushProductQuoteLimit(productId, updateVersion);

        historyMapper.pushProductPremium(productId, updateVersion);
        //更新职业限制
        occupationMapper.deleteProductOccupationLimits(productId, SmConstants.OCCUPL_CATEGORY);
        historyMapper.pushOccupationLimits(productId, updateVersion);

        //更新表单字段
        mapper.deleteProductFormField(productId);
        historyMapper.pushProductFormField(productId, updateVersion);

        //更新客户告知书配置
        historyMapper.pushProductNotify(productId, updateVersion);

        mapper.deleteProductClause(productId, operator);

        historyMapper.pushProductClause(productId, updateVersion);

        mapper.deleteProductClauseContent(productId);
        historyMapper.pushProductClauseContent(productId, updateVersion);

        historyMapper.pushProductConfirm(productId, updateVersion);

        //智能核保问卷
        historyMapper.pushProductQuestionnaire(productId, updateVersion);
        /**
         * 团险智能核保问卷
         */
        historyMapper.pushProductQuestion(productId, updateVersion);
        SmProductDetailVO productDetailVO = historyMapper.getProductById(productId, updateVersion);
        //如果是长险更新险种关联和长险的产品计划
        if (Objects.equals(productDetailVO.getCreateType(), EnumProductCreateType.PERSON_LONG_INSURANCE.name())) {
            //险种管理更新版本
            smProductRiskService.pushNewVersion(productId, updateVersion);
            //长险的产品计划更新版本
            smLongInsurancePlanService.pushNewVersion(productId, updateVersion);
            //健康告知更新版本
            smProductHealthInformService.pushNewVersion(productId, updateVersion);
        }

        //每次发版新的版本的时候把下一个版本的数据初始化
        historyService.initNewHistory(productId, updateVersion, updateVersion + 1);
        clearCache(productId);
    }

    @Autowired
    private MpNotifyFacade mpNotifyFacade;

    @Autowired
    private AttributeProcessorFactory attributeProcessorFactory;

    /**
     * 产品扩展属性
     *
     * @param productId
     * @param version
     */
    public void pushProductAttr(Integer productId, int version) {
        txService.excute(() -> {
            productAttrMapper.deleteByProduct(productId);
            historyMapper.pushProductAttr(productId, version);
        });

        List<SmProductAttr> attrList = productAttrMapper.listEnable(productId);
        attrList.stream().forEach(attributeProcessorFactory::execute);
    }

    /**
     * 缓存更新
     *
     * @param productId
     */
    private void clearCache(int productId) {
        try {
            String cache1 = RedisCache.buildHotOccupationCache(productId);
            String cache2 = RedisCache.buildOccupationCache(productId);
            List<String> keys = new ArrayList<>(2);
            keys.add(cache1);
            keys.add(cache2);
            redisUtil.remove(keys);
            mpNotifyFacade.productChange(String.valueOf(productId));
        } catch (Exception e) {
            log.error("产品变更-缓存清理失败，请开发人员手动清理", e);
        }
    }

    /**
     * 获取某个产品的变更记录
     *
     * @param productId
     * @return
     */
    public List<SmProductVersionVO> listVersionsByProduct(int productId) {

        return productVersionMapper.selectByProductId(productId);
    }


    /**
     * 删除产品保障项目
     *
     * @param productId
     * @param spcId
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void deleteProductCoverage(int productId, int spcId) {
        mapper.deleteProductCoverage(productId, spcId);
        mpNotifyFacade.productChange(String.valueOf(productId));
    }

    /**
     * 查询产品保障项目金额
     *
     * @param productId
     * @return
     */
    public List<SmProductCoverageAmountItemVO> getProductCoverageAmountList(int productId) {
        List<SmProductCoverageAmountVO> amounts = mapper.listProductCoverageAmounts(productId);

        SmProductDetailVO productDetailVO = mapper.getProductById(productId);
        List<SmProductCoverageAmountItemVO> items = new ArrayList<>();
        if (Objects.equals(productDetailVO.getProductAttrCode(), SmConstants.PRODUCT_ATTR_GROUP)) {
            mapper.listProductCoverages(productId)
                    .forEach(cv -> {
                        SmProductCoverageAmountItemVO item = newSmProductCoverageAmountItem(productId, cv);
                        item.setCoverageAmounts(amounts.stream().filter(am -> Objects.equals(am.getSpcId(), cv.getSpcId())).collect(toList()));
                        items.add(item);
                    });
        } else {
            List<SmPlanVO> plans = mapper.listProductPlansById(String.valueOf(productId), true);
            mapper.listProductCoverages(productId)
                    .forEach(cv -> {
                        SmProductCoverageAmountItemVO item = newSmProductCoverageAmountItem(productId, cv);
                        List<SmProductCoverageAmountVO> cpAmounts = new ArrayList<>();
                        plans.forEach(p -> {
                            Optional<SmProductCoverageAmountVO> optional = amounts.stream().filter(am -> Objects.equals(am.getSpcId(), cv.getSpcId()) && Objects.equals(am.getPlanId(), p.getId()))
                                    .findFirst();
                            SmProductCoverageAmountVO cpAmount;
                            if (optional.isPresent()) {
                                cpAmount = optional.get();
                            } else {
                                cpAmount = new SmProductCoverageAmountVO();
                                cpAmount.setProductId(productId);
                                cpAmount.setPlanId(p.getId());
                                cpAmount.setPlanName(p.getPlanName());
                                cpAmount.setSpcId(cv.getSpcId());
                                cpAmount.setCvgType(cv.getCvgType());
                                cpAmount.setCvgItemName(cv.getCvgItemName());
                            }
                            cpAmount.setPlanName(p.getPlanName());
                            cpAmounts.add(cpAmount);
                            item.setCoverageAmounts(cpAmounts);
                        });
                        items.add(item);
                    });
        }
        return items;
    }

    /**
     * 保存产品保障项目金额
     *
     * @param productId
     * @param dtos
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void saveProductCoverageAmount(int productId, List<SmProductCoverageAmountItemDTO> dtos) {
        final int version = getProductUpdateVersion(productId);
        dtos.forEach(dto -> dto.getCoverageAmounts().forEach(
                am -> {
                    am.setProductId(productId);
                    am.setSpcId(dto.getSpcId());
                    if (am.getSpcaId() == null) {
                        mapper.insertProductCoverageAmountDisable(am);
                        am.setVersion(version);
                        historyMapper.insertProductCoverageAmount(am);
                    } else {
                        am.setVersion(version);
                        historyMapper.updateProductCoverageAmount(am);
                    }
                }
        ));
    }

    /**
     * 删除产品保障项目金额
     *
     * @param productId
     * @param spcaId
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void deleteProductCoverageAmount(int productId, int spcaId) {
        historyMapper.deleteProductCoverageAmount(spcaId, getProductUpdateVersion(productId));
    }

    /**
     * 查询单个产品销售区域列表
     *
     * @param id
     * @return
     */
    public List<SmProductSalesOrgVO> getProductSalesOrgsByProductId(int id) {
        return mapper.listProductSalesOrgsByProductId(id);
    }

    /**
     * 查询所有产品销售区域列表
     *
     * @return
     */
    public Collection<SmProductSalesOrgVO> getOnlineProductSalesOrgs() {
        return mapper.listOnlineProductSalesOrgs();
    }

    /**
     * 查询所有产品销售区域列表
     *
     * @return
     */
    public PageInfo<SmActivityProductVO> getActivityProducts(ActivityProductQuery query) {
        if (!query.isAll()) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<SmActivityProductVO> products = mapper.listActivityProducts(query);
        // 多个分类处理
        Map<String, String> categoryMap = dictionaryService.getDictionarysByPage(SmConstants.DICTIONARY_PRODUCT_TYPE, null, null, false)
                .getList().stream()
                .collect(toMap(d -> d.getId() + "", DictionaryVO::getName));
        products.stream().forEach(p -> {
            String categoryId = p.getProductCategoryId();
            if (!StringUtils.isEmpty(categoryId)) {
                String[] categoryIds = categoryId.split(",");
                StringBuilder sb = new StringBuilder();
                for (String s : categoryIds) {
                    sb.append(categoryMap.get(s)).append(" / ");
                }
                p.setProductCategoryName(sb.toString().substring(0, sb.length() - 2));
            }
        });
        return new PageInfo<>(products);
    }

    /**
     * 保存产品销售区域列表
     *
     * @param psos
     */
    @CacheEvict(value = {PRODUCT_LIST, PRODUCT_DETAIL, WX_LBT_IMAGE}, allEntries = true)
    public void saveProductSalesOrgs(List<SmProductSalesOrgDTO> psos) {
        if (psos == null || psos.isEmpty()) {
            throw new BizException(ExcptEnum.PARAMS_ERROR);
        }
        psos.forEach(f -> {
            f.setCreateBy(HttpRequestUtil.getUserId());
            if (StringUtils.isEmpty(f.getOrgPath())) {
                f.setOrgPath(null);
            }
        });
        Integer productId = psos.get(0).getProductId();
        mapper.deleteProductSalesOrgs(psos.get(0).getProductId());
        mapper.insertProductSalesOrgs(psos);
        mpNotifyFacade.productChange(String.valueOf(productId));
    }

    /**
     * 查询单个产品销售区域列表
     *
     * @param productId
     * @return
     */
    public List<SmPlanSalesOrgDTO> getPlanSaleOrgByProductId(int productId) {

        SmPlanSalesOrg planSalesOrg = new SmPlanSalesOrg();
        planSalesOrg.setProductId(productId);
        List<SmPlanSalesOrg> smPlanSalesOrgs = planSaleOrgMapper.selectEnabled(planSalesOrg);
        //如果是无限制的区域
        if (smPlanSalesOrgs.stream().anyMatch(se -> Objects.equals(se.getOrgName(), "无限制"))) {
            List<Integer> planIds = smPlanSalesOrgs.stream().map(SmPlanSalesOrg::getPlanId).distinct().collect(toList());
            SmPlanSalesOrgDTO dto = new SmPlanSalesOrgDTO();
            dto.setPlanIds(planIds);
            dto.setProductId(productId);
            //汇总成一行记录
            SmPlanSalesOrg next = smPlanSalesOrgs.iterator().next();
            next.setPlanId(null);
            dto.setAreas(Collections.singletonList(next));
            return Collections.singletonList(dto);

        }
        Map<Integer, List<SmPlanSalesOrg>> plan = LambdaUtils.groupBy(smPlanSalesOrgs,
                SmPlanSalesOrg::getHrOrgId);

        //分组成 计划1-计划2 List<OrgId,plans>
        Map<String, List<Map.Entry<Integer, List<SmPlanSalesOrg>>>> plansGroup = plan.entrySet()
                .stream().collect(
                        groupingBy(entry -> entry.getValue().stream().map(SmPlanSalesOrg::getPlanId)
                                .map(String::valueOf)
                                .collect(joining("-"))));
        return plansGroup.entrySet()
                .stream()
                .map(en -> {
                    SmPlanSalesOrgDTO dto = new SmPlanSalesOrgDTO();
                    List<Integer> planIds = Stream.of(en.getKey().split("-"))
                            .map(Integer::valueOf).collect(toList());

                    List<SmPlanSalesOrg> sales = en.getValue()
                            .stream()
                            .map(areaPlans -> areaPlans.getValue().get(0))
                            .collect(toList());
                    dto.setPlanIds(planIds);
                    dto.setProductId(productId);
                    dto.setAreas(sales);
                    return dto;
                }).collect(toList());

    }

    /**
     * 保存产品计划销售区域列表
     */
    @CacheEvict(value = {PRODUCT_PLAN_PRICE, PRODUCT_PLAN, PRODUCT_LIST, PRODUCT_DETAIL, WX_LBT_IMAGE}, allEntries = true)
    public void savePlanSalesOrgs(int productId, List<SmPlanSalesOrgDTO> psos) {

        SmPlanSalesOrg planSalesOrg = new SmPlanSalesOrg();
        planSalesOrg.setEnabledFlag(SmConstants.MODEL_DISABLE);
        Example example = new Example(SmPlanSalesOrg.class);
        example.createCriteria()
                .andEqualTo("productId", productId).andEqualTo(
                        "enabledFlag", SmConstants.MODEL_ENABLE
                );
        //禁用以前的数据
        planSaleOrgMapper.updateByExampleSelective(planSalesOrg, example);
        //
        List<SmPlanSalesOrg> models = psos.stream()
                .map(dto -> dto.getPlanIds()
                        .stream()
                        .map(planId -> {
                            //拷贝对象
                            List<SmPlanSalesOrg> copyAreas = jsonMapper.convertValue(dto.getAreas(),
                                    new TypeReference<List<SmPlanSalesOrg>>() {
                                    });
                            copyAreas.forEach(cp -> {
                                cp.setPlanId(planId);
                                cp.setProductId(productId);
                                if (StringUtils.isEmpty(cp.getOrgPath())) {
                                    cp.setOrgPath(null);
                                }
                            });
                            return copyAreas;
                        }).reduce(LambdaUtils.getListMerge()).orElseGet(ArrayList::new))
                .reduce(LambdaUtils.getListMerge()).orElseGet(ArrayList::new);
        planSaleOrgMapper.insertList(models);
        mpNotifyFacade.productChange(String.valueOf(productId));
    }


    /**
     * 查询后端产品续保列表
     *
     * @return
     */
    public PageInfo<SmProductRenewVO> getProductRenews(String productName, Pageable pageable) {
        if (pageable != null) {
            PageHelper.startPage(pageable.getPage(), pageable.getSize());
        }
        return new PageInfo<>(mapper.listProductRenews(productName, null));
    }

    /**
     * 查询微信产品续保列表
     *
     * @return
     */
    public List<WxProductRenewVO> getWxRenewProductsByProductId(int productId) {
        return mapper.listWxRenewProductsProductId(productId);
    }

    /**
     * 查询单个产品续保列表
     *
     * @return
     */
    public List<SmProductRenewVO> getProductRenewsByProductId(int productId) {
        return mapper.listRenewProductsByProductId(productId);
    }

    /**
     * 保存产品续保列表
     *
     * @param id
     * @param renews
     */
    public void saveProductRenews(int id, List<SmProductRenewDTO> renews) {
        mapper.deleteProductRenews(id);
        if (!renews.isEmpty()) {
            renews.forEach(r -> r.setProductId(id));
            mapper.insertProductRenews(renews);
        }
    }

    /**
     * 通过计划Id查询计划详情信息
     *
     * @param planId
     * @return
     */
    public SmPlanVO getPlanById(int planId) {
        return mapper.getPlanById(planId);
    }



    public SmPlanVO getPlanByOrderId(String orderId) {
        return mapper.getPlanByOrderId(orderId);
    }

    /**
     * 通过计划Id查询计划详情信息
     *
     * @param planIdList
     * @return
     */
    public List<SmPlanVO> getPlanById(List<Long> planIdList) {
        return historyMapper.getPlanByPlanIdList(planIdList);
    }

    /**
     * 通过计划Id查询有效的计划详情信息
     *
     * @param planId
     * @return
     */
    public SmPlanVO getActivePlanById(int planId) {
        return mapper.getActivePlanById(planId);
    }


    /**
     * 通过泛华产品Id查询计划详情信息
     *
     * @param productId
     * @return
     */
    public SmPlanVO getPlanByFhProductId(String productId) {
        return mapper.getPlanByFhProductId(productId);
    }

    /**
     * 通过渠道编码，产品Id列表查询计划详情信息
     *
     * @param channel
     * @param fhProductIdList
     * @return
     */
    public List<SmPlanVO> listPlanByFhProductIds(String channel, List<String> fhProductIdList) {
        return mapper.listPlanByFhProductIds(channel, fhProductIdList);
    }

    /**
     * 查询产品保障项目保费
     *
     * @param productId
     * @return
     */
    public List<SmProductCoveragePremiumItemVO> getProductCoveragePremiumList(int productId, Integer version) {
        if (version == null) {
            version = getProductUpdateVersion(version);
        }
        historyMapper.listProductCoveragePremiums(productId, version);
        List<SmProductCoveragePremiumDTO> premiums = mapper.listProductCoveragePremiums(productId);
        SmProductDetailVO productDetail = mapper.getProductById(productId);
        String ocpn = productDetail.getGlOcpnGroup();
        if (StringUtils.isEmpty(ocpn)) {
            throw new BizException(ExcptEnum.PRODUCT_OCPN_ERROR_501111);
        }
        String[] ocpns = ocpn.split(",");
        if (ocpns.length == 0) {
            throw new BizException(ExcptEnum.PRODUCT_OCPN_ERROR_501111);
        }
        String[] sortedOcpns = Arrays.stream(ocpns).sorted().toArray(String[]::new);
        List<SmProductCoveragePremiumItemVO> coverageItems = new ArrayList<>();
        getProductCoverageAmountList(productId)
                .forEach(ci -> {
                    SmProductCoveragePremiumItemVO item = new SmProductCoveragePremiumItemVO();
                    item.setCvgItemName(ci.getCvgItemName());
                    item.setProductId(ci.getProductId());
                    item.setSpcId(ci.getSpcId());
                    item.setCvgType(ci.getCvgType());
                    List<SmProductCoveragePremiumItemVO.CoverageAmount> coverageAmounts = new ArrayList<>();
                    ci.getCoverageAmounts().forEach(ca -> {
                        SmProductCoveragePremiumItemVO.CoverageAmount cav = new SmProductCoveragePremiumItemVO.CoverageAmount();
                        cav.setCvgAmount(ca.getCvgAmount());
                        cav.setCvgNotice(ca.getCvgNotice());
                        cav.setSpcaId(ca.getSpcaId());
                        cav.setPremiums(new ArrayList<>());
                        for (String gp : sortedOcpns) {
                            Optional<SmProductCoveragePremiumDTO> optional = premiums.stream().filter(p -> Objects.equals(p.getSpcaId(), ca.getSpcaId()) && Objects.equals(gp, p.getOccupationGroup())).findFirst();
                            if (optional.isPresent()) {
                                cav.getPremiums().add(optional.get());
                            } else {
                                SmProductCoveragePremiumDTO premium = new SmProductCoveragePremiumDTO();
                                premium.setProductId(ca.getProductId());
                                premium.setSpcaId(ca.getSpcaId());
                                premium.setSpcId(ca.getSpcId());
                                premium.setOccupationGroup(gp);
                                premium.setCvgItemName(ca.getCvgItemName());
                                premium.setCvgAmount(ca.getCvgAmount());
                                premium.setCvgNotice(ca.getCvgNotice());
                                cav.getPremiums().add(premium);
                            }
                        }
                        coverageAmounts.add(cav);
                    });
                    item.setCoverageAmounts(coverageAmounts);
                    coverageItems.add(item);
                });
        return coverageItems;
    }

    /**
     * 查询产品计划
     *
     * @param productId
     * @return
     */
    public List<SmPlanVO> getProductPlans(int productId, Integer version) {
        List<SmPlanVO> plan = historyMapper.listProductPlansById(String.valueOf(productId), true, version);
        plan.forEach(i -> {
            if (org.apache.commons.lang.StringUtils.isBlank(i.getMinPremium())) {
                if (i.getMinPrice() != null) {
                    i.setMinPremium(String.valueOf(i.getMinPrice()));
                }
            }

        });
        return plan;
    }

    /**
     * 查询产品计划
     *
     * @param productId
     * @return
     */
    public Map<String, SmPlanVO> getPlanMap(int productId, Integer version) {
        List<SmPlanVO> plan = historyMapper.listProductPlansById(String.valueOf(productId), true, version);
        Map<String, SmPlanVO> map = new HashMap<>();
        plan.forEach(i -> {
            map.put(i.getPlanName(), i);
        });
        return map;
    }

    public void savePremiumTable(int productId, SmProductCoveragePremiumTable table) {
        if (table.getFiles() != null) {
            savePremiumFile(productId, table.getFiles());
        }
        if (table.getData() != null) {
            saveProductCoveragePremium(productId, table.getData());
        }
    }

    public boolean savePremiumFile(Integer productId, List<PremiumFile> files) {
        mapper.deletePremiumFile(productId);
        String uid = HttpRequestUtil.getUserId();
        files.forEach(f -> {
            SmProductExtend e = new SmProductExtend();
            e.setAttrName(f.getName());
            e.setAttrValue(f.getUrl());
            e.setType(1);
            e.setOperator(uid);
            e.setProductId(productId);
            mapper.insertProductExtend(e);
        });
        return true;
    }

    /**
     * 保存产品保障项目保费
     *
     * @param productId
     * @param items
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void saveProductCoveragePremium(int productId, List<SmProductCoveragePremiumItemDTO> items) {
        BigDecimal minAmount = BigDecimal.ZERO;
        int version = getProductUpdateVersion(productId);
        for (SmProductCoveragePremiumItemDTO item : items) {
            BigDecimal minPremium = new BigDecimal(1000000);
            for (SmProductCoveragePremiumItemDTO.SmProductCoverageAmountVO cvg : item.getCoverageAmounts()) {
                for (SmProductCoveragePremiumDTO pu : cvg.getPremiums()) {
                    pu.setProductId(productId);
                    pu.setSpcId(item.getSpcId());
                    pu.setSpcaId(cvg.getSpcaId());
                    if (pu.getSpcpId() != null && pu.getSpcpId() > 0) {
                        historyMapper.updateProductCoveragePremium(pu, version);
//                        mapper.updateProductCoveragePremium(pu);
                    } else {
//                        mapper.insertProductCoveragePremium();
                        mapper.insertProductCoveragePremiumDisable(Collections.singletonList(pu));
                        historyMapper.insertProductCoveragePremium(Collections.singletonList(pu), version);
                    }
                    if (pu.getPremium() != null && pu.getPremium().compareTo(BigDecimal.ZERO) > 0 && pu.getPremium().compareTo(minPremium) < 0) {
                        minPremium = pu.getPremium();
                    }
                }
            }
            if (Objects.equals(item.getCvgType(), "main")) {
                minAmount = minAmount.add(minPremium);
            }
        }
        historyMapper.updateProductMinAmount(productId, minAmount, HttpRequestUtil.getUserId(), version);
    }

    /**
     * 生成产品保障项目保费
     *
     * @param productId
     * @return
     */
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void generateProductCoveragePremium(int productId) {
        SmProductDetailVO productDetail = getProductById(productId);
        List<SmProductCoveragePremiumDTO> premiums = new ArrayList<>();
        if (productDetail != null && productDetail.getGlOcpnGroup() != null) {
            String[] copnGroups = productDetail.getGlOcpnGroup().split(",");
            List<SmProductCoverageAmountVO> amounts = mapper.listProductCoverageAmounts(productId);
            Stream.of(copnGroups).forEach(gp -> {
                amounts.forEach(am -> {
                    SmProductCoveragePremiumDTO dto = new SmProductCoveragePremiumDTO();
                    dto.setProductId(productId);
                    dto.setOccupationGroup(gp);
                    dto.setSpcId(am.getSpcId());
                    dto.setSpcaId(am.getSpcaId());
                    premiums.add(dto);
                });
            });
        }
        mapper.deleteProductCoveragePremium(productId);
        if (!premiums.isEmpty()) {
            mapper.insertProductCoveragePremium(premiums);
        }
    }

    /**
     * 品保障项目保费模板下载
     *
     * @param productId
     * @return
     */
    public void downloadProductCoveragePremiumTemplate(int productId, HttpServletResponse resp) {
        List<SmProductCoverageAmountItemVO> coverageAmountItems = getProductCoverageAmountList(productId);

        SmProductDetailVO productDetail = mapper.getProductById(productId);
        String glOcpnGroup = productDetail.getGlOcpnGroup();
        if (StringUtils.isEmpty(glOcpnGroup)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品职业配置错误");
        }
        String[] ocpnGroups = glOcpnGroup.split(",");
        if (ocpnGroups.length == 0) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品职业配置错误");
        }
        ocpnGroups = Stream.of(ocpnGroups).sorted().toArray(String[]::new);
        ExcelBuilderUtil excelBuilderUtil = ExcelBuilderUtil.newInstance().createSheet("费率表");
        Sheet sheet = excelBuilderUtil.getSheet();
        Workbook workbook = excelBuilderUtil.getWorkbook();
        // 字体加粗
        CellStyle cellStyle = workbook.createCellStyle();

        int rowNo = 0;
        for (SmProductCoverageAmountItemVO ca : coverageAmountItems) {
            Row row = sheet.createRow(rowNo);
            Cell cell = createCell(row, 0, cellStyle);
            cell.setCellValue("保障项目");
            cell = createCell(row, 1, cellStyle);
            cell.setCellValue("保额");
            for (int i = 0, len = ocpnGroups.length; i < len; i++) {
                cell = createCell(row, i + 2, cellStyle);
                cell.setCellValue(ocpnGroups[i] + "类");
            }
            rowNo++;
            row = sheet.createRow(rowNo);
            cell = createCell(row, 0, cellStyle);
            cell.setCellValue(ca.getCvgItemName());

            List<SmProductCoverageAmountVO> coverageAmounts = ca.getCoverageAmounts();
            if (!coverageAmounts.isEmpty()) {
                cell = createCell(row, 1, cellStyle);
                cell.setCellValue(coverageAmounts.get(0).getCvgNotice());
            }
            rowNo++;
            for (int i = 1, len = coverageAmounts.size(); i < len; i++) {
                row = sheet.createRow(rowNo);
                cell = createCell(row, 0, cellStyle);
                cell.setCellValue("");
                cell = createCell(row, 1, cellStyle);
                cell.setCellValue(coverageAmounts.get(i).getCvgNotice());
                rowNo++;
            }
            row = sheet.createRow(rowNo);
            cell = createCell(row, 0, cellStyle);
            cell.setCellValue("");
            rowNo++;
        }
        sheet.setColumnWidth(0, 256 * 20);
        sheet.setColumnWidth(1, 256 * 10);
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(productDetail.getProductName() + "保障项目费率表模板", StandardCharsets.UTF_8.name()) + ".xlsx");
            resp.setContentType("application/octet-stream");
            excelBuilderUtil.write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }


    /**
     * S48-导入品保障项目保费
     *
     * @param productId
     * @param file
     * @return
     */
    public int importPremiumTable4Group(Integer productId, Integer version, SmProductDetailVO product, MultipartFile file) {
        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(file);
        Sheet sheet = workbook.getSheetAt(0);
        int numOfRows = sheet.getLastRowNum();
        /**
         * 从第二行开始读[row=1]
         */
        String planName = null;
        String dutyName = null;
        String dutyCode = null;
        List<ExcelCoveragePremiumVo> data = new ArrayList<>();
        String ocpn = product.getGlOcpnGroup();
        int size = 0;
        if (ocpn != null) {
            size = ocpn.split(",").length;
        }
        for (int i = 1; i <= numOfRows; i++) {
            Row row = sheet.getRow(i);
            String temPlan = ExcelReadUtil.getCellValue(row.getCell(0));
            String temDuty = ExcelReadUtil.getCellValue(row.getCell(2));
            String temDutyCode = ExcelReadUtil.getCellValue(row.getCell(3));
            if (org.apache.commons.lang.StringUtils.isNotBlank(temPlan)) {
                planName = temPlan;
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(temDuty)) {
                dutyName = temDuty;
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(temDutyCode)) {
                dutyCode = temDutyCode;
            }
            if (!verifyParam(planName, dutyName, dutyCode)) {
                return -1;
            }
            ExcelCoveragePremiumVo tempDto = new ExcelCoveragePremiumVo();
            tempDto.setProductId(productId);
            tempDto.setPlanName(planName);
            tempDto.setCvgCode(dutyCode);
            tempDto.setCvgName(dutyName);

            String amount = ExcelReadUtil.getCellValue(row.getCell(4));
            if (org.apache.commons.lang.StringUtils.isBlank(amount)) {
                continue;
            }
            tempDto.setCvgNotice(amount);
            for (int j = 5; j < 5 + size; j++) {
                tempDto.add(ExcelReadUtil.getCellValue(row.getCell(j)));
            }
//            tempDto.add(ExcelReadUtil.getCellValue(row.getCell(5)));
//            tempDto.add(ExcelReadUtil.getCellValue(row.getCell(6)));
//            tempDto.add(ExcelReadUtil.getCellValue(row.getCell(7)));
//            tempDto.add(ExcelReadUtil.getCellValue(row.getCell(8)));
//            tempDto.add(ExcelReadUtil.getCellValue(row.getCell(9)));
            data.add(tempDto);
        }
        if (data.size() > 0) {
            return saveExcelData(productId, version, product, data);
        }
        return -1;
    }

    /**
     * 存储Excel值
     *
     * @param data
     */
    private int saveExcelData(Integer productId, int version, SmProductDetailVO product, List<ExcelCoveragePremiumVo> data) {
        List<SmProductCoverageAmountVO> cvgAounts = historyMapper.listProductCoverageAmounts(productId, version);
        String[] ocpnGroups = product.getGlOcpnGroup().split(",");
        final String[] sortedOcpnGroups = Stream.of(ocpnGroups).sorted().toArray(String[]::new);
        List<SmProductCoveragePremiumDTO> premiumDTOS = new ArrayList<>();
        BigDecimal minAmount = BigDecimal.ZERO;
        Map<String, SmPlanVO> planMap = getPlanMap(productId, version);
        String numberReg = "^\\d+(\\.\\d*)?$";
        data.forEach(p -> {
            BigDecimal cvgMinAmount = new BigDecimal(10000000);
            Optional<SmProductCoverageAmountVO> optional = cvgAounts.stream()
                    .filter(cai -> Objects.equals(cai.getCvgItemName(), p.getCvgName()) && Objects.equals(cai.getCvgNotice(), p.getCvgNotice()))
                    .findFirst();
            boolean isMainCvg = true;
            if (optional.isPresent()) {
                SmProductCoverageAmountVO coverageAmount = optional.get();
                isMainCvg = isMainCvg && Objects.equals(coverageAmount.getCvgType(), "main");
                List<String> premiums = p.getPremiums();
                if (premiums.size() < ocpnGroups.length) {
                    throw new BizException(ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getCode(), ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getMsg() + "职业类别配置错误");
                }
                if (premiums.size() < sortedOcpnGroups.length) {
                    throw new BizException(ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getCode(), ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getMsg() + "职业类别配置错误");
                }
                int index = 0;
                SmPlanVO plan = planMap.get(p.getPlanName());
                if (plan != null) {
                    p.setPlanId(plan.getPlanId());
                }
                for (String s : sortedOcpnGroups) {
                    SmProductCoveragePremiumDTO dto = new SmProductCoveragePremiumDTO();
                    dto.setSpcId(coverageAmount.getSpcId());
                    dto.setSpcaId(coverageAmount.getSpcaId());
                    dto.setProductId(productId);
                    dto.setOccupationGroup(s);
                    dto.setPlanId(p.getPlanId());
                    dto.setPlanName(p.getPlanName());
                    String premium = premiums.get(index);

                    if (StringUtils.isNotBlank(premium) && premium.matches(numberReg)) {
                        BigDecimal pm = new BigDecimal(premium);
                        dto.setPremium(pm);
                        if (pm.compareTo(cvgMinAmount) < 0) {
                            cvgMinAmount = pm;
                        }
                    }
                    index++;
                    premiumDTOS.add(dto);
                }
            }
        });
        historyMapper.deleteProductCoveragePremium(productId, version);
        historyMapper.updateProductMinAmount(productId, minAmount, HttpRequestUtil.getUserId(), version);
        mapper.insertProductCoveragePremiumDisable(premiumDTOS);
        historyMapper.insertProductCoveragePremium(premiumDTOS, version);
        return 1;
    }

    private boolean verifyParam(String planName, String dutyName, String dutyCode) {
        return org.apache.commons.lang.StringUtils.isNotBlank(planName) &&
                org.apache.commons.lang.StringUtils.isNotBlank(dutyName);
    }

    /**
     * 导入品保障项目保费
     *
     * @param productId
     * @param file
     * @return
     */
    public int importPremiumTable(int productId, MultipartFile file) {
        int version = getProductUpdateVersion(productId);
        SmProductDetailVO product = historyMapper.getProductById(productId, version);
        if (ProductConfig.groupTemplate(product.getProductAttrCode())) {
            log.info(String.format("[团险-%s]-开始导入费率表", product.getProductName()));
            return importPremiumTable4Group(productId, version, product, file);
        } else {
            log.info(String.format("[个险-%s]-开始导入费率表", product.getProductName()));
            return importProductCoveragePremium(productId, product, file);
        }
    }

    /**
     * 导入品保障项目保费
     *
     * @param productId
     * @param file
     * @return
     */
    public int importProductCoveragePremium(int productId, SmProductDetailVO product, MultipartFile file) {
        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(file);
        Sheet sheet = workbook.getSheetAt(0);
        int numOfRows = sheet.getLastRowNum();
        int numOfColumns = sheet.getRow(1).getLastCellNum();
        boolean isNewCvg = false;
        String cvgName = null;
        Map<String, Map<String, List<String>>> cvgAmountPremiumMap = new HashMap<>();
        Map<String, List<String>> amountPremiumMap = new HashMap<>();
        for (int i = 0; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            String amountName = null;
            List<String> premiumList = new ArrayList<>();
            for (int j = 0; j < numOfColumns; j++) {
                String cellValue = ExcelReadUtil.getCellValue(row.getCell(j));
                if (j == 0) {
                    if (StringUtils.isEmpty(cellValue)) {
                        continue;
                    }
                    if (cellValue.startsWith("保障项目")) {
                        isNewCvg = true;
                        break;
                    }
                    cvgName = cellValue;
                }

                if (j == 1 && !StringUtils.isEmpty(cellValue)) {
                    if (StringUtils.isEmpty(cellValue)) {
                        break;
                    } else {
                        amountName = cellValue;
                    }
                } else if (j > 1) {
                    premiumList.add(cellValue);
                }
            }
            if (amountName != null && !premiumList.isEmpty()) {
                amountPremiumMap.put(amountName, premiumList);
            }
            if (isNewCvg || i == numOfRows - 1) {
                if (cvgName != null && !amountPremiumMap.isEmpty()) {
                    cvgAmountPremiumMap.put(cvgName, amountPremiumMap);
                }
                amountPremiumMap = new HashMap<>();
                cvgName = null;
                isNewCvg = false;
            }
        }

        int version = getProductUpdateVersion(productId);
        List<SmProductCoverageAmountVO> cvgAounts = historyMapper.listProductCoverageAmounts(productId, version);
        String[] ocpnGroups = product.getGlOcpnGroup().split(",");
        final String[] sortedOcpnGroups = Stream.of(ocpnGroups).sorted().toArray(String[]::new);

        historyMapper.deleteProductCoveragePremium(productId, version);
        List<SmProductCoveragePremiumDTO> premiumDTOS = new ArrayList<>();
        BigDecimal minAmount = BigDecimal.ZERO;
        for (Map.Entry<String, Map<String, List<String>>> entry : cvgAmountPremiumMap.entrySet()) {
            String cvg = entry.getKey();
            Map<String, List<String>> apMap = entry.getValue();
            BigDecimal cvgMinAmount = new BigDecimal(10000000);
            boolean isMainCvg = true;
            for (Map.Entry<String, List<String>> pEntry : apMap.entrySet()) {
                String amount = pEntry.getKey();
                Optional<SmProductCoverageAmountVO> optional = cvgAounts.stream()
                        .filter(cai -> Objects.equals(cai.getCvgItemName(), cvg) && Objects.equals(cai.getCvgNotice(), amount))
                        .findFirst();
                if (optional.isPresent()) {
                    SmProductCoverageAmountVO coverageAmount = optional.get();
                    isMainCvg = isMainCvg && Objects.equals(coverageAmount.getCvgType(), "main");
                    List<String> premiums = pEntry.getValue();
                    if (premiums.size() != ocpnGroups.length) {
                        throw new BizException(ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getCode(), ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getMsg() + "职业类别配置错误");
                    }
                    for (int i = 0, len = premiums.size(); i < len; i++) {
                        SmProductCoveragePremiumDTO dto = new SmProductCoveragePremiumDTO();
                        dto.setSpcId(coverageAmount.getSpcId());
                        dto.setSpcaId(coverageAmount.getSpcaId());
                        dto.setProductId(productId);
                        dto.setOccupationGroup(sortedOcpnGroups[i]);
                        String premium = premiums.get(i);
                        if (!StringUtils.isEmpty(premium) && !Objects.equals("-", premium.trim())) {
                            BigDecimal pm = new BigDecimal(premium);
                            dto.setPremium(pm);
                            if (pm.compareTo(cvgMinAmount) < 0) {
                                cvgMinAmount = pm;
                            }
                        }
                        premiumDTOS.add(dto);
                    }
                } else {
                    throw new BizException(ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getCode(), ExcptEnum.PRODUCT_TEMPLATE_ERROR_801041.getMsg() + "," + cvg + "保额" + amount + "没有找到");
                }
            }
            // 最低金额取主险最低金额
            if (isMainCvg) {
                minAmount = minAmount.add(cvgMinAmount);
            }
        }
        historyMapper.updateProductMinAmount(productId, minAmount, HttpRequestUtil.getUserId(), version);
        historyMapper.insertProductCoveragePremium(premiumDTOS, version);
        return premiumDTOS.size();
    }

    /**
     * 查询团险产品保费折扣比例
     *
     * @param productId
     * @return
     */
    public List<SmProductCoverageDiscountVO> getProductCoverageDiscountList(int productId) {
        return mapper.listProductCoverageDiscounts(productId);
    }

    /**
     * 保存团险产品保费折扣比例
     *
     * @param productId
     * @param dtos
     */
    public void saveProductCoverageDiscount(int productId, List<SmProductCoverageDiscountDTO> dtos) {
        final int version = getProductUpdateVersion(productId);
        dtos.forEach(d -> {
            d.setProductId(productId);
            if (d.getSpcdId() != null && d.getSpcdId() > 0) {
                historyMapper.updateProductCoverageDiscount(d, version);
//                mapper.updateProductCoverageDiscount(d);
            } else {
                mapper.insertProductCoverageDiscountsDisable(d);
                historyMapper.insertProductCoverageDiscounts(d, version);
            }
        });
    }

    /**
     * 保费浮动
     *
     * @param productId
     * @param vo
     */
    public void savePremiumFactor(int productId, PremiumFlowFactorReqVo vo) {
        final int version = getProductUpdateVersion(productId);
        String uid = HttpRequestUtil.getUserId();

        updateApplyQtyFlow(productId, version, vo.getInsuredQtyF());

        updateDutyFactorFlow(productId, version, uid, vo.getDutyFactor());

        updatePremiumFlow(productId,
                version,
                EnumPremiumFlow.SHORT_TERM_MONTH_FACTOR,
                EnumProductAttr.PREMIUM_SHORT_TERM_ACC,
                vo.getTimeFactor());

        updatePremiumFlow(productId,
                version, EnumPremiumFlow.ENTERPRISE_RISK_FACTOR,
                EnumProductAttr.PREMIUM_ENTERPRISE_RISK_ACC,
                vo.getEnterpriseRiskFactor());
    }

    /**
     * 短期险浮动因子
     *
     * @param productId
     * @param version
     * @param ctf
     */
    private void updatePremiumFlow(int productId,
                                   int version,
                                   EnumPremiumFlow type,
                                   EnumProductAttr attr,
                                   SmProductCoverageDiscountWrap<PremiumFlow> ctf) {
        String uid = HttpRequestUtil.getUserId();
        mapper.deletePremiumFlow(productId, type.name());
        if (ctf != null && !ctf.isEmpty()) {
            String acc = ctf.getAccuracy();
            if (acc != null) {
                historyMapper.deleteAttr(productId, attr.getCode(), version);
                historyMapper.insertProductAttr(productId, attr.getCode(), acc, version);
            }
            ctf.getItems().forEach(d -> {
                d.setOperator(uid);
                d.setProductId(productId);
                d.setType(type.name());
                d.setEnabledFlag(0);
            });
            mapper.insertPremiumFlow(ctf.getItems(), version);
        }
    }

    @Autowired
    private SmProductDutyFactorMapper dutyFactorMapper;

    /**
     * 投保人数-浮动比例
     *
     * @param productId
     * @param version
     * @param cdv
     */
    private void updateDutyFactorFlow(int productId,
                                      int version,
                                      String uid,
                                      SmProductCoverageDiscountWrap<DutyFactorDTO> cdv) {
        historyMapper.deleteDutyFactor(productId, version);
        String acc = cdv.getAccuracy();
        if (acc != null) {
            historyMapper.deleteAttr(productId, EnumProductAttr.PREMIUM_DUTY_FACTOR_ACC.getCode(), version);
            historyMapper.insertProductAttr(productId, EnumProductAttr.PREMIUM_DUTY_FACTOR_ACC.getCode(), acc, version);
        }
        cdv.getItems().forEach(d -> {
            d.setOperator(uid);
            d.setProductId(productId);
            if (d.getId() == null) {
                d.setStatus(-1);
                dutyFactorMapper.insertDutyFactor(d);
            }
            historyMapper.insertDutyFactor(d, version);
        });
    }

    private Integer genDutyFactorId(DutyFactorDTO data) {
        ProductDutyFactor entity = new ProductDutyFactor();
        BeanUtils.copyProperties(data, entity);
        entity.setStatus(-1);
        dutyFactorMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * 投保人数-浮动比例
     *
     * @param productId
     * @param version
     * @param cif
     */
    private void updateApplyQtyFlow(int productId, int version, SmProductCoverageDiscountWrap<SmProductCoverageDiscountVO> cif) {
        historyMapper.deleteProductCoverageAllDiscount(productId, version);
        if (cif != null && !cif.isEmpty()) {
            String acc = cif.getAccuracy();
            if (acc != null) {
                historyMapper.deleteAttr(productId, EnumProductAttr.PREMIUM_PEOPLE_FACTOR_ACC.getCode(), version);
                historyMapper.insertProductAttr(productId, EnumProductAttr.PREMIUM_PEOPLE_FACTOR_ACC.getCode(), acc, version);
            }
            cif.getItems().forEach(d -> {
                d.setProductId(productId);
                if (d.getSpcdId() == null) {
                    mapper.insertProductCoverageDiscountsDisable(d);
                }
                historyMapper.insertProductCoverageDiscounts(d, version);
            });
        }
    }


    /**
     * 删除团险产品保费折扣比例
     *
     * @param spcdId
     * @return
     */
    public void deleteProductCoverageDiscount(int productId, int spcdId) {

        historyMapper.deleteProductCoverageDiscount(spcdId, getProductUpdateVersion(productId));
    }

    /**
     * 保存团险产品报价限制
     *
     * @param productId
     * @param dto
     */
    public void saveProductQuoteLimit(int productId, SmProductQuoteLimitDTO dto) {

        int version = getProductUpdateVersion(productId);
        historyMapper.deleteAllProductQuoteLimit(productId, version);

        SmProductQuoteLimitItemDTO perItemDTO = new SmProductQuoteLimitItemDTO();
        perItemDTO.setSpqlId(dto.getSpqlId());
        perItemDTO.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_PER);
        perItemDTO.setProductId(productId);
        perItemDTO.setMinPerQty(dto.getGlPerStartQty());
        mapper.insertProductQuoteLimitDisable(perItemDTO);
        historyMapper.insertProductQuoteLimit(perItemDTO, version);
        if (dto.getOcpnPerLimit() != null) {
            dto.getOcpnPerLimit().forEach(c -> {
                SmProductQuoteLimitItemDTO itemDTO = mapperToProductQuoteLimitItem(c);
                itemDTO.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER);
                itemDTO.setProductId(productId);
                mapper.insertProductQuoteLimitDisable(itemDTO);
                historyMapper.insertProductQuoteLimit(itemDTO, version);
            });
        }

        if (dto.getCvgRelationLimits() != null) {
            dto.getCvgRelationLimits().forEach(c -> {
                SmProductQuoteLimitItemDTO itemDTO = mapperToProductQuoteLimitItem(c);
                itemDTO.setProductId(productId);
                itemDTO.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY);
                mapper.insertProductQuoteLimitDisable(itemDTO);
                historyMapper.insertProductQuoteLimit(itemDTO, version);
            });
        }

        if (dto.getCvgAmountLimits() != null) {
            dto.getCvgAmountLimits().forEach(c -> {
                SmProductQuoteLimitItemDTO itemDTO = mapperToProductQuoteLimitItem(c);
                itemDTO.setProductId(productId);
                itemDTO.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT);
                mapper.insertProductQuoteLimitDisable(itemDTO);
                historyMapper.insertProductQuoteLimit(itemDTO, version);
            });
        }

        if (dto.getOcp4LiabAmountLimits() != null) {
            dto.getOcp4LiabAmountLimits().forEach(c -> {
                SmProductQuoteLimitItemDTO itemDTO = mapperToProductQuoteLimitItem(c);
                itemDTO.setProductId(productId);
                itemDTO.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT_2);

                mapper.insertProductQuoteLimitDisable(itemDTO);
                historyMapper.insertProductQuoteLimit(itemDTO, version);
            });
        }

        if (dto.getCvgSupportTimeLimits() != null) {
            dto.getCvgSupportTimeLimits().forEach(c -> {
                SmProductQuoteLimitItemDTO itemDTO = mapperToProductQuoteLimitItem(c);
                itemDTO.setProductId(productId);
                itemDTO.setLimitType(SmProductQuoteLimitItemVO.LIMIT_TYPE_TIME);
                if (c.getSpqlId() == null) {
                    mapper.insertProductQuoteLimitDisable(itemDTO);
                }
                historyMapper.insertProductQuoteLimit(itemDTO, version);
            });
        }
    }

    /**
     * 删除团险产品报价限制
     *
     * @param spqlId
     */
    public void deleteProductQuoteLimit(int productId, int spqlId) {
        historyMapper.deleteProductQuoteLimit(spqlId, getProductUpdateVersion(productId));
    }

    /**
     * 查询团险报价记录列表
     *
     * @param query
     * @return
     */
    public PageInfo<SmGlProductQuoteListVO> getGlProductQuotes(SmGlProductQuoteQuery query) {
        if (query.isQueryPage()) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        query.setQuoteNo(CommonUtil.trim(query.getQuoteNo()));
        query.setUserName(CommonUtil.trim(query.getUserName()));
        query.setCustomerName(CommonUtil.trim(query.getCustomerName()));
        query.setAgentName(CommonUtil.trim(query.getAgentName()));
        query.setStartDate(CommonUtil.getStartTimeOfDay(query.getStartDate()));
        query.setEndDate(CommonUtil.getEndTimeOfDay(query.getEndDate()));
        return new PageInfo<>(mapper.listGlProductQuotes(query));
    }

    /**
     * 下载团险报价记录列表
     *
     * @param query
     * @param resp
     */
    public void downloadSmEmployeeSalesSmy(SmGlProductQuoteQuery query, HttpServletResponse resp) {
        Class clazz = SmGlProductQuoteListVO.class;
        query.setQueryPage(false);
        PageInfo<SmGlProductQuoteListVO> smyPageInfo = getGlProductQuotes(query);
        List<SmGlProductQuoteListVO> quoteList = smyPageInfo.getList();
        String fileName = "团险报价记录列表";
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
            resp.setContentType("application/octet-stream");
            ExcelBuilderUtil.newInstance()
                    .createSheet("团险报价记录列表")
                    .buildSheetHead(clazz)
                    .addSheetData(quoteList)
                    .write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    /**
     * 查询团险产品报价限制
     *
     * @param productId
     * @return
     */
    public SmProductQuoteLimitVO getProductQuoteLimit(int productId) {
        List<SmProductQuoteLimitItemVO> quoteLimits = mapper.listProductQuoteLimit(productId, null);
        SmProductQuoteLimitVO quoteLimitVO = new SmProductQuoteLimitVO();
        quoteLimitVO.init();

        quoteLimits.forEach(q -> {
            if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCPN_PER)) {
                SmProductQuoteLimitDTO.OcpnPerLimit limit = new SmProductQuoteLimitDTO.OcpnPerLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getOcpnPerLimit().add(limit);
            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_CVG_RELY)) {
                SmProductQuoteLimitDTO.CvgRelationLimit limit = new SmProductQuoteLimitDTO.CvgRelationLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getCvgRelationLimits().add(limit);
            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT)) {
                SmProductQuoteLimitDTO.CvgAmountLimit limit = new SmProductQuoteLimitDTO.CvgAmountLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getCvgAmountLimits().add(limit);
            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_OCVG_AMOUNT_2)) {
                SmProductQuoteLimitDTO.Ocp4LiabAmountLimit limit = new SmProductQuoteLimitDTO.Ocp4LiabAmountLimit();
                BeanUtils.copyProperties(q, limit);
                quoteLimitVO.getOcp4LiabAmountLimits().add(limit);

            } else if (Objects.equals(q.getLimitType(), SmProductQuoteLimitItemVO.LIMIT_TYPE_PER)) {
                quoteLimitVO.setSpqlId(q.getSpqlId());
                quoteLimitVO.setGlPerStartQty(q.getMinPerQty());
            }
        });
        return quoteLimitVO;
    }


    /**
     * 保存客户告知书配置
     *
     * @param productId
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = PRODUCT_DETAIL, allEntries = true)
    public void saveProductCustNotify(int productId, SmProductNotify dto) {

        int version = getProductUpdateVersion(productId);
        dto.setTakeTime(LocalDateTime.now());
        SmProductCustNotifyDTO updatePro = new SmProductCustNotifyDTO();
        updatePro.setId(productId);
        updatePro.setCustNotify(dto.getCustNotify());
        if (historyMapper.updateProductCustNotify(updatePro, version) != 1) {
            throw new BizException(ExcptEnum.PARAMS_ERROR);
        }

        SmProductNotify smProductNotify = notifyMapper.selectByProductId(updatePro.getId());
        if (smProductNotify == null) {
            notifyMapper.insertUseGeneratedKeys(dto);
            historyMapper.insertVersionProductNotify(productId, version);
        } else {
            dto.setId(smProductNotify.getId());
            dto.setProductId(productId);
//            notifyMapper.updateByPrimaryKeySelective(dto);
            historyMapper.updateProductCustNotifyDetail(dto, version);
        }
    }

    public SmProductNotify getProductCustNotify(int productId) {
        return notifyMapper.selectByProductId(productId);
    }

    /**
     * newSmProductCoverageAmountItem
     *
     * @param productId
     * @param cv
     * @return
     */
    private SmProductCoverageAmountItemVO newSmProductCoverageAmountItem(int productId, SmProductCoverageVO cv) {
        SmProductCoverageAmountItemVO item = new SmProductCoverageAmountItemVO();
        item.setCoverageAmounts(new ArrayList<>());
        item.setProductId(productId);
        item.setCvgItemName(cv.getCvgItemName());
        item.setCvgNameDetail(cv.getCvgNameDetail());
        item.setCvgType(cv.getCvgType());
        item.setSpcId(cv.getSpcId());
        return item;
    }

    /**
     * mapperToProductQuoteLimitItem
     *
     * @param obj
     * @return
     */
    private SmProductQuoteLimitItemDTO mapperToProductQuoteLimitItem(Object obj) {
        SmProductQuoteLimitItemDTO dto = new SmProductQuoteLimitItemDTO();
        BeanUtils.copyProperties(obj, dto);
        return dto;
    }

    /**
     * 查询产品保险价格某个选项
     *
     * @param optionals
     * @param fieldCode
     * @return
     */
    private List<SmFactorOptionalDTO> getOptionalFieldDto(List<SmFactorOptionalDTO> optionals, String fieldCode) {
        return optionals.stream().filter(o -> Objects.equals(o.getFieldCode(), fieldCode)).collect(toList());
    }

    /**
     * 设置修改人信息
     *
     * @param dto
     */
    private void setModifyBy(SmProductDTO dto) {
        dto.setModifyBy(HttpRequestUtil.getUserId());
    }

    /**
     * 加粗字体单元格
     *
     * @param row
     * @param i
     * @param cellStyle
     * @return
     */
    private Cell createCell(Row row, int i, CellStyle cellStyle) {
        Cell cell = row.createCell(i);
        cell.setCellStyle(cellStyle);
        return cell;
    }

    /**
     * 保存 自主确认条款
     *
     * @param productId
     * @param confirms
     */
    public void saveClauseConfirm(int productId, List<SmProductConfirm> confirms) {

        int realVersion = getProductUpdateVersion(productId);
        confirmMapper.deleteHistory(productId, realVersion);
        if (!CollectionUtils.isEmpty(confirms)) {
            Map<Boolean, List<SmProductConfirm>> addMap = LambdaUtils.partitioningBy(confirms, a -> Objects.isNull(a.getId()));
            List<SmProductConfirm> addList = addMap.get(Boolean.TRUE);
            if (!CollectionUtils.isEmpty(addList)) {
                addList.forEach(con -> con.setEnabledFlag(SmConstants.MODEL_DISABLE));
                int i = confirmMapper.insertList(addList);
                confirmMapper.insertHisList(addList, realVersion);
            }
            List<SmProductConfirm> updateList = addMap.get(Boolean.FALSE);
            if (!CollectionUtils.isEmpty(updateList)) {
                for (SmProductConfirm updateModel : updateList) {
                    confirmMapper.updateHis(updateModel, realVersion);
                }
            }
        }
    }


    /**
     * 获取单个产品的扩展属性
     *
     * @param productId
     * @return
     */
    public Map<String, String> getProductAttr(Integer productId) {

        SmProductAttr param = new SmProductAttr();
        param.setProductId(productId);
        return LambdaUtils.toMap(productAttrMapper.select(param), SmProductAttr::getAttrCode, SmProductAttr::getAttrVal);
    }

    /**
     * 获取产品列表的扩展属性
     *
     * @param productIds
     * @return
     */
    public Map<Integer, Map<String, String>> listProductAttr(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyMap();
        }
        Example example = new Example(SmProductAttr.class);
        example.createCriteria().andIn("productId", productIds);
        Map<Integer, List<SmProductAttr>> proMap = LambdaUtils.groupBy(productAttrMapper.selectByExample(example), SmProductAttr::getProductId);

        //转换成产品对象
        List<SmProductDetailVO> collect = proMap.entrySet().stream().map(en -> {
            Map<String, String> map = LambdaUtils.toMap(en.getValue(), SmProductAttr::getAttrCode, SmProductAttr::getAttrVal);
            SmProductDetailVO smProductDetailVO = new SmProductDetailVO();
            smProductDetailVO.setId(en.getKey());
            smProductDetailVO.setAttrs(map);
            return smProductDetailVO;
        }).collect(toList());
        //转换map方便外部是用哪个
        return LambdaUtils.toMap(collect, SmProductDetailVO::getId, SmProductDetailVO::getAttrs);
    }

    /**
     * 保存批改配置
     *
     * @param vo
     * @return
     */
    public Integer saveEndorConfig(ProductEndorVo vo) {
        SmProductEndor endor = new SmProductEndor();
        BeanCopier copier = BeanCopier.create(ProductEndorVo.class, SmProductEndor.class, false);
        copier.copy(vo, endor, null);
        endor.setOperator(HttpRequestUtil.getUserId());
        if (org.apache.commons.lang3.ObjectUtils.equals(vo.getAdditionEffectiveType(), 0)) {
            endor.setMinAdditionEffectiveTime(vo.getAdditionEffectiveTime());
            endor.setMaxAdditionEffectiveTime(vo.getAdditionEffectiveTime());
        }
        if (org.apache.commons.lang3.ObjectUtils.equals(vo.getReductionEffectiveType(), 0)) {
            endor.setMinReductionEffectiveTime(vo.getReductionEffectiveTime());
            endor.setMaxReductionEffectiveTime(vo.getReductionEffectiveTime());
        }
        SmProductEndor record = endorMapper.queryById(vo.getProductId());
        if (record != null) {
            return endorMapper.updateByPrimaryKeySelective(endor);
        } else {
            return endorMapper.insertSelective(endor);
        }
    }

    public ProductEndorVo getEndorConfig(int productId) {
        SmProductEndor endor = endorMapper.queryById(productId);
        if (endor != null) {
            ProductEndorVo vo = new ProductEndorVo();
            BeanCopier copier = BeanCopier.create(SmProductEndor.class, ProductEndorVo.class, false);
            copier.copy(endor, vo, null);
            if (org.apache.commons.lang3.ObjectUtils.equals(vo.getAdditionEffectiveType(), 0)) {
                vo.setAdditionEffectiveTime(vo.getMinAdditionEffectiveTime());
            }
            if (org.apache.commons.lang3.ObjectUtils.equals(vo.getReductionEffectiveType(), 0)) {
                vo.setReductionEffectiveTime(vo.getMinReductionEffectiveTime());
            }
            return vo;
        }
        return null;
    }

    public List<Integer> queryActiveProductIds() {
        return mapper.queryActiveProduct();
    }

    @CacheEvict(value = PRODUCT_LIST, allEntries = true)
    public void saveProductLabel(ProductLabelDTO productLabelDTO) {
        if (CollectionUtils.isEmpty(productLabelDTO.getLabelList())) {
            return;
        }
        String userId = HttpRequestUtil.getUserId();
        List<SmProductLabel> productLabelList = Lists.newArrayList();
        for (ProductLabelDTO.LabelDTO dto : productLabelDTO.getLabelList()) {
            String typeDesc = EnumProductLabelType.dict(dto.getLabelType());
            //标签类型不存在的不处理
            if (StringUtils.isBlank(typeDesc)) {
                continue;
            }
            /*//值为空的也不处理
            if(StringUtils.isBlank(dto.getLabelValue())){
                continue;
            }*/

            SmProductLabel label = new SmProductLabel();
            label.setProductId(productLabelDTO.getProductId());
            label.setLabelType(dto.getLabelType());
            label.setLabelTypeDesc(typeDesc);
            label.setLabelValue(dto.getLabelValue());
            label.setLabelDesc(dto.getLabelDesc());
            label.setCreateBy(userId);
            label.setUpdateBy(userId);
            productLabelList.add(label);
        }
        smProductLabelMapper.batchInsertProductLabel(productLabelList);
        Integer productId = productLabelDTO.getProductId();
        mpNotifyFacade.productChange(String.valueOf(productId));
    }

    @Deprecated
    /**
     * 该方法以前迁移至ProductQueryService
     */
    public ProductLabelDTO getProductLabels(Integer productId) {
        if (Objects.isNull(productId)) {
            return null;
        }
        List<SmProductLabel> labelList = smProductLabelMapper.listByProductId(productId);
        if (CollectionUtils.isEmpty(labelList)) {
            return null;
        }
        ProductLabelDTO labelDTO = new ProductLabelDTO();
        List<ProductLabelDTO.LabelDTO> dtoList = Lists.newArrayList();
        for (SmProductLabel label : labelList) {
            ProductLabelDTO.LabelDTO dto = new ProductLabelDTO.LabelDTO();
            dto.setLabelType(label.getLabelType());
            dto.setLabelValue(label.getLabelValue());
            dto.setLabelDesc(label.getLabelDesc());
            dtoList.add(dto);
        }
        labelDTO.setLabelList(dtoList);
        return labelDTO;
    }

    @Deprecated
    /**
     * 该方法以前迁移至ProductQueryService
     */
    public List<Integer> listProductIdByLabelTypeValue(String labelType, String labelValue) {
        if (StringUtils.isBlank(labelType)) {
            return Collections.emptyList();
        }
        List<SmProductLabel> labelList = smProductLabelMapper.listByLabelType(labelType, labelValue);
        if (CollectionUtils.isEmpty(labelList)) {
            return Collections.emptyList();
        }
        return labelList.stream().map(SmProductLabel::getProductId).collect(Collectors.toList());
    }

    /**
     * 复制产品
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean copyProduct(int productId, String userId) {
        int version = historyService.defaultIfAbsent(productId, null);
        SmProductDetailVO productById = historyService.getProductById(productId, version);

        // 基础信息复制
        BasicProductVo temp = new BasicProductVo();
        BeanUtils.copyProperties(productById, temp);
        temp.setId(null);
        temp.setProductId(null);
        temp.setCompanyId(productById.getCompanyId() + "");
        temp.setCreateType(EnumProductCreateType.valueOf(productById.getCreateType()));
        temp.setProductCode(productById.getProductCode() + "（复制）");
        temp.setProductName(productById.getProductName() + "（复制）");
//        temp.setProductShortName(temp.getProductCode() + "（复制）");
        int newProductId = saveProductBaseInfo(temp);

        if (productById.isLongType()) {
            copyLongIns(productId, newProductId, version);
        } else if (productById.isShotType()) {
            copyOldIns(productId, newProductId, version);
        } else if (productById.isGroupType()) {
            copyGroupIns(productId, newProductId, version);
        } else {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "不支持传模板:" + productById.getCreateType());
        }

        //
        String notice = productById.getGlProductNotice();
        SmProductDTO dto = new SmProductDTO();
        dto.setProductId(newProductId);
        dto.setGlProductNotice(notice);
        saveProductNotice(dto);


        SmProductAttentionDTO attention = new SmProductAttentionDTO();
        attention.setModifyBy(userId);
        attention.setProductId(newProductId);
        attention.setAttentions(productById.getAttentions());
        saveProductAttention(attention);


        SmProductClauseMasterVO clause = historyService.getProductClause(productId, version);
        SmProductClauseMasterDTO masterDTO = new SmProductClauseMasterDTO();
        masterDTO.setContent(clause.getContent());
        List<SmProductClauseDTO> dtos = clause.getClauses().stream().map(cla -> {
            SmProductClauseDTO newCla = new SmProductClauseDTO();
            BeanUtils.copyProperties(cla, newCla);
            newCla.setProductId(newProductId);
            return newCla;
        }).collect(toList());
        masterDTO.setClauses(dtos);
        saveProductClause(newProductId, masterDTO, userId);

        // 自主确认
        List<SmProductConfirmHistory> confirm = historyService.getClauseConfirm(productId, version);
        List<SmProductConfirm> confirms = confirm.stream().map(his -> {
            SmProductConfirm con = new SmProductConfirm();
            BeanUtils.copyProperties(his, con);
            con.setProductId(newProductId);
            con.setId(null);
            return con;
        }).collect(toList());
        saveClauseConfirm(newProductId, confirms);

        // 投保录入
        List<SmProductFormFieldCombDTO> productFormFields = historyService.getProductFormFields(productId, version);
        for (SmProductFormFieldCombDTO formField : productFormFields) {
            for (SmProductFormFieldDTO tempForm : formField.getList()) {
                tempForm.setProductId(newProductId);
            }
        }
        saveProductInsureForm(productFormFields);
        return true;
    }

    private void copyLongIns(int oldProductId, int newProductId, int version) {

        //险种
        List<SmProductRiskVO> risk = riskService.getProductRisk(oldProductId);
        List<SmProductRiskForm> riskForms = risk.stream().map(ri -> {
            SmProductRiskForm form = new SmProductRiskForm();
            BeanUtils.copyProperties(ri, form);
            form.setRiskVersion(ri.getVersion());
            return form;
        }).collect(toList());
        riskService.save(newProductId, riskForms);

        //计划
        List<SmLongInsurancePlanVo> list = longInsurancePlanService.getPlanList(oldProductId);
        for (SmLongInsurancePlanVo oldPlan : list) {

            List<SmPlanRiskVO> planRiskList = oldPlan.getPlanRiskList();
            SmPlanForm planForm = new SmPlanForm();
            BeanUtils.copyProperties(oldPlan, planForm);
            List<SmPlanRiskForm> newRisks = planRiskList.stream().map(oldRisk -> {
                SmPlanRiskForm newRisk = new SmPlanRiskForm();
                BeanUtils.copyProperties(oldRisk, newRisk);
                List<SmPlanRiskDutyForm> dutyForms = oldRisk.getPlanRiskDutyList().stream()
                        .map(oldDuty -> {
                            SmPlanRiskDutyForm dutyForm = new SmPlanRiskDutyForm();
                            BeanUtils.copyProperties(oldDuty, dutyForm);
                            List<Long> amts = oldDuty.getRiskDutyAmountIdList()
                                    .stream().map(Long::valueOf).collect(toList());
                            dutyForm.setRiskDutyAmountIdList(amts);
                            return dutyForm;
                        }).collect(toList());
                newRisk.setPlanRiskDutyList(dutyForms);
                return newRisk;
            }).collect(toList());
            planForm.setPlanCode(oldPlan.getPlanCode() + "（复制）");
            planForm.setPlanRiskList(newRisks);
            longInsurancePlanService.save(newProductId, planForm);
        }

        // 健康告知
        List<SmProductHealthInformVo> healths = smProductHealthInformService.detail(oldProductId, version);
        for (SmProductHealthInformVo health : healths) {
            SmProductHealthInformForm form = new SmProductHealthInformForm();
            BeanUtils.copyProperties(health, form);
            form.setInformObject(EnumInformObject.valueOf(health.getInformObject()));
            smProductHealthInformService.save(newProductId, form);
        }


    }

    /**
     * chang xian
     * changxianram oldProductId
     *
     * @param newProductId
     */
    private void copyOldIns(int oldProductId, int newProductId, int version) {
        log.info("开始复制老模板定制内容{}:{}", oldProductId, newProductId);
        // 产品计划
        List<SmPlanVO> productPlans = historyService.getProductPlans(oldProductId, version);
        List<SmPlanDTO> dtos = productPlans.stream().map(vo -> {
            SmPlanDTO dto = new SmPlanDTO();
            BeanUtils.copyProperties(vo, dto);
            dto.setFhProductId(dto.getFhProductId() + "（复制）");
            dto.setId(null);
            dto.setProductId(newProductId);
            return dto;
        }).collect(toList());
        // 保存产品计划
        if (!CollectionUtils.isEmpty(dtos)) {
            saveProductPlan(newProductId, dtos);
        }
        List<SmPlanVO> newPlans = historyService.getProductPlans(newProductId, null);

        Map<Integer, Integer> planIdMap = Maps.newHashMapWithExpectedSize(newPlans.size());
        for (int i = 0; i < newPlans.size(); i++) {
            planIdMap.put(productPlans.get(i).getPlanId(), newPlans.get(i).getPlanId());
        }

        //保障项目
        List<SmProductCoverageVO> coverageList = historyService.getProductCoverageList(oldProductId, version);
        List<SmProductCoverageDTO> coverages = coverageList.stream()
                .map(vo -> {
                    SmProductCoverageDTO res = new SmProductCoverageDTO();
                    BeanUtils.copyProperties(vo, res);
                    res.setSpcId(null);
                    res.setProductId(newProductId);
                    res.setPlanId(planIdMap.get(res.getPlanId()));
                    return res;
                })
                .collect(toList());
        if (!CollectionUtils.isEmpty(coverages)) {
            saveProductCoverage(newProductId, coverages);
        }

        List<SmProductCoverageVO> newCovs = historyService.getProductCoverageList(newProductId, null);
        Map<Integer, Integer> oldNewMap = new HashMap<>();
        for (int i = 0; i < coverageList.size(); i++) {
            SmProductCoverageVO oldCov = coverageList.get(i);
            SmProductCoverageVO newCov = newCovs.get(i);
            oldNewMap.put(oldCov.getSpcId(), newCov.getSpcId());
        }
        List<SmProductCoverageAmountItemVO> amountList = historyService.getProductCoverageAmountList(oldProductId, version);
        if (!CollectionUtils.isEmpty(amountList)) {
            //保障项目以及保额
            List<SmProductCoverageAmountItemDTO> itemDTOS = amountList.stream()
                    .map(vo -> {
                        SmProductCoverageAmountItemDTO dto = new SmProductCoverageAmountItemDTO();
                        BeanUtils.copyProperties(vo, dto);

                        dto.setSpcId(oldNewMap.get(vo.getSpcId()));
                        dto.setProductId(newProductId);
                        List<SmProductCoverageAmountVO> oldAmts = vo.getCoverageAmounts();
                        List<SmProductCoverageAmountDTO> amtDtos = oldAmts.stream()
                                .map(amtVo -> {
                                    SmProductCoverageAmountDTO amtDto = new SmProductCoverageAmountDTO();
                                    BeanUtils.copyProperties(amtVo, amtDto);
                                    amtDto.setSpcId(oldNewMap.get(amtDto.getSpcId()));
                                    amtDto.setProductId(newProductId);
                                    amtDto.setSpcaId(null);
                                    if (Objects.nonNull(amtVo.getCvgAmount())) {
                                        amtDto.setCvgAmount(amtVo.getCvgAmount().toString());
                                    }
                                    amtDto.setPlanId(planIdMap.get(amtDto.getPlanId()));
                                    return amtDto;
                                }).collect(toList());
                        dto.setCoverageAmounts(amtDtos);

                        return dto;
                    }).collect(toList());

            saveProductCoverageAmount(newProductId, itemDTOS);
        }
        ///  费率表
        List<String> factor = historyService.getProductPriceFactor(oldProductId, version);
        // 费率因子
        if (!CollectionUtils.isEmpty(factor)) {
            saveProductPriceFactor(newProductId, factor);
        }
        List<SmFactorOptionalDTO> oldOpList = historyService.getProductPriceFactorOptionalsList(oldProductId, version);
        List<SmFactorOptionalDTO> saveOps = oldOpList.stream().map(pri -> {
            SmFactorOptionalDTO dto = new SmFactorOptionalDTO();
            BeanUtils.copyProperties(pri, dto);
            dto.setProductId(newProductId);
            dto.setId(null);
            return dto;
        }).collect(toList());
        // 费率选项
        if (!CollectionUtils.isEmpty(oldOpList)) {
            saveProductFactorOptionals(saveOps);
        }

        List<SmFactorOptionalDTO> newOpList = historyService.getProductPriceFactorOptionalsList(newProductId, null);
        Map<Integer, Integer> opIdMap = Maps.newHashMapWithExpectedSize(newOpList.size());
        for (int i = 0; i < newOpList.size(); i++) {
            opIdMap.put(oldOpList.get(i).getId(), newOpList.get(i).getId());
        }

        //风险告知书
        SmProductNotify notify = historyService.getProductCustNotify(oldProductId, version);
        if (Objects.nonNull(notify)) {
            notify.setId(null);
            notify.setProductId(newProductId);
            saveProductCustNotify(newProductId, notify);
        }
        //健康告知书
        try {
            SmProductNotificationDTO notification = historyService.getProductHealthNotification(oldProductId, version);
            if (Objects.nonNull(notification)) {
                notification.setProductId(newProductId);
                saveProductHealthNotification(notification);
            }
        } catch (Exception e) {
            log.warn("复制健康告知失败", e);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "复制健康告知失败！");
        }

        //费率表
        List<SmPlanFactorPriceDT> prices = historyService.getPlanFactorPrice(oldProductId, version);

        if (!CollectionUtils.isEmpty(prices)) {
            List<SmOptionalFieldPriceDTO> priceDTOS = prices.stream().map(dt -> {
                SmOptionalFieldPriceDTO price = new SmOptionalFieldPriceDTO();
                BeanUtils.copyProperties(dt, price);
                price.setId(null);
                price.setPlanId(planIdMap.get(price.getPlanId()));
                price.setProductId(newProductId);
                price.setJapanMedicalTreatmentOptional(opIdMap.get(price.getJapanMedicalTreatmentOptional()));
                price.setUnderWritingAgeOptional(opIdMap.get(price.getUnderWritingAgeOptional()));
                price.setSexOptional(opIdMap.get(price.getSexOptional()));
                price.setSocialSecurityOptional(opIdMap.get(price.getSocialSecurityOptional()));
                price.setSmokeOptional(opIdMap.get(price.getSmokeOptional()));
                price.setOccuplCategoryOptional(opIdMap.get(price.getOccuplCategoryOptional()));
                price.setProtonHeavyIonMedicineOptional(opIdMap.get(price.getProtonHeavyIonMedicineOptional()));
                price.setSpecifiedDiseaseSpecificCareOptional(opIdMap.get(price.getSpecifiedDiseaseSpecificCareOptional()));
                price.setValidPeriodOptional(opIdMap.get(price.getValidPeriodOptional()));
                price.setVehicleSeatNumberOptional(opIdMap.get(price.getVehicleSeatNumberOptional()));
                return price;
            }).collect(toList());
            savePlanFactorPrice(priceDTOS);
        }
    }

    private void copyGroupIns(int oldProductId, int newProductId, int version) {
        log.info("开始复制团险定制内容{}:{}", oldProductId, newProductId);
        // 产品计划
        List<SmPlanVO> productPlans = historyService.getProductPlans(oldProductId, version);
        List<SmPlanDTO> dtos = productPlans.stream().map(vo -> {
            SmPlanDTO dto = new SmPlanDTO();
            BeanUtils.copyProperties(vo, dto);
            dto.setFhProductId(dto.getFhProductId() + "（复制）");
            dto.setId(null);
            dto.setProductId(newProductId);
            return dto;
        }).collect(toList());
        // 保存产品计划
        saveProductPlan(newProductId, dtos);
        List<SmPlanVO> newPlans = historyService.getProductPlans(newProductId, null);

        Map<Integer, Integer> planIdMap = Maps.newHashMapWithExpectedSize(newPlans.size());
        for (int i = 0; i < newPlans.size(); i++) {
            planIdMap.put(productPlans.get(i).getPlanId(), newPlans.get(i).getPlanId());
        }

        //保障项目
        log.info("开始复制保障项目{}:{}", oldProductId, newProductId);
        List<SmProductCoverageVO> coverageList = historyService.getProductCoverageList(oldProductId, version);
        List<SmProductCoverageDTO> coverages = coverageList.stream()
                .map(vo -> {
                    SmProductCoverageDTO res = new SmProductCoverageDTO();
                    BeanUtils.copyProperties(vo, res);
                    res.setSpcId(null);
                    res.setProductId(newProductId);
                    res.setPlanId(planIdMap.get(res.getPlanId()));
                    return res;
                })
                .collect(toList());
        saveProductCoverage(newProductId, coverages);


        List<SmProductCoverageVO> newCovs = historyService.getProductCoverageList(newProductId, null);
        Map<Integer, Integer> spcIdMap = new HashMap<>();
        for (int i = 0; i < coverageList.size(); i++) {
            SmProductCoverageVO oldCov = coverageList.get(i);
            SmProductCoverageVO newCov = newCovs.get(i);
            spcIdMap.put(oldCov.getSpcId(), newCov.getSpcId());
        }
        List<SmProductCoverageAmountItemVO> amountList = historyService.getProductCoverageAmountList(oldProductId, version);

        //保障项目以及保额
        List<SmProductCoverageAmountItemDTO> itemDTOS = amountList.stream()
                .map(vo -> {
                    SmProductCoverageAmountItemDTO dto = new SmProductCoverageAmountItemDTO();
                    BeanUtils.copyProperties(vo, dto);

                    dto.setSpcId(spcIdMap.get(vo.getSpcId()));
                    dto.setProductId(newProductId);
                    List<SmProductCoverageAmountVO> oldAmts = vo.getCoverageAmounts();
                    List<SmProductCoverageAmountDTO> amtDtos = oldAmts.stream()
                            .map(amtVo -> {
                                SmProductCoverageAmountDTO amtDto = new SmProductCoverageAmountDTO();
                                BeanUtils.copyProperties(amtVo, amtDto);
                                amtDto.setSpcId(spcIdMap.get(amtDto.getSpcId()));
                                amtDto.setProductId(newProductId);
                                amtDto.setSpcaId(null);
                                if (Objects.nonNull(amtVo.getCvgAmount())) {
                                    amtDto.setCvgAmount(amtVo.getCvgAmount().toString());
                                }
                                amtDto.setPlanId(planIdMap.get(amtDto.getPlanId()));
                                return amtDto;
                            }).collect(toList());
                    dto.setCoverageAmounts(amtDtos);
                    return dto;
                }).collect(toList());
        saveProductCoverageAmount(newProductId, itemDTOS);
        List<SmProductCoverageAmountItemVO> newAmtList = historyService.getProductCoverageAmountList(newProductId, null);

        //保障项目id隐射
        Map<Integer, Integer> spcaIdMap = new HashMap<>();
        for (int i = 0; i < amountList.size(); i++) {
            SmProductCoverageAmountItemVO old = amountList.get(i);
            SmProductCoverageAmountItemVO newItem = newAmtList.get(i);
            for (int j = 0; j < old.getCoverageAmounts().size(); j++) {
                SmProductCoverageAmountVO oldVo = old.getCoverageAmounts().get(j);
                SmProductCoverageAmountVO newVo = newItem.getCoverageAmounts().get(j);
                spcaIdMap.put(oldVo.getSpcaId(), newVo.getSpcaId());
            }

        }

        log.info("开始复制产品价格{}:{}", oldProductId, newProductId);
        PremiumPage premiumPage = historyService.getPremiumPage(oldProductId, version);

        SmProductCoveragePremiumTable table = new SmProductCoveragePremiumTable();

        List<PremiumFile> premiumFiles = premiumPage.getPremiumFiles();
        table.setFiles(premiumFiles);
        List<SmProductCoveragePremiumItemDTO> pres = premiumPage.getPremiumTable().stream().map(old -> {

            return old.getCoverages().stream()
                    .map(s -> {
                        SmProductCoveragePremiumItemDTO dto = new SmProductCoveragePremiumItemDTO();
                        BeanUtils.copyProperties(old, dto);
                        dto.setPlanId(planIdMap.get(old.getPlanId()));
                        dto.setProductId(newProductId);
                        dto.setSpcId(spcIdMap.get(s.getSpcId()));
                        List<SmProductCoveragePremiumItemDTO.SmProductCoverageAmountVO> vos = s.getCoverageAmounts().stream()
                                .map(amt -> {
                                    SmProductCoveragePremiumItemDTO.SmProductCoverageAmountVO vo = new SmProductCoveragePremiumItemDTO.SmProductCoverageAmountVO();
                                    BeanUtils.copyProperties(amt, vo);
                                    vo.setSpcaId(spcaIdMap.get(amt.getSpcaId()));
                                    List<SmProductCoveragePremiumDTO> premiums = amt.getPremiums();
                                    premiums.forEach(p -> {
                                        p.setPlanId(planIdMap.get(p.getPlanId()));
                                        p.setProductId(newProductId);
                                        p.setSpcId(spcIdMap.get(p.getSpcId()));
                                        p.setSpcaId(spcaIdMap.get(p.getSpcaId()));
                                        p.setSpcpId(null);
                                    });
                                    vo.setPremiums(premiums);
                                    return vo;
                                }).collect(toList());
                        dto.setCoverageAmounts(vos);
                        return dto;
                    }).collect(toList());

        }).flatMap(Collection::stream).collect(toList());
        table.setData(pres);
        savePremiumTable(newProductId, table);

        log.info("开始复制产品介绍{}:{}", oldProductId, newProductId);
        SmProductDetailVO productById = historyService.getProductById(oldProductId, version);
        SmProductDTO inDto = new SmProductDTO();
        inDto.setProductId(newProductId);
        inDto.setGlProductIntroduce(productById.getGlProductIntroduce());
        saveProductIntroduce(inDto);
        // 折扣因子
        log.info("开始复制折扣因子{}:{}", oldProductId, newProductId);
        PremiumFlowFactorVo oldFactor = historyService.getPremiumFactor(oldProductId, version);
        PremiumFlowFactorReqVo newFactor = new PremiumFlowFactorReqVo();
        BeanUtils.copyProperties(oldFactor, newFactor);

        Consumer<PremiumFlow> copyFlow = flow -> {
            flow.setPlanId(planIdMap.get(flow.getPlanId()));
            flow.setProductId(newProductId);
        };
        newFactor.getTimeFactor().getItems().forEach(copyFlow);
        newFactor.getEnterpriseRiskFactor().getItems().forEach(copyFlow);

        newFactor.getInsuredQtyF().getItems()
                .forEach(item -> {
                    item.setProductId(newProductId);
                    item.setSpcdId(null);
                });

        SmProductCoverageDiscountWrap<DutyFactorFlowVo> oldDuty = oldFactor.getDutyFactor();
        if (Objects.nonNull(oldDuty)) {
            SmProductCoverageDiscountWrap<DutyFactorDTO> duty = new SmProductCoverageDiscountWrap<>();
            duty.setAccuracy(oldDuty.getAccuracy());

            List<DutyFactorDTO> newDuties = oldDuty.getItems().stream().map(od -> {
                return od.getItems()
                        .stream().map(odi -> {
                            DutyFactorDTO dto = new DutyFactorDTO();
                            BeanUtils.copyProperties(od, dto);
                            BeanUtils.copyProperties(odi, dto);
                            dto.setProductId(newProductId);
                            dto.setSpcId(spcIdMap.get(od.getSpcId()));
                            return dto;
                        }).collect(toList());
            }).flatMap(Collection::stream).collect(toList());
            duty.setItems(newDuties);
            newFactor.setDutyFactor(duty);
        }
        savePremiumFactor(newProductId, newFactor);

        //配置条件
        log.info("开始复制配置条件{}:{}", oldProductId, newProductId);
        Function<String, String> getNewSpcIds = ids -> {
            if (StringUtils.isBlank(ids)) {
                return null;
            }
            return Arrays.stream(ids.split(","))
                    .map(s -> spcIdMap.get(Integer.valueOf(s))).filter(Objects::nonNull)
                    .map(s -> s + "")
                    .collect(joining(","));
        };
        SmProductQuoteLimitVO limitVo = historyService.getProductQuoteLimit(oldProductId, version);
        SmProductQuoteLimitDTO limitDto = new SmProductQuoteLimitDTO();
        BeanUtils.copyProperties(limitVo, limitDto);
        limitDto.getCvgAmountLimits()
                .forEach(amt -> {
                    amt.setSpqlId(null);
                    amt.setSourceSpcId(getNewSpcIds.apply(amt.getSourceSpcId()));
                    amt.setTargetSpcId(getNewSpcIds.apply(amt.getTargetSpcId()));
                });
        limitDto.getCvgRelationLimits()
                .forEach(amt -> {
                    amt.setSpqlId(null);
                    amt.setSourceSpcId(getNewSpcIds.apply(amt.getSourceSpcId()));
                    amt.setRelyOnSpcId(getNewSpcIds.apply(amt.getRelyOnSpcId()));
                });
        limitDto.getCvgSupportTimeLimits()
                .forEach(amt -> {
                    amt.setSpqlId(null);
                });
        limitDto.getOcpnPerLimit()
                .forEach(amt -> {
                    amt.setSpqlId(null);
                });
        limitDto.getOcp4LiabAmountLimits()
                .forEach(amt -> {
                    amt.setSpqlId(null);
                    amt.setSourceSpcId(getNewSpcIds.apply(amt.getSourceSpcId()));
                });
        saveProductQuoteLimit(newProductId, limitDto);

        log.info("开始复制批改规则{}:{}", oldProductId, newProductId);
        //批改规则
        ProductEndorVo oldEndor = getEndorConfig(oldProductId);
        if (Objects.nonNull(oldEndor)) {
            ProductEndorVo newEndor = new ProductEndorVo();
            BeanUtils.copyProperties(oldEndor, newEndor);
            newEndor.setProductId(newProductId);
            saveEndorConfig(newEndor);
        }
        //健康告知书
        try {
            SmProductNotificationDTO notification = historyService.getProductHealthNotification(oldProductId, version);
            notification.setProductId(newProductId);
            saveProductHealthNotification(notification);
        } catch (Exception e) {
            log.warn("复制健康告知失败", e);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "复制健康告知失败！");
        }
        log.info("团险特性内容复制成功{}:{}", oldProductId, newProductId);
    }

    public boolean deleteProduct(int productId, String userId) {

        SmOrder orderQuery = new SmOrder();
        orderQuery.setProductId(productId);
        if (orderDDDMapper.selectCount(orderQuery) > 0) {
            throw new MSBizNormalException(ExcptEnum.BIZ_VERIFY.getCode(), "该产品关联历史订单，不能删除");
        }

        if (mapper.selectCountPlanBook(productId) > 0) {
            throw new MSBizNormalException(ExcptEnum.BIZ_VERIFY.getCode(), "该产品关联历史计划书，不能删除");
        }

        if (mapper.selectCountGroupQuote(productId) > 0) {
            throw new MSBizNormalException(ExcptEnum.BIZ_VERIFY.getCode(), "该产品关联历史询价记录，不能删除");
        }
        clearCache(productId);
        return mapper.deleteProduct(productId, userId) > 0;
    }

    /**
     * 不可售的则抛出异常，可售则返回产品详情
     *
     * @param productId
     * @return
     */
    public SmProductDetailVO validProductState(Integer productId, String preOrderId) {
        SmProductDetailVO productDetailVO = mapper.getProductById(productId);
        if (Objects.isNull(productDetailVO)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "产品不存在");
        }
        //必须是上线状态才能购买
        if (productDetailVO.getState() == null && !Objects.equals(productDetailVO.getState(), 1)) {
            throw new MSBizNormalException(ExcptEnum.PRODUCT_NOT_ONLINE_201001);
        } else if (Objects.equals(productDetailVO.getState(), 1)) {
            List<Integer> productList = productQueryService.listProductIdByLabelTypeValue(EnumProductLabelType.RENEWAL_PRODUCT.getCode(), SmConstants.LABEL_Y);
            //是续保专用产品，但是原始订单为空,则需要抛出异常
            if (productList.contains(productId) && StringUtils.isBlank(preOrderId)) {
                throw new MSBizNormalException("201201", "该链接已失效");
            }
        }
        return productDetailVO;
    }

    /**
     * 取出一个指定长度大小的随机正整数.
     *
     * @param length int 设定所取出随机数的长度。length小于11
     * @return int 返回生成的随机数。
     */
    public int buildRandom(int length) {
        int num = 1;
        double minRadom = 0.1;
        double random = ThreadLocalRandom.current().nextDouble();
        if (random < minRadom) {
            random = random + minRadom;
        }
        for (int i = 0; i < length; i++) {
            num = num * 10;
        }
        return (int) (random * num);
    }

    public SmProductListVO getProductClaimInfo(Integer id) {
        return this.mapper.getProductById(id);
    }

    public int updateProductClaimInfo(BasicProductVo productVo) {
        return this.mapper.updateProductClaimInfo(productVo);
    }
}
