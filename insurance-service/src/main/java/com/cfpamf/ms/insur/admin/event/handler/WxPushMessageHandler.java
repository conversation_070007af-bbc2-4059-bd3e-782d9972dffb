package com.cfpamf.ms.insur.admin.event.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.StepFinishNotifyDTO;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.bms.facade.vo.UserRoleVO;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimFinishNotifyMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimFinishNotify;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.OrgPicExtraVO;
import com.cfpamf.ms.insur.admin.pojo.vo.ProgressVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.claim.impl.ClaimProcessDistinctServiceImpl;
import com.cfpamf.ms.insur.base.util.Base64Util;
import com.cfpamf.ms.insur.facade.api.OperationFacade;
import com.cfpamf.ms.insur.facade.dto.Push2UserDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxSmClaimTemplateVo;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.SmClaimServiceImpl;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.claim.impl.SmClaimTemplateInfoServiceImpl;
import com.cfpamf.ms.insur.base.config.ClaimConfig;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.service.WxCcClaimService;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 微信理赔推送消息时间处理
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class WxPushMessageHandler implements BaseEventHandler {

    public static final String CLAIM_NOTICE  = "template/claimNotice.ftl";
    /**
     * 理赔结果通知微信模板Id
     */
    @Value("${wechat.message.claimResultId}")
    private String templateId;

    /**
     * 微信参数配置
     */
    @Autowired
    private WechatConfig wechatConfig;

    /**
     * 微信理赔mapper
     */
    @Autowired
    private SmClaimMapper claimMapper;

    /**
     * 微信代理service
     */
    @Lazy
    @Autowired
    private WxMpServiceProxy serviceProxy;

    /**
     * 用户service
     */
    @Autowired
    private UserService userService;

    @Autowired
    private SmClaimMapper smClaimMapper;

    @Autowired
    BmsService bmsService;

    @Autowired
    SmClaimTemplateInfoServiceImpl claimTemplateInfoService;

    @Autowired
    private SmClaimServiceImpl claimService;

    @Autowired
    private WxCcClaimService wxCcClaimService;

    @Autowired
    private OperationFacade operationFacade;

    @Autowired
    private SmClaimFinishNotifyMapper claimFinishNotifyMapper;

    public static final String DEFAULT_NOTIFY_SETTLEMENT = "彭丽敏";

    @Autowired
    SmClaimFinishNotifyMapper finishNotifyMapper;

    /**
     * 处理微信理赔绝陪或者赔付成功的事件
     *
     * @param event
     */
    @Subscribe
    public void handlerWxClaimNotifyEvent(WxClaimNotifyEvent event) {
        log.info("接收处理理赔推送数据 event={}", JSON.toJSONString(event));
        WxClaimNotifyVO wcn = claimMapper.getClaimWxNotify(event.getClaimId());
        if (wcn == null) {
            return;
        }
        wcn.setChangeReason(event.getChangeReason());
        wcn.setOtherExplain(event.getOtherExplain());
        // 结案赔付
        log.info("接收处理理赔推送数据 wcn={}", JSON.toJSONString(wcn));

        if (Objects.equals(event.getOptionCode(), ClaimWorkflow.OTHER_MSG_FOLLOW_UP_REPLY)){
            log.info("推送回复消息{}", JSON.toJSONString(event));
            serviceProxy.sendTemplateMsg(buildClaimReplyMessageToCa(event.getClaimId(), wcn));
            return;
        }

        if (Objects.equals(event.getOptionCode(), ClaimWorkflow.CURRENT_STATUS_COMPANY_SYNC)){
            log.info("推送保司同步消息{}", JSON.toJSONString(event));
            serviceProxy.sendTemplateMsg(buildCompanySyncMsg(event.getClaimId(), wcn));
            return;
        }


        if (Objects.equals(wcn.getFinishState(), ClaimWorkflow.FINISH_STATE_PAYED)) {
            if (Objects.equals(event.getOptionCode(), ClaimWorkflow.CUSTOM_PAY_RESULT)) {
                log.info("推送[支付成功消息]消息{}", JSON.toJSONString(wcn));
                serviceProxy.sendTemplateMsg(buildClaimRealPayedSuccessMessageToPic(event.getClaimId(), wcn));
            }else {
                sendStepFinishReportMsg(event, wcn, false);

                log.info("推送[理赔赔付结案]消息{}", JSON.toJSONString(wcn));
                // 提醒客户经理
                serviceProxy.sendTemplateMsg(buildClaimPayedSuccessMessageToCa(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId())));
                //推送满意度调查
                serviceProxy.sendTemplateMsg(buildClaimSatisfactionSurvey(event.getClaimId(),wcn,wcn.getWxOpenId()));
                // 通知创新业务对接人
                WxMpTemplateMessage wxMpTemplateMessage = buildClaimPayedSuccessMessageToPic(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId()));
                sendMessageToPicAndHead(wxMpTemplateMessage, event.getClaimId());

            }

        }
        // 结案拒赔
        else if (Objects.equals(wcn.getFinishState(), ClaimWorkflow.FINISH_STATE_PAYREJECTED)) {
            sendStepFinishReportMsg(event, wcn, false);
            log.info("推送[理赔绝陪结案]消息{}", JSON.toJSONString(wcn));
            // 提醒客户经理
            Set<String> hadPosted = new HashSet<>();

            WxMpTemplateMessage customerManagerMsg = buildClaimRejectSuccessMessageToCa(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId()));
            serviceProxy.sendTemplateMsg(customerManagerMsg);
            hadPosted.add(wcn.getWxOpenId());
            //推送满意度调查
            serviceProxy.sendTemplateMsg(buildClaimSatisfactionSurvey(event.getClaimId(),wcn,wcn.getWxOpenId()));
            try{
                //区域渠道保险督导
                String orgChannelSupervisorOpenId = getOrgChannelSupervisor(event.getClaimId());
                if (!hadPosted.contains(orgChannelSupervisorOpenId)) {
                    WxMpTemplateMessage supervisorMsg = buildClaimRejectSuccessMessageToCa(event.getClaimId(), wcn, false);
                    sendMessageToChannelSupervisorAndHead(supervisorMsg, event.getClaimId(), orgChannelSupervisorOpenId);
                    hadPosted.add(orgChannelSupervisorOpenId);
                }
            } catch(Exception e) {
                log.warn("发送拒赔区域渠道保险督导通知失败-{}", e.getMessage(), e);
            }
            try{
                // 通知创新业务对接人
                String orgHeadOpenId = getOrgPicOpenId(event.getClaimId());
                if (!hadPosted.contains(orgHeadOpenId)) {
                    WxMpTemplateMessage orgHeadMsg = buildClaimRejectSuccessMessageToPic(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId()), orgHeadOpenId);
                    sendMessageToPicAndHead(orgHeadMsg, event.getClaimId());
                    hadPosted.add(orgHeadOpenId);
                }
            } catch(Exception e) {
                log.warn("发送拒赔机构负责人通知失败-{}", e.getMessage(), e);
            }

        }
        // 结案注销报案
        else if (Objects.equals(wcn.getFinishState(), ClaimWorkflow.FINISH_STATE_CANCEL)) {
            log.info("推送[理赔注销报案]消息{}", JSON.toJSONString(wcn));
            Set<String> hadPosted = new HashSet<>();
            try{
                // 提醒客户经理
                serviceProxy.sendTemplateMsg(buildClaimCancelSuccessMessageToCa(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId())));
                hadPosted.add(wcn.getWxOpenId());
            } catch(Exception e) {
                log.warn("推送[理赔注销报案]提醒客户经理消息失败{}", JSON.toJSONString(wcn), e);
            }
            try{
                // 通知创新业务对接人 和主任
                String orgPicOpendId = getOrgPicOpenId(event.getClaimId());
                if (!hadPosted.contains(orgPicOpendId)){
                    sendMessageToPicAndHead(
                            buildClaimCancelSuccessMessageToPic(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId()),orgPicOpendId),event.getClaimId()
                    );
                    hadPosted.add(orgPicOpendId);
                }
            } catch(Exception e) {
                log.warn("推送[理赔注销报案]提醒主任消息失败{}", JSON.toJSONString(wcn), e);
            }

        }
        // 邮寄资料
        else if (Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_POST_EXPRESS)) {
            log.info("推送[邮件资料提醒]消息{}", JSON.toJSONString(wcn));
            Set<String> hadPosted = new HashSet<>();
            // 提醒客户经理
            serviceProxy.sendTemplateMsg(buildClaimNeedExpressMessageToCa(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId())));
            hadPosted.add(wcn.getWxOpenId());
            String orgPicOpenId = getOrgPicOpenId(event.getClaimId());
            if (!hadPosted.contains(orgPicOpenId)) {
                // 通知到机构对接人
                sendMessageToPicAndHead(buildClaimNeedExpressMessageToPic(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId()), orgPicOpenId), event.getClaimId());
                hadPosted.add(orgPicOpenId);
            }
        }
        // 提交资料
        else if (Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_DATA_PREPARE)) {
            log.info("推送[提交资料提醒]消息{}", JSON.toJSONString(wcn));
            Set<String> hadPosted = new HashSet<>();
            if (Objects.equals(event.getOptionCode(), ClaimWorkflow.OPTION_CODE_REPORT2) || Objects.equals(event.getOptionCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)) {

                if (!Objects.equals(wcn.getProcessType(), EnumClaimProcessType.IMPORT_TO_STEP_PREPARE.getCode())) {
                    try{
                        // 提醒(申请报案/指导报案)客户经理
                        serviceProxy.sendTemplateMsg(buildClaimNeedDataMessageToCa(event.getClaimId(), wcn, wcn.getWxOpenId()));
                        hadPosted.add(wcn.getWxOpenId());
                    } catch(Exception e) {
                        log.warn("{}", e);
                    }
                }

                if (Objects.equals(wcn.getProcessType(), EnumClaimProcessType.IMPORT_TO_STEP_PREPARE.getCode())) {
                    try{
                        // 提醒客户经理
                        if (!hadPosted.contains(wcn.getWxOpenId())) {
                            if (StrUtil.isNotEmpty(wcn.getWxOpenId())) {
                                log.info("导入提醒客户经理-{}", event.getClaimId());
                                serviceProxy.sendTemplateMsg(buildClaimStepPrepareDataToCustomerManger(event.getClaimId(), wcn, wcn.getWxOpenId()));
                                hadPosted.add(wcn.getWxOpenId());
                            }
                        }
                    } catch(Exception e) {
                        log.warn("{}", e);
                    }
                    try{
                        String orgPicOpenId = getOrgPicOpenId(event.getClaimId());
                        if (!hadPosted.contains(orgPicOpenId)) {
                            if (Objects.nonNull(orgPicOpenId)) {
                                log.info("导入提醒机构pco-{}", event.getClaimId());
                                // 提醒pco
                                serviceProxy.sendTemplateMsg(buildClaimStepPrepareData(event.getClaimId(), wcn, orgPicOpenId));
                                hadPosted.add(orgPicOpenId);
                            }
                        }
                     } catch(Exception e) {
                        log.warn("{}", e);
                    }
                    try{
                        // 提醒主任
                        String orgHeadOpenId = getOrgHeadOpenId(event.getClaimId());
                        if (!hadPosted.contains(orgHeadOpenId)) {
                            if (Objects.nonNull(orgHeadOpenId)) {
                                log.info("导入提醒主任-{}", event.getClaimId());
                                serviceProxy.sendTemplateMsg(buildClaimStepPrepareData(event.getClaimId(), wcn, orgHeadOpenId));
                                hadPosted.add(orgHeadOpenId);
                            }
                        }
                    } catch(Exception e) {
                        log.warn("{}", e);
                    }


                }
            }


            if (Objects.equals(event.getOptionCode(), ClaimWorkflow.OPTION_CODE_GUIDED)) {
                Set<String> optionHadPosted = new HashSet<>();

                try{
                    // 提醒(申请报案/指导报案)客户经理
                    serviceProxy.sendTemplateMsg(buildClaimNeedGuideExpressMessageToCa(event.getClaimId(), wcn, "重大案件请24小时内点击查看处理意见", wcn.getWxOpenId()));
                    optionHadPosted.add(wcn.getWxOpenId());
                } catch(Exception e) {
                    log.warn("客户经理发送失败-{}", e);
                }
                //发送给分支渠道PCO和分支负责人
                try{

                    String orgHeadOpenId = getOrgHeadOpenId(event.getClaimId());
                    if (!optionHadPosted.contains(orgHeadOpenId)) {
                        log.info("orgHeadOpenId={}", orgHeadOpenId);
                        serviceProxy.sendTemplateMsg(buildClaimNeedGuideExpressMessageToCa(event.getClaimId(), wcn, "此案件为重大案件，请您后续重点跟进。", orgHeadOpenId));
                        optionHadPosted.add(orgHeadOpenId);
                    }

                } catch(Exception e) {
                    log.warn("渠道pco发送失败-{}", e);
                }
                try{
                    String roleOrgHead = getRoleOrgHead(event.getClaimId());
                    if (!optionHadPosted.contains(roleOrgHead)) {
                        log.info("roleOrgHead={}", roleOrgHead);
                        serviceProxy.sendTemplateMsg(buildClaimNeedGuideExpressMessageToCa(event.getClaimId(), wcn, "此案件为重大案件，请您后续重点跟进。", roleOrgHead));
                        optionHadPosted.add(roleOrgHead);
                    }
                } catch(Exception e) {
                    log.warn("机构负责人发送失败-{}", e);
                }

            }

            WxSmClaimTemplateVo templateVo = null;
            try{
                templateVo = claimTemplateInfoService.detailWxById(event.getClaimId());
            } catch(Exception e) {
                log.warn("{}", e);
            }
            if (Objects.nonNull(templateVo)) {
                serviceProxy.sendTemplateMsg(buildClaimTemplateMessage(event.getClaimId(), wcn));
            }

            if (!Objects.equals(event.getOptionCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)) {
                List<String> jobNumbers = new ArrayList<>();
                jobNumbers.add(wcn.getCustomerAdminId());
                Set<String> hadPostedReceived = new HashSet<>();
                try {
                    //产品确认不管之前的消息，新增一条理赔受理消息发给客户经理，pco，分支主任
                    // 理赔受理消息客户经理
                    serviceProxy.sendTemplateMsg(buildClaimAcceptWorkOrder(event.getClaimId(), wcn, wcn.getWxOpenId()));
                    hadPostedReceived.add(wcn.getWxOpenId());
                } catch (UnsupportedEncodingException|BizException e) {
                    log.warn(e.getMessage(),e);
                }
                try {    // 理赔受理消息pco
                    String orgHeadOpenId = getOrgHeadOpenId(event.getClaimId(),jobNumbers);
                    log.info("orgHeadOpenId={}", orgHeadOpenId);
                    if (!hadPostedReceived.contains(orgHeadOpenId)) {
                        serviceProxy.sendTemplateMsg(buildClaimAcceptWorkOrder(event.getClaimId(), wcn, orgHeadOpenId));
                        hadPostedReceived.add(orgHeadOpenId);
                    }
                } catch (UnsupportedEncodingException|BizException e) {
                    log.warn(e.getMessage(),e);
                }
                try {     // 理赔受理消息分支主任
                    String roleOrgHead = getRoleOrgHead(event.getClaimId(),jobNumbers);
                    log.info("roleOrgHead={}", roleOrgHead);
                    if (!hadPostedReceived.contains(roleOrgHead)) {
                        serviceProxy.sendTemplateMsg(buildClaimAcceptWorkOrder(event.getClaimId(), wcn, roleOrgHead));
                        hadPostedReceived.add(roleOrgHead);
                    }
                } catch (UnsupportedEncodingException|BizException e) {
                    log.warn(e.getMessage(),e);
                }
                sendDingDingMessage(event, jobNumbers);
            }

        }
        // 补充资料
        else if ((Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_DATA_PREPARE)
                || Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_DATA_PREPARE2)
                || Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_DATA_PREPARE3))
                && Objects.equals(event.getOptionCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)) {
            log.info("推送[补充资料提醒]消息{}", JSON.toJSONString(wcn));
            // 提醒客户经理
            serviceProxy.sendTemplateMsg(buildClaimNeedMoreDataMessageToCa(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId())));
            Set<String> hadPosted = new HashSet<>();
            hadPosted.add(wcn.getWxOpenId());

            String orgPicOpenId = getOrgPicOpenId(event.getClaimId());
            if (!hadPosted.contains(orgPicOpenId)) {
                // 提醒业务对接人
                List<ProgressVO> progressVOS = smClaimMapper.listSmClaimProgressList(event.getClaimId());
                progressVOS.sort(Comparator.comparing(ProgressVO::getCreateTime).reversed());
                //如果上一个步骤不是机构对接人(不是机构对接人驳回的则需要提醒机构对接人)
                if (progressVOS.size() >= 2 && !Objects.equals(progressVOS.get(0)
                                                                       .getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_PIC)) {
                    sendMessageToPicAndHead(buildClaimNeedMoreDataMessageToPic(event.getClaimId(), wcn, isNeedPushSettlement(event.getClaimId()), orgPicOpenId), event.getClaimId());
                    hadPosted.add(orgPicOpenId);

                }

            }

        }
        // 审批资料提醒(对接人)
        else if (Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_DATA_CHECK_BY_PIC)
                && Objects.equals(event.getOptionCode(), ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED)) {
            log.info("推送[审批资料提醒对接人]消息{}", JSON.toJSONString(wcn));
            try{
                // 提醒机构对接人
                String suffix = "";
                String sendTo = getOrgHeadOpenId(event.getClaimId());
                if (Objects.nonNull(event.getAutoCheck()) && event.getAutoCheck()) {
                    suffix = "理赔中（请您关注理赔信息）";
                    sendMessageToPicAndHead(buildClaimApprovalDataMessageToPic(event.getClaimId(),"orgView", wcn, suffix, sendTo), event.getClaimId());
                }else {
                    suffix = "资料审核（请您尽快处理）";
                    sendMessageToPicAndHead(buildClaimApprovalDataMessageToPic(event.getClaimId(),"auditMaterial", wcn, suffix, sendTo), event.getClaimId());
                }
            } catch(Exception e) {
                log.info("推送[审批资料提醒对接人]消息{}失败", JSON.toJSONString(wcn), e);
            }

        }
        // 审批资料提醒(保险事业部)
        else if (Objects.equals(wcn.getClaimState(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
            List<ProgressVO> progresses = claimMapper.listSmClaimProgressList(event.getClaimId());
            if (!progresses.isEmpty() && Objects.equals(progresses.get(progresses.size() - 1).getSCode(), ClaimWorkflow.STEP_DATA_PREPARE2)) {
                log.info("推送[审批资料提醒保险事业部]消息{}", JSON.toJSONString(wcn));
                // 提醒保险事业部
                serviceProxy.sendTemplateMsg(buildClaimApprovalDataMessageToSafesCenter(event.getClaimId(), wcn));
            }
        }

    }

    public void sendStepFinishReportMsg(WxClaimNotifyEvent event, WxClaimNotifyVO wcn, boolean isManual) {
        StepFinishReportVO finishReportVO = distinctService.getStepFinish(event.getClaimId());
        if (!Lists.newArrayList("101", "201", "301").contains(finishReportVO.getFinishType())) {
            throw new BizException("", "该结案类型无法推送");
        }
        String reportNum="";
        if (isManual) {
            SmClaimFinishNotify finishNotify = finishNotifyMapper.selectByClaimId(event.getClaimId());
            if (Objects.isNull(finishNotify)) {
                StepFinishNotifyDTO finishNotifyDTO = new StepFinishNotifyDTO();
                finishNotifyDTO.setId(event.getClaimId());
                reportNum = distinctService.addFinishNotify(finishNotifyDTO);
            }
        } else {
            StepFinishNotifyDTO finishNotifyDTO = new StepFinishNotifyDTO();
            finishNotifyDTO.setId(event.getClaimId());
            reportNum = distinctService.addFinishNotify(finishNotifyDTO);
        }

        if (StrUtil.isEmpty(finishReportVO.getFinishReportNo())) {
            finishReportVO.setFinishReportNo(reportNum);
        }

        //推送结案通知书
        List<String> jobNumbers = new ArrayList<>();
        Set<String> hadPosted = new HashSet<>();
        jobNumbers.add(wcn.getCustomerAdminId());
        try{
            serviceProxy.sendTemplateMsg(buildClaimFinishReport(event.getClaimId(), wcn, wcn.getWxOpenId()));
            hadPosted.add(wcn.getWxOpenId());
        } catch(Exception e) {
            log.info("管护客户经理推送[结案报告消息]消息{}", JSON.toJSONString(wcn));
        }
        try{
            String roleOrgHead = getRoleOrgHead(event.getClaimId(), jobNumbers);
            if (!hadPosted.contains(roleOrgHead)) {
                serviceProxy.sendTemplateMsg(buildClaimFinishReport(event.getClaimId(), wcn, roleOrgHead));
                hadPosted.add(roleOrgHead);
            }

        } catch(Exception e) {
            log.info("机构pco推送[结案报告消息]消息{}", JSON.toJSONString(wcn));
        }
        try{
            String orgHeadOpenId = getOrgHeadOpenId(event.getClaimId(), jobNumbers);
            if (!hadPosted.contains(orgHeadOpenId)) {
                serviceProxy.sendTemplateMsg(buildClaimFinishReport(event.getClaimId(), wcn, orgHeadOpenId));
                hadPosted.add(orgHeadOpenId);
            }
        } catch(Exception e) {
            log.info("机构主任推送[结案报告消息]消息{}", JSON.toJSONString(wcn));
        }
        //推送钉钉消息
        try{
            if (CollectionUtils.isNotEmpty(jobNumbers)) {
                jobNumbers.add("ZHNX34439");
                sendStepFinishReportDingDingMessage(event, jobNumbers, finishReportVO);
            }
        } catch(Exception e) {
            log.info("推送[管护、机构pco、主任]消息{}", JSON.toJSONString(wcn));
        }
    }

    protected WxMpTemplateMessage buildCompanySyncMsg(int claimId, WxClaimNotifyVO notifyVO) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        String url = getBackClaimDetailUrl(claimId, "schedule", "cm");
        log.info("跳转地址-{}", url);
        wtm.setUrl(url);
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", notifyVO.getOtherExplain()));
        return wtm;
    }


    private void sendDingDingMessage(WxClaimNotifyEvent event, List<String> jobNumbers) {

        try {
            WxClaimDetailVO vo = wxCcClaimService.getWxClaimById(event.getClaimId(),null,null);
            Push2UserDTO push2UserDTO = new Push2UserDTO();
            push2UserDTO.setJobNumbers(new HashSet<>(jobNumbers));
            ClassPathResource resource = new ClassPathResource("template/claimNotice.ftl");
            StringBuilder contentBuilder = new StringBuilder();
            try (Reader reader = new InputStreamReader(resource.getInputStream(), "UTF-8");
                 BufferedReader bufferedReader = new BufferedReader(reader)) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    contentBuilder.append(line).append("\n");
                }
            }
            // 输出文件内容
            String fileContent = contentBuilder.toString();
            push2UserDTO.setTemplate(fileContent);
            push2UserDTO.setTemplateParams(JSON.toJSONString(vo));
            push2UserDTO.setImageParams("{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}");
            operationFacade.pushImageMsg2User(push2UserDTO);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }

    }

    @Autowired
    private ClaimProcessDistinctServiceImpl distinctService;
    private void sendStepFinishReportDingDingMessage(WxClaimNotifyEvent event, List<String> jobNumbers, StepFinishReportVO finishReportVO) {

        try {
            Push2UserDTO push2UserDTO = new Push2UserDTO();
            push2UserDTO.setJobNumbers(new HashSet<>(jobNumbers));
            ClassPathResource resource = new ClassPathResource("template/claimFinishReport.ftl");
            StringBuilder contentBuilder = new StringBuilder();
            try (Reader reader = new InputStreamReader(resource.getInputStream(), "UTF-8");
                 BufferedReader bufferedReader = new BufferedReader(reader)) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    contentBuilder.append(line).append("\n");
                }
            }
            // 输出文件内容
            String fileContent = contentBuilder.toString();
            push2UserDTO.setTemplate(fileContent);
            push2UserDTO.setTemplateParams(JSON.toJSONString(finishReportVO));
            push2UserDTO.setImageParams("{\"imgParam\": {\"width\": 750, \"screenshot\": {\"fullPage\": true, \"omitBackground\": true}}}");
            operationFacade.pushImageMsg2User(push2UserDTO);
        } catch (Exception e) {
            log.warn(e.getMessage(),e);
        }

    }


    private WxMpTemplateMessage buildClaimAcceptWorkOrder(Integer claimId, WxClaimNotifyVO wcn, String wxOpenId) throws UnsupportedEncodingException {
        WxClaimNeedDataVO needDataVO = getWxClaimNeedDataVO(claimId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getClaimAcceptWorkOrderUrl(claimId));
        wtm.setTemplateId(templateId);
        wtm.setToUser(wxOpenId);
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(wcn), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "点击查看理赔受理工单详情", "#0000FF"));
        return wtm;
    }

    private WxMpTemplateMessage buildClaimSatisfactionSurvey(Integer claimId, WxClaimNotifyVO wcn, String wxOpenId) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        try {
            wtm.setUrl(getClaimSatisfactionSurveyUrl(claimId, wcn.getClaimNo()));
        } catch (UnsupportedEncodingException e) {
            log.warn(e.getMessage());
        }
        wtm.setTemplateId(templateId);
        wtm.setToUser(wxOpenId);
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(wcn), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "点击对理赔服务进行评价", "#0000FF"));
        return wtm;
    }

    private String getClaimAcceptWorkOrderUrl(Integer claimId) throws UnsupportedEncodingException {
        String h5Url = String.format(this.wechatConfig.getClaimAcceptWorkOrderH5(),claimId);
        String h5UrlBase64 = Base64Util.encryptBASE64(h5Url.getBytes(StandardCharsets.UTF_8));
        String authUrl = String.format(this.wechatConfig.getAuthUrl(),h5UrlBase64);
        return String.format(this.wechatConfig.getClaimAcceptWorkOrderUrl(), URLEncoder.encode(authUrl, StandardCharsets.UTF_8.name()));
    }

    private String getClaimSatisfactionSurveyUrl(Integer claimId,String claimNo) throws UnsupportedEncodingException {
        String h5Url = String.format(this.wechatConfig.getClaimSatisfactionSurveyH5(),claimId,claimNo);
        String h5UrlBase64 = Base64Util.encryptBASE64(h5Url.getBytes(StandardCharsets.UTF_8));
        String authUrl = String.format(this.wechatConfig.getAuthUrl(),h5UrlBase64);
        return String.format(this.wechatConfig.getClaimSatisfactionSurveyUrl(), URLEncoder.encode(authUrl, StandardCharsets.UTF_8.name()));
    }

    public static void main(String[] args) {
        //读取template/claimNotice.txt文件打印到控制台
        try {
            String content = FileUtils.readFileToString(new File("template/claimNotice.ftl"), StandardCharsets.UTF_8.name());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("template",content);
            System.out.println(jsonObject);
        } catch (IOException e) {
            e.printStackTrace();
        }
     }

    private WxMpTemplateMessage buildClaimGuideMessageToSettlement(Integer claimId, WxClaimNotifyVO wcn, UserVO userVO) {

        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "sl"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(userVO.getWxOpenId());
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(wcn), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "待指导报案"));
        return wtm;

    }

    public void sendMessageToPicAndHead(WxMpTemplateMessage message, Integer claimId) {

        //只推送给主任
//        if (org.apache.commons.lang3.StringUtils.isNotBlank(message.getToUser())) {
//            //通知创新业务对接人
//            serviceProxy.sendTemplateMsg(message);
//        }
        //通知 主任
//        message.setToUser(getOrgHeadOpenId(claimId));
        serviceProxy.sendTemplateMsg(message);
    }


    public void sendMessageToChannelSupervisorAndHead(WxMpTemplateMessage message, Integer claimId, String sendTo) {


        try{
            message.setToUser(sendTo);
            if (org.apache.commons.lang.StringUtils.isNotEmpty(message.getToUser())) {
                serviceProxy.sendTemplateMsg(message);
            }
        } catch(Exception e) {
            log.warn("理赔消息通知失败，claimId={}，exceptionMsg={}", claimId, e.getMessage());
        }
    }

    /**
     * 赔付成功消息结构->客户经理
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimPayedSuccessMessageToCa(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("赔付成功, 赔付金额为 %s 元", notifyVO.getPayMoney()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" +  getDefaultNotifySettlement(notifyVO.getSettlement()) + "，如对案件有任何疑问，请联系此案件的理赔专员。\r\n" : "")
//                + (notifyVO.getChangeReason() != null ? "更改结案结果原因：" + notifyVO.getChangeReason() + "\r\n" : "")
//                + (notifyVO.getOtherExplain() != null ? "其他说明：" + notifyVO.getOtherExplain() + "\r\n" : "")
//                + "理赔款将于10个工作日内汇入被保险人的账户内,请您注意提醒客户查收。", "#030303"));
        return wtm;
    }

    private WxMpTemplateMessage buildClaimTemplateMessage(Integer claimId, WxClaimNotifyVO wcn) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimTemplateUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(wcn.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", wcn.getCustomerName()));
        wtm.addData(new WxMpTemplateData("keyword2", "理赔申请需准备以下材料，请一次性准备好"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" +  getDefaultNotifySettlement(notifyVO.getSettlement()) + "，如对案件有任何疑问，请联系此案件的理赔专员。\r\n" : "")
//                + (notifyVO.getChangeReason() != null ? "更改结案结果原因：" + notifyVO.getChangeReason() + "\r\n" : "")
//                + (notifyVO.getOtherExplain() != null ? "其他说明：" + notifyVO.getOtherExplain() + "\r\n" : "")
//                + "理赔款将于10个工作日内汇入被保险人的账户内,请您注意提醒客户查收。", "#030303"));
        return wtm;
    }

    protected WxMpTemplateMessage buildClaimRealPayedSuccessMessageToPic(int claimId, WxClaimNotifyVO notifyVO) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", notifyVO.getOtherExplain()));
        return wtm;
    }


    private String getDefaultNotifySettlement(String settlement) {
        return StringUtils.isEmpty(settlement) ? DEFAULT_NOTIFY_SETTLEMENT : settlement;
    }

    /**
     * 赔付成功消息结构->机构对接人
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimPayedSuccessMessageToPic(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(getOrgPicOpenId(claimId));
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您, %s 的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerAdminName(), notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("赔付成功, 赔付金额为 %s 元", notifyVO.getPayMoney()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" + getDefaultNotifySettlement(notifyVO.getSettlement()) + "，如对案件有任何疑问，请联系此案件的理赔专员。\r\n" : "")
//                + (notifyVO.getChangeReason() != null ? "更改结案结果原因：" + notifyVO.getChangeReason() + "\r\n" : "")
//                + (notifyVO.getOtherExplain() != null ? "其他说明：" + notifyVO.getOtherExplain() + "\r\n" : "")
//                + "理赔款将于10个工作日内汇入被保险人的账户内,请您注意提醒客户查收。", "#030303"));
        return wtm;
    }

    private static String formatInsured(WxClaimNotifyVO notifyVO) {

        if (StringUtils.isEmpty(notifyVO.getCustomerAdminName())) {
            return notifyVO.getCustomerName();
        }
        return String.format("%s（%s提交）", notifyVO.getCustomerName(), notifyVO.getCustomerAdminName());
    }

    /**
     * 拒赔消息结构->客户经理
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimRejectSuccessMessageToCa(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("保险公司拒赔, 拒赔理由为 %s", notifyVO.getRejectReason()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" + getDefaultNotifySettlement(notifyVO.getSettlement()) + "\r\n" : "")
//                + (notifyVO.getChangeReason() != null ? "更改结案结果原因：" + notifyVO.getChangeReason() + "\r\n" : "")
//                + (notifyVO.getOtherExplain() != null ? "其他情况说明：" + notifyVO.getOtherExplain() + "\r\n" : "")
//                + "请您与客户做好沟通解释工作,如有疑问,请联系保险事业部理赔岗。", "#030303"));
        return wtm;
    }

    /**
     * 拒赔消息结构->机构对接人
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimRejectSuccessMessageToPic(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement, String sendTo) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(sendTo);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,%s 的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerName(), notifyVO.getProductName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("保险公司拒赔, 拒赔理由为 %s", notifyVO.getRejectReason()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" + getDefaultNotifySettlement(notifyVO.getSettlement()) + "\r\n" : "")
//                + (notifyVO.getChangeReason() != null ? "更改结案结果原因：" + notifyVO.getChangeReason() + "\r\n" : "")
//                + (notifyVO.getOtherExplain() != null ? "其他情况说明：" + notifyVO.getOtherExplain() + "\r\n" : "")
//                + "请您与客户做好沟通解释工作,如有疑问,请联系保险事业部理赔岗。", "#030303"));
        return wtm;
    }

    /**
     * 注销报案消息结构->客户经理
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimCancelSuccessMessageToCa(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerName(), notifyVO.getProductName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("已注销报案,注销原因：%s", notifyVO.getRejectReason()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" + getDefaultNotifySettlement(notifyVO.getSettlement()) + "\r\n" : "")
//                + "如有疑问，请联系保险事业部理赔岗。", "#030303"));
        return wtm;
    }

    /**
     * 注销报案消息结构->机构对接人
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimCancelSuccessMessageToPic(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement, String sendTo) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        if (StrUtil.isNotEmpty(sendTo)) {
            wtm.setToUser(sendTo);
        } else{
            wtm.setToUser(getOrgPicOpenId(claimId));
        }
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,%s 的客户 %s 的理赔报案已结案。\r\n", notifyVO.getCustomerAdminName(), notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("已注销报案,注销原因：%s", notifyVO.getRejectReason()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", (isNeedPushSettlement ? "理赔专员：" + getDefaultNotifySettlement(notifyVO.getSettlement()) + "\r\n" : "")
//                + "如有疑问，请联系保险事业部理赔岗。", "#030303"));
        return wtm;
    }

    /**
     * 提交资料消息结构->客户经理
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimNeedDataMessageToCa(int claimId, WxClaimNotifyVO notifyVO, String toUserOpenId) {

        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(toUserOpenId);
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "待提交资料", "#0000FF"));
        return wtm;
    }

    protected WxMpTemplateMessage buildClaimStepPrepareData(int claimId, WxClaimNotifyVO notifyVO, String toUserOpenId) {

        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "orgView", "pic"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(toUserOpenId);
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "资料准备中", "#0000FF"));
        return wtm;
    }

    protected WxMpTemplateMessage buildClaimStepPrepareDataToCustomerManger(int claimId, WxClaimNotifyVO notifyVO, String toUserOpenId) {

        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(toUserOpenId);
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "资料准备中", "#0000FF"));
        return wtm;
    }


    /**
     * 补充资料消息结构->客户经理
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimNeedMoreDataMessageToCa(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement) {
        WxClaimNeedDataVO needDataVO = getWxClaimNeedDataVO(claimId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案需补充资料,请您尽快处理,以免影响正常理赔。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("需补充资料: %s", needDataVO.getNeedData()), "#0000FF"));
        return addWxMpTemplateMessageSettlement(wtm, isNeedPushSettlement, getClaimSettlement(claimId));
    }

    /**
     * 邮件资料消息结构->客户经理
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimNeedExpressMessageToCa(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement) {
        WxClaimExpressReceiverVO expressVO = getWxClaimExpressVO(claimId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案需邮寄纸质资料,请您尽快处理,以免影响正常理赔。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("请尽快上传快递信息！请将已提交的所有电子版资料的纸质原件邮寄至该地址: %s, 联系人: %s, 电话: %s, 备注: %s, 邮寄资料后请上传快递信息。", expressVO.getReceiverAddress(), expressVO.getReceiverName(), expressVO.getReceiverPhone(), expressVO.getOtherMsg()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", "", "#030303"));
        return addWxMpTemplateMessageSettlement(wtm, isNeedPushSettlement, getClaimSettlement(claimId));
    }

    protected WxMpTemplateMessage buildClaimReplyMessageToCa(int claimId, WxClaimNotifyVO notifyVO) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        String url = getBackClaimHastenReplyDetailUrl(claimId, "consult");
        log.info("跳转地址-{}", url);
        wtm.setUrl(url);
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVO.getWxOpenId());
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "您的咨询已有最新回复！请点击查看详情。"));
        return wtm;
    }

    /**
     * 补充资料消息结构->对接人
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimNeedMoreDataMessageToPic(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement, String sendTo) {
        WxClaimNeedDataVO needDataVO = getWxClaimNeedDataVO(claimId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "orgView", "pic"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(sendTo);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您, %s 的客户 %s 的理赔报案需补充资料，请您提醒客户经理尽快处理。\r\n", notifyVO.getCustomerAdminName(), notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("需补充资料: %s", needDataVO.getNeedData()), "#0000FF"));
        return addWxMpTemplateMessageSettlement(wtm, isNeedPushSettlement, getClaimSettlement(claimId));
    }

    /**
     * 邮件资料消息结构->对接人
     *
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimNeedExpressMessageToPic(int claimId, WxClaimNotifyVO notifyVO, boolean isNeedPushSettlement, String sendTo) {
        WxClaimExpressReceiverVO expressVO = getWxClaimExpressVO(claimId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "pic"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(sendTo);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您, %s 的客户 %s 的理赔报案需邮寄纸质资料, 请您提醒客户经理尽快处理, 以免影响正常理赔。\r\n", notifyVO.getCustomerAdminName(), notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("请尽快上传快递信息！请将已提交的所有电子版资料的纸质原件邮寄至该地址: %s, 联系人: %s, 电话: %s, 备注: %s, 邮寄资料后请上传快递信息。", expressVO.getReceiverAddress(), expressVO.getReceiverName(), expressVO.getReceiverPhone(), expressVO.getOtherMsg()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", "", "#030303"));
        return addWxMpTemplateMessageSettlement(wtm, isNeedPushSettlement, getClaimSettlement(claimId));
    }

    /**
     * 资料审批结构->对接人
     *
     * @param claimId
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimApprovalDataMessageToPic(int claimId, String type, WxClaimNotifyVO notifyVO, String suffix, String sendTo) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, type, "pic"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(sendTo);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您, 请您尽快审核客户 %s 的理赔资料, 以免影响正常理赔。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", suffix, "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", "如对案件有任何疑问，请联系此案件的理赔专员。", "#030303"));
        return wtm;
    }

    /**
     * 资料审批结构->保险额业务中心
     *
     * @param claimId
     * @param notifyVO
     * @return
     */
    protected WxMpTemplateMessage buildClaimApprovalDataMessageToSafesCenter(int claimId, WxClaimNotifyVO notifyVO) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "sc"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(getSettlementOpenId(claimId));
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您, %s 的客户 %s 的理赔补充资料已经提交，请您尽快审核，以免影响正常理赔。\r\n", notifyVO.getCustomerAdminName(), notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "审批资料", "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", "", "#030303"));
        return wtm;
    }


    protected WxMpTemplateMessage buildClaimNeedGuideExpressMessageToCa(int claimId, WxClaimNotifyVO notifyVO, String state, String pushTo) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailUrl(claimId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(pushTo);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您,您的客户 %s 的理赔报案需邮寄纸质资料,请您尽快处理,以免影响正常理赔。\r\n", notifyVO.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", state, "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", "", "#030303"));
        return wtm;
    }


    /**
     * 在【保险事业部资料审核】【核赔中】这两个阶段，保险专员在后台更新进度，微信端提醒中增加理赔专员；
     *
     * @param wtm
     * @param isNeedPushSettlement
     * @param settlement
     * @return
     */
    private WxMpTemplateMessage addWxMpTemplateMessageSettlement(WxMpTemplateMessage wtm, boolean isNeedPushSettlement, String settlement) {
        if (isNeedPushSettlement) {
            wtm.addData(new WxMpTemplateData("remark", "理赔专员：" + getDefaultNotifySettlement(settlement)+"，如对案件有任何疑问，请联系此案件的理赔专员。", "#030303"));
        } else {
            wtm.addData(new WxMpTemplateData("remark", "如对案件有任何疑问，请联系此案件的理赔专员。", "#030303"));
        }
        return wtm;
    }

    /**
     * 获取当前报案对应的保险事业部理赔小组理赔专员openId
     *
     * @param claimId
     * @return
     */
    private String getSettlementOpenId(int claimId) {
        WxClaimDetailVO claimDetail = claimMapper.getSmClaimById(claimId);
//        ClaimConfig claimConfig = SpringFactoryUtil.getBean(ClaimConfig.class);

        List<UserRoleVO> userRoleVOS = bmsService.listUserByClaimClerk();
        String stmName = claimDetail.getSettlement();
        if (StringUtils.isEmpty(stmName)) {
            throw new BizException(ExcptEnum.CLAIM_SETTLEMENT_ERROR);
        }
        Optional<UserRoleVO> optional = userRoleVOS.stream()
                .filter(s -> Objects.equals(s.getUserName(), stmName)).findFirst();
        if (!optional.isPresent()) {
            throw new BizException(ExcptEnum.CLAIM_SETTLEMENT_ERROR);
        }
        String stmUserId = optional.get().getJobNumber();
        AuthUserVO stmUser = userService.getAuthUserByUserId(stmUserId);
        if (stmUser == null) {
            log.info("用户表没有没有找到理赔专员userId={}", stmUserId);
            return null;
        }
        String stmOpenId = stmUser.getWxOpenId();
        if (StringUtils.isEmpty(stmOpenId)) {
            log.info("用户表理赔专员userId={}微信openId为空", stmUserId);
        }
        if (stmOpenId == null) {
            throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR);
        }
        return stmOpenId;
    }

    /**
     * 获取机构负责人审批的openId
     *
     * @param claimId
     * @return
     */
    public String getOrgPicOpenId(int claimId) {
        AuthUserVO authUserVO = claimMapper.getClaimCustomerAdminUser(claimId);
        if (authUserVO == null) {
            log.info("理赔Id={}没有找到负责人,", claimId);
            return null;
        }
        String approvalUserId = null;
        if (Objects.equals(authUserVO.getRegionName(), "总部")) {
            approvalUserId = SpringFactoryUtil.getBean(ClaimConfig.class).getApprover().getUserId();
        } else {
            OrgPicExtraVO orgPicExtraVO = userService.getOrgPicExtraByRegionAndOrg(authUserVO.getRegionName(), authUserVO.getOrganizationName());
            if (orgPicExtraVO == null) {
                log.info("机构负责人没有找到负责人regionName={}, orgName={}", authUserVO.getRegionName(), authUserVO.getOrganizationName());
                return null;
            }
            approvalUserId = orgPicExtraVO.getUserId();
        }
        AuthUserVO approvalUser = userService.getAuthUserByUserId(approvalUserId);
        if (approvalUser == null) {
            log.info("用户表没有没有找到机构对接人userId={}", approvalUserId);
            return null;
        }
        String approvalOpenId = approvalUser.getWxOpenId();
        if (StringUtils.isEmpty(approvalOpenId)) {
            log.info("用户表机构对接人userId={}微信openId为空", approvalUserId);
        }
//        if (approvalOpenId == null) {
//            throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR);
//        }
        return approvalOpenId;
    }

    /**
     * 获取机构主任审批的openId
     *
     * @param claimId
     * @return
     */
    public String getOrgHeadOpenId(int claimId) {
        return this.getOrgHeadOpenId(claimId,null);
    }
    public String getOrgHeadOpenId(int claimId,List<String> jobNumbers) {
        AuthUserVO authUserVO = claimMapper.getClaimCustomerAdminUser(claimId);
        if (authUserVO == null) {
            log.info("理赔Id={}没有找到负责人,", claimId);
            return null;
        }
        if (Objects.equals(authUserVO.getRegionName(), "总部")) {
            return null;
        } else {
            AuthUserVO user = userService.getChannelPCOBydOrg(authUserVO.getRegionName(), authUserVO.getOrganizationName());
            if (Objects.isNull(user)) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR.getCode(), "未找到渠道PCO:" + authUserVO.getOrganizationName());
            }
            if(CollectionUtils.isNotEmpty(jobNumbers)){
                jobNumbers.add(authUserVO.getUserId());
            }
            if (user.getWxOpenId() == null) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR);
            }
            return user.getWxOpenId();
        }
    }

    public String getOrgChannelSupervisor(int claimId) {
        AuthUserVO authUserVO = claimMapper.getClaimCustomerAdminUser(claimId);
        if (authUserVO == null) {
            log.info("理赔Id={}没有找到负责人,", claimId);
            return null;
        }
        if (Objects.equals(authUserVO.getRegionName(), "总部")) {
            return null;
        } else {
            AuthUserVO user = userService.getChannelSupervisorBydOrg(authUserVO.getRegionName(), authUserVO.getOrganizationName());
            if (Objects.isNull(user)) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR.getCode(), "未找到区域渠道保险督导:" + authUserVO.getOrganizationName());
            }
            if (user.getWxOpenId() == null) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR);
            }
            return user.getWxOpenId();
        }
    }

    public String getRoleOrgHead(int claimId) {
        return this.getRoleOrgHead(claimId,null);
    }

    public String getRoleOrgHead(int claimId,List<String> jobNumbers) {
        AuthUserVO authUserVO = claimMapper.getClaimCustomerAdminUser(claimId);
        if (authUserVO == null) {
            log.info("理赔Id={}没有找到负责人,", claimId);
            return null;
        }
        if (Objects.equals(authUserVO.getRegionName(), "总部")) {
            return null;
        } else {
            AuthUserVO user = userService.getRoleOrgHeadByOrg(authUserVO.getRegionName(), authUserVO.getOrganizationName());
            if (Objects.isNull(user)) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR.getCode(), "未找到机构负责人:" + authUserVO.getOrganizationName());
            }
            if (CollectionUtils.isNotEmpty(jobNumbers)){
                jobNumbers.add(user.getUserId());
            }
            if (user.getWxOpenId() == null) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR);
            }
            return user.getWxOpenId();
        }
    }

    /**
     * 获取分支负责人
     * @param claimId
     * @return
     */
    public String getOrgHeadRoleOpenId(int claimId) {
        AuthUserVO authUserVO = claimMapper.getClaimCustomerAdminUser(claimId);
        if (authUserVO == null) {
            log.info("理赔Id={}没有找到负责人,", claimId);
            return null;
        }
        if (Objects.equals(authUserVO.getRegionName(), "总部")) {
            return null;
        } else {
            AuthUserVO user = userService.getChannelPCOBydOrg(authUserVO.getRegionName(), authUserVO.getOrganizationName());
            if (Objects.isNull(user)) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR.getCode(), "未找到渠道PCO:" + authUserVO.getOrganizationName());
            }
            if (user.getWxOpenId() == null) {
                throw new BizException(ExcptEnum.CLAIM_ADMIN_UNBIND_ERROR);
            }
            return user.getWxOpenId();
        }
    }

    /**
     * 在【保险事业部资料审核】【保司核赔中】这两个阶段，
     * 保险专员在后台更新进度，微信端提醒中增加理赔专员
     *
     * @param claimId
     */
    private boolean isNeedPushSettlement(int claimId) {
        // 在【保险事业部资料审核】【保司核赔中】这两个阶段，保险专员在后台更新进度，微信端提醒中增加理赔专员
        List<ProgressVO> progresses = claimMapper.listSmClaimProgressList(claimId);
        boolean isNeedPushSettlement = false;
        if (!progresses.isEmpty()) {
            String lastProcessSCode = progresses.get(progresses.size() - 1).getSCode();
            if (Objects.equals(lastProcessSCode, ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)
                    || Objects.equals(lastProcessSCode, ClaimWorkflow.STEP_TO_PAY)) {
                isNeedPushSettlement = true;
            }
            // 已結案的
            else if (Objects.equals(lastProcessSCode, ClaimWorkflow.STEP_FINISH) && progresses.size() > 1) {
                lastProcessSCode = progresses.get(progresses.size() - 2).getSCode();
                if (Objects.equals(lastProcessSCode, ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)
                        || Objects.equals(lastProcessSCode, ClaimWorkflow.STEP_TO_PAY)) {
                    isNeedPushSettlement = true;
                }
            }
        }

        return isNeedPushSettlement;
    }

    /**
     * 获取微信理赔详情进度页URL
     *
     * @return
     */
    private String getBackClaimDetailUrl(int claimId, String type, String role) {
        try {
            return String.format(wechatConfig.getBackJumpUrl(), URLEncoder.encode(wechatConfig.getBackClaimDetailUrl() + "?claimId=" + claimId + "&type=" + type + "&role=" + role, StandardCharsets.UTF_8.name()), "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private String getBackClaimHastenReplyDetailUrl(int claimId, String type) {
        try {
            return String.format(wechatConfig.getBackJumpUrl(), URLEncoder.encode(wechatConfig.getBackFollowUpDetailUrl() + "?claimId=" + claimId + "&formType=" + type, StandardCharsets.UTF_8.name()), "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }



    /**
     * 获取微信理赔详情进度页URL
     *
     * @return
     */
    private String getBackClaimTemplateUrl(int claimId, String type, String role) {
        try {
            return String.format(wechatConfig.getBackJumpUrl(), URLEncoder.encode(wechatConfig.getBackClaimTemplateUrl() + "?claimId=" + claimId + "&type=" + type + "&role=" + role, StandardCharsets.UTF_8.name()), "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }


    /**
     * 理赔邮件资料
     *
     * @return
     */
    private WxClaimExpressReceiverVO getWxClaimExpressVO(int claimId) {
        return claimMapper.getWxClaimExpress(claimId);
    }

    /**
     * 补充资料
     *
     * @return
     */
    private WxClaimNeedDataVO getWxClaimNeedDataVO(int claimId) {
        return claimMapper.getWxClaimNeedData(claimId);
    }


    /**
     * 获取理赔专员
     *
     * @return
     */
    private String getClaimSettlement(int claimId) {
        return claimMapper.getSmClaimById(claimId).getSettlement();
    }

    protected WxMpTemplateMessage buildClaimFinishReport(int claimId, WxClaimNotifyVO notifyVO, String openId) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(buildBackClaimFinishReportDetailUrl(claimId));
        wtm.setTemplateId(templateId);
        wtm.setToUser(openId);
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVO), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", "点击查看案件结论报告"));
        return wtm;
    }

    private String buildBackClaimFinishReportDetailUrl(int claimId) {
        try {
            return String.format(wechatConfig.getBackJumpUrl(), URLEncoder.encode(wechatConfig.getBackClaimFinishReportUrl() + "?claimId=" + claimId, StandardCharsets.UTF_8.name()), "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

}
