package com.cfpamf.ms.insur.weixin.dao.safes;

import com.cfpamf.ms.insur.admin.pojo.dto.SmProductCoverageDiscountDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.PremiumFlow;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.*;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupApplicant;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupInsured;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 小额保险
 *
 * <AUTHOR>
 */
@Mapper
public interface PolicyMapper {

    /**
     * 查询保险产品详情
     * @param id
     * @return
     */
    ProductDTO getProductDetailById(@Param("id") int id);

    /**
     * 查询保险计划列表
     * @param productId
     * @param regionName
     * @return
     */
    List<PlanDTO> getPlans(@Param("productId")int productId, @Param("regionName")String regionName);

    /**
     * 查询保险计划列表
     * @param productId
     * @param productId
     * @return
     */
    List<PlanDTO> getPlansByProduct(@Param("productId")int productId);

    /**
     * 查询保险保障列表
     * @param productId
     * @param productId
     * @return
     */
    List<CoverageVo> getCoverage(@Param("productId")int productId);

    /**
     * 查询保险保障额度列表
     * @param productId
     * @param productId
     * @return
     */
    List<CoverageAmountVo> getCoverageAmounts(@Param("productId")int productId);

    /**
     * 查询保险保障-责任因子
     * @param productId
     * @param productId
     * @return
     */
    List<DutyFactorFlowDTO> getDutyFactorFlows(@Param("productId")int productId);

    /**
     * 查询投保告知书
     * @param productId
     * @param productId
     * @return
     */
    String getNotice(@Param("productId")int productId);

    /**
     * 查询人数因子限制
     * @param productId
     * @param productId
     * @return
     */
    List<QuoteLimitDTO> queryQuoteLimit(@Param("productId")int productId);

    /**
     * 查询健康告知内容
     * @param productId
     * @param productId
     * @return
     */
    List<QuestionVo> queryHealth(@Param("productId")int productId);

    /**
     * 查询特约信息
     * @param productId
     * @param productId
     * @return
     */
    List<ClauseVo> queryProductClausesByProductId(@Param("productId")int productId);

    /**
     * 查询产品条款内容
     * @param productId
     * @param productId
     * @return
     */
    String getProductClauseContent(@Param("productId")int productId);

    /**
     * 查询自主确认文件内容
     * @param productId
     * @param productId
     * @return
     */
    List<SelfConfirmDTO> querySelfConfirm(@Param("productId")int productId);

    /**
     * 查询保费
     * ★此处不太适合用缓存，后续系统拆分时再优化
     * @param productId
     * @return
     */
//    @Cacheable(value = "product_premiumlist",key = "#productId+':'+#planId+':'+#ocpGroup")
    List<PremiumDTO> queryPremium(@Param("productId")Integer productId,@Param("planId")Integer planId,@Param("ocpGroup")String ocpGroup);

    List<PremiumDTO> queryPremiumByPlan(@Param("productId")Integer productId,@Param("planId")Integer planId);

    /**
     * 查询人数折扣
     * ★此处不太适合用缓存，后续系统拆分时再优化
     * @param productId
     * @return
     */
//    @Cacheable(value = "product_discountlist")
    @Select(" SELECT * FROM sm_product_coverage_discount where productId=#{productId} AND enabled_flag=0 ")
    List<SmProductCoverageDiscountDTO> queryDiscount(@Param("productId")Integer productId);

    /**
     * ★此处不太适合用缓存，后续系统拆分时再优化
     * @param productId
     * @param code
     * @return
     */
//    @Cacheable(value = "product_attr")
    @Select(" SELECT attr_val FROM sm_product_attr WHERE product_id=#{productId} and attr_code=#{code} ")
    String getProductAttr(@Param("productId")Integer productId, @Param("code")String code);

    @Select(" SELECT attr_code as attrCode,attr_val as attrVal FROM sm_product_attr WHERE product_id=#{productId} AND enabled_flag=0")
    List<ProductAttrDTO> listProductAttr(@Param("productId")Integer productId);

    CoverageVo queryCoverageById(@Param("cvgId")String cvgId);

    EndorPolicyInfo queryPolicyInfo(@Param("policyNo")String policyNo, @Param("orderId")String orderId);

    EndorPolicyInfo queryPolicyByVo(OrderDetailQuery query);

    @Select(" SELECT * FROM sm_order_product WHERE order_id=#{orderId}")
    OrderProductDTO queryOrderProduct(@Param("orderId")String orderId);

    EndorConfig queryEndorConfig(@Param("productId") String productId);

    EndorConfig queryEndorConfigByOrder(@Param("orderId") String orderId);

    List<String> queryJobList(@Param("orderId")String orderId, @Param("policyNo")String policyNo);

    /**
     * 只能用原单的订单Id查询，不能用批改单的订单Id查询
     * <p>把当前保单号对应的在保人员查询出来</p>
     * <p>或者用保单号查询</p>
     * @param query
     * @return
     */
    List<InsuredListDTO> queryInsureds(OrderDetailQuery query);


    /**
     * 只能用原单的被保人列表
     * @param rawOrderId
     * @return
     */
    List<InsuredListDTO> queryRawInsureds(@Param("orderId")String rawOrderId);

    List<EndorListDTO> getEndorList(@Param("orderId")String orderId, @Param("policyNo")String policyNo);

    List<EndorListDTO> getEndorList4Invoice(@Param("channel")String channel, @Param("policyNo")String policyNo, @Param("invoiceStatus")String invoiceStatus);

    EndorListDTO getEndorRawOrder(OrderDetailQuery query);

    EndorListDTO getInvoiceRawOrder(OrderDetailQuery query);

    Integer checkPermission(OrderDetailQuery query);

    /**
     * 该列表是在批改完全结束后可用，要在定时任务跑完消息才有数据
     * @param policyNo
     * @param endorNo
     * @return
     */
    List<InsuredListDTO> queryEndorInsuredList(@Param("policyNo")String policyNo, @Param("endorNo")String endorNo);

    GroupApplicant queryApplicant(@Param("orderId")String orderId);

    /**
     * 查询批改信息
     * @param query
     * @return
     */
    EndorDTO queryEndorInfo(OrderDetailQuery query);

    ProductDTO getProductDetailByOrderId(@Param("orderId") String fhOrderId);

    int copyInsured(@Param("orderId") String orderId,@Param("data") GroupInsured insured);

    int copyInsuredItem(@Param("data") GroupInsured insured);
}
