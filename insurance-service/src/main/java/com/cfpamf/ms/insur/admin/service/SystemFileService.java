package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.aliyun.oss.OSS;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.insur.admin.dao.safes.SystemFileMapper;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.util.FileUtils;
import com.cfpamf.ms.insur.admin.pojo.dto.SystemFileDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.product.SortDTO;
import com.cfpamf.ms.insur.admin.pojo.query.SystemFileQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SystemFileVO;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.base.util.FileUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import com.google.common.collect.Maps;
import com.zhongan.filegateway.common.FileUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.File;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统文件service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class SystemFileService {

    /**
     * 文件夹类型
     */
    private final static String FILE_TYPE_FOLDER = "0";
    /**
     * 系统文件Mapper
     */
    @Autowired
    private SystemFileMapper sfMapper;

    /**
     * 微信代理service
     */
    @Lazy
    @Autowired
    private WxMpServiceProxy serviceProxy;

    /**
     * 查询系统文件
     *
     * @param query
     * @return
     */
    public List<SystemFileVO> getSystemFiles(SystemFileQuery query) {
        return sfMapper.listSystemFiles(query);
    }

    /**
     * 通过主键查询系统文件
     *
     * @param fileId
     * @return
     */
    public SystemFileVO getSystemFileById(int fileId) {
        return sfMapper.getSystemFileById(fileId);
    }

    /**
     * 增加文件
     *
     * @param dto
     * @return
     */
    public void addFile(SystemFileDTO dto) {
        checkFileNameNotRepeart(dto);
        String userId = HttpRequestUtil.getUserId();
        dto.setCreateBy(userId);
        dto.setUpdateBy(userId);
        sfMapper.insertSystemFile(dto);
    }

    /**
     * 修改文件名称
     *
     * @param dto
     * @return
     */
    public void reNameFile(SystemFileDTO dto) {
        checkFileNameNotRepeart(dto);
        SystemFileVO oldFile = getSystemFileById(dto.getFileId());
        // 重命名目录名称修复所有子文件目录
        if (Objects.equals(oldFile.getFileType(), FILE_TYPE_FOLDER)) {
            // 旧路径
            String oldFilePath = oldFile.getFilePath() + "/" + oldFile.getFileName();
            // 就路径所有文件
            List<SystemFileVO> subFiles = sfMapper.listFolderSystemFiles(oldFilePath);
            subFiles.forEach(sf -> {
                // 新路径
                String newFilePath = oldFile.getFilePath() + "/" + dto.getFileName();
                if (!Objects.equals(sf.getFilePath(), oldFilePath)) {
                    newFilePath = newFilePath + sf.getFilePath().substring(oldFilePath.length());
                }
                sfMapper.updateSystemFilePath(sf.getFileId(), newFilePath);
            });
        }
        dto.setUpdateBy(HttpRequestUtil.getUserId());
        sfMapper.updateSystemFileName(dto);
    }

    /**
     * 删除系统文件
     *
     * @param fileIds
     */
    public void deleteSystemFile(List<Integer> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return;
        }
        sfMapper.deleteSystemFile(fileIds);
    }

    /**
     * 文件上传
     *
     * @param multipartFile
     * @return
     */
    public String uploadFile(MultipartFile multipartFile) {
        OSS client = AliYunOssUtil.getOSSClient();
        // 文件位置  image/时间戳/uuid/文件名
        String relativePath = AliYunOssUtil.SUB_BUCKET_IMAGE + BaseConstants.STR_LABEL_SLASH + System.currentTimeMillis() + BaseConstants.STR_LABEL_SLASH +
                UUID.randomUUID().toString().replaceAll("-", "") +
                BaseConstants.STR_LABEL_SLASH + multipartFile.getOriginalFilename();
        String aliETagName = AliYunOssUtil.uploadObject2OSS(client, multipartFile, relativePath);
        if (!StringUtils.isEmpty(aliETagName)) {
            return AliYunOssUtil.ACCESS_ENDPOINT + relativePath;
//                    AliYunOssUtil.generatePresignedUri(client, relativePath);
        }
        throw new BizException(ExcptEnum.FILE_UPLOAD_FAIL_501007);
    }

    /**
     * 上传私有文件
     *
     * @param multipartFile
     * @return
     */
    public String uploadFilePrivate(MultipartFile multipartFile) {
        OSS client = AliYunOssUtil.getOSSClient();
        // 文件位置  image/时间戳/uuid/文件名
        String relativePath = AliYunOssUtil.SUB_BUCKET_IMAGE + BaseConstants.STR_LABEL_SLASH + System.currentTimeMillis() + BaseConstants.STR_LABEL_SLASH +
                UUID.randomUUID().toString().replaceAll("-", "") +
                BaseConstants.STR_LABEL_SLASH + multipartFile.getOriginalFilename();
        String aliETagName = AliYunOssUtil.uploadObject2OSS(client, multipartFile, relativePath, false);
        if (!StringUtils.isEmpty(aliETagName)) {
            return AliYunOssUtil.generatePresignedUri5M(client, relativePath);
        }
        throw new BizException(ExcptEnum.FILE_UPLOAD_FAIL_501007);
    }

    /**
     * 上传私有文件（文件是微信mediaId，需重新下载）
     *
     * @param mediaIds
     * @return
     */
    public List<String> uploadMediaIdFilePrivate(List<String> mediaIds) {
        List<String> reList = new ArrayList<>(mediaIds.size());
        for (String mediaId : mediaIds) {
            File file = serviceProxy.mediaDownload(mediaId);
            if (file == null) {
                continue;
            }
            if (FileUtil.isStartWithWxgf(file)) {
                throw new BizException("", "请选择原图重新上传！");
            }
            FileItem fileItem = AliYunOssUtil.createFileItem(file, "media.jpg");
            MultipartFile mfile = new CommonsMultipartFile(fileItem);
            OSS client = AliYunOssUtil.getOSSClient();
            // 文件位置  image/时间戳/uuid/文件名
            String relativePath = AliYunOssUtil.SUB_BUCKET_IMAGE + BaseConstants.STR_LABEL_SLASH + System.currentTimeMillis() + BaseConstants.STR_LABEL_SLASH +
                    UUID.randomUUID().toString().replaceAll("-", "") +
                    BaseConstants.STR_LABEL_SLASH + mfile.getOriginalFilename();
            String aliETagName = AliYunOssUtil.uploadObject2OSS(client, mfile, relativePath, false);
            if (!StringUtils.isEmpty(aliETagName)) {
                String url = AliYunOssUtil.generatePresignedUri5M(client, relativePath);
                if (!StringUtils.isEmpty(url)) {
                    reList.add(url);
                }
            }
        }
        return reList;
    }

    /**
     * 重新为一组url生成可用的连接
     *
     * @param url
     * @return
     */
    public List<String> reGenerateUrl4Expired(List<String> url) {
        if (CollectionUtils.isEmpty(url)) {
            return url;
        }
        return url.stream().map(this::reGenerateUrl4Expired).collect(Collectors.toList());
    }

    /**
     * 重新生成一个30分钟可用的连接
     *
     * @param url
     * @return
     */
    public String reGenerateUrl4Expired(String url) {
        if (UrlUtils.isAbsoluteUrl(url)) {
            //去掉path 中的 第一个/
            String substring = URI.create(url).getPath().substring(1);
            return AliYunOssUtil.generatePresignedUri5M(substring);
        }
        return AliYunOssUtil.generatePresignedUri5M(url);
    }

    /**
     * 检查文件名称不重复
     *
     * @param dto
     */
    private void checkFileNameNotRepeart(SystemFileDTO dto) {
        SystemFileQuery query = new SystemFileQuery();
        query.setFileFullName(dto.getFileName());
        List<SystemFileVO> existFiles = sfMapper.listSystemFiles(query)
                .stream()
                .filter(sf -> Objects.equals(sf.getFilePath(), dto.getFilePath()))
                .collect(Collectors.toList());
        if (existFiles.isEmpty()) {
            return;
        }
        if (dto.getFileId() == null
                || !Objects.equals(dto.getFileId(), existFiles.get(0).getFileId())) {
            throw new BizException(ExcptEnum.FILE_NAME_DUPLICATE);
        }
    }

    public void updateSort(List<SortDTO> sortDTOList) {
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(sortDTOList)){
            return;
        }
        sfMapper.updateSort(sortDTOList);
    }



    @Autowired
    private ZaApiService zaApiService;
    /**
     * 上传私有文件
     *
     * @param multipartFile
     * @return
     */
    public Map<String, String> uploadFilePrivateAndZa(MultipartFile multipartFile) {
        OSS client = AliYunOssUtil.getOSSClient();
        // 文件位置  image/时间戳/uuid/文件名
        String relativePath = AliYunOssUtil.SUB_BUCKET_IMAGE + BaseConstants.STR_LABEL_SLASH + System.currentTimeMillis() + BaseConstants.STR_LABEL_SLASH +
                UUID.randomUUID().toString().replaceAll("-", "") +
                BaseConstants.STR_LABEL_SLASH + multipartFile.getOriginalFilename();
        String aliETagName = AliYunOssUtil.uploadObject2OSS(client, multipartFile, relativePath, false);
        String url = AliYunOssUtil.generatePresignedUri(client, relativePath);
        Map<String, String> result = Maps.newHashMap();
        //线程上传一下
        File file = FileUtils.downNetImage(url, IdGenerator.getUuid(), ".jpg");
        //上传到众安文件服务
        if (Objects.nonNull(file)) {
            FileUploadResponse response = zaApiService.uploadFile(file.getPath());
            if (Objects.isNull(response.getData()) || !Objects.equals(response.getCode(), "0000")) {
                throw new BizException("", "文件上传到众安网关失败");
            }
            result.put("zaKey", response.getData());

        }
        if (!StringUtils.isEmpty(aliETagName)) {
            result.put("fileUrl", AliYunOssUtil.generatePresignedUri5M(client, relativePath));
        } else {
            throw new BizException(ExcptEnum.FILE_UPLOAD_FAIL_501007);
        }

        return result;
    }

    /**
     * 上传私有文件（文件是微信mediaId，需重新下载）
     *
     * @param mediaIds
     * @return
     */
    public List<Map<String, String>> uploadMediaIdFilePrivateToZa(List<String> mediaIds) {
        List<String> reList = new ArrayList<>(mediaIds.size());
        List<Map<String, String>> resultList = Lists.newArrayList();
        for (String mediaId : mediaIds) {
            Map<String, String> map = Maps.newHashMap();
            File file = serviceProxy.mediaDownload(mediaId);
            if (file == null) {
                continue;
            }
            if (FileUtil.isStartWithWxgf(file)) {
                throw new BizException("", "请选择原图重新上传！");
            }
            FileItem fileItem = AliYunOssUtil.createFileItem(file, "media.jpg");
            MultipartFile mfile = new CommonsMultipartFile(fileItem);
            OSS client = AliYunOssUtil.getOSSClient();
            // 文件位置  image/时间戳/uuid/文件名
            String relativePath = AliYunOssUtil.SUB_BUCKET_IMAGE + BaseConstants.STR_LABEL_SLASH + System.currentTimeMillis() + BaseConstants.STR_LABEL_SLASH +
                    UUID.randomUUID().toString().replaceAll("-", "") +
                    BaseConstants.STR_LABEL_SLASH + mfile.getOriginalFilename();
            String aliETagName = AliYunOssUtil.uploadObject2OSS(client, mfile, relativePath, false);

            if (Objects.nonNull(file)) {
                FileUploadResponse response = zaApiService.uploadFile(file.getPath());
                if (Objects.isNull(response.getData()) || !Objects.equals(response.getCode(), "0000")) {
                    throw new BizException("", "文件上传到众安网关失败");
                }
                map.put("zaKey", response.getData());

            }

            if (!StringUtils.isEmpty(aliETagName)) {
                String url = AliYunOssUtil.generatePresignedUri5M(client, relativePath);
                if (!StringUtils.isEmpty(url)) {
                    map.put("fileUrl", AliYunOssUtil.generatePresignedUri5M(client, relativePath));
                }
            }
            resultList.add(map);
        }
        return resultList;
    }

}
