package com.cfpamf.ms.insur.admin.renewal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 待续保单信息
 *
 * <AUTHOR>
 * @date 2021/5/13 16:13
 */
@Data
public class WaitedPolicyVo {

    @ApiModelProperty("保单失效日期")
    private LocalDateTime endTime;

    @ApiModelProperty("投/被保人身份证")
    private String idNumber;

    /** begin add by zhangjian 20250428***/

    @ApiModelProperty("保单生效日期")
    private LocalDateTime startTime;

    @ApiModelProperty("开始时间")
    private LocalDateTime startDate;

    @ApiModelProperty("结束时间")
    private LocalDateTime endDate;

    @ApiModelProperty("险种分类名称")
    private String riskCategory2;
    /** begin add by zhangjian 20250428***/

}
