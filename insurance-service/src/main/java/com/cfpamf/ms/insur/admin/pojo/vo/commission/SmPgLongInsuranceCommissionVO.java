package com.cfpamf.ms.insur.admin.pojo.vo.commission;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.enums.EnumInsuredAppStatus;
import com.cfpamf.ms.insur.admin.enums.EnumSurrenderType;
import com.cfpamf.ms.insur.admin.enums.commission.CommissionConfigTypeEnum;
import com.cfpamf.ms.insur.admin.enums.order.OrderVisitResultEnum;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.RiskCommissionInfoDTO;
import com.cfpamf.ms.insur.base.annotation.ExportField;
import io.swagger.annotations.ApiModelProperty;
import jdk.nashorn.internal.ir.annotations.Ignore;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 长险提成列表视图对象
 *
 * <AUTHOR>
 * @date 2022/4/20 10:57
 */
@Data
public class SmPgLongInsuranceCommissionVO {

    @ApiModelProperty(value = "续期期数")
    @ExportField(name = "投保期数", order = 5)
    private Integer termNum;

    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    private String appStatus;

    /**
     * 投保产品名称
     */
    @ExportField(name = "投保产品名称",order = 3)
    @ApiModelProperty(value = "投保产品名称")
    private String productName;

    /**
     * 保障年限 s64 增加
     */
    @ExportField(name = "保障年限", order = 4)
    @ApiModelProperty(value = "保障年限")
    private String validPeriod;



    @ApiModelProperty(value = "产品id")
    private Integer productId;

    /**
     * 保单号
     */
    @ExportField(name = "保单号", order = 6)
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 缴费年限
     */
    @ExportField(name = "缴费年限", order = 7)
    @ApiModelProperty(value = "缴费年限")
    private String payPeriod;

    /**
     * 保险公司名称
     */
    @ExportField(name = "保险公司名称", order = 8)
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "产品渠道")
    @ExportField(name = "产品渠道", order = 9)
    private String channel;


    /**
     * 被保人姓名
     */
    @ExportField(name = "被保人姓名", order = 11)
    @ApiModelProperty(value = "被保人姓名")
    private String insuredPersonName;


    /**
     * 被保人身份证号
     */
    @ApiModelProperty(value = "被保人身份证号")
    private String insuredIdNumber;


    /**
     * 投保人姓名
     */
    @ExportField(name = "投保人姓名", order = 10)
    @ApiModelProperty(value = "投保人姓名")
    private String applicantPersonName;

    /**
     * 订单金额
     */
    @ExportField(name = "订单金额", order = 12, type = "money")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 字段名称 险种保费
     */
    @ApiModelProperty(value = "原订单金额")
    BigDecimal originalAmount;

    /**
     * 折算保费
     */
    @ApiModelProperty(value = "折算保费")
    @ExportField(name = "折算保费", order = 13, type = "money")
    private BigDecimal convertedAmount;

    @ApiModelProperty("任务保费")
    private BigDecimal taskPremium;


    /**
     * 结算佣金
     */
    @ApiModelProperty(value = "结算佣金")
    @ExportField(name = "结算佣金", order = 15, type = "money")
    private BigDecimal settlementCommission;

    @ApiModelProperty(value = "支付佣金")
    @ExportField(name = "基础佣金", order = 16, type = "money")
    private BigDecimal payCommissionAmount;

    /**
     * 加佣金额
     */
    @ApiModelProperty(value = "加佣金额")
    @ExportField(name = "加佣金额", order = 17, type = "money")
    private BigDecimal addCommissionAmount;

    @ApiModelProperty(value = "支付总佣金")
    @ExportField(name = "支付总佣金", order = 17, type = "money")
    private BigDecimal totalPayCommissionAmount;
    /**
     * 推荐人所属区域
     */
    @ExportField(name = "推荐人所属区域", order = 18)
    @ApiModelProperty(value = "所属区域")
    private String recommendRegionName;

    /**
     * 推荐人所属机构
     */
    @ExportField(name = "推荐人所属机构", order = 19)
    @ApiModelProperty(value = "所属机构")
    private String recommendOrganizationName;

    @ApiModelProperty("支付总佣金")
    public BigDecimal getPaytotalCommissionAmount() {
        return getAddCommissionAmount().add(Objects.isNull(getPayCommissionAmount()) ? BigDecimal.ZERO : getPayCommissionAmount());
    }

    /**
     * 推荐人姓名
     */
    @ExportField(name = "推荐人姓名", order = 20)
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendUserName;

    @ApiModelProperty(value = "推荐人工号")
    @ExportField(name = "推荐人工号", order = 20)
    private String recommendId;

    @ApiModelProperty("是否宣讲会订单")
    private Boolean talkOrder;

    @ApiModelProperty("宣讲会订单-excel导出字段")
    private String talkOrderExcelField;

    public String getTalkOrderExcelField() {
        return Objects.equals(talkOrder, Boolean.TRUE) ? "是" : "否";
    }

    @ExportField(name = "邀约人姓名", order = 22)
    @ApiModelProperty("邀约人姓名")
    private String inviteName;


    /**
     * 订单号
     */
    @ExportField(name = "订单号", order = 23)
    @ApiModelProperty(value = "订单号")
    private String fhOrderId;

    /**
     * 订单创建日期
     */
    @ExportField(name = "订单创建日期", type = "dateTime")
    @ApiModelProperty(value = "订单创建日期")
    private Date createTime;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    @ExportField(name = "支付时间", order = 1, type = "dateTime")
    private Date paymentTime;

    /**
     * 记账时间
     */
    @ApiModelProperty(value = "记账时间")
    @ExportField(name = "记账时间", order = 2, type = "dateTime")
    private Date accountTime;

    @ApiModelProperty("管护客户经理")
    @ExportField(name = "管护客户经理", order = 24)
    private String customerAdminName;

    @ApiModelProperty("管护客户经理工号")
    @ExportField(name = "管护客户经理工号", order = 25)
    private String customerAdminId;

    /**
     * 业务上级
     */
    @ApiModelProperty("业务上级")
    @ExportField(name = "业务上级", order = 25)
    private String userMasterName;

    /**
     * 直线经理
     */
    @ApiModelProperty("直线经理")
    @ExportField(name = "直线经理", order = 26)
    private String userAdminName;

    @ApiModelProperty("保司对账结果")
    private String reconciliationResult;

    @ApiModelProperty("备注-excel导出字段")
    @ExportField(name = "备注", order = 28)
    @Ignore
    private String remarkExcel;

    public String getRemarkExcel() {
        return EnumInsuredAppStatus.getDescByCode(appStatus);
    }

    @ApiModelProperty("支付佣金险种信息")
    private String paymentRiskJson;

    public String getPaymentRiskJson() {
        if (StringUtils.isEmpty(paymentRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(paymentRiskJson, CommissionConfigTypeEnum.PAYMENT.getCode());
    }


    @ApiModelProperty("结算佣金险种信息")
    private String settlementRiskJson;

    public String getSettlementRiskJson() {
        if (StringUtils.isEmpty(settlementRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(settlementRiskJson, CommissionConfigTypeEnum.SETTLEMENT.getCode());
    }


    @ApiModelProperty("折算保费险种信息")
    private String conversionRiskJson;

    public String getConversionRiskJson() {
        if (StringUtils.isEmpty(conversionRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(conversionRiskJson, CommissionConfigTypeEnum.CONVERSION.getCode());
    }

    @ApiModelProperty("回访状态")
    @ExportField(name = "回访状态", order = 32)
    private String visitStatus;

    public String getVisitStatus(){
        if (StringUtils.isEmpty(visitStatus)){
            return null;
        }
        return OrderVisitResultEnum.dict(visitStatus);
    }

    @ApiModelProperty("退保类型")
    @ExportField(name = "是否犹豫期退保", order = 33)
    private String surrenderType;

    public String getSurrenderType(){

        if(appStatus.equals(EnumInsuredAppStatus.SUCCESS.getCode())) {
            return null;
        }else{
            if (StringUtils.isEmpty(surrenderType) || surrenderType.equals(EnumSurrenderType.FIRE_CONTRACT.getCode())){
                return "否";
            }

            if (surrenderType.equals(EnumSurrenderType.DEAL_SURRENDER.getCode())){
                return "否("+EnumSurrenderType.dict(surrenderType)+")";
            }

            return "是";
        }
    }

    @ApiModelProperty("回访日期")
    @ExportField(name = "回访日期", order = 34)
    private LocalDateTime visitTime;

    @ApiModelProperty("回执日期")
    @ExportField(name = "回执日期", order = 35)
    private String receiptSignTime;


    @ApiModelProperty(value = "活动编码")
    @ExportField(name = "活动编码", order = 36)
    private String activityCode;

    @ApiModelProperty(value = "是否整村推进")
    @ExportField(name = "是否整村推进", order = 37)
    private String villageActivity;

    @ApiModelProperty(value = "类型")
    private String type;
    @ApiModelProperty(value = "当月异业相关  1 是，0否")
    @ExportField(name = "当月异业相关", order = 38)
    private String loanMonth;

    @ApiModelProperty(value = "当年异业相关  1 是，0否")
    @ExportField(name = "当年异业相关", order = 39)
    private String loanYear;

    @ApiModelProperty(value = "3天复购  1 是，0否")
    @ExportField(name = "是否三天复购", order = 40)
    private String buyback3d;

    @ApiModelProperty(value = "是否自保件  1 是，0否")
    @ExportField(name = "是否自保件", order = 41)
    private String isLabel;

    public String getIsLabel() {
        if(Objects.equals(isLabel, "0")){
            return "否";
        }else {
            return "是";
        }
    }
    public String getLoanMonth() {
        if(Objects.equals(loanMonth, "0")){
            return "否";
        }else {
            return "是";
        }
    }

    public String getLoanYear() {
        if(Objects.equals(loanYear, "0")){
            return "否";
        }else {
            return "是";
        }
    }

    public String getBuyback3d() {
        if(Objects.equals(buyback3d, "0")){
            return "否";
        }else {
            return "是";
        }
    }

    public String getVillageActivity() {
        if(villageActivity != null && !villageActivity.isEmpty() && type != null && type == "100"){
            return "是";
        }else {
            return "否";
        }
    }

    /**
     * 计算加佣金额
     *
     * @return
     */
    @ApiModelProperty("加佣奖励")
    public BigDecimal getAddCommissionAmount() {
        return Objects.isNull(addCommissionAmount) ? BigDecimal.ZERO : addCommissionAmount;
    }

    /**
     * 计算加佣比例
     *
     * @return
     */
    @ApiModelProperty("加佣比例")
    public BigDecimal getAddCommissionProportion() {
        //if(Objects.nonNull(getTotalAmount()) && getTotalAmount().compareTo(BigDecimal.ZERO) != 0 ){
        if(Objects.nonNull(getTotalAmount()) && BigDecimal.ZERO.compareTo(getTotalAmount()) != 0){
            return getAddCommissionAmount().divide(getTotalAmount(),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        return BigDecimal.ZERO;
    }

    public static String riskCommissionJsonToStr(String riskJson,Integer commissionType) {
        List<RiskCommissionInfoDTO> list = JSONArray.parseArray(riskJson, RiskCommissionInfoDTO.class);
        String str = "";
        if(CollectionUtils.isEmpty(list)){
            return "";
        }
        for (int i=0;i<list.size();i++){
            RiskCommissionInfoDTO dto =  list.get(i);
            str += dto.toShowMsg(i+1,commissionType) + ";";
        }
        return str;
    }

    public BigDecimal getTotalPayCommissionAmount() {
        if (payCommissionAmount == null) {
            return addCommissionAmount;
        }
        if (addCommissionAmount == null) {
            return payCommissionAmount;
        }
        return addCommissionAmount.add(payCommissionAmount);
    }
}
