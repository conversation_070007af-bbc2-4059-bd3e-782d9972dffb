package com.cfpamf.ms.insur.admin.service.order.group;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.external.zhongan.model.enums.ZaGroupPolicyStatusEnum;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupEndorsementInfo;
import com.cfpamf.ms.insur.admin.external.zhongan.model.group.ZaGroupInsuredInfo;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupNotifyService;
import com.cfpamf.ms.insur.admin.service.correct.GroupHelper;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GroupRuleHelper {

    public final String ADD_SUBTRACT_CHECK = "policy:correct:add_subtract:check:";

    @Autowired
    private SmOrderGroupNotifyService groupNotifyService;

    @Autowired
    private TkEmployerQueryService tkEmployerQueryService;

    @Autowired
    private SmOrderItemMapper orderItemMapper;

    @Autowired
    private RedisUtil<String,String> redisUtil;

    public void setAddSubtractCheck(String endorsementNo,String type){
        String key = ADD_SUBTRACT_CHECK+endorsementNo;
        if(Objects.equals(type,"R")){
            redisUtil.set(key,"-1");
        }
        if(Objects.equals(type,"A")){
            redisUtil.remove(key);
        }
    }

    public boolean nCheck(String endorsementNo){
        String key = ADD_SUBTRACT_CHECK + endorsementNo;
        String v = redisUtil.get(key);
        return Objects.equals(v,"-1");
    }

    /**
     * 保单号校验
     * @param policyNo
     */
    public void applyCheck(String policyNo){

        //1.批改单号重复性校验
        int record =orderItemMapper.countPolicyNo(policyNo);
        if(record>0){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"保单号已存在:"+policyNo);
        }
    }

    /**
     * 团险-增减员规则校验
     * @param param
     */
    public void addSubtractCheck(ZaGroupEndorsementInfo param){
        log.info("开始校验增减员数据:{}", JSON.toJSONString(param));
        String policyNo = param.getPolicyNo();
        String endorsementNo = param.getEndorsementNo();

        if(nCheck(endorsementNo)){
            return;
        }

        //1.批改单号重复性校验
        int record =orderItemMapper.countEndorsementNo(policyNo,endorsementNo);
        if(record>0){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"批改单号已存在:"+endorsementNo);
        }
        //2.被保人重复性校验
        List<ZaGroupInsuredInfo> insuredList = param.getInsuredList();
        if(CollectionUtils.isEmpty(insuredList)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"增减员列表为空");
        }

        List<String> idCardList = insuredList.stream().map(ZaGroupInsuredInfo::getCertNo).collect(Collectors.toList());

        List<SmOrderItem> orderItemList = orderItemMapper.listItemByPolicyNo(policyNo,idCardList);

        orderItemList.sort((a,b)->{
            String orderId1 = a.getFhOrderId();
            String orderId2 = a.getFhOrderId();

            Integer seq1 = GroupHelper.extractBatchNo(orderId1);
            Integer seq2 = GroupHelper.extractBatchNo(orderId2);
            return seq1-seq2;
        });
        Map<String,SmOrderItem> orderItemMap = new HashMap<>();
        for(SmOrderItem order:orderItemList){
            String idNumber = order.getIdNumber().toUpperCase();
            String appStatus = order.getAppStatus();
            if(Objects.equals(appStatus, SmConstants.POLICY_STATUS_SUCCESS)){
                orderItemMap.put(idNumber,order);
            }
            else if(Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)){
                orderItemMap.remove(idNumber);
            }
        }

        for(ZaGroupInsuredInfo insured:insuredList){
            Integer handleType = insured.getIndividualStatus();
            String idCard = insured.getCertNo().toUpperCase();

            if(Objects.equals(handleType, ZaGroupPolicyStatusEnum.INFORCE.getCode())){
                if(orderItemMap.containsKey(idCard)){
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"被保人已存在，不能重复投保:"+idCard);
                }
            }
            else {
                if(!orderItemMap.containsKey(idCard)){
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"该证件号未在保单被保人清单中:"+idCard);
                }
            }
        }

    }

    /**
     * 泰康雇主责任险
     * 增减员校验
     * @param param
     */
    public void addSubtractCheck4TkEmployer(ZaGroupEndorsementInfo param){
        String policyNo = param.getPolicyNo();
        String endorsementNo = param.getEndorsementNo();

        if(nCheck(endorsementNo)){
            return;
        }

        //1.批改单号重复性校验
        int record =orderItemMapper.countEndorsementNo(policyNo,endorsementNo);
        if(record>0){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"批改单号已存在:"+endorsementNo);
        }

        //2.被保人互斥性校验
        List<ZaGroupInsuredInfo> insuredList = param.getInsuredList();
        if(CollectionUtils.isEmpty(insuredList)){
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"增减员列表为空");
        }

        List<String> idCardList = insuredList.stream().map(ZaGroupInsuredInfo::getCertNo).collect(Collectors.toList());

        Map<String,SmOrderItem> insuredItemMap = tkEmployerQueryService.queryInsuredItemMap4TkEmployer(policyNo,param.getCorrectTimestamp(),idCardList);

        for(ZaGroupInsuredInfo insured:insuredList){
            Integer handleType = insured.getIndividualStatus();
            String idCard = insured.getCertNo().toUpperCase();

            if(Objects.equals(handleType, ZaGroupPolicyStatusEnum.INFORCE.getCode())){
                if(insuredItemMap.containsKey(idCard)){
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"被保人已存在，不能重复投保:"+idCard);
                }
            }
            else {
                if(!insuredItemMap.containsKey(idCard)){
                    throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),"该证件号未在保单被保人清单中:"+idCard);
                }
            }
        }
    }


}
