package com.cfpamf.ms.insur.commission.converter;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadDetail;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadFile;
import com.cfpamf.ms.insur.commission.domain.CommissionUploadMonth;
import com.cfpamf.ms.insur.commission.po.CommissionUploadDetailPO;
import com.cfpamf.ms.insur.commission.po.CommissionUploadFilePO;
import com.cfpamf.ms.insur.commission.po.CommissionUploadMonthPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR> 2022/12/16 15:49
 */
@Mapper(imports = {JSONObject.class, YearMonth.class, Feature.class})
public interface CommissionUploadConverter {
    CommissionUploadConverter INS = Mappers.getMapper(CommissionUploadConverter.class);

    /**
     * po转领域对象
     *
     * @param m
     * @return
     */
    @Mappings({
            @Mapping(target = "commissionMonth", expression = "java(YearMonth.parse(m.getCommissionMonth()))"),
            @Mapping(target = "commissionItem", expression = "java(JSONObject.parseObject(m.getCommissionItem(),Feature.OrderedField))")
    })
    CommissionUploadDetail po2Domain(CommissionUploadDetailPO m);

    /**
     * 领域对象转PO
     *
     * @param m
     * @return
     */
    @Mappings({
            @Mapping(target = "commissionMonth", expression = "java(m.getCommissionMonth().toString())"),
            @Mapping(target = "commissionItem", expression = "java(JSONObject.toJSONString(m.getCommissionItem()))")
    })
    CommissionUploadDetailPO domain2Po(CommissionUploadDetail m);

    /**
     * 领域对象转PO
     *
     * @param m
     * @return
     */
    CommissionUploadFilePO domainFile2Po(CommissionUploadFile m);


    /**
     * po转领域对象
     *
     * @param m
     * @return
     */
    CommissionUploadFile poFile2domain(CommissionUploadFilePO m);


    /**
     * po转领域对象
     *
     * @param m
     * @return
     */
    List<CommissionUploadFile> poFile2domain(List<CommissionUploadFilePO> m);

    /**
     * 领域对象转PO
     *
     * @param m
     * @return
     */
    List<CommissionUploadDetailPO> domain2Po(List<CommissionUploadDetail> m);

    /**
     * 月份数据转换
     *
     * @param m
     * @return
     */
    List<CommissionUploadMonth> poMonth2Domain(List<CommissionUploadMonthPO> m);

    /**
     * po转领域对象
     *
     * @param m
     * @return
     */
    CommissionUploadMonth poMonth2Domain(CommissionUploadMonthPO m);
}
