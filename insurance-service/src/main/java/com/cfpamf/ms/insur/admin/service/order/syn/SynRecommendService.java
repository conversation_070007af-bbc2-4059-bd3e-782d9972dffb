package com.cfpamf.ms.insur.admin.service.order.syn;

import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.model.ChannelInfo;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.pojo.dto.order.UpdateOrderRecommendDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: yangdonglin
 * @create: 2023/10/31 9:28
 * @description: 同步渠道推荐人
 */
@Service
@Slf4j
public class SynRecommendService {

    @Autowired
    private UserPostService userPostService;
    @Autowired
    private SmOrderMapper smOrderMapper;
    @Autowired
    private WhaleOrderServiceAdapterImpl adapter;
    @Autowired
    private SmOrderInsuredMapper smOrderInsuredMapper;
    @Autowired
    private SmOrderDDDMapper orderMapper;


    /**
     * 更新小鲸最新的客户信息工具
     *
     * @param whaleCodes
     */
    public void whaleSynRecommendIdTool(final List<String> whaleCodes) {
        if (CollectionUtils.isEmpty(whaleCodes)) {
            log.info("合同编号为空{}", whaleCodes);
            XxlJobHelper.log("合同编号为空{}", whaleCodes);
            return;
        }
        for (String code : whaleCodes) {
            try {
                final WhaleResp<WhaleContract> policy = adapter.getPolicyNo(code, "CREATE");
                if (Objects.isNull(policy) || Objects.isNull(policy.getData())) {
                    log.info("查询小鲸返回为空{}", code);
                    XxlJobHelper.log("查询小鲸返回为空{}", code);
                    continue;
                }
                final WhaleContract policyNo = policy.getData();
                if (Objects.isNull(policyNo.getContractBaseInfo()) || StringUtils.isBlank(policyNo.getContractBaseInfo().getPolicyNo())) {
                    log.info("保单信息为空{}", code);
                    XxlJobHelper.log("保单信息为空{}", code);
                    continue;
                }
                String policyNoCode = policyNo.getContractBaseInfo().getPolicyNo();
                //查询被保人信息
                final SmOrderInsured qryInsured = new SmOrderInsured();
                qryInsured.setPolicyNo(policyNoCode);
                final List<String> fhOrderIds = Optional.ofNullable(smOrderInsuredMapper.select(qryInsured))
                        .filter(CollectionUtils::isNotEmpty)
                        .map(e -> e.stream().map(SmOrderInsured::getFhOrderId)
                                .filter(StringUtils::isNotBlank)
                                .distinct()
                                .collect(Collectors.toList())).orElse(new ArrayList<>());
                if (fhOrderIds.size() != 1) {
                    log.info("农保订单信息不唯一{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final String fhOrderId = fhOrderIds.get(0);
                if (StringUtils.isBlank(fhOrderId)) {
                    log.info("农保订单号为空{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final SmOrder smOrder = new SmOrder();
                smOrder.setFhOrderId(fhOrderId);
                final List<SmOrder> smOrders = orderMapper.select(smOrder);
                if (CollectionUtils.isEmpty(smOrders) || smOrders.size() != 1 || Objects.isNull(smOrders.get(0))) {
                    log.info("农保订单信息不唯一{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final SmOrder thisSmOrder = smOrders.get(0);
                if (modifyChannelRecommender(code, policyNo, fhOrderId, thisSmOrder)) {
                    continue;
                }
                log.info("更新成功{}", code);
                XxlJobHelper.log("更新成功{}", code);
            } catch (Exception e) {
                log.info("更新失败{},{}", code, e);
                XxlJobHelper.log("更新失败{},{}", code, e);
            }
        }
    }

    /**
     * 修改渠道推荐人
     * @param code 小鲸合同号
     * @param policyNo 小鲸活动信息
     * @param fhOrderId 订单id
     * @param thisSmOrder 当前订单信息
     * @return
     */
    public boolean modifyChannelRecommender(String code, WhaleContract policyNo, String fhOrderId, SmOrder thisSmOrder) {
        final String newRecommendId = Optional.ofNullable(policyNo.getChannelInfo()).filter(e -> e.getReferrerType() == 0)
                .map(ChannelInfo::getReferrerWno).orElse(null);
        if (StringUtils.isBlank(newRecommendId)) {
            log.info("小鲸渠道推荐人工号为空{}", code);
            XxlJobHelper.log("小鲸渠道推荐人工号为空{}", code);
            return true;
        }
        //查询用户职位信息
        List<UserPost> userPosts = userPostService.listUserPostByJobNumber(newRecommendId);
        if (CollectionUtils.isEmpty(userPosts)) {
            log.info("新推荐人的职位信息不存在{}", code);
            XxlJobHelper.log("新推荐人的职位信息不存在{}", code);
            return true;
        }
        //获取主职
        Optional<UserPost> mainPostOpt = userPosts.stream().filter(p -> Objects.equals(p.getServiceType(), 0)).findFirst();
        if (!mainPostOpt.isPresent()) {
            log.info("新推荐人的职位信息不存在{}", code);
            XxlJobHelper.log("新推荐人的职位信息不存在{}", code);
            return true;
        }
        final UserPost mainPost = mainPostOpt.get();
        UpdateOrderRecommendDTO orderRecommendDTO = new UpdateOrderRecommendDTO();
        orderRecommendDTO.setFhOrderId(fhOrderId);
        orderRecommendDTO.setOldRecommendId(thisSmOrder.getRecommendId());
        orderRecommendDTO.setOldRecommendJobCode(thisSmOrder.getRecommendJobCode());
        orderRecommendDTO.setOldRecommendMainJobNumber(thisSmOrder.getRecommendMainJobNumber());
        orderRecommendDTO.setOldRecommendOrgCode(thisSmOrder.getRecommendOrgCode());
        orderRecommendDTO.setNewRecommendId(newRecommendId);
        orderRecommendDTO.setNewRecommendJobCode(mainPost.getJobCode());
        orderRecommendDTO.setNewRecommendMainJobNumber(mainPost.getMainJobNumber());
        orderRecommendDTO.setNewRecommendOrgCode(mainPost.getOrgCode());
        orderRecommendDTO.setNewRecommendAdminName(mainPost.getUserAdminName());
        orderRecommendDTO.setNewRecommendPostName(mainPost.getPostName());
        orderRecommendDTO.setNewRecommendMasterName(mainPost.getUserMasterName());
        //更新推荐人信息
        smOrderMapper.updateOrderRecommendIdByOrderId(orderRecommendDTO);
        return false;
    }
}
