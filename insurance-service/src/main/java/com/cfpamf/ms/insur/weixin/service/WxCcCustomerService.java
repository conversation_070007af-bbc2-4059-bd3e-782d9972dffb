package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.common.ms.vo.PageVO;
import com.cfpamf.ms.bms.facade.api.BmsUserFacade;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.customer.facade.api.ManageFacade;
import com.cfpamf.ms.customer.facade.request.manage.QueryCustBaseInfoPageWithAuthcReq;
import com.cfpamf.ms.customer.facade.vo.CustDetailVo;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.UserPostMapper;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.vo.CustPropertyVO;
import com.cfpamf.ms.insur.base.bean.SmyPageInfo;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCmsQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCustOrderQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxCustomerQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.WxOrderQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportListVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.util.HttpRequestUtil.getReferer;
import static com.cfpamf.ms.insur.base.util.HttpRequestUtil.getUserAgent;

/**
 * 微信客户中心/我的客户Service
 **/
@Slf4j
@Service
public class WxCcCustomerService extends WxAbstractService {

    /**
     * 客户类型-保险客户
     */
    private final static String CUSTOMER_TYPE_INS = "insur";
    /**
     * 客户类型-信贷
     */
    private final static String CUSTOMER_TYPE_XINDAI = "xindai";

    /**
     * 客户mapper
     */
    @Autowired
    private CustomerMapper customerMapper;

    /**
     * 订单mapper
     */
    @Autowired
    private WxOrderMapper orderMapper;

    /**
     * 客户中心接口
     */
    @Autowired
    private ManageFacade manageFacade;

    /**
     * 用户mapper
     */
    @Autowired
    private AuthUserMapper authUserMapper;
    @Autowired
    private UserPostMapper userPostMapper;

    @Autowired
    protected SmClaimMapper smClaimMapper;

    @Resource
    private BmsUserFacade bmsUserFacade;

    /**
     * 查询用户微信我的保险用户
     *
     * @param query
     * @return
     */
    public PageInfo<WxCustomerVO> getWxInsurCustomerListByPage(WxCustomerQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());
        if (session.isBindWeixin()) {
            return new PageInfo<>();
        }
        query.setUserId(session.getRoleUniqueId());
        query.setUserType(session.getUserType());
        PageHelper.startPage(query.getPage(), query.getSize());
        PageInfo<WxCustomerVO> pageInfo = new PageInfo<>(customerMapper.listWxCustomer(query));
        if (pageInfo.getList() != null) {
            pageInfo.getList().forEach(c -> {
                if (c != null) {
                    c.setFromXinDai(Boolean.FALSE);
                }
            });
        }
        return pageInfo;
    }

    /**
     * 查询用户微信我的保险用户-日复盘用
     *
     * @param query
     * @return
     */
    public PageInfo<WxCustomerVO> getWxInsurCustomerListRetrospective(WxCustomerQuery query) {
        String authorization = query.getAuthorization();
        String session =HttpRequestUtil.getToken();
        if(session == null || !session.equals(authorization)){
            log.warn("当前登录token失效，或者token不匹配，authorization：{}, session: {}",authorization,session);
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        query.setUserId(query.getUserId());
        query.setUserType(query.getUserType());
        PageHelper.startPage(query.getPage(), query.getSize());
        PageInfo<WxCustomerVO> pageInfo = new PageInfo<>(customerMapper.listWxCustomer(query));
        if (pageInfo.getList() != null) {
            pageInfo.getList().forEach(c -> {
                if (c != null) {
                    c.setFromXinDai(Boolean.FALSE);
                }
            });
        }
        return pageInfo;
    }

    public Object getWxXinDaiCustomer(String custId) {
        return manageFacade.custBaseInfo(custId);
    }

    /**
     * 查询用户微信我的信贷用户
     *
     * @param query
     * @return
     */
    public PageInfo<WxCustomerVO> getWxXinDaiCustomerListByPage(WxCustomerQuery query) {
        WxSessionVO session = checkAuthorityEmployee(query.getOpenId(), query.getAuthorization());
        log.info("session.getUserId()={}", session.getUserId());
        if (!StringUtils.isEmpty(session.getUserId())) {
            WxUserVO wxUserVO = authUserMapper.getUserByUserId(session.getUserId());
            if (!Objects.equals(wxUserVO.getPostCode(), "1081")) {
                PageInfo<WxCustomerVO> page = new PageInfo<>();
                page.setList(Collections.emptyList());
                return page;
            }
        }
        QueryCustBaseInfoPageWithAuthcReq queryReq = new QueryCustBaseInfoPageWithAuthcReq();
        //modify by zhangjian 2020-07-20 客户中心接口变更 入参由以前的兼职工号 -- 》 主职工号加+orgNo ,发版有依赖关系
        //queryReq.setOperator(session.getUserId());
        UserPost userPost = userPostMapper.selectUserPostByJobCode(session.getJobCode());
        log.info("userPost.getMainJobNumber()={},userPost.getOrgCode()={}", userPost.getMainJobNumber(), userPost.getOrgCode());
        queryReq.setOperator(userPost.getMainJobNumber());
        queryReq.setOrgNo(userPost.getOrgCode());

        queryReq.setCustName(query.getKeyword());
        queryReq.setPageNumber(query.getPage());
        queryReq.setPageSize(query.getSize());

        Result<PageVO<CustInfoVo>> pageVOResult = manageFacade.custBaseInfoPageWithAuthc(queryReq);

        log.info("信贷用户列表：[{}]", JSON.toJSONString(pageVOResult));
        PageInfo<WxCustomerVO> pageInfo = new PageInfo<>();

        if (pageVOResult.isSuccess()) {
            PageVO<CustInfoVo> pageResult = pageVOResult.getData();

            pageInfo.setTotal(pageResult.getRowTotal());
            pageInfo.setPageNum(pageResult.getPageNumber());
            pageInfo.setSize(pageResult.getPageSize());
            pageInfo.setHasNextPage(pageResult.getPageTotal() > pageResult.getPageTotal());
            if (pageResult.getRows() == null) {
                pageResult.setRows(Collections.emptyList());
            }
            pageInfo.setList(pageResult.getRows().stream().map(c -> {
                WxCustomerVO wxc = new WxCustomerVO();
                wxc.setCustomerName(c.getCustName());
                wxc.setCustomerId("-1");
                if (Objects.equals(c.getIdType(), "20")) {
                    wxc.setIdType("身份证");
                    wxc.setIdNumber(c.getIdNo());
                    wxc.setCellPhone(c.getMobile());
                    String idNo = IdcardUtils.getBirthByIdCard(c.getIdNo());
                    wxc.setBirthday(idNo.substring(0, 4) + "-" + idNo.substring(4, 6) + "-" + idNo.substring(6, 8));
                    if (c.getCustDetail() != null) {
                        wxc.setAddress(c.getCustDetail().getLiveAddress());
                    }
                }
                CustDetailVo custDetail = c.getCustDetail();
                if (Objects.nonNull(custDetail)) {
                    wxc.setCustomerId(custDetail.getLoanCustId());
                    StringBuilder stringBuilder = new StringBuilder();
                    appendByLong(stringBuilder, custDetail.getProvince());
                    appendByLong(stringBuilder, custDetail.getCity());
                    appendByLong(stringBuilder, custDetail.getCounty());
                    appendByLong(stringBuilder, custDetail.getTown());
                    appendByLong(stringBuilder, custDetail.getVillage());
                    if (stringBuilder.length() > 0) {
                        wxc.setArea(stringBuilder.toString());
                    }
                }
                wxc.setFromXinDai(true);
                return wxc;
            }).collect(Collectors.toList()));
        } else {
            pageInfo.setPageNum(0);
            pageInfo.setSize(0);
            pageInfo.setHasNextPage(false);
            pageInfo.setList(Collections.emptyList());
        }
        return pageInfo;
    }

    private void appendByLong(StringBuilder sb, String str) {
        if (!StringUtils.isEmpty(str)) {
            if (sb.length() > 0) {
                sb.append("|");
            }
            sb.append(str);
        }
    }

    /**
     * 查询用户微信我的信贷用户
     *
     * @param query
     * @return
     */
    public PageInfo<WxCustomerVO> searchCustomerListByPage(WxCustomerQuery query) {
        WxCustomerQuery query2 = JSON.parseObject(JSON.toJSONString(query), WxCustomerQuery.class);
        query2.setPage(1);
        PageInfo<WxCustomerVO> wxCustPage1 = getWxInsurCustomerListByPage(query2);
        List<WxCustomerVO> mergeWxCustList = new ArrayList<>();
        Set<String> wxCustIdNoSet = new HashSet<>();
        wxCustPage1.getList().forEach(cl -> {
            cl.setFromXinDai(false);
            if (!wxCustIdNoSet.contains(cl.getIdNumber())) {
                wxCustIdNoSet.add(cl.getIdNumber());
                mergeWxCustList.add(cl);
            }
        });
        long total = wxCustPage1.getTotal();
        WxSessionVO session = checkAuthority(query.getOpenId(), query.getAuthorization());
        if (session.isBindEmployee()) {
            PageInfo<WxCustomerVO> wxCustPage2 = getWxXinDaiCustomerListByPage(query2);
            wxCustPage2.getList().forEach(cl -> {
                if (!wxCustIdNoSet.contains(cl.getIdNumber())) {
                    wxCustIdNoSet.add(cl.getIdNumber());
                    mergeWxCustList.add(cl);
                }
            });
            total = wxCustPage1.getTotal() + wxCustPage2.getTotal() - (wxCustPage1.getList().size() + wxCustPage2.getList().size() - mergeWxCustList.size());
        }
        PageInfo<WxCustomerVO> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(query.getSize());
        pageInfo.setPageNum(query.getPage());
        pageInfo.setTotal(total);
        pageInfo.setList(mergeWxCustList.stream().skip((query.getPage() - 1) * query.getSize()).limit(query.getSize()).collect(Collectors.toList()));
        pageInfo.setSize(pageInfo.getList().size());
        pageInfo.setHasNextPage(query.getPage() * query.getSize() < mergeWxCustList.size());
        return pageInfo;
    }

    /**
     * 查询用户微信我的信贷用户
     *
     * @param query
     * @return
     */
    public PageInfo<WxCustomerVO> searchCustomerListRetrospective(WxCustomerQuery query) {
        WxCustomerQuery query2 = JSON.parseObject(JSON.toJSONString(query), WxCustomerQuery.class);
        query2.setPage(1);
        PageInfo<WxCustomerVO> wxCustPage1 = getWxInsurCustomerListRetrospective(query2);
        List<WxCustomerVO> mergeWxCustList = new ArrayList<>();
        Set<String> wxCustIdNoSet = new HashSet<>();
        wxCustPage1.getList().forEach(cl -> {
            cl.setFromXinDai(false);
            if (!wxCustIdNoSet.contains(cl.getIdNumber())) {
                wxCustIdNoSet.add(cl.getIdNumber());
                mergeWxCustList.add(cl);
            }
        });
        long total = wxCustPage1.getTotal();
        PageInfo<WxCustomerVO> pageInfo = new PageInfo<>();
        pageInfo.setPageSize(query.getSize());
        pageInfo.setPageNum(query.getPage());
        pageInfo.setTotal(total);
        pageInfo.setList(mergeWxCustList.stream().skip((query.getPage() - 1) * query.getSize()).limit(query.getSize()).collect(Collectors.toList()));
        pageInfo.setSize(pageInfo.getList().size());
        pageInfo.setHasNextPage(query.getPage() * query.getSize() < mergeWxCustList.size());
        return pageInfo;
    }

    /**
     * 查询用户微信我的保险用户基本信息
     *
     * @param idNumber
     * @return
     */
    public WxCustBaseVO getCustomerByIdNumber(String openId, String authorization, String idNumber, String customerType) {
        WxSessionVO sessionVO = checkAuthorityEpAg(openId, authorization);
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            WxCustomerQuery query = new WxCustomerQuery();
            query.setOpenId(openId);
            query.setAuthorization(authorization);
            WxCustBaseVO wxc = customerMapper.getCustomerByIdNumber(idNumber);
            wxc.setCustomerTypes(new ArrayList<>());
            wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);
            query.setKeyword(wxc.getCustomerName());
            if (sessionVO.isBindEmployee()) {
                PageInfo<WxCustomerVO> pageInfo = getWxXinDaiCustomerListByPage(query);
                if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
                    if (pageInfo.getList().stream().anyMatch(c -> Objects.equals(c.getIdNumber(), wxc.getIdNumber())
                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
                    }
                }
            }
            return wxc;
        } else {
            Result<CustInfoVo> result = manageFacade.custBaseInfoByIdNo(idNumber);

            if (result != null && result.isSuccess()) {
                CustInfoVo custInfo = result.getData();
                WxCustBaseVO wxc = new WxCustBaseVO();
                wxc.setAddress(custInfo.getCustDetail().getLiveAddress());
                wxc.setIdType("身份证");
                wxc.setIdNumber(custInfo.getIdNo());
                wxc.setCustomerName(custInfo.getCustName());
                wxc.setCellPhone(custInfo.getMobile());
                wxc.setGender(getGenderByIdCard(custInfo.getIdNo()));
                wxc.setCustomerTypes(new ArrayList<>());
                wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);


                WxCustomerQuery query = new WxCustomerQuery();
                query.setKeyword(custInfo.getCustName());
                query.setUserType(sessionVO.getUserType());
                query.setUserId(sessionVO.getUserId());
                List<WxCustomerVO> customerList = customerMapper.listWxCustomer(query);
                if (customerList != null && !customerList.isEmpty()) {
                    if (customerList.stream().anyMatch(c -> Objects.equals(c.getIdNumber(), idNumber)
                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);
                    }
                }
                return wxc;
            }
            return null;
        }
    }

    /**
     * 查询用户微信我的保险用户基本信息
     *
     * @param id
     * @return
     */
    public WxCustBaseVO getCustomerById(String openId, String authorization, String id, String customerType) {
        WxSessionVO sessionVO = checkAuthorityEpAg(openId, authorization);
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            WxCustBaseVO wxc = customerMapper.getCustomerById(id);
            if (wxc == null) {
                throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
            }
            wxc.setCustomerTypes(new ArrayList<>());
            wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);


            if (sessionVO.isBindEmployee()) {
                WxCustomerQuery query = new WxCustomerQuery();
                query.setOpenId(openId);
                query.setAuthorization(authorization);
                query.setKeyword(wxc.getCustomerName());
                PageInfo<WxCustomerVO> pageInfo = getWxXinDaiCustomerListByPage(query);

                if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
                    if (pageInfo.getList().stream().anyMatch(c -> Objects.equals(c.getIdNumber(), wxc.getIdNumber())
                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
                    }
                }
            }
            return wxc;
        } else {
            Result<CustInfoVo> result = manageFacade.custDetail(id);

            if (result != null && result.isSuccess()) {
                CustInfoVo custInfo = result.getData();
                WxCustBaseVO wxc = new WxCustBaseVO();
                wxc.setAddress(custInfo.getCustDetail().getLiveAddress());
                wxc.setIdType("身份证");
                wxc.setIdNumber(custInfo.getIdNo());
                wxc.setCustomerName(custInfo.getCustName());
                wxc.setCellPhone(custInfo.getMobile());
                wxc.setGender(getGenderByIdCard(custInfo.getIdNo()));
                wxc.setCustomerTypes(new ArrayList<>());
                wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
                wxc.setCustomerId(id);

                WxCustomerQuery query = new WxCustomerQuery();
                query.setKeyword(custInfo.getCustName());
                query.setUserType(sessionVO.getUserType());
                query.setUserId(sessionVO.getUserId());
                List<WxCustomerVO> customerList = customerMapper.listWxCustomer(query);
                if (customerList != null && !customerList.isEmpty()) {
                    if (customerList.stream().anyMatch(c -> Objects.equals(c.getIdNumber(), custInfo.getIdNo())
                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);
                    }
                }
                return wxc;
            }
            return null;
        }
    }

    /**
     * 查询用户微信我的保险用户基本信息
     *
     * @param id
     * @return
     */
    public WxCustBaseVO getCustomerByIdRetrospective(String authorization, String id, String customerType, String jobNumber) {
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            WxCustBaseVO wxc = customerMapper.getCustomerById(id);
            if(wxc==null){
                throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(),ExcptEnum.DATA_NOT_FOUNT.getMsg());
            }
            wxc.setCustomerTypes(new ArrayList<>());
            wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);


//            if (sessionVO.isBindEmployee()) {  获取客户是否信贷的标识   目前信贷客户查询业务已经停用
//                WxCustomerQuery query = new WxCustomerQuery();
//                query.setOpenId(openId);
//                query.setAuthorization(authorization);
//                query.setKeyword(wxc.getCustomerName());
//                PageInfo<WxCustomerVO> pageInfo = getWxXinDaiCustomerListByPage(query);
//
//                if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
//                    if (pageInfo.getList().stream().anyMatch(c -> Objects.equals(c.getIdNumber(), wxc.getIdNumber())
//                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
//                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
//                    }
//                }
//            }
            return wxc;
        } else {
            Result<CustInfoVo> result = manageFacade.custDetail(id);

            if (result != null && result.isSuccess()) {
                CustInfoVo custInfo = result.getData();
                WxCustBaseVO wxc = new WxCustBaseVO();
                wxc.setAddress(custInfo.getCustDetail().getLiveAddress());
                wxc.setIdType("身份证");
                wxc.setIdNumber(custInfo.getIdNo());
                wxc.setCustomerName(custInfo.getCustName());
                wxc.setCellPhone(custInfo.getMobile());
                wxc.setGender(getGenderByIdCard(custInfo.getIdNo()));
                wxc.setCustomerTypes(new ArrayList<>());
                wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
                wxc.setCustomerId(id);

                WxCustomerQuery query = new WxCustomerQuery();
                query.setKeyword(custInfo.getCustName());
                query.setUserType("employee");//日复盘对象只有员工
                query.setUserId(jobNumber);
                List<WxCustomerVO> customerList = customerMapper.listWxCustomer(query);
                if (customerList != null && !customerList.isEmpty()) {
                    if (customerList.stream().anyMatch(c -> Objects.equals(c.getIdNumber(), custInfo.getIdNo())
                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);
                    }
                }
                return wxc;
            }
            return null;
        }
    }

    /**
     * 查询用户微信我的保险用户关系信息
     *
     * @param idNumber
     * @return
     */
    @Deprecated
    public List<WxCustShipVO> getWxCustomerShipByIdNumber(String openId, String authorization, String idNumber, String customerType) {
        checkAuthorityEpAg(openId, authorization);
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            return customerMapper.listWxCustomerShipByIdNumber(idNumber);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 查询用户微信我的保险用户关系信息
     *
     * @param custId
     * @return
     */
    public List<WxCustShipVO> getWxCustomerShipById(String openId, String authorization, String custId, String customerType) {
        checkAuthorityEpAg(openId, authorization);
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            return customerMapper.queryCustomerShipByCustomerId(custId);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 查询用户微信我的保险用户关系信息
     *
     * @param idNumber
     * @return
     */
    @Deprecated
    public WxCustPropertyVO getWxCustomerPropertyByIdNumber(String openId, String authorization, String idNumber, String customerType) {
        checkAuthorityEpAg(openId, authorization);
        WxCustPropertyVO wxcp = new WxCustPropertyVO();
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            List<CustPropertyVO> propertys = customerMapper.listWxCustomerPropertyByIdNumber(idNumber);
            wxcp.setCars(propertys.stream().filter(p -> !StringUtils.isEmpty(p.getCarPlateNo())).collect(Collectors.toList()));
            wxcp.setHouses(propertys.stream().filter(p -> !StringUtils.isEmpty(p.getHourseNo()) || !StringUtils.isEmpty(p.getPropertyAddress())).collect(Collectors.toList()));
        } else {
            wxcp.setCars(Collections.emptyList());
            wxcp.setHouses(Collections.emptyList());
        }
        return wxcp;
    }

    /**
     * 查询用户微信我的保险用户关系信息
     *
     * @param custId
     * @return
     */
    public WxCustPropertyVO getWxCustomerPropertyById(String openId, String authorization, String custId, String customerType) {
        checkAuthorityEpAg(openId, authorization);
        WxCustPropertyVO wxcp = new WxCustPropertyVO();
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            List<CustPropertyVO> propertys = customerMapper.listWxCustomerPropertyById(custId);
            wxcp.setCars(propertys.stream().filter(p -> !StringUtils.isEmpty(p.getCarPlateNo())).collect(Collectors.toList()));
            wxcp.setHouses(propertys.stream().filter(p -> !StringUtils.isEmpty(p.getHourseNo()) || !StringUtils.isEmpty(p.getPropertyAddress())).collect(Collectors.toList()));
        } else {
            wxcp.setCars(Collections.emptyList());
            wxcp.setHouses(Collections.emptyList());
        }
        return wxcp;
    }

    /**
     * 查询用户微信我的保险用户保单信息
     *
     * @param wq
     * @return
     */
    @Deprecated()
    public SmyPageInfo<WxPolicyListVo, WxCmsSmyVo> getWxCustomerPolicysById(WxCustOrderQuery wq) {
        WxSessionVO session = checkAuthorityEpAg(wq.getOpenId(), wq.getAuthorization());

        SmyPageInfo<WxPolicyListVo, WxCmsSmyVo> pageInfo;
        if (Objects.equals(wq.getCustomerType(), CUSTOMER_TYPE_INS)) {
            // 查询报单统计信息
            WxCmsQuery query2 = new WxCmsQuery();
            query2.setChannel(session.getChannel());
            if (session.isBindEmployee()) {
                query2.setCustomerAdminId(session.getUserId());
            } else if (session.isBindAgent()) {
                query2.setAgentId(session.getAgentId());
            }
            query2.setIdNumber(wq.getIdNumber());
            query2.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            query2.setIdNumberType(wq.getType());
            WxCmsSmyVo summary = orderMapper.getWxPolicySummary(query2);
            if (summary == null) {
                summary = new WxCmsSmyVo();
                summary.setOrderQty(0);
            }
            if (summary.getOrderAmount() == null) {
                summary.setOrderAmount(BigDecimal.ZERO);
            }
            if (summary.getCmsAmount() == null) {
                summary.setCmsAmount(BigDecimal.ZERO);
            }

            // 查询保单列表
            WxOrderQuery query = new WxOrderQuery();
            query.setChannel(session.getChannel());
            if (session.isBindEmployee()) {
                query.setCustomerAdminId(session.getUserId());
            } else if (session.isBindAgent()) {
                query.setAgentId(session.getAgentId());
            }
            query.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
            query.setIdNumber(wq.getIdNumber());
            query.setIdNumberType(wq.getType());
            if (wq.getPage() != null && wq.getSize() != null) {
                PageHelper.startPage(wq.getPage(), wq.getSize());
            }
            pageInfo = new SmyPageInfo<>(orderMapper.listWxPolicyList(query));
            pageInfo.setSummary(summary);
        } else {
            pageInfo = new SmyPageInfo<>();
            WxCmsSmyVo smy = new WxCmsSmyVo();
            smy.setCmsAmount(BigDecimal.ZERO);
            smy.setOrderAmount(BigDecimal.ZERO);
            smy.setOrderQty(0);
            pageInfo.setSummary(smy);
            pageInfo.setList(Collections.emptyList());
        }
        return pageInfo;
    }

    /**
     * 查询用户微信我的保险用户保单信息
     *
     * @param query
     * @return
     */
    public SmyPageInfo<WxPolicyListVo, WxCmsSmyVo> getWxCustomerPolicys(WxCustOrderQuery query) {
        WxSessionVO session = checkAuthorityEpAg(query.getOpenId(), query.getAuthorization());

        SmyPageInfo<WxPolicyListVo, WxCmsSmyVo> pageInfo = new SmyPageInfo<>();
        WxCmsSmyVo smy = new WxCmsSmyVo();
        smy.setCmsAmount(BigDecimal.ZERO);
        smy.setOrderAmount(BigDecimal.ZERO);
        smy.setOrderQty(0);
        pageInfo.setSummary(smy);
        pageInfo.setList(Collections.emptyList());
        if (!Objects.equals(query.getCustomerType(), CUSTOMER_TYPE_INS)) {
            return pageInfo;
        }

        WxCustBaseVO customer = customerMapper.getCustomerById(query.getCustomerId());
        if (customer == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
        }
        String idNumber = customer.getIdNumber();
        // 查询报单统计信息
        WxCmsQuery cmsquery = new WxCmsQuery();
        cmsquery.setChannel(session.getChannel());
        if (session.isBindEmployee()) {
            cmsquery.setCustomerAdminId(session.getUserId());
        } else if (session.isBindAgent()) {
            cmsquery.setAgentId(session.getAgentId());
        }
        cmsquery.setIdNumber(idNumber);
        cmsquery.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
        cmsquery.setIdNumberType(query.getType());
        WxCmsSmyVo summary = orderMapper.getWxPolicySummary(cmsquery);
        if (summary == null) {
            summary = new WxCmsSmyVo();
            summary.setOrderQty(0);
        }
        if (summary.getOrderAmount() == null) {
            summary.setOrderAmount(BigDecimal.ZERO);
        }
        if (summary.getCmsAmount() == null) {
            summary.setCmsAmount(BigDecimal.ZERO);
        }

        // 查询保单列表
        WxOrderQuery orderQuery = new WxOrderQuery();
        orderQuery.setChannel(session.getChannel());
        if (session.isBindEmployee()) {
            orderQuery.setCustomerAdminId(session.getUserId());
        } else if (session.isBindAgent()) {
            orderQuery.setAgentId(session.getAgentId());
        }
        orderQuery.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS);
        orderQuery.setIdNumber(idNumber);
        orderQuery.setIdNumberType(query.getType());
        if (query.getPage() != null && query.getSize() != null) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        pageInfo = new SmyPageInfo<>(orderMapper.listWxPolicyList(orderQuery));
        pageInfo.setSummary(summary);
        return pageInfo;
    }

    /**
     * 获取身份证性别
     *
     * @param idNumber
     * @return
     */
    private String getGenderByIdCard(String idNumber) {
        return Objects.equals(IdcardUtils.getGenderByIdCard(idNumber), "M") ? "男" : "女";
    }

    /**
     * 查询我的客户对应理赔信息
     * 1、查询客户的所有保单（客户作为个险的投保人 / 被保人的保单或作为团险的投保人的保单）
     * 2、查找对应的理赔记录
     *
     * @param query 微信客户订单query
     * @return SmClaimVO
     */
    public PageInfo<WxClaimCancelReportListVo> getWxCustomerClaims(WxCustOrderQuery query) {
        List<Long> insuredIds = orderMapper.getInsuredIdsByCustomerId(query.getCustomerId());
        if (insuredIds.isEmpty()) {
            return new PageInfo<>();
        }

        List<Long> distinctInsuredIds = insuredIds.stream().distinct().collect(Collectors.toList());
        if (Objects.nonNull(query.getPage())) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        List<WxClaimCancelReportListVo> claimList = smClaimMapper.listSmClaimsByInsuredIds(distinctInsuredIds);
        return new PageInfo<>(claimList);
    }

    /**
     * 在保险业务助手中查询我的保险用户基本信息
     *
     * @param id
     * @param userInfo
     * @return
     */
    public WxCustBaseVO getCustomerById(JwtUserInfo userInfo, String authorization, String customerId, String customerType) {
        if (Objects.equals(customerType, CUSTOMER_TYPE_INS)) {
            WxCustBaseVO wxc = customerMapper.getCustomerById(customerId);
            if (wxc == null) {
                throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), ExcptEnum.DATA_NOT_FOUNT.getMsg());
            }
            wxc.setCustomerTypes(new ArrayList<>());
            wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);
            PageInfo<WxCustomerVO> pageInfo = new PageInfo<>();
            if (!StringUtils.isEmpty(userInfo.getJobNumber())) {
                WxUserVO wxUserVO = authUserMapper.getUserByUserId(userInfo.getJobNumber());
                if (!Objects.equals(wxUserVO.getPostCode(), "1081")) {
                    PageInfo<WxCustomerVO> page = new PageInfo<>();
                    page.setList(Collections.emptyList());
                    return wxc;
                }
            }

            Result<UserDetailVO> userDetailVOResult = bmsUserFacade.getContextUserDetailForSafes(authorization);
            log.info("当前登录用户的相关信息：{}", JSON.toJSONString(userDetailVOResult));
            if (!userDetailVOResult.isSuccess()) {
                MSException msException = new MSException(ExcptEnum.HTTP_METHOD_ERROR_000098);
                msException.setErrorMsg("HTTP调用BMS的服务获取用户的相关信息对方返回失败，返回的错误信息：" + userDetailVOResult.getErrorMsg());
                throw msException;
            }
            UserDetailVO userDetailVO = userDetailVOResult.getData();
            if (userDetailVO == null) {
                throw new MSBizNormalException(ExcptEnum.DATA_NOT_EXISTS);
            }
            QueryCustBaseInfoPageWithAuthcReq queryReq = new QueryCustBaseInfoPageWithAuthcReq();
            queryReq.setOperator(userDetailVO.getJobNumber());
            queryReq.setOrgNo(userDetailVO.getOrgCode());
            queryReq.setCustName(wxc.getCustomerName());
            queryReq.setPageNumber(1);
            queryReq.setPageSize(20);

            Result<PageVO<CustInfoVo>> pageVOResult = manageFacade.custBaseInfoPageWithAuthc(queryReq);
            log.info("信贷用户列表：[{}]", JSON.toJSONString(pageVOResult));
            if (pageVOResult.isSuccess()) {
                PageVO<CustInfoVo> pageResult = pageVOResult.getData();
                pageInfo.setTotal(pageResult.getRowTotal());
                pageInfo.setPageNum(pageResult.getPageNumber());
                pageInfo.setSize(pageResult.getPageSize());
                pageInfo.setHasNextPage(pageResult.getPageTotal() > pageResult.getPageTotal());
                if (pageResult.getRows() == null) {
                    pageResult.setRows(Collections.emptyList());
                }
                pageInfo.setList(pageResult.getRows().stream().map(c -> {
                    WxCustomerVO wxcv = new WxCustomerVO();
                    wxcv.setCustomerName(c.getCustName());
                    wxcv.setCustomerId("-1");
                    if (Objects.equals(c.getIdType(), "20")) {
                        wxcv.setIdType("身份证");
                        wxcv.setIdNumber(c.getIdNo());
                        wxcv.setCellPhone(c.getMobile());
                        String idNo = IdcardUtils.getBirthByIdCard(c.getIdNo());
                        wxcv.setBirthday(idNo.substring(0, 4) + "-" + idNo.substring(4, 6) + "-" + idNo.substring(6, 8));
                        if (c.getCustDetail() != null) {
                            wxcv.setAddress(c.getCustDetail().getLiveAddress());
                        }
                    }
                    CustDetailVo custDetail = c.getCustDetail();
                    if (Objects.nonNull(custDetail)) {
                        wxcv.setCustomerId(custDetail.getLoanCustId());
                        StringBuilder stringBuilder = new StringBuilder();
                        appendByLong(stringBuilder, custDetail.getProvince());
                        appendByLong(stringBuilder, custDetail.getCity());
                        appendByLong(stringBuilder, custDetail.getCounty());
                        appendByLong(stringBuilder, custDetail.getTown());
                        appendByLong(stringBuilder, custDetail.getVillage());
                        if (stringBuilder.length() > 0) {
                            wxcv.setArea(stringBuilder.toString());
                        }
                    }
                    wxcv.setFromXinDai(true);
                    return wxcv;
                }).collect(Collectors.toList()));
            } else {
                pageInfo.setPageNum(0);
                pageInfo.setSize(0);
                pageInfo.setHasNextPage(false);
                pageInfo.setList(Collections.emptyList());
            }

            if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
                if (pageInfo.getList()
                        .stream()
                        .anyMatch(c -> Objects.equals(c.getIdNumber(), wxc.getIdNumber()) && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                    wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
                }
            }
            return wxc;
        } else {
            Result<CustInfoVo> result = manageFacade.custDetail(customerId);

            if (result != null && result.isSuccess()) {
                CustInfoVo custInfo = result.getData();
                WxCustBaseVO wxc = new WxCustBaseVO();
                wxc.setAddress(custInfo.getCustDetail().getLiveAddress());
                wxc.setIdType("身份证");
                wxc.setIdNumber(custInfo.getIdNo());
                wxc.setCustomerName(custInfo.getCustName());
                wxc.setCellPhone(custInfo.getMobile());
                wxc.setGender(getGenderByIdCard(custInfo.getIdNo()));
                wxc.setCustomerTypes(new ArrayList<>());
                wxc.getCustomerTypes().add(CUSTOMER_TYPE_XINDAI);
                wxc.setCustomerId(customerId);

                WxCustomerQuery query = new WxCustomerQuery();
                query.setKeyword(custInfo.getCustName());
                query.setUserType("employee");
                query.setUserId(userInfo.getJobNumber());
                List<WxCustomerVO> customerList = customerMapper.listWxCustomer(query);
                if (customerList != null && !customerList.isEmpty()) {
                    if (customerList.stream().anyMatch(c -> Objects.equals(c.getIdNumber(), custInfo.getIdNo())
                            && Objects.equals(c.getCustomerName(), wxc.getCustomerName()))) {
                        wxc.getCustomerTypes().add(CUSTOMER_TYPE_INS);
                    }
                }
                return wxc;
            }
            return null;
        }
    }
}
