package com.cfpamf.ms.insur.admin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.enums.order.EnumDistributionState;
import com.cfpamf.ms.insur.admin.enums.order.OrderSourceEnum;
import com.cfpamf.ms.insur.admin.enums.pco.EnumTalkInviteType;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.RiskCommissionInfoDTO;
import com.cfpamf.ms.insur.base.annotation.ExportField;
import com.cfpamf.ms.insur.base.annotation.Mask;
import com.cfpamf.ms.insur.base.util.reflect.FieldRemoveDefault;
import com.cfpamf.ms.insur.base.util.reflect.JsonRemove;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 小额保险订单vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmOrderListVO {

    Integer insuredId;

    /**
     * 记账时间
     */
    private Date accountTime;

    /**
     * 订单提交日期
     */
    private Date submitTime;

    private Date applyTime;

    /**
     * 订单创建日期
     */
    @ExportField(name = "订单创建日期", order = 1)
    @ApiModelProperty(value = "订单创建日期")
    private Date createTime;

    /**
     * 支付时间
     */
    @ApiModelProperty(value = "支付时间")
    private Date paymentTime;

    /**
     * 订单号
     */
    @ExportField(name = "订单号", order = 2)
    @ApiModelProperty(value = "订单号")
    private String fhOrderId;

    /**
     * 订单状态
     */
    private String payStatus;

    /**
     * 支付方式：online/offline
     */
    private String payType;

    /**
     * 订单状态
     */
    @ExportField(name = "订单状态", order = 3)
    @ApiModelProperty(value = "订单状态")
    private String payStatusName;

    /**
     * 投保产品Id
     */
    private Integer productId;

    /**
     * 保险公司产品Id
     */
    private String fhProductId;

    /**
     * 数量
     */
    private Integer orderQty;

    /**
     * 投保产品名称
     */
    @ExportField(name = "投保产品名称", order = 4)
    @ApiModelProperty(value = "投保产品名称")
    private String productName;

    /**
     * 投保计划Id
     */
    @ApiModelProperty(value = "投保计划Id")
    private Integer planId;

    /**
     * 投保计划名称
     */
    @ExportField(name = "计划名称", order = 4)
    @ApiModelProperty(value = "投保计划名称")
    private String planName;



    @ApiModelProperty("是否含有主险-中文")
    private String mainInsurance;

    /**
     * 订单金额
     */
    @ExportField(name = "订单金额", order = 5, type = "money")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 保单号
     */
    @ExportField(name = "保单号", order = 6)
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ExportField(name = "原始保单号")
    @ApiModelProperty(value = "原始保单号")
    private String thPolicyNo;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "批改单号")
    private String endorsementNo;
    /**
     * 保单状态
     */
    @ApiModelProperty(value = "保单状态")
    private String appStatus;

    /**
     * 保单状态
     */
    @ExportField(name = "保单状态", order = 7)
    @ApiModelProperty(value = "保单状态")
    private String appStatusName;

    /**
     * 保单起保日期
     */
    @ExportField(name = "保单起保日期", order = 8)
    @ApiModelProperty(value = "保单起保日期")
    private Date startTime;

    /**
     * 保单失效日期
     */
    @ExportField(name = "保单失效日期", order = 9)
    @ApiModelProperty(value = "保单失效日期")
    private Date endTime;

    /**
     * 保险期限
     */
    @ApiModelProperty(value = "保险期限")
    private String validPeriod;

    /**
     * 订单出单类型:区分见费、非见费
     */
    private String orderOutType;

    /**
     * 保险公司Id
     */
    private Integer companyId;


    /**
     * 保险公司名称
     */
    @ExportField(name = "保险公司名称", order = 10)
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;
    /**
     * 渠道
     */
    @ExportField(name = "产品渠道", order = 11)
    @ApiModelProperty(value = "渠道名称")
    private String channelName;


    /**
     * 保单下载地址
     */
    @ExportField(name = "保单下载地址", order = 12)
    @ApiModelProperty(value = "保单下载地址")
    @JsonRemove
    private String downloadURL;

    /**
     * 投保人姓名
     */
    @ExportField(name = "投保人姓名", order = 13)
    @ApiModelProperty(value = "投保人姓名")
    private String applicantPersonName;

    /**
     * 投保人证件类型
     */
    private String aplicantIdType;

    /**
     * 投保人证件类型
     */
    private String aplicantIdTypeName;

    /**
     * 投保人证件号
     */
    @ExportField(name = "投保人证件号", order = 14)
    @ApiModelProperty(value = "投保人证件号")
    @Mask(dataType = Mask.DataType.ID_CARD)
    @JsonRemove(classify = FieldRemoveDefault.class)
    private String aplicantIdNumber;

    /**
     * 投保人性别
     */
    @ExportField(name = "投保人性别", order = 15)
    @ApiModelProperty(value = "投保人性别")
    @JsonRemove(classify = NonCommission.class)
    private String applicantPersonGender;

    /**
     * 投保人手机号
     */
    @ExportField(name = "投保人手机号", order = 16)
    @ApiModelProperty(value = "投保人手机号")
    @Mask(dataType = Mask.DataType.MOBILE)
    @JsonRemove
    private String applicantCellPhone;

    /**
     * 投保人邮箱
     */
    @ExportField(name = "投保人邮箱", order = 17)
    @ApiModelProperty(value = "投保人邮箱")
    @Mask(dataType = Mask.DataType.EMAIL)
    @JsonRemove
    private String applicantEmail;

    /**
     * 被保人生日
     */
    private String applicantBirthday;

    /**
     * 投保人证件有效期起期
     */
    private String applicantIdPeriodStart;

    /**
     * 投保人证件有效期止期
     */
    private String applicantIdPeriodEnd;

    /**
     * 投被保人关系
     */
    @ExportField(name = "投被保人关系", order = 18)
    @ApiModelProperty(value = "投被保人关系")
    @JsonRemove(classify = NonCommission.class)
    private String insuredRelationship;

    /**
     * 被保人姓名
     */
    @ExportField(name = "被保人姓名", order = 19)
    @ApiModelProperty(value = "被保人姓名")
    private String insuredPersonName;


    private Integer insuredSn;
    /**
     * 被保人证件类型
     */
    private String insuredIdType;

    /**
     * 被保人证件类型
     */
    private String insuredIdTypeName;

    /**
     * 投保人证件号
     */
    @ExportField(name = "被保人证件号", order = 20)
    @ApiModelProperty(value = "被保人证件号")
    @Mask(dataType = Mask.DataType.ID_CARD)
    @JsonRemove
    private String insuredIdNumber;

    /**
     * 被保人性别
     */
    @ExportField(name = "被保人性别", order = 21)
    @ApiModelProperty(value = "被保人性别")
    @JsonRemove(classify = NonCommission.class)
    private String insuredPersonGender;

    /**
     * 被保人手机号
     */
    @ExportField(name = "被保人手机号", order = 22)
    @ApiModelProperty(value = "被保人手机号")
    @Mask(dataType = Mask.DataType.MOBILE)
    @JsonRemove
    private String insuredCellPhone;

    /**
     * 被保人邮箱
     */
    @ExportField(name = "被保人邮箱", order = 23)
    @ApiModelProperty(value = "被保人邮箱")
    @Mask(dataType = Mask.DataType.EMAIL)
    @JsonRemove(classify = FieldRemoveDefault.class)
    private String insuredEmail;

    /**
     * 被保人地区
     */
    private String insuredArea;

    /**
     * 被保人地址
     */
    private String insuredAddress;

    /**
     * 年收入
     */
    private String insuredAnnualIncome;

    /**
     * 职业名称
     */
    private String insuredOccupationCode;

    /**
     * 职业名称
     */
    private String insuredOccupationName;

    /**
     * 被保人生日
     */
    private String insuredBirthday;

    /**
     * 被保人证件有效期起期
     */
    private String insuredIdPeriodStart;

    /**
     * 被保人证件有效期止期
     */
    private String insuredIdPeriodEnd;

    /**
     * 院校类型
     */
    private String schoolNature;

    /**
     * 学生类型
     */
    private String studentType;

    /**
     * 学校类型
     */
    private String schoolType;

    /**
     * 院校类型
     */
    private String schoolNatureName;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 班级
     */
    private String schoolClass;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 代理人Id
     */
    private Integer agentId;

    /**
     * 代理人姓名
     */
    @ExportField(name = "代理人姓名", order = 26)
    @ApiModelProperty(value = "代理人姓名")
    @JsonRemove(classify = Common.class)
    private String agentName;

    /**
     * 代理人账号
     */
    @ExportField(name = "代理人账号", order = 26)
    @ApiModelProperty(value = "代理人账号")
    @JsonRemove(classify = Common.class)
    private String agentMobile;

    /**
     * 代理工号
     */
    @ExportField(name = "代理工号", order = 26)
    @ApiModelProperty(value = "代理工号")
    @JsonRemove(classify = Common.class)
    private String agentJobNumber;

    /**
     * 推荐人员工编号
     */
    @ExportField(name = "推荐人手机号", order = 24)
    @ApiModelProperty(value = "推荐人手机号")
    @JsonRemove
    private String recommendUserMobile;

    /**
     * 推荐人姓名
     */
    @ExportField(name = "推荐人姓名", order = 25)
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendUserName;

    /**
     * 推荐人手机号
     */
    @ExportField(name = "推荐人员工编号", order = 26)
    @ApiModelProperty(value = "推荐人员工编号")
    private String recommendUserId;

    /**
     * 推荐人所属区域
     */
    @ExportField(name = "推荐人所属区域", order = 27)
    @ApiModelProperty(value = "所属区域")
    private String recommendRegionName;

    /**
     * 推荐人所属机构
     */
    @ExportField(name = "推荐人所属机构", order = 28)
    @ApiModelProperty(value = "所属机构")
    private String recommendOrganizationName;

    /**
     * 业务上级
     */
    @ApiModelProperty("业务上级")
    private String userMasterName;

    /**
     * 直线经理
     */
    @ApiModelProperty("直线经理")
    private String userAdminName;

    /**
     * 入职时间
     */
    @ApiModelProperty("入职时间")
    private String entryDate;

    /**
     * 推荐人所属机构
     */
    @ApiModelProperty("所属岗位")
    private String postName;

    /**
     * 支付佣金比例
     */
    @ApiModelProperty(value = "支付佣金比例")
    private BigDecimal paymentProportion;

    /**
     * 支付佣金
     */
    @ExportField(name = "支付佣金", order = 30, type = "money")
    @ApiModelProperty(value = "支付佣金")
    @JsonRemove(classify = NonCommission.class)
    private BigDecimal paymenyCommission;

    /**
     * 结算佣金比例
     */
    @ApiModelProperty(value = "结算佣金比例")
    private BigDecimal settlementProportion;

    /**
     * 结算佣金
     */
    @ApiModelProperty(value = "结算佣金")
    private BigDecimal settlementCommission;

    /**
     * 结算佣金比例
     */
    @ApiModelProperty(value = "折算保费比例")
    @ExportField(name = "折算保费比例", order = 30)
    @JsonRemove(classify = NonCommission.class)
    private BigDecimal convertedProportion;


    /**
     * 加佣金额
     */
    @ApiModelProperty(value = "加佣金额")
    private BigDecimal addCommissionAmount;


    @ApiModelProperty("是否主营保单-中文")
    private String loanFlag;

    @ApiModelProperty(value = "是否主营保单  0 是，1否")
    private Integer isLoanFlag;

    @ApiModelProperty(value = "是否含主险  0 是，1否")
    private Integer hasMainInsurance;

    @ApiModelProperty(value = "是否在贷  0 是，1否")
    private Integer isValidLoan;
    /**
     * 获取贷款标识
     *
     * @return 贷款标识
     */
    public String getLoanFlag() {
        if (Objects.equals(isLoanFlag, 1)) {
            return "是";
        }
        return "否";
    }

    public String getValidLoan() {
        if (Objects.equals(isValidLoan, 1)) {
            return "是";
        }
        return "否";
    }


    /**
     * 获取主保险是否有的方法
     * @return 如果主保险为是，则返回"是"，否则返回"否"
     */
    public String getMainInsurance() {
        if (Objects.equals(hasMainInsurance, 1)) {
            return "是";
        }
        return "否";
    }


    /**
     * 计算加佣金额
     *
     * @return
     */
    @ApiModelProperty("加佣奖励")
    public BigDecimal getAddCommissionAmount() {
        return Objects.isNull(addCommissionAmount) ? BigDecimal.ZERO : addCommissionAmount;
    }


    /**
     * 计算加佣比例
     *
     * @return
     */
    @ApiModelProperty("加佣比例")
    public BigDecimal getAddCommissionProportion() {
        //if(Objects.nonNull(getTotalAmount()) && getTotalAmount().compareTo(BigDecimal.ZERO) != 0 ){
        if (Objects.nonNull(getTotalAmount()) && BigDecimal.ZERO.compareTo(getTotalAmount()) != 0) {
            return getAddCommissionAmount().divide(getTotalAmount(), 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
        }
        return BigDecimal.ZERO;
    }


    /**
     * 计算支付总佣金
     *
     * @return
     */
    @ApiModelProperty
    public BigDecimal getTotalCommissionAmount() {
        return getPaymenyCommission().add(getAddCommissionAmount());
    }

    /**
     * 折算保费
     */
    @ApiModelProperty(value = "折算保费")
    @ExportField(name = "折算保费", order = 29, type = "money")
    private BigDecimal convertedAmount;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 承保年龄
     */
    private String underWritingAge;

    /**
     * 提成Id
     */
    private Integer commissionId;

    /**
     * 是否有社保
     */
    private String isSecurity;

    /**
     * 订单客户处理Flag
     */
    private String extractFlag;

    /**
     * 产品类型 个险 团险
     */
    private String productAttrCode;

    private String payId;

    private String applicantAreaName;
    private String applicantArea;
    private String applicantAddress;

    private String insuredAreaName;

    private String appNo;


    @ApiModelProperty("出单渠道 enumOrderSubChannel")
    private String subChannel;

    @ExportField(name = "订单来源", order = 30)
    @ApiModelProperty("推荐渠道 enumOrderSubChannel")
    private String subChannelDesc;

    @ApiModelProperty("最后更新时间")
    private Date updateTime;


    /**
     * 保单最终状态
     */
    @ApiModelProperty("保单最终状态")
    private String finalStatus;

    @ApiModelProperty("缴费年限")
    private String payingYears;

    @ApiModelProperty("保障类型")
    private String coveredType;

    @ApiModelProperty("缴费方式")
    private String payingType;

    @ApiModelProperty("保额")
    private BigDecimal insuredAmount;


    //  t12.distribution_amount distributionAmount,t12.distribution_state distributionState
    @ExportField(name = "小鲸站长", order = 20)
    @ApiModelProperty(value = "新增-小鲸站长")
    private String distributionCustName;
    @ExportField(name = "分销等级", order = 21)
    @ApiModelProperty(value = "新增-分销等级")
    private String distributionLevel;
    @ExportField(name = "分销佣金", order = 21, type = "money")
    @ApiModelProperty(value = "分销佣金")
    private java.math.BigDecimal distributionAmount;

    /**
     * 字段名称 分销订单号
     */
    @ExportField(name = "分销等级", order = 4)
    @ApiModelProperty("分销订单号")
    private String distributionOrderNo;
    /**
     * 字段名称 佣金发放状态
     */
    @ApiModelProperty(value = "佣金发放状态 1-待结算,2-已结算,3-已作废,4-结算中")
    private String distributionState;

    /**
     * 字段名称 佣金发放状态
     */
    @ExportField(name = "分销等级", order = 22)
    @ApiModelProperty(value = "佣金发放状态 1-待结算,2-已结算,3-已作废,4-结算中")
    private String distributionStateDesc;
    /**
     * 字段名称 绑定id
     */
    @ApiModelProperty(value = "绑定id")
    private Integer bindId;
    /**
     * 字段名称 绑定状态
     */
    @ApiModelProperty(value = "绑定状态，未绑定 undo,已绑定 bind,已解绑 remove")
    private String bindStatus;
    /**
     * 字段名称 绑卡业务类型 续保:RENEWAL,续期:INSTALMENT,续保+续期:BOTH
     */
    @ApiModelProperty(value = "绑卡业务类型 续保:RENEWAL,续期:INSTALMENT,续保+续期:BOTH")
    private String bizType;

    @ApiModelProperty("产品创建类型")
    private String productCreateType;

    @ApiModelProperty("是否宣讲会订单")
    private Boolean talkOrder;

    @ExportField(name = "宣讲会推广单", order = 42)
    @ApiModelProperty("是否宣讲会订单翻译")
    private String talkOrderName;

    @ExportField(name = "邀约人姓名", order = 43)
    @ApiModelProperty("邀约人姓名")
    private String inviteName;


    @ApiModelProperty("宣讲会邀约人类型")
    private Integer inviteType;

    @ExportField(name = "邀约人类型", order = 44)
    @ApiModelProperty("宣讲会邀约人类型翻译")
    private String inviteTypeName;

    @ApiModelProperty("产品类型编码")
    private String productType;

    @ApiModelProperty("产品类型")
    @ExportField(name = "产品类型", order = 45)
    private String productTypeName;


    @ApiModelProperty(value = "推荐渠道名称")
    @ExportField(name = "推荐渠道", order = 46)
    private String recommendChannelName;

    @ApiModelProperty(value = "推荐渠道")
    private String recommendChannel;

    @ApiModelProperty(value = "推荐人")
    private String recommendId;

    @ApiModelProperty(value = "管护客户经理")
    private String customerAdminId;

    /**
     * 引流分享码
     */
    @ApiModelProperty(value = "引流分享码")
    @ExportField(name = "引流分享码", order = 47)
    private String drainageShareCode;

    /**
     * 引流分享姓名
     */
    @ApiModelProperty(value = "引流分享姓名")
    @ExportField(name = "引流分享姓名", order = 48)
    private String drainageShareName;

    /**
     * 引流账户
     */
    @ApiModelProperty(value = "引流账户")
    private String drainageShareAccount;

    /**
     * 引流平台
     */
    @ApiModelProperty(value = "引流平台")
    private String drainagePlatform;

    /**
     * 保单综合评分
     */
    @ApiModelProperty(value = "保单综合评分")
    @ExportField(name = "保单综合评分", order = 51)
    private String businessScore;

    @ApiModelProperty(value = "保单综合评分")
    @ExportField(name = "活动编码", order = 52)
    private String activityCode;

    @ApiModelProperty(value = "是否整村推进")
    @ExportField(name = "是否整村推进", order = 53)
    private String villageActivity;
    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "当月是否信贷  1 是，0否")
    private String loanMonth;

    @ApiModelProperty(value = "当年是否信贷  1 是，0否")
    private String loanYear;

    @ApiModelProperty(value = "是否整村推进  1 是，0否")
    private String buyback3d;

    @ApiModelProperty(value = "是否自保件  1 是，0否")
    private String isLabel;

    @ApiModelProperty(value = "是否分销单  1 是，0否")
    private Integer orderType;

    @ApiModelProperty(value = "是否分销单  1 是，0否")
    private String orderTypeName;

    public String getOrderTypeName() {
        if (Objects.equals(orderType, 0)) {
            return "普通订单";
        } else {
            return "分销订单";
        }
    }

    public String getLoanMonth() {
        if (Objects.equals(loanMonth, "0")) {
            return "否";
        } else {
            return "是";
        }
    }

    public String getLoanYear() {
        if (Objects.equals(loanYear, "0")) {
            return "否";
        } else {
            return "是";
        }
    }

    public String getBuyback3d() {
        if (Objects.equals(buyback3d, "0")) {
            return "否";
        } else {
            return "是";
        }
    }

    @ApiModelProperty(value = "是否自保件 0 否 1 是")
    private String selfInsured;

    @ExportField(name = "是否自保件", order = 55)
    @ApiModelProperty(value = "自保件")
    private String selfInsuredName;

    @ApiModelProperty(value = "其他标识符 0为无状态，1为理赔终止，2为理赔豁免 ")
    private String otherIdentifiers;

    public String getIsLabel() {
        if (Objects.equals(isLabel, "0")) {
            return "否";
        } else {
            return "是";
        }
    }


    public String getVillageActivity() {
        if (villageActivity != null && !villageActivity.isEmpty() && type != null && type == 100) {
            return "是";
        } else {
            return "否";
        }
    }

    public String getInviteTypeName() {
        if (Objects.isNull(inviteType)) {
            return null;
        }
        return EnumTalkInviteType.USER.getCode().equals(inviteType) ?
                EnumTalkInviteType.USER.getDesc() : EnumTalkInviteType.WHALE_AGENT.getDesc();
    }

    public String getTalkOrderName() {

        return Boolean.TRUE.equals(talkOrder) ? "是" : "否";
    }

    @ApiModelProperty("保司对账结果")
    private String reconciliationResult;

    @ApiModelProperty("任务保费")
    private BigDecimal taskPremium;

    public String getDistributionStateDesc() {
        return EnumDistributionState.dict(distributionState);
    }

    public String getSubChannelDesc() {
        return EnumOrderSubChannel.dict(subChannel);
    }


    public BigDecimal getPaymenyCommission() {

        if (paymentProportion != null && getTotalAmount() != null) {
            return paymentProportion == null ? new BigDecimal(0) : paymentProportion.multiply(getTotalAmount())
                    .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);
        } else if (paymentProportion == null && paymenyCommission != null) {
            return paymenyCommission;
        }
        return BigDecimal.ZERO;
    }

    public BigDecimal getSettlementCommission() {
        if (settlementProportion != null && getTotalAmount() != null) {
            return settlementProportion == null ? new BigDecimal(0) : settlementProportion.multiply(getTotalAmount())
                    .divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP);

        } else if (settlementProportion == null && settlementCommission != null) {
            return settlementCommission;
        }
        return BigDecimal.ZERO;
    }

    public void handlerCommissionTotalAmount() {
        this.totalAmount = Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS) ? totalAmount.divide(new BigDecimal(-1), 2, BigDecimal.ROUND_HALF_UP) : totalAmount;
    }

    public String getOrderAdminName() {
        if (!StringUtils.isEmpty(agentName)) {
            return agentName;
        }
        return recommendUserName;
    }

    public String getOrderAminMobile() {
        if (!StringUtils.isEmpty(agentMobile)) {
            return agentMobile;
        }
        return recommendUserMobile;
    }

    /**
     * 订单导出的公共字段
     */
    public interface Common extends FieldRemoveDefault {
    }

    /**
     * 非查提提成
     */
    public interface NonCommission extends FieldRemoveDefault, Common {
    }


    @ApiModelProperty("支付佣金险种信息")
    @ExportField(name = "支付佣金险种信息", order = 30)
    private String paymentRiskJson;

    private Date detailCreateTime;

    public String getPaymentRiskJson() {
        if (StringUtils.isEmpty(paymentRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(paymentRiskJson);
    }


    @ApiModelProperty("结算佣金险种信息")
    private String settlementRiskJson;

    public String getSettlementRiskJson() {
        if (StringUtils.isEmpty(settlementRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(settlementRiskJson);
    }

    public String getRecommendChannelName() {
        return OrderSourceEnum.dict(recommendChannel);
    }

    @ApiModelProperty("折算保费险种信息")
    @ExportField(name = "折算保费险种信息", order = 29)
    private String conversionRiskJson;

    public String getConversionRiskJson() {
        if (StringUtils.isEmpty(conversionRiskJson)) {
            return null;
        }
        return riskCommissionJsonToStr(conversionRiskJson);
    }


    public static String riskCommissionJsonToStr(String riskJson) {
        List<RiskCommissionInfoDTO> list = JSONArray.parseArray(riskJson, RiskCommissionInfoDTO.class);
        String str = "";
        for (RiskCommissionInfoDTO dto : list) {
            str += dto.toShowString() + "\r\n";
        }
        return str;
    }


    /**
     * 字段名称 联合出单标识
     */
    @ApiModelProperty(value = "联合出单标识")
    private String jointIssuingFlag;
    /**
     * 字段名称 投保地
     */
    @ApiModelProperty(value = "投保地")
    private String insurancePlace;
    @ApiModelProperty("分销类型 1-普通分销 2-四级分销")
    private Integer distributionType;

    @ApiModelProperty("温馨提示")
    private String friendlyReminder;
    @ApiModelProperty("后续流程")
    private String followingSteps;

    @ApiModelProperty(value = "是否生服合伙人（0否，1是）")
    private Integer isLifeServicePartner;

    @ApiModelProperty(value = "是否生服合伙人（0否，1是）")
    private String isLifeServicePartnerDesc;
    @ApiModelProperty(value = "是否生服合伙人（0否，1是）")
    public String getIsLifeServicePartnerDesc() {
        if (Objects.equals(isLifeServicePartner, 1)) {
            return "是";
        } else {
            return "否";
        }
    }
}
