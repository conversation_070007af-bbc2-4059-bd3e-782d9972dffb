package com.cfpamf.ms.insur.admin.service.order;

import com.beust.jcommander.internal.Maps;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.enums.EnumAiCheckResType;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderSubChannel;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingRespDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingValidateDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRenewBindInfo;
import com.cfpamf.ms.insur.admin.pojo.query.BankListQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.BankListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.SmCancelRefundService;
import com.cfpamf.ms.insur.admin.service.SmOrderRenewBindService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.sys.SysNotifyService;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify.TKPayNotify;
import com.cfpamf.ms.pay.facade.dto.PayOrderNotifyDTO;
import com.cfpamf.ms.pay.facade.dto.PayRefundNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 订单方法的调用
 *
 * <AUTHOR> 2020/3/18 09:21
 */
@Service
@Slf4j
public class SmOrderServiceWrapper {

    @Autowired
    private List<SmOrderService> smOrderServices;

    @Autowired
    private SmProductService productService;

    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private SysNotifyService notifyService;


    @Autowired
    private SmCancelRefundService refundService;

    @Autowired
    private SmOrderRenewBindService smOrderRenewBindService;


    /**
     * 校验订单数据是否正确
     *
     * @param userUniqueId
     * @param dto
     */
    public OrderSubmitResponse checkOrderData(String channel, String userUniqueId, OrderSubmitRequest dto) {
        return findServiceByChannel(channel).checkOrderData(userUniqueId, dto);
    }

    public OrderPreAiCheckResp jumpAiCheck(String userUniqueId, OrderSubmitRequest dto, HttpServletResponse response) {

        SmPlanVO planById = productService.getPlanById(dto.getPlanId());
//        SmProductDetailVO productById = productService.getProductById(planById.getProductId());

        if (notifyService.isMaintenance(planById.getChannel(), dto.getSubChannel())) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_8020001);
        }
        return findServiceByChannel(planById.getChannel()).jumpAiCheck(userUniqueId, dto, response);
    }

    public List<AICheckQueryResponse> queryAiCheck(String channel, String orderId, String questionnaireId) {
        List<AICheckQueryResponse> aiCheckQueryResponses = findServiceByChannel(channel).aiCheckQuery(orderId, questionnaireId);

        //如果所有人都被拒保了 修改成订单关闭
        if (!CollectionUtils.isEmpty(aiCheckQueryResponses)
                && aiCheckQueryResponses.stream().noneMatch(res -> EnumAiCheckResType.canContinue(res.getConclusionType()))) {
            orderMapper.updateOrderPayStatus(orderId, SmConstants.ORDER_STATUS_CANCEL);
        }
        return aiCheckQueryResponses;
    }

    /**
     * 提交订单
     *
     * @param userUniqueId
     * @param dto
     * @return
     */
    public OrderSubmitResponse submitOrder(String userUniqueId, OrderSubmitRequest dto) {
        SmPlanVO planById = productService.getPlanById(dto.getPlanId());

        if (notifyService.isMaintenance(planById.getChannel(), dto.getSubChannel())) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_8020001);
        }
        return findServiceByChannel(planById.getChannel()).submitOrder(userUniqueId, dto);
    }

    public SmOrderService findServiceWithDeep(String channel) {
        return smOrderServices.stream()
                .filter(service -> service.support(StringUtils.isNotBlank(channel) ? channel : EnumChannel.FH.getCode()))
                .findAny().orElse(null);
    }

    public SmOrderService findServiceByChannel(String channel) {
        return smOrderServices.stream()
                .filter(service -> service.support(StringUtils.isNotBlank(channel) ? channel : EnumChannel.FH.getCode()))
                .findAny().orElseThrow(() -> new BizException(ExcptEnum.CHANNEL_NOT_CONFIG.getCode(), "该渠道暂未配置：" + channel));
    }

    /**
     * 刷新一段时间内所有未支付的单子
     *
     * @param start
     * @param end
     */
    public void updateNotPay(LocalDateTime start, LocalDateTime end) {
        List<String> list = orderMapper.listOrderIdNotPay(start, end);
        list.forEach(this::updateOrderPolicyInfo);
    }

    /**
     * 支付保险公司支付回调(异步)
     *
     * @param req
     */
    public void handAsyncPayCallback(String channel, String orderId, Object req,
                                     HttpServletRequest request,
                                     HttpServletResponse response) throws IOException {

        findServiceByChannel(channel).handAsyncPayCallback(orderId, req, response, request);
    }


    /**
     * 支付保险公司支付回调(同步)
     *
     * @param req
     */
    public void handSyncPayCallback(String channel, String orderId,
                                    Object req,
                                    HttpServletRequest request,
                                    HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handSyncPayCallback(orderId, req, response, request);
    }

    public Object handleOfflinePayNotify(String channel, Object data) {
        return findServiceByChannel(channel).handleOfflinePayNotify(data);
    }


    /**
     * 同步保险公司的保单状态
     *
     * @param orderId
     * @return
     */
    public Map<String, String> updateOrderPolicyInfo(String orderId) {

        SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (Objects.isNull(dbOrderVO)) {
            return Collections.emptyMap();
        }
        String orderChannel = dbOrderVO.getChannel();

        // 打个补丁，由于国寿财的接口爆503，所以退保的时候不再进行状态更新
        if (EnumChannel.GSC.getCode().equals(orderChannel)){
            return null;
        }

        SmOrderService orderService = findServiceByChannel(orderChannel);

        //如果线下导入的订单
        if (Objects.equals(dbOrderVO.getSubChannel(), EnumOrderSubChannel.IMPORT.getCode())) {
            orderService.notifyBackVisit(orderId);
            Map<String, String> res = Maps.newHashMap();
            List<SmOrderInsuredVO> smOrderInsuredVOS = orderMapper.listOrderInsuredsByOrderId(orderId, null);
            smOrderInsuredVOS
                    .forEach(insured -> res.put(insured.getIdNumber(), insured.getAppStatus()));
            // 每次同步重新计算提成信息防止提成丢失
            SpringFactoryUtil.getBean(EventBusEngine.class).publish(new OrderCommissionChangeEvent(orderId));
            return res;
        }
        return orderService.updateOrderPolicyInfo(orderId);

    }

    /**
     * @param orderId
     * @param type    0=投保,1=批改
     * @return
     */
    public String getOrderPayUrl(String orderId, Integer type) {
        SmBaseOrderVO dbOrderVO = null;
        /**
         * 批改单,需从批改记录表中获取订单信息
         */
        if (Objects.equals(type, 1)) {
            dbOrderVO = orderMapper.getEndorOrderById(orderId);
        } else {
            dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        }

        if (dbOrderVO == null) {
            throw new MSBizNormalException(ExcptEnum.DATA_NOT_FOUNT.getCode(), "订单数据不存在");
        }
        return findServiceByChannel(dbOrderVO.getChannel()).getOrderPayUrl(orderId, type);
    }

    /**
     * 获取订单信息【预下单的时候】
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getOrderInfo(String orderId) {
        SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        String orderChannel = dbOrderVO.getChannel();
        //如果线下导入的订单
        if (Objects.equals(dbOrderVO.getSubChannel(), EnumOrderSubChannel.IMPORT.getCode())) {
            return findServiceByChannel(orderChannel).getOrderInfoForLocal(orderId);
        }
        return findServiceByChannel(orderChannel).getOrderInfo(orderId);
    }

    /**
     * 获取订单
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getOrderInfoForLocal(String orderId) {
        return OrderConvertor.mapperOrderQuery4Local(orderId);
        // SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        // String orderChannel = dbOrderVO.getChannel();
        // return findServiceByChannel(orderChannel).getOrderInfoForLocal(orderId);
    }

    /**
     * 获取订单
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getOrderInfoForLocal_V3(String orderId) {
        SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        String orderChannel = dbOrderVO.getChannel();
        return findServiceByChannel(orderChannel).getOrderInfoForLocal(orderId, "V3");
    }


    /**
     * 获取车险订单
     *
     * @param orderId
     * @return
     */
    public AutoOrderQueryResponse getAutoOrderInfoForLocal_V3(String orderId) {
        SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        String orderChannel = dbOrderVO.getChannel();
        return findServiceByChannel(orderChannel).getAutoOrderInfoForLocal(orderId, "V3");
    }

    public void handAsyncPayCallback(PayOrderNotifyDTO notifyDTO,
                                     HttpServletRequest request,
                                     HttpServletResponse response) throws IOException {

        SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(notifyDTO.getSourceOrderId());

        if (Objects.isNull(dbOrderVO)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "订单不存在");
        }
        findServiceByChannel(dbOrderVO.getChannel())
                .handAsyncPayCallback(notifyDTO.getSourceOrderId(),
                        notifyDTO, response, request);

    }

    public void handAsyncRefundCallback(PayRefundNotifyDTO notifyDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.getWriter().write(refundService.refundStatusChange(notifyDTO));
    }


    /**
     * 核保查询重定向
     *
     * @param channel
     * @param orderId
     * @param questionnaireId
     * @param request
     * @param response
     * @throws IOException
     */
    public void handAICheckAccept(String channel, String orderId, String questionnaireId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handAICheckAccept(orderId, questionnaireId, request, response);
    }

    public void handChannelCallBackAccept(String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handAcceptSuccess(request, response);
    }

    /**
     * 渠道退保回调
     *
     * @param channel
     * @param request
     * @param response
     * @throws IOException
     */
    public void handChannelCancelCallBack(String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handCancelCallBack(null, request, response);
    }


    /**
     * 支付保险公司支付回调(异步)
     */
    public void manualHandCallBack(String channel, HttpServletRequest request, HttpServletResponse response) throws IOException {

        findServiceByChannel(channel).manualHandCallBack(request, response);
    }

    /**
     * 渠道团险回调
     *
     * @param channel
     * @param request
     * @param response
     */
    public void handChannelCallBackGroupAccept(String channel, Object obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handChannelCallBackGroupAccept(obj, request, response);
    }

    /**
     * 渠道团险回调
     *
     * @param channel
     * @param groupPolicyNo
     */
    public void queryChannelGroupOrder(String channel, String groupPolicyNo) throws IOException {
        findServiceByChannel(channel).queryChannelGroupOrder(groupPolicyNo);
    }

    /**
     * 渠道团险回调
     *
     * @param channel
     * @param groupPolicyNo
     */
    public void queryChannelGroupEndorsement(String channel, String groupPolicyNo, String groupEndorsementNo) throws IOException {
        findServiceByChannel(channel).queryChannelGroupEndorsement(groupPolicyNo, groupEndorsementNo);
    }

    /**
     * 渠道雇主责任险回调
     *
     * @param channel
     * @param request
     * @param response
     */
    public void handChannelCallBackEmployerAccept(String channel, Object obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handChannelCallBackEmployerAccept(obj, request, response);
    }
    /****begin s52 泰康雇主责任险批改回传***/
    /**
     * 渠道雇主责任险批改回调
     *
     * @param channel
     * @param request
     * @param response
     */
    public void handChannelCallBackEmployerEndorAccept(String channel, Object obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handChannelCallBackEmployerEndorAccept(obj, request, response);
    }


    /**
     * 渠道雇主责任险回调
     *
     * @param channel
     * @param request
     * @param response
     */
    public void handChannelCallBackV2EmployerAccept(String channel, Object obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handChannelCallBackV2EmployerAccept(obj, request, response);
    }
    /****begin s52 泰康雇主责任险批改回传***/
    /**
     * 渠道雇主责任险批改回调
     *
     * @param channel
     * @param request
     * @param response
     */
    public void handChannelCallBackV2EmployerEndorAccept(String channel, Object obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handChannelCallBackV2EmployerEndorAccept(obj, request, response);
    }
    /****end s52 泰康雇主责任险批改回传***/

    /**
     * 渠道雇主责任险专票回调
     *
     * @param channel
     * @param request
     * @param response
     */
    public void handChannelCallBackV2EmployerTicketAccept(String channel, Object obj, HttpServletRequest request, HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handChannelCallBackV2EmployerTicketAccept(obj, request, response);
    }

    /*** s48 绑卡/自动续保 add by zhangjian **/
    /**
     * 查询保司支持的银行列表
     *
     * @param bankListQuery
     */
    public BankListVO listBankInfo(BankListQuery bankListQuery) {
        return findServiceByChannel(bankListQuery.getChannel()).listBankInfo(bankListQuery);
    }

    /**
     * 查询保司支持的银行列表
     *
     * @param reqDTO
     */
    public BankCardBindingRespDTO bindingCard(BankCardBindingReqDTO reqDTO) {
        return findServiceByChannel(reqDTO.getChannel()).bindingCard(reqDTO);
    }

    /**
     * 查询保司支持的银行列表
     *
     * @param validateDTO
     */
    public boolean validateBind(BankCardBindingValidateDTO validateDTO) {
        SmOrderRenewBindInfo smOrderRenewBindInfo = smOrderRenewBindService.getById(validateDTO.getBindId());

        return findServiceByChannel(smOrderRenewBindInfo.getChannel()).validateBind(validateDTO);
    }

    /**
     * @param channel
     * @param request
     * @param response
     * @throws IOException
     */

    public void handBindCardCallback(String channel,
                                     HttpServletRequest request,
                                     HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handBindCardCallback(request, response);
    }

    /**
     * 续保保单回调
     *
     * @param channel
     * @param request
     * @param response
     * @throws IOException
     */

    public void handRenewalCallback(String channel,
                                    HttpServletRequest request,
                                    HttpServletResponse response) throws IOException {
        findServiceByChannel(channel).handRenewalCallback(request, response);
    }

    /**
     * 生成续保投保地址
     *
     * @param channel
     * @param policyNo
     * @param customerAdminId
     * @throws IOException
     */

    public String getRenewalUrl(String channel, String policyNo, String customerAdminId) {
        return findServiceByChannel(channel).getRenewalUrl(policyNo, customerAdminId);
    }

    /**
     * @param channel
     * @param request
     * @param response
     * @throws IOException
     */

    public void toInsurCompanyBind(String channel, String fhOrderId, String policyNo,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {

        findServiceByChannel(channel).toInsurCompanyBind(fhOrderId, policyNo, request, response);
    }

    /**
     * 重定向渠道绑卡地址
     *
     * @param channel
     * @param request
     * @param response
     * @throws IOException
     */

    public void handChannelBindRedirect(String channel,
                                        HttpServletRequest request,
                                        HttpServletResponse response) throws IOException {

        findServiceByChannel(channel).handChannelBindRedirect(request, response);
    }

    /*********** s50 续保资质推送 *************/
    public void handChannelWaitRenewalNotify(String channel,
                                             HttpServletRequest request,
                                             HttpServletResponse response) throws IOException {

        findServiceByChannel(channel).handWaitRenewalNotify(request, response);
    }

    /**
     * 重新处理回调消息
     *
     * @param orderId
     */
    public void redoNotify(String channel, String orderId) {
//        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
//        if(order==null){
//            throw new BizException("-1","订单数据不存在");
//        }
        findServiceByChannel(channel).redoNotify(orderId);
    }

    public void redoSelfPayNotify(String channel, String orderId) {
        findServiceByChannel(channel).redoSelfPayNotify(orderId);
    }

    /**
     * 团单支付结果通知
     *
     * @param channel
     * @param data
     * @throws IOException
     */
    public Object groupPayNotify(String channel, Object data) {
        SmOrderService orderService = findServiceByChannel(channel);
        return orderService.groupPayNotify(data);
    }

    /**
     * 团单批减回调
     *
     * @param channel
     * @param data
     * @throws IOException
     */
    public Object groupReductionNotify(String channel, TKPayNotify data) {
        SmOrderService orderService = findServiceByChannel(channel);
        return orderService.groupReductionNotify(data);
    }

    /**
     * 团单批减回调
     *
     * @param channel
     * @param data
     * @throws IOException
     */
    public Object memberChangeNotify(String channel, Object data) {
        SmOrderService orderService = findServiceByChannel(channel);
        return orderService.memberChangeNotify(data);
    }

    /*********** PC010 团险续保资质推送 *************/
    public void groupHandChannelWaitRenewalNotify(String channel,
                                             HttpServletRequest request,
                                             HttpServletResponse response) throws IOException {

        findServiceByChannel(channel).handGroupWaitRenewalNotify(request, response);
    }
}
