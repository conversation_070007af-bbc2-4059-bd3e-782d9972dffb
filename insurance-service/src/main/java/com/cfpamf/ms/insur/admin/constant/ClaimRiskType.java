package com.cfpamf.ms.insur.admin.constant;

import com.alibaba.druid.util.StringUtils;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

/**
 * 理赔出险类型（1意外住院 2一般疾病 3重大疾病 4意外身故 5残疾 6意外门诊 7家财 8疾病身故）
 *
 * <AUTHOR>
 **/
public enum ClaimRiskType {

    /**
     * 意外住院
     */
    ACCIDENT_HOSP("意外住院", "1", 2),

    /**
     * 一般疾病
     */
    GENERAL_DISEASE("一般疾病", "2", 3),

    /**
     * 重大疾病
     */
    MAJOR_DISEASES("重大疾病", "3", 4, "1.需提供完整齐全的治疗病历资料，包括首次就诊治疗的门诊病历和检查报告；\n 2.需提供重大疾病病理诊断证明"),

    /**
     * 意外身故
     */
    Death("意外身故", "4", 5, "1.需出险内24小时拨打保险公司报案，说明出险情况;\n 2.需提供居民医学死亡推断证明，死因不明确的需要提供尸检报告单（没有尸检无法确认死因的情况下会影响赔付）;\n 3.涉及抢救需提供抢救记录;\n4.如涉及交通事故或其他第三方需提供交通事故责任认定书或公安权威机关事故情况说明书"),

    /**
     * 残疾
     */
    DISABILITY("残疾", "5", 6, "拨打保司客服电话说明要申请伤残鉴定，需按《人身保险伤残鉴定标准》进行伤残等级鉴定，其他标准不认可。"),

    /**
     * 意外门诊
     */
    ACCIDENT_CLINIC("意外门诊", "6", 1),

    /**
     * 家财
     */
    FAMILY_PROPERTY("家财", "7", 8),
    /**
     * 疾病身故
     */
    DISEASE_DEATH("疾病身故", "8", 7, "1.需出险内24小时拨打保险公司报案，说明出险情况;\n 2.需提供居民医学死亡推断证明，死因不明确的需要提供尸检报告单（没有尸检无法确认死因的情况下会影响赔付）;\n 3.涉及抢救需提供抢救记录;\n4.如涉及交通事故或其他第三方需提供交通事故责任认定书或公安权威机关事故情况说明书"),

    /**
     * 疾病身故
     */
    FEMALE_FERTILITY("女性生育", "9", 9),

    OTHER("其他", "10", 10),
    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    @Getter
    private String guideContent;

    @Getter
    private int sort;

    ClaimRiskType(String name, String code) {
        this.code = code;
        this.name = name;
    }

    ClaimRiskType(String name, String code, int sort) {
        this.code = code;
        this.name = name;
        this.sort = sort;
    }

    ClaimRiskType(String name, String code, int sort, String guideContent) {
        this.code = code;
        this.name = name;
        this.sort = sort;
        this.guideContent = guideContent;
    }

    public static ClaimRiskType getClaimRiskTypeByCode(String code) {
        for (ClaimRiskType v : ClaimRiskType.values()) {
            if (Objects.equals(v.code, code)) {
                return v;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        ClaimRiskType type = Stream.of(values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
        return type != null ? type.getMame() : "";
    }

    public static String mapCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        return Stream.of(values())
                .filter(e -> e.getCode().equals(code))
                .findAny().map(ClaimRiskType::getMame)
                .orElse(null);

    }

    public String getCode() {
        return code;
    }

    public String getMame() {
        return name;
    }

    /**
     * 重大理赔出险类型
     *
     * @return
     */
    public static List<String> importantClaim() {
        return Lists.newArrayList(
                ClaimRiskType.MAJOR_DISEASES.getCode()
                , ClaimRiskType.Death.getCode()
                , ClaimRiskType.DISEASE_DEATH.getCode()
                , ClaimRiskType.DISABILITY.getCode()
        );
    }

    public static boolean isImportantClaim(String riskType) {
        return importantClaim().contains(riskType);
    }

    public static List<String> gtccExclude() {
        return Lists.newArrayList(
                ClaimRiskType.FAMILY_PROPERTY.getCode()
                , ClaimRiskType.OTHER.getCode()
                , ClaimRiskType.FEMALE_FERTILITY.getCode()
        );
    }

    public static List<String> kpContains() {
        return Lists.newArrayList(
                ClaimRiskType.ACCIDENT_CLINIC.getCode()
                , ClaimRiskType.ACCIDENT_HOSP.getCode()
                , ClaimRiskType.DISABILITY.getCode()
                , ClaimRiskType.Death.getCode()
        );
    }

    public static Set<String> kpSameRiskTypeContains() {
        return Sets.newHashSet(
                ClaimRiskType.ACCIDENT_CLINIC.getCode()
                , ClaimRiskType.ACCIDENT_HOSP.getCode()
        );
    }


    public static List<String> kpFamilyContains() {
        return Lists.newArrayList(
                ClaimRiskType.FAMILY_PROPERTY.getCode()
        );
    }


    public static ClaimRiskType mapByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return Stream.of(values())
                .filter(e -> e.getMame().equals(name))
                .findAny()
                .orElse(null);
    }
}
