package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductNotify;
import com.cfpamf.ms.insur.admin.pojo.query.SmPlanFactorQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.SmProductHistoryService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.weixin.dao.safes.WxProductHistoryMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxProductPosterMapper;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxCoveragePlanVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxProductClauseVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxProductDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 微信产品查询service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class WxProductHistoryService extends WxAbstractService {

    /**
     * 产品mapper
     */
    @Autowired
    private WxProductHistoryMapper wxProductMapper;

    /**
     * 产品mapper
     */
    @Autowired
    private SmProductMapper smProductMapper;



    @Autowired
    SmProductHistoryMapper historyMapper;
    /**
     * 海报mapper
     */
    @Autowired
    private WxProductPosterMapper wxProductPosterMapper;


    @Autowired
    private SmProductHistoryService historyService;


    public WxProductDetailVO getWxProductDetailById(int productId, Integer version) {
        int queryVersion = historyService.defaultIfAbsent(productId, version);

        WxProductDetailVO detailVo = wxProductMapper.getWxProductDetailById(productId, queryVersion);
        if (detailVo == null) {
            throw new BizException(ExcptEnum.PRODUCT_NOT_ONLINE_201001.getCode(), "产品信息不存在{" + productId + ":" + queryVersion + "}");
        }
        detailVo.setHasPoster(wxProductPosterMapper.getProductPosterByProductId(productId) != null);
//        detailVo.setSalesOrgs(wxProductMapper.listProductSalePlanByProductId(productId));
        detailVo.setClauseContent(wxProductMapper.getProductClauseContent(productId, queryVersion));

//        wxProductMapper.selectNotifyByProductId
        List<WxProductClauseVO> clauseVos = wxProductMapper.listProductClausesByProductId(productId,queryVersion);
        detailVo.setClauses(clauseVos);

        SmProductNotify smProductNotify = historyService.getProductCustNotify(productId, queryVersion);
        if (Objects.nonNull(smProductNotify) && Objects.equals(smProductNotify.getCustNotify(), 1)) {
            detailVo.setCustNotifyDetail(smProductNotify.getCustNotifyContent());
        }
        detailVo.setCoveragePlan(buildWxCoveragePlanVO(productId,queryVersion));
        return detailVo;
    }

    /**
     * 创建微信产品详情保障项目计划价格表
     *
     * @param productId
     * @return
     */
    private WxCoveragePlanVO buildWxCoveragePlanVO(int productId,int version) {
        List<SmProductCoverageAmountVO> coverageAmounts =
                historyMapper.listProductCoverageAmounts(productId,version);

        //筛选区域需要卖的计划
        List<SmPlanVO> plans = historyMapper.listProductPlansById(
                String.valueOf(productId), true,version)
               ;

        List<Integer> planIds = plans.stream().map(SmPlanVO::getId).distinct().collect(Collectors.toList());

        List<SmProductCoverageVO> coverages = historyMapper.listProductCoverages(productId,version);

        WxCoveragePlanVO coveragePlan = new WxCoveragePlanVO();
        List<WxCoveragePlanVO.CoverageAmount> planCoverageAmounts = new ArrayList<>();
        coveragePlan.setPlanNames(plans.stream()
                .map(p -> {
                    WxCoveragePlanVO.Plan plan = new WxCoveragePlanVO.Plan();
                    plan.setPlanId(p.getPlanId());
                    plan.setPlanName(p.getPlanName());
                    plan.setCoverages(wxProductMapper.listWxProductPlanCoverages(p.getPlanId(), version));
                    return plan;
                }).collect(Collectors.toList()));
        coverages.forEach(cvg -> {
            WxCoveragePlanVO.CoverageAmount planCoverageAmount = new WxCoveragePlanVO.CoverageAmount();
            planCoverageAmount.setCoverageName(cvg.getCvgItemName());
            List<String> cvgNotices = new ArrayList<>();
            plans.forEach(plan -> {
                Optional<SmProductCoverageAmountVO> optional = coverageAmounts.stream().filter(ca -> Objects.equals(ca.getPlanId(), plan.getId())
                        && Objects.equals(cvg.getSpcId(), ca.getSpcId())).findFirst();
                if (optional.isPresent()) {
                    cvgNotices.add(optional.get().getCvgNotice());
                } else {
                    cvgNotices.add("");
                }
            });
            planCoverageAmount.setCvgNotices(cvgNotices);
            planCoverageAmounts.add(planCoverageAmount);
        });
        coveragePlan.setCoverageAmounts(planCoverageAmounts);

        List<SmPlanFactorPriceDT> planPrices = historyMapper.listPlanFactorPrices(productId, planIds,version);
        List<BigDecimal> planMinPrices = new ArrayList<>();
        plans.forEach(plan -> {
            BigDecimal minPrice = planPrices.stream()
                    .filter(pp -> Objects.equals(pp.getPlanId(), plan.getId()) && !StringUtils.isEmpty(pp.getPrice()))
                    .map(pp -> new BigDecimal(pp.getPrice()))
                    .filter(p -> p.compareTo(BigDecimal.ZERO) > 0)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
            planMinPrices.add(minPrice);
        });
        coveragePlan.setPlanMinPrices(planMinPrices);
        return coveragePlan;
    }
//    /**
//     * 查询微信商品保障计划详情
//     *
//     * @param planId
//     * @return
//     */
//    public List<WxPlanCoverageVO> getWxProductPlanCoverages(int planId) {
//        return wxProductMapper.listWxProductPlanCoverages(planId,ve);
//    }

    /**
     * 微信查询产品所有计划
     *
     * @param productId
     * @return
     */
    public List<SmPlanVO> getWxProductPlans(int productId, Integer version) {

        return historyService.getProductPlans(productId, version);

    }

    /**
     * 微信查询产品所有计划
     *
     * @param productId
     * @return
     */
    public SmPlanCmsVO getWxProductCms(int productId, Integer version) {
        SmPlanCmsVO planCmsVO = new SmPlanCmsVO();
        planCmsVO.setShowCmsRatio(Boolean.FALSE);
        planCmsVO.setPlans(getWxProductPlans(productId, version));
        return planCmsVO;
    }

    /**
     * 微信查询产品所有计划选项
     *
     * @return
     */
    public SmPriceFactorOptionalsVO getWxFactorOptionalsVo(int productId, int planId, Integer version) {
        int i = historyService.defaultIfAbsent(productId, version);
        return historyService.getPlanPriceFactorOptions(planId, i);
    }

    /**
     * 查询微信产品因素价格
     *
     * @param query
     * @return
     */
    public SmPlanFactorPriceDT getPlanFactorPrice(SmPlanFactorQuery query) {
        List<SmPlanFactorPriceDT> planSpecs = smProductMapper.getPlanSpecPrice(query);
        if (!planSpecs.isEmpty()) {
            return planSpecs.get(0);
        }
        throw new BizException(ExcptEnum.PRODUCT_PLAN_PRICE_ERROR_201004);
    }


    /**
     * 微信查询保险某个计划选项所有价格
     *
     * @param id
     * @return
     */
    public List<SmPlanFactorPriceDT> getAllPlanFactorPrices(int productId, int planId, Integer version) {
        int queryVersion = historyService.defaultIfAbsent(productId, version);
        SmPlanFactorQuery query = new SmPlanFactorQuery();
        query.setPlanId(planId + "");
        query.setVersion(queryVersion);
        return historyMapper.getPlanSpecPrice(query);
    }

}
