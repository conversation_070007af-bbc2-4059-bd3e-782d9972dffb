package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.RedisUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxChangeOpenIdDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.exception.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpMaterialService;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
import me.chanjar.weixin.mp.bean.result.WxMpOAuth2AccessToken;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 微信公众号代理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WxMpServiceProxy {

    private static final int WX_ERROR_CODE_NOT_SUBSCRIBE = 43004;
    /**
     * 微信公众号MpService
     */
    @Autowired
    private WxMpService wxMpService;

    @Autowired
    private WxMpMaterialService wxMpMaterialService;

    /**
     * 用户service
     */
    @Autowired
    private UserService userService;

    /**
     * RedisUtil
     */
    @Autowired
    private RedisUtil<String, String> cache;

    @Autowired
    WechatConfig wechatConfig;


    @Autowired
    ObjectMapper objectMapper;

    /**
     * 查询微信用户信息
     * s53：等保测评整改，删除code缓存
     *
     * @param code
     * @param state
     * @return
     * @throws WxErrorException
     */
    public String getWeixnUserInfo(String code, String state) {
        log.info("微信回调 code={},state={}", code, state);
        // 防止微信40163错误
//        String cacheOpenId = cache.get(code);
//        if (!StringUtils.isEmpty(cacheOpenId)) {
//            return cacheOpenId;
//        }

        WxMpOAuth2AccessToken authToken;
        try {
            authToken = wxMpService.oauth2getAccessToken(code);
        } catch (Exception e) {
            log.warn("微信授权失败", e);
            throw new BizException(ExcptEnum.WEIXIN_AUTH_FAIL_601000);
        }
        log.info("微信获取用户信息 authToken={}", JSON.toJSONString(authToken));
        String openId = authToken.getOpenId();

        updateWeixinUserInfo(authToken);
        cache.set(code, openId, 600L);
        return openId;
    }

    /**
     * 更新用户微信信息
     *
     * @param authToken
     * @throws WxErrorException
     */
    @Async("update-wx-Executor")
    public void updateWeixinUserInfo(WxMpOAuth2AccessToken authToken) {
        try {
            log.info("更新微信信息开始");
            WxMpUser wxMpUser = wxMpService.oauth2getUserInfo(authToken, "zh_CN");
            log.info("微信获取用户信息 WxMpUser={}", JSON.toJSONString(wxMpUser));
            userService.updateWxUserInfo(WxUserDTO.newWxUserDto(wxMpUser));
            log.info("更新微信信息结束");
        } catch (Exception e) {
            log.warn("更新微信信息失败", e);
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
    }

    /**
     * 2. 转换openid的API接口如下，可在帐号迁移审核完成后开始调用，并最多保留15天。若帐号迁移没完成，调用时无返回结果或报错。帐号迁移15天后，该转换接口将会失效、无法拉取到数据。
     * ◆ URL：http://api.weixin.qq.com/cgi-bin/changeopenid?access_token=xxxxx
     * 此处token为新帐号的token
     * ◆ 请求方式：post
     * ◆ 请求数据：
     * {
     * "from_appid":"xxxxxxxx",//此处为原帐号的appid
     * "openid_list":["oEmYbwN-n24jxvk4Sox81qedINkQ","oEmYbwH9uVd4RKJk7ZZg6S
     * zL6tTo"]//需要转换的openid，即第1步中拉取的原帐号用户列表，这些必须是旧账号目前关注的才行，否则会出错；一次最多100个，不能多。
     * 获取openId
     */
    public WxChangeOpenIdDTO changeOpenid(List<String> oldOpenIds) throws WxErrorException, IOException {

        if (CollectionUtils.isEmpty(oldOpenIds) || oldOpenIds.size() > 100) {
            throw new BizException(ExcptEnum.WEIXIN_PARAM_601007);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("from_appid", wechatConfig.getOldAppId());
        params.put("openid_list", oldOpenIds);
        String post = wxMpService.post(
                "https://api.weixin.qq.com/cgi-bin/changeopenid", objectMapper.writeValueAsString(params));

        return objectMapper.readValue(post, WxChangeOpenIdDTO.class);
//        if (!changeOpen.isSuccess()) {
//            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
//        }
//        return changeOpen;
    }

    /**
     * 获取js api微信签名
     *
     * @return
     */
    public WxJsapiSignature getWxJsapiSignature(String url) {
        try {
            return wxMpService.createJsapiSignature(url);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.WEIXIN_AUTH_FAIL_601010, e);
        }
    }

    /**
     * 给用户发送模板消息
     *
     * @param message
     */
    public boolean sendTemplateMsg(WxMpTemplateMessage message) {
        try {
            log.info("发送微信模板消息:{}", message.toJson());
            WxMpTemplateMsgService wmtService = wxMpService.getTemplateMsgService();
            wmtService.sendTemplateMsg(message);
            return true;
        } catch (Exception e) {
            if (e instanceof WxErrorException) {
                WxErrorException wxErrorException = (WxErrorException) e;
                int errorCode = wxErrorException.getError().getErrorCode();
                if (errorCode == WX_ERROR_CODE_NOT_SUBSCRIBE) {
                    log.info("接收人：{}，没有关注公众号", message.getToUser());
                    return false;
                }
            }
            log.warn("微信通知失败", e);
            return false;
        }
    }


    public File mediaDownload(String mediaId) {
        try {
            log.info("获取临时素材（即下载临时的多媒体文件）mediaId:{}", mediaId);
            return wxMpMaterialService.mediaDownload(mediaId);
        } catch (Exception e) {
            log.warn("获取临时素材（即下载临时的多媒体文件）失败,mediaId:{}", mediaId, e);
        }
        return null;
    }
}
