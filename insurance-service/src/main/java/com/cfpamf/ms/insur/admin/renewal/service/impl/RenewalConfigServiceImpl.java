package com.cfpamf.ms.insur.admin.renewal.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumRenewalPlatform;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewCountDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderWaitRenewal;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.renewal.dao.InsuranceRenewMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigHistoryMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalConfigMapper;
import com.cfpamf.ms.insur.admin.renewal.entity.RenewalConfig;
import com.cfpamf.ms.insur.admin.renewal.exception.RenewalConfigBusinessException;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalConfigForm;
import com.cfpamf.ms.insur.admin.renewal.form.RenewalConfigSearchForm;
import com.cfpamf.ms.insur.admin.renewal.service.RenewalConfigHistoryService;
import com.cfpamf.ms.insur.admin.renewal.service.RenewalConfigService;
import com.cfpamf.ms.insur.admin.renewal.vo.PlanRenewalConfigVo;
import com.cfpamf.ms.insur.admin.renewal.vo.ProductRenewalConfigVo;
import com.cfpamf.ms.insur.admin.renewal.vo.ZaGroupRenewalConfigVO;
import com.cfpamf.ms.insur.admin.renewal.vo.excel.ProductRenewalConfigExcelVo;
import com.cfpamf.ms.insur.admin.service.RenewalManagerService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021/5/11 9:28
 */
@Service
public class RenewalConfigServiceImpl implements RenewalConfigService {

    @Autowired
    RenewalConfigMapper renewalConfigMapper;

    @Autowired
    SmProductService smProductService;

    @Autowired
    RenewalManagerService renewalManagerService;

    @Autowired
    SmOrderMapper smOrderMapper;

    @Autowired
    InsuranceRenewMapper insuranceRenewMapper;

    @Autowired
    SmProductService productService;

    @Autowired
    RenewalConfigHistoryService renewalConfigHistoryService;

    @Autowired
    RenewalConfigHistoryMapper renewalConfigHistoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(RenewalConfigForm renewalConfigForm) {

        commonCheck(renewalConfigForm);

        Integer id = renewalConfigForm.getId();
        RenewalConfig renewalConfig;
        //校验农保续配置表单
        if (Objects.equals(renewalConfigForm.getRenewalPlatform(), EnumRenewalPlatform.NB.getCode())) {
            checkNbRenewalConfigForm(renewalConfigForm);
            renewalConfig = handleNbSave(renewalConfigForm, id);
            //保存数据库实体
            renewalConfigMapper.insertOrUpdate(renewalConfig);

            //保存版本信息
            renewalConfigHistoryService.save(renewalConfig);

            return renewalConfig.getId();
        } else {
            handleXjSave(renewalConfigForm, id);
        }
        return null;
    }

    private void commonCheck(RenewalConfigForm renewalConfigForm) {
        if (renewalConfigForm.getAfterExpirationDay() <= renewalConfigForm.getBeforeExpirationDay()) {
            throw RenewalConfigBusinessException.RENEWAL_CONFIG_EXPIRATION_DAY_ERROR;
        }

        //平台不能一致
        List<RenewalConfig> dbRenewalConfigList = renewalConfigMapper.getActiveRenewalConfigByProductId(renewalConfigForm.getProductId());
        if (CollectionUtils.isEmpty(dbRenewalConfigList)) {
            return;
        }

        boolean samePlateFormConfig = dbRenewalConfigList.stream().anyMatch(x -> !StringUtils.equals(x.getRenewalPlatform(), renewalConfigForm.getRenewalPlatform()));

        if (samePlateFormConfig) {
            throw new BizException("","新续保规则与已有的续购规则平台不一致，请删除已有的续购规则后再保存。");
        }

    }

    private void handleXjSave(RenewalConfigForm renewalConfigForm, Integer id) {

        //查出所有的计划
        Integer productId = renewalConfigForm.getProductId();
        List<SmPlanVO> planVOList = productService.getProductPlans(productId);

        if (CollectionUtils.isEmpty(planVOList)) {
            throw new BizException("","产品计划配置缺失");
        }

        List<RenewalConfig> dbRenewalConfigList = renewalConfigMapper.getActiveRenewalConfigByProductId(productId);

        if (CollectionUtils.isNotEmpty(dbRenewalConfigList)) {
            //根据产品id进行删除
            renewalConfigMapper.deleteByProductId(productId);
        }

        List<RenewalConfig> renewalConfigList = planVOList.stream().map(
                x -> {
                    RenewalConfig renewalConfig = new RenewalConfig();
                    renewalConfig.setProductId(productId);
                    renewalConfig.setPlanId(x.getId());
                    renewalConfig.setRenewalPlatform(renewalConfigForm.getRenewalPlatform());
                    renewalConfig.setBeforeExpirationDay(renewalConfigForm.getBeforeExpirationDay());
                    renewalConfig.setAfterExpirationDay(renewalConfigForm.getAfterExpirationDay());
                    return renewalConfig;
                }
        ).collect(Collectors.toList());


      renewalConfigMapper.insertList(renewalConfigList);

      //保存版本信息
      renewalConfigHistoryService.saveList(renewalConfigList);

    }

    private RenewalConfig handleNbSave(RenewalConfigForm renewalConfigForm, Integer id) {
        RenewalConfig dbRenewalConfig = renewalConfigMapper.getActiveRenewalConfigByPlanId(renewalConfigForm.getPlanId());
        if (Objects.nonNull(dbRenewalConfig) && Objects.isNull(id)) {
            //如果数据库已存在该配置 则设置为软删除状态
            dbRenewalConfig.setEnabledFlag(1);
            renewalConfigMapper.updateByPrimaryKeySelective(dbRenewalConfig);
        }

        //表单转换成实体
        RenewalConfig renewalConfig = renewalConfigForm.convertToRenewalConfig();
        return renewalConfig;
    }

    @Override
    public PageInfo<ProductRenewalConfigVo> search(RenewalConfigSearchForm renewalConfigSearchForm) {
        //获取产品续保配置集合
        PageInfo<ProductRenewalConfigVo> productRenewalConfigVoPageInfo = PageHelper.startPage(renewalConfigSearchForm.getPageNo(), renewalConfigSearchForm.getPageSize())
                .doSelectPageInfo(() -> renewalConfigMapper.searchProductRenewalConfigVo(renewalConfigSearchForm));
        List<ProductRenewalConfigVo> productRenewalConfigVoList = productRenewalConfigVoPageInfo.getList();
        if (CollectionUtils.isEmpty(productRenewalConfigVoList)) {
            //如果数据为空  直接返回
            return productRenewalConfigVoPageInfo;
        }

        //通过产品id集合获取计划续保配置集合
        List<Integer> productIdList = productRenewalConfigVoList.stream()
                .map(ProductRenewalConfigVo::getProductId)
                .collect(Collectors.toList());
        List<PlanRenewalConfigVo> planRenewalConfigVoList = getPlanRenewalConfigVoList(productIdList);
        if (CollectionUtils.isEmpty(planRenewalConfigVoList)) {
            return productRenewalConfigVoPageInfo;
        }

        //产品计划续保配置集合通过产品id分组
        Map<Integer, List<PlanRenewalConfigVo>> planRenewalConfigVoMap = planRenewalConfigVoList.stream()
                .collect(Collectors.groupingBy(PlanRenewalConfigVo::getProductId));

        for (ProductRenewalConfigVo productRenewalConfigVo : productRenewalConfigVoList) {
            //获取产品对应的产品计划续保配置集合
            List<PlanRenewalConfigVo> productPlanRenewalConfigVoList = planRenewalConfigVoMap.getOrDefault(productRenewalConfigVo.getProductId(), Lists.newArrayList());
            productRenewalConfigVo.setPlanRenewalConfigVoList(productPlanRenewalConfigVoList);
        }
        productRenewalConfigVoPageInfo.setList(productRenewalConfigVoList);

        return productRenewalConfigVoPageInfo;
    }

    /**
     * 下载产品续保配置列表
     * @param renewalConfigSearchForm
     * @param response
     */
    @Override
    public void downloadProductConfig(RenewalConfigSearchForm renewalConfigSearchForm, HttpServletResponse response) {
        renewalConfigSearchForm.setPageNo(0);
        renewalConfigSearchForm.setPageSize(Integer.MAX_VALUE);
        List<ProductRenewalConfigVo> list = search(renewalConfigSearchForm).getList();
        List<RenewCountDTO> renewCountDTOS = insuranceRenewMapper.selectByPlanId();
        Map<Integer, Integer> collect = renewCountDTOS.stream()
                .collect(Collectors.toMap(
                        RenewCountDTO::getPlanId,
                        RenewCountDTO::getCount
                ));
        List<ProductRenewalConfigExcelVo> configExcelVoList = new ArrayList<>();
        for (ProductRenewalConfigVo productRenewalConfigVo:list){
            List<PlanRenewalConfigVo> planRenewalConfigVoList = productRenewalConfigVo.getPlanRenewalConfigVoList();
            if(planRenewalConfigVoList.isEmpty()){
                ProductRenewalConfigExcelVo productRenewalConfigExcelVo=new ProductRenewalConfigExcelVo();
                productRenewalConfigExcelVo.setProductName(productRenewalConfigVo.getProductName());
                productRenewalConfigExcelVo.setProductStatus("1".equals(productRenewalConfigVo.getProductStatus()) ? "已上线" : "已下线");
                configExcelVoList.add(productRenewalConfigExcelVo);
            }else {
                for (PlanRenewalConfigVo planRenewalConfigVo:planRenewalConfigVoList){
                    ProductRenewalConfigExcelVo productRenewalConfigExcelVo1=new ProductRenewalConfigExcelVo();
                    productRenewalConfigExcelVo1.setProductName(planRenewalConfigVo.getProductName());
                    productRenewalConfigExcelVo1.setProductStatus("1".equals(planRenewalConfigVo.getProductStatus()) ? "已上线" : "已下线");
                    productRenewalConfigExcelVo1.setPlanName(planRenewalConfigVo.getPlanName());
                    String expirationDay=null;
                    if (planRenewalConfigVo.getAfterExpirationDay() != 0 && planRenewalConfigVo.getBeforeExpirationDay() != 0) {
                        expirationDay = "到期前" + Math.abs(planRenewalConfigVo.getBeforeExpirationDay()) + "天-到期后" + planRenewalConfigVo.getAfterExpirationDay() + "天";
                        productRenewalConfigExcelVo1.setExpirationDay(expirationDay);
                    } else if (planRenewalConfigVo.getAfterExpirationDay() != 0) {
                        expirationDay = "到期后" + planRenewalConfigVo.getAfterExpirationDay() + "天";
                        productRenewalConfigExcelVo1.setExpirationDay(expirationDay);
                    } else if (planRenewalConfigVo.getBeforeExpirationDay() != 0) {
                        expirationDay = "到期前" + Math.abs(planRenewalConfigVo.getBeforeExpirationDay()) + "天";
                        productRenewalConfigExcelVo1.setExpirationDay(expirationDay);
                    }
                    productRenewalConfigExcelVo1.setRenewalHint(planRenewalConfigVo.getRenewalHint());
                    productRenewalConfigExcelVo1.setChangeHint(planRenewalConfigVo.getChangeHint());
                    productRenewalConfigExcelVo1.setRenewal(collect.getOrDefault(planRenewalConfigVo.getPlanId(), 0));
                    List<SmPlanVO> renewalPlanVoList = planRenewalConfigVo.getRenewalPlanVoList();
                    List<SmPlanVO> changePlanVoList = planRenewalConfigVo.getChangePlanVoList();
                    if(!renewalPlanVoList.isEmpty()){
                        List<String> productNamePlanList = renewalPlanVoList.stream()
                                .map(renewalPlanVo -> renewalPlanVo.getProductName() + renewalPlanVo.getPlanName())
                                .collect(Collectors.toList());
                        productRenewalConfigExcelVo1.setRenewalPlan(productNamePlanList);
                    }
                    if(!changePlanVoList.isEmpty()){
                        List<String> productNamePlanList = changePlanVoList.stream()
                                .map(changePlanVo -> changePlanVo.getProductName() + changePlanVo.getPlanName())
                                .collect(Collectors.toList());
                        productRenewalConfigExcelVo1.setChangePlan(productNamePlanList);
                    }
                    configExcelVoList.add(productRenewalConfigExcelVo1);
                }
            }
        }
        try (OutputStream os = response.getOutputStream()) {
            String fileName = URLEncoder.encode("续保配置列表" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".xlsx", StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream");
            EasyExcel.write(response.getOutputStream(), ProductRenewalConfigExcelVo.class)
                    .sheet("续保配置列表").doWrite(configExcelVoList);
        } catch (Exception e) {
            throw new MSBizNormalException("","文件下载失败");
        }
    }

    @Override
    public List<PlanRenewalConfigVo> getHistoryDetail(Integer productId, Integer version) {
        List<PlanRenewalConfigVo> planRenewalConfigVoList = renewalConfigHistoryMapper.findPlanRenewalConfigHistoryVoByProductId(productId,version);
        if (CollectionUtils.isEmpty(planRenewalConfigVoList)) {
            return planRenewalConfigVoList;
        }
        //设置计划续保配置的续保/转保产品计划信息
        settingRenewalConfigSmPlanVo(planRenewalConfigVoList);
        return planRenewalConfigVoList;
    }

    @Override
    public List<PlanRenewalConfigVo> getProductRenewalConfigByProductId(Integer productId) {
        List<PlanRenewalConfigVo> planRenewalConfigVoList = renewalConfigMapper.findPlanRenewalConfigVoByProductId(productId);
        if (CollectionUtils.isEmpty(planRenewalConfigVoList)) {
            return planRenewalConfigVoList;
        }
        //设置计划续保配置的续保/转保产品计划信息
        settingRenewalConfigSmPlanVo(planRenewalConfigVoList);
        return planRenewalConfigVoList;
    }

    @Override
    public PlanRenewalConfigVo getPlanRenewalConfigByPlanId(Integer planId) {
        PlanRenewalConfigVo planRenewalConfigVo = renewalConfigMapper.getPlanRenewalConfigByPlanId(planId);
        if (Objects.isNull(planRenewalConfigVo)) {
            return null;
        }
        //设置计划续保配置的续保/转保产品计划信息
        settingRenewalConfigSmPlanVo(Lists.newArrayList(planRenewalConfigVo));
        return planRenewalConfigVo;
    }

    @Override
    public void delete(Integer renewalConfigId) {
        RenewalConfig dbRenewalConfig = renewalConfigMapper.getRenewalConfigById(renewalConfigId);
        //数据存在则更新
        if (Objects.nonNull(dbRenewalConfig)) {
            //数据存在则更新
            dbRenewalConfig.setEnabledFlag(1);
            renewalConfigMapper.updateByPrimaryKey(dbRenewalConfig);

            //保存版本记录
            renewalConfigHistoryService.save(dbRenewalConfig);
        }
    }

    /**
     * 通过产品id集合获取计划续保配置集合
     *
     * @param productIdList
     * @return
     */
    public List<PlanRenewalConfigVo> getPlanRenewalConfigVoList(List<Integer> productIdList) {
        //通过产品id集合获取产品计划续保配置集合
        List<PlanRenewalConfigVo> planRenewalConfigVoList = renewalConfigMapper.findPlanRenewalConfigVoByProductIdList(productIdList);
        if (CollectionUtils.isEmpty(planRenewalConfigVoList)) {
            return planRenewalConfigVoList;
        }
        //设置计划续保配置的续保/转保产品计划信息
        settingRenewalConfigSmPlanVo(planRenewalConfigVoList);
        return planRenewalConfigVoList;
    }

    /**
     * 设置计划续保配置的续保/转保产品计划信息
     *
     * @param planRenewalConfigVoList
     */
    private void settingRenewalConfigSmPlanVo(List<PlanRenewalConfigVo> planRenewalConfigVoList) {
        List<Integer> planIdList = Lists.newArrayList();
        planRenewalConfigVoList.stream().forEach(planRenewalConfigVo -> {
            planIdList.addAll(planRenewalConfigVo.getChangePlanIdListByString());
            planIdList.addAll(planRenewalConfigVo.getRenewalPlanVoListByString());
        });
        if (CollectionUtils.isEmpty(planIdList)) {
            return;
        }
        //通过产品计划id获取产品计划集合
        List<SmPlanVO> planListByPlanIdList = smProductService.getPlanListByPlanIdList(planIdList,true);
        //获取产品计划map
        Map<Integer, SmPlanVO> planVoMap = planListByPlanIdList.stream()
                .collect(Collectors.toMap(SmPlanVO::getId, smPlanVO -> smPlanVO, (k1, k2) -> k2));
        for (PlanRenewalConfigVo planRenewalConfigVo : planRenewalConfigVoList) {
            //设置续保产品计划信息集合
            List<Integer> renewalPlanIdList = planRenewalConfigVo.getRenewalPlanVoListByString();
            List<SmPlanVO> renewalPlanList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(renewalPlanIdList)) {
                renewalPlanList = renewalPlanIdList.stream()
                        .map(planId -> planVoMap.get(planId))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            planRenewalConfigVo.setRenewalPlanVoList(renewalPlanList);
            if (CollectionUtils.isNotEmpty(renewalPlanList)) {
                planRenewalConfigVo.setRenewalPlanVoMap(
                        renewalPlanList.stream()
                                .collect(Collectors.groupingBy(SmPlanVO::getProductId))
                );
            }

            //设置转保产品计划信息集合
            List<Integer> changePlanIdList = planRenewalConfigVo.getChangePlanIdListByString();
            List<SmPlanVO> changePlanList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(changePlanIdList)) {
                changePlanList = changePlanIdList.stream()
                        .map(planId -> planVoMap.get(planId))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            planRenewalConfigVo.setChangePlanVoList(changePlanList);
            Integer recommendPlanId = planRenewalConfigVo.getRecommendPlanId();
            if (CollectionUtils.isNotEmpty(changePlanList)) {
                planRenewalConfigVo.setChangePlanVoMap(
                        changePlanList.stream()
                                .collect(Collectors.groupingBy(SmPlanVO::getProductId))
                );
            }
            //设置推荐计划信息
            if (Objects.nonNull(recommendPlanId)) {
                planRenewalConfigVo.setRecommendPlanVo(planVoMap.get(recommendPlanId));
            }
        }
    }

    /**
     * 校验续保配置表单
     * <p>
     * 当前配置的计划id是否存在
     * 续保/转保的产品计划id是否存在
     * 计划id是否和产品id对应
     * 校验 到期后天数是否大于到期前天数
     * 续保计划id和转保计划id不能有交集
     * 配置了续保转保计划，就需要配置推荐计划
     * ：只能选择一个计划，且推荐计划必须是续保/转保配置的计划
     * </p>
     *
     * @param renewalConfigForm
     */
    private void checkNbRenewalConfigForm(RenewalConfigForm renewalConfigForm) {
        //获取当前配置的产品计划信息
        SmPlanVO configPlanVO = smProductService.getActivePlanById(renewalConfigForm.getPlanId());
        //判断当前配置的计划id或者产品是否存在
        if (Objects.isNull(configPlanVO) || !configPlanVO.getProductId().equals(renewalConfigForm.getProductId())) {
            throw RenewalConfigBusinessException.RENEWAL_CONFIG_PRODUCT_OR_PLAN_NOT_EXIST;
        }

        List<Integer> renewalPlanIdList = renewalConfigForm.getRenewalPlanIdList();
        List<Integer> renewalProductIdList = renewalConfigForm.getRenewalProductIdList();
        List<Integer> changePlanIdList = renewalConfigForm.getChangePlanIdList();
        List<Integer> changeProductIdList = renewalConfigForm.getChangeProductIdList();
        //判断续保计划id和转保计划id是否有交集
        List<Integer> tempList = changePlanIdList.stream().filter(renewalPlanIdList::contains).collect(Collectors.toList());
        if (tempList.size() > 0) {
            throw RenewalConfigBusinessException.RENEWAL_CONFIG_RENEWAL_PLAN_CHANGE_PLAN_EXIST;
        }
        //如果续保产品id或者计划id集合不为空则校验续购产品和计划是否匹配
        if (CollectionUtils.isNotEmpty(renewalPlanIdList) || CollectionUtils.isNotEmpty(renewalProductIdList)) {
            checkProductAndPlan(renewalProductIdList, renewalPlanIdList);
        }

        //如果转保产品id或者计划id集合不为空则校验续购产品和计划是否匹配
        if (CollectionUtils.isNotEmpty(changePlanIdList) || CollectionUtils.isNotEmpty(changeProductIdList)) {
            checkProductAndPlan(changeProductIdList, changePlanIdList);
        }

        //校验推荐计划id
        Integer recommendPlanId = renewalConfigForm.getRecommendPlanId();
        if (CollectionUtils.isNotEmpty(renewalPlanIdList) || CollectionUtils.isNotEmpty(changePlanIdList)) {
            if (Objects.isNull(recommendPlanId)) {
                throw new MSBizNormalException("", "配置了续保转保计划，就需要配置推荐计划");
            }
            boolean renewalContains = CollectionUtils.isNotEmpty(renewalPlanIdList) ? renewalPlanIdList.contains(recommendPlanId) : Boolean.FALSE;
            boolean changeContains = CollectionUtils.isNotEmpty(changePlanIdList) ? changePlanIdList.contains(recommendPlanId) : Boolean.FALSE;
            if (!renewalContains && !changeContains) {
                throw new MSBizNormalException("", "推荐计划必须是续保/转保配置的计划");
            }
        }
    }

    /**
     * 校验续购产品和计划是否匹配
     *
     * @param productIdList
     * @param planIdList
     */
    private void checkProductAndPlan(List<Integer> productIdList, List<Integer> planIdList) {
        //如果续购产品为空
        if (CollectionUtils.isEmpty(productIdList) || CollectionUtils.isEmpty(planIdList)) {
            throw RenewalConfigBusinessException.RENEWAL_CONFIG_RENEWAL_OR_CHANGE_PRODUCT_OR_PLAN_NOT_EXIST;
        }
        //查询产品计划列表
        List<SmPlanVO> planListByPlanIdList = smProductService.getPlanListByPlanIdList(planIdList);
        //判断产品id和计划id是否匹配
        List<Integer> dbProductIdList = planListByPlanIdList
                .stream()
                .map(SmPlanVO::getProductId)
                .distinct()
                .collect(Collectors.toList());
        if (!productIdList.containsAll(dbProductIdList) || !dbProductIdList.containsAll(productIdList)) {
            throw RenewalConfigBusinessException.RENEWAL_CONFIG_RENEWAL_OR_CHANGE_PRODUCT_OR_PLAN_NOT_EXIST;
        }
    }

    @Override
    public Map<Integer, PlanRenewalConfigVo> listRenewAfterExpirationDayByProductIds(List<Integer> productIds) {
        List<PlanRenewalConfigVo> list = renewalConfigMapper.findPlanRenewalConfigVoByProductIdList(productIds);
        Map<Integer, PlanRenewalConfigVo> map = new HashMap<>();
        list.forEach(vo -> {
            if (map.get(vo.getProductId()) == null) {
                map.put(vo.getProductId(), vo);
            }
        });
        return map;
    }

    @Override
    public PlanRenewalConfigVo listRenewAfterExpirationDayByProductId(Integer productId) {
        List<PlanRenewalConfigVo> list = renewalConfigMapper.findPlanRenewalConfigVoByProductId(productId);
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public Integer getValidateRenewFlag(Integer planId, String endTimeString) {
        RenewalConfig renewalConfig = renewalConfigMapper.getActiveRenewalConfigByPlanId(planId);
        if(Objects.isNull(renewalConfig)){
            return 0;
        }
        Date orderEndDate = DateUtil.parseDate(endTimeString);
        Integer afterExpirationDay = renewalConfig.getAfterExpirationDay();
        Integer beforeExpirationDay = renewalConfig.getBeforeExpirationDay();
        long startDate = DateUtil.addDay(orderEndDate, beforeExpirationDay).getTime();
        long endDate = DateUtil.addDay(orderEndDate, afterExpirationDay).getTime();
        long now = System.currentTimeMillis();
        return now > startDate && now < endDate ? 1 : 0 ;
    }

    @Override
    public ZaGroupRenewalConfigVO getZaGroupPlanRenewalConfigByPlanId(Integer planId, String orderId,String policyNo){
        List<SmOrderWaitRenewal> smOrderWaitRenewalList = renewalManagerService.querySmOrderWaitRenewalByPolicyNo(policyNo,orderId);
        if(CollectionUtils.isEmpty(smOrderWaitRenewalList)){
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "还未获取到保司的续保资质，请联系客服进行处理。");
        }
        SmOrderWaitRenewal smOrderWaitRenewal = smOrderWaitRenewalList.get(0);
        ZaGroupRenewalConfigVO zaGroupRenewalConfigVO = new ZaGroupRenewalConfigVO();
        if("Y".equals(smOrderWaitRenewal.getRenewBlackFlag())){
            throw new MSBizNormalException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "该保单因保司风控无法续保，请重新询价或更换产品投保");
        }else if("N".equals(smOrderWaitRenewal.getRenewBlackFlag())){
            //众安团险续保资质为白名单续保产品用续保管理的配置
            PlanRenewalConfigVo planRenewalConfigVo = renewalConfigMapper.getPlanRenewalConfigByPlanId(planId);
            if (Objects.isNull(planRenewalConfigVo)) {
                throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "众安团险续保配置异常");
            }
            System.out.println(JSON.toJSONString(planRenewalConfigVo));
            zaGroupRenewalConfigVO.setPlanId(getOnlyOneData(planRenewalConfigVo.getRenewalPlanIdList()));
            zaGroupRenewalConfigVO.setProductId(getOnlyOneData(planRenewalConfigVo.getRenewalProductIdList()));
        }else{
            throw new BizException(ExcptEnum.ZA_BIZ_ERROR.getCode(), "众安团险资质黑名单异常");
        }
        return zaGroupRenewalConfigVO;
    }

    @Override
    public void deleteXjRenewalConfig(Integer productId) {
        List<RenewalConfig> renewalConfigList = renewalConfigMapper.getActiveRenewalConfigByProductId(productId);
        if (CollectionUtils.isEmpty(renewalConfigList)) {
            return;
        }

        if (!Objects.equals(renewalConfigList.get(0).getRenewalPlatform(), EnumRenewalPlatform.XJ.getCode())) {
            throw new BizException("", "续保平台非小鲸平台不能通过该接口删除配置");
        }

        renewalConfigMapper.deleteByProductId(productId);

        //保存版本记录
        Integer version = renewalConfigHistoryService.getMaxVersion(productId) + 1;
        renewalConfigHistoryService.saveEmptyRecord(renewalConfigList.get(0),version);
    }



    private Integer getOnlyOneData(String config){
        if(StringUtils.isBlank(config)){
            //todo
        }
        String [] array = config.split(",");
        int count = 0;
        Integer rlt = null;
        for(String str : array){
            if(StringUtils.isBlank(str)){
                //todo
            }
            rlt = Integer.valueOf(str);
        }
        if(rlt == null){
           //todo
        }
        return rlt;
    }

}
