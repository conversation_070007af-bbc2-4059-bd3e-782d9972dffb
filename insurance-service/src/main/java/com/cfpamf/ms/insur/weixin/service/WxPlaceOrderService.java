package com.cfpamf.ms.insur.weixin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDraftMapper;
import com.cfpamf.ms.insur.admin.external.AICheckQueryResponse;
import com.cfpamf.ms.insur.admin.external.OrderAICheckRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingReqDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingRespDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.BankCardBindingValidateDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.BankListVO;
import com.cfpamf.ms.insur.admin.renewal.dao.RenewalOrderMapper;
import com.cfpamf.ms.insur.admin.service.OccupationService;
import com.cfpamf.ms.insur.admin.service.SmOrderCoreService;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.base.constant.CacheKeyConstants;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.InsurPayService;
import com.cfpamf.ms.insur.base.util.ExcelReadUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.SmsSenderUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxPreOrderDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.InsuredListDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OrderDraftDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.SmOrderMinInfo;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.insured.ExcelCorrectInsured;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.insured.GroupInsuredUploadWrap;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.InsuredDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OccupationDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.ProductDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.UploadInsuredDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.WxBankListQuery;
import com.cfpamf.ms.insur.weixin.pojo.query.order.OrderDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.request.order.OrderBasicVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxOrderSimpleVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.MemberUploadRequestVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.OrderVo;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import com.cfpamf.ms.pay.facade.dto.PreCreateOrderDTO;
import com.cfpamf.ms.pay.facade.vo.PreCreateOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 微信订单service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WxPlaceOrderService extends WxAbstractService {
    @Autowired
    SmsSenderUtil smsSenderUtil;
    /**
     * 订单核心
     */
    @Autowired
    private SmOrderCoreService orderCoreService;
    @Autowired
    SmOrderManageService manageService;
    /**
     * 订单mapper
     */
    @Autowired
    private WxOrderMapper wxOrderMapper;
    @Autowired
    private SmOrderServiceWrapper orderServiceWrapper;
    /**
     * 订单mapper
     */
    @Autowired
    private SmOrderMapper orderMapper;
    @Autowired
    private SmOrderDraftMapper orderDraftMapper;
    @Autowired
    private InsurPayService insurPayService;
    @Autowired
    private RenewalOrderMapper renewalOrderMapper;
    @Autowired
    private OccupationService occupationService;

    /**
     * 保存微信小额保险订单
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderSubmitResponse submitWxOrder(OrderSubmitRequest dto) {
        // 检验授权
        WxSessionVO session = checkAuthority(dto.getWxOpenId(), dto.getAuthorization());
        //add by zhangjian 2020-08-19当前岗位编码
        dto.setJobCode(session.getJobCode());
        // 设置订单来源乡助微服务
        dto.setSubChannel(ChannelConstant.ORDER_CHANNEL_XIANGZHU);
        // 推荐人取session 用户Id
        if (!StringUtils.isEmpty(session.getUserId())) {
            dto.getProductInfo().setRecommendId(session.getUserId());
            dto.getProductInfo().setRecommendOrgCode(session.getOrgCode());
            dto.getProductInfo().setRecommendMainJobNumber(session.getMainJobNumber());
        }
        // 代理人取session 代理Id
        if (session.getAgentId() != null) {
            dto.setAgentId(session.getAgentId());
        }
        return orderServiceWrapper.submitOrder(dto.getWxOpenId(), dto);
    }

    /**
     * 校验订单数据
     *
     * @param dto
     * @return
     */
    public OrderSubmitResponse checkOrderData(String channel, OrderSubmitRequest dto) {
        // 检验授权
        WxSessionVO session = checkAuthority(dto.getWxOpenId(), dto.getAuthorization());
        //add by zhangjian 2020-08-19当前岗位编码
        dto.setJobCode(session.getJobCode());
        // 设置订单来源乡助微服务
        dto.setSubChannel(ChannelConstant.ORDER_CHANNEL_XIANGZHU);
        // 推荐人取session 用户Id
        if (!StringUtils.isEmpty(session.getUserId())) {
            dto.getProductInfo().setRecommendId(session.getUserId());
            dto.getProductInfo().setRecommendOrgCode(session.getOrgCode());
            dto.getProductInfo().setRecommendMainJobNumber(session.getMainJobNumber());
        }
        // 代理人取session 代理Id
        if (session.getAgentId() != null) {
            dto.setAgentId(session.getAgentId());
        }
        return orderServiceWrapper.checkOrderData(channel, dto.getWxOpenId(), dto);
    }

    /**
     * 保存微信小额保险订单
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderPreAiCheckResp aiCheck(OrderAICheckRequest dto, HttpServletResponse response) {
        // 检验授权
        WxSessionVO session = checkAuthority();
        //add by zhangjian 2020-08-19当前岗位编码
        dto.setJobCode(session.getJobCode());
        // 设置订单来源乡助微服务
        dto.setSubChannel(ChannelConstant.ORDER_CHANNEL_XIANGZHU);
        // 推荐人取session 用户Id
        if (!StringUtils.isEmpty(session.getUserId())) {
            dto.getProductInfo().setRecommendId(session.getUserId());
            dto.getProductInfo().setRecommendOrgCode(session.getOrgCode());
            dto.getProductInfo().setRecommendMainJobNumber(session.getMainJobNumber());
        }
        // 代理人取session 代理Id
        if (session.getAgentId() != null) {
            dto.setAgentId(session.getAgentId());
        }
        return orderServiceWrapper.jumpAiCheck(dto.getWxOpenId(), dto, response);
    }

    /**
     * 查询当前未支付订单数量
     *
     * @param wxOpenId
     * @return
     */
    public int countUnPayedOrder(String wxOpenId, Date fromDate) {
        return wxOrderMapper.countWxUnPayOrder(wxOpenId, fromDate);
    }

    public List<AICheckQueryResponse> aiCheckQuery(String channel, String orderId, String questionnaireId) {
        return orderServiceWrapper.queryAiCheck(channel, orderId, questionnaireId);
    }


    /**
     * 订单预支付接口
     *
     * @param dto
     * @return
     */
    public PreCreateOrderVO preCreateOrder(WxPreOrderDTO dto, String ip) {
        WxSessionVO session = checkAuthority(dto.getOpenId(), dto.getAuthorization());
        List<WxOrderSimpleVo> orderList = wxOrderMapper.listWxOrderSimpleList(dto.getFhOrderId());
        if (orderList.isEmpty()) {
            throw new BizException(ExcptEnum.ORDER_PAY_REQUEST_ERROR.getCode(), "订单号错误");
        }
        WxOrderSimpleVo simpleVo = orderList.get(0);
        if (Objects.equals(simpleVo.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
            throw new BizException(ExcptEnum.ORDER_PAY_REQUEST_ERROR.getCode(), "订单已经支付");
        }

        //判断是否过期或者已关闭
        if (Objects.equals(simpleVo.getPayStatus(), SmConstants.ORDER_STATUS_EXPIRE)
                || Objects.equals(simpleVo.getPayStatus(), SmConstants.ORDER_STATUS_CANCEL)) {
            throw new BizException(ExcptEnum.ORDER_PAY_REQUEST_ERROR.getCode(), "订单已过期");
        }

        PreCreateOrderDTO pcoDTO = new PreCreateOrderDTO();
        pcoDTO.setOpenId(dto.getOpenId());
        pcoDTO.setOrderTime(new Date());
        pcoDTO.setOrderAmount(simpleVo.getTotalAmount());
        pcoDTO.setPayType(dto.getPayType());
        pcoDTO.setPayMethod(dto.getPayMethod());
        pcoDTO.setProductName(simpleVo.getProductName() + simpleVo.getPlanName());
        pcoDTO.setProductId(simpleVo.getPlanId().toString());
        pcoDTO.setUserNo(session.getUserId());
        pcoDTO.setSourceOrderId(dto.getFhOrderId());
        pcoDTO.setProductType(simpleVo.getProductAttrCode());
        pcoDTO.setOrderIp(ip);
        pcoDTO.setReturnUrl(dto.getReturnUrl());
        return insurPayService.preCreateOrder(pcoDTO);
    }


    /**
     * 获取预支付支付结果
     *
     * @param fhOrderId
     * @return
     */
    public Boolean getPreOrderPayResult(String fhOrderId) {
        List<WxOrderSimpleVo> orderList = wxOrderMapper.listWxOrderSimpleList(fhOrderId);
        if (!orderList.isEmpty()) {
            if (Objects.equals(orderList.get(0).getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
                return true;
            }
            // 同步第三方支付结果
            return orderCoreService.updateNonSeeFeeUnPayOrderPayInfo(fhOrderId);
//            orderList = wxOrderMapper.listWxOrderSimpleList(fhOrderId);

        }
        return false;
    }

    public void handAICheckAccept(String channel, String orderId, String questionnaireId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        orderServiceWrapper.handAICheckAccept(channel, orderId, questionnaireId, request, response);
    }


    /**
     * 发送短信验证码
     *
     * @param mobile
     */
    public void sendVerifyCode(String mobile) {
        if (!Pattern.matches("^1[\\d]{10}$", mobile)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "手机号码格式错误！");
        }
        String key = genVerifyCodeKey(mobile);
        String code = genVerifyCode();
//        String code = redisUtil.get(key);
//        if (org.apache.commons.lang3.StringUtils.isBlank(code)) {
//            code = genVerifyCode();
//            redisUtil.set(key, code);
//        }
        //过期时间一小时
        redisUtil.set(key, code);
        redisUtil.expire(key, 60 * 60L);
        smsSenderUtil.sendApplicantCode(mobile, code);
    }

    /**
     * 发送短信验证码
     *
     * @param mobile
     */
    public boolean verifyCode(String mobile, String code) {
        String key = genVerifyCodeKey(mobile);
        String s = redisUtil.get(key);
        return StringUtils.equals(code, s);
    }

    /**
     * 生成一个随机码
     *
     * @return
     */
    public static String genVerifyCode() {
        String v = RandomUtils.nextDouble() + "";
        if (v.length() < 8) {
            return genVerifyCode();
        }
        return v.substring(2, 6);
    }

    private static String genVerifyCodeKey(String mobile) {
        return CacheKeyConstants.APPLICANT_CODE + mobile;
    }

    /********* s48 add by zhangjian 2021-07-27 *********/
    public BankListVO listBankInfo(WxBankListQuery query) {
        return orderServiceWrapper.listBankInfo(query);
    }

    public BankCardBindingRespDTO bindingCard(BankCardBindingReqDTO reqDTO) {
        return orderServiceWrapper.bindingCard(reqDTO);
    }

    public boolean validateBind(BankCardBindingValidateDTO validateDTO) {
        return orderServiceWrapper.validateBind(validateDTO);
    }

    /**
     * 跳转保司绑卡
     *
     * @param fhOrderId
     * @return
     */
    public void toInsurCompanyBind(String channel, String fhOrderId, String policyNo, HttpServletRequest request, HttpServletResponse response) throws Exception {
        orderServiceWrapper.toInsurCompanyBind(channel, fhOrderId, policyNo, request, response);
    }

    /**
     * 暂存订单：仅保存数据，不做数据校验
     *
     * @param order
     * @return
     */
    public OrderVo holdOrder(String channel, OrderVo order) {
        String orderId = order.getOrderId();
        String userId = HttpRequestUtil.getUserId();
        String openId = HttpRequestUtil.getWxOpenId();
        OrderDraftDTO dto = OrderDraftDTO.build(order);
        dto.setUserId(userId);
        dto.setOpenId(openId);
        dto.setCreateTime(new Date());
        if (StringUtils.isBlank(orderId)) {
            orderId = IdGenerator.getNextNo(channel);
            dto.setOrderId(orderId);
            order.setOrderId(orderId);
            orderDraftMapper.insert(dto);
        } else {
            orderDraftMapper.updateOrder(dto);
        }
        return order;

    }

    private static final int FOUR = 4;
    private static final int TWO = 2;


    /**
     * 人员名单录入
     *
     * @param data
     * @return
     */
    public boolean inputInsured(MemberUploadRequestVo data) {
        int opType = data.getOpType();
        String requestId = data.getRequestId();
        String companyId = data.getCompanyId();
        String policyNo = data.getPolicyNo();
        Integer productId = data.getProductId();
        String file = data.getFile();

        List<UploadInsuredDTO> insuredList = null;
        if (Objects.equals(opType, FOUR)) {
            insuredList = parseReplaceMemberFile(requestId, companyId, productId, policyNo, file);
        } else if (Objects.equals(opType, TWO)) {
            insuredList = parseExcel4SubtractMember(requestId, policyNo, file);
        } else {
            insuredList = parseExcel4AddMember(requestId, companyId, productId, policyNo, file);
        }

        log.info("被保人上传文件解析完成:{}", insuredList);
        if (!CollectionUtils.isEmpty(insuredList)) {
            int r1 = wxOrderMapper.deleteInsuredByRequestId(requestId);
            int r2 = wxOrderMapper.saveUploadInsured(opType, insuredList);
            log.info("数据入库完成:{},{}", r1, r2);
        }
        return true;

    }

//    /**
//     * 替换人
//     *
//     * @param policyNo
//     * @param companyId
//     * @param requestId
//     * @param file
//     * @return
//     */
//    public boolean replaceMemberInput(String policyNo, String companyId, String requestId, String file) {
//        List<UploadInsuredDTO> data = parseReplaceMemberFile(requestId, companyId, policyNo, file);
//        if (CollectionUtils.isEmpty(data)) {
//            log.warn("被保人导入数据为空:{}", requestId);
//            return false;
//        }
//
//        wxOrderMapper.deleteInsuredByRequestId(requestId);
//        wxOrderMapper.saveUploadInsured(4, data);
//        return true;
//    }

    @Autowired
    private PolicyMapper policyMapper;

    /**
     * @param requestId
     * @param policyNo
     * @param filePath
     * @return
     */
    public List<UploadInsuredDTO> parseExcel4SubtractMember(String requestId, String policyNo, String filePath) {
        String uid = HttpRequestUtil.getUserId();
        OrderDetailQuery query = buildQuery();
        query.setPolicyNo(policyNo);

        List<InsuredListDTO> insureds = policyMapper.queryInsureds(query);

        Map<String, InsuredListDTO> insuredMap = LambdaUtils.safeToMap(insureds, a -> a.getIdNumber().toLowerCase());

        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(filePath);
        Sheet sheet = workbook.getSheetAt(0);
        int numOfRows = sheet.getLastRowNum();

        List<UploadInsuredDTO> data = new ArrayList<>();
        Set<String> idCardSet = new HashSet<>();
        checkHeader();
        for (int i = 6; i <= numOfRows; i++) {
            String name = ExcelReadUtil.getCellValue(sheet, i, 0);
            String idCard = ExcelReadUtil.getCellValue(sheet, i, 1);
            if (StringUtils.isBlank(name) && StringUtils.isBlank(idCard)) {
                log.warn("文件空行,跳过:{}", i);
                continue;
            }

            if (StringUtils.isBlank(idCard)) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), String.format("证件号不能为空：%s行", i + 1));
            }

            if (idCardSet.contains(idCard)) {
                throw new MSBizNormalException(ExcptEnum.INSURED_NO_REPEAT.getCode(), idCard + ":证件重复");
            }
            idCardSet.add(idCard);

            InsuredListDTO insured = insuredMap.get(idCard.toLowerCase());
            if (insured == null) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), idCard + ":被保人不存在或已失效");
            }

            UploadInsuredDTO vo = new UploadInsuredDTO();
            vo.setRequestId(requestId);
            vo.setPolicyNo(policyNo);
            vo.setIdCard(idCard);
            vo.setName(name);
            vo.setBranchId(insured.getBranchId());
            vo.setOccupCode(insured.getOccupationCode());
            vo.setOccupGroup(insured.getOccupationGroup());
            vo.setOperator(uid);
            data.add(vo);
        }
        return data;
    }

    /**
     * 判断是不是空行
     *
     * @param row
     * @return
     */
    private boolean emptyLine(Row row) {
        if (row == null) {
            return true;
        }
        try {
            String name = ExcelReadUtil.getCellValue(row.getCell(0));
            String idCard = ExcelReadUtil.getCellValue(row.getCell(1));
            return StringUtils.isBlank(name) && StringUtils.isBlank(idCard);
        } catch (Exception e) {
            log.warn("解析文件行失败:{}", row);
            return false;
        }
    }

    /**
     * 解析替换人的文件导入
     *
     * @param requestId
     * @param companyId
     * @param policyNo
     * @param filePath
     * @return
     */
    private List<UploadInsuredDTO> parseReplaceMemberFile(String requestId, String companyId, Integer productId, String policyNo, String filePath) {
        List<GroupInsuredUploadWrap> data = parseFile4ReplaceMember(companyId, productId, policyNo, filePath);
        if (data == null) {
            return Collections.emptyList();
        }

        String userId = HttpRequestUtil.getUserId();
        return data.stream().map(entry -> {
            ExcelCorrectInsured excelCorrectInsured = entry.getData();
            InsuredListDTO insuredDTO = entry.getBeforeInsured();

            UploadInsuredDTO uploadInsured = new UploadInsuredDTO();
            uploadInsured.setBranchId(insuredDTO.getBranchId());
            uploadInsured.setOccupCode(excelCorrectInsured.getOccupationCode());
            uploadInsured.setOccupGroup(excelCorrectInsured.getOccupationGroup());
            uploadInsured.setIdCard(excelCorrectInsured.getAfterIdNumber());
            uploadInsured.setName(excelCorrectInsured.getAfterName());

            uploadInsured.setRequestId(requestId);
            uploadInsured.setPolicyNo(policyNo);
            uploadInsured.setOperator(userId);
            return uploadInsured;
        }).collect(Collectors.toList());
    }

    /**
     * @param companyId
     * @param policyNo
     * @param filePath
     * @return
     */
    private List<GroupInsuredUploadWrap> parseFile4ReplaceMember(String companyId, Integer productId, String policyNo, String filePath) {
        OrderDetailQuery query = buildQuery();
        query.setPolicyNo(policyNo);

        List<InsuredListDTO> insureds = policyMapper.queryInsureds(query);
        if (insureds.size() == 0) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "无法查询到保单信息");
        }
        Map<String, InsuredListDTO> insuredMap = LambdaUtils.safeToMap(insureds, entry -> entry.getIdNumber().toLowerCase());

        Set<String> businessLevel = insureds.stream().map(InsuredListDTO::getOccupationGroup).collect(Collectors.toSet());

        ProductDTO productDTO = policyMapper.getProductDetailByOrderId(insureds.get(0).catNativeOrderId());
        if (productDTO == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "产品信息不存在或产品已下线");
        }

        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(filePath);
        Sheet sheet = workbook.getSheetAt(0);
        int numOfRows = sheet.getLastRowNum();
        List<GroupInsuredUploadWrap> data = new ArrayList<>();
        checkHeader();
        Map<String, String> map = queryOccupationMapV2(companyId, productId, policyNo);

        Set<String> beforeCheck = new HashSet<>();
        Set<String> afterCheck = new HashSet<>();
        StringBuilder errorMsg = new StringBuilder();


        for (int i = 6; i <= numOfRows; i++) {
            Row row = sheet.getRow(i);
            ExcelCorrectInsured vo = new ExcelCorrectInsured();
            vo.setBeforeName(ExcelReadUtil.getCellValue(row.getCell(0)));
            vo.setBeforeIdNumber(ExcelReadUtil.getCellValue(row.getCell(1)));
            vo.setAfterName(ExcelReadUtil.getCellValue(row.getCell(2)));
            vo.setAfterIdNumber(ExcelReadUtil.getCellValue(row.getCell(3)));
            vo.setOccupationCode(ExcelReadUtil.getCellValue(row.getCell(4)));

            String beforeIdNumber = vo.getBeforeIdNumber().toLowerCase();
            String afterIdNumber = vo.getAfterIdNumber().toLowerCase();
            if (beforeCheck.contains(beforeIdNumber)) {
                errorMsg.append(idDuplicate(i) + "<br>");
            }
            if (afterCheck.contains(afterIdNumber)) {
                errorMsg.append(idDuplicate(i) + "<br>");
            }

            InsuredListDTO beforeMan = insuredMap.get(beforeIdNumber.toLowerCase());
            if (beforeMan == null) {
                errorMsg.append(String.format("第%s行：原被保人不存在或已退保", i + 1) + "<br>");
            } else {
                if (!Objects.equals(beforeMan.getPersonName(), vo.getBeforeName())) {
                    errorMsg.append(String.format("第%s行：原被保名字有误，请核对后再提交", i + 1) + "<br>");
                }
                vo.setOccupationGroup(beforeMan.getOccupationGroup());
            }


            InsuredListDTO replaceInsured = insuredMap.get(afterIdNumber.toLowerCase());
            if (replaceInsured != null) {
                errorMsg.append(String.format("第%s行：替换后身份证号与在保人员重复", i + 1) + "<br>");
            }


            if (!StringTools.validIdCard(afterIdNumber)) {
                errorMsg.append(String.format("第%s行：替换后身份证号校验失败", i + 1) + "<br>");
            } else {
                String ageChecker = checkAge(i, afterIdNumber, productDTO);
                if (StringUtils.isNotBlank(ageChecker)) {
                    errorMsg.append(ageChecker);
                }
            }

            String occupationGroup = map.get(vo.getOccupationCode());
            if (StringUtils.isBlank(occupationGroup)) {
                errorMsg.append(String.format("第%s行：职业编码有误，请检查拒保职业或者职业表版本", i + 1) + "<br>");
            }
            if (!businessLevel.contains(occupationGroup)) {
                errorMsg.append(String.format("第%s行：替换人员时不能更改职业类别，如需更改请先批减后再批增", i + 1) + "<br>");
            }
            vo.setOccupationGroup(occupationGroup);
            beforeCheck.add(beforeIdNumber.toLowerCase());
            afterCheck.add(afterIdNumber.toLowerCase());
            data.add(new GroupInsuredUploadWrap(vo, beforeMan));
        }
        if (errorMsg != null && errorMsg.length() > 0) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), errorMsg.toString());
        }
        return data;
    }

    private String idDuplicate(int line) {
        return String.format("第%s行证件重复", line);
    }

    private String checkAge(int i, String idNumber, ProductDTO productDTO) {
        Integer age = StringTools.parseAge(idNumber);
        String applyAgeFrom = productDTO.catAgeFrom();
        String applyAgeTo = productDTO.catAgeTo();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(applyAgeFrom)) {
            Integer applyAgeFrom1 = Integer.parseInt(applyAgeFrom);
            if (age < applyAgeFrom1) {
                return String.format("第%s行，身份证号年龄不在承保年龄范围内", i + 1) + "\n";
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(applyAgeTo)) {
            Integer applyAgeTo1 = Integer.parseInt(applyAgeTo);
            if (age > applyAgeTo1) {
                return String.format("第%s行，身份证号年龄不在承保年龄范围内", i + 1) + "\n";
            }
        }
        return null;
    }

    /**
     * 解析文件
     *
     * @param requestId
     * @param companyId
     * @param policyNo
     * @param filePath
     * @return
     */
    public List<UploadInsuredDTO> parseExcel4AddMember(String requestId, String companyId, Integer productId, String policyNo, String filePath) {
        String uid = HttpRequestUtil.getUserId();
        Map<String, String> map = queryOccupationMapV2(companyId, productId, policyNo);
        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(filePath);
        Sheet sheet = workbook.getSheetAt(0);

        int numOfRows = sheet.getLastRowNum();
        List<UploadInsuredDTO> data = new ArrayList<>();
        Set<String> idCardSet = new HashSet<>();
        checkHeader();

        StringBuilder sb = new StringBuilder();
        for (int i = 6; i <= numOfRows; i++) {
            UploadInsuredDTO vo = new UploadInsuredDTO();
            vo.setPolicyNo(policyNo);
            vo.setRequestId(requestId);
            vo.setName(ExcelReadUtil.getCellValue(sheet, i, 0));

            String idCard = ExcelReadUtil.getCellValue(sheet, i, 1);
            if (idCardSet.contains(idCard)) {
                String errorMsg = String.format("第%s行：证件重复", i + 1);
                sb.append(errorMsg + "<br>");
            }
            idCardSet.add(idCard);
            vo.setIdCard(idCard);

            String occupationCode = ExcelReadUtil.getCellValue(sheet, i, 2);
            String grp = map.get(occupationCode);
            if (StringUtils.isBlank(grp)) {
                String errorMsg = String.format("第%s行：[%s]职业编码有误，请检查产品的拒保职业或者职业表版本", i + 1, occupationCode);
                sb.append(errorMsg + "<br>");
            }

            if (!StringTools.validIdCard(idCard)) {
                String errorMsg = String.format("第%s行：证件格式不正确", i + 1);
                sb.append(errorMsg + "<br>");
            }

            grp = fixNumber(grp);
            vo.setOccupCode(occupationCode);
            vo.setOccupGroup(grp);
            vo.setOperator(uid);
            data.add(vo);
        }
        String err = sb.toString();
        if (StringUtils.isNotBlank(err)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), err);
        }
        return data;
    }

    private void checkHeader() {
    }

    private String fixNumber(String occup) {
        try {
            BigDecimal decimal = new BigDecimal(occup);
            return String.valueOf(decimal.intValue());
        } catch (Exception e) {
            return occup;
        }
    }

    /**
     * 老版本的职业表查询逻辑
     *
     * @param companyId
     * @return
     */
    private Map<String, String> queryOccupationMap(String companyId) {
        List<OccupationDTO> ocs = wxOrderMapper.queryOccupationByCompany(companyId);
        Map<String, String> map = new HashMap<>(16);
        for (OccupationDTO dto : ocs) {
            if (Objects.nonNull(dto.getOccupationCode()) && Objects.nonNull(dto.getOccupationGroup())) {
                map.put(dto.getOccupationCode(), dto.getOccupationGroup());
            }
        }
        return map;
    }

    /**
     * 三个场景需要考虑
     * 新契约：可按通用流程查询职业表(过滤产品配置中的拒保职业表)
     * 批增：
     * 替换人员：
     *
     * @param companyId
     * @param productId
     * @param policyNo
     * @return
     */
    private Map<String, String> queryOccupationMapV2(String companyId, Integer productId, String policyNo) {
        Integer cid = Integer.parseInt(companyId);

        List<WxTreeVO> occupationTree = occupationService.getOccupationList(cid, productId, policyNo);
        if (CollectionUtils.isEmpty(occupationTree)) {
            return Collections.emptyMap();
        }
        Map<String, String> map = new HashMap<>(16);
        for (WxTreeVO tree : occupationTree) {
            if (StringUtils.isNotBlank(tree.getValue()) && StringUtils.isNotBlank(tree.getType())) {
                map.put(tree.getValue(), tree.getType());
            }
        }
        return map;
    }

    /**
     * 获取被保人列表
     *
     * @param requestId
     * @param opType
     * @return
     */
    public List<InsuredDTO> getInsuredList(String requestId, Integer opType) {
        List<InsuredDTO> data = wxOrderMapper.queryInsuredListFromTemp(requestId, opType);
        InsuredDTO insuredDTO = data.stream()
                .filter(i -> StringUtils.isNotBlank(i.getPolicyNo()))
                .findAny()
                .orElse(null);
        if (insuredDTO == null) {
            return data;
        }

        OrderDetailQuery query = buildQuery();
        query.setPolicyNo(insuredDTO.getPolicyNo());
        List<InsuredListDTO> insureds = policyMapper.queryInsureds(query);
        List<InsuredListDTO> activeInsureds = insureds.stream()
                .filter(a -> {
                    return Objects.equals(a.getAppStatus(), "1");
                })
                .collect(Collectors.toList());
        Map<String, InsuredListDTO> insuredMap = LambdaUtils.safeToMap(activeInsureds, InsuredListDTO::getBranchId);
        data.stream().forEach(a -> {
            InsuredListDTO entry = insuredMap.get(a.getBranchId());
            if (entry != null) {
                a.setOldName(entry.getPersonName());
                a.setOldIdNumber(entry.getIdNumber());
            }
        });
        return data;
    }

    public OrderVo getOrder(String orderId) {
        OrderDraftDTO param = new OrderDraftDTO();
        param.setOrderId(orderId);
        OrderDraftDTO draftOrder = orderDraftMapper.selectOne(param);
        if (draftOrder == null) {
            return null;
        }
        OrderVo orderVo = draftOrder.build();
        groupRenewalHandle(draftOrder, orderVo);
        return orderVo;
    }

    public Boolean rmHolderOrder(String orderId) {
        return orderDraftMapper.deleteByOrderId(orderId) > 0;
    }

//    public OrderVo getOrder(String orderId) {
//        OrderVo order = new OrderVo();
//        OrderBasicVo basicVo = wxOrderMapper.queryFastOrder(orderId);
//        if(basicVo==null){
//            return order;
//        }
//        order.setOrderId(orderId);
//        order.setOrderInfo(basicVo);
//        OrderProductDTO products = wxOrderMapper.queryOrderProduct(orderId);
//        order.setProduct(OrderConvertor.convert2OrderPlan(products));
//
//        ApplicantDTO applicant = wxOrderMapper.getApplicant(orderId);
//        GroupApplicant ga = new GroupApplicant();
//        BeanUtils.copyProperties(applicant,ga);
//        order.setProposerInfo(ga);
//
//        List<FastInsuredDTO> insureds = wxOrderMapper.queryFastInsureds(orderId);
//        order.setInsuredPerson(insureds);
//
//        List<AiQuestion> question = wxOrderMapper.queryOrderQuestion(orderId);
//        order.setQuestion(question);
//        return order;
//    }

    /**
     * 暂时的单可能是团险续保的单也可能是新契约的单
     *
     * @param draftOrder
     * @param orderVo
     */
    private void groupRenewalHandle(OrderDraftDTO draftOrder, OrderVo orderVo) {
        OrderBasicVo orderInfoVO = orderVo.getOrderInfo();
        String oldOrderId = null;
        OrderBasicVo orderInfo = null;
        orderInfo = JSON.parseObject(draftOrder.getBasicInfo(), OrderBasicVo.class);
        oldOrderId = orderInfo.getOldOrderId();
        if (orderInfo.getOldOrderId() == null) {
            //不是续保不做处理
            return;
        }
        SmOrderMinInfo smOrderMinInfo = orderMapper.querySmOrderMinInfo(orderInfo.getOldOrderId());
        if (smOrderMinInfo == null) {
            //todo
        }
        orderInfoVO.setOldOrderId(oldOrderId);
        orderInfoVO.setRealRenewFlag(Boolean.TRUE);
    }
}
