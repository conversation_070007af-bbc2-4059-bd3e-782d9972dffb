package com.cfpamf.ms.insur.app.service;

import com.cfpamf.ms.insur.admin.pojo.vo.SmCompanyRegionTreeVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOccupationTreeVO;
import com.cfpamf.ms.insur.admin.service.OccupationService;
import com.cfpamf.ms.insur.admin.service.SmCmpySettingService;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2020/8/14 16:35
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AppCompanyService {

    @Autowired
    OccupationService occupationService;

    @Autowired
    SmCmpySettingService cmpySettingService;


    public SmOccupationTreeVO getOccupationParentTree(int companyId, String occCode) {
        return occupationService.getOccupationParentTree(companyId, occCode);
    }


    public SmCompanyRegionTreeVO getCompanyRegionParentTree(int companyId, String areaCode) {
        SmCompanyRegionTreeVO companyRegionParentTree = cmpySettingService.getCompanyRegionParentTree(companyId, areaCode);
        return companyRegionParentTree;
    }
}
