package com.cfpamf.ms.insur.app.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 微信返产品列表VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class AppProductListVO implements Serializable {
    private static final long serialVersionUID = 8422190416851112796L;

    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id")
    private Integer productId;

    /**
     * 保险产品
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty("产品简称")
    private String productShortName;


    /**
     * 头部图片地址
     */
    @ApiModelProperty(value = "capp 头部图片地址")
    private String headImageUrl;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String productFeature;

    /**
     * 产品标签
     */
//    @JsonIgnore
    @ApiModelProperty(value = "产品标签")
    private String productTagsJoin;

    /**
     * 产品标签
     */
    @ApiModelProperty(value = "产品标签")
    private String[] productTags;

    /**
     * 列表显示图片地址
     */
    @ApiModelProperty(value = "列表显示图片地址")
    private String thumbnailImageUrl;

    /**
     * 最低起保金额 小数位数0
     */
    @ApiModelProperty(value = "最低起保金额")
    private BigDecimal minAmount;

    public String[] getProductTags() {
        return StringUtils.isEmpty(productTagsJoin) ? new String[0] : productTagsJoin.split(",");
    }
}
