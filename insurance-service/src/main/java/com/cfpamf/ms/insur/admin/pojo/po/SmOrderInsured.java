package com.cfpamf.ms.insur.admin.pojo.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@ApiModel("被保人")
@Table(name = "sm_order_insured")
public class SmOrderInsured {

    @Id
    private Integer id;

//    private BigDecimal totalAmount;
//
//    @ApiModelProperty("购买几份")
//    private Integer qty;

    @ApiModelProperty("泛华订单Id")
    @Column(name = "fhOrderId")
    private String fhOrderId;

    @ApiModelProperty("投保人与被保险人关系")
    @Column(name = "relationship")
    private String relationship;

    @ApiModelProperty("被保险人姓名")
    @Column(name = "personName")
    private String personName;

    @ApiModelProperty("被保险人性别")
    @Column(name = "personGender")
    private String personGender;

    @ApiModelProperty("被保险人证件类型")
    @Column(name = "idType")
    private String idType;

    @ApiModelProperty("被保险人证件号")
    @Column(name = "idNumber")
    private String idNumber;

    @ApiModelProperty("身份证有效期开始时间")
    @Column(name = "idPeriodStart")
    private String idPeriodStart;

    @ApiModelProperty("身份证有效期结束时间")
    @Column(name = "idPeriodEnd")
    private String idPeriodEnd;

    @ApiModelProperty("被保险人生日")
    @Column(name = "birthday")
    private String birthday;

    @ApiModelProperty("被保险人手机号")
    @Column(name = "cellPhone")
    private String cellPhone;

    @ApiModelProperty("被保险人Email")
    @Column(name = "email")
    private String email;

    @ApiModelProperty("年收入")
    @Column(name = "annualIncome")
    private String annualIncome;

    @ApiModelProperty("航班号")
    @Column(name = "flightNo")
    private String flightNo;

    @ApiModelProperty("起飞时间")
    @Column(name = "flightTime")
    private java.time.LocalDateTime flightTime;

    @ApiModelProperty("职业编码")
    @Column(name = "occupationCode")
    private String occupationCode;

    @ApiModelProperty("职业类别")
    @Column(name = "occupation_group")
    private String occupationGroup;

    @ApiModelProperty("职业")
    @Column(name = "occupation")
    private String occupation;

    @ApiModelProperty("旅游目的地")
    @Column(name = "destinationCountryText")
    private String destinationCountryText;

    @ApiModelProperty("地区")
    @Column(name = "area")
    private String area;

    @ApiModelProperty("地址")
    @Column(name = "address")
    private String address;

    @ApiModelProperty("承保状态")
    @Column(name = "appStatus")
    private String appStatus;

    @ApiModelProperty("保单号")
    @Column(name = "policyNo")
    private String policyNo;

    @ApiModelProperty("保单下载地址")
    @Column(name = "downloadURL")
    private String downloadUrl;

    @ApiModelProperty("学生类别")
    @Column(name = "studentType")
    private String studentType;

    @ApiModelProperty("学校类型")
    @Column(name = "schoolType")
    private String schoolType;

    @ApiModelProperty("学校属性")
    @Column(name = "schoolNature")
    private String schoolNature;

    @ApiModelProperty("学校名称")
    @Column(name = "schoolName")
    private String schoolName;

    @ApiModelProperty("班级")
    @Column(name = "schoolClass")
    private String schoolClass;

    @ApiModelProperty("学号")
    @Column(name = "studentId")
    private String studentId;

    @ApiModelProperty("户籍类型")
    @Column(name = "hdrType")
    private String hdrType;

    @ApiModelProperty("承保时间")
    @Column(name = "insured_time")
    private java.time.LocalDateTime insuredTime;

    @ApiModelProperty("退保时间")
    @Column(name = "surrender_time")
    private java.time.LocalDateTime surrenderTime;

    @ApiModelProperty("是否有社保")
    @Column(name = "isSecurity")
    private String isSecurity;

    @ApiModelProperty("是否删除")
    @Column(name = "enabled_flag")
    private Integer enabledFlag;

    @ApiModelProperty("职业版本号")
    @Column(name="occupation_version")
    private String occupationVersion;

    @ApiModelProperty("创建时间")
    @Column(name="create_time")
    private Date createTime;
}
