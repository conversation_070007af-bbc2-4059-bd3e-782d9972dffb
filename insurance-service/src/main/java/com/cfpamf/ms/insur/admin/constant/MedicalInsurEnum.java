package com.cfpamf.ms.insur.admin.constant;

import com.alibaba.druid.util.StringUtils;

import java.util.stream.Stream;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2020/6/12 00:51
 * @description:
 */
public enum MedicalInsurEnum {


    NO_MEDICAl_INSUR("0","没有医保"),
    NOT_EIMBURSED("1","有医保未报销"),
    REIMBURSED("2","已报销医保"),

    ;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;


    private MedicalInsurEnum[] medicalInsurEnums;


    MedicalInsurEnum( String code,String name) {
        this.code = code;
        this.name = name;
    }


    public static String getNameByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        MedicalInsurEnum insurEnum = Stream.of(values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
        return insurEnum != null ? insurEnum.getName() : "";
    }
    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
