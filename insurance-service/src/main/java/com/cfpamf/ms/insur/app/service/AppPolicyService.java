package com.cfpamf.ms.insur.app.service;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.OrderQueryResponse;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.app.dao.AppOrderMapper;
import com.cfpamf.ms.insur.app.pojo.query.AppPolicyQuery;
import com.cfpamf.ms.insur.app.pojo.vo.AppOrderQueryDetailVO;
import com.cfpamf.ms.insur.app.pojo.vo.AppPolicyListVO;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.weixin.service.WxDownloadService;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * app订单相关接口
 *
 * <AUTHOR>
 */
@Service
public class AppPolicyService {


    /**
     * 后台订单mapper
     */
    @Autowired
    private SmOrderMapper smOrderMapper;

    /**
     * app订单service
     */
    @Autowired
    private AppOrderMapper appOrderMapper;

    //这里直接用微信的下载 因为跳转的也是跳转到和微信是同一个地址
    @Autowired
    WxDownloadService downloadService;

    @Autowired
    SmOrderServiceWrapper orderServiceWrapper;


    /**
     * 查询用户保单列表
     *
     * @param query
     * @return
     */
    public PageInfo<AppPolicyListVO> getPolicyList(AppPolicyQuery query) {
//        PageHelper.startPage(query.getPage(), query.getSize());
        List<AppPolicyListVO> res = new ArrayList<>();
        query.setInsured(true);
        List<AppPolicyListVO> insureds = appOrderMapper.listAppPolicyList(query);
        query.setInsured(false);
        List<AppPolicyListVO> applicant = appOrderMapper.listAppPolicyList(query);
        res.addAll(applicant);
        for (AppPolicyListVO insured : insureds) {
            if (res.stream().noneMatch(app ->
                    //如果是同一个单 那么慢直过滤掉 避免重复显示
                    Objects.equals(app.getFhOrderId(), insured.getFhOrderId()) &&
                            Objects.equals(app.getApplicantPersonName(), insured.getApplicantPersonName())
                            &&
                            Objects.equals(app.getInsuredPersonName(), insured.getInsuredPersonName()))) {
                res.add(insured);
            }
        }
        res.forEach(pl -> {
            String showStatusName = "";
            if (StringUtil.isNotEmpty(pl.getPayStatus()) && StringUtil.isNotEmpty(pl.getAppStatus()) && StringUtil.isNotEmpty(pl.getFullEndTime())) {
                //映射显示状态
                showStatusName = CommonUtil.getShowStatusName(pl.getPayStatus(), pl.getAppStatus(), DateUtil.parseDate(pl.getFullEndTime(), DateUtil.CN_LONG_FORMAT));
            }
            pl.setShowStatusName(showStatusName);

            if (Objects.equals(pl.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)
                    && DateUtil.getEndOfDay(DateUtil.parseDate(pl.getEndTime(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT)).compareTo(new Date()) < 0) {
                pl.setAppStatus(SmConstants.POLICY_STATUS_INVALID);
            }
        });
        return new PageInfo<>(res);
    }

    /**
     * 下载保单
     *
     * @param policyNo
     * @param url
     * @param response
     * @throws IOException
     */
    public void download(String policyNo, String url, HttpServletResponse response) throws IOException {

        downloadService.downloadPolicy(url, policyNo, response);

    }

    /**
     * 查询用户保单详情
     *
     * @param orderId
     * @return
     */
    public AppOrderQueryDetailVO getPolicyById(int orderId) {
        SmOrderInsuredVO insuredVo = smOrderMapper.getOrderInsuredByInsId(orderId);
//        OrderQueryResponse fhOrderDTO = smOrderCoreService.getOrderInfo(insuredVo.getFhOrderId());
        OrderQueryResponse fhOrderDTO = orderServiceWrapper.getOrderInfoForLocal(insuredVo.getFhOrderId());
        AppOrderQueryDetailVO detailVO = new AppOrderQueryDetailVO();
        BeanUtils.copyProperties(fhOrderDTO, detailVO);
        detailVO.getOrderInfo().setCompanyFullName(insuredVo.getCompanyName());
        detailVO.setPolicyId(orderId);
        String showStatusName = "";
        if (detailVO.getOrderInfo() != null && detailVO.getPolicyInfo() != null && StringUtil.isNotEmpty(detailVO.getOrderInfo().getEndTime())) {
            //映射显示状态
            showStatusName = CommonUtil.getShowStatusName(detailVO.getOrderInfo().getOrderState(), detailVO.getPolicyInfo().getAppStatus(), DateUtil.parseDate(detailVO.getOrderInfo().getEndTime(), DateUtil.CN_LONG_FORMAT));
        }
        detailVO.setShowStatusName(showStatusName);
        if (DateUtil.parseDate(detailVO.getOrderInfo().getEndTime(), DateUtil.CN_LONG_FORMAT).compareTo(new Date()) < 0) {
            detailVO.getPolicyInfos().forEach(pl -> {
                if (Objects.equals(pl.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                    pl.setAppStatus(SmConstants.POLICY_STATUS_INVALID);
                }
            });
            if (detailVO.getPolicyInfo() != null && Objects.equals(detailVO.getPolicyInfo().getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
                detailVO.getPolicyInfo().setAppStatus(SmConstants.POLICY_STATUS_INVALID);
            }
        }
        detailVO.setPlanFactorPriceOptionJson(insuredVo.getPlanFactorPriceOptionJson());
        return detailVO;
    }

    public void downloadById(Integer insuredId, HttpServletResponse response) throws IOException {
        SmOrderInsuredVO orderInsuredByInsId = smOrderMapper.getOrderInsuredByInsId(insuredId);
        download(orderInsuredByInsId.getPolicyNo(), orderInsuredByInsId.getDownloadURL(), response);
    }
}
