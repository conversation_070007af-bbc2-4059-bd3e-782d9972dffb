package com.cfpamf.ms.insur.app.service;

import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.query.SmOrderQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.SmOrderCoreService;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.app.pojo.dto.AppPreOrderDTO;
import com.cfpamf.ms.insur.base.constant.ChannelConstant;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.service.InsurPayService;
import com.cfpamf.ms.pay.facade.dto.PreCreateOrderDTO;
import com.cfpamf.ms.pay.facade.vo.PreCreateOrderVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * app订单相关接口
 *
 * <AUTHOR>
 */
@Service
public class AppOrderService {

    @Autowired
    SmOrderManageService manageService;
    /**
     * 后台订单 service
     */
    @Autowired
    private SmOrderCoreService smOrderCoreService;

    @Autowired
    SmOrderServiceWrapper orderServiceWrapper;


    @Autowired
    UserService userService;
    /**
     * 业务token service
     */
    @Autowired
    private BusinessTokenService businessTokenService;

    @Autowired
    InsurPayService insurPayService;

    @Autowired
    SmOrderMapper orderMapper;


    @Autowired
    SmProductService productService;

    /**
     * 订单保存接口
     *
     * @param dto
     * @return
     */
    public OrderSubmitResponse submitSmOrder(OrderSubmitRequest dto, String idNo) {
        // 设置订单来源capp
        UserPost userByIdNo = userService.getUserByIdNo(idNo);
        if (Objects.nonNull(userByIdNo)) { //如果当前登录人是员工 推荐人是自己
            dto.getProductInfo().setRecommendId(userByIdNo.getJobNumber());
            dto.getProductInfo().setRecommendOrgCode(userByIdNo.getOrgCode());
            dto.getProductInfo().setRecommendMainJobNumber(userByIdNo.getMainJobNumber());
        }
        dto.setSubChannel(ChannelConstant.ORDER_CHANNEL_CAPP);
        return orderServiceWrapper.submitOrder(dto.getUuid(), dto);
    }

    /**
     * 订单token接口
     *
     * @param uuid
     * @return
     */
    public String getOrderBusinessToken(String uuid) {
        return businessTokenService.getBusinessToken(uuid, SmConstants.SM_ORDER_BUSINESS_CODE);
    }

    /**
     * 查询订单信息
     *
     * @param orderId
     * @return
     */
    public OrderQueryResponse getChannelOrderInfo(String orderId) {
        return smOrderCoreService.getOrderInfo(orderId);
    }

    /**
     * 查询订单支付地址接口
     *
     * @param orderId
     */
    public Object getOrderPayUrl(String orderId) {
        return smOrderCoreService.getOrderPayUrl(orderId,0);
    }


    /**
     * 保存微信小额保险订单
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderPreAiCheckResp aiCheck(OrderAICheckRequest dto,
                                       String idNo,
                                       HttpServletResponse response) {
        // 检验授权
        dto.setSubChannel(ChannelConstant.ORDER_CHANNEL_CAPP);
        return orderServiceWrapper.jumpAiCheck(idNo, dto, response);
    }

    public List<AICheckQueryResponse> aiCheckQuery(String channel, String orderId, String questionnaireId) {
        return orderServiceWrapper.queryAiCheck(channel, orderId, questionnaireId);
    }

    /**
     * 校验订单数据
     *
     * @param dto
     * @return
     */
    public OrderSubmitResponse checkOrderData(String channel, String idNo, OrderSubmitRequest dto) {
        // 检验授权
        // 设置订单来源乡助微服务
        dto.setSubChannel(ChannelConstant.ORDER_CHANNEL_CAPP);
        return orderServiceWrapper.checkOrderData(channel, idNo, dto);
    }


    /**
     * 订单预支付接口
     *
     * @param dto
     * @return
     */
    public PreCreateOrderVO preCreateOrder(AppPreOrderDTO dto, String ip) {

        SmOrderQuery smOrderQuery = new SmOrderQuery();
        smOrderQuery.setFullMatch(true);
        smOrderQuery.setOrderNo(dto.getFhOrderId());
        List<SmOrderListVO> smOrderListVOS = orderMapper.listOrders(smOrderQuery);
        if (CollectionUtils.isEmpty(smOrderListVOS)) {
            throw new BizException(ExcptEnum.ORDER_PAY_REQUEST_ERROR.getCode(), "订单号错误");
        }
        SmOrderListVO smOrder = smOrderListVOS.get(0);
        if (Objects.equals(smOrder.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
            throw new BizException(ExcptEnum.ORDER_PAY_REQUEST_ERROR.getCode(), "订单已经支付");
        }

        //判断是否过期或者已关闭
        if (Objects.equals(smOrder.getPayStatus(), SmConstants.ORDER_STATUS_EXPIRE)
                || Objects.equals(smOrder.getPayStatus(), SmConstants.ORDER_STATUS_CANCEL)) {
            throw new BizException(ExcptEnum.ORDER_PAY_REQUEST_ERROR.getCode(), "订单已过期");
        }

        SmPlanVO planById = productService.getPlanById(smOrder.getPlanId());
//
        PreCreateOrderDTO pcoDTO = new PreCreateOrderDTO();
        pcoDTO.setOrderTime(new Date());
        pcoDTO.setOrderAmount(smOrder.getTotalAmount());
        pcoDTO.setPayType(dto.getPayType());
        pcoDTO.setPayMethod(dto.getPayMethod());
        pcoDTO.setProductName(planById.getProductName() + planById.getPlanName());
        pcoDTO.setProductId(smOrder.getPlanId().toString());
        pcoDTO.setUserNo(dto.getCustNo());
        pcoDTO.setSourceOrderId(dto.getFhOrderId());
        pcoDTO.setProductType(smOrder.getProductAttrCode());
        pcoDTO.setOrderIp(ip);
        pcoDTO.setReturnUrl(dto.getReturnUrl());
        return insurPayService.preCreateOrder(pcoDTO);
    }

    /**
     * 获取预支付支付结果
     *
     * @param fhOrderId
     * @return
     */
    public Boolean getPreOrderPayResult(String fhOrderId) {
        SmBaseOrderVO orderId = orderMapper.getBaseOrderInfoByOrderId(fhOrderId);
        if (Objects.nonNull(orderId)) {
            if (Objects.equals(orderId.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
                return true;
            }
            // 同步第三方支付结果
            return smOrderCoreService.updateNonSeeFeeUnPayOrderPayInfo(fhOrderId);
        }
        throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "订单不存在");
    }
}
