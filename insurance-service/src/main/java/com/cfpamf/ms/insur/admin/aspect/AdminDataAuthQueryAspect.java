package com.cfpamf.ms.insur.admin.aspect;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.OrganizationBaseVO;
import com.cfpamf.ms.bms.facade.vo.OrganizationVO;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.annotation.AdminAutoAuthQuery;
import com.cfpamf.ms.insur.admin.enums.EnumBmsRole;
import com.cfpamf.ms.insur.admin.pojo.query.BaseQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.OrgVO;
import com.cfpamf.ms.insur.admin.service.OrgService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.PermissionUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.service.WxCancelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 微信数据权限查询基类构建[这里只做了一件事 就是封装基本的查询条件]
 *
 * <AUTHOR> 2020/2/17 15:04
 */
@Aspect
@Component
@Slf4j
public class AdminDataAuthQueryAspect {

    /**
     * 随便拿的一个 抽象service的子类
     */
    @Autowired
    WxCancelService cancelService;

    @Autowired
    PermissionUtil permissionUtil;

    /**
     * orgService
     */
    @Autowired
    private OrgService orgService;
    @Autowired
    private BmsService bmsService;

    @Pointcut("@annotation(com.cfpamf.ms.insur.admin.annotation.AdminAutoAuthQuery)")
    public void query() {
        throw new UnsupportedOperationException();
    }

    @Around("query()")
    public Object around(final ProceedingJoinPoint joinPoint) throws Throwable {
        ThreadUserUtil.USER_DETAIL_TL.remove();
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        try {
            init(joinPoint, userDetailVO);
            return joinPoint.proceed();
        } finally {
            ThreadUserUtil.USER_DETAIL_TL.remove();
        }

    }

    private void init(final ProceedingJoinPoint joinPoint, UserDetailVO userDetailVO) {
        Object[] args = joinPoint.getArgs();

        Signature signature = joinPoint.getSignature();
        if (signature instanceof MethodSignature) {
            Method method = ((MethodSignature) signature).getMethod();
            AdminAutoAuthQuery annotation = method.getAnnotation(AdminAutoAuthQuery.class);
            if (annotation == null) {
                return;
            }
            List<UserDetailVO.UserRoleVO> roleList = userDetailVO.getRoleList();
            if(CollectionUtils.isEmpty(roleList)){
                String token = HttpRequestUtil.getToken();
                //log.error("current token: {}",token);
                throw new MSBizNormalException(ExcptEnum.ROLE_LIST_ERROR);
            }
            //如果当前角色拥有所有权限直接return
            if (annotation.allRole().length > 0) {

                List<String> list = Stream.of(annotation.allRole()).map(EnumBmsRole::getCode).collect(Collectors.toList());
                if (roleList.stream().anyMatch(role -> list.contains(role.getRoleCode()))) {
                    return;
                }
            }

            //赋值是机构人员
            peekArg(args, qu -> qu.setBranch(permissionUtil.isBranchRole(userDetailVO)));

            boolean isNewDataAuth = bmsService.isNewDataAuth();
            if (permissionUtil.matchHeadRole(userDetailVO, isNewDataAuth)) {
                log.info("工号:{}命中了总部角色！", userDetailVO.getJobNumber());
            }
            //当前角色是事业部
            else if (permissionUtil.matchBranchBuRole(userDetailVO, isNewDataAuth)) {
                log.info("工号:{}命中了事业部角色！", userDetailVO.getJobNumber());
                setQueryOrgInfo(args, userDetailVO, PermissionUtil.ROLE_TYPE_BRANCHBU);

            }
            //渠道保险督导(保险特有角色)
            else if (permissionUtil.isChannelSuper(userDetailVO, isNewDataAuth)) {
                log.info("工号:{}命中了渠道保险督导角色！", userDetailVO.getJobNumber());
                setChannelSuperQueryOrgInfo(args, userDetailVO);
            } else if (permissionUtil.matchRegionRole(userDetailVO, isNewDataAuth)) {
                // 用户组织是区域+办公室
                log.info("工号:{}命中了区域+办公室角色！", userDetailVO.getJobNumber());
                setQueryOrgInfo(args, userDetailVO, PermissionUtil.ROLE_TYPE_REGION);
            }
            //当前角色是片区负责人
            else if (permissionUtil.matchZoneRole(userDetailVO, isNewDataAuth)) {
                log.info("工号:{}命中了片区负责人角色！", userDetailVO.getJobNumber());
                changeZone(args, userDetailVO.getOrgName(), userDetailVO.getOrgCode(), userDetailVO.getHrOrgTreePath());
                OrganizationVO orgVO = bmsService.getOrganizationByHrOrgId(userDetailVO.getHrOrgId());
                if (orgVO != null) {
                    changeRegionName(args, orgVO.getParentOrgName(),orgVO.getParentOrgCode());
                } else {
                    throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
                }
            }
            // 当前角色是分支负责人、分支督导、分支内务 增加分支过滤条件
            else if (permissionUtil.matchBranchRole(userDetailVO, isNewDataAuth)) {
                log.info("工号:{}命中了分支负责人、分支督导、分支内务角色！", userDetailVO.getJobNumber());
                changeOrgName(args, userDetailVO.getOrgName(),userDetailVO.getOrgCode());
                OrgVO orgVO = orgService.getOrgByHrOrgId(userDetailVO.getHrOrgId() + "");
                if (orgVO != null) {
                    changeRegionName(args, orgVO.getParentOrgName(),orgVO.getParentOrgCode());
                } else {
                    throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
                }
            } else if (permissionUtil.matchCustomerManager(userDetailVO, isNewDataAuth)) {
                log.info("工号:{}命中了客户经理角色！", userDetailVO.getJobNumber());
                change(args, query -> query.setUserId(StringUtils.isEmpty(userDetailVO.getJianzhiJobNumber()) ? userDetailVO.getJobNumber() : userDetailVO.getJianzhiJobNumber()));
                changeOrgName(args, userDetailVO.getOrgName(), userDetailVO.getOrgCode());
                OrgVO orgVO = orgService.getOrgByHrOrgId(userDetailVO.getHrOrgId() + "");
                if (orgVO != null) {
                    changeRegionName(args, orgVO.getParentOrgName(), orgVO.getParentOrgCode());
                } else {
                    throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
                }
            } else if(permissionUtil.matchHeadEmpRole(userDetailVO, isNewDataAuth)){
                log.info("工号:{}命中总部员工角色", userDetailVO.getJobNumber());
                change(args, query -> query.setUserId(StringUtils.isEmpty(userDetailVO.getJianzhiJobNumber()) ? userDetailVO.getJobNumber() : userDetailVO.getJianzhiJobNumber()));
                changeOrgName(args, userDetailVO.getOrgName(), userDetailVO.getOrgCode());
                OrganizationVO orgVO = bmsService.getOrganizationByHrOrgId(userDetailVO.getHrOrgId());
                if (orgVO != null) {
                    changeRegionName(args, orgVO.getParentOrgName(), orgVO.getParentOrgCode());
                } else {
                    throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
                }
            }
            else {
                log.info("工号:{}没有命中任意角色", userDetailVO.getJobNumber());
                throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
            }
        }

    }

    public void setChannelSuperQueryOrgInfo(Object[] args, UserDetailVO contextUser) {
        OrganizationBaseVO orgInfo = permissionUtil.getChannelSuperOrgInfo(contextUser);
        changeRegionName(args, orgInfo.getOrgName(),orgInfo.getOrgCode());
    }

    /**
     * 如果是总部机构树下的事业部、区域就需要重新设置
     *
     * @param args
     * @param contextUser
     * @param type
     */
    public void setQueryOrgInfo(Object[] args, UserDetailVO contextUser, String type) {
        String userOrgPath = contextUser.getHrOrgTreePath();
        if (permissionUtil.isHeadOrgTree(userOrgPath)) {
            OrganizationBaseVO org = bmsService.getBizOrg(contextUser.getOrgCode());
            userOrgPath = org.getTreePath();
            if (Objects.equals(type, PermissionUtil.ROLE_TYPE_BRANCHBU)) {
                changeBranchBu(args, org.getOrgName(), org.getOrgCode(), userOrgPath);
            } else if (Objects.equals(type, PermissionUtil.ROLE_TYPE_REGION)) {
                changeRegionName(args, org.getOrgName(), org.getOrgCode());
            }
        } else {
            if (Objects.equals(type, PermissionUtil.ROLE_TYPE_BRANCHBU)) {
                changeBranchBu(args, contextUser.getOrgName(), contextUser.getOrgCode(), userOrgPath);
            } else if (Objects.equals(type, PermissionUtil.ROLE_TYPE_REGION)) {
                changeRegionName(args, contextUser.getOrgName(), contextUser.getOrgCode());
            }
        }
    }

    private void changeBranchBu(Object[] args, String branchBuName, String brancBuCode, String orgPath) {
        for (Object arg : args) {
            if (arg instanceof BaseQuery) {
                BaseQuery tmp = (BaseQuery) arg;
                tmp.setBranchBuName(branchBuName);
                tmp.setBranchBuCode(brancBuCode);
                if (tmp.getOrgPath() == null) {
                    tmp.setOrgPath(orgPath);
                }
            }
        }
    }

    private void change(Object[] args, Consumer<BaseQuery> setter) {
        for (Object arg : args) {
            if (arg instanceof BaseQuery) {
                setter.accept((BaseQuery) arg);
            }
        }
    }

    /**
     * @param args
     * @param consumer
     */
    private void peekArg(Object[] args, Consumer<BaseQuery> consumer) {
        for (Object arg : args) {
            if (arg instanceof BaseQuery) {
                consumer.accept((BaseQuery) arg);
            }
        }
    }

    private void changeRegionName(Object[] args, String regionName,String regionCode) {
        for (Object arg : args) {
            if (arg instanceof BaseQuery) {
                BaseQuery tmp = (BaseQuery) arg;
                tmp.setRegionName(regionName);
                tmp.setRegionCode(regionCode);
            }
        }
    }

    private void changeZone(Object[] args, String zoneName, String zoneCode, String orgPath) {
        for (Object arg : args) {
            if (arg instanceof BaseQuery) {
                BaseQuery tmp = (BaseQuery) arg;
                tmp.setZoneName(zoneName);
                tmp.setZoneCode(zoneCode);
                if (tmp.getOrgPath() == null) {
                    tmp.setOrgPath(orgPath);
                }

            }
        }
    }

    private void changeOrgName(Object[] args, String orgName,String orgCode) {
        for (Object arg : args) {
            if (arg instanceof BaseQuery) {
                BaseQuery tmp = (BaseQuery) arg;
                tmp.setOrgName(orgName);
                tmp.setOrgCode(orgCode);
            }
        }
    }

}
