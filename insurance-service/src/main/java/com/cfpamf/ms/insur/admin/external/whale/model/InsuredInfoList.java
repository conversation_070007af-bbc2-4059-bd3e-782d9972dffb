package com.cfpamf.ms.insur.admin.external.whale.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@FieldDefaults(level = AccessLevel.PRIVATE)
@Data
public class InsuredInfoList {
    /**
     * 保司计划编码
     */
    @ApiModelProperty("保司计划编码")
    String companyPlanCode;

    @ApiModelProperty("被保人地址")
    String insuredAddress;

    @ApiModelProperty("被保人生日")
    String insuredBirthday;

    String insuredCareer;

    String insuredCityCode;

    String insuredCompany;

    String insuredEducation;

    String insuredEmail;

    @ApiModelProperty("被保人性别")
    Integer insuredGender;

    @ApiModelProperty("被保人证件")
    String insuredIdCard;

    String insuredIdCardPermanent;

    String insuredIdCardValidityEnd;

    String insuredIdCardValidityStart;

    @ApiModelProperty("被保人证件类型")
    String insuredIdType;

    String insuredMarital;

    Integer insuredMedicare;

    String insuredMobile;

    String insuredName;

    String insuredNation;

    String insuredNationality;

    String insuredOccupationalCategory;

    String insuredPosition;

    String insuredPostcode;

    String insuredProvinceCode;

    String insuredRegionCode;

    String insuredRelation;

    @ApiModelProperty("电子保单地址")
    private String ePolicyUrl;

    @ApiModelProperty("被保人险种列表")
    List<ProductInfoList> productInfoList;

    public BigDecimal sumPremium() {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return BigDecimal.ZERO;
        } else {
            return productInfoList.stream()
                    .map(ProductInfoList::getPremium)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }

    public BigDecimal sumAmount() {
        if (CollectionUtils.isEmpty(productInfoList)) {
            return BigDecimal.ZERO;
        } else {
            return productInfoList.stream()
                    .map(ProductInfoList::getCoverage)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
    }


}
