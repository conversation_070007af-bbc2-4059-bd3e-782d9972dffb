package com.cfpamf.ms.insur.base.util;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 获取http request 参数信息
 *
 * <AUTHOR>
 */
public class HttpRequestUtil {

    private static JwtHelper jwtHelper = SpringFactoryUtil.getBean(JwtHelper.class);

    private HttpRequestUtil() {
    }

    /**
     * 获取用户Id
     *
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String getUserId() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo.getJobNumber();
    }

    /**
     * 获取当前用户信息
     *
     * @return
     */
    public static JwtUserInfo getUser() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo;
    }

    /**
     * 获取当前用户信息
     *
     * @return
     */
    public static JwtUserInfo getUserOrThrowExp() {
        String token = getToken();
        if (token == null) {
            throw new BizException(ExcptEnum.HTTP_METHOD_ERROR_000098.getCode(), "登录信息丢失");
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo;
    }


    /**
     * 获取用户Id
     *
     * @return
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static String getUserName() {
        String token = getToken();
        if (token == null) {
            return null;
        }
        JwtUserInfo jwtUserInfo = jwtHelper.getUserFromToken(token);
        return jwtUserInfo.getUserName();
    }

    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        String token = request.getHeader(BaseConstants.API_AUTH_NAME);
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(BaseConstants.API_AUTH_NAME);
        }
        return token;
    }
    //

    /**
     * 获取微信openid
     *
     * @return
     */
    public static String getWxOpenId() {
        HttpServletRequest request = getRequest();
        String openId = request.getHeader(BaseConstants.WX_HTTP_HEAD_OPENID);
        if (StringUtils.isEmpty(openId)) {
            openId = request.getParameter(BaseConstants.WX_HTTP_HEAD_OPENID);
        }
        return openId;
    }

    public static String getAuthToken(){
        HttpServletRequest request = getRequest();
        String token = request.getHeader(BaseConstants.API_AUTH_NAME);
        if (StringUtils.isEmpty(token)) {
            token = request.getParameter(BaseConstants.API_AUTH_NAME);
        }
        return token;
    }

    /**
     * 获取http 请求头 user-agent
     *
     * @return
     */
    public static String getUserAgent() {
        return getRequest().getHeader("user-agent");
    }

    /**
     * 获取http 请求头 referer
     *
     * @return
     */
    public static String getReferer() {
        return getRequest().getHeader("referer");
    }

    /**
     * 获取requestUrl
     *
     * @return
     */
    public static String getRequestURI() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return getRequest().getRequestURI();
        }
        return "";
    }

    /**
     * 获取request请求所有参数
     *
     * @return
     */
    public static Map<String, String> getParameterMap() {
        Map<String, String> params = new HashMap<>();
        HttpServletRequest request = getRequest();
        if (request == null) {
            return params;
        }
        Enumeration<String> enums = getRequest().getParameterNames();
        while (enums.hasMoreElements()) {
            String paraName = enums.nextElement();
            params.put(paraName, getRequest().getParameter(paraName));
        }
        return params;
    }

    /**
     * 获取request请求所有参数
     *
     * @param request
     * @return
     */
    public static Map<String, String> getParameterMap(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Enumeration<String> enums = request.getParameterNames();
        while (enums.hasMoreElements()) {
            String paraName = enums.nextElement();
            params.put(paraName, request.getParameter(paraName));
        }
        return params;
    }

    public static String getIpAddr() {
        return getIpAddr(Objects.requireNonNull(getRequest()));
    }

    /**
     * 获取客户端Id
     *
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        String[] ips = ip.split(",");
        if (ips.length > 0) {
            ip = ips[ips.length - 1];
        }
        return org.apache.commons.lang3.StringUtils.trim(ip);
    }

    /**
     * 获取当前请求主体
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;

    }

    /**
     * 获取工号
     *
     * @return
     */
    public static String getUserIdOrThrowExp() {
        return getUserOrThrowExp().getJobNumber();
    }


    private static final int BUFF_SIZE = 1024 * 8;

    public static String read(HttpServletRequest request) throws IOException {
        InputStream is = request.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
       return  HttpRequestUtil.read(reader);
    }

    public static String read(Reader reader) throws IOException {
        StringWriter writer = new StringWriter();
        try {
            writer(reader, writer);
            return writer.getBuffer().toString();
        } finally {
            writer.close();
        }
    }

    public static void writer(Reader reader, Writer writer) throws IOException {
        writer(reader, writer, BUFF_SIZE);
    }

    private static void writer(Reader reader, Writer writer, int BUFF_SIZE) throws IOException {
        int len;
        long tot = 0;
        char[] buf = new char[BUFF_SIZE];
        while ((len=reader.read(buf))!=-1){
            writer.write(buf,0,len);
        }
    }

}
