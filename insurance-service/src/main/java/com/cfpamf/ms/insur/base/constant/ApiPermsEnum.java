package com.cfpamf.ms.insur.base.constant;

/**
 * 保险权限枚举
 *
 * <AUTHOR>
 **/
public enum ApiPermsEnum {

    /**
     * 对外接口权限
     */
    PUBLIC("PUBLIC", "对外接口权限"),

    /**
     * 允许访问 (默认不要验证权限)
     */
    NOT_NEED("INS_NOT_NEED", "允许访问"),

    /**
     * 车险-今日汇总
     */
    AM_DASHBOARD("INS_AM_DASHBOARD", "车险/今日汇总"),

    /**
     * 车险-订单查询
     */
    AM_ORDER_QUERY("INS_CAR_ORDER_QUERY", "车险/订单管理/车险订单查询"),

    /**
     * 车险-订单导出
     */
    AM_ORDER_EXPORT("INS_CAR_ORDER_REPORT", "车险/订单管理/车险订单导出"),

    /**
     * 车险-保单提成查询
     */
    AM_ORDER_CMISON_QUERY("INS_CAR_COMMISSION_QUERY", "车险/提成管理/车险保单提成查询"),

    /**
     * 车险-保单提成导出
     */
    AM_ORDER_CMISON_EXPORT("INS_CAR_COMMISSION_REPORT", "车险/提成管理/车险保单提成导出"),

    /**
     * 小额保险-订单查询
     */
    SM_ORDER_QUERY("INS_SM_ORDER_QUERY", "小额保险/订单管理/订单查询"),

    /**
     * 小额保险-订单导出
     */
    SM_ORDER_EXPORT("INS_SM_ORDER_REPORT", "小额保险/订单管理/订单导出"),

    /**
     * 小额保险-补单增加
     */
    SM_ORDER_SUPMT_ADD("INS_SM_ORDER_SUPMT_ADD", "小额保险/订单管理/补录订单"),

    /**
     * 小额保险-补单查询
     */
    SM_ORDER_SUPMT_QUERY("INS_SM_ORDER_QUERY", "小额保险/订单管理/补单记录"),

    /**
     * 小额保险-保单提成查询
     */
    SM_ORDER_CMISON_QUERY("INS_SM_ORDER_CMISON_QUERY", "小额保险/提成管理/保单提成明细查询"),

    /**
     * 小额保险-保单提成导出
     */
    SM_ORDER_CMISON_EXPORT("INS_SM_COMMISSION_REPORT", "小额保险/提成管理/保单提成明细导出"),

    /**
     * 小额保险-理赔列表查询
     */
    SM_CLAIM_QUERY("INS_SM_CLAIM_REPORT", "小额保险/理赔管理/理赔列表查询"),

    /**
     * 小额保险-理赔列表导出
     */
    SM_CLAIM_DOWNLOAD("INS_SM_CLAIM_REPORT", "小额保险/理赔管理/理赔列表导出"),

    /**
     * 小额保险-产品理赔数据查询
     */
    SM_CLAIM_PRODUCT_QUERY("INS_SM_CLAIM_REPORT", "小额保险/理赔管理产品理赔数据查询"),

    /**
     * 小额保险-产品理赔数据导出
     */
    SM_CLAIM_PRODUCT_DOWNLOAD("INS_SM_CLAIM_REPORT", "小额保险/理赔管理/产品理赔数据导出"),

    /**
     * 小额保险-机构理赔数据查询
     */
    SM_CLAIM_REGION_QUERY("INS_SM_CLAIM_REPORT", "小额保险/理赔管理/机构理赔数据查询"),

    /**
     * 小额保险-机构理赔数据导出
     */
    SM_CLAIM__REGION_DOWNLOAD("INS_SM_CLAIM_REPORT", "小额保险/理赔管理/机构理赔数据导出"),

    /**
     * 小额保险-产品新建
     */
    SM_PRODUCT_ADD("INS_SM_PRODUCT_ADD", "小额保险/配置管理/产品配置/保存产品"),

    /**
     * 小额保险-产品查询
     */
    SM_PRODUCT_QUERY("INS_SM_PRODUCT_QUERY", "小额保险/配置管理/产品配置/产品查询"),

    /**
     * 小额保险-参数增加
     */
    SM_PARAM_ADD("INS_SM_PARAM_ADD", "小额保险/配置管理/参数配置/保存参数"),

    /**
     * 小额保险-参数查询
     */
    SM_PARAM_QUERY("INS_SM_PARAM_QUERY", "小额保险/配置管理/参数配置/参数查询"),

    /**
     * 小额保险-提成配置增加
     */
    SM_CMISON_ADD("INS_SM_CMISON_ADD", "小额保险/配置管理/提成配置/保存提成配置"),

    /**
     * 小额保险-提成配置查询
     */
    SM_CMISON_QUERY("INS_SM_CMISON_QUERY", "小额保险/配置管理/提成配置/提成配置查询"),

    /**
     * 小额保险-提成配置增加
     */
    INS_SM_RC_ADD("INS_SM_RC_ADD", "小额保险/配置管理/轮播图配置/保存轮播图"),

    /**
     * 小额保险-提成配置查询
     */
    INS_SM_RC_QUERY("INS_SM_RC_QUERY", "小额保险/配置管理/轮播图配置/轮播图查询"),

    /**
     * 小额保险-提成配置增加
     */
    INS_SM_POSTER_ADD("INS_SM_POSTER_ADD", "小额保险/配置管理/海报图库/保存海报图库"),

    /**
     * 小额保险-提成配置查询
     */
    INS_SM_POSTER_QUERY("INS_SM_POSTER_QUERY", "小额保险/配置管理/海报图库/海报图库查询"),

    /**
     * 小额保险-提成配置增加
     */
    INS_SM_DOC_ADD("INS_SM_DOC_ADD", "小额保险/配置管理/资料专区/保存资料"),

    /**
     * 小额保险-提成配置查询
     */
    INS_SM_DOC_QUERY("INS_SM_DOC_QUERY", "小额保险/配置管理/资料专区/资料查询"),

    /**
     * 小额保险-保险公司增加
     */
    SM_COMPANY_ADD("INS_SM_COMPANY_ADD", "基础信息/保险公司配置/保存保险公司"),

    /**
     * 小额保险-保险公司查询
     */
    SM_COMPANY_QUERY("INS_SM_COMPANY_QUERY", "基础信息/保险公司配置/保险公司查询"),

    /**
     * 小额保险-字典增加
     */
    SM_DICTIONY_ADD("INS_SM_DICTIONY_ADD", "基础信息/配置管理/保存字典"),

    /**
     * 小额保险-字典查询
     */
    SM_DICTIONY_QUERY("INS_SM_DICTIONY_QUERY", "基础信息/配置管理/字典查询"),

    /**
     * 小额保险-用户查询导出
     */
    SM_EMPLOYEE_DOWNLOAD("INS_EMPLOYEE_REPORT", "基础信息/用户管理/用户列表导出"),

    /**
     * 小额保险-用户查询导出
     */
    SM_EMPLOYEE_QUERY("SM_EMPLOYEE_QUERY", "基础信息/用户管理/用户列表查询"),

    /**
     * 小额保险-客户列表导出
     */
    SM_CUSTOMER_QUERY("M005004001_F001", "小额保险/客户管理/客户列表查询"),

    /**
     * 小额保险-客户列表导出
     */
    SM_CUSTOMER_DOWNLOAD("INS_CUSTOMER_REPORT", "小额保险/客户管理/客户列表导出"),

    /**
     * 小额保险-变更负责人
     */
    INS_CHANGE_EMPLOYEE("INS_CHANGE_EMPLOYEE", "小额保险/客户管理/变更负责人"),

    /**
     * 小额保险-用户列表
     */
    INS_USER_QUERY("M005003_F001", "小额保险/用户管理/用户管理列表"),




    /**
     * 小额保险-订单新导出
     */
    SM_ORDER_EXPORT_NEW("ORDER_LIST_EXPORT", "小额保险/订单管理/订单(新)导出"),


    /**
     * 小额保险-分销订单导出
     */
    SM_DIST_ORDER_EXPORT("SM_DIST_ORDER_EXPORT", "小额保险/订单管理/分销订单导出"),

    /**
     * 小额保险-佣金配置新增
     */
    COMMISSION_CONFIG_ADD("COMMISSION_ADD_CONFIG", "小额保险/配置管理/佣金配置新增"),

    /**
     * 小额保险-佣金配置删除
     */
    COMMISSION_CONFIG_DEL("COMMISSION_DELETE_CONFIG", "小额保险/配置管理/佣金配置删除"),
    /**
     * 小额保险-佣金配置暂存
     */
    COMMISSION_CONFIG_TEMPORARY("COMMISSION_CONFIG_TEMPORARY", "小额保险/配置管理/佣金配置暂存"),

    COMMISSION_LIST_EXPORT("COMMMISSION_LIST_EXPORT","小额保险/提成管理/24版提成明细导出"),

    ABBREVIATED_VERSION("ABBREVIATED_VERSION","小额保险/提成管理/提成明细精简版"),

    STANDARD_VERSION("STANDARD_VERSION","小额保险/提成管理/提成明细标准版"),

    MANUAL_CORRECTION("REVERSE","小额保险/提成管理/提成明细标准版/手动冲正"),
    /**
     * 管理员权限
     */
    ADMIN("ADMIN", "允许访问");

    /**
     * 权限编码
     */
    private String authCode;

    /**
     * 权限名称
     */
    private String authName;

    /**
     * 构造函数
     *
     * @param authCode
     * @param authName
     */
    ApiPermsEnum(String authCode, String authName) {
        this.authCode = authCode;
        this.authName = authName;
    }

    public String getAuthCode() {
        return authCode;
    }

    public String getAuthName() {
        return authName;
    }
}
