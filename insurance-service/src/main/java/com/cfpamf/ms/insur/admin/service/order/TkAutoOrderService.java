package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.auto.EnumTkSyncState;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AmPolicyTkDetailMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AmPolicyTkMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AmSyncTkRecordMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.event.OrderCustomerChangeEvent;
import com.cfpamf.ms.insur.admin.external.ChannelOrderService;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.tk.TkConsts;
import com.cfpamf.ms.insur.admin.external.tk.auto.TkAutoCvt;
import com.cfpamf.ms.insur.admin.external.tk.auto.TkAutoServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.tk.auto.enums.EnumTkAutoRiskCode;
import com.cfpamf.ms.insur.admin.external.tk.auto.model.*;
import com.cfpamf.ms.insur.admin.external.tk.auto.util.TkAutoCvtHelper;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.convertor.SmOrderCarConvert;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.auto.AutoOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmPolicyTk;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmPolicyTkDetail;
import com.cfpamf.ms.insur.admin.pojo.po.auto.AmSyncTkRecord;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderCar;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.XxlLogger;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2020/11/30 16:43
 */
@Service
@Slf4j
public class TkAutoOrderService extends AbstractAutoOrderService {


    @Autowired
    private TkAutoServiceAdapterImpl adapter;

    @Autowired
    TkAutoCvtHelper cvtHelper;

    @Autowired
    SmOrderMapper orderMapper;
    /**
     * 保单列表
     */
    @Autowired
    AmPolicyTkMapper policyTkMapper;

    /**
     * 保单详情
     */
    @Autowired
    AmPolicyTkDetailMapper policyTkDetailMapper;

    /**
     * 同步记录
     */
    @Autowired
    AmSyncTkRecordMapper recordMapper;

    /**
     * 获取泰康车险地址
     *
     * @return
     */
    public String getJumpAutoUri(String tkUserId, int productId) {

        SmProductDetailVO productById = productService.getProductById(productId);

        String url = productById.getH5Url();
        if (UrlUtils.isAbsoluteUrl(url)) {
            String autoQueryString = adapter.getAutoQueryString(tkUserId);
            String jumpUrl = url + "?" + autoQueryString;
            if (url.contains("?")) {//  如果有queryString
                jumpUrl = url + "&" + autoQueryString;
            }
            log.info("泰康车险{}跳转{}:{}", productId, tkUserId, jumpUrl);
            return jumpUrl;
        } else {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042);
        }
    }

    /**
     * 同步数据
     *
     * @param start
     * @param end
     */
    public synchronized void sync(LocalDate start, LocalDate end) {

        XxlLogger.info(log, "开始同步数据" + start + "~" + end);
        TkAutoRespHelper<TkAutoQueryPolicyResp> queryTkListHelper = adapter.queryTkList(start, end);
        TkAutoQueryPolicyResp queryTkList = queryTkListHelper.getData();

        AmSyncTkRecord record = new AmSyncTkRecord();
        record.setBatchNo(OrderNoGenerator.timeBatchNo());
        record.setRespContent(JSON.toJSONString(queryTkList));
        record.setEndDate(end);
        record.setStartDate(start);
        record.setOrderCount(queryTkList.getQueryPolicyDtoList().size());
        recordMapper.insertUseGeneratedKeys(record);

        if (!CollectionUtils.isEmpty(queryTkList.getQueryPolicyDtoList())) {
            //查询数据库当天数据
            List<AmPolicyTk> dbDatas = selectTkRecordByDate(start, end);
            //排除当天已有数据
            List<AmPolicyTk> notExists = queryTkList.getQueryPolicyDtoList()
                    .stream()
                    .filter(tk -> dbDatas.stream()
                            .noneMatch(db -> Objects.equals(db.getSubPolicyNo(), tk.getSubPolicyNo())))
                    .map(TkAutoCvt.INS::policy)
                    .peek(policy -> policy.setState(EnumTkSyncState.INIT.getCode()))
                    .collect(Collectors.toList());
            log.info("当前同步到的新数据{}", notExists.size());
            insertTkList(notExists);
        }

        syncDetail();
    }

    @Transactional(rollbackFor = Exception.class)
    public List<String> syncPolicyDetail(@NotNull String policyNo) {
        log.info("syncPolicyDetail start ={}",policyNo);
        TkAutoRespHelper<TkAutoQueryPolicyDetailResp> detail = null;
        try {
            detail = adapter.queryTkDetail(policyNo);
            log.info("tk syncPolicyDetail ================ {}",JSON.toJSON(detail));
        } catch (Exception e) {
            log.warn("tk syncPolicyDetail error",e);
            detail = new TkAutoRespHelper<>();
            detail.setAppCode("1");
            detail.setAppMessage(e.getMessage());
        }

        if (!detail.isSuccess()) {
            // 同步失败 保存数据
            syncError(policyNo, detail.errorMsg());
            return null;
        } else {
            return syncSuccess(policyNo, detail.getData(), detail.getContent());
        }

    }

    /**
     * 同步所有未同步的数据
     */
    public void syncDetail() {
        List<AmPolicyTk> amPolicyTks = selectNotSync();
        log.info("开始同步明细数据:{}", amPolicyTks.size());
        TkAutoOrderService proxy = (TkAutoOrderService) AopContext.currentProxy();
        amPolicyTks.stream()
                .map(AmPolicyTk::getPolicyNo).distinct().forEach(policyNo -> {
            try {
                //接口限制请求频次为1s/次 泰康车险针对单个渠道调用接口有限制 执行一次睡一秒
                //实际1s会经常被拒绝 改成3l
                Thread.sleep(3000L);
            } catch (InterruptedException e) {
                // skip
            }
            List<String> s = proxy.syncPolicyDetail(policyNo);
            //同步成功
            if (!CollectionUtils.isEmpty(s)) {
                s.forEach(orderId-> busEngine.publish(new OrderCommissionChangeEvent(orderId)));
                //同步成功之后驾乘险特殊处理
                s.forEach(orderId-> afterSynchDrivingHandle(orderId));

            }
        });
    }

    public void syncError(String policyNo, String msg) {

        AmPolicyTk update = new AmPolicyTk();
        update.setState(EnumTkSyncState.ERROR.getCode());
        update.setRemark(msg);
        if (StringUtils.length(update.getRemark()) > 500) {
            update.setRemark(StringUtils.left(update.getRemark(), 500));
        }
        updateByPolicyNo(policyNo, update);
    }

    /**
     * 同步成功 修改数据
     *
     * @param tkDetail
     */
    public List<String> syncSuccess(final String policyNo, TkAutoQueryPolicyDetailResp tkDetail, String content) {

        //修改所有子弹好
        tkDetail.getRiskInfoList().forEach(riskInfoD -> {
            TkAutoBaseInfoD baseInfoD = riskInfoD.getBaseInfoD();
            AmPolicyTk update = new AmPolicyTk();
            update.setState(EnumTkSyncState.SYNCED.getCode());
            update.setRiskCode(riskInfoD.getRiskCode());
            update.setRiskName(baseInfoD.getRiskName());
            update.setBenchmarkPremium(baseInfoD.getPremium());
            update.setIssueDate(baseInfoD.getIssueDate());
            update.setStartDate(baseInfoD.getStartDate());
            update.setEndDate(baseInfoD.getEndDate());
            update.setPolicyUrl(baseInfoD.getPolicyViewList()
                    .getPolicyData().getPolicyDownLoadUrl());

            int i = updateBySubPolicyNo(baseInfoD.getSubPolicyNo(), update);
            //补偿操作 如果没有子单号就插入
            if (i <= 0) {
                update.setSubPolicyNo(baseInfoD.getSubPolicyNo());
                update.setPolicyNo(policyNo);
                update.setRiskCode(riskInfoD.getRiskCode());
                update.setRiskName(baseInfoD.getRiskName());
                update.setInputDate(baseInfoD.getInputDate());
                update.setRemark("补录");
                policyTkMapper.insertUseGeneratedKeys(update);
            }
        });

        //查找泰康车险详情是否存在驾乘险,如果存在则把商业险或者交强险的投被保人关系赋值给驾乘险
        TkAutoRiskInfoD drivingTkAutoRiskInfoD = cvtHelper.findDrivingTkAutoRiskInfoD(tkDetail.getRiskInfoList());
        log.info("find drivingTkAutoRiskInfoD================ [{}]",JSON.toJSON(drivingTkAutoRiskInfoD));
        if(drivingTkAutoRiskInfoD != null){
            //查找泰康车险详情中险种列表靠前的商业险或者驾乘险的投被保人关系
            List<TkAutoRelatedInfoD> tkAutoRelatedInfoDListTmp = cvtHelper.getRelatedInfoDList(tkDetail.getRiskInfoList());
            drivingTkAutoRiskInfoD.setRelatedInfoDList(tkAutoRelatedInfoDListTmp);
            log.info("update drivingTkAutoRiskInfoD================ [{}]",JSON.toJSON(drivingTkAutoRiskInfoD));
        }

        AmPolicyTkDetail detail = new AmPolicyTkDetail();
        detail.setPolicyNo(policyNo);
        detail.setContent(content);
        policyTkDetailMapper.insertUseGeneratedKeys(detail);

        //判断保司险种是否成功入库,如果已入库则返回null
        if(isExistsSuccessSave(tkDetail)){
            log.warn("泰康车险信息已入库触发重复规则,不处理数据 content= [{}]",content);
            return null;
        }

        AmPolicyTk amPolicyTk = policyTkMapper.selectOneByPolicyNo(policyNo);
        Map<String,String> planCodeOrderIdMap = Maps.newHashMap();
        List<SmCreateOrderSubmitRequest> submitRequests = cvtHelper.cvtOrderInfo(amPolicyTk, tkDetail, planCodeOrderIdMap);
        log.info("show planCodeOrderIdMap= [{}]",policyNo,content,JSON.toJSONString(planCodeOrderIdMap));
        log.info("syncSuccess policyNo= [{}]; content= [{}]; planCodeOrderIdMap= [{}]",policyNo,content,JSON.toJSONString(planCodeOrderIdMap));
        List<String> orderIds = submitRequests.stream().map(SmCreateOrderSubmitRequest::getFhOrderId).collect(Collectors.toList());
        //车险独立模块
        List<AutoOrderDTO> autoOrders = cvtHelper.cvtAutoInfoList(amPolicyTk, orderIds, tkDetail, planCodeOrderIdMap);
        autoOrders.forEach(this::saveAutoInfo);
        //保存到当前的订单表里面

        submitRequests.forEach(submitRequest->{
            String productId = submitRequest.getProductId();
            SmPlanVO planVO = productService.getPlanByFhProductId(productId);
            if (Objects.nonNull(planVO)) {
                submitRequest.setProductId(planVO.getProductId() + "");
                submitRequest.setPlanId(planVO.getId());
            }
            //泰康驾乘险走普通险种逻辑
            if(EnumTkAutoRiskCode.DR.getCode().equals(productId)){
                saveOrderInfoForTkDriving(submitRequest, null, planVO,drivingTkAutoRiskInfoD);
            }else{
                saveOrderInfo(submitRequest, null, planVO);
            }
        });

        return orderIds;
    }

    public void updateByPolicyNo(String policyNo, AmPolicyTk update) {
        Example example = new Example(AmPolicyTk.class);
        example.createCriteria().andEqualTo("policyNo", policyNo);
        policyTkMapper.updateByExampleSelective(update, example);
    }

    public int updateBySubPolicyNo(String subPolicyNo, AmPolicyTk update) {

        Example example = new Example(AmPolicyTk.class);
        example.createCriteria().andEqualTo("subPolicyNo", subPolicyNo);
        return policyTkMapper.updateByExampleSelective(update, example);
    }

    /**
     * 插入列表
     */
    public List<AmPolicyTk> selectNotSync() {

        Example example = new Example(AmPolicyTk.class);
        example.createCriteria().andIn("state", Arrays.asList(EnumTkSyncState.INIT.getCode(), EnumTkSyncState.ERROR.getCode()));
        return policyTkMapper.selectByExample(example);
    }

    /**
     * 插入列表
     *
     * @param notExists
     */
    public void insertTkList(List<AmPolicyTk> notExists) {
        if (!CollectionUtils.isEmpty(notExists)) {
            policyTkMapper.insertList(notExists);
        }
    }

    /**
     * 根据时间查询 保单列表
     */
    public List<AmPolicyTk> selectTkRecordByDate(LocalDate start, LocalDate end) {

        Example example = new Example(AmPolicyTk.class);
        example.createCriteria()
                .andGreaterThanOrEqualTo("inputDate", start)
                .andLessThan("inputDate", end.plusDays(1L));
        return policyTkMapper.selectByExample(example);

    }

    @Override
    protected String channel() {
        return EnumChannel.TK_AM.getCode();
    }

    @Override
    protected ChannelOrderService orderService() {
        return adapter;
    }

    @Override
    public boolean support(String channel) {
        return Objects.equals(channel, channel());
    }

    @Override
    public Map<String, String> updateOrderPolicyInfo(String orderId) {

        return super.updateByLocalIfCancel(orderId);
    }


    @Override
    protected OrderSubmitResponse submitSuccessAfter(String userUniqueId, SmPlanVO planVo, OrderSubmitRequest dto, OrderSubmitRequest change, OrderSubmitResponse submitResponse) {
        throw new UnsupportedOperationException("信美暂不支持");
    }

    @Override
    public OrderSubmitResponse submitOrder(String userUniqueId, OrderSubmitRequest dto) {
        throw new UnsupportedOperationException("信美暂不支持订单创建");
    }

    @Override
    public void saveOrderInfo(OrderSubmitRequest dto, OrderSubmitResponse smOrderResp, SmPlanVO planVo) {
        SmCreateOrderSubmitRequest createDto = OrderConvertor.mapperSmCreateOrderSubmitRequest(dto, smOrderResp, planVo);

        createDto.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        createDto.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        createDto.getInsuredPerson().forEach(ip -> ip.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS));
        createDto.setAppNo(dto.getAppNo());
        orderMapper.insertOrder(createDto);
        orderMapper.insertOrderApplicant(createDto);
        orderMapper.insertOrderInsured(createDto);

        //保存车辆信息
        SmOrderCar smOrderCar = SmOrderCarConvert.CNT.cvtDTO(createDto.getCarInfo());
        smOrderCar.setFhOrderId(createDto.getFhOrderId());
        carMapper.insertUseGeneratedKeys(smOrderCar);

        updateOrderPaymentTimeAndCommission(createDto.getFhOrderId());

    }

    /**
     * 泰康驾乘险保存处理
     * @param dto
     * @param smOrderResp
     * @param planVo
     */
    public void saveOrderInfoForTkDriving(OrderSubmitRequest dto, OrderSubmitResponse smOrderResp, SmPlanVO planVo, TkAutoRiskInfoD drivingTkAutoRiskInfoD) {
        String relationship = null;
        if(drivingTkAutoRiskInfoD != null){
            List<TkAutoRelatedInfoD> relatedInfoDList = drivingTkAutoRiskInfoD.getRelatedInfoDList();
            //查找驾乘险被保人数据
            Optional<TkAutoRelatedInfoD> opl = relatedInfoDList.stream().filter(x ->TkConsts.TK_DRIVING_INSURE_FLAG_INSURED.equals(x.getInsureFlag())).findFirst();
            TkAutoRelatedInfoD tkAutoRelatedInfoD = opl.orElse(null);
            log.info("saveOrderInfoForTkDriving relatedInfoDList===================== {}; tkAutoRelatedInfoD= {}",JSON.toJSONString(relatedInfoDList),JSON.toJSONString(tkAutoRelatedInfoD));
            if(tkAutoRelatedInfoD == null){
                log.error("泰康车险交强险或者商业险被保人数据缺失 req= {}",JSON.toJSONString(dto));
            }else{
                if(TkConsts.TK_DRIVING_INSURE_TYPE_CODE_PERSONAL.equals(tkAutoRelatedInfoD.getInsureTypeCode())){
                    relationship = TkConsts.TK_DRIVING_INSURE_RELATIONSHIP;
                }
            }
        }
        final String relationshipVal = relationship;
        //驾乘险信息再处理
        dto.getInsuredPerson().forEach(x ->{
            x.setRelationship(relationshipVal);
        });

        SmCreateOrderSubmitRequest createDto = OrderConvertor.mapperSmCreateOrderSubmitRequest(dto, smOrderResp, planVo);

        createDto.setPayStatus(SmConstants.ORDER_STATUS_PAYED);
        createDto.setOrderState(SmConstants.ORDER_STATUS_PAYED);
        createDto.getInsuredPerson().forEach(ip -> ip.setAppStatus(SmConstants.POLICY_STATUS_SUCCESS));
        createDto.setAppNo(dto.getAppNo());
        orderMapper.insertOrder(createDto);
        orderMapper.insertOrderApplicant(createDto);
        orderMapper.insertOrderInsured(createDto);
        //驾乘险需要更新支付时间才能结佣
        int updateCount = orderMapper.updateOrderPaymentTime(createDto.getFhOrderId());
        if(updateCount == 0){
            log.error("saveOrderInfoForTkDriving update smOrder paymentTime error createDto= [{}]; dto= [{}]",JSON.toJSONString(createDto),JSON.toJSONString(dto));
            throw new BizException(ExcptEnum.BIZ_VERIFY.getCode(),"泰康驾乘险订单记录更新支付时间异常");
        }
    }

    /**
     * 同步成功之后驾乘险特殊处理
     * @param fhOrderId
     * @return
     */
    private void afterSynchDrivingHandle(String fhOrderId){
        List<String> fhProductIdList = orderMapper.getFhProductIdByOrderId(fhOrderId);
        log.info("afterSynchDrivingHandle firstInfo fhProductIdList= [{}]; fhOrderId= [{}]",JSON.toJSONString(fhProductIdList),fhOrderId);
        if(CollectionUtils.isEmpty(fhProductIdList)){
            log.warn("fhOrderId= [{}] 未查到对应的保司计划编码",fhOrderId);
            return;
        }
        if(fhProductIdList.size() > 1){
            log.warn("fhOrderId= [{}] 查到多条对应的保司计划编码",fhOrderId);
            return;
        }
        String fhProductId = fhProductIdList.get(0);
        log.info("afterSynchDrivingHandle fhProductId= [{}]; fhOrderId= [{}]",fhProductId,fhOrderId);
        if(EnumTkAutoRiskCode.DR.getCode().equals(fhProductId)){
            log.info("afterSynchDrivingHandle 开始驾乘险同步成功发送MQ fhProductId= [{}]; fhOrderId= [{}]",fhProductId,fhOrderId);
            //出单后逻辑
            busEngine.publish(new OrderCustomerChangeEvent(fhOrderId));
        }

    }

    /**
     * 判断保司返回险种是否已经成功入库
     * @return
     */
    private boolean isExistsSuccessSave(TkAutoQueryPolicyDetailResp tkDetail){
        Map<String,String> fhProductIdMap = Maps.newHashMap();
        List<String> riskCodes = tkDetail.getRiskInfoList().stream().map(x ->{
            return x.getRiskCode();
        }).collect(Collectors.toList());
        List<TkAutoBaseInfoD> tkAutoBaseInfoDList = tkDetail.getRiskInfoList().stream().map(x ->{
            return x.getBaseInfoD();
        }).collect(Collectors.toList());
        List<String> policyList = tkAutoBaseInfoDList.stream().map(x -> {
            return x.getSubPolicyNo();
        }).collect(Collectors.toList());
        Integer count = insuredMapper.countByFhProductIdAndPolicyNo(policyList,riskCodes);
        log.info("isExistsSuccessSave before policyList= [{}], riskCodes= [{}]; count= [{}]",JSON.toJSONString(policyList),JSON.toJSONString(riskCodes), count);
        if(count != null && count.intValue() > 0){
            if(count.intValue() != riskCodes.size()){
                log.warn("泰康车险同步保司险种与入库险种数量不一致 req= [{}]; policyList= [{}]; riskCodes= [{}]; count= [{}]",JSON.toJSONString(tkDetail), JSON.toJSONString(policyList), JSON.toJSONString(riskCodes),count);
                return false;
            }
            return true;
        }else{
            return false;
        }
    }
}
