package com.cfpamf.ms.insur.weixin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 微信返产品列表VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxProductListVO implements Serializable {
    private static final long serialVersionUID = 8422190406851112796L;

    /**
     * id
     */
    @ApiModelProperty(value = "产品Id")
    private Integer id;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 产品属性
     */
    @ApiModelProperty(value = "产品属性")
    private String productAttrCode;

    /**
     * 保险产品
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty("产品简称")
    private String productShortName;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String productFeature;

    /**
     * 列表显示图片地址
     */
    @ApiModelProperty(value = "列表显示图片地址")
    private String thumbnailImageUrl;

    /**
     * 最低起保金额 小数位数0
     */
    @ApiModelProperty(value = "最低起保金额")
    private BigDecimal minAmount;

    /**
     * 销售数量
     */
    @ApiModelProperty(value = "销售数量")
    private Integer saleQty;

    /**
     * 已配置海报
     */
    @ApiModelProperty(value = "已配置海报")
    private Boolean hasPoster;

    @ApiModelProperty(value = "对接类型 1-api 2-H5", example = "2")
    private Integer apiType;

    @ApiModelProperty("h5地址 不能为空")
    private String h5Url;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    private Integer sortNum;

    /**
     * 排序号
     */
    @ApiModelProperty("版本号")
    private Integer version;


    @ApiModelProperty("产品扩展属性 具体 同后台")
    private Map<String, String> attrs;

    @JsonIgnore
    @ApiModelProperty(value = "产品标签join")
    private String productTagsJoin;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    String productCode;
    /**
     * 讲解视频
     */
    @ApiModelProperty("讲解视频")
    String explainVideo;

    @ApiModelProperty("讲解视频封面图片")
    String explainVideoImg;
    /**
     * 创建类型 个险短险-PERSON_SHORT_INSURANCE  个险长险-PERSON_LONG_INSURANCE 团险-GROUP_INSURANCE 车险CAR_INSURNACE
     */
    @ApiModelProperty("创建类型 个险短险-PERSON_SHORT_INSURANCE  个险长险-PERSON_LONG_INSURANCE 团险-GROUP_INSURANCE 车险CAR_INSURANCE 历史数据缺省类型- DEFAULT_TYP")
    String createType;

    /**
     * 最低保费
     */
    @ApiModelProperty(value = "最低保费")
    BigDecimal miniPremium;

    /**
     * 产品标签
     */
    @ApiModelProperty(value = "产品标签")
    private String[] productTags;

    public String[] getProductTags() {
        if (productTags != null && productTags.length > 0) {
            return productTags;
        }
        return StringUtils.isEmpty(productTagsJoin) ? new String[0] : productTagsJoin.split(",");
    }
}
