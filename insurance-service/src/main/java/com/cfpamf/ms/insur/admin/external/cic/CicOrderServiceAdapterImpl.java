package com.cfpamf.ms.insur.admin.external.cic;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.constant.AmConstants;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.cic.api.CicApiService;
import com.cfpamf.ms.insur.admin.external.cic.dto.Head;
import com.cfpamf.ms.insur.admin.external.cic.dto.InsureRet;
import com.cfpamf.ms.insur.admin.external.cic.util.CicConstants;
import com.cfpamf.ms.insur.admin.external.cic.util.SerialNumberGenerator;
import com.cfpamf.ms.insur.admin.external.cic.util.XmlMapperUtil;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPropertyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * 中华联合保险API接口适配器
 *
 * <AUTHOR>
 */
@Slf4j
@Service("cic")
public class CicOrderServiceAdapterImpl extends CicApiService implements ChannelOrderService {

    /**
     * 泛华远程调用api调用保存订单
     *
     * @param request
     * @return
     */
    @Override
    public OrderSubmitResponse submitChannelOrder(OrderSubmitRequest request) {
        String serialNumber = SerialNumberGenerator.getUniqueNumber();
        request.setSerialNumber(serialNumber);
        OrderSubmitResponse respDTO = CicBeanAdapterBuilder.parseSubmitOrderResp(
                super.submitOrder(
                        CicBeanAdapterBuilder.buildSubmitOrderInfoRequest(request)
                )
        );
        respDTO.setOrderId(serialNumber);
        return respDTO;
    }

    /**
     * 泛华远程调用api调用查询订单
     *
     * @param request
     * @return
     */
    @Override
    public OrderQueryResponse queryChannelOrderInfo(OrderQueryRequest request) {
        OrderQueryResponse respDTO = new OrderQueryResponse();
        respDTO.setNoticeCode(AmConstants.API_FANHUA_SUCCESS_0);
        respDTO.setOrderId(request.getOrderId());
        respDTO.setOrderstatus("1");
        respDTO.setOrderInfo(new OrderQueryResponse.OrderInfo());
        respDTO.setPropertyInfo(new SmPropertyVO());
        respDTO.setAppntInfo(new OrderQueryResponse.AppntInfo());
        respDTO.setInsuredInfos(new ArrayList<>());
        respDTO.setPolicyInfo(new OrderQueryResponse.PolicyInfo());
        if (!StringUtils.isEmpty(request.getPayId())) {
            CicBeanAdapterBuilder.parseOrderPaymentResp(
                    super.queryOrderPayment(
                            CicBeanAdapterBuilder.buildQueryOrderPaymentRequest(request)
                    ), respDTO);
        } else {
            respDTO.getOrderInfo().setOrderState(SmConstants.ORDER_STATUS_TO_PAY);
        }
        return respDTO;
    }

    /**
     * 获取订单支付URL
     *
     * @param request
     * @return
     */
    @Override
    public OrderPrePayResponse prePayChannelOrder(OrderPrePayRequest request) {
        return CicBeanAdapterBuilder.parseOrderPrePayResp(
                super.prePayOrder(
                        CicBeanAdapterBuilder.buildPrePayOrderRequest(request)
                )
        );
    }

    /**
     * 获取支付回调响应字符串
     *
     * @param request
     * @return
     */
    @Override
    public String getOrderPayedCallbackResp(String request) {
        InsureRet insureRet = new InsureRet();
        Head head = new Head();
        head.setTransNo(CicConstants.CIC_TRANSRNO_PAY_CALLBACK);
        head.setResultCode(CicConstants.CIC_PAY_CALLBACK_RESP_RESULTCODE);
        head.setErrInfo(CicConstants.CIC_PAY_CALLBACK_RESP_ERRINFO);
        insureRet.setHead(head);
        return XmlMapperUtil.beanToString(insureRet);
    }

    public static void main(String[] args) {
        System.err.println(new CicOrderServiceAdapterImpl().getOrderPayedCallbackResp(""));
    }
}
