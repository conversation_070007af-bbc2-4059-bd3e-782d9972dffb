package com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class TKPayNotify {

    @ApiModelProperty("团险保单号")
    private String groupPolicyNo;

    @ApiModelProperty("渠道订单号")
    private String channelContractNo;

    private String groupAppEndorNo;

    @ApiModelProperty("电子保单地址")
    private String groupEpolicyUrl;

    @ApiModelProperty("团险批单号")
    private String groupEndorNo;

    private String endorStatus;

    private String payWayType;

    private List<TKNotifyPersonal> personalPolicyList;

    public boolean success() {
        return Objects.equals("1", endorStatus);
    }
}
