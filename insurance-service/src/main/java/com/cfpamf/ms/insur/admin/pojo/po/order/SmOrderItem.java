package com.cfpamf.ms.insur.admin.pojo.po.order;

import com.cfpamf.ms.insur.admin.pojo.po.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;

@Data
@ApiModel("订单明细信息")
@Table(name = "sm_order_item")
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmOrderItem extends BasePO {
    /**
     * 字段名称 泛华订单Id
     */
    @ApiModelProperty(value = "泛华订单Id")
    String fhOrderId;
    /**
     * 字段名称 保单号
     */
    @ApiModelProperty(value = "保单号")
    String policyNo;
    /**
     * 字段名称 保司保单号
     */
    @ApiModelProperty(value = "保司保单号")
    String thPolicyNo;

    @ApiModelProperty("保司-分单号")
    String branchId;

    @ApiModelProperty("我司-分单号")
    String activeBranchId;
    /**
     * 字段名称 保司批单号
     */
    @ApiModelProperty(value = "保司批单号")
    String thEndorsementNo;
    /**
     * 字段名称 类型 0保单，1批单
     */
    @ApiModelProperty(value = "类型 0保单，1批单")
    Integer type;
    /**
     * 字段名称 保司计划编码
     */
    @ApiModelProperty(value = "保司计划编码")
    String planCode;
    /**
     * 字段名称 保司计划编码
     */
    @ApiModelProperty(value = "状态")
    String appStatus;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    String idType;
    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号")
    String idNumber;
    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    BigDecimal unitPrice;
    /**
     * 份数
     */
    @ApiModelProperty(value = "份数")
    Integer qty;
    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "分单总金额：每个分单的总保费")
    BigDecimal totalAmount;

    /**
     * 保额
     */
    @ApiModelProperty(value = "保额")
    BigDecimal insuredAmount;
    /**
     * 批单剩余金额
     */
    @ApiModelProperty(value = "批单剩余金额")
    BigDecimal endorsementAmount;


    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    Integer productId;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    Integer planId;

    @ApiModelProperty("提层id")
    Integer commissionId;

    @Transient
    boolean memberExist = false;

    @Transient
    Long seq;

}
