package com.cfpamf.ms.insur.weixin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmProductConfirm;
import com.cfpamf.ms.insur.admin.pojo.po.sys.SystemShareKnowledgeConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 微信返产品详情VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class WxProductDetailVO implements Serializable {
    private static final long serialVersionUID = -6962598474653204709L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Integer id;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 保险产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty("产品简称")
    private String productShortName;

    @ApiModelProperty("capp头部图片地址")
    private String headImageUrl;

    /**
     * 产品标签join
     */
    @JsonIgnore
    @ApiModelProperty(value = "产品标签join")
    private String productTagsJoin;

    /**
     * 产品标签
     */
    @ApiModelProperty(value = "产品标签")
    private String[] productTags;

    /**
     * 产品特色 摘要
     */
    @ApiModelProperty(value = "产品特色")
    private String productFeature;

    /**
     * 产品介绍图片地址
     */
    @ApiModelProperty(value = "产品介绍图片地址")
    private String introduceImageUrl;

    /**
     * 保险公司Id
     */
    @ApiModelProperty(value = "保险公司Id")
    private String companyId;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 保险公司图片
     */
    @ApiModelProperty(value = "保险公司图片")
    private String companyLogoImageUrl;

    /**
     * 常见问题
     */
    @ApiModelProperty(value = "常见问题")
    private String attentions;

    @ApiModelProperty(value="产品属性编码：团险，个险")
    private String productAttrCode;

    /**
     * 最低起保金额 小数位数0
     */
    @ApiModelProperty(value = "最低起保金额")
    private List<WxProductClauseVO> clauses;

    /**
     * 最低起保金额
     */
    @ApiModelProperty(value = "最低起保金额")
    private BigDecimal minAmount;

    /**
     * 限购份数
     */
    @ApiModelProperty(value = "限购份数")
    private Integer buyLimit;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Integer effectWaitingDayMin;
    private Integer effectWaitingDayMax;

    /**
     * 健康告知
     */
    @ApiModelProperty(value = "健康告知")
    private String healthNotification;

    /**
     * 是否已经收藏
     */
    @ApiModelProperty(value = "是否已经收藏")
    private Boolean collected;

    @ApiModelProperty(value="销售模式:0=询价，1=线上销售，2=线下销售")
    private String salesMode;

    @ApiModelProperty(value="产品编码")
    private String productCode;
    /**
     * 已配置海报
     */
    @ApiModelProperty(value = "已配置海报")
    private Boolean hasPoster;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private List<WxProductSalesOrgVO> salesOrgs;

    /**
     * 保障计划
     */
    @ApiModelProperty(value = "保障计划")
    private WxCoveragePlanVO coveragePlan;

    /**
     * 产品特色图片地址列表
     */
    @ApiModelProperty(value = "产品特色图片地址列表")
    private List<String> productSpecials;

    /**
     * 产品特色图片URL拼接地址
     */
    @JsonIgnore
    private String productSpecialsJoin;

    /**
     * 投保须知
     */
    @ApiModelProperty(value = "投保须知")
    private String glProductNotice;

    /**
     * 保险公司报案电话
     */
    @ApiModelProperty(value = "保险公司报案电话")
    private String companyReportPhoneNo;

    @ApiModelProperty(value = "客户告知书")
    private String custNotifyDetail;

    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty("条款内容")
    private String clauseContent;

    @ApiModelProperty("是否配置客户告知书")
    private Integer custNotify;

    @ApiModelProperty("是否支持智能核保")
    private Boolean aiCheck;

    @ApiModelProperty("智能核保方式 1 h5连接，2题库")
    private Integer aiCheckWay;

    @ApiModelProperty("自主确认条款")
    private List<SmProductConfirm> confirms;

    /**
     * 讲解视频
     */
    @ApiModelProperty("讲解视频")
    String explainVideo;

    @ApiModelProperty("讲解视频")
    String explainVideoImg;
    /**
     * 创建类型 个险短险-PERSON_SHORT_INSURANCE  个险长险-PERSON_LONG_INSURANCE 团险-GROUP_INSURANCE 车险CAR_INSURNACE
     */
    @ApiModelProperty("创建类型 个险短险-PERSON_SHORT_INSURANCE  个险长险-PERSON_LONG_INSURANCE 团险-GROUP_INSURANCE 车险CAR_INSURANCE 历史数据缺省类型- DEFAULT_TYP")
    String createType;

    /**
     * 最低保费
     */
    @ApiModelProperty(value = "最低保费")
    BigDecimal miniPremium;

    /**
     * 分享知识点
     */
    @ApiModelProperty(value = "分享知识点")
    SystemShareKnowledgeConfig  shareKnowledge;

    /**
     * 是否支持绑卡业务
     */
    @ApiModelProperty(value = "是否支持绑卡业务")
    private Boolean supportBindFlag ;

    @ApiModelProperty("产品扩展属性 具体 同后台")
    private Map<String, String> attrs;

    @ApiModelProperty("h5链接")
    private String h5Url;

    @ApiModelProperty(value = "对接类型 1-api 2-H5", example = "2")
    private Integer apiType;
    public String[] getProductTags() {
        if (productTags != null && productTags.length > 0) {
            return productTags;
        }
        return StringUtils.isEmpty(productTagsJoin) ? new String[0] : productTagsJoin.split(",");
    }

    public List<String> getProductSpecials() {
        if (productSpecials != null && !productSpecials.isEmpty()) {
            return productSpecials;
        }
        return productSpecialsJoin == null ? Collections.emptyList() : Arrays.asList(productSpecialsJoin.split(","));
    }
}
