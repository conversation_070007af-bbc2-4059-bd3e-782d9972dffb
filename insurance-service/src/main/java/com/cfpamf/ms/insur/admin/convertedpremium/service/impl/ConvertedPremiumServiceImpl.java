package com.cfpamf.ms.insur.admin.convertedpremium.service.impl;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.convertedpremium.dao.ConvertedPremiumConfigMapper;
import com.cfpamf.ms.insur.admin.convertedpremium.dao.ConvertedPremiumMapper;
import com.cfpamf.ms.insur.admin.convertedpremium.entity.ConvertedPremiumConfig;
import com.cfpamf.ms.insur.admin.convertedpremium.entity.OrderConvertedPremium;
import com.cfpamf.ms.insur.admin.convertedpremium.exception.ConvertedPremiumException;
import com.cfpamf.ms.insur.admin.convertedpremium.service.ConvertedPremiumService;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumOrderProductType;
import com.cfpamf.ms.insur.admin.enums.EnumSurrenderType;
import com.cfpamf.ms.insur.admin.enums.order.OrderVisitResultEnum;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrder;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSmsNotifyVO;
import com.cfpamf.ms.insur.admin.service.order.SmOrderPolicyService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/8 14:11
 */
@Service
@Slf4j
public class ConvertedPremiumServiceImpl implements ConvertedPremiumService {
    @Autowired
    private ConvertedPremiumMapper convertedPremiumMapper;

    @Autowired
    private SmOrderMapper smOrderMapper;


    @Autowired
    private ConvertedPremiumConfigMapper convertedPremiumConfigMapper;

    @Autowired
    private SmOrderPolicyService smOrderPolicyService;
    @Autowired
    private AutoOrderMapper autoOrderMapper;

    /**
     * 计算折算保费
     *
     * @param fhOrderId
     */
    @Override
    public void calculateConvertPremium(String fhOrderId) {
        log.info("订单号{}，计算折算保费", fhOrderId);

        //判断订单是否存在
        SmBaseOrderVO smBaseOrderVO = smOrderMapper.getBaseOrderInfoByOrderId(fhOrderId);
        if (Objects.isNull(smBaseOrderVO)) {
            throw ConvertedPremiumException.CONVERTED_PREMIUM_ORDER_NOT_EXIST;
        }
        //获取当前订单的折算保费配置
        ConvertedPremiumConfig convertedPremiumConfig = convertedPremiumConfigMapper.getByPlanIdAndPayTime(smBaseOrderVO.getPlanId(), smBaseOrderVO.getPaymentTime());
        if (Objects.isNull(convertedPremiumConfig)) {
            throw ConvertedPremiumException.CONVERTED_PREMIUM_CONFiG_NOT_EXIST;
        }
        calculateConvertPremium(fhOrderId, convertedPremiumConfig);
    }

    /**
     * 计算折算保费
     *
     * @param fhOrderId
     * @param convertedPremiumConfig
     */
    @Override
    public void calculateConvertPremium(String fhOrderId, ConvertedPremiumConfig convertedPremiumConfig) {
        log.info("订单号{}，订单产品计划的折算保费配置：{}", fhOrderId, JSON.toJSONString(convertedPremiumConfig));
        //折算保费订单新增集合
        List<OrderConvertedPremium> addOrderConvertedPremiumList = Lists.newArrayList();
        //折算保费订单修改集合
        List<OrderConvertedPremium> updateOrderConvertedPremiumList = Lists.newArrayList();
        //处理订单折算保费
        handlerOrderConvertedPremium(fhOrderId, convertedPremiumConfig, addOrderConvertedPremiumList, updateOrderConvertedPremiumList);
        //
        if (CollectionUtils.isNotEmpty(addOrderConvertedPremiumList)) {
            convertedPremiumMapper.insertList(addOrderConvertedPremiumList);
        }
        if (CollectionUtils.isNotEmpty(updateOrderConvertedPremiumList)) {
            convertedPremiumMapper.batchUpdateConvertedPremiumOrderAmount(updateOrderConvertedPremiumList);
        }
    }

    /**
     * 处理订单的折算保费
     *
     * @param fhOrderId                       订单id
     * @param convertedPremiumConfig          折算保费配置
     * @param addOrderConvertedPremiumList    新增的折算保费记录集合
     * @param updateOrderConvertedPremiumList 更新的折算保费记录集合
     */
    private void handlerOrderConvertedPremium(String fhOrderId, ConvertedPremiumConfig convertedPremiumConfig, List<OrderConvertedPremium> addOrderConvertedPremiumList, List<OrderConvertedPremium> updateOrderConvertedPremiumList) {
        //获取订单保单信息
        List<SmOrderSmsNotifyVO> orderInsuredList = smOrderMapper.getOrderInsuredByOrderId(fhOrderId);
        //获取折订单算保费记录
        Map<String, List<OrderConvertedPremium>> orderConvertedPremiumMap = convertedPremiumMapper.findByFhOrderId(fhOrderId)
                .stream()
                .collect(Collectors.groupingBy(OrderConvertedPremium::getInsIdNumber));

        //遍历保单信息生成折算保费记录
        for (SmOrderSmsNotifyVO orderInsured : orderInsuredList) {
            String appStatus = orderInsured.getAppStatus();
            //如果订单状态不为承保成功或者退保成功不计算折算保费
            if (!Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS) && !Objects.equals(appStatus, SmConstants.POLICY_STATUS_SUCCESS)) {
                continue;
            }


            //获取保单的被保人身份证
            String insuredIdNumber = orderInsured.getInsuredIdNumber();
            //获取被保人历史的折算保费记录
            List<OrderConvertedPremium> orderConvertedPremiumList = orderConvertedPremiumMap.getOrDefault(insuredIdNumber, Lists.newArrayList())
                    .stream()
                    .filter(orderConvertedPremium -> Objects.equals(orderConvertedPremium.getPolicyNo(), orderInsured.getPolicyNo()))
                    .collect(Collectors.toList());

            //如果表中没有记录则生成一条数据
            if (CollectionUtils.isEmpty(orderConvertedPremiumList)) {
                //生成折算保费记录
                OrderConvertedPremium orderConvertedPremium = getOrderConvertedPremium(convertedPremiumConfig, orderInsured);
                addOrderConvertedPremiumList.add(orderConvertedPremium);
                log.info("计算折算保费添加记录：{}", JSON.toJSONString(orderConvertedPremium));
                continue;
            } else {
                //处理存在的记录
                handlerExistRecord(convertedPremiumConfig, addOrderConvertedPremiumList, updateOrderConvertedPremiumList, orderInsured, orderConvertedPremiumList);
            }
        }
    }


    /**
     * 处理存在的订单记录
     * <p>
     * 1.如果承保成功且存在记录 则判断折算保费记录的订单金额和新的订单金额是否一致 不一致就修改金额和折算保费
     * 2.如果是退保成功 存在承保记录不存在退保记录  生成新的折算保费记录
     * 2.如果退保成功-存在承保记录和退保记录  判断退保的折算保费记录的订单金额和新的订单金额是否一致 不一致就修改金额和折算保费
     * </p>
     *
     * @param convertedPremiumConfig
     * @param addOrderConvertedPremiumList
     * @param updateOrderConvertedPremiumList
     * @param orderInsured
     * @param orderConvertedPremiumList
     */
    private void handlerExistRecord(ConvertedPremiumConfig convertedPremiumConfig, List<OrderConvertedPremium> addOrderConvertedPremiumList, List<OrderConvertedPremium> updateOrderConvertedPremiumList, SmOrderSmsNotifyVO orderInsured, List<OrderConvertedPremium> orderConvertedPremiumList) {
        //获取当前保单金额
        BigDecimal orderAmount = getOrderAmount(orderInsured);
        String appStatus = orderInsured.getAppStatus();
        //承保成功
        if (Objects.equals(appStatus, SmConstants.POLICY_STATUS_SUCCESS)) {
            log.info("计算情况：承保成功");
            for (OrderConvertedPremium orderConvertedPremium : orderConvertedPremiumList) {
                //处理重复记录信息
                handlerRepeatRecord(updateOrderConvertedPremiumList, orderAmount, appStatus, orderConvertedPremium,convertedPremiumConfig);
            }
        }
        //退保成功
        if (Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            boolean existPolicyCancelRecord = orderConvertedPremiumList.stream().anyMatch(orderConvertedPremium -> Objects.equals(orderConvertedPremium.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS));
            //如果存在承保记录不存在退保记录
            if (!existPolicyCancelRecord) {
                log.info("计算情况：退保成功-不存在退保记录");
                //通过承保记录生成折算保费配置 进行退保记录生成
                ConvertedPremiumConfig snapshotConvertedPremiumConfig = getSnapshotConvertedPremiumConfig(orderConvertedPremiumList);
                OrderConvertedPremium orderConvertedPremium = getOrderConvertedPremium(snapshotConvertedPremiumConfig, orderInsured);
                addOrderConvertedPremiumList.add(orderConvertedPremium);
                log.info("计算折算保费添加记录：{}", JSON.toJSONString(orderConvertedPremium));

            }
            //如果存在承保记录和退保记录
            if (existPolicyCancelRecord) {
                log.info("计算情况：退保成功-存在退保记录");
                orderConvertedPremiumList.stream()
                        .filter(orderConvertedPremium -> Objects.equals(orderConvertedPremium.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS))
                        .forEach(orderConvertedPremium -> {
                            handlerRepeatRecord(updateOrderConvertedPremiumList, orderAmount, appStatus, orderConvertedPremium,convertedPremiumConfig);
                        });
            }

        }
    }

    /**
     * 处处理重复折算保费记录
     * <p>
     * 如果金额不一样或者配置的比例变更就去修改历史记录的金额
     * </p>
     *  @param updateOrderConvertedPremiumList
     * @param orderAmount
     * @param appStatus
     * @param orderConvertedPremium
     * @param convertedPremiumConfig
     */
    private void handlerRepeatRecord(List<OrderConvertedPremium> updateOrderConvertedPremiumList, BigDecimal orderAmount, String appStatus, OrderConvertedPremium orderConvertedPremium, ConvertedPremiumConfig convertedPremiumConfig) {
        //如果判断库中数据是否和订单金额是否一样
        Integer newProportion = convertedPremiumConfig.getProportion();
        Integer oldProportion = orderConvertedPremium.getProportion();
        if (orderAmount.compareTo(orderConvertedPremium.getOrderAmount()) != 0 || oldProportion.intValue() != newProportion.intValue()) {
            //订单金额变更后修改折算保费记录的订单金额和折算保费
            orderConvertedPremium.setOrderAmount(orderAmount);
            BigDecimal convertedPremium = getConvertedPremium(appStatus, orderAmount, newProportion);
            orderConvertedPremium.setConvertedPremium(convertedPremium);
            orderConvertedPremium.setConvertedPremiumConfigId(convertedPremiumConfig.getId());
            orderConvertedPremium.setProportion(newProportion);
            updateOrderConvertedPremiumList.add(orderConvertedPremium);
            log.info("计算折算保费更新记录：{}", JSON.toJSONString(orderConvertedPremium));
        }
    }

    /**
     * 获取承保成功时候的折算保费配置对象
     *
     * @param orderConvertedPremiumList
     * @return
     */
    private ConvertedPremiumConfig getSnapshotConvertedPremiumConfig(List<OrderConvertedPremium> orderConvertedPremiumList) {
        OrderConvertedPremium policySuccessRecord = orderConvertedPremiumList.stream().filter(orderConvertedPremium -> Objects.equals(orderConvertedPremium.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)).findFirst().get();
        ConvertedPremiumConfig snapshotConvertedPremiumConfig = new ConvertedPremiumConfig();
        BeanUtils.copyProperties(policySuccessRecord, snapshotConvertedPremiumConfig);
        snapshotConvertedPremiumConfig.setId(policySuccessRecord.getConvertedPremiumConfigId());
        snapshotConvertedPremiumConfig.setUpdateBy(policySuccessRecord.getConvertedPremiumConfigUpdateBy());
        snapshotConvertedPremiumConfig.setUpdateTime(policySuccessRecord.getConvertedPremiumConfigUpdateTime());
        return snapshotConvertedPremiumConfig;
    }

    /**
     * 生成订单折算保费
     *
     * @param convertedPremiumConfig
     * @param orderInsured
     */
    private OrderConvertedPremium getOrderConvertedPremium(ConvertedPremiumConfig convertedPremiumConfig, SmOrderSmsNotifyVO orderInsured) {
        String insuredIdNumber = orderInsured.getInsuredIdNumber();
        String fhOrderId = orderInsured.getFhOrderId();

        OrderConvertedPremium orderConvertedPremium = new OrderConvertedPremium();
        orderConvertedPremium.setFhOrderId(fhOrderId);
        orderConvertedPremium.setPolicyNo(orderInsured.getPolicyNo());
        orderConvertedPremium.setInsIdNumber(insuredIdNumber);
        orderConvertedPremium.setAppStatus(orderInsured.getAppStatus());
        //设置金额
        BigDecimal orderAmount = getOrderAmount(orderInsured);
        orderConvertedPremium.setOrderAmount(orderAmount);

        Integer proportion = convertedPremiumConfig.getProportion();
        //计算折算保费
        BigDecimal convertedPremium = getConvertedPremium(orderInsured.getAppStatus(), orderAmount, proportion);
        orderConvertedPremium.setConvertedPremium(convertedPremium);
        orderConvertedPremium.setConvertedPremiumConfigId(convertedPremiumConfig.getId());
        orderConvertedPremium.setProportion(convertedPremiumConfig.getProportion());
        orderConvertedPremium.setPaymentPeriod(convertedPremiumConfig.getPaymentPeriod());
        orderConvertedPremium.setStartTermValidity(convertedPremiumConfig.getStartTermValidity());
        orderConvertedPremium.setEndTermValidity(convertedPremiumConfig.getEndTermValidity());
        orderConvertedPremium.setConvertedPremiumConfigUpdateTime(convertedPremiumConfig.getUpdateTime());
        orderConvertedPremium.setConvertedPremiumConfigUpdateBy(convertedPremiumConfig.getUpdateBy());
        return orderConvertedPremium;
    }

    /**
     * 计算折算保费
     *
     * @param appStatus
     * @param orderAmount
     * @param proportion
     * @return
     */
    private BigDecimal getConvertedPremium(String appStatus, BigDecimal orderAmount, Integer proportion) {
        //计算折算保费
        BigDecimal convertedPremium = orderAmount.multiply(BigDecimal.valueOf(proportion)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        //如果是退保成功为负数
        if (Objects.equals(appStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            convertedPremium = convertedPremium.abs().negate();
        }
        return convertedPremium;
    }

    /**
     * 通过订单信息获取金额
     *
     * @param orderInsured
     * @return
     */
    public BigDecimal getOrderAmount(SmOrderSmsNotifyVO orderInsured) {
        String productAttrCode = orderInsured.getProductAttrCode();
        //设置金额
        if (Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_GROUP) || Objects.equals(productAttrCode, SmConstants.PRODUCT_ATTR_EMPLOYER)) {
            //团险 订单金额设置 如果item表中取不到订单金额就取unitPrice 单价
            BigDecimal itemTotalAmount = orderInsured.getItemTotalAmount();
            if (Objects.isNull(itemTotalAmount)) {
                return orderInsured.getUnitPrice();
            } else {
                return itemTotalAmount;
            }
        }

        if (Objects.equals(orderInsured.getProductType(), EnumOrderProductType.CAR.getCode())) {
            //车险
            String fhOrderId = orderInsured.getFhOrderId();
            AutoOrder autoOrder = autoOrderMapper.getAutoOrderByOrderNo(fhOrderId);
            BigDecimal premium = autoOrder.getPremium();
            if (Objects.nonNull(premium)) {
                return premium;
            }
            BigDecimal totalAmount = autoOrder.getTotalAmount();
            BigDecimal tax = autoOrder.getTax();
            if (Objects.isNull(tax)) {
                tax = BigDecimal.ZERO;
            }
            return totalAmount.subtract(tax);

        }

        BigDecimal amt = getFxSurrenderOrderAmount(orderInsured);

        //个险订单直接返回订单金额
        return amt != null ? amt : orderInsured.getTotalAmount();
    }

    /**
     * s53迭代
     * 复星非犹豫期退保，且已经回访成功的保单，则认为退保金额为0
     *
     * @param orderInsured
     */
    private BigDecimal getFxSurrenderOrderAmount(SmOrderSmsNotifyVO orderInsured) {
        log.info("保单号{},{},{}", orderInsured.getPolicyNo(), orderInsured.getChannel(), orderInsured.getAppStatus());
        if (!Objects.equals(orderInsured.getChannel(), EnumChannel.FX.getCode())) {
            return null;
        }
        if (!Objects.equals(orderInsured.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            return null;
        }
        SmOrderPolicy policy = smOrderPolicyService.getOrderPolicyByPolicyNo(orderInsured.getPolicyNo(), orderInsured.getChannel());
        if (policy != null
                && Objects.equals(policy.getVisitStatus(), OrderVisitResultEnum.SUCCESS.getCode())
                && !Objects.equals(policy.getSurrenderType(), EnumSurrenderType.HESITATE_PERIOD_SURRENDER.getCode())) {
            log.info("保单号{},非犹豫期退保", orderInsured.getPolicyNo());
            return BigDecimal.ZERO;
        }
        return null;
    }

    @Override
    public List<OrderConvertedPremium> listOrderConvertedPremium(String fhOrderId, String appStatus, List<String> insIdNumberList) {
        Example e = new Example(OrderConvertedPremium.class);
        e.createCriteria().andEqualTo("fhOrderId", fhOrderId)
                .andEqualTo("appStatus", appStatus)
                .andIn("insIdNumber", insIdNumberList);
        return convertedPremiumMapper.selectByExample(e);
    }
}
