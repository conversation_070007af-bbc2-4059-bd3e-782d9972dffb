package com.cfpamf.ms.insur.admin.job;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.excel.util.CollectionUtils;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerEducationMapper;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmCmpySettingMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.event.CustomerPushEvent;
import com.cfpamf.ms.insur.admin.pojo.dto.CustomerAgentDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.CustomerDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.CustomerPropertyDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.CustomerRemdDTO;
import com.cfpamf.ms.insur.admin.pojo.po.CustomerEducation;
import com.cfpamf.ms.insur.admin.pojo.query.CmpySettingQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.SmCmpySettingService;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.XxlLogger;
import com.cfpamf.ms.insur.weixin.pojo.dto.customer.WxCustomerDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 从订单里面抽取客户定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
//@JobHandler(value = "customer-extract")
public class SmCustomerExtractHandler {

    /**
     * 被保人与投保人关系map
     */
    private final static Map<String, String> RS_MAP = new HashMap<>();

    static {
        RS_MAP.put("丈夫", "配偶");
        RS_MAP.put("妻子", "配偶");
        RS_MAP.put("配偶", "配偶");
        RS_MAP.put("母女", "父母");
        RS_MAP.put("母子", "父母");
        RS_MAP.put("父女", "父母");
        RS_MAP.put("父子", "父母");
        RS_MAP.put("父母", "父母");
        RS_MAP.put("子女", "子女");
        RS_MAP.put("儿子", "子女");
        RS_MAP.put("女儿", "子女");
    }

    /**
     * 订单mapper
     */
    @Autowired
    private SmOrderMapper smOrderMapper;

    /**
     * 客户mapper
     */
    @Autowired
    private CustomerMapper cMapper;
    /**
     * 保险公司参数查询mapper
     */
    @Autowired
    private SmCmpySettingMapper smapper;
    /**
     * 区域mapper
     */
    @Autowired
    private SmCmpySettingService settingService;

    @Autowired
    private CustomerEducationMapper customerEducationMapper;

    /**
     * 事件总线
     */
    @Lazy
    @Autowired
    private EventBusEngine busEngine;

    /**
     * 从订单里面抽取客户定时任务  125
     */
    @XxlJob("customer-extract")
    public void execute() {
        //新版本传参方式
        String fhOrderId = XxlJobHelper.getJobParam();
        customerExecuteV1(fhOrderId);
        //return SUCCESS;
    }

    /**
     * 从订单里面抽取客户定时任务  125
     */
    @XxlJob("customer-new-extract")
    public void extractNew() {
        List<String> unExtractIds = smOrderMapper.list30UnExtractFhOrderId();
        extractCustomer(unExtractIds);
    }
    public void extractCustomer(List<String> fhOrderIds){
        if(CollectionUtils.isEmpty(fhOrderIds)){
            return ;
        }
        CmpySettingQuery settingQuery = new CmpySettingQuery();
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<SmCompanySettingVO> sexSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP);
        List<SmCompanySettingVO> relationshipSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_IDTYPE);
        List<SmCompanySettingVO> idTypeSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_HOUSE_TYPE);
        List<SmCompanySettingVO> houseTypeSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SCHOOL_NATURE);
        Map<String, SmCompanySettingVO> schoolNatureDict = LambdaUtils.groupByAndToFirstMap(smapper.listCompanySettings(settingQuery),
                a -> String.format("%s-%s-%s", a.getCompanyId(), a.getOptionCode(), a.getChannel()));
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SCHOOL_TYPE);
        Map<String, SmCompanySettingVO> schoolTypeDict = LambdaUtils.groupByAndToFirstMap(smapper.listCompanySettings(settingQuery),
                a -> String.format("%s-%s-%s", a.getCompanyId(), a.getOptionCode(), a.getChannel()));
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_STUDENT_TYPE);
        Map<String, SmCompanySettingVO> studentTypeDict = LambdaUtils.groupByAndToFirstMap(smapper.listCompanySettings(settingQuery),
                a -> String.format("%s-%s-%s", a.getCompanyId(), a.getOptionCode(), a.getChannel()));
        fhOrderIds.stream().forEach(fhOrderId->{
            try {
                doExtract(fhOrderId, sexSettings, relationshipSettings, idTypeSettings, houseTypeSettings, schoolNatureDict, schoolTypeDict, studentTypeDict);
            }catch (Exception e){
                log.warn("订单{}客户信息同步失败",fhOrderId);
            }
        });
    }
    private void doExtract(String fhOrderId,List<SmCompanySettingVO> sexSettings,List<SmCompanySettingVO> relationshipSettings,List<SmCompanySettingVO> idTypeSettings
            ,List<SmCompanySettingVO> houseTypeSettings,Map<String, SmCompanySettingVO> schoolNatureDict,Map<String, SmCompanySettingVO> schoolTypeDict
            ,Map<String, SmCompanySettingVO> studentTypeDict){
        // 定时任务同步承保成功的订单， 支付成功抽取支付成功的客户信息
        List<SmExtractOrderVO> smOrderList = smOrderMapper.listUnExtractCustomerOrders(fhOrderId, null, null);
        if (smOrderList.isEmpty()) {
            return ;
        }
        Set<Integer> pushCustomerIdSet = new HashSet<>();
        Set<String> idNumbers = smOrderList.stream().map(SmExtractOrderVO::getInsuredIdNumber).collect(Collectors.toSet());
        idNumbers.addAll(smOrderList.stream().map(SmExtractOrderVO::getAplicantIdNumber).collect(Collectors.toSet()));
        List<WxCustomerDTO> existCustomerList = cMapper.queryCustomerByIdCard(idNumbers);
        List<String> extractOrderList = new ArrayList<>();
        for (SmExtractOrderVO odr : smOrderList) {//设置投保人性别
            sexSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                    && Objects.equals(v.getOptionCode(), odr.getApplicantPersonGender())
                    && Objects.equals(v.getChannel(), odr.getChannel())
            ).findFirst()
                    .ifPresent(smCompanySettingVO -> odr.setApplicantPersonGender(smCompanySettingVO.getOptionName()));
            //设置被保人性别
            sexSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                    && Objects.equals(v.getOptionCode(), odr.getInsuredPersonGender())
                    && Objects.equals(v.getChannel(), odr.getChannel())
            ).findFirst()
                    .ifPresent(smCompanySettingVO -> odr.setInsuredPersonGender(smCompanySettingVO.getOptionName()));
            relationshipSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                    && Objects.equals(v.getOptionCode(), odr.getInsuredRelationship())
                    && Objects.equals(v.getChannel(), odr.getChannel())
            ).findFirst()
                    .ifPresent(smCompanySettingVO -> odr.setInsuredRelationship(smCompanySettingVO.getOptionName()));
            idTypeSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                    && Objects.equals(v.getOptionCode(), odr.getInsuredIdTypeName())
                    && Objects.equals(v.getChannel(), odr.getChannel())
            ).findFirst()
                    .ifPresent(smCompanySettingVO -> odr.setInsuredIdTypeName(smCompanySettingVO.getOptionName()));
            idTypeSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                    && Objects.equals(v.getOptionCode(), odr.getAplicantIdTypeName())
                    && Objects.equals(v.getChannel(), odr.getChannel())
            ).findFirst()
                    .ifPresent(smCompanySettingVO -> odr.setAplicantIdTypeName(smCompanySettingVO.getOptionName()));
            houseTypeSettings.stream().filter(v ->
                    Objects.equals(v.getOptionCode(), odr.getHourseType())
                            && Objects.equals(v.getChannel(), odr.getChannel())).findFirst()
                    .ifPresent(smCompanySettingVO -> odr.setHourseType(smCompanySettingVO.getOptionName()));
            CustomerDTO appCustomerDTO = mapperOrderApplicantToCustomerDTO(odr);
            Optional<WxCustomerDTO> optional = existCustomerList.stream().filter(c -> Objects.equals(c.getIdNumber(), odr.getAplicantIdNumber())).findFirst();
            if (existCustomerList.stream().noneMatch(c -> Objects.equals(c.getIdNumber(), odr.getAplicantIdNumber()))) {
                cMapper.insertCustomer(appCustomerDTO);
                existCustomerList.add(convert(appCustomerDTO));
            } else {
                cMapper.updateCustomer(appCustomerDTO);
                appCustomerDTO.setId(optional.get().getCustomerId());
            }
            //被保人
            CustomerDTO insurdCustomerDTO = mapperOrderInsuredToCustomerDTO(odr);
            optional = existCustomerList.stream().filter(c -> Objects.equals(c.getIdNumber(), odr.getInsuredIdNumber())).findFirst();
            if (!optional.isPresent()) {
                cMapper.insertCustomer(insurdCustomerDTO);
                existCustomerList.add(convert(insurdCustomerDTO));
            } else {
                cMapper.updateCustomer(insurdCustomerDTO);
                insurdCustomerDTO.setId(optional.get().getCustomerId());
            }
            extractOrderList.add(odr.getFhOrderId());
            if (!StringUtils.isEmpty(odr.getRecommendUserId())) {
                List<CustomerAdminVO> customerRemds = cMapper.listCustomerAdmins(odr.getRecommendUserId());
                if (customerRemds.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), appCustomerDTO.getId()))) {
                    //modify by zhangjian 2020-07-08 统一工号
                    cMapper.insertCustomerRemd(new CustomerRemdDTO(appCustomerDTO.getId(), odr.getRecommendUserId(), odr.getRecommendJobCode()));
                    customerRemds.add(new CustomerAdminVO(appCustomerDTO.getId(), odr.getRecommendUserId(), odr.getRecommendJobCode()));
                }
                if (customerRemds.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), insurdCustomerDTO.getId()))) {
                    //modify by zhangjian 2020-07-08 统一工号
                    cMapper.insertCustomerRemd(new CustomerRemdDTO(insurdCustomerDTO.getId(), odr.getRecommendUserId(), odr.getRecommendJobCode()));
                }
            }
            if (odr.getAgentId() != null) {
                List<CustomerAgentVO> customerAgents = cMapper.listCustomerAgents(odr.getAgentId());
                if (customerAgents.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), appCustomerDTO.getId()))) {
                    cMapper.insertCustomerAgent(new CustomerAgentDTO(appCustomerDTO.getId(), odr.getAgentId()));
                    customerAgents.add(new CustomerAgentVO(appCustomerDTO.getId(), odr.getAgentId()));
                }
                if (customerAgents.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), insurdCustomerDTO.getId()))) {
                    cMapper.insertCustomerAgent(new CustomerAgentDTO(insurdCustomerDTO.getId(), odr.getAgentId()));
                }
            }
            //客户关系
            List<CustomerRelationshipVO> relationships = cMapper.listCustomerRelationship(appCustomerDTO.getId());
            if (relationships.stream().noneMatch(r -> Objects.equals(r.getCustomerId2(), insurdCustomerDTO.getId()))) {
                if (!Objects.equals(odr.getInsuredRelationship(), "本人") && odr.getInsuredRelationship() != null) {
                    String relationship = RS_MAP.get(odr.getInsuredRelationship());
                    if (relationship != null) {
                        cMapper.insertCustomerRelationship(appCustomerDTO.getId(), insurdCustomerDTO.getId(), relationship);
                    }
                }
            }
            //客户家财【车辆 房屋】
            if (Objects.equals(odr.getPropertyInfoIsExist(), "1")) {
                List<CustomerPropertyVO> propertys = cMapper.listCustomerProperty(insurdCustomerDTO.getId());
                Map<String, String> regionMap = new HashMap<>(8);
                if (!StringUtils.isEmpty(odr.getPropertyArea1()) || !(StringUtils.isEmpty(odr.getPropertyArea2()))) {
                    regionMap = settingService.getCompanyRegionList(odr.getCompanyId())
                            .stream()
                            .collect(Collectors.toMap(WxTreeVO::getValue, WxTreeVO::getName));
                }
                CustomerPropertyDTO cpDTO = mapperOrderToCustomerPropertyDTO(odr, regionMap);
                cpDTO.setCustomerId(insurdCustomerDTO.getId());
                if (propertys.stream().noneMatch(r -> Objects.equals(r.getHourseNo(), cpDTO.getHourseNo()))
                        || propertys.stream().noneMatch(r -> Objects.equals(r.getCarPlateNo(), cpDTO.getCarPlateNo()))
                        || propertys.stream().noneMatch(r -> Objects.equals(r.getPropertyAddress(), cpDTO.getPropertyAddress()))) {
                    cMapper.insertCustomerProperty(cpDTO);
                }
            }
            //客户教育经历
            if (!StringUtils.isEmpty(odr.getStudentType())) {
                CustomerEducation education = mapperOrderToCustomerEducation(odr, insurdCustomerDTO.getId());
                String schoolType = schoolTypeDict.getOrDefault(odr.getCompanyId() + "-" + education.getSchoolType(), new SmCompanySettingVO()).getOptionName();
                if (!StringUtils.isEmpty(schoolType)) {
                    education.setSchoolType(schoolType);
                }
                String schoolNature = schoolNatureDict.getOrDefault(odr.getCompanyId() + "-" + education.getSchoolNature(), new SmCompanySettingVO()).getOptionName();
                if (!StringUtils.isEmpty(schoolNature)) {
                    education.setSchoolNature(schoolNature);
                }
                String studentType = studentTypeDict.getOrDefault(odr.getCompanyId() + "-" + education.getStudentType(), new SmCompanySettingVO()).getOptionName();
                if (!StringUtils.isEmpty(studentType)) {
                    education.setStudentType(studentType);
                }
                List<CustomerEducation> select = customerEducationMapper.listByCustomerId(insurdCustomerDTO.getId());
                if (select.stream().noneMatch(a -> a.equals(education))) {
                    customerEducationMapper.insertSelective(education);
                }
            }
            pushCustomerIdSet.add(appCustomerDTO.getId());
            pushCustomerIdSet.add(insurdCustomerDTO.getId());
        }
        // 更新已抽取状态
        smOrderMapper.updateOrderExtractCustomerFlag(extractOrderList);
        // 发布推送客户中心客户事件 虽然已经调用了实名认证 但是没有调用fill方法 填充客户一些基本资料信息
        if (!pushCustomerIdSet.isEmpty()) {
            pushCustomerIdSet.forEach(customerId -> busEngine.publish(new CustomerPushEvent(customerId)));
        }
    }


    public void customerExecuteV1(String fhOrderId){
        XxlLogger.info(log, "开始从订单里面抽取客户定时任务");
        fhOrderId = StringUtils.isEmpty(fhOrderId) ? null : fhOrderId;

        CmpySettingQuery settingQuery = new CmpySettingQuery();
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SEX);
        List<SmCompanySettingVO> sexSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_API_RELATIONSHIP);
        List<SmCompanySettingVO> relationshipSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_IDTYPE);
        List<SmCompanySettingVO> idTypeSettings = smapper.listCompanySettings(settingQuery);
        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_HOUSE_TYPE);
        List<SmCompanySettingVO> houseTypeSettings = smapper.listCompanySettings(settingQuery);

        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SCHOOL_NATURE);
        Map<String, SmCompanySettingVO> schoolNatureDict = LambdaUtils.groupByAndToFirstMap(smapper.listCompanySettings(settingQuery),
                a -> String.format("%s-%s-%s", a.getCompanyId(), a.getOptionCode(), a.getChannel()));

        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_SCHOOL_TYPE);
        Map<String, SmCompanySettingVO> schoolTypeDict = LambdaUtils.groupByAndToFirstMap(smapper.listCompanySettings(settingQuery),
                a -> String.format("%s-%s-%s", a.getCompanyId(), a.getOptionCode(), a.getChannel()));

        settingQuery.setFieldCode(SmConstants.PRODUCT_FORM_FIELD_TYPE_STUDENT_TYPE);
        Map<String, SmCompanySettingVO> studentTypeDict = LambdaUtils.groupByAndToFirstMap(smapper.listCompanySettings(settingQuery),
                a -> String.format("%s-%s-%s", a.getCompanyId(), a.getOptionCode(), a.getChannel()));

        Set<Integer> pushCustomerIdSet = new HashSet<>();
        boolean hasNext = true;
        while (hasNext) {
            // 定时任务同步承保成功的订单， 支付成功抽取支付成功的客户信息
            List<SmExtractOrderVO> smOrderList = smOrderMapper.listUnExtractCustomerOrders(fhOrderId, null, null);
            if (fhOrderId != null || smOrderList.isEmpty()) {
                hasNext = false;
            }
            if (!smOrderList.isEmpty()) {
                Set<String> idNumbers = smOrderList.stream().map(SmExtractOrderVO::getInsuredIdNumber).collect(Collectors.toSet());
                idNumbers.addAll(smOrderList.stream().map(SmExtractOrderVO::getAplicantIdNumber).collect(Collectors.toSet()));
                List<WxCustomerDTO> existCustomerList = cMapper.queryCustomerByIdCard(idNumbers);
                List<String> extractOrderList = new ArrayList<>();
                for (SmExtractOrderVO odr : smOrderList) {//设置投保人性别
                    sexSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                            && Objects.equals(v.getOptionCode(), odr.getApplicantPersonGender())
                            && Objects.equals(v.getChannel(), odr.getChannel())
                    ).findFirst()
                            .ifPresent(smCompanySettingVO -> odr.setApplicantPersonGender(smCompanySettingVO.getOptionName()));
                    //设置被保人性别
                    sexSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                            && Objects.equals(v.getOptionCode(), odr.getInsuredPersonGender())
                            && Objects.equals(v.getChannel(), odr.getChannel())
                    ).findFirst()
                            .ifPresent(smCompanySettingVO -> odr.setInsuredPersonGender(smCompanySettingVO.getOptionName()));

                    relationshipSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                            && Objects.equals(v.getOptionCode(), odr.getInsuredRelationship())
                            && Objects.equals(v.getChannel(), odr.getChannel())
                    ).findFirst()
                            .ifPresent(smCompanySettingVO -> odr.setInsuredRelationship(smCompanySettingVO.getOptionName()));

                    idTypeSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                            && Objects.equals(v.getOptionCode(), odr.getInsuredIdTypeName())
                            && Objects.equals(v.getChannel(), odr.getChannel())
                    ).findFirst()
                            .ifPresent(smCompanySettingVO -> odr.setInsuredIdTypeName(smCompanySettingVO.getOptionName()));

                    idTypeSettings.stream().filter(v -> Objects.equals(v.getCompanyId(), odr.getCompanyId())
                            && Objects.equals(v.getOptionCode(), odr.getAplicantIdTypeName())
                            && Objects.equals(v.getChannel(), odr.getChannel())
                    ).findFirst()
                            .ifPresent(smCompanySettingVO -> odr.setAplicantIdTypeName(smCompanySettingVO.getOptionName()));

                    houseTypeSettings.stream().filter(v ->
                            Objects.equals(v.getOptionCode(), odr.getHourseType())
                                    && Objects.equals(v.getChannel(), odr.getChannel())).findFirst()
                            .ifPresent(smCompanySettingVO -> odr.setHourseType(smCompanySettingVO.getOptionName()));

                    CustomerDTO appCustomerDTO = mapperOrderApplicantToCustomerDTO(odr);
                    Optional<WxCustomerDTO> optional = existCustomerList.stream().filter(c -> Objects.equals(c.getIdNumber(), odr.getAplicantIdNumber())).findFirst();
                    if (existCustomerList.stream().noneMatch(c -> Objects.equals(c.getIdNumber(), odr.getAplicantIdNumber()))) {
                        cMapper.insertCustomer(appCustomerDTO);
                        existCustomerList.add(convert(appCustomerDTO));
                    } else {
                        cMapper.updateCustomer(appCustomerDTO);
                        appCustomerDTO.setId(optional.get().getCustomerId());
                    }

                    //被保人
                    CustomerDTO insurdCustomerDTO = mapperOrderInsuredToCustomerDTO(odr);
                    optional = existCustomerList.stream().filter(c -> Objects.equals(c.getIdNumber(), odr.getInsuredIdNumber())).findFirst();
                    if (!optional.isPresent()) {
                        cMapper.insertCustomer(insurdCustomerDTO);
                        existCustomerList.add(convert(insurdCustomerDTO));
                    } else {
                        cMapper.updateCustomer(insurdCustomerDTO);
                        insurdCustomerDTO.setId(optional.get().getCustomerId());
                    }
                    extractOrderList.add(odr.getFhOrderId());

                    if (!StringUtils.isEmpty(odr.getRecommendUserId())) {
                        List<CustomerAdminVO> customerRemds = cMapper.listCustomerAdmins(odr.getRecommendUserId());
                        if (customerRemds.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), appCustomerDTO.getId()))) {
                            //modify by zhangjian 2020-07-08 统一工号
                            cMapper.insertCustomerRemd(new CustomerRemdDTO(appCustomerDTO.getId(), odr.getRecommendUserId(), odr.getRecommendJobCode()));
                            customerRemds.add(new CustomerAdminVO(appCustomerDTO.getId(), odr.getRecommendUserId(), odr.getRecommendJobCode()));
                        }
                        if (customerRemds.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), insurdCustomerDTO.getId()))) {
                            //modify by zhangjian 2020-07-08 统一工号
                            cMapper.insertCustomerRemd(new CustomerRemdDTO(insurdCustomerDTO.getId(), odr.getRecommendUserId(), odr.getRecommendJobCode()));
                        }
                    }
                    if (odr.getAgentId() != null) {
                        List<CustomerAgentVO> customerAgents = cMapper.listCustomerAgents(odr.getAgentId());
                        if (customerAgents.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), appCustomerDTO.getId()))) {
                            cMapper.insertCustomerAgent(new CustomerAgentDTO(appCustomerDTO.getId(), odr.getAgentId()));
                            customerAgents.add(new CustomerAgentVO(appCustomerDTO.getId(), odr.getAgentId()));
                        }
                        if (customerAgents.stream().noneMatch(c -> Objects.equals(c.getCustomerId(), insurdCustomerDTO.getId()))) {
                            cMapper.insertCustomerAgent(new CustomerAgentDTO(insurdCustomerDTO.getId(), odr.getAgentId()));
                        }
                    }

                    //客户关系
                    List<CustomerRelationshipVO> relationships = cMapper.listCustomerRelationship(appCustomerDTO.getId());
                    if (relationships.stream().noneMatch(r -> Objects.equals(r.getCustomerId2(), insurdCustomerDTO.getId()))) {
                        if (!Objects.equals(odr.getInsuredRelationship(), "本人") && odr.getInsuredRelationship() != null) {
                            String relationship = RS_MAP.get(odr.getInsuredRelationship());
                            if (relationship != null) {
                                cMapper.insertCustomerRelationship(appCustomerDTO.getId(), insurdCustomerDTO.getId(), relationship);
                            }
                        }
                    }

                    //客户家财【车辆 房屋】
                    if (Objects.equals(odr.getPropertyInfoIsExist(), "1")) {

                        List<CustomerPropertyVO> propertys = cMapper.listCustomerProperty(insurdCustomerDTO.getId());
                        Map<String, String> regionMap = new HashMap<>(8);
                        if (!StringUtils.isEmpty(odr.getPropertyArea1()) || !(StringUtils.isEmpty(odr.getPropertyArea2()))) {
                            regionMap = settingService.getCompanyRegionList(odr.getCompanyId())
                                    .stream()
                                    .collect(Collectors.toMap(WxTreeVO::getValue, WxTreeVO::getName));
                        }
                        CustomerPropertyDTO cpDTO = mapperOrderToCustomerPropertyDTO(odr, regionMap);
                        cpDTO.setCustomerId(insurdCustomerDTO.getId());
                        if (propertys.stream().noneMatch(r -> Objects.equals(r.getHourseNo(), cpDTO.getHourseNo()))
                                || propertys.stream().noneMatch(r -> Objects.equals(r.getCarPlateNo(), cpDTO.getCarPlateNo()))
                                || propertys.stream().noneMatch(r -> Objects.equals(r.getPropertyAddress(), cpDTO.getPropertyAddress()))) {
                            cMapper.insertCustomerProperty(cpDTO);
                        }
                    }

                    //客户教育经历
                    if (!StringUtils.isEmpty(odr.getStudentType())) {

                        CustomerEducation education = mapperOrderToCustomerEducation(odr, insurdCustomerDTO.getId());

                        String schoolType = schoolTypeDict.getOrDefault(odr.getCompanyId() + "-" + education.getSchoolType(), new SmCompanySettingVO()).getOptionName();
                        if (!StringUtils.isEmpty(schoolType)) {
                            education.setSchoolType(schoolType);
                        }
                        String schoolNature = schoolNatureDict.getOrDefault(odr.getCompanyId() + "-" + education.getSchoolNature(), new SmCompanySettingVO()).getOptionName();
                        if (!StringUtils.isEmpty(schoolNature)) {
                            education.setSchoolNature(schoolNature);
                        }
                        String studentType = studentTypeDict.getOrDefault(odr.getCompanyId() + "-" + education.getStudentType(), new SmCompanySettingVO()).getOptionName();
                        if (!StringUtils.isEmpty(studentType)) {
                            education.setStudentType(studentType);
                        }
                        List<CustomerEducation> select = customerEducationMapper.listByCustomerId(insurdCustomerDTO.getId());
                        if (select.stream().noneMatch(a -> a.equals(education))) {
                            customerEducationMapper.insertSelective(education);
                        }
                    }
                    pushCustomerIdSet.add(appCustomerDTO.getId());
                    pushCustomerIdSet.add(insurdCustomerDTO.getId());
                }

                // 更新已抽取状态
                smOrderMapper.updateOrderExtractCustomerFlag(extractOrderList);

                // 发布推送客户中心客户事件 虽然已经调用了实名认证 但是没有调用fill方法 填充客户一些基本资料信息
                if (!pushCustomerIdSet.isEmpty()) {
                    pushCustomerIdSet.forEach(customerId -> busEngine.publish(new CustomerPushEvent(customerId)));
                }
            }
        }

        XxlLogger.info(log, "订单里面抽取客户定时任务结束SUCCESS");
    }

    private CustomerEducation mapperOrderToCustomerEducation(SmExtractOrderVO odr, Integer customerId) {
        CustomerEducation customerEducation = new CustomerEducation();
        BeanUtils.copyProperties(odr, customerEducation);
        customerEducation.setCustomerId(customerId);
        return customerEducation;
    }

    /**
     * mapperOrderApplicantToCustomerDTO
     *
     * @param orderVO
     * @return
     */
    private CustomerDTO mapperOrderApplicantToCustomerDTO(SmExtractOrderVO orderVO) {
        CustomerDTO dto = new CustomerDTO();
        dto.setGender(orderVO.getApplicantPersonGender());
        dto.setCustomerName(orderVO.getApplicantPersonName());
        dto.setIdNumber(orderVO.getAplicantIdNumber());
        dto.setIdType(CommonUtil.isIdCardNo(dto.getIdNumber()) ? "身份证" : "其他");
        dto.setBirthday(orderVO.getAplicantBirthday());
        dto.setCellPhone(orderVO.getApplicantCellPhone());
        dto.setRecommendId(orderVO.getRecommendUserId());
        //add by zhangjian 2020-07-08 统一工号
        dto.setRecommendJobCode(orderVO.getRecommendJobCode());
        dto.setEmail(orderVO.getApplicantEmail());
        dto.setApPolicyQty(1);
        dto.setApPolicyAmount(orderVO.getPolicyAmount());
        dto.setIsPolicyAmount(BigDecimal.ZERO);
        dto.setIsPolicyQty(0);
        // add by 20200923 客户信息收集
        dto.setNewestAdmin(orderVO.getRecommendMainJobNumber());
        dto.setOrgCode(orderVO.getOrgCode());
        dto.setAreaName(orderVO.getApplicantAreaName());
        dto.setArea(orderVO.getApplicantAreaCode());
        dto.setAddressProvider(orderVO.getApplicantAddressProvider());
        return dto;
    }


    /**
     * mapperOrderInsuredToCustomerDTO
     *
     * @param orderVO
     * @return
     */
    private CustomerDTO mapperOrderInsuredToCustomerDTO(SmExtractOrderVO orderVO) {
        CustomerDTO dto = new CustomerDTO();
        dto.setGender(orderVO.getInsuredPersonGender());
        dto.setCustomerName(orderVO.getInsuredPersonName());
        dto.setIdNumber(orderVO.getInsuredIdNumber());
        dto.setIdType(orderVO.getInsuredIdTypeName());
        if (StringUtils.isEmpty(dto.getIdType())) {
            dto.setIdType(CommonUtil.isIdCardNo(dto.getIdNumber()) ? "身份证" : "其他");
        }
        dto.setBirthday(orderVO.getInsuredBirthday());
        dto.setCellPhone(orderVO.getInsuredCellPhone());
        dto.setRecommendId(orderVO.getRecommendUserId());
        //add by zhangjian 2020-07-08 统一工号
        dto.setRecommendJobCode(orderVO.getRecommendJobCode());
        dto.setEmail(orderVO.getInsuredEmail());
        dto.setAddress(orderVO.getInsuredAddress());
        dto.setOccupation(orderVO.getInsuredOccupationName());
        dto.setIsPolicyAmount(orderVO.getPolicyAmount());
        dto.setIsPolicyQty(1);
        dto.setApPolicyQty(0);
        dto.setApPolicyAmount(BigDecimal.ZERO);
        // add by 20200923 客户信息收集
        dto.setNewestAdmin(orderVO.getRecommendMainJobNumber());
        dto.setOrgCode(orderVO.getOrgCode());
        dto.setAreaName(orderVO.getInsuredAreaName());
        dto.setArea(orderVO.getInsuredAreaCode());
        dto.setAddressProvider(orderVO.getInsuredAddressProvider());
        if (!StringUtils.isEmpty(orderVO.getInsuredAnnualIncome())) {
            dto.setAnnualIncome(new BigDecimal(orderVO.getInsuredAnnualIncome()));
        }
        return dto;
    }

    /**
     * mapperOrderToCustomerPropertyDTO
     *
     * @param vo
     * @return
     */
    private CustomerPropertyDTO mapperOrderToCustomerPropertyDTO(SmExtractOrderVO vo, Map<String, String> regionMap) {
        CustomerPropertyDTO dto = new CustomerPropertyDTO();
        dto.setCarPlateNo(getNotBlackString(vo.getCarPlateNo()));
        dto.setCarManufacturerModel(getNotBlackString(vo.getAnufacturerModel()));
        dto.setChassisNumber(getNotBlackString(vo.getChassisNumber()));
        dto.setEngineNo(getNotBlackString(vo.getEngineNo()));
        dto.setApprovedNum(getNotBlackString(vo.getApprovedNum()));
        dto.setHourseType(getNotBlackString(vo.getHourseType()));
        dto.setHourseNo(getNotBlackString(vo.getHourseNo()));
        StringBuilder asb = new StringBuilder();
        if (StringUtils.isEmpty(vo.getPropertyArea1())) {
            asb.append(getNotNullString(regionMap.get(vo.getPropertyArea1())));
        }
        if (StringUtils.isEmpty(vo.getPropertyArea2())) {
            asb.append(getNotNullString(regionMap.get(vo.getPropertyArea2())));
        }
        asb.append(getNotNullString(vo.getPropertyAddress()));
        dto.setPropertyAddress(getNotBlackString(asb.toString()));
        dto.setHourseAge(getNotBlackString(vo.getHourseAge()));
        return dto;
    }

    /**
     * 获取非null字符串
     *
     * @param str
     * @return
     */
    private String getNotNullString(String str) {
        return str == null ? "" : str.trim();
    }

    private String getNotBlackString(String str) {
        return StringUtils.isEmpty(str) ? null : str.trim();
    }

    /**
     * bean转换
     *
     * @param dto
     * @return
     */
    private WxCustomerDTO convert(CustomerDTO dto) {
        WxCustomerDTO vo = new WxCustomerDTO();
        vo.setIdNumber(dto.getIdNumber());
        vo.setCustomerId(dto.getId());
        vo.setCustomerName(dto.getCustomerName());
        vo.setIdType(dto.getIdType());
        return vo;
    }
}
