package com.cfpamf.ms.insur.admin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 小额保险短信用订单vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class SmOrderSmsNotifyVO {

    private String fhOrderId;

    private Integer insuredId;
    private Integer productId;
    /**
     * 投保产品名称
     */
    @ApiModelProperty(value = "投保产品名称")
    private String productName;

    @ApiModelProperty("产品属性")
    private String productAttrCode;

    /**
     * 投保计划名称
     */
    @ApiModelProperty(value = "投保计划名称")
    private String planName;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal totalAmount;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "item表订单金额",hidden = true)
    private BigDecimal itemTotalAmount;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    /**
     * 保单号
     */
    @ApiModelProperty(value = "投保单号")
    private String appNo;
    /**
     * 保单号
     */
    @ApiModelProperty(value = "保单号")
    private String policyNo;

    /**
     * 保单状态
     */
    private String appStatus;

    /**
     * 保单下载地址
     */
    @ApiModelProperty(value = "保单下载地址")
    private String downloadURL;

    /**
     * 投保人姓名
     */
    @ApiModelProperty(value = "投保人姓名")
    private String applicantPersonName;

    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "投保人证件号")
    private String aplicantIdNumber;

    /**
     * 投保人性别
     */
    @ApiModelProperty(value = "投保人性别")
    private String applicantPersonGender;

    /**
     * 投保人手机号
     */
    @ApiModelProperty(value = "投保人手机号")
    private String applicantCellPhone;

    /**
     * 被保人姓名
     */
    @ApiModelProperty(value = "被保人姓名")
    private String insuredPersonName;

    /**
     * 投保人证件号
     */
    @ApiModelProperty(value = "被保人证件号")
    private String insuredIdNumber;

    /**
     * 被保人手机号
     */
    @ApiModelProperty(value = "被保人手机号")
    private String insuredCellPhone;

    /**
     * 被保人邮箱
     */
    @ApiModelProperty(value = "被保人邮箱")
    private String insuredEmail;

    /**
     * 代理人姓名
     */
    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    /**
     * 代理人账号
     */
    @ApiModelProperty(value = "代理人账号")
    private String agentMobile;

    /**
     * 推荐人员工编号
     */
    @ApiModelProperty(value = "推荐人手机号")
    private String recommendUserMobile;

    /**
     * 推荐人姓名
     */
    @ApiModelProperty(value = "推荐人姓名")
    private String recommendUserName;

    @ApiModelProperty("渠道来源")
    private String subChannel;

    @ApiModelProperty(value = "渠道来源",hidden = true)
    private String channel;

    @ApiModelProperty(value = "产品类型",hidden = true)
    private String productType;

    private LocalDateTime paymentTime;
    private LocalDateTime createTime;

    @ApiModelProperty(value = "职业类别")
    private String occupationGroup;

    public String getOrderAdminName() {
        if (!StringUtils.isEmpty(recommendUserName)) {
            return recommendUserName;
        }
        return agentName;
    }

    public String getOrderAminMobile() {
        if (!StringUtils.isEmpty(recommendUserMobile)) {
            return recommendUserMobile;
        }
        return agentMobile;
    }
}
