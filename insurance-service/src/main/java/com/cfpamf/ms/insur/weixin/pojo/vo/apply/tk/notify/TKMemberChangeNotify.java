package com.cfpamf.ms.insur.weixin.pojo.vo.apply.tk.notify;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 泰康人员替换-回调通知
 * <AUTHOR>
 */
@Data
public class TKMemberChangeNotify {

    @ApiModelProperty("团险保单号")
    private String groupPolicyNo;

    @ApiModelProperty("渠道编号")
    private String channelContractNo;

    @ApiModelProperty("批改申请号")
    private String groupEndorAppNo;

    @ApiModelProperty("批改状态")
    private String endorStatus;

    @ApiModelProperty("批改单号")
    private String groupEndorNo;

    @ApiModelProperty("团险-批改电子保单")
    private String groupEpolicyUrl;

    @ApiModelProperty("团险-批改被保人信息")
    private List<TKCorrectPersonal> personalPolicyList;

    private String EdrFailCode;

    private String EdrFailMsg;

    public boolean success() {
        return Objects.equals("1", endorStatus);
    }
}
