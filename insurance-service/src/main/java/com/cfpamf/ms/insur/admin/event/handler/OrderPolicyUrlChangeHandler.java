package com.cfpamf.ms.insur.admin.event.handler;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmPolicyCacheMapper;
import com.cfpamf.ms.insur.admin.event.OrderPolicyUrlChangeEvent;
import com.cfpamf.ms.insur.admin.pojo.po.SmPolicyCache;
import com.cfpamf.ms.insur.admin.service.SmPolicyCacheService;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.google.common.eventbus.Subscribe;
import lombok.Cleanup;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR> 2020/3/20 14:21
 */
@Slf4j
@Component
public class OrderPolicyUrlChangeHandler implements BaseEventHandler {

    @Setter
    @Value("${spring.profiles.active:dev}")
    private String env;

    @Autowired
    private SmPolicyCacheMapper policyCacheMapper;

    @Autowired
    SmOrderMapper orderMapper;

    @Autowired
    private OSS ossClient;
    @Autowired
    private SmPolicyCacheService smPolicyCacheService;
    /**
     * https 调用RestTemplate 单例不能每次新建！！
     */
    private static RestTemplate restTemplate = getHttpsTemplate();

    /**
     * 构造https请求参数
     *
     * @return
     */
    private static RestTemplate getHttpsTemplate() {
        // RestTemplate 支持服务器内302重定向
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        HttpClient httpClient = HttpClientBuilder.create()
                .setRedirectStrategy(new LaxRedirectStrategy())
                .build();
        factory.setHttpClient(httpClient);
        factory.setReadTimeout(10_000);
        factory.setConnectTimeout(1_000);
        return new RestTemplate(factory);
    }

    @SneakyThrows
    @Subscribe
    public void handler(OrderPolicyUrlChangeEvent event) {

        log.info(" policy url change event {}", event);
        if (StringUtils.isBlank(event.getDownloadUrl())) {
            log.warn("policy DownloadUrl is null {}", event);
            return;
        }

        String endpoint = "https://" + AliYunOssUtil.ALIYUN_OSS_BUCKET + ".oss-cn-beijing.aliyuncs.com/";
        if (StringUtils.startsWith(event.getDownloadUrl(), endpoint)) {
            log.warn("保单目标地址是oss {}", event);
            //如果目标地址也是aliyun 就不缓存这个文件
            return;
        }

        SmPolicyCache query = new SmPolicyCache();
        query.setOrderPolicy(event.getOrderId() + event.getPolicyNo());
        SmPolicyCache cacheData = policyCacheMapper.selectOne(query);

        // 如果相同地址已经缓存过了则跳过这个步骤
        if (Objects.nonNull(cacheData) &&
                Objects.equals(cacheData.getSourceUrl(), event.getDownloadUrl())) {
            log.info("保单文件已存在 跳过{}", event);
            return;
        }

        log.info("开始缓存保单文件{}", event);
        SmPolicyCache smPolicyCache = new SmPolicyCache();

        smPolicyCache.setOrderId(event.getOrderId());
        smPolicyCache.setOrderPolicy(event.getOrderId() + event.getPolicyNo());
        smPolicyCache.setPolicyNo(event.getPolicyNo());
        // 文件名 {env}/policy/cache/{yyyyMMdd}/{orderId}-{policyNo}/{uuid}.pdf
        String ossKey = env + "/policy/cache/" +
                DateUtil.getTodayString() + "/" + event.getOrderId() + "-" + event.getPolicyNo() + "/" +
                UUID.randomUUID().toString().replaceAll("-", "") + ".pdf";


        smPolicyCache.setOssKey(ossKey);
        smPolicyCache.setSourceUrl(event.getDownloadUrl());
        ResponseEntity<Resource> responseEntity = null;
        String orderId = event.getOrderId();
        String downloadUrl = event.getDownloadUrl();
        try {
            log.info("开始读取保司电子保单文件流:{}", orderId);
            responseEntity = restTemplate.getForEntity(downloadUrl, Resource.class);

        } catch (Exception e) {
            log.warn("读取电子保单异常，可通过其他方式补偿,{},{}", orderId, downloadUrl, e);
        }
        if (responseEntity == null) {
            log.warn("读取保司电子保单文件流失败-{}",orderId);
            return;
        }
        @Cleanup
        InputStream is = responseEntity.getBody().getInputStream();
        //判断文件类型
        byte[] headBytes = new byte[4];
        is.read(headBytes, 0, 4);
        String headHexStr = Hex.encodeHexString(headBytes);
        is.reset();

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(is.available());

        metadata.setCacheControl("public");
        metadata.setHeader("Pragma", "cache");
        metadata.setContentEncoding("utf-8");
        metadata.setContentType("application/pdf");
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);

        // PDF 类别
        if (Objects.equals(BaseConstants.PDF_HEAD_HEX_STR, headHexStr)) {
            ossClient.putObject(AliYunOssUtil.ALIYUN_OSS_BUCKET,
                    ossKey, is, metadata);
        } else {// 统一安装zip处理
            @Cleanup
            ZipInputStream zis = new ZipInputStream(is);
            ZipEntry ze = null;
            if (((ze = zis.getNextEntry()) != null) && !ze.isDirectory()) {
                try {
                    ossClient.putObject(AliYunOssUtil.ALIYUN_OSS_BUCKET, ossKey, zis, metadata);
                } catch (Exception io) {
                    throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "上传文件到OSS出错", io);
                }
            }
        }
        if (ossClient.doesObjectExist(AliYunOssUtil.ALIYUN_OSS_BUCKET, ossKey)) {
            smPolicyCacheService.savePolicyCache(event,smPolicyCache,ossKey);
//            policyCacheMapper.insertOnDuplicate(smPolicyCache);
//            policyCacheMapper.updatePolicyUrl(event.getOrderId(), event.getPolicyNo(),
//                    "https://" + AliYunOssUtil.ALIYUN_OSS_BUCKET + ".oss-cn-beijing.aliyuncs.com/" + ossKey);
        }
    }

    public String handleEPolicyUrl(String url) throws IOException {

        String endpoint = "https://" + AliYunOssUtil.ALIYUN_OSS_BUCKET + ".oss-cn-beijing.aliyuncs.com/";
        if (StringUtils.startsWith(url, endpoint)) {
            log.warn("保单目标地址是oss {}", url);
            return null;
        }

        log.info("开始缓存保单文件{}", url);
        SmPolicyCache smPolicyCache = new SmPolicyCache();
        String today = DateUtil.getTodayString();
        String orderId = IdGenerator.getNextNo("Mock");
        String uuid = IdGenerator.getUuid();
        String ossKey = env + "/policy/cache/" + today + "/" + orderId + "/" + uuid + ".pdf";

        ResponseEntity<Resource> responseEntity = restTemplate.getForEntity(url, Resource.class);

        @Cleanup
        InputStream is = responseEntity.getBody().getInputStream();
        //判断文件类型
        byte[] headBytes = new byte[4];
        is.read(headBytes, 0, 4);
        String headHexStr = Hex.encodeHexString(headBytes);
        is.reset();

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(is.available());

        metadata.setCacheControl("public");
        metadata.setHeader("Pragma", "cache");
        metadata.setContentEncoding("utf-8");
        metadata.setContentType("application/pdf");
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);

        if (Objects.equals(BaseConstants.PDF_HEAD_HEX_STR, headHexStr)) {
            ossClient.putObject(AliYunOssUtil.ALIYUN_OSS_BUCKET,
                    ossKey, is, metadata);
        } else {
            @Cleanup
            ZipInputStream zis = new ZipInputStream(is);
            ZipEntry ze = null;
            if (((ze = zis.getNextEntry()) != null) && !ze.isDirectory()) {
                try {
                    ossClient.putObject(AliYunOssUtil.ALIYUN_OSS_BUCKET, ossKey, zis, metadata);
                } catch (Exception io) {
                    throw new MSBizNormalException(ExcptEnum.THIRD_REQUEST_ERROR.getCode(), "上传文件到OSS出错", io);
                }
            }
        }
        String ossUrl = "https://" + AliYunOssUtil.ALIYUN_OSS_BUCKET + ".oss-cn-beijing.aliyuncs.com/" + ossKey;
        if (ossClient.doesObjectExist(AliYunOssUtil.ALIYUN_OSS_BUCKET, ossKey)) {
            log.info("数据上传地址：{}", ossUrl);
        } else {
            log.error("数据上传失敗：{}", ossUrl);
        }
        return ossUrl;
    }

}
