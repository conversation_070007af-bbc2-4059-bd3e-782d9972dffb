package com.cfpamf.ms.insur.app.util;

import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR> 2020/9/8 09:57
 */
@UtilityClass
public class AuthContextHolder {

    static final String HEADER_NAME_USER_INFO = "x-cfpamf-auth-user-info";

    static final ObjectMapper JSON_MAPPER = new ObjectMapper();

    public CappUserInfo getUserInfo() {

        String header = getRequest().getHeader(HEADER_NAME_USER_INFO);
        if (StringUtils.isNotBlank(header)) {
            try {
                return JSON_MAPPER.readValue(header, CappUserInfo.class);
            } catch (IOException e) {
                throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097.getCode(), "http code不存在");
            }
        }
        return null;
    }

    /**
     * 获取session
     *
     * @return
     */
    private static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097);
    }
}
