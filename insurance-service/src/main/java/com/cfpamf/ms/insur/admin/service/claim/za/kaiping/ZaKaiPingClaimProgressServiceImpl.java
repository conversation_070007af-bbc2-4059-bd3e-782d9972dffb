package com.cfpamf.ms.insur.admin.service.claim.za.kaiping;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.ClaimRiskType;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCompanyStatusZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementKpMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.EnumCompanyCode;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.enums.claim.kp.*;
import com.cfpamf.ms.insur.admin.event.ClaimProcessNodeChangeEvent;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.model.claim.za.kaiping.*;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileUnitDTO;
import com.cfpamf.ms.insur.admin.pojo.form.claim.SmClaimFileSupplementVO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCompanyStatusZa;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementKp;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.BaseCommonClaimService;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.SmClaimServiceImpl;
import com.cfpamf.ms.insur.admin.util.TxUtil;
import com.cfpamf.ms.insur.admin.util.UtilDate;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimFileCombDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.za.WxKpClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.za.WxKpDetailVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zhongan.scorpoin.common.ZhongAnNotifyClient;
import com.zhongan.scorpoin.common.ZhongAnOpenException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.cfpamf.ms.insur.admin.service.ClaimWorkflow.*;

/**
 * <AUTHOR>
 * @Date 2024/6/28 10:30
 * @Version 1.0
 */
@Slf4j
@Service
public class ZaKaiPingClaimProgressServiceImpl extends BaseCommonClaimService {
    private final String KP_CHANNEL_CODE = "JX00001";

    @Autowired
    private ZaApiService zaApiService;

    @Autowired
    private SmClaimReimbursementKpMapper kpMapper;

    @Autowired
    private SmClaimServiceImpl claimService;


    @Autowired
    private SmOrderInsuredMapper insuredMapper;

    @Autowired
    private TransactionTemplate transactionTemplate;


    @Override
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimFIleIdList(int claimId, List<Integer> cfIdList) {
        List<SmClaimFileUnitVO> existedFiles = mapper.listSmClaimFileUnitByClaimFileIdList(claimId, cfIdList);

        if (CollectionUtils.isNotEmpty(existedFiles)) {
            //获取同类型文件集合
            Map<String, List<SmClaimFileUnitVO>> fileSimpleVoListMap = existedFiles.stream()
                    .collect(
                            Collectors.groupingBy(SmClaimFileUnitVO::getFileTypeCode)
                    );

            return fileSimpleVoListMap.entrySet().stream().map(entry -> {
                SmClaimFileCombVO claimFileCombVO = new SmClaimFileCombVO();
                claimFileCombVO.setFileUrls(entry.getValue().stream().map(SmClaimFileUnitVO::getFileUrl).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(entry.getValue())) {
                    claimFileCombVO.setFileTypeName(entry.getValue().get(0).getFileTypeName());
                }

                claimFileCombVO.setFileTypeCode(entry.getKey());
                return claimFileCombVO;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProgress(ProgressDTO dto) {

        log.info("ProgressDTO={}", JSON.toJSON(dto));

        Integer claimId = dto.getClaimId();
        claimMapper.updateCurrentNodeMemo(claimId, null);
        WxClaimDetailVO claimDetail = mapper.getSmClaimById(claimId);
        if (Objects.isNull(claimDetail)) {
            throw new BizException("", "理赔信息不存在");
        }
        //添加案件流程节点事件触发器 add by zhangjian 2024-10-29
        busEngine.publish(new ClaimProcessNodeChangeEvent(dto.getClaimId()));

        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY) && !(ClaimWorkflow.CLAIM_MANAGER.contains(HttpRequestUtil.getUserId()) || StringUtils.isEmpty(HttpRequestUtil.getUserId()))) {
            throw new BizException(ExcptEnum.CLAIM_ZA_STEP_TO_PAY);
        }

        //保存操作节点信息
        ClaimWorkflow.Step nextStep = nodeProcess(dto, claimDetail);
        if (Objects.equals(STEP_POST_EXPRESS, claimDetail.getClaimState())) {
            handleExpressNode(dto, claimDetail);
        }

        SmClaimReimbursementKp claimReimbursementKp = kpMapper.selectByClaimId(claimId);

        boolean orgDirectStepToPay = !ClaimRiskType.isImportantClaim(claimDetail.getRiskType())
                && (
                        Objects.equals(dto.getSCode(), STEP_DATA_CHECK_BY_PIC)
                                && (Objects.equals(dto.getOCode(), JUMP_TO_DATA_CHECK_BY_SAFES_CENTER) || Objects.equals(dto.getOCode(), OPTION_DATA_CHECK_PASS_BY_PIC))
        )
                && (Objects.isNull(claimDetail.getSafesCenterApprovalTimes()) || Objects.equals(claimDetail.getSafesCenterApprovalTimes(), 0));

//        if ((Objects.equals(dto.getSCode(), STEP_DATA_PREPARE) && Objects.equals(dto.getOCode(), DIRECT_REPORT_TO_STEP_PAY))) {
//            distinctService.autoPassStepToPay(claimId);
//        }

        // 向保司发送资料
        boolean safesCenterDirectToStepToPay = ((Objects.equals(dto.getSCode(), STEP_DATA_CHECK_BY_SAFES_CENTER) && Objects.equals(dto.getOCode(), OPTION_CODE_CHECK_BY_SAFES_CENTER)));
//                || (Objects.equals(dto.getSCode(), STEP_DATA_PREPARE) && Objects.equals(dto.getOCode(), DIRECT_REPORT_TO_STEP_PAY)))

        if (orgDirectStepToPay) {
            SmClaim smClaim = claimMapper.getByClaimId(claimId);
            distinctService.autoPassStepToPayAndUpdate(smClaim);
//            claimMapper.updateSafesCenterTimes(claimId);
        }
        if ((orgDirectStepToPay || safesCenterDirectToStepToPay) && StrUtil.isEmpty(claimReimbursementKp.getZaReportNo())) {
            uploadFileToThirdCompany(dto);
        } else if (((Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER) && Objects.equals(
                dto.getOCode(),
                ClaimWorkflow.OPTION_CODE_CHECK_BY_SAFES_CENTER
        )) || safesCenterDirectToStepToPay || orgDirectStepToPay)
                && StrUtil.isNotEmpty(claimReimbursementKp.getZaReportNo())) {
            uploadSupplementFile(dto, claimDetail);
        }

        // 如果下一步骤结案  自动结案
        if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_FINISH)) {
            ProgressDTO pDto = new ProgressDTO();
            pDto.setClaimId(claimId);
            convertStepToDTO(nextStep, pDto);
            pDto.setSettlement(claimDetail.getSettlement());
            pDto.setCreateBy(dto.getCreateBy());
            dto.setEvaluationStatus(0);
            mapper.updateFinishInfo(claimId, dto.getCreateBy(), dto);
            mapper.insertProgress(pDto);
        }

        // 补充资料提醒/邮件资料提醒微信提醒客户经理
        // 理赔赔付或者拒赔微信提醒提醒客户经理
        // 提交资料提醒机构对接人
        // 资料审核提醒保险业务中心理赔专员
        WxClaimDetailVO claim = mapper.getSmClaimById(claimId);
        Integer companyId = mapper.getCompanyIdByClaimId(claimId);
        AuthUserVO authUserVO = mapper.getClaimCustomerAdminUser(claimId);
        if (Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_POST_EXPRESS)
                || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)
                || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_GUIDED)
                || Objects.equals(dto.getOCode(), ClaimWorkflow.FINISH_STATE_PAYED)
                || Objects.equals(dto.getOCode(), ClaimWorkflow.FINISH_STATE_PAYREJECTED)
                || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED)
                || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)
                || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE)
                || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_CODE)
        ) {
            log.info("发送理赔推送 bean={}", JSON.toJSONString(dto));
            String otherExplain = null;
            if (!org.apache.commons.lang3.StringUtils.isEmpty(dto.getDataJson())) {
                otherExplain = JSON.parseObject(dto.getDataJson()).getString("otherExplain");
            }
//            boolean isHebei = Objects.nonNull(authUserVO) && Objects.equals(authUserVO.getRegionName(), "河北区域");
//            boolean isCCIC = Objects.equals(companyId, EnumCompanyCode.CCIC.getId()) && Objects.equals(claim.getRiskType(), ClaimRiskType.ACCIDENT_CLINIC.getCode());
//            boolean isDataSubmit = (
//                    Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED)
//                            && Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE)
//            ) && Objects.equals(claim.getClaimState(), ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
//            Boolean systemCheck = !isHebei && !isCCIC && isDataSubmit;

            SpringFactoryUtil.getBean(EventBusEngine.class).post(new WxClaimNotifyEvent(claimId, dto.getOCode(), null, otherExplain, false));
        }

    }

    public void uploadSupplementFile(ProgressDTO dto, WxClaimDetailVO claimDetail) {

        log.info("进入众安开平补材流程-{}", dto.getClaimId());
        Integer claimId = dto.getClaimId();
        SmClaimReimbursementKp claimReimbursementKp = kpMapper.selectByClaimId(claimId);
        if (Objects.isNull(claimReimbursementKp)) {
            throw new BizException("", "众安开平信息不存在");
        }

        WxClaimDetailVO currentClaimDetail = mapper.getSmClaimById(claimId);
        List<ProgressVO> progressVOS = mapper.listSmClaimProgressList(claimId);

        List<ProgressVO> stepProgressList = progressVOS.stream().filter(x -> Objects.equals(x.getSCode(), STEP_TO_PAY)).collect(Collectors.toList());

        ProgressVO lastStepRejectProgressVO = stepProgressList.get(stepProgressList.size() - 1);

        ZaKpCallbackParams callbackParams = JSON.parseObject(lastStepRejectProgressVO.getDataJson()).getObject("originalText", ZaKpCallbackParams.class);

        if (Objects.isNull(callbackParams)) {
            throw new BizException("", "未查到批次号");
        }

        if (CollectionUtil.isEmpty(callbackParams.getWaitSupplements())) {
            throw new BizException("", "批次号不存在");
        }

        String batchNo = callbackParams.getWaitSupplements().get(0).getBatchNo();

        if (Objects.equals(STEP_DATA_CHECK_BY_SAFES_CENTER, claimDetail.getClaimState()) || Objects.equals(STEP_TO_PAY, currentClaimDetail.getClaimState())) {

            ZaFileSupplement fileSupplement = new ZaFileSupplement();
            fileSupplement.setChannelCode(KP_CHANNEL_CODE);

            fileSupplement.setSupplementOrderNo(UUID.fastUUID().toString(true));
            fileSupplement.setReportNo(claimReimbursementKp.getZaReportNo());
            fileSupplement.setBatchNo(batchNo);
            ZaKpOperator kpOperator = new ZaKpOperator();
            kpOperator.setName(HttpRequestUtil.getUserName());
            fileSupplement.setOperator(kpOperator);
            updateZaFileInfo(claimId);
            List<SmClaimFileUnitVO> supplementsFileList = mapper.listSupplementFileByClaimId(claimId, STEP_TO_PAY);
            List<ZaKpClaimDocument> documents = getZaKpClaimDocuments(supplementsFileList);

            //查找上一个驳回节点
            Set<String> fileTypeSet = callbackParams.getWaitSupplements()
                    .stream()
                    .map(WaitSupplements::getSupplementDetails)
                    .filter(CollectionUtil::isNotEmpty)
                    .flatMap(Collection::stream)
                    .map(ZaKpSupplementFileDetail::getDocTypeCode)
                    .collect(Collectors.toSet());

//            for (ZaKpClaimDocument claimDocument : documents) {
//                if (!fileTypeSet.contains(claimDocument.getDocumentType())) {
//                    claimDocument.setDocumentType("10");
//                }
//            }
            if (CollectionUtils.isNotEmpty(fileTypeSet)) {
                documents = documents.stream().filter(x -> fileTypeSet.contains(x.getDocumentType())).collect(Collectors.toList());
            }
            fileSupplement.setClaimDocuments(documents);

            zaApiService.kpClaimSupplement(fileSupplement);
        } else if (Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_POST_EXPRESS)
                && (Objects.equals(dto.getOCode(), ClaimWorkflow.STEP_DATA_HAS_POST_EXPRESS) || Objects.equals(dto.getOCode(), ClaimWorkflow.POST_EXPRESS_INFO))) {

            SmClaimExpressVO expressVO = mapper.getLastClaimExpressByClaimId(dto.getClaimId());
            if (Objects.isNull(expressVO)) {
                throw new MSBizNormalException("", "未查询到快递信息");
            }
            ZaFileSupplement fileSupplement = new ZaFileSupplement();
            fileSupplement.setChannelCode(KP_CHANNEL_CODE);

            fileSupplement.setSupplementOrderNo(UUID.fastUUID().toString(true));
            fileSupplement.setReportNo(claimReimbursementKp.getZaReportNo());
            fileSupplement.setBatchNo(batchNo);
            ZaKpOperator kpOperator = new ZaKpOperator();
            kpOperator.setName(HttpRequestUtil.getUserName());
            fileSupplement.setOperator(kpOperator);
            fileSupplement.setExpressNo(expressVO.getExpressNo());

            zaApiService.kpClaimSupplement(fileSupplement);
        }


    }

    private void uploadFileToThirdCompany(ProgressDTO dto) {

        Integer claimId = dto.getClaimId();
        WxClaimDetailVO claimDetail = mapper.getSmClaimById(claimId);
        SmClaimReimbursementKp claimReimbursementKp = kpMapper.selectByClaimId(claimId);

        if (Objects.isNull(claimReimbursementKp)) {
            throw new BizException("", "众安开平信息不存在");
        }

        SmOrderInsured insured = insuredMapper.selectByPrimaryKeyMustExists(claimDetail.getInsuredId());
        updateZaFileInfo(claimId);
        List<SmClaimFileUnitVO> zaFileUnitVOS = mapper.listSmClaimFileUnitByClaimId(dto.getClaimId());
//        SmOrder order = Optional.ofNullable(orderMapper.listSmOrderByOrderIds(Collections.singletonList(insured.getFhOrderId())))
//                .map(list -> list.get(0)).orElseThrow(() -> new BizException("", "未查询到订单信息"));

        ZaKpApply kpApply = new ZaKpApply();
        kpApply.setChannelCode(KP_CHANNEL_CODE);
        kpApply.setPolicyNo(insured.getPolicyNo());
        kpApply.setClaimOrderNo(claimDetail.getClaimNo());
        kpApply.setAccidentDesc(claimDetail.getRiskDesc());
        //报案时间取向众安首次提交时间
        if (org.springframework.util.StringUtils.isEmpty(claimReimbursementKp.getZaReportNo())) {
            kpApply.setReportTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        }
        if (Objects.isNull(claimDetail.getRiskTime())) {
            throw new BizException("", "出险时间异常");
        }
        kpApply.setAccidentDate(UtilDate.dateToLocalDateTime(claimDetail.getRiskTime()).format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        kpApply.setAccidentReason(EnumKpRiskType.mapByClaimRiskTypeCode(claimDetail.getRiskType()).getCode());
        kpApply.setReportAmount(claimDetail.getEstimatedAmount().toPlainString());
        kpApply.setAccidentType(EnumKpRiskType.mapByClaimRiskTypeCode(claimDetail.getRiskType()).getCode());

        ZaKpClaimReporter reporter = new ZaKpClaimReporter();
        reporter.setName(claimReimbursementKp.getReporterName());

        reporter.setPhoneNo(claimReimbursementKp.getReporterPhone());

        reporter.setRelationToPH(claimReimbursementKp.getReporterRelationToInsured());
        kpApply.setReporter(reporter);

        ZaKpAccidentPerson accidentPerson = new ZaKpAccidentPerson();

        accidentPerson.setName(claimReimbursementKp.getAccidentName());
        accidentPerson.setRelationToPH(claimReimbursementKp.getAccidentRelationToInsured());
        kpApply.setAccidentPersons(Collections.singletonList(accidentPerson));

        List<ZaKpClaimDocument> documents = getZaKpClaimDocuments(zaFileUnitVOS);

        kpApply.setClaimDocuments(documents);

        ZaKpClaimAccountInfo account = new ZaKpClaimAccountInfo();
        account.setPayChannel(claimReimbursementKp.getPayeeWay());
        account.setAccountName(claimReimbursementKp.getPayeeName());
        if (Objects.equals("1", claimReimbursementKp.getPayeeWay())) {
            account.setAccountNo(claimReimbursementKp.getZfbAccount());
        } else if (Objects.equals("2", claimReimbursementKp.getPayeeWay())) {
            account.setAccountNo(claimReimbursementKp.getBankCard());
            account.setBankName(claimReimbursementKp.getDepositBankName() + claimReimbursementKp.getBranchBankName());
        }
        kpApply.setAccount(account);

        ZaKpResponse kpResponse = zaApiService.kpClaimReport(kpApply);

        kpMapper.updateKpReportNoByClaimId(claimId, kpResponse.getReportNo());

    }

    private List<ZaKpClaimDocument> getZaKpClaimDocuments(List<SmClaimFileUnitVO> zaFileUnitVOS) {


        List<ZaKpClaimDocument> documents = zaFileUnitVOS.stream().collect(Collectors.groupingBy(
                SmClaimFileUnitVO::getFileTypeCode
        )).values().stream().flatMap(
                x -> IntStream.range(0, x.size()).mapToObj(
                        i -> {
                            ZaKpClaimDocument kpClaimDocument = new ZaKpClaimDocument();
                            kpClaimDocument.setDocumentName(x.get(i).getFileTypeName() + i);
                            String mapCode = EnumKpFileMap.mapByCode(x.get(i).getFileTypeCode());
                            if (Objects.isNull(mapCode)) {
                                throw new BizException("", "理赔材料编码不匹配");
                            }
                            kpClaimDocument.setDocumentType(mapCode);
                            kpClaimDocument.setDocumentKey(x.get(i).getZaKey());
                            return kpClaimDocument;
                        }
                )
        ).collect(Collectors.toList());
        return documents;
    }

    private void handleExpressNode(ProgressDTO dto, WxClaimDetailVO claimDetailVO) {

        // 管理后台理操作已邮寄资料  邮寄资料日期
        if (dto.getOTime() != null && Objects.equals(dto.getOCode(), ClaimWorkflow.STEP_DATA_HAS_POST_EXPRESS)) {
            updateExpressTime(dto.getClaimId(), CommonUtil.getStartTimeOfDay(dto.getOTime()));
            uploadSupplementFile(dto, claimDetailVO);
        }

        // 微信理赔填写邮寄资料  邮寄资料日期
        if (dto.getExpressTime() != null && Objects.equals(dto.getOCode(), POST_EXPRESS_INFO)) {
            updateExpressTime(dto.getClaimId(), CommonUtil.getStartTimeOfDay(dto.getExpressTime()));
            uploadSupplementFile(dto, claimDetailVO);
        }

    }


    /**
     * 处理流程节点
     *
     * @param dto
     * @param claimDetail
     * @return
     */
    private ClaimWorkflow.Step nodeProcess(ProgressDTO dto, WxClaimDetailVO claimDetail) {

        if (!Objects.equals(claimDetail.getClaimState(), dto.getSCode())) {
            throw new MSBizNormalException("-1", "不可操作该流程，");
        }

        // 如果页面不输入时间 创建时间取当前时间
        if (dto.getOTime() == null) {
            dto.setOTime(new Date());
        } else {
            dto.setOTime(DateUtil.getBeginOfDay(dto.getOTime()));
        }

        ClaimWorkflow.Step nextStep = workflow.goToNextStep(dto.getSCode(), dto.getOCode());

        if (Objects.isNull(nextStep)) {
            throw new MSBizNormalException("-1", "不可操作该流程，下一步选择错误");
        }

        dto.setSettlement(claimDetail.getSettlement());
        mapper.insertProgress(dto);

        // 资料审核(渠道pco)需补充资料审批节需要修改下一节点名称为补充资料提交
        if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) && Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)) {
            nextStep.setSName("补充资料提交");
        }
        // 更新理赔状态
        int claimId = dto.getClaimId();
        // 更新邮寄资料时间/结案时间/邮寄纸质资料时间 当前环节开始时间/ 保险业务中心审核的次数
        mapper.updateClaimStatus(
                claimId
                , dto.getOCode()
                , dto.getCreateBy()
                , dto.getOTime()
                , nextStep
                , claimHastenProcessor.calHastenTime(nextStep.getSCode(), claimId)
        );
        if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_TO_PAY) ||
                Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) ||
                Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE2) ||
                Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE3)
        ) {
            claimHastenProcessor.updateExpectDays(nextStep.getSCode(), claimId);
        }

        signFileApprove(claimId, claimDetail.getClaimState());

        return nextStep;
    }


    @Override
    public String deFirstRow(String code) {
        return null;
    }

    @Override
    public String deFirstColumn(String code) {
        return null;
    }

    @Override
    public List<String> channel() {
        return Lists.newArrayList(
                EnumClaimProcessType.ZA_KAI_PING_API.getCode()
                , EnumClaimProcessType.ZA_KAI_PING_THIRD_REPORT_API.getCode()
                , EnumClaimProcessType.ZA_KAI_PING_THIRD_REPORT_FAMILY_API.getCode()
        );
    }

    @Override
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimId(int claimId) {
        return super.getSmClaimFileCombByClaimId(claimId);
    }

    @Override
    public void callbackWhenReport(WxClaimDTO dto) {

        SmClaimReimbursementKp reimbursementKp = new SmClaimReimbursementKp();
        reimbursementKp.setAccidentName(dto.getAccidentName());
        reimbursementKp.setAccidentRelationToInsured(dto.getAccidentRelationToInsured());
        reimbursementKp.setClaimId(dto.getId());
        reimbursementKp.setCreateBy(HttpRequestUtil.getUserId());
        kpMapper.insertOrUpdate(reimbursementKp);

    }





//    @Transactional(rollbackFor = Exception.class)
//    public ProgressDTO constructSaveFileProgressDto(int claimId, WxClaimFileCombDTO dto, WxSessionVO session) {
//        WxClaimDetailVO claimDetailVO = mapper.getSmClaimById(claimId);
//        // 触发上传理赔文件流程
//        ProgressDTO expressDTO = new ProgressDTO();
//        expressDTO.setClaimId(claimDetailVO.getId());
//        expressDTO.setSName(claimDetailVO.getClaimResult());
//        expressDTO.setSCode(claimDetailVO.getClaimState());
//        if (ClaimRiskType.isImportantClaim(claimDetailVO.getRiskType()) || Objects.equals(expressDTO.getSCode(), STEP_DATA_PREPARE2)) {
//            expressDTO.setOCode(JUMP_TO_DATA_CHECK_BY_SAFES_CENTER);
//            expressDTO.setOName("跳转进度至总部审核阶段");
//        } else {
//            expressDTO.setOName("跳转进度至保司核赔");
//            expressDTO.setOCode(DIRECT_REPORT_TO_STEP_PAY);
//        }
//
//        expressDTO.setCreateBy(dto.getUserName());
//        return expressDTO;
//
//    }




    @Transactional(rollbackFor = Exception.class)
    public void saveKpClaimData(WxKpClaimDTO zaClaimDTO) {
        log.info("众安开平理赔参数：{}", JSONObject.toJSONString(zaClaimDTO));
//        checkZaData(zaClaimDTO);
        WxClaimDTO wxClaimDTO = zaClaimDTO.getWxClaimDTO();
        wxClaimDTO.setVisitingDate(zaClaimDTO.getVisitingDate());
        wxClaimDTO.setVisitingHospital(zaClaimDTO.getVistingHospital());
        SmClaimReimbursementKp reimbursementKP = new SmClaimReimbursementKp();
        BeanUtil.copyProperties(zaClaimDTO, reimbursementKP);
        //查找id
        Integer claimId = wxClaimDTO.getId();
        SmClaimReimbursementKp existedZaClaim = kpMapper.selectByClaimId(claimId);

        SmClaim claim = claimMapper.getByClaimId(claimId);
        claimService.updateClaim(wxClaimDTO);
        if (Objects.isNull(existedZaClaim)) {
            reimbursementKP.setClaimId(claimId);
            reimbursementKP.setCreateBy(HttpRequestUtil.getUserId());
            kpMapper.insertSelective(reimbursementKP);
            return;
        }

        if (
                StrUtil.isNotEmpty(existedZaClaim.getZaReportNo())
                        && !(Objects.equals(claim.getRiskType(), wxClaimDTO.getRiskType()) || (ClaimRiskType.kpSameRiskTypeContains().contains(claim.getRiskType()) && ClaimRiskType
                        .kpSameRiskTypeContains().contains(wxClaimDTO.getRiskType())))
        ) {
            throw new BizException("", "该保单暂不支持所选出险类型");
        }

        reimbursementKP.setId(existedZaClaim.getId());
        kpMapper.updateByPrimaryKeySelective(reimbursementKP);
    }

    @Autowired
    private SmClaimReimbursementKpMapper claimReimbursementKpMapper;

    /**
     * 查询详情
     *
     * @param claimId
     * @return
     */
    public WxKpDetailVO detailKp(Integer claimId) {

        //查询详情
        WxClaimDetailVO claimDetailVO = mapper.getSmClaimById(claimId);
        if (Objects.isNull(claimDetailVO)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR);
        }

//        SmClaimProgressVO claimProgressVO = getClaimProgress(claimId);
//        if (Objects.nonNull(claimProgressVO.getNextProgress())) {
//            List<BaseWorkflow.Option> options = Optional.ofNullable(claimProgressVO.getNextProgress()).map(BaseWorkflow.Step::getOptions)
//                    .orElse(Collections.emptyList())
//                    .stream()
//                    .filter(x -> !org.apache.commons.lang3.StringUtils.equals(x.getOType(), "-6"))
//                    .collect(Collectors.toList());
//            claimProgressVO.getNextProgress().setOptions(options);
//        }
//        claimDetailVO.setProgress(claimProgressVO);

        //返回保险公司信息
        SmClaimReimbursementKp claimReimbursementKp = claimReimbursementKpMapper.selectByClaimId(claimId);

        WxKpDetailVO result = new WxKpDetailVO();

        if (Objects.isNull(claimReimbursementKp)) {
            result.setClaimDetailVO(claimDetailVO);
            return result;
        }

        BeanUtil.copyProperties(claimReimbursementKp, result);
        result.setClaimDetailVO(claimDetailVO);
        return result;
    }

    private void updateZaFileInfo(int claimId) {

        List<SmClaimFileUnitVO> zaFileUnitVOS = mapper.listSmClaimFileUnitByClaimId(claimId);
        if (CollectionUtils.isNotEmpty(zaFileUnitVOS)) {
            distinctService.uploadToZa(zaFileUnitVOS);
        }

    }


    private Map<String, KpClaimStatusUpdateService> reportStatusProgressMap;

    @Autowired
    public void constructCallback(@Autowired List<KpClaimStatusUpdateService> claimStatusUpdateServiceList) {

        this.reportStatusProgressMap = claimStatusUpdateServiceList.stream().collect(
                HashMap::new,
                (map, t) -> map.putAll(t.getClaimStatusType().stream().collect(Collectors.toMap(Function.identity(), key -> t))),
                HashMap::putAll
        );

    }

    @Autowired
    private ZaApiProperties zaApiProperties;

    @Autowired
    private SmClaimCompanyStatusZaMapper claimCompanyStatusZaMapper;

    @Autowired
    private DLockTemplate lockTemplate;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private SmClaimMapper claimMapper;

    public void handlerZaKpReportStatusCallBack(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        ZaKpCallbackParams claimReport = null;
        String messageId = UUID.fastUUID().toString(true);
        ZhongAnNotifyClient notify = null;
        SmClaimCompanyStatusZa companyStatusZa = null;
        try{
            notify = new ZhongAnNotifyClient(zaApiProperties.getEnv(), zaApiProperties.getZaKpPrivateKey());
            Map<String, String[]> map = request.getParameterMap();
            log.info("众安开平回调原报文-{}", JSONObject.toJSONString(map));
            String result = notify.parseNotifyRequest(map);
            log.info("众安开平回调报文-{}", result);

            claimReport = jsonMapper.readValue(result, ZaKpCallbackParams.class);

//            messageId = claimReport.getWaitSupplements().get(0).getBatchNo();

            log.info("众安开平回调报文messageId-{}", messageId);
            if (claimCompanyStatusZaMapper.existedByMessageId(messageId)) {
                response.getWriter().write(notify.buildNotifyResponse("success"));
                return;
            }
            Integer claimId;
            SmClaimReimbursementKp reimbursementKp = kpMapper.selectByReportNo(claimReport.getClaimNo());

            if (!Objects.equals(claimReport.getClaimStatus(), EnumKpCallbackStatus.REPORTED.getCode()) && Objects.isNull(reimbursementKp)) {
                log.warn("众安开平回调fail-报案信息不存在-{}", claimReport.getClaimNo());
                throw new BizException("", "报案信息不存在");
            }

            companyStatusZa = new SmClaimCompanyStatusZa();

            if (Objects.nonNull(reimbursementKp)) {
                companyStatusZa.setClaimId(reimbursementKp.getClaimId());
                claimId = reimbursementKp.getClaimId();
            } else {
                claimId = null;
            }

            companyStatusZa.setZaReportNo(claimReport.getClaimNo());
            companyStatusZa.setReportBizNo(claimReport.getChannelClaimNo());
//            companyStatusZa.setSource("ZA-KP");
            companyStatusZa.setMessageId(messageId);
            companyStatusZa.setDataJson(JSONObject.toJSONString(claimReport));
            companyStatusZa.setHandleStatus("0");
            claimCompanyStatusZaMapper.insertSelective(companyStatusZa);

            lockTemplate.lock(claimReport.getClaimNo(), 5);



            if (!EnumKpCallbackStatus.propertySupport().contains(claimReport.getClaimStatus())
                    && EnumKpRiskType.kpFamilyProperty.contains(claimReport.getClaimInfo().getAccidentReason())
            ) {
                response.getWriter().write(notify.buildNotifyResponse("success"));
                return;
            }

            ZaKpCallbackParams finalClaimReport = claimReport;
            TxUtil.getInstance(transactionTemplate).doTransactionWithoutResult(
                    () -> {
                        if (Objects.nonNull(claimId)) {
                            SmClaim smClaim = claimMapper.getByClaimId(claimId);

                            if (Objects.equals(smClaim.getClaimState(), STEP_FINISH) && !EnumKpCallbackStatus.payStatus().contains(finalClaimReport.getClaimStatus())) {
                                throw new BizException("", StrUtil.format("已结案案件不支持该操作-{}", finalClaimReport.getClaimStatus()));
                            }

                            //先跳到保司核赔节点
                            if (!EnumKpCallbackStatus.unJumpStatus().contains(finalClaimReport.getClaimStatus())) {
                                if (Objects.equals(finalClaimReport.getClaimSubStatus(), EnumKpHangSign.SUSPENDING.getCode()) ) {
                                    //挂起，不处理
                                    log.info("理赔{}补材标记为{}", claimId, finalClaimReport.getClaimSubStatus());
                                    claimMapper.updateCurrentNodeMemo(claimId, EnumKpClaimHangCause.mapByCode(finalClaimReport.getClaimHangCause()));
                                    return;
                                } else {
                                    distinctService.autoPassStepToPayAndUpdate(smClaim);
                                }
                            }
                        }

                        reportStatusProgressMap.get(finalClaimReport.getClaimStatus()).handlerProcess(finalClaimReport, claimId);
                    }
            );
            SmClaimCompanyStatusZa updateObj = new SmClaimCompanyStatusZa();
            updateObj.setHandleStatus("1");
            updateObj.setId(companyStatusZa.getId());
            claimCompanyStatusZaMapper.updateByPrimaryKeySelective(updateObj);
            response.getWriter().write(notify.buildNotifyResponse("success"));
        } catch(Exception e) {
            log.warn("众安开平理赔回调通知处理失败-{}", e);

            if (Objects.nonNull(companyStatusZa) && Objects.nonNull(companyStatusZa.getId())) {
                SmClaimCompanyStatusZa updateObj = new SmClaimCompanyStatusZa();
                updateObj.setHandleStatus("2");
                updateObj.setId(companyStatusZa.getId());
                claimCompanyStatusZaMapper.updateByPrimaryKeySelective(updateObj);
            }
            if (Objects.nonNull(notify)) {
                try{
                    response.getWriter().write(notify.buildNotifyResponse(StrUtil.format("fail-{}", e.getMessage())));
                } catch(ZhongAnOpenException ex) {
                    log.info("众安开平理赔回调通知处理失败-{}", ex);
                }
            }

        } finally {
            if (Objects.nonNull(claimReport)) {
                lockTemplate.unLock(claimReport.getClaimNo());
            }
        }

    }



    public String queryKpStatus(int claimId) {

        SmClaimReimbursementKp kp = kpMapper.selectByClaimId(claimId);
        if (Objects.isNull(kp) || StrUtil.isEmpty(kp.getZaReportNo())) {
            throw new BizException("", "案件未与保司同步");
        }
        ZaKpQuery query = new ZaKpQuery();
        query.setChannelCode(KP_CHANNEL_CODE);
        query.setReportNo(kp.getZaReportNo());
        ZaKpQueryResponse queryResponse = zaApiService.queryZaKpClaimInfo(query);
        return EnumKpCallbackStatus.getNameByCode(queryResponse.getClaimStatus());

    }

    public void test(ZaKpCallbackParams claimReport, int claimId) {
        reportStatusProgressMap.get(claimReport.getClaimStatus()).handlerProcess(claimReport, claimId);
    }
}
