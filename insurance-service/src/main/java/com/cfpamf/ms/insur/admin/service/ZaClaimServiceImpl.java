package com.cfpamf.ms.insur.admin.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.claim.form.ZaClaimQuery;
import com.cfpamf.ms.insur.admin.claim.vo.ZaBankInfoVO;
import com.cfpamf.ms.insur.admin.claim.vo.ZaClaimRegionInfoVO;
import com.cfpamf.ms.insur.admin.constant.EnumZaClaimRiskTypeMap;
import com.cfpamf.ms.insur.admin.constant.ZaClaimFileOrder;
import com.cfpamf.ms.insur.admin.constant.ZaClaimFileType;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimBankZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCompanyStatusZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimRegionZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementZaMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.enums.claim.za.EnumZaChannelAccidentType;
import com.cfpamf.ms.insur.admin.event.ClaimProcessNodeChangeEvent;
import com.cfpamf.ms.insur.admin.enums.claim.za.EnumZaClaimStatus;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.external.zhongan.ZaApiProperties;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.model.claim.*;
import com.cfpamf.ms.insur.admin.external.zhongan.util.ZaAESUtils;
import com.cfpamf.ms.insur.admin.pojo.convertor.ZaClaimConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileCombDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileUnitDTO;
import com.cfpamf.ms.insur.admin.pojo.form.claim.ValidatorParams;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimCompanyStatusZa;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementZa;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.po.zhongan.ZaClaimResponse;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ClaimFileSimpleVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.ZaClaimStatusDetailVO;
import com.cfpamf.ms.insur.admin.service.claim.ZaClaimStatusUpdateService;
import com.cfpamf.ms.insur.admin.util.TxUtil;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.RegExUtils;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimFileCombDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.za.WxZaClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.za.WxZaClaimDetailVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.util.UrlUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.service.ClaimWorkflow.STEP_TO_PAY;

/**
 * <AUTHOR>
 * @Date 2022/3/28 10:40
 * @Version 1.0
 */
@Service
@Slf4j
public class ZaClaimServiceImpl extends BaseCommonClaimService {
    /**
     * 理赔工作流
     */
    @Autowired
    private ClaimWorkflow workflow;

    /**
     * 小额保险理赔mapper
     */
    @Autowired
    private SmClaimMapper claimMapper;

    /**
     * 小额保险订单mapper
     */
    @Autowired
    private SmOrderMapper orderMapper;

    @Autowired
    private SmClaimReimbursementZaMapper claimReimbursementZaMapper;

    @Autowired
    private SmOrderInsuredMapper insuredMapper;

    /**
     * 用户service
     */
    @Autowired
    private UserService userService;

    @Autowired
    private ZaApiService zaApiService;

    @Autowired
    private SmClaimBankZaMapper claimBankZaMapper;

    @Autowired
    private SmClaimRegionZaMapper regionZaMapper;

    @Autowired
    private AuthUserMapper authUserMapper;

    @Autowired
    private ZaApiProperties zaApiProperties;

    @Autowired
    private SmClaimCompanyStatusZaMapper claimCompanyStatusZaMapper;

    @Autowired
    private DLockTemplate lockTemplate;

    @Autowired
    private ObjectMapper jsonMapper;

    @Autowired
    private SmClaimServiceImpl claimService;

    @Autowired
    private SmProductMapper productMapper;

    private Map<String, ZaClaimStatusUpdateService> reportStatusProgressMap;

    @Autowired
    private SmOrderItemMapper smOrderItemMapper;

    public static final String KEY = "NoXWI0gnu76DP6Uz";

    @Autowired
    public void constructReportStatusProgressMap(@Autowired List<ZaClaimStatusUpdateService> zaClaimStatusUpdateServices) {
        this.reportStatusProgressMap = zaClaimStatusUpdateServices.stream().collect(
                HashMap::new,
                (map, t) -> map.putAll(t.getClaimStatusType().stream().collect(Collectors.toMap(Function.identity(), key -> t))),
                HashMap::putAll
        );
    }

    /**
     * 查询详情
     *
     * @param claimId
     * @return
     */
    public WxZaClaimDetailVO detail(Integer claimId) {

        //查询详情
        WxClaimDetailVO claimDetailVO = claimMapper.getSmClaimById(claimId);
        if (Objects.isNull(claimDetailVO)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR);
        }

        SmClaimProgressVO claimProgressVO = getClaimProgress(claimId);
        if (Objects.nonNull(claimProgressVO.getNextProgress())) {
            List<BaseWorkflow.Option> options = Optional.ofNullable(claimProgressVO.getNextProgress()).map(BaseWorkflow.Step::getOptions)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(x -> !org.apache.commons.lang3.StringUtils.equals(x.getOType(), "-6"))
                    .collect(Collectors.toList());
            claimProgressVO.getNextProgress().setOptions(options);
        }
        claimDetailVO.setProgress(claimProgressVO);

        //返回保险公司信息
        SmClaimReimbursementZa claimReimbursementZa = claimReimbursementZaMapper.selectByClaimId(claimId);

        WxZaClaimDetailVO result = new WxZaClaimDetailVO();

        if (Objects.isNull(claimReimbursementZa)) {
            result.setClaimDetailVO(claimDetailVO);
            return result;
        }

        result = ZaClaimConvertor.CNT.toWxZaClaimDetailVO(claimReimbursementZa);
        result.setClaimDetailVO(claimDetailVO);
        result.setZaClaimFiles(getCombineFile(claimId));
        return result;
    }



    /**
     * 保存理赔数据
     *
     * @param zaClaimDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveZaClaimData(WxZaClaimDTO zaClaimDTO) {
        log.info("众安理赔参数：{}", JSONObject.toJSONString(zaClaimDTO));
        checkZaData(zaClaimDTO);
        SmClaimReimbursementZa reimbursementZa = WxZaClaimDTO.convertToSmClaimReimbursementZa(zaClaimDTO);
        //查找id
        Integer claimId = zaClaimDTO.getWxClaimDTO().getId();
        SmClaimReimbursementZa existedZaClaim = claimReimbursementZaMapper.selectByClaimId(claimId);
        if (Objects.isNull(existedZaClaim)) {
            reimbursementZa.setClaimId(claimId);
            reimbursementZa.setCreateBy(HttpRequestUtil.getUserId());
            claimReimbursementZaMapper.insert(reimbursementZa);
            return;
        }
        reimbursementZa.setId(existedZaClaim.getId());
        claimService.updateClaim(zaClaimDTO.getWxClaimDTO());
        claimReimbursementZaMapper.updateByPrimaryKeySelective(reimbursementZa);
    }

    private void checkZaData(WxZaClaimDTO zaClaimDTO) {
        boolean success = Arrays.stream(zaClaimDTO.getReimbursementType().split(","))
                .allMatch(item -> EnumZaChannelAccidentType.queryChannelCode(zaClaimDTO.getAccidentType()).contains(item));
        if (!success) {
            throw new BizException("", "事故类别与涉及报销类型不匹配");
        }
        if (!StringUtils.isEmpty(zaClaimDTO.getApplierEmail()) && !RegExUtils.validMail(zaClaimDTO.getApplierEmail())) {
            throw new BizException("", "邮箱格式不正确，请删除或重新填写！");
        }
    }

    public List<SmClaimFileCombVO> getCombineFileByFileIds(Integer claimId, List<Integer> fileIdList) {
        List<SmClaimFileUnitVO> existedFiles = claimMapper.listSmClaimFileUnitByClaimFileIdList(claimId, fileIdList);

        if (CollectionUtils.isNotEmpty(existedFiles)) {
            //获取同类型文件集合
            Map<String, List<ClaimFileSimpleVo>> fileSimpleVoListMap = existedFiles.stream()
                    .map(smClaimFileUnitVO -> {
                        ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
                        claimFileSimpleVo.setCfId(smClaimFileUnitVO.getCfId());
                        claimFileSimpleVo.setFileUrl(smClaimFileUnitVO.getFileUrl());
                        claimFileSimpleVo.setNewFlag(smClaimFileUnitVO.getNewFlag());
                        claimFileSimpleVo.setFileTypeCode(smClaimFileUnitVO.getFileTypeCode());
                        claimFileSimpleVo.setZaKey(smClaimFileUnitVO.getZaKey());
                        return claimFileSimpleVo;
                    })
                    .collect(Collectors.groupingBy(ClaimFileSimpleVo::getFileTypeCode));

            return fileSimpleVoListMap.entrySet().stream().map(entry -> {
                SmClaimFileCombVO claimFileCombVO = new SmClaimFileCombVO();
                claimFileCombVO.setFileUrls(entry.getValue().stream().map(ClaimFileSimpleVo::getFileUrl).collect(Collectors.toList()));
                claimFileCombVO.setFileTypeName(ZaClaimFileType.getNameByCode(entry.getKey()));
                claimFileCombVO.setFileTypeCode(entry.getKey());
                return claimFileCombVO;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 组合默认的文件和已存在的文件
     *
     * @param claimId
     */
    public List<SmClaimFileCombVO> getCombineFile(Integer claimId) {
        List<SmClaimFileUnitVO> existedFiles = claimMapper.listSmClaimFileUnitByClaimId(claimId);

        //获取同类型文件集合
        Map<String, List<ClaimFileSimpleVo>> fileSimpleVoListMap = existedFiles.stream()
                .map(smClaimFileUnitVO -> {
                    ClaimFileSimpleVo claimFileSimpleVo = new ClaimFileSimpleVo();
                    claimFileSimpleVo.setCfId(smClaimFileUnitVO.getCfId());
                    claimFileSimpleVo.setFileUrl(smClaimFileUnitVO.getFileUrl());
                    claimFileSimpleVo.setNewFlag(smClaimFileUnitVO.getNewFlag());
                    claimFileSimpleVo.setFileTypeCode(smClaimFileUnitVO.getFileTypeCode());
                    claimFileSimpleVo.setZaKey(smClaimFileUnitVO.getZaKey());
                    claimFileSimpleVo.setFileApproveNode(smClaimFileUnitVO.getFileApproveNode());
                    claimFileSimpleVo.setFileUniqueCode(smClaimFileUnitVO.getFileUniqueCode());
                    return claimFileSimpleVo;
                })
                .collect(Collectors.groupingBy(ClaimFileSimpleVo::getFileTypeCode));

        Map<String, List<SmClaimFileUnitVO>> existedFileCodeMap = existedFiles.stream()
                .collect(Collectors.groupingBy(SmClaimFileUnitVO::getFileTypeCode));

        SmClaimReimbursementZa zaClaim = claimReimbursementZaMapper.selectByClaimId(claimId);

        if (Objects.isNull(zaClaim)) {
            return Collections.emptyList();
        }

        Map<String, SmClaimFileCombVO> defaultMap = listDefaultFile(claimId).stream()
                .collect(Collectors.toMap(SmClaimFileCombVO::getFileTypeCode, Function.identity()));
        ValidatorParams validatorParams = constructValidatorParams(claimId);
        for (String fileTypeCode : existedFileCodeMap.keySet()) {
            if (!defaultMap.containsKey(fileTypeCode)) {
                SmClaimFileCombVO smClaimFileCombVO = getSmClaimFileCombVO(
                        validatorParams
                        , EnumZaChannelAccidentType.queryByAccidentType(zaClaim.getAccidentType())
                                .row(zaClaim.getReimbursementType().split(",")[0])
                                .entrySet()
                                .stream()
                                .filter(item -> Objects.equals(item.getKey(), fileTypeCode))
                                .findAny()
                                .orElse(null)
                );
                if (Objects.isNull(smClaimFileCombVO)) {
                    smClaimFileCombVO = new SmClaimFileCombVO();
                    smClaimFileCombVO.setFileRequire(Boolean.FALSE);
                    smClaimFileCombVO.setFileTypeCode(fileTypeCode);
                    if (CollectionUtil.isNotEmpty(existedFileCodeMap.get(fileTypeCode))) {
                        smClaimFileCombVO.setFileTypeName(existedFileCodeMap.get(fileTypeCode).get(0).getFileTypeName());
                    }else {
                        smClaimFileCombVO.setFileTypeName(fileTypeCode);
                    }

                }
                defaultMap.put(
                        fileTypeCode,
                        smClaimFileCombVO
                );
            }
        }

        for (String fileCode : defaultMap.keySet()) {
            if (Objects.nonNull(defaultMap.get(fileCode))) {
                defaultMap.get(fileCode).setFileUrls(
                        Optional.ofNullable(fileSimpleVoListMap.get(fileCode))
                                .map(i -> i.stream()
                                        .map(ClaimFileSimpleVo::getFileUrl)
                                        .collect(Collectors.toList()))
                                .orElse(Collections.emptyList())

                );
                defaultMap.get(fileCode).setFileSimpleVoList(
                        CollectionUtils.isEmpty(fileSimpleVoListMap.get(fileCode)) ? Collections.emptyList() : fileSimpleVoListMap.get(fileCode)
                );
            }
        }

        return defaultMap.values().stream().filter(Objects::nonNull).sorted((a, b) -> {
            int o1 = ZaClaimFileOrder.getOrder(a.getFileTypeCode());
            int o2 = ZaClaimFileOrder.getOrder(b.getFileTypeCode());
            return o1 - o2;
        }).collect(Collectors.toList());

    }


    /**
     * @return
     */
    public List<SmClaimFileCombVO> listDefaultFile(int claimId) {

        ValidatorParams validatorParams = constructValidatorParams(claimId);

        SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(claimId);

        return listDefaultFile(
                EnumZaChannelAccidentType.queryByAccidentType(reimbursementZa.getAccidentType()),
                validatorParams
        );

    }

    private ValidatorParams constructValidatorParams(Integer claimId) {
        //查找理赔信息
        SmClaim claim = claimMapper.getByClaimId(claimId);
        //查找众安理赔信息
        //根据保单号查询被保人信息
        SmOrderInsured insured = insuredMapper.selectByPrimaryKeyMustExists(claim.getInsuredId());
        SmClaimReimbursementZa zaClaim = claimReimbursementZaMapper.selectByClaimId(claimId);

        if (Objects.isNull(zaClaim)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "众安理赔信息未填写");
        }

        //转换成对象
        ValidatorParams validatorParams = new ValidatorParams();
        validatorParams.setAccidentType(zaClaim.getReimbursementType());
        validatorParams.setApplyAmount(claim.getEstimatedAmount());
        validatorParams.setInsuredName(insured.getPersonName());
        validatorParams.setInsuredIdNumber(insured.getIdNumber());
        validatorParams.setPayeeName(zaClaim.getPayeeName());
        validatorParams.setPayeeRelation(zaClaim.getPayeeWithCasualty());
        validatorParams.setPayeeWay(zaClaim.getPayeeWay());
        return validatorParams;
    }


    private <T extends ValidatorParams> List<SmClaimFileCombVO> listDefaultFile(
            Table<String, String, Function<T, Integer>> table, T validatorParams) {
        return Arrays.stream(validatorParams.getAccidentType().split(","))
                .flatMap(type -> table.row(type).entrySet().stream())
                .filter(item -> Objects.nonNull(item.getValue().apply(validatorParams)))
                .map(item -> getSmClaimFileCombVO(validatorParams, item))
                .collect(
                        Collectors.toMap(
                                SmClaimFileCombVO::getFileTypeCode
                                , Function.identity()
                                , (a, b) -> Boolean.TRUE.equals(a.getFileRequire()) ? a : b
                        )
                ).values().stream().collect(Collectors.toList());

    }

    private <T extends ValidatorParams> SmClaimFileCombVO getSmClaimFileCombVO(T validatorParams,
                                                                               Map.Entry<String, Function<T, Integer>> item) {

        if (Objects.isNull(item)) {
            return null;
        }
        SmClaimFileCombVO combVO = new SmClaimFileCombVO();
        combVO.setFileRequire(Objects.equals(item.getValue().apply(validatorParams), 1));
        combVO.setExampleFileUrlList(ZaClaimFileType.getByCode(item.getKey()).getExampleFileUrlList());
        combVO.setFileTypeCode(item.getKey());
        combVO.setExampleFileUrlDescription(ZaClaimFileType.getByCode(item.getKey()).getExampleFileUrlDescription());
        combVO.setFileTypeName(ZaClaimFileType.getByCode(item.getKey()).getName());
        return combVO;
    }

    /**
     * 保存用户理赔文件
     *
     * @param claimId
     * @param session
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWxClaimFileByClaimId(int claimId, WxClaimFileCombDTO dto, WxSessionVO session) {

        WxClaimDetailVO claimDetailVO = claimMapper.getSmClaimById(claimId);

        // 触发上传理赔文件流程
        ProgressDTO expressDTO = new ProgressDTO();
        expressDTO.setClaimId(claimId);
        expressDTO.setSName(claimDetailVO.getClaimResult());
        expressDTO.setSCode(claimDetailVO.getClaimState());
        expressDTO.setOName("提交资料");
        expressDTO.setOCode("dataSubmitted");
        expressDTO.setCreateBy(dto.getUserName());
        //总部人员（包含区域岗）负责的理赔案件，提交理赔资料后，流程自动到总部审核阶段，跳过机构审核阶段
        if (Objects.equals(
                expressDTO.getSCode(),
                ClaimWorkflow.STEP_DATA_PREPARE
        ) && Objects.equals(expressDTO.getOCode(), ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED)) {
            AuthUserVO currUser = userService.getAuthUserByUserId(session.getUserId());
            //当前登录用户不存在
            if (Objects.isNull(currUser)) {
                throw new BizException(ExcptEnum.DATA_ERROR_801304);
            }
            AuthUserVO orgHead = userService.getChannelPCOByHrOrgId(currUser.getHrOrgId());
            //查询机构对接人
            if (Objects.isNull(orgHead)) {
                expressDTO.setOCode(ClaimWorkflow.JUMP_TO_DATA_CHECK_BY_SAFES_CENTER);
                expressDTO.setOName("跳转进度至总部审核阶段");
            }
        }
        saveProgress(expressDTO);

        List<SmClaimFileUnitDTO> fileUnitDTOS = new ArrayList<>();
        dto.getFileCombs().stream()
                .forEach(fc -> fc.getNewFlagFileList().forEach(newFlagFileForm -> {
                    String url = newFlagFileForm.getFileUrl();
                    SmClaimFileUnitDTO fileUnitDTO = new SmClaimFileUnitDTO();
                    fileUnitDTO.setClaimId(claimId);
                    fileUnitDTO.setFileTypeCode(fc.getFileTypeCode());
                    fileUnitDTO.setFileTypeName(fc.getFileTypeName());
                    fileUnitDTO.setNewFlag(newFlagFileForm.getNewFlag());
                    if (UrlUtils.isAbsoluteUrl(url)) {
                        //去掉path 中的 第一个/
                        String substring = URI.create(url).getPath().substring(1);
                        fileUnitDTO.setFileUrl(substring);
                    } else {
                        fileUnitDTO.setFileUrl(url);
                    }

                    fileUnitDTOS.add(fileUnitDTO);
                }));

        mapper.deleteClaimFiles(claimId);
        if (!fileUnitDTOS.isEmpty()) {
            mapper.insertClaimFiles(fileUnitDTOS);
        }
    }


    @Override
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimFIleIdList(int claimId, List<Integer> cfIdList) {
        return getCombineFileByFileIds(claimId, cfIdList);
    }


    /**
     * 保存理赔流程
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = {MSBizNormalException.class})
    public void saveProgress(ProgressDTO dto) {

        WxClaimDetailVO claimDetail = mapper.getSmClaimById(dto.getClaimId());
        //添加案件流程节点事件触发器 add by zhangjian 2024-10-29
        busEngine.publish(new ClaimProcessNodeChangeEvent(dto.getClaimId()));

        if (Objects.equals(dto.getOCode(), ClaimWorkflow.MANNUAL_JUMP_TO_ZA_STEP_PAY)) {
            handleManualStepToPay(claimDetail, dto);
            return;
        }

        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY) && !StringUtils.isEmpty(HttpRequestUtil.getUserId())) {
            throw new BizException(ExcptEnum.CLAIM_ZA_STEP_TO_PAY);
        }

        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY) && Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CLAIM_UPDATE_ZA_STATUS)) {
            log.info("刷新众安节点状态-{}", JSONObject.toJSONString(dto));
            triggerZaStatus(dto);
            return;
        }

        log.info("ProgressDTO={}", JSON.toJSON(dto));
        // 如果页面不输入时间 创建时间取当前时间
        if (dto.getOTime() == null) {
            dto.setOTime(new Date());
        } else {
            dto.setOTime(DateUtil.getBeginOfDay(dto.getOTime()));
        }
        // 保存当前操作步骤
        dto.setSettlement(claimDetail.getSettlement());
        ClaimWorkflow.Step nextStep = workflow.goToNextStep(dto.getSCode(), dto.getOCode());

        if (Objects.isNull(nextStep)) {
            throw new MSBizNormalException("-1", "不可操作该流程，下一步选择错误");
        }


        boolean isSendCompany = Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_CHECK_BY_SAFES_CENTER);

        //当前节点不为保司审核，插入节点
        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_TO_PAY) || ClaimWorkflow.END_STEPS.contains(dto.getOCode())) {
            //更新选择的节点
            mapper.updateZaProgress(dto.getClaimId(), dto.getSCode(), dto);
        } else {
            mapper.insertProgress(dto);
        }
        signFileApprove(dto.getClaimId(), claimDetail.getClaimState());

        if (!isSendCompany) {
            // 管理后台理操作已邮寄资料  邮寄资料日期
            if (dto.getOTime() != null && Objects.equals(dto.getOCode(), ClaimWorkflow.STEP_DATA_HAS_POST_EXPRESS)) {
                updateExpressTime(dto.getClaimId(), CommonUtil.getStartTimeOfDay(dto.getOTime()));
            }

            // 微信理赔填写邮寄资料  邮寄资料日期
            if (dto.getExpressTime() != null) {
                updateExpressTime(dto.getClaimId(), CommonUtil.getStartTimeOfDay(dto.getExpressTime()));
            }

            // 资料审核(渠道pco)需补充资料审批节需要修改下一节点名称为补充资料提交
            if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) && Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)) {
                nextStep.setSName("补充资料提交");
            }

            // 更新理赔状态
            int claimId = dto.getClaimId();
            // 如果下一步骤结案  自动结案
            if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_FINISH)) {
                ProgressDTO pDto = new ProgressDTO();
                pDto.setClaimId(claimId);
                convertStepToDTO(nextStep, pDto);
                pDto.setSettlement(claimDetail.getSettlement());
                pDto.setCreateBy(dto.getCreateBy());
                dto.setEvaluationStatus(0);
                mapper.updateFinishInfo(claimId, dto.getCreateBy(), dto);
                mapper.insertProgress(pDto);
            }
            // 更新邮寄资料时间/结案时间/邮寄纸质资料时间 当前环节开始时间/ 保险业务中心审核的次数
            mapper.updateClaimStatus(
                    claimId
                    , dto.getOCode()
                    , dto.getCreateBy()
                    , dto.getOTime()
                    , nextStep
                    , claimHastenProcessor.calHastenTime(nextStep.getSCode(), claimId)
            );
            if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_TO_PAY) ||
                    Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) ||
                    Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE2) ||
                    Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE3)
            ) {
                claimHastenProcessor.updateExpectDays(nextStep.getSCode(), claimId);
            }

            //提交邮寄
            addExpressToZa(dto);
            //第一次从机构审核跳过保司审核
            jumpHeadQuarterFirstTime(dto, claimDetail, nextStep);
            // 补充资料提醒/邮件资料提醒微信提醒客户经理
            // 理赔赔付或者拒赔微信提醒提醒客户经理
            // 提交资料提醒机构对接人
            // 资料审核提醒保险业务中心理赔专员
            try{
                if (Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_POST_EXPRESS)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_GUIDED)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.FINISH_STATE_PAYED)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.FINISH_STATE_PAYREJECTED)
                        || Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_DATA_SUBMITTED)
                        || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)
                        || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE)
                        || Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_CLAIM_CANCEL_REPORT_CODE)
                ) {
                    log.info("发送理赔推送 bean={}", JSON.toJSONString(dto));
                    String otherExplain = null;
                    if (!StringUtils.isEmpty(dto.getDataJson())) {
                        otherExplain = JSON.parseObject(dto.getDataJson()).getString("otherExplain");
                    }
                    SpringFactoryUtil.getBean(EventBusEngine.class).post(new WxClaimNotifyEvent(
                            dto.getClaimId(),
                            dto.getOCode(),
                            null,
                            otherExplain
                    ));
                }
            } catch(Exception e) {
                log.info("众安理赔，发送理赔推送信息失败");
            }
        } else {
            // 处理众安远程接口调用
            reportToZa(dto, claimDetail, nextStep);
        }


    }

    private void handleManualStepToPay(WxClaimDetailVO claimDetail, ProgressDTO dto) {

        SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(dto.getClaimId());
        if (Objects.isNull(reimbursementZa) || StringUtils.isEmpty(reimbursementZa.getZaReportNo())) {
            throw new BizException("", "该案件未向众安报案，不可进行此操作");
        }
        if (Objects.equals(claimDetail.getClaimState(), ClaimWorkflow.STEP_FINISH)) {
            return;
        }
        //更新为保司节点
        claimMapper.manualUpdateCurrentNode(dto.getClaimId());
        //插入流程
        claimMapper.insertProgress(dto);
        //先默认插入一条保司审核的审批节点
        ProgressDTO progressDTO = new ProgressDTO();
        progressDTO.setClaimId(claimDetail.getId());
        progressDTO.setSCode(ClaimWorkflow.STEP_TO_PAY);
        progressDTO.setSName("保司核赔");
        progressDTO.setDataJson("{}");
        progressDTO.setCreateBy(dto.getCreateBy());
        progressDTO.setSettlement(dto.getSettlement());
        mapper.insertProgress(progressDTO);
    }

    private void triggerZaStatus(ProgressDTO dto) {
        SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(dto.getClaimId());
        ZaSearchRequest request = new ZaSearchRequest();
        request.setReportNo(reimbursementZa.getZaReportNo());
        request.setSource(zaApiProperties.getClaimCode());
        ZaClaimQueryResponse claimQueryResponse = zaApiService.claimSearch(new ZaSearchRequestWrapper(request));

        ZaReportStatusReceive statusReceive = new ZaReportStatusReceive();
        if (Objects.nonNull(claimQueryResponse)) {
            statusReceive.setTargetStatus(claimQueryResponse.getCurrentStatus());
            statusReceive.setReportNo(claimQueryResponse.getReportNo());
            statusReceive.setReportBizNo(claimQueryResponse.getReportBizNo());
            statusReceive.setFallbackReason(claimQueryResponse.getFallbackReason());
            statusReceive.setFallbackType(claimQueryResponse.getBackType());
            reportStatusProgressMap.get(statusReceive.getTargetStatus()).handlerProcess(statusReceive, claimQueryResponse);
        }

    }

    private void addExpressToZa(ProgressDTO dto) {
        if (Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_POST_EXPRESS)
                && (Objects.equals(dto.getOCode(), ClaimWorkflow.STEP_DATA_HAS_POST_EXPRESS) || Objects.equals(dto.getOCode(), ClaimWorkflow.POST_EXPRESS_INFO))) {
            ZaExpressTask zaExpressTask = new ZaExpressTask();
            SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(dto.getClaimId());
            SmClaimExpressVO expressVO = claimMapper.getLastClaimExpressByClaimId(dto.getClaimId());
            if (Objects.isNull(expressVO)) {
                throw new MSBizNormalException("", "未查询到快递信息");
            }
            zaExpressTask.setReportNo(reimbursementZa.getZaReportNo());
            zaExpressTask.setSource(zaApiProperties.getClaimCode());
            zaExpressTask.setWayBillNo(expressVO.getExpressNo());
            zaApiService.addExpressTask(new ZaClaimExpressTaskWrapper(zaExpressTask));

            //进入保司核赔阶段
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setClaimId(dto.getClaimId());
            progressDTO.setSCode(ClaimWorkflow.STEP_TO_PAY);
            progressDTO.setSName("保司核赔");
            progressDTO.setDataJson("{}");
            mapper.insertProgress(progressDTO);
        }
    }

    private void reportToZa(ProgressDTO dto, WxClaimDetailVO claimDetail, ClaimWorkflow.Step nextStep) {

        boolean isOrgAudit = Objects.equals(dto.getSCode(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
        boolean isSendToHeadQuarter = Objects.equals(dto.getOCode(), ClaimWorkflow.OPTION_CODE_CHECK_BY_SAFES_CENTER);

        if (isOrgAudit && isSendToHeadQuarter) {
            SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(dto.getClaimId());
            SmOrderInsured insured = insuredMapper.selectByPrimaryKeyMustExists(claimDetail.getInsuredId());
            List<SmClaimFileUnitVO> fileUnitVOS = null;
            List<SmClaimFileUnitVO> zaFileUnitVOS = claimMapper.listSmClaimFileUnitByClaimId(dto.getClaimId());
            if (CollectionUtils.isNotEmpty(zaFileUnitVOS)) {
                distinctService.uploadToZa(zaFileUnitVOS);
            }
            if (StrUtil.isBlank(reimbursementZa.getZaReportNo())) {
                fileUnitVOS = claimMapper.listSmClaimFileUnitByClaimId(dto.getClaimId());
            } else {
                fileUnitVOS = mapper.listSupplementFileByClaimId(dto.getClaimId(), STEP_TO_PAY);
            }

            if (CollectionUtil.isEmpty(fileUnitVOS)) {
                throw new BizException("", "未发现上传的新文件");
            }

//            List<SmClaimFileUnitVO> zaFileUnitVOS = claimMapper.listSmClaimFileUnitByClaimId(dto.getClaimId());

            SmOrder order = Optional.ofNullable(orderMapper.listSmOrderByOrderIds(Collections.singletonList(insured.getFhOrderId())))
                    .map(list -> list.get(0)).orElseThrow(() -> new BizException("", "未查询到订单信息"));

            //真实的保单号在sm_order_item表里面
            List<SmOrderItem> orderItems = smOrderItemMapper.selectByOrderId(insured.getFhOrderId());
            SmOrderItem orderItem = Optional.ofNullable(orderItems).orElseGet(ArrayList::new).stream()
                    .filter(item -> Objects.equals(item.getPolicyNo(), insured.getPolicyNo()))
                    .findAny().orElse(null);
            SmProductDetailVO product = productMapper.getProductById(order.getProductId());

            //报案时间取向众安首次提交时间
            if (StringUtils.isEmpty(reimbursementZa.getZaReportNo())) {
                reimbursementZa.setZaReportTime(LocalDateTime.now());
            }
            ReportRequest report = ReportRequest.convertToReportRequest(claimDetail, reimbursementZa, insured, fileUnitVOS, product, orderItem);
            report.setSource(zaApiProperties.getClaimCode());
            ZaRequestWrapper<ReportRequest> reportWrapper = new ZaRequestWrapper<>();
            reportWrapper.setReportRequest(report);

            ReportResponse response = zaApiService.claimReport(reportWrapper);

            //更新众安的理赔编码
            claimReimbursementZaMapper.updateZaReportNoByClaimId(reimbursementZa.getClaimId(), response.getReportNo(), reimbursementZa.getZaReportTime());
            //先默认插入一条保司审核的审批节点
            ProgressDTO progressDTO = new ProgressDTO();
            progressDTO.setClaimId(claimDetail.getId());
            progressDTO.setSCode(ClaimWorkflow.STEP_TO_PAY);
            progressDTO.setSName("保司核赔");
            progressDTO.setDataJson("{}");
            mapper.insertProgress(progressDTO);
            //设置当前步骤为保司核赔中
//                    mapper.updateZaProgress(claimDetail.getId(), ClaimWorkflow.STEP_TO_PAY, dto);
            mapper.updateClaimStatus(
                    claimDetail.getId()
                    , dto.getOCode()
                    , dto.getCreateBy()
                    , dto.getOTime()
                    , nextStep
                    , claimHastenProcessor.calHastenTime(nextStep.getSCode(), dto.getClaimId())
            );

        }
    }


    @Override
    public List<String> channel() {
        return Collections.singletonList(EnumClaimProcessType.ZA_API.getCode());
    }

    @Override
    public List<SmClaimFileCombVO> getSmClaimFileCombByClaimId(int claimId) {
        List<SmClaimFileCombVO> result = getCombineFile(claimId);
        handleNewFlag(result, claimId);
        handleAiResult(result, claimId);
        return result;
    }



    public List<ZaBankInfoVO> queryHeadBank(String keyword) {
        if (keyword != null) {
            keyword = keyword.trim();
        }
        return claimBankZaMapper.selectHeadBank(keyword);
    }

    public List<ZaBankInfoVO> queryBranchBank(ZaClaimQuery claimQuery) {
        return claimBankZaMapper.selectBranchBank(claimQuery);
    }

    public List<ZaClaimRegionInfoVO> queryRegionByHeadBankCode(String headBankCode) {
        List<ZaClaimRegionInfoVO> subRegionList = regionZaMapper.queryRegionByBankCode(headBankCode);
        if (CollectionUtils.isEmpty(subRegionList)) {
            return null;
        }
        List<ZaClaimRegionInfoVO> headRegionList = regionZaMapper.queryRegionByIds(
                subRegionList.stream().map(ZaClaimRegionInfoVO::getParentId)
                        .collect(Collectors.toList())
        );

        Map<Integer, List<ZaClaimRegionInfoVO>> pMap = subRegionList.stream().collect(Collectors.groupingBy(
                ZaClaimRegionInfoVO::getParentId));

        headRegionList.forEach(item -> item.setChildren(pMap.get(item.getId())));

        return headRegionList;
    }

    @Autowired
    private TransactionTemplate transactionTemplate;

    public void handlerZaReportStatusCallBack(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        ZaReportStatusReceive claimReport = null;
        try{
            String s = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8.toString());
            String plain = ZaAESUtils.decryptBase64(KEY, s);
            log.info("众安理赔成功回调明文:{}", plain);

            claimReport = jsonMapper.readValue(plain, ZaReportStatusReceive.class);

            if (Objects.isNull(claimReport)) {
                response.getWriter().write(ZaClaimResponse.error("无内容信息").toString());
                return;
            }

            if (claimCompanyStatusZaMapper.existedByMessageId(claimReport.getMessageId())) {
                response.getWriter().write(ZaClaimResponse.ok("成功").toString());
                return;
            }

            SmClaimCompanyStatusZa companyStatusZa = ZaReportStatusReceive.convertToSmClaimCompanyStatusZa(claimReport);
            companyStatusZa.setDataJson(s);

            response.setContentType("application/json;charset=UTF-8");
            if (Objects.isNull(claimReport.getMessageId())) {
                claimCompanyStatusZaMapper.insertSelective(companyStatusZa);
                response.getWriter().write(new JSONObject().fluentPut("code", "10000").fluentPut("message", "messageId缺失").toString());
                return;
            }

            lockTemplate.lock(claimReport.getMessageId(), 5);

            SmClaim smClaim = claimMapper.getByClaimNo(claimReport.getReportBizNo());
            //根据报案号查询当前流程所处审批节点
            if (Objects.isNull(smClaim)) {
                claimCompanyStatusZaMapper.insertSelective(companyStatusZa);
                response.getWriter().write(ZaClaimResponse.error("无理赔案件信息").toString());
                return;
            }

            companyStatusZa.setClaimId(smClaim.getId());
            claimCompanyStatusZaMapper.insertSelective(companyStatusZa);

//            if (!Objects.equals(smClaim.getClaimState(), ClaimWorkflow.STEP_TO_PAY)) {
//                response.getWriter().write(ZaClaimResponse.ok("成功").toString());
//                return;
//            }

            //调用保司接口查找流程赔付金额
            ZaSearchRequest queryReq = new ZaSearchRequest();
            queryReq.setReportNo(claimReport.getReportNo());
            queryReq.setSource(zaApiProperties.getClaimCode());
            ZaClaimQueryResponse claimQueryResponse = zaApiService.claimSearch(new ZaSearchRequestWrapper(queryReq));

//            //分发处理流程
//            if (Objects.isNull(reportStatusProgressMap.get(claimQueryResponse.getCurrentStatus())) && ) {
//                response.getWriter().write(ZaClaimResponse.error("无该案件回退状态处理逻辑").toString());
//                return;
//            }
            ZaReportStatusReceive finalClaimReport = claimReport;
            TxUtil.getInstance(transactionTemplate).doTransactionWithoutResult(
                    () -> {
                        if (!Objects.equals(smClaim.getClaimState(), STEP_TO_PAY) && !Objects.equals(finalClaimReport.getFallbackNode(), "reportAutoBack")) {
                            distinctService.autoPassStepToPayAndUpdate(smClaim);
                            //先默认插入一条保司审核的审批节点
                            ProgressDTO progressDTO = new ProgressDTO();
                            progressDTO.setClaimId(smClaim.getId());
                            progressDTO.setSCode(ClaimWorkflow.STEP_TO_PAY);
                            progressDTO.setSName("保司核赔");
                            progressDTO.setDataJson("{}");
                            claimMapper.insertProgress(progressDTO);
                        }
                        if(reportStatusProgressMap.containsKey(claimQueryResponse.getCurrentStatus())) {
                            reportStatusProgressMap.get(claimQueryResponse.getCurrentStatus()).handlerProcess(finalClaimReport, claimQueryResponse);
                        }
                    }
            );

            response.getWriter().write(ZaClaimResponse.ok("成功").toString());
        } catch(Exception e) {
            log.error("众安理赔回调通知处理失败", e);
            response.getWriter().write(new JSONObject().fluentPut("code", "10000").fluentPut("message", e.getMessage()).toString());
        } finally {
            if (Objects.nonNull(claimReport)) {
                lockTemplate.unLock(claimReport.getMessageId());
            }
        }

    }

    @Override
    public Boolean checkFiles(int claimId, List<SmClaimFileCombDTO> fileCombDTOS) {
        ValidatorParams validatorParams = constructValidatorParams(claimId);
        int age = IdcardUtils.getAgeByIdCard(validatorParams.getInsuredIdNumber());
        if (Objects.isNull(validatorParams.getInsuredIdNumber()) || age > 18) {
            if (fileCombDTOS.stream().anyMatch(i -> Objects.equals(i.getFileTypeCode(), ZaClaimFileType.INSURANCE_IDENTIFY_CERT.getCode()))) {
                long count = fileCombDTOS.stream()
                        .filter(i -> Objects.equals(i.getFileTypeCode(), ZaClaimFileType.INSURANCE_IDENTIFY_CERT.getCode()))
                        .map(SmClaimFileCombDTO::getNewFlagFileList)
                        .map(List::size)
                        .findAny()
                        .orElse(0);
                if (count < 2L) {
                    throw new BizException("", "被保险人身份证明至少上传两张");
                }
            }
        }
        return Boolean.TRUE;
    }

    public ZaClaimStatusDetailVO searchZaClaim(int claimId) {
        ZaSearchRequest request = new ZaSearchRequest();
        SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(claimId);
        if (Objects.isNull(reimbursementZa) || StringUtils.isEmpty(reimbursementZa.getZaReportNo())) {
            throw new BizException("", "理赔信息不存在");
        }
        request.setReportNo(reimbursementZa.getZaReportNo());
        request.setSource(zaApiProperties.getClaimCode());

        ZaClaimQueryResponse claimQueryResponse = zaApiService.claimSearch(new ZaSearchRequestWrapper(request));
        if(reportStatusProgressMap.containsKey(claimQueryResponse.getCurrentStatus())) {
            return reportStatusProgressMap.get(claimQueryResponse.getCurrentStatus()).explainZaStatus(claimQueryResponse);
        }
        ZaClaimStatusDetailVO statusDetailVO = new ZaClaimStatusDetailVO();
        statusDetailVO.setStatusMemo("");
        statusDetailVO.setStatusName(EnumZaClaimStatus.toNameByCode(claimQueryResponse.getCurrentStatus()));
        return statusDetailVO;
    }

    @Override
    public void callbackWhenReport(WxClaimDTO dto) {
        Integer claimId = dto.getId();
        SmClaimReimbursementZa reimbursementZa = claimReimbursementZaMapper.selectByClaimId(claimId);

        if (Objects.isNull(reimbursementZa)) {
            reimbursementZa = new SmClaimReimbursementZa();

            EnumZaClaimRiskTypeMap zaClaimRiskTypeMap = EnumZaClaimRiskTypeMap.findByRiskType(dto.getRiskType());
            reimbursementZa.setAccidentType(zaClaimRiskTypeMap.getAccidentType());
            reimbursementZa.setReimbursementType(String.join(",", zaClaimRiskTypeMap.getRelationReimbursementType()));
            reimbursementZa.setZaReportTime(LocalDateTime.now());
            reimbursementZa.setClaimId(claimId);
            reimbursementZa.setCreateBy(HttpRequestUtil.getUserId());
            claimReimbursementZaMapper.insert(reimbursementZa);
        }

        if (StrUtil.isNotBlank(reimbursementZa.getZaReportNo())) {
            return;
        }

        WxClaimDetailVO claimDetail = mapper.getSmClaimById(claimId);
        SmOrderInsured insured = insuredMapper.selectByPrimaryKeyMustExists(claimDetail.getInsuredId());

        SmOrder order = Optional.ofNullable(orderMapper.listSmOrderByOrderIds(Collections.singletonList(insured.getFhOrderId())))
                .map(list -> list.get(0)).orElseThrow(() -> new BizException("", "未查询到订单信息"));

        //真实的保单号在sm_order_item表里面
        List<SmOrderItem> orderItems = smOrderItemMapper.selectByOrderId(insured.getFhOrderId());
        SmOrderItem orderItem = Optional.ofNullable(orderItems)
                .orElseGet(ArrayList::new)
                .stream()
                .filter(item -> Objects.equals(item.getPolicyNo(), insured.getPolicyNo()))
                .findAny().orElse(null);
        SmProductDetailVO product = productMapper.getProductById(order.getProductId());
        ReportRequest report = ReportRequest.convertFirstToReportRequest(claimDetail, reimbursementZa, insured, product, orderItem);
        report.setSource(zaApiProperties.getClaimCode());
        ZaRequestWrapper<ReportRequest> reportWrapper = new ZaRequestWrapper<>();
        reportWrapper.setReportRequest(report);
        ReportResponse response = zaApiService.claimReport(reportWrapper);
        log.info("众安返回信息-{}", JSON.toJSONString(response));
        reimbursementZa.setZaReportNo(response.getReportNo());
        claimReimbursementZaMapper.updateByPrimaryKeySelective(reimbursementZa);

    }

    @Override
    public List<SmClaimFileUnitVO> listDefaultFile() {
        return Arrays.stream(ZaClaimFileType.values()).map(
                x->{
                    SmClaimFileUnitVO fileUnitVO = new SmClaimFileUnitVO();
                    fileUnitVO.setFileTypeCode(x.getCode());
                    fileUnitVO.setFileTypeName(x.getName());
                    return fileUnitVO;
                }
        ).collect(Collectors.toList());
    }

    public void validateZaAuth() {
        ZaSearchRequest request = new ZaSearchRequest();
        request.setReportNo("1111");
        request.setSource(zaApiProperties.getClaimCode());

        ZaClaimQueryResponse claimQueryResponse = zaApiService.claimSearch(new ZaSearchRequestWrapper(request));

    }

    public void validateZaReport() {
        ZaSearchRequest request = new ZaSearchRequest();
        request.setReportNo("1111");
        request.setSource(zaApiProperties.getClaimCode());

        zaApiService.claimReport(new ZaRequestWrapper());

    }

    public void validateZaTask() {
        ZaSearchRequest request = new ZaSearchRequest();
        request.setReportNo("1111");
        request.setSource(zaApiProperties.getClaimCode());

        zaApiService.addExpressTask(new ZaClaimExpressTaskWrapper());
    }

    public static void main(String[] args) {
        String result = ZaAESUtils.decryptBase64(
                KEY,
                "kO7CN9oF2y8wQFIierb4NT126Yl/Cjyfn3xC1lNG7f3bijye2YDPYxilUoofS9eKX9wscIHUIZESB4pfV6CUYEWCKxL19Fz7w1xL6wF1H6RngHckkPdN+S1pQZn6ZReeMZOySyh4iWxIjFmq1R3VqdQvXhtsV/lScAFtdkkNwy6HUUuIQme/yY+ODb9W5f8BUKR/nR9NlgHmd5FPBs18eWJ+IJcHe/ou2RKzrzqyYU98gAEZ+uQjC0sdD2IYxO+/Vs2rJxtXvpS8qBRliDjkxUd+r1f/YJ+WqASE6QPwcDo8aNjV/yKudrzHgeMDeljxEd4wsolBPKTx1QpxniMp/noxRv0rno+WbuHqKXKtagz6HoNwhx+bawS3imQPN2GOm6cte/76qq60zJe3OTFhmX2mSbEEiDNX8Ly9wXYINcdHA40HKl2BgvDVc/72yO6Q35gRycZDZ41BBlfFeIwzQn4e3HtXZ/IVm4rqP08AKniZ1LA4ld24B+KHzofZs5yvszFa39Zkk3juKoThveKrix/DyBbpcBJNJNlupO3IfTfKjsjH3KFaxpVF8SKL3rSD3rc5z8VwXCZwT5lvvhJ1WtnAeJ1HuQKGsXffKvp/z7+BkOC8YFaqaapCPXZlzQKYUUChmU1SWhwTJOpJRNyHsZZFTCdh9+LZ3wvxwjJpgUJYl9az8Z6YurbGLAC8lGWFkRdnH6qjhMOiCLkJZEyJYtrHNEesWPEZFAYLNJ7D1WSRY7vpnoCZNCAfSVV+lKOdpZ+rNdu9ZTZHfXkl3Am9VLYuaV0+SZdn+7Ta/5d7PCIwffDvWzORCiQgWilLTORdENBMKVroN4lmmDsrPBB9X8yTqd2d850XC33NziqgUZi7huk3ny6foVbjfSaHe2yY0QnxFxN1e3A0VYjdT+Vx6mvXfOU8HbIISwcWSzZrW0DqhMvNhmxcUjfC4WbXH2FISqwg3Sn133AGkvyE5fIbywEM5Nf9r+45cw0n6LpkF71Atq+HQeKC0Wv/EFu88WGP+fEP8d/Dwxb0/lk9sHgXkn14I46S0pN1ma5Fvk5AqMgyfZhQm5n13x2LhVavI6ruR362UVB2WcVDB8j4kAg/YYJEwfmud8W5L66FQcjs2tsVakuncLmN1BQys5D/1U0fV9wP8G/HhNv2Vn95oykLcfv1Fcvz71tkcG2JpDmVucm0o904WFRfKv6QIbXgvkxmX578vHgnhFU4owfTM6s/8OEy4CLPm2uJHKrlJUoChSRBFz+wBq0/DLv82p6+wFOeANdasDIPnXQ9JmWR8p+zrDT8Os4AwLKxpznAzzACkh5XVHX4TbKkQcORo1NBA+SASzcFauI7KjRCjfSwmK25mibfZYMkzVtksNz2JH3qkxWs8nF/STt0+nRmQGWDL12JRSj8Bor8lmAnz4TatT+2JQ=="
        );
        JSONObject object = JSONObject.parseObject(result);
        object.put("messageId", object.get("messageId") + "_bak111");
        System.out.println(ZaAESUtils.encryptBase64(
                KEY,
                object.toJSONString()
        ));
        ZaAESUtils.encryptBase64(
                KEY,
                object.toJSONString()
        );
    }


    public void manualUpdateFile(Integer claimId) {
        if (Objects.equals("ZHNX34439", HttpRequestUtil.getUserId())) {
            claimMapper.repairZaClaimFileData(claimId);
            claimMapper.repairClaimFileData(claimId);
            claimMapper.manualUpdateCurrentNode(claimId);
        }
    }

    public void manualUpdateProgress(Integer claimId, Integer progressId) {
        if (Objects.equals("ZHNX34439", HttpRequestUtil.getUserId())) {
            claimMapper.manualUpdateProgress(claimId, progressId);
        }
    }

    public void manualDeleteProgress(Integer claimId, List<Integer> progressIds) {
        if (Objects.equals("ZHNX34439", HttpRequestUtil.getUserId())) {
            claimMapper.manualDeleteNode(claimId, progressIds);
        }
    }

    public void manualInsertProgress(ProgressDTO dto) {
        if (Objects.equals("ZHNX34439", HttpRequestUtil.getUserId())) {
            claimMapper.insertProgress(dto);
        }
    }

    public void manualUpdateNode(Integer claimId) {
        if (Objects.equals("ZHNX34439", HttpRequestUtil.getUserId())) {
            claimMapper.manualUpdateCurrentNode(claimId);
        }
    }


    @Override
    public String deFirstRow(String code) {
        throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "暂不支持的操作");
    }

    @Override
    public String deFirstColumn(String code) {
        throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "暂不支持的操作");
    }
}
