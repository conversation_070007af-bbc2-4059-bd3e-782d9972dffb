package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.IdcardUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.exception.MSException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.config.ProductProperties;
import com.cfpamf.ms.insur.admin.config.ProductSpecialRuleProperties;
import com.cfpamf.ms.insur.admin.constant.AmConstants;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.event.*;
import com.cfpamf.ms.insur.admin.external.*;
import com.cfpamf.ms.insur.admin.external.common.model.OrderPreAiCheckResp;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhOrderInfo;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhProposer;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.convertor.SmOrderCarConvert;
import com.cfpamf.ms.insur.admin.pojo.convertor.SmOrderDutyConvert;
import com.cfpamf.ms.insur.admin.pojo.convertor.SmOrderHouseConvert;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCommonSettingVO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderInsuredBuyDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmChannelCallback;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.SmProductFormBuyLimit;
import com.cfpamf.ms.insur.admin.pojo.po.order.*;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.renewal.service.OrderRenewalRecordService;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.util.OrderRiskDataUtils;
import com.cfpamf.ms.insur.admin.validation.SmOfflineOrderValidation;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.pojo.dto.ValidOrderParam;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.ExceptionUtils;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.facade.api.OperationFacade;
import com.cfpamf.ms.insur.weixin.dao.safes.EndorMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.Endor;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxFormFieldOptionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.cfpamf.ms.insur.weixin.service.WxHomeProductService;
import com.cfpamf.ms.pay.facade.config.PayFacadeConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.admin.constant.SmConstants.PRODUCT_FORM_FIELD_TYPE_IDTYPE;

/**
 * <AUTHOR> 2020/3/18 09:23
 */
@Slf4j
public abstract class AbstractSmOrderService implements SmOrderService {

    public static final int YEAR_NODE = 2021;
    protected static DateTimeFormatter FMT_PARSE = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    protected static DateTimeFormatter FMT_SUB = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    protected BusinessMonitorService monitorService;
    /**
     * 产品service
     */
    @Autowired
    protected SmProductService productService;
    /**
     * 业务token service
     */
    @Autowired
    protected BusinessTokenService tokenService;

    /**
     * 客户中心 service
     */
    @Autowired
    protected CustomerCenterService ccService;

    /**
     * 产品提成 service
     */
    @Autowired
    protected SmCommissionMapper cmsMapper;

    /**
     * 订单mapper
     */
    @Autowired
    protected SmOrderMapper orderMapper;

    /**
     * 订单mapper
     */
    @Autowired
    protected SmOrderItemMapper orderItemMapper;
    /**
     * 订单mapper
     */
    @Autowired
    protected SmProductFormBuyLimitMapper formBuyLimitMapper;

    /**
     * 用户mapper
     */
    @Autowired
    protected AuthUserMapper userMapper;

    @Autowired
    protected OrderRenewalRecordService orderRenewalRecordService;
    @Autowired
    protected OrderCoreService orderCoreService;
    @Autowired
    protected OperationFacade operationFacade;

    @Autowired
    private SmChannelCallbackMapper channelCallbackMapper;

    /**
     * 事件工作流
     */
    @Lazy
    @Autowired
    protected EventBusEngine busEngine;

    @Autowired
    protected SmOrderCarMapper carMapper;

    @Autowired
    protected SmOrderHouseMapper houseMapper;

    @Autowired
    protected SmOrderGroupNotifyService smOrderGroupNotifyService;

    @Autowired
    protected SmCommonSettingService commonSettingService;

    @Autowired
    protected SmOrderRenewBindService smOrderRenewBindService;

    @Autowired
    SmXjxhService smXjxhService;
    @Autowired
    SmOrderPolicyMapper smOrderPolicyMapper;
    @Autowired
    protected ChOrderPersonalNotifyService chOrderPersonalNotifyService;

    @Autowired
    protected SmOrderRiskDutyMapper riskDutyMapper;
    @Autowired
    protected SmProductMapper productMapper;
    @Autowired
    protected SmOrderPolicyService smOrderPolicyService;

    @Autowired
    protected RenewalManagerService renewalManagerService;

    @Autowired
    protected SmOrderInsuredMapper insuredMapper;


    /**
     * token锁时间
     */
    @Value("${author.token.lockTime}")
    protected long tokenLockTime;

    /**
     * 投保人手机号码限制
     */
    @Value("${order.phoneLimit:10}")
    protected int phoneLimit;


    @Autowired
    ProductProperties properties;

    @Autowired
    ProductSpecialRuleProperties productSpecialRuleProperties;

    @Autowired
    protected EndorMapper paymentMapper;

    @Autowired
    protected PayFacadeConfigProperties payFacadeConfigProperties;

    protected abstract String channel();

    protected abstract ChannelOrderService orderService();


    /**
     * 各渠道自己的提交方法
     *
     * @param userUniqueId
     * @param planVo
     * @param source
     * @param change
     * @return
     */
    protected OrderSubmitResponse channelSubmitOrder(String userUniqueId, SmPlanVO planVo, OrderSubmitRequest source, OrderSubmitRequest change) {

        return orderService().submitChannelOrder(change);
    }


    /**
     * 各渠道自己的提交方法
     *
     * @return
     */
    protected OrderPreAiCheckResp channelSubmitOrderAICheck(OrderSubmitRequest request, String localProductId) {

        return orderService().aiCheck(request, localProductId);
    }


    public List<AICheckQueryResponse> queryAiCheckResult(String channel, String orderId, String questionnaireId) {

        AICheckQueryRequest request = new AICheckQueryRequest();
        request.setOrderId(orderId);
        request.setQuestionnaireId(questionnaireId);
        return getExternalThirdPartyOrderService(channel)
                .aiCheckQuery(request);
    }


    /**
     * 提交订单成功之后
     *
     * @param userUniqueId
     * @param planVo
     * @param change
     * @param submitResponse 渠道返回值
     * @return
     */
    protected OrderSubmitResponse submitSuccessAfter(
            String userUniqueId, SmPlanVO planVo, OrderSubmitRequest dto, OrderSubmitRequest change,
            OrderSubmitResponse submitResponse) {
        // 订单创建成功
        // 泛华续保 时间与泛华订单修正
        if (submitResponse.getReturnMap() != null) {
            adjustRenewOrderResp(submitResponse, dto);
        }

        //s48 绑卡/自动续保
        smOrderRenewBindService.addFhOrderIdToRenewBindInfo(submitResponse.getOrderId(), change);
        // 订单入库
        saveOrderInfo(dto, submitResponse, planVo);

        // 续保订单更新旧的订单续保订单信息
        if (!StringUtils.isEmpty(dto.getPreOrderId()) && dto.isRenew()) {
            log.info("订单号为{}的订单是{}订单的续保订单,", submitResponse.getOrderId(), dto.getPreOrderId());
            orderRenewalRecordService.create(dto.getPreOrderId(), submitResponse.getOrderId());
        }
        // 订单修改  删除客户中心之前下的订单
        else if (!StringUtils.isEmpty(dto.getPreOrderId())) {
            orderMapper.deleteOrder(dto.getPreOrderId());
        }
        return submitResponse;
    }

    /**
     * 保存小额保险订单
     * <p>
     * 订单创建成功noticeCode=0, dto.token失效不能再次使用
     * 否认创建失败, dto.token可以再次使用
     *
     * @param userUniqueId 第三方用户唯一Id
     * @param dto          创建订单dto
     * @return
     */
    @Override
    public OrderSubmitResponse submitOrder(String userUniqueId, OrderSubmitRequest dto) {
        // 校验证件号码与生日是否匹配 TODO 动态规则检测所有字段
        validOrderData(dto);
        // 检验token 防止超时
        boolean validToken = tokenService.validateBusinessToken(userUniqueId, SmConstants.SM_ORDER_BUSINESS_CODE, dto.getToken());
        if (!validToken) {
//            throw new BizException(ExcptEnum.OPERATION_TIMEOUT_504005);
        }
        // 锁token  防订单重复提交
        boolean lockSuccess = tokenService.lockBusinessToken(dto.getToken(), tokenLockTime);
        if (!lockSuccess) {
//            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }
        try {
            // 判断投保人和被保人都实名认证
            certifyRealName(dto);
            //校验投保人手机号
            checkProposerPhone(dto.getProposerInfo());
            // 20201216 重新设置推荐人
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getBizCode())) {
                String jobNumberByBizCode = userMapper.getMainJobNumberByBizCode(dto.getBizCode());
                if (StringUtils.isEmpty(jobNumberByBizCode)) {
                    throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "推荐码不存在！");
                }
                dto.getProductInfo().setRecommendId(jobNumberByBizCode);
            }

            ValidOrderParam validOrderParam = new ValidOrderParam();
            validOrderParam.setRequest(dto);
            SmPlanVO planVo = productService.getPlanById(dto.getPlanId());
            //验证产品是否可售
            //productService.validProductState(planVo.getProductId(),dto.getPreOrderId());

            validOrderParam.setPlanVO(planVo);
            // 检验产品是否查过最大购买份数限制
            checkSubmitOrderInsuredProductBuyLimit(dto, planVo.getProductId(), planVo.getId(), planVo.getBuyLimit());
            // 修改订单产品信息为第三方产品Id, 订单提交时间

            setOrderRequestDefaultParams(dto, planVo);

            //查询历史保单信息 2021-03-26 续保管理添加
            setOldPolicyInfo(dto);

            //数据库保存订单被保人 投保人手机号与第三方调用不是一样
            // 修改提交给泛华的手机号码为客户经理的手机号码
            OrderSubmitRequest submitDTO = dto;
            //20201119 所有渠道去掉修改手机号码的功能
//            if (!Objects.equals(dto.getSubChannel(), EnumOrderSubChannel.CAPP.getCode())
//                    && !StringUtils.isEmpty(dto.getProductInfo().getRecommendId())
//                    && !Objects.equals(planVo.getChannel(), EnumChannel.GSC.getCode())) {
//                submitDTO = changeOrderMobilesToCustManagerMobile(dto);
//            }
            //log.info("submitDTO_1={}",submitDTO);
            // 第三方下单接口
            OrderSubmitResponse submitResponse = channelSubmitOrder(userUniqueId, planVo, dto, submitDTO);
            // 订单创建失败
            if (!submitResponse.isSuccess()) {
                log.warn("创建订单失败 resp=[{}]", JSON.toJSONString(submitResponse));
                // 解锁token可再次使用
                tokenService.unlockBusinessToken(dto.getToken());
                throw new BizException(submitResponse.getNoticeCode(), submitResponse.getNoticeMsg());
            }

            FhOrderInfo orderInfo = dto.getOrderInfo();
            if(orderInfo!=null){
                orderInfo.setApplyTime(new Date());
            }
            submitSuccessAfter(userUniqueId, planVo, dto, submitDTO, submitResponse);
            // 解锁token
            tokenService.unlockBusinessToken(dto.getToken());
            // 正常下单后token不能再用
            tokenService.deleteBusinessToken(userUniqueId, SmConstants.SM_ORDER_BUSINESS_CODE, dto.getToken());

            busEngine.publish(new OrderCustomerChangeEvent(submitResponse.getOrderId()));
            return submitResponse;
        }
        // 业务异常抛出提示
        catch (BizException | MSException e) {
            tokenService.unlockBusinessToken(dto.getToken());
            throw e;
        }
        // 未知异常包装
        catch (Exception e) {
            log.error("订单创建失败", e);
            tokenService.unlockBusinessToken(dto.getToken());
            BizException bizError = ExceptionUtils.getBizError(e);
            if (bizError != null) {
                throw bizError;
            }
            throw new BizException(ExcptEnum.ORDER_ERROR_501010, e);
        }
    }

    /**
     * 投保人手机号限制
     * 原因：合规要求，1个手机号最多被10个投保人使用，客户经理的手机号号也需要做限制；现有逻辑对客户经理没有做限制，现在需要增加上这个限制；
     * 今年（2021年）统计区间为2021年9月1日~2021年12月30日，之后的年份按照当年1月1日~12月30日统计
     * 线下单不统计：补单-批量导入的订单不计入
     *
     * @param proposerInfo
     */
    public void checkProposerPhone(FhProposer proposerInfo) {
        //今年（2021年）统计区间为2021年9月1日~2021年12月30日，之后的年份按照当年1月1日~12月30日统计；
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;
        if (now.getYear() == YEAR_NODE) {
            startTime = LocalDateTime.of(2021, 9, 1, 0, 0, 0);
        } else {
            startTime = LocalDateTime.of(LocalDate.from(now.with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        }

        //统计当前手机号码 除去当前证件号码还有多个个证件号
        int countApplicantByPhone = orderMapper.countApplicantByPhone(
                proposerInfo.getCellPhone(),
                proposerInfo.getIdNumber(),
                startTime);
        if (countApplicantByPhone >= phoneLimit) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该手机号已经被" + countApplicantByPhone + "个不同的投保人使用，请核实录入的手机号是否正确或者更换手机号");
        }
    }

    /**
     * TODO 暂存保险订单
     *
     * @param userId 第三方用户唯一Id
     * @param order  创建订单dto
     * @return
     */
    @Override
    public OrderSubmitResponse holdOrder(String userId, OrderSubmitRequest order) {

        SmPlanVO planVo = productService.getPlanById(order.getPlanId());
        validOrder(userId, order);
        boolean lockSuccess = tokenService.lockBusinessToken(order.getToken(), tokenLockTime);
        if (!lockSuccess) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }
        try {
            saveOrderInfo(order, null, planVo);
            return null;
        } catch (Exception e) {
            BizException bizError = ExceptionUtils.getBizError(e);
            if (bizError != null) {
                throw bizError;
            }
            throw new BizException(ExcptEnum.ORDER_ERROR_501010, e);
        } finally {
            /**
             * 释放锁
             */
            tokenService.unlockBusinessToken(order.getToken());
        }
    }

    /**
     * 统一验证订单
     *
     * @return
     */
    private void validOrder(String userId, OrderSubmitRequest order) {
        /**
         * 1.鉴权
         */
        boolean validToken = tokenService.validateBusinessToken(userId, SmConstants.SM_ORDER_BUSINESS_CODE, order.getToken());
        if (!validToken) {
            throw new BizException(ExcptEnum.OPERATION_TIMEOUT_504005);
        }
        /**
         * 2.统计2020年1月1号以后的数据,判断手机号绑定次数限制
         */
        int countApplicantByPhone = orderMapper.countApplicantByPhone(
                order.getProposerInfo().getCellPhone(),
                order.getProposerInfo().getIdNumber(),
                LocalDateTime.of(2021, 1, 1, 0, 0, 0));
        if (countApplicantByPhone >= phoneLimit) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该手机号已经被" + countApplicantByPhone + "个不同的投保人使用，请核实录入的手机号是否正确或者更换手机号");
        }
        /**
         * 3.推荐人修正
         */
        if (org.apache.commons.lang3.StringUtils.isNotBlank(order.getBizCode())) {
            String jobNumberByBizCode = userMapper.getMainJobNumberByBizCode(order.getBizCode());
            if (StringUtils.isEmpty(jobNumberByBizCode)) {
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "推荐码不存在！");
            }
            order.getProductInfo().setRecommendId(jobNumberByBizCode);
        }
    }

    /**
     * 校验数据是否正确 调用保险公司的核保接口【可能部分保险公司有问题】
     *
     * @param userUniqueId
     * @param dto
     * @return
     */
    @Override
    public OrderSubmitResponse checkOrderData(String userUniqueId, OrderSubmitRequest dto) {
        validOrderData(dto);
        // 判断投保人和被保人都实名认证
        certifyRealName(dto);

        ValidOrderParam validOrderParam = new ValidOrderParam();
        validOrderParam.setRequest(dto);
        SmPlanVO planVo = productService.getPlanById(dto.getPlanId());
        //验证产品是否可售
        //productService.validProductState(planVo.getProductId(),dto.getPreOrderId());

        validOrderParam.setPlanVO(planVo);
        // 检验产品是否查过最大购买份数限制
        checkSubmitOrderInsuredProductBuyLimit(dto, planVo.getProductId(), planVo.getPlanId(), planVo.getBuyLimit());
        // 修改订单产品信息为第三方产品Id, 订单提交时间

        setOrderRequestDefaultParams(dto, planVo);

        //查询历史保单信息 2021-03-26 续保管理添加
        setOldPolicyInfo(dto);

        //数据库保存订单被保人 投保人手机号与第三方调用不是一样
        // 修改提交给泛华的手机号码为客户经理的手机号码
        OrderSubmitRequest submitDTO = dto;
        //20201119 所有渠道都会传客户自己的手机号码
//        if (!Objects.equals(dto.getSubChannel(), EnumOrderSubChannel.CAPP.getCode())
//                && !StringUtils.isEmpty(dto.getProductInfo().getRecommendId())) {
//            submitDTO = changeOrderMobilesToCustManagerMobile(dto);
//        }

        // 第三方下单接口
        //log.info("submitDTO_1={}",submitDTO);
        OrderSubmitResponse submitResponse = channelSubmitOrder(userUniqueId, planVo, dto, submitDTO);
        //s48 绑卡/自动续保
        smOrderRenewBindService.addFhOrderIdToRenewBindInfo(submitResponse.getOrderId(), submitDTO);

        // 订单创建失败
        if (!submitResponse.isSuccess()) {
            throw new BizException(submitResponse.getNoticeCode(), submitResponse.getNoticeMsg());
        }
        return submitResponse;
    }


    @Override
    public OrderPreAiCheckResp jumpAiCheck(String userUniqueId, OrderSubmitRequest dto, HttpServletResponse response) {
        // 校验证件号码与生日是否匹配 TODO 动态规则检测所有字段
        validOrderData(dto);
        // 检验token 防止超时
        boolean validToken = tokenService.validateBusinessToken(userUniqueId, SmConstants.SM_ORDER_BUSINESS_CODE, dto.getToken());
        if (!validToken) {
            throw new BizException(ExcptEnum.OPERATION_TIMEOUT_504005);
        }
        // 锁token  防订单重复提交
        boolean lockSuccess = tokenService.lockBusinessToken(dto.getToken(), tokenLockTime);
        if (!lockSuccess) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }
        try {
            // 判断投保人和被保人都实名认证
            certifyRealName(dto);

            ValidOrderParam validOrderParam = new ValidOrderParam();
            validOrderParam.setRequest(dto);
            SmPlanVO planVo = productService.getPlanById(dto.getPlanId());

            validOrderParam.setPlanVO(planVo);
            // 检验产品是否查过最大购买份数限制
            checkSubmitOrderInsuredProductBuyLimit(dto, planVo.getProductId(), planVo.getPlanId(), planVo.getBuyLimit());
            String localProductId = dto.getProductInfo().getProductId();
            setOrderRequestDefaultParams(dto, planVo);
            //查询历史保单信息 2021-03-26 续保管理添加
            //setOldPolicyInfo(dto);

            OrderSubmitRequest submitDTO = dto;
            // 智能核保预下单
            OrderPreAiCheckResp check = channelSubmitOrderAICheck(submitDTO, localProductId);

            if (org.apache.commons.lang3.StringUtils.isBlank(check.getRedirectPage())) {
                throw new BizException(ExcptEnum.ORDER_ERROR_501010.getCode(), "生成问卷页面失败！");
            }
            //保存待核保数据
            SmCreateOrderSubmitRequest createDto = OrderConvertor.mapperSmCreateOrderSubmitRequest(dto, null, planVo);
            createDto.setFhOrderId(check.getOrderId());

            //s48 绑卡/自动续保 2021-07-29 绑卡信息与订单信息映射
            smOrderRenewBindService.addFhOrderIdToRenewBindInfo(check.getOrderId(), createDto);

            if (!StringUtils.isEmpty(dto.getPreOrderId())) {
                orderMapper.deleteOrder(dto.getPreOrderId());
            }
            FhOrderInfo orderInfo = createDto.getOrderInfo();
            if(orderInfo!=null) {
                orderInfo.setApplyTime(new Date());
            }
            saveOrderByReq(createDto);
            // 解锁token
            tokenService.unlockBusinessToken(dto.getToken());
            // 正常下单后token不能再用
            tokenService.deleteBusinessToken(userUniqueId, SmConstants.SM_ORDER_BUSINESS_CODE, dto.getToken());

            return check;
        }
        // 业务异常抛出提示
        catch (BizException | MSException e) {
            tokenService.unlockBusinessToken(dto.getToken());
            throw e;
        }
        // 未知异常包装
        catch (Exception e) {
            tokenService.unlockBusinessToken(dto.getToken());
            BizException bizError = ExceptionUtils.getBizError(e);
            if (bizError != null) {
                throw bizError;
            }
            throw new BizException(ExcptEnum.ORDER_ERROR_501010, e);
        }
    }


    /**
     * 校验订单数据【被保人 投保人身份证】
     *
     * @param dto
     */
    private void validOrderData(OrderSubmitRequest dto) {

        List<FhInsuredPerson> insuredPerson = dto.getInsuredPerson();
        if (CollectionUtils.isEmpty(insuredPerson)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人不能为空");
        }
        insuredPerson.forEach(insured -> validIdCardAndBirthDay(insured.getIdNumber(), insured.getBirthday()));
        validIdCardAndBirthDay(dto.getProposerInfo().getIdNumber(),
                dto.getProposerInfo().getBirthday());
    }

    private void validIdCardAndBirthDay(String idCardNum, String birthday) {
        if (org.apache.commons.lang3.StringUtils.isBlank(birthday)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "生日不能为空");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(idCardNum)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "证件号码不能为空");
        }
        //如果是身份证
        if (IdcardUtils.validateCard(idCardNum)) {
            String birthByIdCard = IdcardUtils.getBirthByIdCard(idCardNum);
            String s = birthday.replaceAll("-", "");
            if (!Objects.equals(birthByIdCard, s)) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "身份证[" + idCardNum + "]与生日不匹配[birthday" + birthday + "]");
            }
        }
    }

    /**
     * 异步保存订单信息
     *
     * @param dto
     * @param smOrderResp
     * @param planVo
     */
    @Override
    public void saveOrderInfo(OrderSubmitRequest dto, OrderSubmitResponse smOrderResp, SmPlanVO planVo) {
        SmCreateOrderSubmitRequest createDto = OrderConvertor.mapperSmCreateOrderSubmitRequest(dto, smOrderResp, planVo);
        saveOrderByReq(createDto);
    }

    /**
     * 根据保存对象保存数据
     *
     * @param createDto
     */
    protected void saveOrderByReq(SmCreateOrderSubmitRequest createDto) {
        orderMapper.insertOrder(createDto);
        orderMapper.insertOrderApplicant(createDto);
        orderMapper.insertOrderInsured(createDto);

        //Deprecated 放在一起的方式 在下个版本删除
        if (createDto.getPropertyInfo() != null
                && !Objects.equals(createDto.getPropertyInfo().getPropertyInfoIsExist(), "0")) {
            orderMapper.insertOrderPropertyInfo(createDto);
        }

        // since by 20210225
        //保存车辆信息
        if (Objects.nonNull(createDto.getCarInfo())) {
            SmOrderCar smOrderCar = SmOrderCarConvert.CNT.cvtDTO(createDto.getCarInfo());
            smOrderCar.setFhOrderId(createDto.getFhOrderId());
            carMapper.insertUseGeneratedKeys(smOrderCar);
        }

        // since by 20210225
        //保存房屋信息
        if (Objects.nonNull(createDto.getHouse())) {
            SmOrderHouse house = SmOrderHouseConvert.CNT.cvtDTO(createDto.getHouse());
            house.setFhOrderId(createDto.getFhOrderId());
            houseMapper.insertUseGeneratedKeys(house);
        }
        // since by 20210822
        //保存责任保额信息
        final List<SmOrderRiskDuty> duties = createDto.getInsuredPerson().stream()
                .filter(ip -> !CollectionUtils.isEmpty(ip.getDuties()))
                //转换责任到DO
                .map(ip -> ip.getDuties().stream().map(SmOrderDutyConvert.CNT::cvtSmOrderRiskDuty)
                        .peek(duty -> {
                            duty.setInsuredIdNumber(ip.getIdNumber());
                            duty.setFhOrderId(createDto.getFhOrderId());
                        }).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(duties)) {
            OrderRiskDataUtils.riskAmount(duties);
            riskDutyMapper.insertList(duties);
        }
        //投保人数大于1  团险和雇主责任险都不会走这个逻辑
        if (createDto.getInsuredPerson().size() > 1) {
            List<SmOrderItem> items = createDto.getInsuredPerson()
                    .stream()
                    .filter(o -> Objects.nonNull(o.getProduct()))
                    .map(ip -> {
                        SmOrderItem orderItem = new SmOrderItem();
                        orderItem.setFhOrderId(createDto.getFhOrderId());
                        orderItem.setIdNumber(ip.getIdNumber());
                        orderItem.setAppStatus(SmConstants.POLICY_STATUS_BLANK);
                        orderItem.setProductId(ip.getProduct().getProductId());
                        orderItem.setPlanId(ip.getProduct().getPlanId());
                        orderItem.setPlanCode(ip.getProduct().getPlanCode());
                        orderItem.setQty(Objects.isNull(ip.getProduct().getQty()) ? createDto.getQty() : ip.getProduct().getQty());
                        orderItem.setIdType(ip.getIdType());
                        orderItem.setType(0);
                        orderItem.setTotalAmount(ip.getProduct().getPremium());
                        orderItem.setUnitPrice(ip.getProduct().getPremium().divide(new BigDecimal(orderItem.getQty()), BigDecimal.ROUND_UP));
                        return orderItem;
                    }).collect(Collectors.toList());
            orderItemMapper.insertList(items);
        }
    }

    /**
     * 泛华保险支付回调
     *
     * @param orderId
     * @return
     */
    @Override
    public void handFhOrderPayedCallback(String orderId) {
        OrderQueryResponse orderQueryResp = getExternalThirdPartyOrderInfoByOrderId(orderId);
        if (!Objects.equals(orderQueryResp.getOrderInfo().getOrderState(), SmConstants.ORDER_STATUS_PAYED)) {
            log.error("中华联合支付回调获取支付状态不是已支付order={}", JSON.toJSONString(orderQueryResp));
            return;
        }
        orderMapper.updateOrderPayStatus(orderId, SmConstants.ORDER_STATUS_PAYED);
        orderMapper.updateOrderAppStatus(orderId, SmConstants.POLICY_STATUS_PROCESS, null);
        updateOrderPaymentTimeAndCommission(orderId);
        // 发布订单提成变更事件
        SpringFactoryUtil.getBean(EventBusEngine.class).publish(new OrderCommissionChangeEvent(orderId));
    }

    /**
     * 更新订单保单状态
     *
     * @param orderId
     * @param appStatus
     */
    @Override
    public void updateOrderAppStatus(String orderId, String appStatus) {
        orderMapper.updateOrderAppStatus(orderId, appStatus, null);
    }

    /**
     * 更新订单支付状态
     *
     * @param orderId
     * @param payStatus
     */
    @Override
    public void updateOrderPayStatus(String orderId, String payStatus) {
        orderMapper.updateOrderPayStatus(orderId, payStatus);
    }

    /**
     * 更新订单支付佣金和支付时间，
     * 抽取用户，计算佣金明细，代理人佣金明细
     * =========================================
     * 注意 一定要更新订单状态支付成功才能调用此方法
     *
     * @param orderId
     */
    @Override
    public void updateOrderPaymentTimeAndCommission(String orderId) {
        // 设置提成
        SmBaseOrderVO baseOrder = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (!Objects.equals(baseOrder.getPayStatus(), SmConstants.ORDER_STATUS_PAYED)) {
            log.error("订单= {} 未支付成功不能计算提成。", orderId);
            return;
        }
        // 分销订单不需要计算佣金
        if (!baseOrder.distributionOrder()) {
            List<SmOrderItem> smOrderItems = orderItemMapper.selectByOrderId(orderId);
            //如果有item表 并且都有planId 家庭版  团险
            if (!CollectionUtils.isEmpty(smOrderItems)
                    && smOrderItems.stream().anyMatch(item -> Objects.nonNull(item.getPlanId()))) {
                boolean flag = true;
                for (SmOrderItem item : smOrderItems) {
                    SmCommissionSettingVO settingVo = cmsMapper.getCommissionSettingByPlanId(item.getPlanId(), baseOrder.getCreateTime());
                    if (Objects.nonNull(settingVo)) {
                        orderItemMapper.updateCommission(orderId, item.getIdNumber(), settingVo.getId());
                    } else {
                        //只要有一个元素没有
                        flag = false;
                        log.warn("订单={} {} 没有正确的提成配置。", item.getPlanId(), orderId);
                    }
                    //标记成-1  代表已经算过了
                    if (flag) {
                        orderMapper.updateOrderCommission(orderId, "-1");
                    }
                }

            } else {
                SmCommissionSettingVO settingVo = cmsMapper.getCommissionSettingByPlanId(baseOrder.getPlanId(), baseOrder.getCreateTime());
                String commissionId = null;
                if (settingVo != null) {
                    commissionId = String.valueOf(settingVo.getId());
                } else {
                    log.warn("订单={} {} 没有正确的提成配置。", baseOrder.getPlanId(), orderId);
                }
                orderMapper.updateOrderCommission(orderId, commissionId);

            }
        }
        orderMapper.updateOrderPaymentTime(orderId);
    }

    /**
     * 如果是退保状态 返回被保人证件号以及 只支持一个被保人的订单
     *
     * @return
     */
    protected Map<String, String> updateByLocalIfCancel(String orderId) {
        List<SmOrderListVO> orderVos = orderMapper.listOrderInsuredDetailByOrderId(orderId);
        if (orderVos == null || orderVos.size() == 0) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201009);
        }
        SmOrderListVO order = orderVos.iterator().next();
        if (Objects.equals(order.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            busEngine.publish(new OrderCommissionChangeEvent(orderId));
            return Collections.singletonMap(order.getInsuredIdNumber(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        }
        return null;
    }

    /**
     * 与泛华同步更新存小额保险保单信息
     *
     * @param orderId
     * @return 更新保单后身份证保单状态map insIdNumber->appStatus
     */
    @Override
    public Map<String, String> updateOrderPolicyInfo(String orderId) {
        SmBaseOrderVO dbOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        String orderChannel = dbOrderVO.getChannel();
        OrderQueryResponse resp = getExternalThirdPartyOrderInfoByOrderId(dbOrderVO);
        // 第三方API调用失败
        if (!Objects.equals(resp.getNoticeCode(), AmConstants.API_FANHUA_SUCCESS_0)) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201008);
        }

        // 处理过期失效订单
        adjustExpireOrderResp(orderId, resp);

        // 订单支付状态不一样修改订单支付状态
        if (resp.getOrderInfo() != null &&
                !Objects.equals(dbOrderVO.getPayStatus(), resp.getOrderInfo().getOrderState())) {
            orderMapper.updateOrderPayStatus(orderId, resp.getOrderInfo().getOrderState());
            if (Objects.equals(resp.getOrderInfo().getOrderState(), SmConstants.ORDER_STATUS_PAYED)) {
                orderMapper.updateOrderAppStatus(orderId, SmConstants.POLICY_STATUS_PROCESS, null);
            }
        }
        // 如果支付回调丢失，计算提成
        if ((dbOrderVO.getCommissionId() == null || dbOrderVO.getPaymentTime() == null)
                && resp.getOrderInfo() != null
                && Objects.equals(resp.getOrderInfo().getOrderState(), SmConstants.ORDER_STATUS_PAYED)) {
            updateOrderPaymentTimeAndCommission(orderId);
        }

        Map<String, String> map = processPolicySuccess(orderId, orderChannel, resp);
        // 每次同步重新计算提成信息防止提成丢失
        if (Objects.equals(resp.getOrderInfo().getOrderState(), SmConstants.ORDER_STATUS_PAYED) &&
                Boolean.FALSE.equals(resp.getEvent())) {
            busEngine.publish(new OrderCommissionChangeEvent(orderId));
        }
        return map;
    }

    /**
     * 处理承包成功逻辑 [泛华和 和中和联合的逻辑]
     *
     * @param orderId
     */
    protected Map<String, String> processPolicySuccess(String orderId, String orderChannel, OrderQueryResponse resp) {

        boolean isSuccess = false;
        /*
         * 修改承保状态
         */
        Map<String, String> insIdNumberAppStatusMap = new HashMap<>();
        // 泛华承保逻辑
        if (Objects.equals(orderChannel, SmConstants.CHANNEL_FH)) {
            List<OrderQueryResponse.PolicyInfo> policyInfos = resp.getPolicyInfos();
            List<OrderQueryResponse.InsuredInfo> insuredInfos = resp.getInsuredInfos();

            Map<String, List<OrderQueryResponse.InsuredInfo>> insuredMap = LambdaUtils.groupBy(insuredInfos, OrderQueryResponse.InsuredInfo::getInsuredIdNo);

            Map<String, List<SmOrderSmsNotifyVO>> dbInsuredMap = Collections.emptyMap();
            //存在任意证件号码重复的被保人
            if (insuredMap.values().stream().anyMatch(va -> va.size() > 1)) {
                dbInsuredMap = LambdaUtils
                        .groupBy(orderMapper.getOrderInsuredByOrderId(orderId),
                                SmOrderSmsNotifyVO::getInsuredIdNumber);
            }
            if (insuredInfos != null && !insuredInfos.isEmpty()) {

                for (int i = 0, len = insuredInfos.size(); i < len; i++) {
                    isSuccess = false;
                    OrderQueryResponse.InsuredInfo insured = insuredInfos.get(i);
                    // 查询当前被保人的保单信息
                    Optional<OrderQueryResponse.PolicyInfo> optional = policyInfos.stream()
                            .filter(p -> Objects.equals(p.getInsuredSn(), insured.getInsuredSn()))
                            .findFirst();
                    // 匹配到被保人保单
                    if (optional.isPresent()) {
                        OrderQueryResponse.PolicyInfo policyInfo = optional.get();
                        // 更新保单信息
                        //如果这个订单 有多个一样的被保人

                        List<OrderQueryResponse.InsuredInfo> idNumberSameList = insuredMap.getOrDefault(insured.getInsuredIdNo(), Collections.emptyList());
                        int insuredIndex = -1;
                        List<SmOrderSmsNotifyVO> insureds = dbInsuredMap.getOrDefault(insured.getInsuredIdNo(), Collections.emptyList());

                        if (idNumberSameList.size() > 1) {
                            insuredIndex = 0;
                            for (int i1 = 0; i1 < idNumberSameList.size(); i1++) {

                                if (Objects.equals(idNumberSameList.get(i1).getInsuredSn(), policyInfo.getInsuredSn())) {
                                    insuredIndex = i1;
                                    break;
                                }
                            }
                            if (insureds.size() > insuredIndex) {
                                orderMapper.updateOrderPolicyInfoByInduredIdV2(insureds.get(insuredIndex).getInsuredId(), policyInfo);
                            } else {
                                throw new BizException(ExcptEnum.DATA_ERROR_801304.getCode(), "数据异常，请联系管理员:" + orderId);
                            }
                        } else {
                            orderMapper.updateOrderPolicyInfoV2(orderId, insured.getInsuredIdNo(), policyInfo);
                        }
                        // 退保成功
                        if (Objects.equals(policyInfo.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                            // 更新退保状态
                            if (idNumberSameList.size() > 1) {
                                orderMapper.updateOrderSurrenderTimeByInsuredId(insureds.get(insuredIndex).getInsuredId());
                            } else {
                                orderMapper.updateOrderSurrenderTime(orderId, insured.getInsuredIdNo());
                            }
                        } else {
                            isSuccess = true;
                            // 承包成功 更新保单缓存事件
                            busEngine.publishTime(new OrderPolicyUrlChangeEvent(orderId, policyInfo.getPolicyNo()
                                    , policyInfo.getDownloadURL()));
                            //可重发的保单时间
                            busEngine.publish(new OrderAppSuccessRetryEvent(orderId, policyInfo.getPolicyNo()));
                        }
                        insIdNumberAppStatusMap.put(insured.getInsuredIdNo(), policyInfo.getAppStatus());
                    }
                }
            }
        }
        // 中华联合承保逻辑 (承保成功和退保成功才更新)
        else if (resp.getPolicyInfo() != null) {
            String newAppStatus = resp.getPolicyInfo().getAppStatus();
            if (Objects.equals(newAppStatus, SmConstants.POLICY_STATUS_SUCCESS)
                    || (Objects.equals(newAppStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS))) {
                // 中华保险只有一个被保人
                List<SmBaseOrderInsuredVO> baseOrderInsureds = orderMapper.listBaseOrderInsuredByOrderId(orderId);
                String insIdNumber = baseOrderInsureds.get(0).getInsuredIdNumber();
                orderMapper.updateOrderPolicyInfoV2(orderId, insIdNumber, resp.getPolicyInfo());
                // 退保成功更新退保状态 发布事件更新提成信息
                if (Objects.equals(newAppStatus, SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
                    orderMapper.updateOrderSurrenderTime(orderId, null);
                } else {
                    //标记承包成功
                    isSuccess = true;
                    busEngine.publishTime(new OrderPolicyUrlChangeEvent(orderId, resp.getPolicyInfo().getPolicyNo()
                            , resp.getPolicyInfo().getDownloadURL()));
                    //可重发的保单事件
                    busEngine.publish(new OrderAppSuccessRetryEvent(orderId, resp.getPolicyInfo().getPolicyNo()));


                }
                insIdNumberAppStatusMap.put(insIdNumber, newAppStatus);
            }
        }
        if (isSuccess) {
            busEngine.publish(new OrderAppSuccessSmsEvent(orderId));
        }
        // 每次同步重新计算提成信息防止提成丢失
        return insIdNumberAppStatusMap;
    }

    /**
     * 更新订单成已过期状态
     *
     * @param orderId
     * @return
     */
    @Override
    public void updateExpireOrderInfo(String orderId) {
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (Objects.equals(baseOrderVO.getPayStatus(), SmConstants.ORDER_STATUS_TO_PAY)) {
            int productId = baseOrderVO.getProductId();
            SmProductDetailVO productDetail = productService.getProductById(productId);
            int effectWaitingDay = productDetail.getEffectWaitingDayMin();
            Date todayBuyStartTime = DateUtil.addDay(DateUtil.getBeginOfDay(new Date()), effectWaitingDay);
            if (todayBuyStartTime.compareTo(baseOrderVO.getStartTime()) > 0) {
                orderMapper.updateOrderPayStatus(orderId, SmConstants.ORDER_STATUS_EXPIRE);
            }
        }
    }

    /**
     * 查询订单信息 泛华 和中华联合是查询第三方的   大家没有查询接口 使用本地的数据
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderQueryResponse getOrderInfo(String orderId) {
        OrderQueryResponse resp = getExternalThirdPartyOrderInfoByOrderId(orderId);
        if (resp == null) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201009);
        }
        OrderConvertor.mapperOrderQueryResponse(orderId, resp);
        return resp;
    }

    @Override
    public OrderQueryResponse getOrderInfoForLocal(String orderId) {
        return OrderConvertor.mapperOrderQuery4Local(orderId);
    }

    @Override
    public OrderQueryResponse getOrderInfoForLocal(String orderId, String version) {
        if (Objects.equals(version, "V3")) {
            return OrderConvertor.mapperOrderQuery4LocalV3(orderId);
        } else {
            return OrderConvertor.mapperOrderQuery4Local(orderId);
        }
    }

    @Override
    public AutoOrderQueryResponse getAutoOrderInfoForLocal(String orderId, String version) {
        if (Objects.equals(version, "V3")) {
            return OrderConvertor.mapperAutoOrderQuery4LocalV3(orderId);
        } else {
            throw new BizException("", "暂时不支持该版本接口");
        }
    }

    /**
     * 获取订单支付URL
     *
     * @param orderId
     * @param type
     * @return
     */
    @Override
    public String getOrderPayUrl(String orderId, Integer type) {
        if (type == null || type == 0) {
            return getOrderPayUrl(orderId);
        }

        if (type == 1) {
            Endor param = new Endor();
            param.setOrderId(orderId);
            Endor endor = paymentMapper.selectOne(param);
            if (!StringUtils.isEmpty(endor.getPayUrl())) {
                return endor.getPayUrl();
            }
            OrderPrePayRequest req = new OrderPrePayRequest();
            req.setAppNo(endor.getPolicyNo());
            req.setOrderAmount(endor.getAmount());
            req.setOrderId(orderId);
            req.setTransDate(new Date());
            /**
             * 暂时只兼容见费单，后续有扩展再实现相关逻辑
             */
            if (Objects.equals(endor.getOrderType(), SmConstants.PLAN_ORDER_OUT_TYPE_SEE_FEE)) {
                OrderPrePayResponse resp = getExternalThirdPartyOrderService(endor.getChannel()).seeFeePayChannel(req);
                return resp.getPayUrl();
            }
        }
        throw new BizException("-1", "功能未实现");
    }


    protected String getOrderPayUrl(String orderId) {
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (order == null) {
            throw new BizException(ExcptEnum.DATA_NOT_EXISTS);
        }
        // 数据库已经保存支付Url
        if (!StringUtils.isEmpty(order.getPayUrl())) {
            return order.getPayUrl();
        }

        //如果是非见费出单 走自己的支付地址
        if (Objects.equals(order.getOrderOutType(), SmConstants.PLAN_ORDER_OUT_TYPE_NON_SEE_FEE)) {
            String payUrl = String.format(payFacadeConfigProperties.getFrontPayUrl(), orderId);
            orderMapper.updateOrderPaymentId(orderId, null, payUrl);
            return payUrl;
        }
        OrderPrePayRequest prePayDTO = new OrderPrePayRequest();
        prePayDTO.setOrderId(orderId);
        prePayDTO.setTransDate(order.getSubmitTime());
        prePayDTO.setOrderAmount(order.getTotalAmount());
        prePayDTO.setAppNo(order.getAppNo());
        String channel = order.getChannel();
        OrderPrePayResponse prePayRespDTO;
        if (Objects.equals(order.getOrderOutType(), SmConstants.PLAN_ORDER_OUT_TYPE_SEE_FEE)) {
            prePayRespDTO = getExternalThirdPartyOrderService(channel).seeFeePayChannel(prePayDTO);
        } else {
            prePayRespDTO = getExternalThirdPartyOrderService(channel).prePayChannelOrder(prePayDTO);
        }
        orderMapper.updateOrderPaymentId(orderId, prePayRespDTO.getPayId(), prePayRespDTO.getPayUrl());
        return prePayRespDTO.getPayUrl();
    }

    /**
     * 获取第三方订单详情信息
     *
     * @return
     */
    protected OrderQueryResponse getExternalThirdPartyOrderInfoByOrderId(String orderId) {
        SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
        if (order == null) {
            log.warn("订单信息为空:{}", orderId);
        }
        return getExternalThirdPartyOrderInfoByOrderId(order);

    }

    /**
     * 处理投保流程
     *
     * @return
     */
    protected OrderQueryResponse getExternalThirdPartyOrderInfoByOrderId(SmBaseOrderVO order) {
        log.info("开始处理个险投保流程:{}", order);
        if (order == null) {
            log.warn("订单信息为空");
        }
        String orderId = order.getFhOrderId();

        OrderQueryRequest queryDTO = new OrderQueryRequest();
        queryDTO.setOrderId(orderId);
        queryDTO.setPayId(order.getPayId());
        queryDTO.setAppNo(order.getAppNo());
        queryDTO.setSubmitTime(order.getSubmitTime());
        queryDTO.setOrderOutType(order.getOrderOutType());
        queryDTO.setProductAttrCode(order.getProductAttrCode());

        String channel = order.getChannel();
        return getExternalThirdPartyOrderService(channel)
                .queryChannelOrderInfo(queryDTO);
    }


    /**
     * 获取渠道订单service
     *
     * @param channel
     * @return
     */
    public ChannelOrderService getExternalThirdPartyOrderService(String channel) {
        try {
            return SpringFactoryUtil.getBean(channel, ChannelOrderService.class);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.PRODUCT_CHANNEL_ERROR, e);
        }
    }

    /**
     * 处理异步回调
     *
     * @param object
     * @param response
     * @param request
     * @return
     */
    @Override
    public void handAsyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException {
    }

    /**
     * 处理支付同步回调
     *
     * @param object
     * @param response
     * @param request
     * @return
     */
    @Override
    public void handSyncPayCallback(String orderId, Object object, HttpServletResponse response, HttpServletRequest request) throws IOException {
    }

    /**
     * 订单请求默认参数设置
     *
     * @param dto
     * @param planVo
     */
    private void setOrderRequestDefaultParams(OrderSubmitRequest dto, SmPlanVO planVo) {
        // 修改订单产品信息为第三方产品Id
        dto.getProductInfo().setProductId(planVo.getFhProductId());
        if (StringUtils.isEmpty(dto.getProductInfo().getProductId())) {
            dto.getProductInfo().setProductId(planVo.getPlanCode());
        }
        dto.getOrderInfo().setSubmitTime(DateUtil.format(new Date(), DateUtil.CN_LONG_FORMAT));
    }

    /**
     * 修改提交给泛华的手机号码为客户经理的手机号码
     *
     * @param dto
     * @return
     */
    private OrderSubmitRequest changeOrderMobilesToCustManagerMobile(OrderSubmitRequest dto) {
        // 深拷贝对象
        WxUserVO userVO = userMapper.getUserByUserId(dto.getProductInfo().getRecommendId());
        if (userVO != null) {
            OrderSubmitRequest submitDTO = JSON.parseObject(JSON.toJSONString(dto), OrderSubmitRequest.class);
            submitDTO.getProposerInfo().setCellPhone(userVO.getUserMobile());
            submitDTO.getInsuredPerson().forEach(is -> is.setCellPhone(userVO.getUserMobile()));
            return submitDTO;
        }
        return dto;
    }

    /**
     * 如果第三方下单返回续保信息，需要调整续保提示信息
     *
     * @param smOrderResp
     * @param dto
     */
    private void adjustRenewOrderResp(OrderSubmitResponse smOrderResp, OrderSubmitRequest dto) {
        OrderQueryRequest queryDTO = new OrderQueryRequest();
        queryDTO.setOrderId(smOrderResp.getOrderId());
        OrderQueryResponse thisOrderResponse = getExternalThirdPartyOrderService("fh").queryChannelOrderInfo(queryDTO);
        String thisStartTime = thisOrderResponse.getOrderInfo().getStartTime();
        String thisEndTime = thisOrderResponse.getOrderInfo().getEndTime();
        if (!Objects.equals(dto.getOrderInfo().getStartTime(), thisStartTime)
                || !Objects.equals(dto.getOrderInfo().getEndTime(), thisEndTime)) {
            String renewOrderId = smOrderResp.getReturnMap().getRenewOrderSn();
            queryDTO.setOrderId(renewOrderId);
            OrderQueryResponse renewOrderResponse = getExternalThirdPartyOrderService("fh").queryChannelOrderInfo(queryDTO);
            dto.getOrderInfo().setStartTime(thisStartTime);
            dto.getOrderInfo().setEndTime(thisEndTime);
            smOrderResp.getReturnMap().setMsg("已修改本次订单的起保日期为：" +
                    thisOrderResponse.getOrderInfo().getStartTime() +
                    "，续接上一年保单" +
                    smOrderResp.getReturnMap().getRenewOrderSn() +
                    "的终止日期：" +
                    renewOrderResponse.getOrderInfo().getEndTime());
        } else {
            smOrderResp.getReturnMap().setMsg(null);
        }
    }

    /**
     * 判断订单过期
     *
     * @param orderId
     * @param resp
     */
    protected void adjustExpireOrderResp(String orderId, OrderQueryResponse resp) {
        OrderQueryResponse.OrderInfo orderInfo = resp.getOrderInfo();
        if (orderInfo != null
                && !StringUtils.isEmpty(orderInfo.getStartTime())
                && Objects.equals(orderInfo.getOrderState(), SmConstants.ORDER_STATUS_TO_PAY)) {

            int effectWaitingDay = orderMapper.countOrderProductEffectWaitingDay(orderId);
            Date todayBuyStartTime = DateUtil.addDay(DateUtil.getBeginOfDay(new Date()), effectWaitingDay);
            Date orderRealStartTime = DateUtil.parseDate(orderInfo.getStartTime(), SmConstants.DATE_FORMAT_1);

            if (todayBuyStartTime.compareTo(orderRealStartTime) > 0) {
                orderInfo.setOrderState(SmConstants.ORDER_STATUS_EXPIRE);
            }
        }
    }

    @Override
    public boolean checkSubmitOrderInsuredProductBuyLimit(List<SmOrderInsuredBuyDTO> idNumbers,
                                                          int productId) {

        SmProductDetailVO productById = productService.getProductById(productId);
        Integer productBuyLimit = productById.getBuyLimit();
        SmProductFormBuyLimit query = new SmProductFormBuyLimit();

        query.setProductId(productId);
        List<SmProductFormBuyLimit> formBuyLimits = formBuyLimitMapper.selectEnabled(query);

        for (int i = 0; i < idNumbers.size(); i++) {

            SmOrderInsuredBuyDTO dto = idNumbers.get(i);
            if (!CollectionUtils.isEmpty(formBuyLimits)) {//如果当前产品有特殊职业配置
                int occLimit = formBuyLimits.stream().map(SmProductFormBuyLimit::getLimitCode)
                        .mapToInt(Integer::parseInt).min().orElse(productBuyLimit);

                List<String> os = formBuyLimits.stream().map(SmProductFormBuyLimit::getFiledValue).collect(Collectors.toList());
                int occHasPolicyQty = orderMapper.countProductPolicyQty(productId,
                        dto.getIdNumber(), os);
                if (occHasPolicyQty >= occLimit) {
                    return false;
//                    throw new BizException(ExcptEnum.OVER_PRODUCT_LIMIT);
                }
            }
            Optional<SmProductFormBuyLimit> specialOcc = findSpecialOcc(dto.getOccupationCode(), formBuyLimits);

            //如果当前职业单独配置了限购分数则用配置的限购分数 否则使用计划设置
            int condBuyLimit = specialOcc.map(SmProductFormBuyLimit::getLimitCode)
                    .map(Integer::parseInt)
                    .orElse(productBuyLimit);

            int hasPolicyQty = orderMapper.countProductPolicyQty(productId, dto.getIdNumber(), null);
            if (dto.getQty() + hasPolicyQty > condBuyLimit) {
                return false;
//                throw new BizException(ExcptEnum.OVER_PRODUCT_LIMIT);
            }
        }
        return true;
    }

    /**
     * 检验产品是否查过最大购买份数限制
     *
     * @param dto
     */
    private void checkSubmitOrderInsuredProductBuyLimit(OrderSubmitRequest dto, int productId, int planId, int buyLimit) {

        SmProductFormBuyLimit query = new SmProductFormBuyLimit();
        query.setProductId(productId);
        query.setPlanId(planId);
        List<SmProductFormBuyLimit> formBuyLimits = formBuyLimitMapper.selectEnabled(query);
        dto.getInsuredPerson().forEach(ins -> {

            Optional<SmProductFormBuyLimit> specialOcc = findSpecialOcc(ins, formBuyLimits);

            //如果当前职业单独配置了限购分数则用配置的限购分数 否则使用计划设置
            int condBuyLimit = specialOcc.map(SmProductFormBuyLimit::getLimitCode)
                    .map(Integer::parseInt)
                    .orElse(buyLimit);

            String type = productSpecialRuleProperties.queryProductTypeHandlerById(productId);
            List<Integer> sameProduct = productSpecialRuleProperties.queryProductCategoryById(productId);
            int hasPolicyQty = productSpecialRuleProperties.routerToHandler(type)
                    .countProductsPolicyQty(
                            sameProduct, ins.getIdNumber(), null, dto.getOrderInfo().getStartTime()
                    );

            if (dto.getQty() + hasPolicyQty > condBuyLimit) {
                if (condBuyLimit == 0) {
                    throw new BizException(ExcptEnum.OVER_PRODUCT_LIMIT.getCode(), "被保人不能投保该计划");
                }
                throw new BizException(ExcptEnum.OVER_PRODUCT_LIMIT.getCode(), "被保人[" + ins.getPersonName() + "]投保该产品已达上限，保存失败");
            }
        });
    }

    /**
     * 是否特殊职业
     */
    private Optional<SmProductFormBuyLimit> findSpecialOcc(FhInsuredPerson insuredPerson, List<SmProductFormBuyLimit> formBuyLimits) {

        return formBuyLimits.stream()
                .filter(pre -> Objects.equals(pre.getFiledValue(), insuredPerson.getOccupationCode()))
                .findFirst();
    }

    /**
     * 是否特殊职业
     */
    private Optional<SmProductFormBuyLimit> findSpecialOcc(String occCode, List<SmProductFormBuyLimit> formBuyLimits) {

        return formBuyLimits.stream()
                .filter(pre -> Objects.equals(pre.getFiledValue(), occCode))
                .findFirst();
    }

    /**
     * 投保人和被保人实名认证
     *
     * @param dto
     */
    private void certifyRealName(OrderSubmitRequest dto) {
        WxHomeProductService productService = SpringFactoryUtil.getBean(WxHomeProductService.class);
        Map<String, String> idTypeMap = productService.getCompanySettings(dto.getPlanId(), SmConstants.PRODUCT_FORM_FIELD_TYPE_IDTYPE)
                .stream()
                .distinct()
                .collect(Collectors.toMap(WxFormFieldOptionVO::getOptionCode, WxFormFieldOptionVO::getOptionName));
        FhProposer proposer = dto.getProposerInfo();
        //验证投保人
        if (isIdCard(idTypeMap.get(proposer.getIdType()))) {
            ccService.createCustInfo(proposer.getPersonName(), proposer.getIdNumber(),
                    dto.getProductInfo().getRecommendOrgCode(), dto.getProductInfo().getRecommendMainJobNumber());
        }
        //验证被保人
        dto.getInsuredPerson().forEach(ins -> {
            //身份证 并且证件号码和投保人不一样
            if (isIdCard(idTypeMap.get(ins.getIdType()))
                    && !Objects.equals(proposer.getIdNumber(), ins.getIdNumber())) {
                ccService.createInsuredCustInfo(ins.getPersonName(), ins.getIdNumber(),
                        dto.getProductInfo().getRecommendOrgCode(), dto.getProductInfo().getRecommendMainJobNumber());
            }
        });
    }

    /**
     * 验证证件名称是否是身份证
     *
     * @param idTypeName
     * @return
     */
    private boolean isIdCard(String idTypeName) {
        return idTypeName != null && idTypeName.contains("身份证");
    }


    private void setOldPolicyInfo(OrderSubmitRequest dto) {
        //log.info("dto2={}",dto);
        if (dto.isRealRenewFlag()) {
            List<SmOrderInsured> insuredList = orderMapper.listSmOrderInsuredByOrderId(dto.getPreOrderId());
            dto.getInsuredPerson().stream().forEach(fhInsuredPerson -> {
                Optional<SmOrderInsured> opt = insuredList.stream().filter(insure -> Objects.equals(fhInsuredPerson.getIdNumber(), insure.getIdNumber())).findFirst();
                if (opt.isPresent()) {
                    fhInsuredPerson.setOldPolicyNo(opt.get().getPolicyNo());
                }
            });
            Optional<FhInsuredPerson> fh = dto.getInsuredPerson().stream().filter(person -> org.apache.commons.lang3.StringUtils.isNotBlank(person.getOldPolicyNo())).findAny();
            if (!fh.isPresent()) {
                log.warn("没有找到订单号为[{}]的对应的历史保单号", dto.getPreOrderId());
                throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "提交信息和原订单信息不匹配，不支持续保");
            }
        }
        //log.info("dto3={}",dto);
    }

    public List<SmCommonSettingVO> mapperOption2Company(Integer companyId) {
        SmOfflineOrderValidation.CodeMapperHelper codeMapperHelper = new SmOfflineOrderValidation.CodeMapperHelper();
        codeMapperHelper.setCompanyId(companyId.toString());
        Set<String> fields = new HashSet<>();
        fields.add(PRODUCT_FORM_FIELD_TYPE_IDTYPE);
        codeMapperHelper.setFieldCode(fields);
        //codeMapperHelper.setOptionName(new HashSet<>());
        return commonSettingService.mapperOption2Company(codeMapperHelper);
    }

    /**** add by zhangjian s47 保单回访状态更新 ****/
    public void batchUpdateOrderVisitInfo(List<SmOrderPolicy> smOrderPolicyList) {
        if (CollectionUtils.isEmpty(smOrderPolicyList)) {
            return;
        }
        smOrderPolicyMapper.batchUpdateOrderVisitInfo(EnumChannel.FX.getCode(), smOrderPolicyList);
    }

    public void batchSaveOrderInfoGroup(List<SmCreateOrderSubmitRequest> submitRequestList) {
        orderCoreService.batchSaveOrderInfoGroup(submitRequestList);
    }

    /****佣金改造，并行计算临时解决方案**/
    @Autowired
    private TempOrderNewProductMapper tempOrderNewProductMapper;

    public void addTempOrderNewProductInfo(String orderId, String fhProductId) {
        SmPlanVO planVO = productService.getPlanByFhProductId(fhProductId);
        if (Objects.nonNull(planVO)) {
            TempOrderNewProductPO po = new TempOrderNewProductPO();
            po.setOrderId(orderId);
            po.setProductId(planVO.getProductId());
            po.setPlanId(planVO.getId());
            po.setFhProductId(fhProductId);
            tempOrderNewProductMapper.insertSelective(po);
        }
    }

    @Override
    public void pushRenewalTerm(Integer beforeDay, Integer defaultGraceDays) {
        log.info("渠道:{}暂不需要初始化续期数据", channel());

    }


    protected int saveNotifyLog(String orderId, String channel, String body) {
        return saveNotifyLog(orderId, channel, null, body);
    }

    protected int saveNotifyLog(String orderId, String channel, String recommendId, String body) {
        if (org.apache.commons.lang.StringUtils.isBlank(body)) {
            return -1;
        }
        if (body.length() > 65000) {
            log.info("回调报文数据过长:{},{}", orderId, body);
            return -1;
        }
        if (orderId == null) {
            orderId = "-1";
        }
        SmChannelCallback notifyLog = new SmChannelCallback();
        notifyLog.setChannel(channel);
        notifyLog.setFhOrderId(orderId);
        notifyLog.setQueryString(body);
        notifyLog.setRecommendId(recommendId);
        notifyLog.setState(40);
        try {
            return channelCallbackMapper.insertSelective(notifyLog);
        } catch (Exception e) {
            log.error("外部系统-通知报文日志保存失败:{}", orderId, e);
            return -1;
        }
    }
}
