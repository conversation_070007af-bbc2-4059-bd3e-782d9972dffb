package com.cfpamf.ms.insur.weixin.service.claim.helper;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimCancelReportMapper;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.pojo.vo.UserVO;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimListVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimNotifyVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.claim.WxClaimCancelReportNotifyVo;
import com.cfpamf.ms.insur.weixin.service.WxMpServiceProxy;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/2/15 10:01
 */
@Component
@Slf4j
public class ClaimCancelReportWxNotifyHelper {
    /**
     * 理赔结果通知微信模板Id
     */
    @Value("${wechat.message.claimResultId}")
    private String templateId;


    /**
     * 微信参数配置
     */
    @Autowired
    private WechatConfig wechatConfig;

    @Autowired
    SmClaimCancelReportMapper claimCancelReportMapper;

    @Value("${claim.cancel-report.overdue-day}")
    Integer overdueDay;

    /**
     * 微信代理service
     */
    @Lazy
    @Autowired
    private WxMpServiceProxy serviceProxy;
    @Autowired
    private UserService userService;


    /**
     * 注销申请通过-提醒管护客户经理
     *
     * @param claimCancelReportId
     * @param rejectReason
     */
    public void sendRejectMessage(Integer claimCancelReportId, String rejectReason) {
        log.info("发送注销理赔驳回微信通知：claimCancelReportId：{}，rejectReason：{}", claimCancelReportId, rejectReason);
        WxMpTemplateMessage wxMpTemplateMessage = buildClaimCancelRejectMessage(claimCancelReportId, rejectReason);
        serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
    }


    /**
     * 注销申请通过-提醒管护客户经理
     *
     * @param claimCancelReportId
     */
    public void sendSuccessMessage(Integer claimCancelReportId) {
        log.info("发送注销申请通过微信通知：claimCancelReportId：{}", claimCancelReportId);
        WxMpTemplateMessage wxMpTemplateMessage = buildClaimCancelSuccessMessage(claimCancelReportId);
        serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
    }

    /**
     * 注销前通知-提醒管护客户经理
     *
     */
    public void sendCancelBeforeMessage(WxClaimListVo wxClaimListVo) {
        WxMpTemplateMessage wxMpTemplateMessage = buildClaimCancelBeforeMessage(wxClaimListVo);
        serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
    }

    /**
     * 注销申请通过-提醒管护客户经理
     * <p>
     * ————————————————————————————————————————————
     * 【小鲸向海】提醒您，您的客户某某的注销报案申请已通过。
     * 理赔状态：已注销报案，注销原因：客户主动放弃了索赔
     * 报案号：xxxxxx
     * 备注：如有疑问，请联系保险事业部理赔专员某某。
     *
     * @param claimCancelReportId
     */
    private WxMpTemplateMessage buildClaimCancelSuccessMessage(Integer claimCancelReportId) {
        WxClaimCancelReportNotifyVo notifyVo = claimCancelReportMapper.getWxClaimCancelReportNotifyVoByClaimCancelReportId(claimCancelReportId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailCancelUrl(claimCancelReportId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVo.getCreateUserWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，您的客户 %s 的注销报案申请已通过。\r\n", notifyVo.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVo), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("已注销报案，注销原因：%s", notifyVo.getCancelReportCause()), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", String.format("如有疑问，请联系保险事业部理赔专员 %s ", (notifyVo.getSettlement() == null ? "" : notifyVo.getSettlement())), "#030303"));
        return wtm;
    }

    /**
     * 注销前通知-提醒管护客户经理
     * <p>
     * ————————————————————————————————————————————
     * 【小鲸向海】提醒您，您的客户XXX的理赔申请将超过2年索赔时效，届时系统将自动注销，请尽快协助客户理赔。
     * 理赔状态：理赔状态
     * 报案号：xxxxxx
     * 备注：如有疑问，请联系保险事业部理赔专员某某。
     *
     */
    private WxMpTemplateMessage buildClaimCancelBeforeMessage(WxClaimListVo wxClaimListVo) {
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setTemplateId(templateId);
        wtm.setToUser(wxClaimListVo.getCustomerAdminWxOpenid());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，您的客户 %s 的理赔申请将超过2年索赔时效，届时系统将自动注销，请尽快协助客户理赔。\r\n", wxClaimListVo.getInsuredPersonName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatBuildClaimCancelBeforeMessage(wxClaimListVo.getCustomerAdminName(), wxClaimListVo.getInsuredPersonName()), "#0000FF"));
        wtm.addData(new WxMpTemplateData("keyword2", wxClaimListVo.getClaimResult(), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", String.format("如有疑问，请联系保险事业部理赔专员 %s ", (wxClaimListVo.getSettlement() == null ? "" : wxClaimListVo.getSettlement())), "#030303"));
        return wtm;
    }

    /**
     * 机构审核-提醒渠道pco
     *
     * @param claimCancelReportId
     */
    public void sendNotifyMessageToPic(Integer claimCancelReportId) {
        log.info("发送注销申请机构审核微信通知：claimCancelReportId：{}", claimCancelReportId);
        //获取渠道PCO信息
        WxClaimCancelReportNotifyVo notifyVo = claimCancelReportMapper.getWxClaimCancelReportNotifyVoByClaimCancelReportId(claimCancelReportId);
        String createUserId = notifyVo.getCreateBy();
        List<AuthUserVO> userChannelPCOList = userService.getUserChannelPCO(createUserId);
        if (CollectionUtils.isEmpty(userChannelPCOList)) {
            return;
        }
        userChannelPCOList.forEach(authUserVO -> {
            if (Objects.nonNull(authUserVO.getWxOpenId())) {
                WxMpTemplateMessage wxMpTemplateMessage = buildClaimCancelPicPcoMessage(notifyVo, authUserVO.getWxOpenId());
                serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
            }
        });

    }

    /**
     * 对象：总部理赔专员
     *
     * @param claimCancelReportId
     */
    public void sendNotifyMessageToClaimAdmin(Integer claimCancelReportId) {
        log.info("发送注销申请总部审核微信通知：claimCancelReportId：{}", claimCancelReportId);
        //获取渠道总部理赔管理人员信息
        WxClaimCancelReportNotifyVo notifyVo = claimCancelReportMapper.getWxClaimCancelReportNotifyVoByClaimCancelReportId(claimCancelReportId);
        List<UserVO> userVOList = userService.listByClaimAdmin();
        if (CollectionUtils.isEmpty(userVOList)) {
            return;
        }
        userVOList.forEach(userVO -> {
            if (Objects.nonNull(userVO.getWxOpenId())) {
                WxMpTemplateMessage wxMpTemplateMessage = buildClaimCancelAdminMessage(notifyVo, userVO.getWxOpenId());
                serviceProxy.sendTemplateMsg(wxMpTemplateMessage);
            }
        });

    }

    private static String formatInsured(WxClaimCancelReportNotifyVo notifyVO) {
        if (StringUtils.isEmpty(notifyVO.getCustomerAdminName())) {
            return notifyVO.getCustomerName();
        }
        return String.format("%s（%s提交）", notifyVO.getCustomerName(), notifyVO.getCustomerAdminName());
    }



    private static String formatBuildClaimCancelBeforeMessage(String customerAdminName, String customerName) {
        if (StringUtils.isEmpty(customerAdminName)) {
            return customerName;
        }
        return String.format("%s（%s提交）", customerName, customerAdminName);
    }


    /**
     * 对象：总部理赔专员
     * 理赔进度通知
     * ———————————————————————————————————
     * 【小鲸向海】提醒您，钟小福的客户李乐乐 申请注销报案，注销原因：客户主动放弃了索赔
     * 理赔状态：注销报案审核
     * 报案号：xxxxxx
     * 备注：请前往保险业务系统审核相关案件
     * ———————————————————————————————————
     * 查看详情
     *
     * @param notifyVo
     * @param wxOpenId
     */
    private WxMpTemplateMessage buildClaimCancelAdminMessage(WxClaimCancelReportNotifyVo notifyVo, String wxOpenId) {
        Integer claimCancelReportId = notifyVo.getId();
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailCancelUrl(claimCancelReportId, "auditMaterial", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(wxOpenId);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，%s的客户%s申请注销报案，注销原因：%s\r\n", notifyVo.getCreateUser(), notifyVo.getCustomerName(), notifyVo.getCancelReportCause()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVo)));
        wtm.addData(new WxMpTemplateData("keyword2", "注销报案审核", "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", "请前往保险业务系统审核相关案件", "#030303"));
        return wtm;
    }

    /**
     * 理赔进度通知
     * ———————————————————————————————————
     * 【小鲸向海】提醒您，钟小福的客户李乐乐 申请注销报案，注销原因：客户主动放弃了索赔
     * 理赔状态：注销报案审核
     * 报案号：xxxxxx
     * 备注：如对案件由任何疑问，请联系此案的理赔专员某某。
     *
     * @param notifyVo
     * @param wxOpenId
     */
    private WxMpTemplateMessage buildClaimCancelPicPcoMessage(WxClaimCancelReportNotifyVo notifyVo, String wxOpenId) {
        Integer claimCancelReportId = notifyVo.getId();

        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailCancelUrl(claimCancelReportId, "auditMaterial", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(wxOpenId);
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，%s的客户%s申请注销报案，注销原因：%s\r\n", notifyVo.getCreateUser(), notifyVo.getCustomerName(), notifyVo.getCancelReportCause()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVo)));
        wtm.addData(new WxMpTemplateData("keyword2", "注销报案审核", "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", String.format("如对案件由任何疑问，请联系此案件的理赔专员 %s " , (notifyVo.getSettlement() == null ? "" : notifyVo.getSettlement())), "#030303"));
        return wtm;
    }

    /**
     * 注销申请被驳回-提醒管护客户经理
     * ————————————————————————————————————————————
     * 【小鲸向海】提醒您，您的客户某某的注销报案申请被驳回。
     * 理赔状态：注销报案被驳回。驳回理由：证明材料不清晰。
     * 报案号：xxxxxx
     * 备注：超过x天未处理，系统将自动撤回注销报案申请，请及时处理。
     *
     * @param claimCancelReportId
     * @return
     */
    protected WxMpTemplateMessage buildClaimCancelRejectMessage(int claimCancelReportId, String rejectReason) {
        WxClaimCancelReportNotifyVo notifyVo = claimCancelReportMapper.getWxClaimCancelReportNotifyVoByClaimCancelReportId(claimCancelReportId);
        WxMpTemplateMessage wtm = new WxMpTemplateMessage();
        wtm.setUrl(getBackClaimDetailCancelUrl(claimCancelReportId, "schedule", "cm"));
        wtm.setTemplateId(templateId);
        wtm.setToUser(notifyVo.getCreateUserWxOpenId());
//        wtm.addData(new WxMpTemplateData("first", String.format("【小鲸向海】提醒您，您的客户 %s 的注销报案申请被驳回。\r\n", notifyVo.getCustomerName()), "#030303"));
        wtm.addData(new WxMpTemplateData("keyword1", formatInsured(notifyVo)));
        wtm.addData(new WxMpTemplateData("keyword2", String.format("注销报案被驳回。驳回理由：%s", rejectReason), "#0000FF"));
//        wtm.addData(new WxMpTemplateData("remark", String.format("超过%s天未处理，系统将自动撤回注销报案申请，请及时处理", overdueDay), "#030303"));
        return wtm;
    }

    /**
     * 获取微信理赔详情进度页URL
     *
     * @return
     */
    public String getBackClaimDetailCancelUrl(int claimCancelReportId, String type, String role) {
        try {
            return String.format(wechatConfig.getBackJumpUrl(), URLEncoder.encode(wechatConfig.getBackClaimDetailCancelUrl() + "?claimCancelReportId=" + claimCancelReportId + "&type=" + type + "&role=" + role, StandardCharsets.UTF_8.name()), "");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }
}
