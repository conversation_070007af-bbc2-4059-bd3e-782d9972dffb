package com.cfpamf.ms.insur.admin.service.order.syn;

import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.enums.commission.CommissionConfigTypeEnum;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.model.InsuredInfoList;
import com.cfpamf.ms.insur.admin.external.whale.model.ProductInfoList;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCarOrderExcelDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CalcCommissionItemResultDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.SmCarOrderManageService;
import com.cfpamf.ms.insur.admin.service.commission.CommissionManagerService;
import com.cfpamf.ms.insur.admin.util.AssertUtills;
import com.cfpamf.ms.insur.common.enums.EnumMonitor;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: yangdonglin
 * @create: 2024/1/19 16:44
 * @description: 同步佣金信息
 */
@Service
@Slf4j
public class SynCommissionInfoService {
    @Autowired
    private CommissionManagerService commissionManagerService;
    @Autowired
    private SmCarOrderManageService smCarOrderManageService;
    @Autowired
    private SmOrderMapper smOrderMapper;
    @Autowired
    private WhaleOrderServiceAdapterImpl adapter;
    @Autowired
    private SmOrderInsuredMapper smOrderInsuredMapper;
    @Autowired
    private SmOrderDDDMapper orderMapper;
    @Autowired
    private BusinessMonitorService businessMonitorService;
    /**
     * 处理佣金信息
     *
     * @param whaleContract 小鲸信息
     * @param fhOrderId     订单ID
     * @return 处理结果
     */
    public boolean whaleSynCommissionInfo(final WhaleContract whaleContract, final String fhOrderId) {
        log.info("开始一单一议逻辑{}", fhOrderId);
        if (Objects.isNull(whaleContract)) {
            return false;
        }
        if (CollectionUtils.isEmpty(whaleContract.getInsuredInfoList())) {
            return false;
        }
        final List<SmCarOrderExcelDTO> applyOrder = new ArrayList<>();
        // 获取合约中所有的结算比率
        final List<String> settlementRatios = whaleContract
                .getInsuredInfoList()
                .stream()
                .map(InsuredInfoList::getProductInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(ProductInfoList::getSettlementRatio)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        // 获取合约中所有的支出比率
        final List<String> expenditureRatios = whaleContract
                .getInsuredInfoList()
                .stream()
                .map(InsuredInfoList::getProductInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(ProductInfoList::getExpenditureRatio)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(settlementRatios) && CollectionUtils.isEmpty(expenditureRatios)) {
            log.info("非一单一议，不需要特殊处理佣金");
            return false;
        }
        AssertUtills.isTrue(CollectionUtils.isNotEmpty(settlementRatios), "结算比例为空，无法同步");
        AssertUtills.isTrue(CollectionUtils.isNotEmpty(expenditureRatios), "支出比例为空，无法同步");
        AssertUtills.isTrue(settlementRatios.size() == 1, "存在多个结算比例，无法同步");
        AssertUtills.isTrue(expenditureRatios.size() == 1, "存在多个支出比例，无法同步");
        log.info("开始处理一单一议数据{}", fhOrderId);
        // 查询当前订单信息
        final SmOrder thisSmOrder = smOrderMapper.queryByOrderId(fhOrderId);
        // 计算合约中的保费
        final BigDecimal premium = whaleContract
                .getInsuredInfoList()
                .stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getProductInfoList()))
                .map(InsuredInfoList::getProductInfoList)
                .flatMap(List::stream)
                .map(e -> {
                    if (Objects.nonNull(e.getPremium())) {
                        log.info("保费为空{}", e);
                        AssertUtills.isTrue(Objects.nonNull(e.getPremium()),
                                "存在保费为空的情况，无法同步");
                    }
                    return e.getPremium();
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        AssertUtills.notNull(whaleContract.getContractBaseInfo(), "保单基本信息为空");
        final SmCarOrderExcelDTO smCarOrderExcelDTO = new SmCarOrderExcelDTO();
        // 设置订单相关信息
        smCarOrderExcelDTO.setOrderNo(fhOrderId);
        smCarOrderExcelDTO.setPolicyNo(whaleContract.getContractBaseInfo().getPolicyNo());
        smCarOrderExcelDTO.setInsuredIdNumber(whaleContract
                .getInsuredInfoList().get(0).getInsuredIdCard());
        smCarOrderExcelDTO.setPlanId(thisSmOrder.getPlanId());
        smCarOrderExcelDTO.setAppStatus("");
        smCarOrderExcelDTO.setPremium(premium);
        smCarOrderExcelDTO.setRecommendUserId(thisSmOrder.getCustomerAdminId());
        smCarOrderExcelDTO.setPaymentRate(new BigDecimal(expenditureRatios.get(0)));
        smCarOrderExcelDTO.setSettlementRate(new BigDecimal(settlementRatios.get(0)));
        final List<CalcCommissionItemResultDTO> itemDTOList = Lists.newArrayList();
        applyOrder.add(smCarOrderExcelDTO);
        for (SmCarOrderExcelDTO order : applyOrder) {
            // 添加支付佣金
            itemDTOList.add(smCarOrderManageService.doCreateNewCommissionItemResult(order, CommissionConfigTypeEnum.PAYMENT.getCode(), order.getPaymentRate()));
            // 添加结算佣金
            itemDTOList.add(smCarOrderManageService.doCreateNewCommissionItemResult(order, CommissionConfigTypeEnum.SETTLEMENT.getCode(), order.getSettlementRate()));
        }
        AssertUtills.isTrue(CollectionUtils.isNotEmpty(itemDTOList),
                "佣金记录为空");
        // 计算佣金
        commissionManagerService.calcCarOrderImportCommission(itemDTOList);
        log.info("一单一议，特殊处理佣金成功{}", fhOrderId);
        return true;
    }

    /**
     * 判断是否为一单一议的保单
     * @param whaleContract 保单对象
     * @return 是否为一单一议的保单
     */
    public boolean whaleSynIsASingleProposal(final WhaleContract whaleContract) {
        if (Objects.isNull(whaleContract)) {
            return false;
        }
        if (CollectionUtils.isEmpty(whaleContract.getInsuredInfoList())) {
            return false;
        }
        // 获取合约中所有的结算比率
        final List<String> settlementRatios = whaleContract
                .getInsuredInfoList()
                .stream()
                .map(InsuredInfoList::getProductInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(ProductInfoList::getSettlementRatio)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        // 获取合约中所有的支出比率
        final List<String> expenditureRatios = whaleContract
                .getInsuredInfoList()
                .stream()
                .map(InsuredInfoList::getProductInfoList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .map(ProductInfoList::getExpenditureRatio)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(settlementRatios) && CollectionUtils.isEmpty(expenditureRatios)) {
            return false;
        }
        return true;
    }

    /**
     * 同步鲸鱼的佣金任务
     * @param whaleCodes 鲸鱼代码列表
     */
    public void synWhaleSynCommissionJob(final List<String> whaleCodes){
        if (CollectionUtils.isEmpty(whaleCodes)) {
            // 如果合同编码为空
            log.info("合同编号为空{}", whaleCodes);
            XxlJobHelper.log("合同编号为空{}", whaleCodes);
            return;
        }
        for (String code : whaleCodes) {
            try {
                // 查询小鲸策略
                final WhaleResp<WhaleContract> policy = adapter.getPolicyNo(code, "CREATE");
                if (Objects.isNull(policy) || Objects.isNull(policy.getData())) {
                    // 如果查询小鲸返回为空
                    log.info("查询小鲸返回为空{}", code);
                    XxlJobHelper.log("查询小鲸返回为空{}", code);
                    continue;
                }
                final WhaleContract policyNo = policy.getData();
                if (Objects.isNull(policyNo.getContractBaseInfo()) || StringUtils.isBlank(policyNo.getContractBaseInfo().getPolicyNo())) {
                    // 如果保单信息为空
                    log.info("保单信息为空{}", code);
                    XxlJobHelper.log("保单信息为空{}", code);
                    continue;
                }
                String policyNoCode = policyNo.getContractBaseInfo().getPolicyNo();
                // 查询被保人信息
                final SmOrderInsured qryInsured = new SmOrderInsured();
                qryInsured.setPolicyNo(policyNoCode);
                final List<String> fhOrderIds = Optional.ofNullable(smOrderInsuredMapper.select(qryInsured))
                        .filter(CollectionUtils::isNotEmpty)
                        .map(e -> e.stream().map(SmOrderInsured::getFhOrderId)
                                .filter(StringUtils::isNotBlank)
                                .distinct()
                                .collect(Collectors.toList())).orElse(new ArrayList<>());
                if (fhOrderIds.size() != 1) {
                    // 如果农保订单信息不唯一
                    log.info("农保订单信息不唯一{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final String fhOrderId = fhOrderIds.get(0);
                if (StringUtils.isBlank(fhOrderId)) {
                    // 如果农保订单号为空
                    log.info("农保订单号为空{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final SmOrder smOrder = new SmOrder();
                smOrder.setFhOrderId(fhOrderId);
                final List<SmOrder> smOrders = orderMapper.select(smOrder);
                if (CollectionUtils.isEmpty(smOrders) || smOrders.size() != 1 || Objects.isNull(smOrders.get(0))) {
                    // 如果农保订单信息不唯一
                    log.info("农保订单信息不唯一{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final SmOrder thisSmOrder = smOrders.get(0);
                final SmPlanVO planVO = adapter.convertPlan(policyNo);
                if (Objects.isNull(planVO)) {
                    // 如果小鲸通知产品计划未配置
                    log.info("小鲸通知产品计划未配置{}", code);
                    businessMonitorService.addMonitor(EnumMonitor.WHALE_POLICY_RISK_UNKNOWN,"小鲸通知产品计划未配置:"+code);
                    continue;
                }
                whaleSynCommissionInfo(policyNo,fhOrderId);
                // 更新成功
                log.info("更新成功{}", code);
                XxlJobHelper.log("更新成功{}", code);
            } catch (Exception e) {
                // 更新失败
                log.info("更新失败{},{}", code, e);
                XxlJobHelper.log("更新失败{},{}", code, e);
            }
        }
    }
}
