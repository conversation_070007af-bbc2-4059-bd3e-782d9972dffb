package com.cfpamf.ms.insur.admin.service.product.impl;

import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductVersionMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.*;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskAmountMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyAmountMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskFactorFileMapper;
import com.cfpamf.ms.insur.admin.enums.product.EnumRiskType;
import com.cfpamf.ms.insur.admin.pojo.form.product.SmPlanForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.SmPlanRiskDutyForm;
import com.cfpamf.ms.insur.admin.pojo.form.product.SmPlanRiskForm;
import com.cfpamf.ms.insur.admin.pojo.po.product.*;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskAmount;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskDutyAmount;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskFactorFile;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.SmLongInsurancePlanVo;
import com.cfpamf.ms.insur.admin.pojo.vo.product.SmPlanRiskDutyVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.SmPlanRiskVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.SmProductRiskVO;
import com.cfpamf.ms.insur.admin.service.product.SmLongInsurancePlanService;
import com.cfpamf.ms.insur.admin.service.product.SmProductRiskService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/27 19:48
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SmLongInsurancePlanServiceImpl implements SmLongInsurancePlanService {

    public static final int AMOUNT_TYPE_DIY = 2;
    public static final int AMOUNT_TYPE_SAME_RISK = 1;
    SmProductMapper smProductMapper;
    SmProductHistoryMapper smProductHistoryMapper;

    SmProductRiskService smProductRiskService;

    PlanMapper planMapper;

    SmPlanHistoryMapper smPlanHistoryMapper;

    SysRiskDutyMapper sysRiskDutyMapper;

    SmProductVersionMapper productVersionMapper;

    SmPlanRiskHistoryMapper smPlanRiskHistoryMapper;

    SmPlanRiskMapper smPlanRiskMapper;

    SmPlanRiskDutyHistoryMapper smPlanRiskDutyHistoryMapper;

    SmPlanRiskDutyMapper smPlanRiskDutyMapper;

    SysRiskDutyAmountMapper sysRiskDutyAmountMapper;

    SysRiskAmountMapper sysRiskAmountMapper;

    SysRiskFactorFileMapper sysRiskFactorFileMapper;

    public SmLongInsurancePlanServiceImpl(SmProductMapper smProductMapper, SmProductHistoryMapper smProductHistoryMapper, SmProductRiskService smProductRiskService, PlanMapper planMapper, SmPlanHistoryMapper smPlanHistoryMapper, SysRiskDutyMapper sysRiskDutyMapper, SmProductVersionMapper productVersionMapper, SmPlanRiskHistoryMapper smPlanRiskHistoryMapper, SmPlanRiskMapper smPlanRiskMapper, SmPlanRiskDutyHistoryMapper smPlanRiskDutyHistoryMapper, SmPlanRiskDutyMapper smPlanRiskDutyMapper, SysRiskDutyAmountMapper sysRiskDutyAmountMapper, SysRiskAmountMapper sysRiskAmountMapper, SysRiskFactorFileMapper sysRiskFactorFileMapper) {
        this.smProductMapper = smProductMapper;
        this.smProductHistoryMapper = smProductHistoryMapper;
        this.smProductRiskService = smProductRiskService;
        this.planMapper = planMapper;
        this.smPlanHistoryMapper = smPlanHistoryMapper;
        this.sysRiskDutyMapper = sysRiskDutyMapper;
        this.productVersionMapper = productVersionMapper;
        this.smPlanRiskHistoryMapper = smPlanRiskHistoryMapper;
        this.smPlanRiskMapper = smPlanRiskMapper;
        this.smPlanRiskDutyHistoryMapper = smPlanRiskDutyHistoryMapper;
        this.smPlanRiskDutyMapper = smPlanRiskDutyMapper;
        this.sysRiskDutyAmountMapper = sysRiskDutyAmountMapper;
        this.sysRiskAmountMapper = sysRiskAmountMapper;
        this.sysRiskFactorFileMapper = sysRiskFactorFileMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer save(int productId, SmPlanForm smPlanForm) {
        Integer maxVersion = productVersionMapper.getMaxVersion(productId);
        maxVersion = maxVersion == 0 ? 1 : maxVersion;
        SmProductDetailVO productDetailVO = smProductHistoryMapper.getProductById(productId, maxVersion);
        if (Objects.isNull(productDetailVO)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品不存在");
        }
        //校验险种编码重复
        checkPlan(smPlanForm,null);
        List<SmPlanRiskForm> planRiskList = smPlanForm.getPlanRiskList();
        Integer apiType = productDetailVO.getApiType();
        String userId = HttpRequestUtil.getUserId();
        //险种配置为空
        if (CollectionUtils.isEmpty(planRiskList)) {
            if (Objects.equals(apiType, SmConstants.PRODUCT_API_TYPE_API)) {
                //产品为API对接方式的时候 险种为必填
                throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，险种选择为必填");
            }
            //保存计划
            return saveSmPlanHistory(productId, smPlanForm, userId).getPlanId();
        }
        //校验责任
        checkDuty(productId, planRiskList);

        //保存计划
        SmPlanHistory smPlanHistory = saveSmPlanHistory(productId, smPlanForm, userId);

        saveRiskAndDuty(productId, planRiskList, userId, smPlanHistory.getPlanId(), smPlanHistory.getVersion());
        //更新产品最低保费
        updateProductMinAmount(productId, smPlanHistory.getVersion(), userId);
        return smPlanHistory.getPlanId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(int planId, SmPlanForm smPlanForm) {
        SmPlan smPlan = planMapper.selectByPrimaryKey(planId);
        if (Objects.isNull(smPlan)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品计划不存在");
        }
        int productId = smPlan.getProductId().intValue();
        SmProductDetailVO productDetailVO = smProductMapper.getProductById(productId);
        if (Objects.isNull(productDetailVO)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品不存在");
        }
        //校验险种编码重复
        checkPlan(smPlanForm, Collections.singletonList(productId));
        List<SmPlanRiskForm> planRiskList = smPlanForm.getPlanRiskList();
        Integer apiType = productDetailVO.getApiType();
        String userId = HttpRequestUtil.getUserId();
        int productVersion = getProductUpdateVersion(productId);

        //险种配置为空
        if (CollectionUtils.isEmpty(planRiskList)) {
            if (Objects.equals(apiType, SmConstants.PRODUCT_API_TYPE_API)) {
                //产品为API对接方式的时候 险种为必填
                throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品对接方式为api时，险种选择为必填");
            }
        }
        updatePlan(planId, smPlanForm, productId, userId, productVersion);
        if (CollectionUtils.isEmpty(planRiskList)) {
            return;
        }
        //校验责任
        checkDuty(productId, planRiskList);

        //删除之前的计划险种配置
        SmPlanRiskHistory deleteSmPlanRiskHistory = new SmPlanRiskHistory();
        deleteSmPlanRiskHistory.setPlanId(planId);
        deleteSmPlanRiskHistory.setVersion(productVersion);
        smPlanRiskHistoryMapper.delete(deleteSmPlanRiskHistory);
        SmPlanRisk deleteSmPlanRisk = new SmPlanRisk();
        deleteSmPlanRisk.setPlanId(planId);
        smPlanRiskMapper.delete(deleteSmPlanRisk);
        //删除之前的计划险种责任配置
        SmPlanRiskDuty deletePlanRiskDuty = new SmPlanRiskDuty();
        deletePlanRiskDuty.setPlanId(planId);
        smPlanRiskDutyMapper.delete(deletePlanRiskDuty);
        SmPlanRiskDutyHistory deletePlanRiskDutyHistory = new SmPlanRiskDutyHistory();
        deletePlanRiskDutyHistory.setPlanId(planId);
        deletePlanRiskDutyHistory.setVersion(productVersion);
        smPlanRiskDutyHistoryMapper.delete(deletePlanRiskDutyHistory);
        //保存信的计划险种配置
        saveRiskAndDuty(productId, planRiskList, userId, planId, productVersion);

        //更新产品最低保费
        updateProductMinAmount(productId, productVersion, userId);
    }

    /**
     * 获取产品修改版本
     *
     * @param productId
     * @return
     */
    public int getProductUpdateVersion(int productId) {
        return productVersionMapper.getMaxVersion(productId) + 1;
    }


    /**
     * 更新计划
     *
     * @param planId
     * @param smPlanForm
     * @param productId
     * @param userId
     * @param productNewestVersion
     */
    private void updatePlan(int planId, SmPlanForm smPlanForm, int productId, String userId, int productNewestVersion) {
        //更新计划
        SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setProductId(productId);
        smPlanHistory.setPlanName(smPlanForm.getPlanName());
        smPlanHistory.setPlanCode(smPlanForm.getPlanCode());
        smPlanHistory.setFhProductId(smPlanForm.getPlanCode());
        smPlanHistory.setPlanType(smPlanForm.getPlanType());
        smPlanHistory.setMinPremium(smPlanForm.getMinPremium());
        smPlanHistory.setPlanId(planId);
        smPlanHistory.setUpdateBy(userId);
        smPlanHistory.setVersion(productNewestVersion);
        smPlanHistoryMapper.updateByPlanIdAndVersion(smPlanHistory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(int planId) {
        SmPlan smPlan = planMapper.selectByPrimaryKey(planId);
        if (Objects.isNull(smPlan)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品计划不存在");
        }
        int productId = smPlan.getProductId().intValue();
        SmProductDetailVO productDetailVO = smProductMapper.getProductById(productId);
        if (Objects.isNull(productDetailVO)) {
            return;
        }
        //删除计划历史快照
        int productNewestVersion = getProductUpdateVersion(productId);
        smPlanHistoryMapper.softDelete(planId, productNewestVersion);
        //删除计划险种
        smPlanRiskHistoryMapper.softDelete(planId, productNewestVersion);
        //删除计划险种
        smPlanRiskDutyHistoryMapper.softDelete(planId, productNewestVersion);
    }

    @Override
    public SmLongInsurancePlanVo detail(int planId, int productId) {
        SmProductDetailVO productDetailVO = smProductMapper.getProductById(productId);
        if (Objects.isNull(productDetailVO)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品不存在");
        }
        int productNewestVersion = getProductUpdateVersion(productId);
        //查找产品计划
        SmLongInsurancePlanVo smLongInsurancePlanVo = smPlanHistoryMapper.getSmLongInsurancePlanVoByPlanId(planId, productNewestVersion);
        //设置产品计划的险种和责任信息
        setSmLongInsurancePlanRiskDuty(productNewestVersion, Lists.newArrayList(smLongInsurancePlanVo));
        return smLongInsurancePlanVo;
    }

    @Override
    public List<SmLongInsurancePlanVo> getPlanList(int productId) {
        SmProductDetailVO productDetailVO = smProductMapper.getProductById(productId);
        if (Objects.isNull(productDetailVO)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品不存在");
        }
        int productNewestVersion = getProductUpdateVersion(productId);
        return getPlanList(productId, productNewestVersion);
    }

    @Override
    public List<SmLongInsurancePlanVo> getPlanListByVersion(int productId,int version) {
        return getPlanList(productId, version);
    }

    @Override
    public List<SmLongInsurancePlanVo> getPlanList(int productId, Integer version) {
        SmProductDetailVO productDetailVO = smProductMapper.getProductById(productId);
        if (Objects.isNull(productDetailVO)) {
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品不存在");
        }
        if (Objects.isNull(version)) {
            version = getProductUpdateVersion(productId);
        }
        //获取产品计划集合
        List<SmLongInsurancePlanVo> planList = smPlanHistoryMapper.getSmLongInsurancePlanVo(productId, version);
        setSmLongInsurancePlanRiskDuty(version, planList);
        return planList;
    }

    /**
     * 更新产品最低保费
     *
     * @param productId
     * @param version
     * @param updateBy
     */
    public void updateProductMinAmount(int productId, int version, String updateBy) {
        BigDecimal minAmount = smPlanHistoryMapper.selectMinAmount(productId, version);
        smProductHistoryMapper.updateProductMinAmount(productId, minAmount, updateBy, version);
    }

    /**
     * 设置计划的险种和责任信息
     *
     * @param productNewestVersion
     * @param planList
     */
    private void setSmLongInsurancePlanRiskDuty(int productNewestVersion, List<SmLongInsurancePlanVo> planList) {
        if (CollectionUtils.isEmpty(planList)) {
            return;
        }
        List<Long> planIdList = planList.stream()
                .map(SmLongInsurancePlanVo::getPlanId)
                .collect(Collectors.toList());
        //获取计划的险种集合
        List<SmPlanRiskVO> smPlanRiskList = getSmPlanRiskList(productNewestVersion, planIdList);
        if (CollectionUtils.isEmpty(smPlanRiskList)) {
            return;
        }
        //设置计划险种的责任
        setPlanRiskDuty(productNewestVersion, smPlanRiskList);

        //设置计划的计划险种
        Map<Long, List<SmPlanRiskVO>> smPlanRiskMap = smPlanRiskList.stream()
                .collect(Collectors.groupingBy(SmPlanRiskVO::getPlanId));
        for (SmLongInsurancePlanVo smLongInsurancePlanVo : planList) {
            smLongInsurancePlanVo.setPlanRiskList(smPlanRiskMap.getOrDefault(smLongInsurancePlanVo.getPlanId(), Collections.emptyList()));
        }
    }

    /**
     * 获取计划险种信息
     *
     * @param productNewestVersion
     * @param planIdList
     * @return
     */
    @Override
    public List<SmPlanRiskVO> getSmPlanRiskList(int productNewestVersion, List<Long> planIdList) {
        List<SmPlanRiskVO> smPlanRiskList = smPlanRiskHistoryMapper.getSmPlanRiskList(planIdList, productNewestVersion);
        if (CollectionUtils.isNotEmpty(smPlanRiskList)) {
            smPlanRiskList.forEach(smPlanRiskVO -> {
                Integer riskType = smPlanRiskVO.getRiskType();
                smPlanRiskVO.setRiskTypeName(EnumRiskType.ADDITION.getDesc(riskType));
            });
        }
        return smPlanRiskList;
    }

    @Override
    public void pushNewVersion(int productId, int version) {
        //更新计划险种数据
        smPlanRiskMapper.pushNewVersion(productId, version);
        //初始化计划险种新版本的快照数据
        smPlanRiskHistoryMapper.initNewHistory(productId, version + 1);
        //更新计划险种数据
        smPlanRiskDutyMapper.pushNewVersion(productId, version);
        //初始化计划险种新版本的快照数据
        smPlanRiskDutyHistoryMapper.initNewHistory(productId, version + 1);

    }

    /**
     * 设置计划险种的责任
     *
     * @param productNewestVersion
     * @param smPlanRiskList
     */
    private void setPlanRiskDuty(int productNewestVersion, List<SmPlanRiskVO> smPlanRiskList) {
        List<Integer> planRiskIdList = smPlanRiskList.stream()
                .map(SmPlanRiskVO::getPlanRiskId)
                .collect(Collectors.toList());
        //获取险种集合的险种责任集合
        List<SmPlanRiskDutyVO> planRiskDutyList = getPlanRiskDutyList(productNewestVersion, planRiskIdList);
        Map<Integer, List<SmPlanRiskDutyVO>> smPlanRiskDutyMap = planRiskDutyList.stream()
                .collect(Collectors.groupingBy(SmPlanRiskDutyVO::getSmPlanRiskId));
        //获取险种的费率文件
        List<Integer> sysRiskIdList = smPlanRiskList.stream()
                .map(SmPlanRiskVO::getSysRiskId)
                .collect(Collectors.toList());
        Map<Integer, SysRiskFactorFile> sysRiskFactorFileMap = sysRiskFactorFileMapper.selectByRiskIdList(sysRiskIdList)
                .stream()
                .collect(Collectors.toMap(SysRiskFactorFile::getTargetId, e -> e));
        //设置计划险种的计划险种责任
        for (SmPlanRiskVO smPlanRiskVO : smPlanRiskList) {
            List<SmPlanRiskDutyVO> riskDutyVOList = smPlanRiskDutyMap.getOrDefault(smPlanRiskVO.getPlanRiskId(), Lists.newArrayList());
            riskDutyVOList.sort(Comparator.comparingInt(SmPlanRiskDutyVO::getDisplaySort));
            smPlanRiskVO.setPlanRiskDutyList(riskDutyVOList);
            //责任费率文件的优先级大于责任费率文件
            SysRiskFactorFile sysRiskFactorFile = sysRiskFactorFileMap.get(smPlanRiskVO.getSysRiskId());
            for (SmPlanRiskDutyVO smPlanRiskDutyVO : smPlanRiskVO.getPlanRiskDutyList()) {
                smPlanRiskDutyVO.setFile(sysRiskFactorFile);
            }
        }
    }

    /**
     * 获取计划险种责任集合
     *
     * @param productNewestVersion
     * @param planRiskIdList
     * @return
     */
    private List<SmPlanRiskDutyVO> getPlanRiskDutyList(int productNewestVersion, List<Integer> planRiskIdList) {
        //获取险种责任集合
        List<SmPlanRiskDutyVO> smPlanRiskDutyList = smPlanRiskDutyHistoryMapper.getSmPlanRiskDutyList(planRiskIdList, productNewestVersion);
        //获取责任费率文件集合
        List<Integer> sysRiskDutyId = smPlanRiskDutyList.stream()
                .map(SmPlanRiskDutyVO::getSysRiskDutyId)
                .collect(Collectors.toList());
        Map<Integer, SysRiskFactorFile> sysRiskFactorFileMap = sysRiskFactorFileMapper.selectByDutyIdList(sysRiskDutyId)
                .stream().collect(Collectors.toMap(SysRiskFactorFile::getTargetId, e -> e));
        //获取险种责任金额集合
        List<Integer> riskDutyAmountIdList = Lists.newArrayList();
        for (SmPlanRiskDutyVO smPlanRiskDutyVO : smPlanRiskDutyList) {
            List<Integer> dutyAmountIdList = smPlanRiskDutyVO.getRiskDutyAmountIdList();
            riskDutyAmountIdList.addAll(dutyAmountIdList);
        }
        List<SysRiskDutyAmount> sysRiskDutyAmountList = getSysRiskDutyAmounts(riskDutyAmountIdList);
        List<SysRiskAmount> riskAmountList = getSysRiskAmounts(riskDutyAmountIdList);
        //险种责任集合设置责任金额对象和文件对象
        for (SmPlanRiskDutyVO smPlanRiskDutyVO : smPlanRiskDutyList) {
            setAmount(sysRiskDutyAmountList, riskAmountList, smPlanRiskDutyVO);
            smPlanRiskDutyVO.setFile(sysRiskFactorFileMap.get(smPlanRiskDutyVO.getSysRiskDutyId()));
        }
        return smPlanRiskDutyList;
    }

    /**
     * 设置amount
     * 责任保费类型 1-同基本保额 2-自定义 两种类型
     *
     * @param sysRiskDutyAmountList
     * @param riskAmountList
     * @param smPlanRiskDutyVO
     */
    private void setAmount(List<SysRiskDutyAmount> sysRiskDutyAmountList, List<SysRiskAmount> riskAmountList, SmPlanRiskDutyVO smPlanRiskDutyVO) {
        List<Integer> dutyAmountIdList = smPlanRiskDutyVO.getRiskDutyAmountIdList();
        Integer amountType = smPlanRiskDutyVO.getAmountType();
        if (Objects.equals(amountType,AMOUNT_TYPE_DIY)) {
            // 2-自定义
            List<SysRiskDutyAmount> planRiskDutyAmountList = sysRiskDutyAmountList.stream()
                    .filter(sysRiskDutyAmount -> dutyAmountIdList.contains(sysRiskDutyAmount.getId()))
                    .collect(Collectors.toList());

            smPlanRiskDutyVO.setSysRiskDutyAmountList(planRiskDutyAmountList);
        }

        if (Objects.equals(amountType,AMOUNT_TYPE_SAME_RISK)) {
            //1-同基本保额
            List<SysRiskAmount> planRiskAmountList = riskAmountList.stream()
                    .filter(sysRiskDutyAmount -> dutyAmountIdList.contains(sysRiskDutyAmount.getId()))
                    .collect(Collectors.toList());
            smPlanRiskDutyVO.setSysRiskAmountList(planRiskAmountList);
        }
    }

    /**
     * 获取计划险种责任金额集合
     *
     * @param riskDutyAmountIdList
     * @return
     */
    private List<SysRiskDutyAmount> getSysRiskDutyAmounts(List<Integer> riskDutyAmountIdList) {
        if (CollectionUtils.isEmpty(riskDutyAmountIdList)) {
            return Collections.EMPTY_LIST;
        }
        return sysRiskDutyAmountMapper.selectByIds(riskDutyAmountIdList);
    }

    /**
     * 获取计划险种责任金额集合
     *
     * @param riskDutyAmountIdList
     * @return
     */
    private List<SysRiskAmount> getSysRiskAmounts(List<Integer> riskDutyAmountIdList) {

        if (CollectionUtils.isEmpty(riskDutyAmountIdList)) {
            return Collections.EMPTY_LIST;
        }
        return sysRiskAmountMapper.selectByIds(riskDutyAmountIdList);
    }

    /**
     * 保存险种和责任
     *
     * @param productId
     * @param planRiskList
     * @param userId
     * @param planId
     * @param productVersion
     * @return
     */
    public Integer saveRiskAndDuty(int productId, List<SmPlanRiskForm> planRiskList, String userId, Integer planId, Integer productVersion) {
        //保存计划险种和责任
        List<SmPlanRiskDutyHistory> smPlanRiskDutyHistoryList = Lists.newArrayList();
        for (int i = 0; i < planRiskList.size(); i++) {
            SmPlanRiskForm smPlanRiskForm = planRiskList.get(i);
            //保存计划险种
            SmPlanRiskHistory smPlanRiskHistory = savePlanRisk(productId, userId, planId, productVersion, smPlanRiskForm, i + 1);
            List<SmPlanRiskDutyForm> planRiskDuctList = smPlanRiskForm.getPlanRiskDutyList();
            for (SmPlanRiskDutyForm smPlanRiskDutyForm : planRiskDuctList) {
                //保存计划险种责任
                SmPlanRiskDuty smPlanRiskDuty = saveSmPlanRiskDuty(productId, userId, planId, smPlanRiskHistory, smPlanRiskDutyForm);
                SmPlanRiskDutyHistory smPlanRiskDutyHistory = getSmPlanRiskDutyHistory(productVersion, smPlanRiskDuty);
                smPlanRiskDutyHistoryList.add(smPlanRiskDutyHistory);
            }
        }
        if (CollectionUtils.isNotEmpty(smPlanRiskDutyHistoryList)) {
            smPlanRiskDutyHistoryMapper.insertList(smPlanRiskDutyHistoryList);
        }
        return planId;
    }

    /**
     * 获取计划险种责任历史对象
     *
     * @param productVersion
     * @param smPlanRiskDuty
     * @return
     */
    private SmPlanRiskDutyHistory getSmPlanRiskDutyHistory(Integer productVersion, SmPlanRiskDuty smPlanRiskDuty) {
        SmPlanRiskDutyHistory smPlanRiskDutyHistory = new SmPlanRiskDutyHistory();
        BeanUtils.copyProperties(smPlanRiskDuty, smPlanRiskDutyHistory);
        smPlanRiskDutyHistory.setPlanRiskDutyId(smPlanRiskDuty.getId());
        smPlanRiskDutyHistory.setEnabledFlag(0);
        smPlanRiskDutyHistory.setVersion(productVersion);
        return smPlanRiskDutyHistory;
    }

    /**
     * 保存计划险种责任
     *
     * @param productId
     * @param userId
     * @param planId
     * @param smPlanRiskHistory
     * @param smPlanRiskDutyForm
     * @return
     */
    private SmPlanRiskDuty saveSmPlanRiskDuty(int productId, String userId, Integer planId, SmPlanRiskHistory smPlanRiskHistory, SmPlanRiskDutyForm smPlanRiskDutyForm) {
        SmPlanRiskDuty smPlanRiskDuty = new SmPlanRiskDuty();
        smPlanRiskDuty.setPlanId(planId);
        smPlanRiskDuty.setProductId(productId);
        smPlanRiskDuty.setSmPlanRiskId(smPlanRiskHistory.getPlanRiskId());
        smPlanRiskDuty.setDutyKey(smPlanRiskDutyForm.getDutyKey());
        smPlanRiskDuty.setDutyVersion(smPlanRiskHistory.getRiskVersion());
        smPlanRiskDuty.setAmountText(smPlanRiskDutyForm.getAmountText());
        smPlanRiskDuty.setRiskDutyAmountIdArr(smPlanRiskDutyForm.getRiskDutyAmountIdArr());
        smPlanRiskDuty.setIncludedRiskInsuredAmount(smPlanRiskDutyForm.getIncludedRiskInsuredAmount());
        smPlanRiskDuty.setMandatory(smPlanRiskDutyForm.getMandatory());
        smPlanRiskDuty.setEnabledFlag(1);
        smPlanRiskDuty.setCreateBy(userId);
        smPlanRiskDuty.setUpdateBy(userId);
        smPlanRiskDutyMapper.insertUseGeneratedKeys(smPlanRiskDuty);

        return smPlanRiskDuty;
    }

    /**
     * 保存计划险种
     *
     * @param productId
     * @param userId
     * @param planId
     * @param productVersion
     * @param smPlanRiskForm
     * @param sortField
     */
    private SmPlanRiskHistory savePlanRisk(int productId, String userId, Integer planId, Integer productVersion, SmPlanRiskForm smPlanRiskForm, int sortField) {
        SmPlanRisk smPlanRisk = new SmPlanRisk();
        smPlanRisk.setPlanId(planId);
        smPlanRisk.setRiskKey(smPlanRiskForm.getRiskKey());
        smPlanRisk.setRiskVersion(smPlanRiskForm.getRiskVersion());
        smPlanRisk.setProductId(productId);
        smPlanRisk.setIncludedTotalInsuredAmount(smPlanRiskForm.getIncludedTotalInsuredAmount());
        smPlanRisk.setCreateBy(userId);
        smPlanRisk.setUpdateBy(userId);
        smPlanRisk.setEnabledFlag(1);
        smPlanRisk.setSortField(sortField);
        smPlanRiskMapper.insertUseGeneratedKeys(smPlanRisk);


        SmPlanRiskHistory smPlanRiskHistory = new SmPlanRiskHistory();
        smPlanRiskHistory.setPlanId(planId);
        smPlanRiskHistory.setProductId(productId);
        smPlanRiskHistory.setRiskKey(smPlanRiskForm.getRiskKey());
        smPlanRiskHistory.setRiskVersion(smPlanRiskForm.getRiskVersion());
        smPlanRiskHistory.setIncludedTotalInsuredAmount(smPlanRiskForm.getIncludedTotalInsuredAmount());
        smPlanRiskHistory.setCreateBy(userId);
        smPlanRiskHistory.setUpdateBy(userId);
        smPlanRiskHistory.setPlanRiskId(smPlanRisk.getId());
        smPlanRiskHistory.setVersion(productVersion);
        smPlanRiskHistory.setSortField(sortField);
        smPlanRiskHistoryMapper.insert(smPlanRiskHistory);
        return smPlanRiskHistory;
    }

    /**
     * 校验责任
     * 1.产品计划必选险种
     * 2.计划配置的险种是否存在
     * 3.计划配置的险种责任是否存在
     *
     * @param productId
     * @param planRiskList
     */
    private void checkDuty(int productId, List<SmPlanRiskForm> planRiskList) {
        //获取产品的险种库
        List<SmProductRiskVO> productRiskList = smProductRiskService.getProductRisk(productId);
        Map<String, SmProductRiskVO> smProductRiskMap = productRiskList.stream()
                .collect(Collectors.toMap(
                        smProductRiskVO -> smProductRiskVO.getRiskKey() + File.pathSeparator + smProductRiskVO.getVersion(),
                        smProductRiskVO -> smProductRiskVO
                ));
        //获取计划表单的险种配置
        Map<String, SmPlanRiskForm> smPlanRiskFormMap = planRiskList.stream()
                .collect(Collectors.toMap(
                        smPlanRiskForm -> smPlanRiskForm.getRiskKey() + File.pathSeparator + smPlanRiskForm.getRiskVersion(),
                        smPlanRiskForm -> smPlanRiskForm));
        Set<String> smPlanRiskFormMapKeyList = smPlanRiskFormMap.keySet();
        //校验产品必选的险种是被选中
//        productRiskList.stream()
//                .filter(SmProductRiskVO::isRequiredRisk)
//                .forEach(smProductRiskVO -> {
//                    String riskKey = smProductRiskVO.getRiskKey();
//                    Integer version = smProductRiskVO.getVersion();
//                    boolean contains = smPlanRiskFormMapKeyList.contains(riskKey + File.pathSeparator + version);
//                    if (!contains) {
//                        throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "产品计划未选必选险种");
//                    }
//                });

        //校验责任是否存在
        for (String riskKeyVersion : smPlanRiskFormMap.keySet()) {
            SmPlanRiskForm smPlanRiskForm = smPlanRiskFormMap.get(riskKeyVersion);
            SmProductRiskVO smProductRiskVO = smProductRiskMap.get(riskKeyVersion);
            if (Objects.isNull(smProductRiskVO)) {
                throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "当前产品未配置产品计划配置的险种");
            }

            //获取险种所有责任的key
            List<String> riskDutyKeyList = sysRiskDutyMapper.selectByRisk(smPlanRiskForm.getRiskKey(), smPlanRiskForm.getRiskVersion())
                    .stream()
                    .map(SysRiskDuty::getDutyKey)
                    .collect(Collectors.toList());
            //获取配置的所有责任的key
            List<SmPlanRiskDutyForm> planRiskDuctList = smPlanRiskForm.getPlanRiskDutyList();
            List<String> configRiskDutyKeyList = planRiskDuctList.stream()
                    .map(SmPlanRiskDutyForm::getDutyKey)
                    .collect(Collectors.toList());
            //判断是否都存在
            if (!riskDutyKeyList.containsAll(configRiskDutyKeyList)) {
                throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(), "当前产品险种责任未配置于产品计划险种中");
            }
        }
    }

    /**
     * 保存计划
     * 1.先保存计划 然后再更新
     *
     * @param productId
     * @param smPlanForm
     * @param userId
     * @return
     */
    private SmPlanHistory saveSmPlanHistory(int productId, SmPlanForm smPlanForm, String userId) {
        //保存删除状态的计划
        SmPlan smPlan = new SmPlan();
        smPlan.setProductId(Long.valueOf(productId));
        smPlan.setPlanName(smPlanForm.getPlanName());
        smPlan.setPlanCode(smPlanForm.getPlanCode());
        smPlan.setPlanType(smPlanForm.getPlanType());
        smPlan.setMinPremium(smPlanForm.getMinPremium());
        smPlan.setFhProductId(smPlanForm.getPlanCode());
        smPlan.setCreateBy(userId);
        smPlan.setUpdateBy(userId);
        smPlan.setEnabledFlag(1);
        planMapper.insertUseGeneratedKeys(smPlan);
        //保存计划历史快照数据
        int productNewestVersion = getProductUpdateVersion(productId);
        SmPlanHistory smPlanHistory = new SmPlanHistory();
        smPlanHistory.setProductId(productId);
        smPlanHistory.setPlanName(smPlanForm.getPlanName());
        smPlanHistory.setPlanCode(smPlanForm.getPlanCode());
        smPlanHistory.setPlanType(smPlanForm.getPlanType());
        smPlanHistory.setMinPremium(smPlanForm.getMinPremium());
        smPlanHistory.setPlanId(smPlan.getId());
        smPlanHistory.setCreateBy(userId);
        smPlanHistory.setUpdateBy(userId);
        smPlanHistory.setVersion(productNewestVersion);
        smPlanHistory.setFhProductId(smPlanForm.getPlanCode());
        smPlanHistoryMapper.insert(smPlanHistory);
        return smPlanHistory;
    }

    /**
     * 校验产品编码重复
     * @param smPlanForm
     * @param productIds 编辑时排除的计划id
     */
    private void checkPlan(SmPlanForm smPlanForm,final List<Integer> productIds) {
        if (Objects.isNull(smPlanForm)) {
            return;
        }
        if (StringUtils.isBlank(smPlanForm.getPlanCode())) {
            return;
        }
        checkFhProductId(Collections.singletonList(smPlanForm.getPlanCode()),productIds);
    }


    /**
     * 校验计划id是否重复
     * @param fhProductIds
     * @param productIds 排除掉本产品
     */
    public void checkFhProductId(final List<String> fhProductIds,final List<Integer> productIds) {
        if(CollectionUtils.isEmpty(fhProductIds)){
           return;
        }
        //校验本身是否重复
        final Map<String, Long> counts = fhProductIds.stream()
                .collect(Collectors.groupingBy(e -> e, Collectors.counting()));
        counts.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .forEach(entry -> {
                    throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(),
                "当前产品：计划编码/计划ID["+entry.getKey()+"重复]");});
        //查询存在计划信息
        List<SmPlanHistory> smPlans=smPlanHistoryMapper.queryPlanByfhProductIds(fhProductIds);
        if(CollectionUtils.isNotEmpty(productIds)&&CollectionUtils.isNotEmpty(smPlans)){
            final Set<Integer> upPlanIdMap = productIds
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            smPlans=smPlans
                    .stream()
                    .filter(e->!upPlanIdMap.contains(e.getProductId()))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(smPlans)) {
            return;
        }
        //校验数据库是否重复
        final List<String> errorList=new ArrayList<>();
        smPlans.stream()
                .filter(e -> Objects.nonNull(e.getProductId()))
                .limit(5)
                .forEach(e -> {
                    //判断重复计划对应的产品是否存在
                    Integer maxVersion = productVersionMapper.getMaxVersion(e.getProductId().intValue());
                    if (Objects.isNull(maxVersion)) {
                        return;
                    }
                    maxVersion = maxVersion == 0 ? 1 : maxVersion;
                    final SmProductDetailVO productDetailVO = smProductHistoryMapper.getProductById(e.getProductId().intValue(),
                            maxVersion);
                    if (Objects.nonNull(productDetailVO)) {
                        log.info( "计划编码/计划ID重复：{}",e);
                        errorList.add( "[计划编码/计划ID："+ e.getFhProductId()+"已被产品【"+productDetailVO.getProductName()+"】占用，不能重复]");
                    }
                });
        if(CollectionUtils.isNotEmpty(errorList)){
            throw new BizException(ExcptEnum.PRODUCT_CONFIG_ERROR_801042.getCode(),
                    String.join(",", errorList));
        }
    }
}
