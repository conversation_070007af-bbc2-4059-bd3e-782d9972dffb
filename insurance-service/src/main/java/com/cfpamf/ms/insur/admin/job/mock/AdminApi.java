package com.cfpamf.ms.insur.admin.job.mock;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper;
import com.cfpamf.ms.insur.admin.dao.safes.renewalTerm.SmOrderRenewalTermPushWhaleLogMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.external.fx.FxOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyResultV2;
import com.cfpamf.ms.insur.admin.external.xm.XmOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.job.renewal.FxPullRenewalTermJobHandler;
import com.cfpamf.ms.insur.admin.job.renewal.RenewalTermChannelPushJobHandler;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderWhaleNotify;
import com.cfpamf.ms.insur.admin.pojo.po.renewal.SmOrderRenewalTermPushWhaleLogPo;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupNotifyService;
import com.cfpamf.ms.insur.admin.service.SmOrderGroupService;
import com.cfpamf.ms.insur.admin.service.correct.GroupHelper;
import com.cfpamf.ms.insur.admin.service.order.SmOrderPolicyService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderServiceWrapper;
import com.cfpamf.ms.insur.admin.service.order.SmOrderWhaleNotifyService;
import com.cfpamf.ms.insur.admin.service.renewalterm.SmOrderRenewalTermService;
import com.cfpamf.ms.insur.base.config.mq.MqConstants;
import com.cfpamf.ms.insur.base.config.mq.RabbitMqUtils;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BusinessTokenService;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.common.enums.EnumMonitor;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.job.EPolicyNotifyJobHandler;
import com.cfpamf.ms.insur.weixin.mq.message.NotifyEPolicyMessage;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.GroupEPolicyResponse;
import com.xxl.job.core.context.XxlJobHelper;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;

import static com.cfpamf.ms.insur.base.config.mq.MqConstants.*;

/**
 * <AUTHOR>
@Slf4j
@RestController
@RequestMapping(BaseConstants.WX_VERSION)
public class AdminApi {

    @Autowired
    private SmOrderGroupNotifyService orderService;

    @Autowired
    private SmOrderGroupService smOrderGroupService;

    @Autowired
    private SmOrderItemMapper orderItemMapper;

    @Autowired
    protected BusinessTokenService tokenService;

    @Autowired
    private FxOrderServiceAdapterImpl fxOrderServiceAdapter;

    @Autowired
    private XmOrderServiceAdapterImpl xmOrderServiceAdapter;

    @GetMapping("/policy/correct/seq")
    public Object correctSeq(@RequestParam("orderId") String orderId) throws IOException {
        GroupHelper groupHelper = SpringFactoryUtil.getBean(GroupHelper.class);

        return  groupHelper.nextCorrectSeq(orderId);
    }

    @GetMapping("/run/endor/job")
    public Object runGroupEndorJob(@RequestParam("id") Integer id) throws IOException {
        SmOrderGroupNotify notify = orderService.getById(id);
        if (Objects.equals(GroupNotify.TypeEnum.ORDER.getCode(), notify.getType())) {
            smOrderGroupService.doGroupOrder(notify);
        } else {
            smOrderGroupService.doGroupEndorsement(notify);
        }
        return "success";
    }

    @Autowired
    RabbitMqUtils rabbitTemplate;

    @Autowired
    SmOrderServiceWrapper wrapper;

    @GetMapping("/run/epolicy")
    public Object push(@RequestParam("id") String id, @RequestParam("channel") String channel) {
        GroupEPolicyResponse resp = wrapper.findServiceByChannel(channel).notifyEPolicy(id);
        return resp;
    }

    @GetMapping("/{channel}/genId")
    public Object genId(@PathVariable("channel") String channel) {
        return IdGenerator.getNextNo(channel);
    }

    @Autowired
    private EPolicyNotifyJobHandler handler;

    @Autowired
    private SmOrderWhaleNotifyService smOrderWhaleNotifyService;

    @GetMapping("/run/notify/job")
    public Object runNotifyJob(String channel, String orderId) throws IOException {
        NotifyEPolicyMessage msg = new NotifyEPolicyMessage(EnumChannel.valueOf(channel), orderId);
        rabbitTemplate.sendMessage(msg, EXC_DELAY_INS, ROUTKEY_INS, m -> {
            m.getMessageProperties().setHeader(EXC_DELAY_HEADER, MqConstants.NOTIFY_EPOLICY_DELAY_TIME);
            return m;
        });
        return "success";
    }

    @GetMapping("/handle/whale/event")
    public Object handleWhaleEvent(@RequestParam("contractCode") String contractCode,
                                   @RequestParam("opType") String opType,
                                   @RequestParam(value = "preservationCode",required = false)String preservationCode,
                                   @RequestParam(value = "opMethod",required = false)Integer opMethod) {
        List<SmOrderWhaleNotify> notify = smOrderWhaleNotifyService.getByContractAndPreservationCode(contractCode,opType,preservationCode);
        notify.stream().forEach(entry->{
            if(entry.getStatus()==2){
                return; 
            }
            try {
                // 部分重跑场景需要重新获取数据
                if(opMethod!=null){
                    entry.setOpMethod(opMethod);
                }
                smOrderGroupService.processWhaleGroup(entry);
            } catch (MSBizNormalException e) {
                entry.setErrorCode(e.getErrorCode());
                entry.setStatus(GroupNotify.StatusEnum.FAIL.getCode());
                entry.setRemark(e.getErrorMsg());
                smOrderWhaleNotifyService.updateFailStatus(entry);
                log.warn("小鲸团险回调消息处理失败:{}", notify, e);
            }catch(Exception e){
                entry.setStatus(GroupNotify.StatusEnum.FAIL.getCode());
                entry.setRemark(e.getMessage()!=null && e.getMessage().length()>300?e.getMessage().substring(0,300):e.getMessage());
                smOrderWhaleNotifyService.updateFailStatus(entry);
                log.warn("小鲸团险回调消息处理失败:{}", notify, e);
            }
        });

        return "success";
    }

    @Value("${renewal-term.before-day}")
    Integer beforeDay;

    @Value("${renewal-term.default-grace-days}")
    Integer defaultGraceDays;

    @Autowired
    private RenewalTermChannelPushJobHandler channelPushJobHandler;

    @Autowired
    SmOrderPolicyService smOrderPolicyService;

    @GetMapping("/gen/renewalTerm/v1")
    public Object genRenewalTermList() {
        List<String> excludeChannel = Arrays.asList(EnumChannel.FX.getCode(), EnumChannel.XM.getCode());
        smOrderPolicyService.pushFirstPhaseRenewalTermV2(beforeDay, defaultGraceDays, null, excludeChannel);
        return "success";
    }

    @GetMapping("/gen/renewalTerm/v2")
    public Object genRenewalTermListV2() {
        List<String> excludeChannel = Arrays.asList(EnumChannel.FX.getCode(), EnumChannel.XM.getCode());
        smOrderPolicyService.pushToRenewalTermV2(beforeDay, defaultGraceDays, null, excludeChannel);
        return "success";
    }

    @GetMapping("/gen/renewalTerm/v3")
    public Object genRenewalTermListV3(@RequestParam("orderId") String orderId, @RequestParam("period") Integer period) {
        List<String> excludeChannel = Arrays.asList(EnumChannel.FX.getCode(), EnumChannel.XM.getCode());
        List<String> orderIdList = Arrays.asList(orderId);
        smOrderPolicyService.doCompensateInitRenewalTerm(orderIdList, period, beforeDay, defaultGraceDays);
        return "success";
    }


    @Autowired
    private SmOrderRenewalTermService orderRenewalTermService;

    @GetMapping("/gen/renewal-term-product")
    public Object genRenewalTermProduct(@RequestParam(value = "policyNo", required = false) String policyNo) {
        if (StringUtils.isBlank(policyNo)) {
            orderRenewalTermService.genRenewalTermProduct(Collections.emptyList());
        } else {
            orderRenewalTermService.genRenewalTermProduct(Collections.singletonList(policyNo));
        }
        return "success";
    }

    @GetMapping("/reset/renewal-term-product")
    public Object resetRenewalTermProduct(@RequestParam("orderId") String orderId) {
        orderRenewalTermService.resetRenewalTermProduct(orderId);
        return "success";
    }

    @Autowired
    private FxPullRenewalTermJobHandler fxPullRenewalTermJobHandler;

    @Autowired
    private SmOrderRenewalTermPushWhaleLogMapper renewalTermPushWhaleLogMapper;

    @GetMapping("/pull/fx/renewalTerm")
    public Object pullFxRenewalTerm(@RequestParam("date") String date) {
        Date handleTime = DateUtil.parseDate(date, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        fxPullRenewalTermJobHandler.handleDateRenewal(handleTime);
        return "success";
    }

    @GetMapping("/read/fx/renewalTerm")
    public Object readFxRenewalTerm(@RequestParam("date") String date) {
        Date handleTime = DateUtil.parseDate(date, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        return fxOrderServiceAdapter.getRenewalTermList(handleTime);
    }

    @GetMapping("/download/fx/renewalTerm")
    public Object downloadFxRenewalTerm(@RequestParam("date") String date) {
        Date handleTime = DateUtil.parseDate(date, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        fxOrderServiceAdapter.downloadRenewalTerm(handleTime);
        return "success";
    }

    @GetMapping("/read/xm/renewalTerm")
    public Object readXmRenewalTerm(@RequestParam("date") String date) {
        Date handleTime = DateUtil.parseDate(date, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        return xmOrderServiceAdapter.getRenewalTermList(handleTime);
    }

    @GetMapping("/download/xm/renewalTerm")
    public Object downloadXmRenewalTerm(@RequestParam("date") String date) {
        Date handleTime = DateUtil.parseDate(date, DateUtil.CN_YEAR_MONTH_DAY_FORMAT);
        xmOrderServiceAdapter.downloadRenewalTerm(handleTime);
        return "success";
    }


    @GetMapping("/push/whale")
    public Object pushWhale(Integer id) {
        SmOrderRenewalTermPushWhaleLogPo po = renewalTermPushWhaleLogMapper.selectByPrimaryKey(id);
        if (po == null) {
            return "数据不存在";
        }
        RenewalTermNotifyResultV2 result = orderRenewalTermService.push2Whale(po);
        if (result == null) {
            result = RenewalTermNotifyResultV2.nok("网络异常", po.getPolicyNo(), po.getTermNum());
        }
        if (result.success()) {
            po.setStatus(1);
        } else {
            po.setStatus(-1);
        }
        po.setMessage(result.getMessage());

        renewalTermPushWhaleLogMapper.updatePeriod(po);
        channelPushJobHandler.pushEmail(Collections.singletonList(result));
        return "success";
    }

    @GetMapping("/fix/policy/item")
    public String fixOrderItem(@RequestParam(value = "id") Integer id,@RequestParam(value = "type") String  type) {
        SmOrderItem orderItem = orderItemMapper.selectByPrimaryKey(id);
        if(orderItem==null){
            return "分单数据不存在";
        }

        String orderId = orderItem.getFhOrderId();
        String idNumber = orderItem.getIdNumber();
        SmOrderItem param = new SmOrderItem();
        param.setFhOrderId(orderId);
        param.setIdNumber(idNumber);
        List<SmOrderItem> orderItemList =  orderItemMapper.select(param);
        int r = 0;
        if(orderItemList.size()>1){
            if(Objects.equals(type,"F")) {
                r = orderItemMapper.deleteByPrimaryKey(id);
            }else{
                r= orderItemMapper.logicDel(id);
            }
        }
        return "数据处理完成:"+r;
    }

}
