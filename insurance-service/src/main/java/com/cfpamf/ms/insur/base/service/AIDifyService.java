package com.cfpamf.ms.insur.base.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimAiFileMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimAiFileType;
import com.cfpamf.ms.insur.admin.external.ai.AiDifyApi;
import com.cfpamf.ms.insur.admin.pojo.dto.claim.*;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimAiFile;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.AIDifyResponse;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.util.AliYunOssUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Version 1.0
 */
@Service
@Slf4j
public class AIDifyService {

    @Autowired
    private AiDifyApi difyApi;

    @Autowired
    private SmClaimAiFileMapper aiFileMapper;

    @Autowired
    private SmClaimMapper claimMapper;

    public List<AiFileResponseVO> executeClaimWorkFlow(AiBusinessParams params) {

        EnumClaimAiFileType fileType = EnumClaimAiFileType.queryByCode(params.getAiFileType());
        if (Objects.isNull(fileType)) {
            throw new BizException("", "ai识别文件编码不存在");
        }

        ClaimAiDescParams aiDescParams = claimMapper.queryAllAiParamsByClaimId(params.getClaimId());
        if (Objects.isNull(aiDescParams)) {
            throw new BizException("", "理赔信息不存在");
        }

        List<AiFileResponseVO> difyFormList = params.getFileList().parallelStream().map(
                x -> {

                    AiRequestInput aiRequestInput = new AiRequestInput();
                    aiRequestInput.setDesc(fileType.generateDescParams(aiDescParams));
                    aiRequestInput.setUser(StrUtil.isEmpty(HttpRequestUtil.getUserId()) ? "wuxi" : HttpRequestUtil.getUserId());
                    aiRequestInput.setFileType(fileType.getCodeName());
                    aiRequestInput.setParams(Objects.nonNull(params.getParams()) ? JSONObject.toJSONString(params.getParams()) : "");
                    aiRequestInput.setFileUrl(x.getFileUrl());
                    AiDifyForm difyForm = new AiDifyForm();
                    difyForm.setResponse_mode("blocking");
                    difyForm.setUser(StrUtil.isEmpty(HttpRequestUtil.getUserId()) ? "wuxi" : HttpRequestUtil.getUserId());
                    AIDifyFile difyFile = new AIDifyFile();
                    difyFile.setType("image");
                    difyFile.setUrl(x.getFileUrl());
                    difyFile.setTransfer_method("remote_url");
                    difyForm.setFiles(Lists.newArrayList(difyFile));
                    difyForm.setInputs(JSONObject.parseObject(JSONObject.toJSONString(aiRequestInput)));
                    SmClaimAiFile claimAiFile = new SmClaimAiFile();
                    claimAiFile.setClaimId(params.getClaimId());
                    claimAiFile.setFileUniqueCode(x.getFileUniqueCode());
                    claimAiFile.setState("0");
                    claimAiFile.setUpdateBy(HttpRequestUtil.getUserId());
                    claimAiFile.setRequestJson(JSONObject.toJSONString(difyForm));
                    aiFileMapper.insert(claimAiFile);
                    JSONObject jsonObject = null;
                    try{
                        AIDifyResponse response = difyApi.executeClaimWorkFlow(difyForm, new Request.Options(3 * 1000, 60 * 1000));
                        if (Objects.nonNull(response)) {
                            String realStr = Optional.ofNullable(response.getData()).map(y -> y.getJSONObject("outputs")).map(y -> y.getString("text")).orElse(null);
                            if (Objects.nonNull(realStr)) {
                                String r = "";
                                if (StrUtil.isNotEmpty(realStr) && realStr.startsWith("```json")) {
                                    r = ReUtil.extractMulti("\\`\\`\\`json(.*)\\`\\`\\`", realStr, "$1");
                                } else {
                                    r = realStr;
                                }

                                jsonObject = JSONObject.parseObject(r);
                            }
                        }
                        claimAiFile.setResponseJson(JSONObject.toJSONString(response));
                        claimAiFile.setParseJson(JSONObject.toJSONString(jsonObject));
                        claimAiFile.setState("1");
                        claimAiFile.setServiceType("DIFY_CLAIM_VERIFY");
                        String substring = URI.create(x.getFileUrl()).getPath().substring(1);
                        claimAiFile.setImmortalUrl(AliYunOssUtil.generatePresignedUri(AliYunOssUtil.getOSSClient(), substring));
                        aiFileMapper.updateByPrimaryKey(claimAiFile);
                    } catch(Exception e) {
                        log.warn("ai识别异常", e);
                        claimAiFile.setRemark(e.getMessage());
                        aiFileMapper.updateByPrimaryKey(claimAiFile);
                    }

                    AiFileResponseVO aiFileResponseVO = new AiFileResponseVO();
                    aiFileResponseVO.setFileUniqueCode(x.getFileUniqueCode());
                    aiFileResponseVO.setJsonObject(jsonObject);

                    return aiFileResponseVO;
                }
        ).collect(Collectors.toList());

        return difyFormList;

    }



    public void deleteById(int id) {
        aiFileMapper.deleteByPrimaryKey(id);
    }


    public void reRunUrl() {
        List<SmClaimAiFile> claimAiFiles = aiFileMapper.selectAll();
        for (SmClaimAiFile aiFile:claimAiFiles) {
            try{
                if (StrUtil.isEmpty(aiFile.getRequestJson())) {
                    continue;
                }
                
                if (StrUtil.isNotEmpty(aiFile.getImmortalUrl())) {
                    continue;
                }
                
                String reUrl = Optional.ofNullable(JSONObject.parseObject(aiFile.getRequestJson()).getJSONArray("files"))
                        .filter(CollUtil::isNotEmpty).map(x -> x.getJSONObject(0))
                        .map(x -> x.getString("url"))
                        .filter(StrUtil::isNotEmpty)
                        .orElse(null);
                if (StrUtil.isEmpty(reUrl)) {
                    continue;
                }

                String substring = URI.create(reUrl).getPath().substring(1);
                aiFile.setImmortalUrl(AliYunOssUtil.generatePresignedUri(AliYunOssUtil.getOSSClient(), substring));
                aiFileMapper.updateByPrimaryKey(aiFile);
            } catch(Exception e) {
                log.warn("解析异常-{}", aiFile.getId(), e);
            }
        }
    }


    public static void main(String[] args) {

        String text = "```json\n{\n    \"data\": {\n        \"match\": true,\n        \"comPanyName\": \"中华联合财产保险股份有限公司\"\n    }\n}\n```";
        String response = ReUtil.extractMulti("\\`\\`\\`json(.*)\\`\\`\\`", text, "$1");
        JSONObject.parse(response);
    }
}
