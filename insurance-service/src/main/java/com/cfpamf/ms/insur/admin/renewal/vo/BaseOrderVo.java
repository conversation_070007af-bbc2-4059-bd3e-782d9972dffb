package com.cfpamf.ms.insur.admin.renewal.vo;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumRenewalIntention;
import com.cfpamf.ms.insur.admin.enums.renewal.EnumServiceMode;
import com.cfpamf.ms.insur.base.annotation.ExportField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 基础订单信息视图对象
 * <AUTHOR>
 * @date 2021/5/13 16:14
 */
@Getter
@Setter
public class BaseOrderVo {

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private String fhOrderId;

    /**
     * 保单记录id
     */
    @ApiModelProperty("保单记录id")
    private Long orderInsuredId;

    @ApiModelProperty("订单类别 group/团建  person/个险")
    private String productAttrCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("计划名称")
    private String planName;

    @ApiModelProperty("计划id")
    private Integer planId;

    @ApiModelProperty("产品id")
    private Integer productId;

    @ApiModelProperty("产品渠道")
    private String channel;

    @ApiModelProperty("投保人姓名")
    private String applicantPersonName;

    @ApiModelProperty("被保人姓名")
    private String insuredPersonName;

    @ApiModelProperty("被保人身份证")
    private String insuredIdNumber;

    @ApiModelProperty("保费")
    private BigDecimal insuredAmount;

    @ApiModelProperty("保单")
    private String policyNo;

    @ApiModelProperty("失效时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime invalidTime;

    @ApiModelProperty("生效时间")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime startTime;

    @ApiModelProperty("推荐人姓名")
    private String recommendUserName;

    @ApiModelProperty("管护客户经理区域")
    private String customerManagerRegion;

    @ApiModelProperty("管护客户经理")
    private String customerManagerOrganization;

    @ApiModelProperty("管护客户经理")
    private String customerManager;

    @ApiModelProperty("过期天数")
    private Integer surplusDay;

    @ApiModelProperty("宽限天数")
    private Integer graceDay;

    @ApiModelProperty("提前续保天数")
    private Integer advanceDay;

    @ApiModelProperty("保单地址")
    private String downloadURL;

    @ApiModelProperty
    private String companyName;

    @ApiModelProperty("断保原因")
    private String overReason;

    /**
     * 续保订单id
     */
    @ApiModelProperty("续保订单id")
    private String renewalOrderId;
    /**
     * 续保保单id
     */
    @ApiModelProperty("续保保单id")
    private String renewalPolicyNo;



    /**
     * 是否可续保标志
     */
    @ApiModelProperty("是否可续保标志,0不可续保，1可续保")
    private Integer renewable;
    /**
     * 是否可续保标志
     */
    @ApiModelProperty("不可续保原因")
    private String nonRenewableReason;

    @ApiModelProperty("服务方式")
    String serviceMode;

    @ApiModelProperty("跟进情况 noFollowUp:未跟进,willing:愿意续保,consider:考虑, unwilling:不愿续保,loseContact:联系不上")
    private String intention;

    @ApiModelProperty(name = "自保件")
    private String selfInsuredName;

    @ApiModelProperty(name = "是否异业客户")
    private String judgeCustomerLoan;

    public String getIntention() {
        if (EnumServiceMode.LOSE_CONTACT.getCode().equals(serviceMode)){
            return EnumRenewalIntention.LOSE_CONTACT.getDesc();
        }

        if (StringUtils.isEmpty(intention)){
            return EnumRenewalIntention.NO_FOLLOW_UP.getDesc();
        }
        return EnumRenewalIntention.dict(intention);
    }
}
