package com.cfpamf.ms.insur.app.dao;

import com.cfpamf.ms.insur.app.pojo.query.AppProductQuery;
import com.cfpamf.ms.insur.app.pojo.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 小额保险产品数据库访问mapper
 *
 * <AUTHOR>
 */
public interface AppProductMapper {

    /**
     * 查询产品分类
     *
     * @return
     */
    List<AppCategoryVO> listProductCategory();

    /**
     * 查询app产品列表
     *
     * @param query
     * @return
     */
    List<AppProductListVO> listAppProducts(AppProductQuery query);

    /**
     * 查询app热门产品列表
     *
     * @return
     */
    List<AppProductListVO> listAppHotProducts(AppProductQuery query);

    /**
     * 查询微信产品详情
     *
     * @param productId
     * @return
     */
    AppProductDetailVO getProductDetailById(@Param("productId") int productId);

    /**
     * 查询产品保险条款列表
     *
     * @param productId
     * @return
     */
    List<AppProductClauseVO> listProductClausesByProductId(int productId);

    /**
     * 查询app商品保障计划详情
     *
     * @param planId
     * @return
     */
    List<AppPlanCoverageVO> listAppProductPlanCoverages(int planId);

//    /**
//     * 查询团险产品保费折扣比例
//     *
//     * @param productId
//     * @return
//     */
//    WxGlProductDetailVO getGlProductById(@Param("productId") int productId);
//
//    /**
//     * 查询产品保险条款列表
//     *
//     * @param productId
//     * @return
//     */
//    List<WxProductClauseVO> listProductClausesByProductId(int productId);
//
//    /**
//     * 查询产品销售区域列表
//     *
//     * @param orgName
//     * @return
//     */
//    List<Integer> listProductSalesOrgsByOrgPath(@Param("orgName") String orgName);
//
//    /**
//     * 查询产品保障项目
//     *
//     * @param productId
//     * @return
//     */
//    List<WxProductCoverageVO> listProductCoverages(@Param("productId") int productId);
//
//    /**
//     * 查询单个产品销售区域列表
//     *
//     * @param productId
//     * @return
//     */
//    List<WxProductSalesOrgVO> listProductSalesOrgsByProductId(@Param("productId") int productId);
}
