package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.bms.facade.vo.FdSimpleUserVO;
import com.cfpamf.ms.bms.facade.vo.UserRoleVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.enums.EnumBmsRole;
import com.cfpamf.ms.insur.admin.enums.EnumPostCode;
import com.cfpamf.ms.insur.admin.job.SyncEmployeeJobHandler;
import com.cfpamf.ms.insur.admin.pojo.dto.OrgPicExtraDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmAgentRegisterDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.UserAuthorityDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.UserDTO;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.query.ChangeUserQuery;
import com.cfpamf.ms.insur.admin.pojo.query.OrgPicExtraQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.base.bean.BmsAuthRequest;
import com.cfpamf.ms.insur.base.bean.BmsUserResponse;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.config.ClaimConfig;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.infeface.CustomerCenterService;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxBindDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserBindDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.cfpamf.ms.insur.weixin.service.WxCcUserService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.USER_IS_ORG_ADMIN;

/**
 * 用户service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserService {

    /**
     * 车险授权service
     */
    @Lazy
    @Autowired
    private AmAuthService authService;

    /**
     * 客户中心service
     */
    @Autowired
    private CustomerCenterService ccService;

    /**
     * 用户mapper
     */
    @Autowired
    private AuthUserMapper userMapper;

    /**
     * 用户权限mapper
     */
    @Autowired
    private UserAuthorityMapper uaMapper;

    /**
     * 微信用户中心service
     */
    @Lazy
    @Autowired
    private WxCcUserService ccUserService;

    /**
     * 同步员工定时任务
     */
    @Autowired
    private SyncEmployeeJobHandler syncTask;

    /**
     * OmsService
     */
    @Autowired
    private BmsService bmsService;

    /**
     * 代理人service
     */
    @Autowired
    private SmAgentService agentService;

    /**
     * 组织mapper
     */
    @Autowired
    private OrgMapper orgMapper;

    /**
     * 机构负责人mapper
     */
    @Autowired
    private OrgPicExtraMapper picMapper;

    @Autowired
    private UserPostMapper userPostMapper;
    @Autowired
    private UserSyncService userSyncService;
    @Autowired
    private UserPostService userPostService;

    @Autowired
    private BmsConfig bmsConfig;

    /**
     * 查询用户分页列表附带权限
     *
     * @param keyword
     * @param pageable
     * @return
     */
    public PageInfo<UserVO> getUsersByPage(String keyword, boolean inOffice, Pageable pageable) {
        if (pageable != null) {
            PageHelper.startPage(pageable.getPage(), pageable.getSize());
        }
        List<UserVO> userVoList = userMapper.listUsers(keyword, inOffice);
        userVoList.forEach(u -> {
            u.setPermissions(uaMapper.listUserAuthoritysByUserId(u.getUserId()));
        });
        // 对客户敏感数据进行脱敏
        DataMaskUtil.maskList(userVoList);
        return new PageInfo<>(userVoList);
    }

    /**
     * 查询用户变更分页列表附带权限
     * <p>
     * 总部人员		公司的全部员工数据	公司的全部员工数据
     * 机构人员	机构负责人（主任、副主任、管理顾问、主任助理）	该机构的员工数据	该机构的员工数据
     * 机构督导、分支内务	该机构的员工数据	该机构的员工数据
     *
     * @param query
     * @return
     */
    public PageInfo<UserVO> getChangeUsersByPage(ChangeUserQuery query) {

        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);
        permissionUtil.buildDataPermissionPageQuery(query);

//        //  区域角色 没权限查看
//        if (permissionUtil.isRegionRole(ThreadUserUtil.USER_DETAIL_TL.get())) {
//            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
//        }
//        // 客户经理没权限查看
//        if (permissionUtil.isCustomerManager(ThreadUserUtil.USER_DETAIL_TL.get())) {
//            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
//        }

        PageHelper.startPage(query.getPage(), query.getSize());
        List<UserVO> userVoList;
        if (query.isDimission()) {
            userVoList = userMapper.listChangeUsersV1(query);
        } else {
            userVoList = userMapper.listChangeUsers(query);
        }
        //赋值权限
        userVoList.forEach(u -> {
            u.setPermissions(uaMapper.listUserAuthoritysByUserId(u.getUserId()));
        });
        return new PageInfo<>(userVoList);
    }

    /**
     * 查询用户分页列表附带权限
     *
     * @param keyword
     * @param pageable
     * @return
     */
    public PageInfo<UserVO> getUsersByPageV2(String keyword, boolean inOffice, Pageable pageable) {
        if (pageable != null) {
            PageHelper.startPage(pageable.getPage(), pageable.getSize());
        }
        List<UserVO> userVoList = userMapper.listUsersV2(keyword, inOffice);
        userVoList.forEach(u -> {
            u.setPermissions(uaMapper.listUserAuthoritysByUserId(u.getUserId()));
        });
        return new PageInfo<>(userVoList);
    }

    /**
     * 查询用户分页列表附带权限
     *
     * @param keyword
     * @param pageable
     * @return
     */
    public PageInfo<UserVO> getUsersByOrgPage(String keyword, String orgName, Pageable pageable) {
        return PageHelper.startPage(pageable.getPage(), pageable.getSize())
                .doSelectPageInfo(() -> userMapper.listUserByOrg(keyword, orgName));
    }

    /**
     * 查询用户分页列表
     *
     * @param keyword
     * @return
     */
    public List<UserVO> getUserList(String keyword) {
        return userMapper.listUsers(keyword, false);
    }


    /**
     * 查询用户分页列表无权限
     *
     * @param jobNumbers
     * @return
     */
    public List<UserVO> getUsersByPageWithJobNumbers(List<String> jobNumbers) {
        return userMapper.listUsersByPageWithJobNumbers(jobNumbers);
    }


    /**
     * 查询用户姓名
     *
     * @param jobNumbers
     * @return
     */
    public List<AuthUserNameVO> getUserNameByUserId(List<String> jobNumbers) {
        return userMapper.listUserNameByUserId(jobNumbers);
    }

    /**
     * 查询用户授权信息
     *
     * @param mobile
     * @return
     */
    public AuthUserVO getAuthUserByUserMobile(String mobile) {
        return userMapper.getAuthUserByUserMobile(mobile);
    }

    /**
     * 查询用户授权信息
     *
     * @param userId
     * @return
     */
    public AuthUserVO getAuthUserByUserId(String userId) {
        return userMapper.getAuthUserByUserId(userId);
    }


    /**
     * 查询用户授权信息
     *
     * @param id
     * @return
     */
    public AuthUserVO getAuthUserById(String id) {
        return userMapper.getAuthUserById(Integer.parseInt(id));
    }

    /**
     * 查询用户授权信息
     *
     * @param token
     * @return
     */
    public AuthUserVO getAuthUserByToken(String token) {
        return userMapper.getAuthUserByToken(token);
    }

    /**
     * 删除用户
     *
     * @param id
     */
    public void deleteUser(int id) {
        userMapper.deleteUser(id);
    }

    /**
     * 新建用户
     *
     * @param dto
     * @return
     */
    public int insertUser(UserDTO dto) {
        AuthUserVO authUserVO = userMapper.getAuthUserByUserId(dto.getUserId());
        if (authUserVO != null) {
            log.warn("用户已经存在 userId={}", dto.getUserId());
            throw new BizException(ExcptEnum.USER_EXISTS_ERROR_801003);
        }
        dto.setUserType(SmConstants.USER_TYPE_EMPLOYEE);
        userMapper.insertUser(dto);
        if (dto.getPermissions() != null) {
            dto.getPermissions().stream().forEach(p -> {
                p.setUserId(dto.getUserId());
                uaMapper.insertUserAuthority(p);
            });
        }
        return dto.getId();
    }

    /**
     * 修改用户和优化权限信息
     *
     * @param id
     * @param dto
     */
    public void updateEmployeeBaseInfo(int id, UserDTO dto) {
        dto.setUserType(SmConstants.USER_TYPE_EMPLOYEE);
        userMapper.updateUser(id, dto);
    }

    /**
     * 修改用户
     *
     * @param id
     * @param dto
     */
    public void updateUser(int id, UserDTO dto) {
        AuthUserVO authUserVO = userMapper.getAuthUserByUserMobile(dto.getUserId());
        if (authUserVO != null && authUserVO.getId() != id) {
            throw new BizException(ExcptEnum.USER_EXISTS_ERROR_801003);
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(dto.getUserMobile())) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "手机号不能为空");
        }
        if (!dto.getUserMobile().matches("^1\\d{10}$")) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "手机号码格式不正确");
        }
        //校验手机号是否格式正常
        dto.setUserType(SmConstants.USER_TYPE_EMPLOYEE);
        userMapper.updateUser(id, dto);
        uaMapper.deleteUserAuthority(dto.getUserId());
        if (dto.getPermissions() != null) {
            dto.getPermissions().stream().forEach(p -> {
                p.setUserId(dto.getUserId());
                uaMapper.insertUserAuthority(p);
            });
        }
    }

    /**
     * 判断是否车险用户
     *
     * @param userId
     * @param loginId
     * @param expireTime
     * @return
     */
    public boolean loginCarInsuranceUser(String userId, String loginId, Date expireTime) {
        int count = userMapper.updateUserLoginInfo(userId, loginId, expireTime);
        return count >= 1;
    }

    /**
     * 退出登录
     *
     * @param userId
     * @return
     */
    public boolean logout(String userId) {
        int count = userMapper.updateUserLogoutInfo(userId);
        return count == 1;
    }

    /**
     * 验证用户授权
     *
     * @param authorization
     * @return
     */
    public boolean validate(String authorization) {
        AuthUserVO authUser = userMapper.getAuthUserByToken(authorization);
        return authUser != null && (authUser.getExpireTime() != null) && (authUser.getExpireTime().compareTo(new Date()) > 0);
    }

    /**
     * 更新微信用户信息
     *
     * @param wxUserDto
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateWxUserInfo(WxUserDTO wxUserDto) {
        List<WxUserVO> wxUserVos = userMapper.listUserByWxOpenId(wxUserDto.getWxOpenId());
        wxUserDto.setUserType(SmConstants.USER_TYPE_WEIXIN);
        if (wxUserVos.isEmpty()) {
            userMapper.insertWxUserInfo(wxUserDto);
        } else {
            userMapper.updateWxUserInfo(wxUserDto);
        }
    }

    /**
     * 查询绑定员工的微信主账户用户信息
     *
     * @param openId
     * @return
     */
    public WxUserVO getUserInfoByWxOpenId(String openId) {
        List<WxUserVO> wxUserVos = userMapper.listUserByWxOpenId(openId);
        return wxUserVos.stream().filter(w -> !StringUtils.isEmpty(w.getUserId())).findFirst().orElse(null);
    }

    /**
     * 查询微信主账户用户信息
     *
     * @param openId
     * @return
     */
    public WxUserVO getWxInfoByWxOpenId(String openId) {
        List<WxUserVO> wxUserVos = userMapper.listUserByWxOpenId(openId);
        return wxUserVos.size() > 0 ? wxUserVos.get(0) : null;
    }

    /**
     * 查询微信绑定所有账户用户信息
     *
     * @param openId
     * @return
     */
    public List<WxUserVO> getWxUsersByWxOpenId(String openId) {
        List<WxUserVO> wxUsers = userMapper.listUserByWxOpenId(openId);
        List<WxUserVO> allWxUsers = new ArrayList<>(wxUsers);
        wxUsers.forEach(wu -> userMapper.listUserByUserMobile(wu.getUserMobile())
                .forEach(oa -> {
                    if (allWxUsers.stream().noneMatch(awx -> Objects.equals(awx.getId(), oa.getId()))) {
                        //判断openid是否存在，不存在需补上并且替换微信昵称和图标
                        if (StringUtils.isEmpty(oa.getWxOpenId())) {
                            oa.setWxOpenId(wxUsers.get(0).getWxOpenId());
                            oa.setWxNickName(wxUsers.get(0).getWxNickName());
                            oa.setWxImgUrl(wxUsers.get(0).getWxImgUrl());
                        }
                        allWxUsers.add(oa);
                    }
                }));
        return allWxUsers;
    }
    /**
     * 查询微信绑定所有账户用户信息
     *
     * @param userId
     * @return
     */
    public WxUserVO getWxUsersByUserId(String userId) {
        WxUserVO wxUsers = userMapper.getUserByUserId(userId);
        return wxUsers;
    }

    /**
     * 微信用户公司员工绑定
     *
     * @param bindDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean bindUserAgentWxInfo(WxBindDTO bindDto) {
        String custNo = ccService.login(bindDto.getLoginID(), bindDto.getLoginPwd());
        if (StringUtils.isEmpty(custNo)) {
            throw new BizException(ExcptEnum.AGENT_NOT_REAL_CHECK_801036);
        }
        userMapper.updateUserBindAgentBaseInfo(custNo, bindDto.getWxOpenId());
        return true;
    }

    /**
     * 微信用户公司员工绑定
     * 安全规范整改：登录场景统一异常码
     *
     * @param bindDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean bindUserEmployeeWxInfo(WxBindDTO bindDto) {
        // 查询微信账户信息
        List<WxUserVO> wxUserInfos = userMapper.listUserByWxOpenId(bindDto.getWxOpenId());
        // 不是从微信进入没有微信信息
        if (wxUserInfos.isEmpty()) {
            throw new BizException(ExcptEnum.WEIXIN_BIND_ERROR_601003);
        }
        // 该微信账号已经绑定其他手机号码
        if (wxUserInfos.stream().anyMatch(wu -> !StringUtils.isEmpty(wu.getUserMobile()))) {
            throw new BizException(ExcptEnum.WEIXIN_BIND_DUPLICATE_601002);
        }
        // BMS 获取员工账户信息失败
        List<FdSimpleUserVO> bmsUsers = bmsService.getContextUserDetail(bindDto.getLoginID());
        if (bmsUsers == null || bmsUsers.isEmpty()) {
            throw new BizException(ExcptEnum.WEIXIN_BIND_ERROR_601003);
        }
        // 信贷系统验证用户有效性
        BmsAuthRequest authDto = new BmsAuthRequest();
        authDto.setLoginID(bindDto.getLoginID());
        authDto.setLoginPwd(bindDto.getLoginPwd());
        BmsUserResponse.UserDetail userDetail = authService.getBmsAuth(authDto);
        if (userDetail == null) {
            throw new BizException(ExcptEnum.WEIXIN_BIND_ERROR_601003);
        }

        FdSimpleUserVO mainJob = bmsUsers.stream().filter(us -> Objects.equals(us.getServiceType(), "0"))
                .findAny()
                .orElseThrow(() -> new BizException(ExcptEnum.WEIXIN_BIND_ERROR_601003.getCode(), "员工主职未找到"));
        WxUserBindDTO userDto = new WxUserBindDTO(wxUserInfos.get(0));
        // 构建更新DTO
        BeanUtils.copyProperties(mainJob, userDto);
        userDto.setOrganizationName(mainJob.getOrgName());
        userDto.setOrganizationFullName(userDto.getOrganizationName());
        userDto.setUserId(mainJob.getJobNumber());
        userDto.setUserName(mainJob.getEmployeeName());
        userDto.setUserMobile(mainJob.getMobile());
        userDto.setUserType(SmConstants.USER_TYPE_EMPLOYEE);
        //add by zhangjian 2021-01-04 474
        userDto.setBmsUserId(mainJob.getUserId());
//        userDto.setMainJobNumber(mainJob.getMasterJobNumber());
        userDto.setRegionCode(mainJob.getRegionCode());
        userDto.setUserIdCard(mainJob.getEmployeeIdCard());


        if (Objects.equals(userDto.getRegionName(), BaseConstants.ORG_REGION_HQ)) {
            userDto.setOrganizationName(BaseConstants.ORG_BRANCH_HQ);
            userDto.setRegionCode(BaseConstants.ORG_REGION_HQ_CODE);
        }
        // 微信名称因为特殊字符需要base64加密
        String wxNickName = userDto.getWxNickName();
        if (!Base64Util.isEncrypt(wxNickName)) {
            userDto.setWxNickName(Base64Util.encryptBASE64(wxNickName.getBytes(StandardCharsets.UTF_8)));
        }

        /**
         * 员工账号和微信信息合并
         */
        List<WxUserVO> employees = userMapper.listUserByUserMobile(userDetail.getEmployeePhone());
        // 微信用户信息 和员工用户都有 账号都存在
        if (!employees.isEmpty()) {
            // 员工已绑定其他微信号
            if (employees.stream().anyMatch(u -> !StringUtils.isEmpty(u.getWxOpenId()))) {
                throw new BizException(ExcptEnum.WEIXIN_BIND_DUPLICATE_601007);
            }
            //  员工没绑定其他微信号 更新员工账号绑定微信信息
            userMapper.updatedUserWxBindInfo(userDto);
            userMapper.deleteUnnecessaryWxInfoByOpenId(bindDto.getWxOpenId());
            return true;
        }

        /**
         * 解决更换手机号当天就来重新绑定的情况 通过工号二次判断
         */
        AuthUserVO userVO = userMapper.getAuthUserByUserId(userDto.getUserId());
        // 没有员工账号  新建员工账号和为微信信息
        if (userVO == null) {

            userDto.setBizCode(getBizCodeByIdNumber(userDto.getUserIdCard()));
            userMapper.insertUserWxBindInfo(userDto);
            //处理职位信息. 511行
            userSyncService.syncSingleBindUser(mainJob, userDto.getRegionCode(), userDto.getOrganizationName(), null);
        } else {
            userMapper.updatedUserWxBindInfoByUserId(userDto);
        }

        userMapper.deleteUnnecessaryWxInfoByOpenId(bindDto.getWxOpenId());
        return true;
    }

    /**
     * 查询用户权限
     *
     * @param userId
     * @return
     */
    public List<UserAuthorityVO> listUserAuthoritysByUserMobile(String userId) {
        return uaMapper.listUserAuthoritysByUserMobile(userId);
    }

    /**
     * 新建用户权限
     *
     * @param dto
     * @return
     */
    public int insertUserAuthority(UserAuthorityDTO dto) {
        return uaMapper.insertUserAuthority(dto);
    }

    /**
     * 手动同步员工所有数据
     *
     * @return
     */
    public void syncEmployees() {
        syncTask.execute();
    }

    /**
     * 微信用户解绑
     *
     * @param wxOpenId
     */
    public void unbindUserWx(String wxOpenId) {
        userMapper.deleteUserWxInfoByOpenId(wxOpenId);
        // 查询加载session信息
        WxSessionVO session = ccUserService.getWxSession(wxOpenId);
        if (null != session) {
            ccUserService.reloadWxSession(wxOpenId, null, session.getChannel(), null);
        }
    }

    /**
     * 后台管理系统用户解绑
     *
     * @param id
     */
    public void unbindUser(int id) {
        AuthUserVO userPO = userMapper.getAuthUserById(id);
        userMapper.deleteUserWxInfoById(id);
        if (userPO != null && !StringUtils.isEmpty(userPO.getWxOpenId())) {
            // 查询加载session信息
            WxSessionVO session = ccUserService.getWxSession(userPO.getWxOpenId());
            if (null != session) {
                ccUserService.reloadWxSession(userPO.getWxOpenId(), null, session.getChannel(), null);
            }
        }
    }

    /**
     * 更新用户邮箱
     *
     * @param id
     * @param userEmail
     */
    public void updateUserEmail(Integer id, String userEmail) {
        userMapper.updateUserEmail(id, userEmail);
    }

    /**
     * 更新切换组织时间
     *
     * @param id
     */
    public void updateSwitchTime(Integer id, String jobCode, String postCode, String postName) {
        userMapper.updateSwitchTime(id, jobCode, postCode, postName);
    }

    /**
     * 查询用户信息
     *
     * @param wxOpenId
     * @param recommendId
     * @return
     */
    public WxUserVO getUserInfoByOpenIdAndUserId(String wxOpenId, String recommendId) {
        return userMapper.getUserInfoByOpenIdAndUserId(wxOpenId, recommendId);
    }

    /**
     * 微信绑定用户下载
     *
     * @param keyword
     * @param response
     */
    public void downloadUsers(String keyword, HttpServletResponse response) {
        List<UserVO> users = userMapper.listUsers(keyword, false);
        users.stream().forEach(u -> {
            if (Objects.equals(u.getStatus(), BaseConstants.USER_STATUS_2)) {
                u.setStatus("试用");
            } else if (Objects.equals(u.getStatus(), BaseConstants.USER_STATUS_3)) {
                u.setStatus("正式");
            } else if (Objects.equals(u.getStatus(), BaseConstants.USER_STATUS_8)) {
                u.setStatus("离职");
            } else if (Objects.equals(u.getStatus(), BaseConstants.USER_STATUS_12)) {
                u.setStatus("外部员工");
            }
        });
        try (OutputStream os = response.getOutputStream()) {
            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode("绑定微信用户数据", StandardCharsets.UTF_8.name())
                    + new SimpleDateFormat("yyyy_MM_dd").format(new Date()) + ".xlsx");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil.newInstance()
                    .createSheet("用户列表")
                    .buildSheetHead(UserVO.class)
                    .addSheetData(users)
                    .write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    /**
     * 导入机构创新业务对接人与负责人信息表
     *
     * @param file
     * @return
     */
    public void importOrgPic(MultipartFile file) {
        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(file);

        Map<String, OrgVO> orgMap = new HashMap<>();
        orgMapper.listOrgs().forEach(o -> orgMap.put(o.getOrgName(), o));

        Sheet sheet = workbook.getSheetAt(0);
        int numOfRows = sheet.getLastRowNum() + 1;
        List<OrgPicExtraDTO> pics = new ArrayList<>(numOfRows - 1);
        for (int i = 1; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            String c1 = ExcelReadUtil.getCellValue(row.getCell(0));
            String c2 = ExcelReadUtil.getCellValue(row.getCell(1));
            String c3 = ExcelReadUtil.getCellValue(row.getCell(2));
            String c4 = ExcelReadUtil.getCellValue(row.getCell(3));
            String c5 = ExcelReadUtil.getCellValue(row.getCell(4));

            OrgPicExtraDTO po = new OrgPicExtraDTO();
            if (orgMap.get(c1) != null) {
                po.setRegionHrId(orgMap.get(c1).getHrOrgId());
            }
            po.setRegionName(c1);
            OrgVO orgVO = orgMap.get(c2);
            if (orgVO != null) {
                po.setOrgHrId(orgVO.getHrOrgId());
                po.setOrgCode(orgVO.getOrgCode());
            }
            po.setOrgName(c2);
            po.setUserId(c4);
            po.setUserName(c3);
            po.setUserJobPost(c5);
            po.setJobCode(null);
            AuthUserVO userVO = getAuthUserByUserId(c4);
            po.setMainJobNumber(userVO.getMainJobNumber());
            pics.add(po);
        }
        pics.forEach(p -> {
            picMapper.deleteOrgPicExtraByOrgId(p.getRegionHrId(), p.getOrgHrId());
            picMapper.insertOrgPicExtra(p);
        });
    }

    /**
     * 机构创新业务对接人分页查询
     *
     * @param query
     * @return
     */
    public PageInfo<OrgPicExtraVO> getOrgPicExtraByPage(OrgPicExtraQuery query) {
        if (query != null) {
            PageHelper.startPage(query.getPage(), query.getSize());
        }
        return new PageInfo<>(picMapper.listOrgPicExtra(query));
    }

    /**
     * 通过工号查询机构创新业务对接人
     *
     * @param userId
     * @return
     */
//    public OrgPicExtraVO getOrgPicExtraByUserId(String userId) {
//        return picMapper.getOrgPicExtraByUserId(userId);
//    }
    public OrgPicExtraVO getOrgPicExtraByUserIdAndOrg(String userId, String orgName) {
        return picMapper.getOrgPicExtraByUserIdAndOrg(userId, orgName);
    }

    /**
     * 通过客户经理工号查询对应机构创新业务对接人
     *
     * @param userId
     * @return
     */
    public List<AuthUserVO> listOrgPicUserInfoByAdminUserId(String userId) {
        return picMapper.listOrgPicUserInfoByAdminUserId(userId);
    }

    /**
     * 通过区域和机构名称查询机构创新业务对接人
     *
     * @param regionName
     * @return
     */
    public OrgPicExtraVO getOrgPicExtraByRegionAndOrg(String regionName, String orgName) {
        return picMapper.getOrgPicExtraByRegionAndOrg(regionName, orgName);
    }

    /**
     * 新建机构创新业务对接人
     *
     * @param dto
     * @return
     */
    public void insertOrgPicExtra(OrgPicExtraDTO dto) {
        Map<String, OrgVO> orgMap = new HashMap<>();
        orgMapper.listOrgs().forEach(o -> orgMap.put(o.getOrgName(), o));
        /*dto.setRegionHrId(orgMap.get(dto.getRegionName()));
        dto.setOrgHrId(orgMap.get(dto.getOrgName()));*/
        if (orgMap.get(dto.getRegionName()) != null) {
            dto.setRegionHrId(orgMap.get(dto.getRegionName()).getHrOrgId());
        }
        OrgVO orgVO = orgMap.get(dto.getOrgName());
        if (orgVO != null) {
            dto.setOrgHrId(orgVO.getHrOrgId());
            dto.setOrgCode(orgVO.getOrgCode());
        }
        //查用户主工号
        AuthUserVO userVO = getAuthUserByUserId(dto.getUserId());
        dto.setMainJobNumber(userVO.getMainJobNumber());
        picMapper.deleteOrgPicExtraByOrgId(dto.getRegionHrId(), dto.getOrgHrId());
        picMapper.insertOrgPicExtra(dto);
    }

    /**
     * 更新机构创新业务对接人
     *
     * @param entity
     * @return
     */
    public void updateOrgPicExtra(OrgPicExtraDTO entity) {
        Map<String, OrgVO> orgMap = new HashMap<>();
        orgMapper.listOrgs().forEach(o -> orgMap.put(o.getOrgName(), o));
        if (orgMap.get(entity.getRegionName()) != null) {
            entity.setRegionHrId(orgMap.get(entity.getRegionName()).getHrOrgId());
        }
        OrgVO orgVO = orgMap.get(entity.getOrgName());
        if (orgVO != null) {
            entity.setOrgHrId(orgVO.getHrOrgId());
            entity.setOrgCode(orgVO.getOrgCode());
        }
        //查用户主工号
        AuthUserVO userVO = getAuthUserByUserId(entity.getUserId());
        entity.setMainJobNumber(userVO.getMainJobNumber());

        picMapper.updateOrgPicExtra(entity);
    }

    /**
     * 更新员工状态（试用 在职 离职）
     *
     * @param id
     * @param status
     */
    public void updateEmployeeStatus(int id, int status) {
        userMapper.updateEmployeeStatus(id, status);
    }

    public void batchUpdateEmployeeStatus(List<String> collect, int status) {
        userMapper.batchUpdateEmployeeStatus(collect, status);
    }

    /**
     * 初始化员工代理账户信息
     *
     * @param userId
     * @return
     */
    public void openAgentAccountByEmployeeUserId(String userId) {
        AuthUserVO authUser = getAuthUserByUserId(userId);
        if (authUser.getAgentId() == null) {
            SmAgentVO agentVO = agentService.getAgentByAgentBindUserId(authUser.getUserId());
            if (agentVO == null) {
                agentVO = agentService.getAgentByAgentMobile(authUser.getUserMobile());
                if (agentVO == null) {
                    userMapper.insertAndUpdateEmployeeAgent(userId);
                } else {
                    SmAgentRegisterDTO dto = new SmAgentRegisterDTO();
                    dto.setAgentId(agentVO.getAgentId());
                    dto.setAgentTopUserId(userId);
                    dto.setAgentBindUserId(userId);
                    userMapper.updateEmployeeAgentBindInfo(dto);
                    agentService.updateAgentEmployeeInfo(authUser.getUserMobile(), userId);
                }
            } else {
                userMapper.updateUserBindAgentIdInfo(userId, agentVO.getAgentId(), userId);
            }
        }
    }

    /**
     * 开通内部员工代理人账号
     *
     * @param orgPath
     * @return
     */
    public void openAgentAccountByOrgPath(String orgPath) {
        userMapper.listUnRegisteredEmployeeeUserIdByOrgPath(orgPath)
                .forEach(this::openAgentAccountByEmployeeUserId);
    }

    /**
     * 更新员工代理人状态（试用 在职 离职）
     *
     * @param userId
     */
    public void updateEmployeeLeaveOfficeAgentInfo(String userId) {
        AuthUserVO authUser = getAuthUserByUserId(userId);
        if (authUser == null) {
            return;
        }
        // 没有开通代理人账号
        if (authUser.getAgentId() == null) {
            // 自动微信解绑
            userMapper.deleteUserWxInfoById(authUser.getId());
        }
        // 已开通代理人账号
        else {
            // 不解绑微信 内部员工变成代理人角色
            userMapper.updateUserType(authUser.getId(), "agent");
        }
    }

    /**
     * 删除机构创新业务对接人
     *
     * @param id
     * @return
     */
    public void deleteOrgPicExtra(Integer id) {
        picMapper.deleteOrgPicExtraById(id);
    }

    /**
     * 当前用户是否创新业务对接人
     *
     * @param userId
     * @return
     */
    @Cacheable(value = USER_IS_ORG_ADMIN, key = "#userId")
    public boolean isOrgAdmin(String userId) {
        return Objects.nonNull(picMapper.getOrgPicExtraByUserId(userId));
    }

    /**
     * 获取保全业务支持的所有员工
     *
     * @return
     */
    public List<UserVO> listByBizSupport() {
        List<UserRoleVO> userRoleVOS = bmsService.listUserByRoleCode(EnumBmsRole.BIZ_SUPPORT.getCode());

        log.info("获取bms角色数据{}", userRoleVOS);
        List<String> collect = userRoleVOS.stream().map(UserRoleVO::getJobNumber)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        return getUsersByPageWithJobNumbers(collect);
    }


    /**
     * 获取保全业务支持的所有员工
     *
     * @return
     */
    public List<UserVO> listByClaimAdmin() {
        List<UserRoleVO> userRoleVOS = bmsService.listUserByRoleCode(EnumBmsRole.CLAIM_ADMIN.getCode());

        log.info("获取bms角色数据{}", userRoleVOS);
        List<String> collect = userRoleVOS.stream().map(UserRoleVO::getJobNumber)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());
        return getUsersByPageWithJobNumbers(collect);
    }

    public UserPost getUserPostByJobCode(String jobCode) {
        /*if (jobCode == null) {
            return null;
        }*/
        if (StringUtils.isEmpty(jobCode)) {
            return null;
        }
        return userPostMapper.selectUserPostByJobCode(jobCode);
    }

    /**
     * 是否总部用户【判断逻辑是取对应机构是否有创新业务对接人 】
     *
     * @param jobNumber
     * @return
     */
    public boolean isHeadUser(@NotNull String jobNumber) {
        if (StringUtils.isEmpty(jobNumber)) {
            return false;
        }
        //查询机构对接人
        return CollectionUtils.isEmpty(picMapper.listByCustomerUserId(jobNumber));
    }

    /**
     * 根据身份证号码查询
     *
     * @param idNo
     * @return
     */
    public UserPost getUserByIdNo(String idNo) {

        return userMapper.getUserPostMasterByIdNo(idNo);
    }


    public List<AuthUserVO> listUsersByJobNumbers(List<String> userIdList) {
        return userMapper.listUsersByJobNumbers(userIdList);
    }

    public List<AuthUserVO> listUsersByName(List<String> userNameList) {
        return userMapper.listUsersByName(userNameList);
    }


    public List<WxUserVO> listUsersByPhones(List<String> phones) {
        return userMapper.listUserNormalByUserMobiles(phones);
    }

    /**
     * 初始化所有人的推荐码
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public int initBizCodes() {

        List<String> idNumbers = userMapper.listAllNoBizCodeIdNumbers();
        for (String idNumber : idNumbers) {
            //修改业务编码，啊
            /*String bizCode = userMapper.getUserBizCodeByIdNumber(idNumber);
            if (org.apache.commons.lang3.StringUtils.isBlank(bizCode)) {
                bizCode = genAvailableBizCode();
            }*/
            String bizCode = getBizCodeByIdNumber(idNumber);
            userMapper.updateUserBizCode(idNumber, bizCode);
        }
        return idNumbers.size();
    }

    private String getBizCodeByIdNumber(String idNumber) {
        String bizCode = userMapper.getUserBizCodeByIdNumber(idNumber);
        if (org.apache.commons.lang3.StringUtils.isBlank(bizCode)) {
            bizCode = genAvailableBizCode();
        }
        return bizCode;
    }


    /**
     * 生成一个可用的推荐码
     *
     * @return
     */
    public String genAvailableBizCode() {

        List<String> collect = Stream.generate(UserService::genBizCode).distinct().limit(22).collect(Collectors.toList());
        List<String> availableBizCode = userMapper.getAvailableBizCode(collect);
        if (CollectionUtils.isEmpty(availableBizCode)) {
            throw new BizException(ExcptEnum.BIZ_CODE_ERROR);
        }
        return availableBizCode.iterator().next();
    }

    /**
     * 生成一个随机码
     *
     * @return
     */
    public static String genBizCode() {
        String v = RandomUtils.nextDouble() + "";
        if (v.length() < 10) {
            return genBizCode();
        }
        return v.substring(2, 8);
    }

    public int updateBizCode(String idCard, String bizCode) {
        List<String> exists = userMapper.getAvailableBizCode(Collections.singletonList(bizCode));

        if (CollectionUtils.isEmpty(exists)) {
            return userMapper.updateUserBizCode(idCard, bizCode);
        } else {
            throw new BizException(ExcptEnum.BIZ_CODE_ERROR.getCode(), "推荐码已存在");
        }
    }

    /**
     * 根据bizcode 获取
     *
     * @param bizCode
     * @return
     */
    public WxUserVO getUserByBizCode(String bizCode) {
        if (StringUtils.isEmpty(bizCode)) {
            return null;
        }

        String mainJobNumberByBizCode = userMapper.getMainJobNumberByBizCode(bizCode);
        if (StringUtils.isEmpty(mainJobNumberByBizCode)) {
            return null;
        }
        return userMapper.getUserByUserId(mainJobNumberByBizCode);
    }

    /**
     * 查询主任 如果主任不存在就会查询副主任
     *
     * @param regionName
     * @param organizationName
     * @return
     */
    public AuthUserVO getOrgHeadByRegionAndOrg(String regionName, String organizationName) {

        AuthUserVO header = getUserByPostAndOrg(EnumPostCode.ORG_HEAD.getCode(), regionName, organizationName);
        if (Objects.isNull(header)) {
            return getUserByPostAndOrg(EnumPostCode.VICE_HEAD.getCode(), regionName, organizationName);
        }
        return header;
    }


    /**
     * 获取用户所在机构的渠道PCO
     *
     * @param createUserId
     * @return
     */
    public List<AuthUserVO> getUserChannelPCO(String createUserId) {
        AuthUserVO authUserByUserId = getAuthUserByUserId(createUserId);
        if (Objects.isNull(authUserByUserId)) {
            return Lists.newArrayList();
        }
        List<UserRoleVO> channelPCOUserRoleList = getChannelPCOUserRoleList(authUserByUserId.getHrOrgId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(channelPCOUserRoleList)) {
            return Lists.newArrayList();
        }
        List<String> userIdList = channelPCOUserRoleList.stream()
                .map(UserRoleVO::getJobNumber)
                .collect(Collectors.toList());
        return listUsersByJobNumbers(userIdList);
    }

    /**
     * 查询机构的渠道PCO(取任意一个渠道PCO)
     *
     * @param hrOrgId
     * @return
     */
    public AuthUserVO getChannelPCOByHrOrgId(Integer hrOrgId) {
        List<UserRoleVO> orgUserRoleList = getChannelPCOUserRoleList(hrOrgId);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgUserRoleList)) {
            return null;
        }
        UserRoleVO userRoleVO = orgUserRoleList.iterator().next();
        return userMapper.getAuthUserByUserId(userRoleVO.getJobNumber());
    }


    public AuthUserVO getChannelSupervisorByHrOrgId(Integer hrOrgId) {
        List<UserRoleVO> orgUserRoleList = getChannelSupervisorUserRoleList(hrOrgId);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgUserRoleList)) {
            return null;
        }
        UserRoleVO userRoleVO = orgUserRoleList.iterator().next();
        return userMapper.getAuthUserByUserId(userRoleVO.getJobNumber());
    }

    /**
     * 通过hr机构id获取
     *
     * @param hrOrgId
     * @return
     */
    public List<UserRoleVO> getChannelSupervisorUserRoleList(Integer hrOrgId) {
        if (Objects.isNull(hrOrgId)) {
            return null;
        }
        return bmsService.getOrgNormalUserRoleList(hrOrgId, Lists.newArrayList(EnumBmsRole.CHANNEL_SUPERVISE.getCode()));
    }

    /**
     * 通过hr机构id获取
     *
     * @param hrOrgId
     * @return
     */
    public List<UserRoleVO> getChannelPCOUserRoleList(Integer hrOrgId) {
        if (Objects.isNull(hrOrgId)) {
            return null;
        }
        return bmsService.getOrgNormalUserRoleList(hrOrgId, Lists.newArrayList(EnumBmsRole.PCO_INFO.getCode()));
    }

    /**
     * 通过hr机构id获取
     *
     * @param hrOrgId
     * @return
     */
    public List<UserRoleVO> getOrgHeadRoleList(Integer hrOrgId) {
        if (Objects.isNull(hrOrgId)) {
            return null;
        }
        return bmsService.getOrgNormalUserRoleList(hrOrgId, Lists.newArrayList(EnumBmsRole.ROLE_ORG_HEAD.getCode()));
    }


    /**
     * 通过机构信息获取机构的渠道PCO
     *
     * @param regionName
     * @param organizationName
     * @return
     */
    public AuthUserVO getChannelPCOBydOrg(String regionName, String organizationName) {
        List<AuthUserVO> authUserVOS = userMapper.listOrgUserByPost(null, regionName, organizationName);
        if (CollectionUtils.isEmpty(authUserVOS)) {
            return null;
        }
        return getChannelPCOByHrOrgId(authUserVOS.iterator().next().getHrOrgId());
    }

    public AuthUserVO getChannelSupervisorBydOrg(String regionName, String organizationName) {
        List<AuthUserVO> authUserVOS = userMapper.listOrgUserByPost(null, regionName, organizationName);
        if (CollectionUtils.isEmpty(authUserVOS)) {
            return null;
        }
        return getChannelSupervisorByHrOrgId(authUserVOS.iterator().next().getHrOrgId());
    }

    public AuthUserVO getRoleOrgHeadByOrg(String regionName, String organizationName) {
        List<AuthUserVO> authUserVOS = userMapper.listOrgUserByPost(null, regionName, organizationName);
        if (CollectionUtils.isEmpty(authUserVOS)) {
            return null;
        }
        return getRoleOrgHeadByOrgId(authUserVOS.iterator().next().getHrOrgId());
    }

    private AuthUserVO getRoleOrgHeadByOrgId(Integer hrOrgId) {
        List<UserRoleVO> orgUserRoleList = getOrgHeadRoleList(hrOrgId);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(orgUserRoleList)) {
            return null;
        }
        UserRoleVO userRoleVO = orgUserRoleList.iterator().next();
        return userMapper.getAuthUserByUserId(userRoleVO.getJobNumber());
    }


    public List<UserVO> listUserByOrg(String keyword, String organizationName) {
        return userMapper.listUserByOrg(keyword, organizationName);
    }

    private AuthUserVO getUserByPostAndOrg(String postCode, String regionName, String organizationName) {
        List<AuthUserVO> authUserVOS = userMapper.listOrgUserByPost(postCode, regionName, organizationName);
        if (CollectionUtils.isEmpty(authUserVOS)) {
            return null;
        }
        return authUserVOS.iterator().next();
    }


    public List<UserPost> listUserPostByUserIds(List<String> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        //UserService userService = SpringFactoryUtil.getBean(UserService.class);
        //UserPostService postService = SpringFactoryUtil.getBean(UserPostService.class);

        List<AuthUserVO> userList = listUsersByJobNumbers(userIdList);
        List<String> jobCodeList = userList.stream().map(AuthUserVO::getJobCode).filter(v -> !StringUtils.isEmpty(v)).collect(Collectors.toList());
        List<UserPost> userPostList = userPostService.listUserPostByJobCodes(jobCodeList);
        log.info("获取到用户职位信息为：{}", userPostList.size());
        //auth_user表中jobCode为空的情况处理
        List<AuthUserVO> jobCodeNull_authUserList = userList.stream().filter(u -> StringUtils.isEmpty(u.getJobCode())).collect(Collectors.toList());
        List<String> jobNumberList = jobCodeNull_authUserList.stream().map(AuthUserVO::getUserId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(jobNumberList)) {
            log.info("获取到auth_user表中jobCode为空：{}", jobNumberList.size());

            List<UserPost> postList = userPostService.listUserPostByJobNumbers(jobNumberList);
            for (String jobNumber : jobNumberList) {
                Optional<UserPost> opt = postList.stream().filter(p -> Objects.equals(p.getJobNumber(), jobNumber)).findFirst();
                if (opt.isPresent()) {
                    userPostList.add(opt.get());
                }
            }
        }
        return userPostList;
    }

    /**
     * 获取理赔专员
     *
     * @return
     */
    public List<ClaimConfig.User> getSettlements() {
        List<UserRoleVO> userRoleVOS = bmsService.listUserByRoleCode(bmsConfig.getClaimClerkCode());

        return userRoleVOS.stream()
                .map(user -> {
                    ClaimConfig.User model = new ClaimConfig.User();
                    model.setUserId(user.getJobNumber());
                    model.setUserName(user.getUserName());
                    return model;
                }).collect(Collectors.toList());
    }
}
