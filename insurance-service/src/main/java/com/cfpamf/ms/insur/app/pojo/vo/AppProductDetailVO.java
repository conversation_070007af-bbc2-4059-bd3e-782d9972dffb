package com.cfpamf.ms.insur.app.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 微信返产品详情VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class AppProductDetailVO implements Serializable {
    private static final long serialVersionUID = -6962598474653214709L;

    /**
     * productId
     */
    @ApiModelProperty(value = "productId")
    private Integer productId;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 保险产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty("产品简称")
    private String productShortName;

    @ApiModelProperty("capp头部图片地址")
    private String headImageUrl;
    /**
     * 产品标签join
     */
    @JsonIgnore
    @ApiModelProperty(value = "产品标签join")
    private String productTagsJoin;

    /**
     * 产品标签
     */
    @ApiModelProperty(value = "产品标签")
    private String[] productTags;

    /**
     * 产品特色 摘要
     */
    @ApiModelProperty(value = "产品特色")
    private String productFeature;

    /**
     * 产品介绍图片地址
     */
    @ApiModelProperty(value = "产品介绍图片地址")
    private String introduceImageUrl;

    /**
     * 保险公司Id
     */
    @ApiModelProperty(value = "保险公司Id")
    private String companyId;

    /**
     * 保险公司名称
     */
    @ApiModelProperty(value = "保险公司名称")
    private String companyName;

    /**
     * 保险公司图片
     */
    @ApiModelProperty(value = "保险公司图片")
    private String companyLogoImageUrl;

    /**
     * 常见问题
     */
    @JsonIgnore
    private String attentions;

    /**
     * 常见问题
     */
    @ApiModelProperty(value = "常见问题")
    private List<QuestionAnswer> questions;

    /**
     * 保险条款
     */
    @ApiModelProperty(value = "保险条款")
    private List<AppProductClauseVO> clauses;

    /**
     * 最低起保金额
     */
    @ApiModelProperty(value = "最低起保金额")
    private BigDecimal minAmount;

    /**
     * 限购份数
     */
    @ApiModelProperty(value = "限购份数")
    private Integer buyLimit;

    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Integer effectWaitingDayMin;
    private Integer effectWaitingDayMax;

    /**
     * 健康告知
     */
    @JsonIgnore
    @ApiModelProperty(value = "健康告知")
    private String healthNotification;

    /**
     * 健康告知
     */
    @ApiModelProperty(value = "健康告知")
    private List<String> healthNotifications;

    /**
     * 产品特色图片地址列表
     */
    @ApiModelProperty(value = "产品特色图片地址列表")
    private List<String> productSpecials;

    /**
     * 保障计划
     */
    @ApiModelProperty(value = "保障计划")
    private AppCoveragePlanVO coveragePlan;

    /**
     * 产品特色图片URL拼接地址
     */
    @JsonIgnore
    private String productSpecialsJoin;

    public String[] getProductTags() {
        if (productTags != null && productTags.length > 0) {
            return productTags;
        }
        return StringUtils.isEmpty(productTagsJoin) ? new String[0] : productTagsJoin.split(",");
    }

    public List<String> getProductSpecials() {
        if (productSpecials != null && !productSpecials.isEmpty()) {
            return productSpecials;
        }
        return productSpecialsJoin == null ? Collections.emptyList() : Arrays.asList(productSpecialsJoin.split(","));
    }

    @Data
    @Builder
    public static class QuestionAnswer implements Serializable {

        private static final long serialVersionUID = 8207089763471157706L;
        /**
         * 问题
         */
        private String question;
        /**
         * 答案
         */
        private String answer;
    }
}
