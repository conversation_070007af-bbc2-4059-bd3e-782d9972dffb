package com.cfpamf.ms.insur.admin.job;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmAgentMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmAgentMonthAllowanceDTO;
import com.cfpamf.ms.insur.admin.pojo.query.SmAgentTeamCmsQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.SmAgentLevelAllowanceSettingVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmAgentTeamOrderListVO;
import com.cfpamf.ms.insur.base.util.XxlLogger;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * 代理人月度佣金计算定时任务
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
//@JobHandler(value = "agent-month-allowance")
public class AgentMonthAllowanceJobHandler{

    /**
     * 代理人mapper
     */
    @Autowired
    private SmAgentMapper agentMapper;

    /**
     * 定时任务执行方法
     *
     * @return
     */
    @XxlJob("agent-month-allowance")
    public void execute() {
        //新版本传参方式
        String param = XxlJobHelper.getJobParam();

        Date baseDay = DateUtil.addMonth(new Date(), -1);
        if (!StringUtils.isEmpty(param)) {
            baseDay = DateUtil.parseDate(param.trim(), DateUtil.CN_YEAR_MONTH_FORMAT);
        }
        XxlLogger.info(log, "代理人月度佣金计算定时任务 baseDay = " + DateUtil.format(baseDay, DateUtil.CN_YEAR_MONTH_FORMAT));
        Date startDate = DateUtil.getBeginOfMonth(baseDay);
        Date endDate = DateUtil.getEndOfMonth(baseDay);
        SmAgentTeamCmsQuery query = new SmAgentTeamCmsQuery();
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        List<SmAgentTeamOrderListVO> teamTimeCms = agentMapper.listAgentTeamCms(query);
        List<SmAgentLevelAllowanceSettingVO> levelSettings = agentMapper.listAgentLevelAllowanceSettings();
        levelSettings.sort(Comparator.comparing(SmAgentLevelAllowanceSettingVO::getSorting).reversed());
        teamTimeCms.forEach(ttc -> {
            SmAgentLevelAllowanceSettingVO levelSetting = getAgentTeamMatchAllowanceSetting(levelSettings, ttc.getTeamScale(), ttc.getPerQty(), ttc.getOrderQty(), ttc.getTeamOrderAmount());
            if (levelSetting != null) {
                SmAgentMonthAllowanceDTO dto = SmAgentMonthAllowanceDTO.builder()
                        .yearMonth(DateUtil.format(startDate, DateUtil.CN_YEAR_MONTH_FORMAT))
                        .teamId(ttc.getTeamId())
                        .teamName(ttc.getTeamName())
                        .agentId(ttc.getAgentId())
                        .agentName(ttc.getAgentName())
                        .agentMobile(ttc.getAgentMobile())
                        .agentJobNumber(ttc.getAgentJobNumber())
                        .teamOrderAmount(ttc.getTeamOrderAmount())
                        .teamOrderCms(ttc.getTeamOrderCms())
                        .allowanceRatio(levelSetting.getAllowanceRatio())
                        .allowanceAmount(ttc.getTeamOrderCms().multiply(levelSetting.getAllowanceRatio()).divide(new BigDecimal(100), BigDecimal.ROUND_HALF_UP))
                        .levelId(levelSetting.getLevelId())
                        .teamScale(ttc.getTeamScale())
                        .personQty(ttc.getPerQty())
                        .orderQty(ttc.getOrderQty())
                        .build();

                // 新建佣金
//                agentMapper.insertAgentMonthAllowance(dto);
                // 更新代理人等级
                agentMapper.updateAgentLevel(ttc.getAgentId(), levelSetting.getLevelId());

                // 新建佣金提成
//                SmAgentCmsDetailDTO cmsDTO = new SmAgentCmsDetailDTO();
//                cmsDTO.setAccountTime(DateUtil.add(DateUtil.getEndOfMonth(new Date()), -1, Calendar.MONTH));
//                cmsDTO.setCmsType(SmAgentCmsDetailDTO.AGENT_CMS_TYPE_ALLOWANCE);
//                cmsDTO.setCmsAgentId(ttc.getAgentId());
//                cmsDTO.setAllowance(dto.getAllowanceAmount());
//                agentMapper.insertAgentCmsDetail(cmsDTO);
            }
        });
        XxlLogger.info(log, "代理人月度佣金计算定时任务结束 ");
        //return ReturnT.SUCCESS;
    }

    /**
     * 获取当前团队匹配的佣金设置
     *
     * @param teamScale
     * @param personQty
     * @param orderQty
     * @param orderAmount
     * @return
     */
    private SmAgentLevelAllowanceSettingVO getAgentTeamMatchAllowanceSetting(List<SmAgentLevelAllowanceSettingVO> settings, int teamScale, int personQty, int orderQty, BigDecimal orderAmount) {
        for (int i = 0, size = settings.size(); i < size; i++) {
            SmAgentLevelAllowanceSettingVO setting = settings.get(i);
            if (setting.getTeamScaleFrom() == null) {
                setting.setTeamScaleFrom(0);
            }
            if (setting.getTeamScaleTo() == null) {
                setting.setTeamScaleTo(Integer.MAX_VALUE);
            }
            if (setting.getTeamPerRatio() == null) {
                setting.setTeamPerRatio(BigDecimal.ZERO);
            }
            if (setting.getTeamOrderQty() == null) {
                setting.setTeamOrderQty(0);
            }
            if (setting.getTeamOrderAmount() == null) {
                setting.setTeamOrderAmount(BigDecimal.ZERO);
            }
            if (teamScale >= setting.getTeamScaleFrom()
                    && teamScale <= setting.getTeamScaleTo()
                    && personQty >= setting.getTeamPerRatio().multiply(new BigDecimal(teamScale)).divide(BigDecimal.valueOf(100.00), BigDecimal.ROUND_HALF_UP).floatValue()
                    && orderQty >= setting.getTeamOrderQty()
                    && orderAmount.compareTo(setting.getTeamOrderAmount()) >= 0) {
                return setting;
            }
        }
        return null;
    }
}
