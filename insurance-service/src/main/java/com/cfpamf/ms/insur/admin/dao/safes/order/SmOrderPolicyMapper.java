package com.cfpamf.ms.insur.admin.dao.safes.order;

import com.cfpamf.ms.insur.admin.pojo.po.order.FastOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.renewal.RenewalTermPolicyDetailVo;
import com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyVisitNotifyDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SmOrderPolicyMapper extends MyMappler<SmOrderPolicy> {


    /**
     * 批量更新保单回访信息
     *
     * @param dtos
     * @return
     */
    int batchUpdateOrderVisitInfo(@Param("channel") String channel, @Param("dtos") List<SmOrderPolicy> dtos);

    /**
     * 更新退保信息
     *
     * @param smOrderPolicy
     * @return
     */
    @Update("update sm_order_policy set " +
            " policy_state=#{policyState}," +
            " surrender_valid_time=#{surrenderValidTime}," +
            " surrender_no=#{surrenderNo}," +
            " cancel_amount=#{cancelAmount}," +
            " surrender_reason=#{surrenderReason}," +
            " surrender_type=#{surrenderType}," +
            " surrender_time=now() " +
            "   where channel=#{channel} " +
            "   and policy_no=#{policyNo} ")
    int updateOrderCancelInfo(SmOrderPolicy smOrderPolicy);

    /**
     * 根据保单好获取信息
     *
     * @param policyNo
     * @param channel
     * @return
     */
    default SmOrderPolicy getOrderPolicyByPolicyNo(String policyNo, String channel) {
        SmOrderPolicy query = new SmOrderPolicy();
        query.setPolicyNo(policyNo);
        //query.setChannel(channel);

        return selectOne(query);
    }

    /**
     * 根据订单号获取信息
     *
     * @param fhOrderId
     * @return
     */
    default SmOrderPolicy getOrderPolicyByFhOrderId(String fhOrderId) {
        SmOrderPolicy query = new SmOrderPolicy();
        query.setFhOrderId(fhOrderId);

        return selectOne(query);
    }


    default List<SmOrderPolicy> selectOrderPolicyByFhOrderId(String fhOrderId) {
        SmOrderPolicy query = new SmOrderPolicy();
        query.setFhOrderId(fhOrderId);

        return select(query);
    }

    /**
     * 根据退保单号和保单号查询个人退保信息
     * @param surrenderNo 退保单号
     * @param policyNo 保单号
     * @return 个人退保信息列表
     */
    default List<SmOrderPolicy> qryPersonalSurrender(String surrenderNo,String policyNo) {
        // 创建查询对象
        SmOrderPolicy query = new SmOrderPolicy();
        // 设置退保单号和保单号
        query.setSurrenderNo(surrenderNo);
        query.setPolicyNo(policyNo);
        // 执行查询并返回结果
        return select(query);
    }


    /**
     * 根据保单好查询订单信息
     *
     * @param policyList
     * @return
     */
    List<FastOrderPolicy> queryByPolicyList(@Param("data") List<String> policyList);


    /**
     * 获取未进行回访的表单集合
     *
     * @param pushProductIdList
     * @return
     */
    List<PolicyVisitNotifyDTO> getUndoVisitPolicyList(@Param("pushProductIdList") List<String> pushProductIdList);


    /**
     * 获取渠道需要初始化的续期数据 期数从2开始
     * 缴费以年为单位 长险 未过订单期限 未产生已经续期期数的数据
     *
     * @param channel
     * @param beforeDay
     * @param defaultGraceDays
     */
    void pushRenewalTerm(@Param("channel") String channel, @Param("beforeDay") Integer beforeDay, @Param("defaultGraceDays") Integer defaultGraceDays);

    /**
     * 渠道初始化第一期续期数据
     *
     * @param channel
     * @param beforeDay
     * @param defaultGraceDays
     */
    void pushFirstPhaseRenewalTerm(@Param("channel") String channel, @Param("beforeDay") Integer beforeDay, @Param("defaultGraceDays") Integer defaultGraceDays);


    /**
     * 获取渠道需要初始化的续期数据 期数从2开始
     * 缴费以年为单位 长险 未过订单期限 未产生已经续期期数的数据
     *
     * @param channel
     * @param beforeDay
     * @param defaultGraceDays
     */
    void pushOtherRenewalTerm(@Param("channel") String channel, @Param("beforeDay") Integer beforeDay, @Param("defaultGraceDays") Integer defaultGraceDays);


    /**
     * 查询第一期续期数据
     *
     *
     * @param defaultGraceDays
     * @param channel
     * @param excludeChannel
     * @param offset
     * @param size
     * @return
     */
    List<SmOrderRenewalTerm> queryFirstPhaseRenewalTerm(@Param("defaultGraceDays") Integer defaultGraceDays, @Param("channel") String channel, @Param("excludeChannel") List<String> excludeChannel,@Param("offset")int offset,@Param("size")int size);

    /**
     * 查询待续期续期数据
     *
     * @param beforeDay
     * @param defaultGraceDays
     * @param channel
     * @param excludeChannel
     * @param offset
     * @param size
     * @return
     */
    List<SmOrderRenewalTerm> queryToRenewalTermList(@Param("beforeDay") Integer beforeDay,@Param("defaultGraceDays") Integer defaultGraceDays, @Param("channel") String channel, @Param("excludeChannel") List<String> excludeChannel,@Param("offset")int offset,@Param("size")int size);

    /**
     * 根据订单号按期数补偿续期初始化数据
     *
     * @param orderIdList
     * @param termNum
     * @param beforeDay
     * @param defaultGraceDays
     * @return
     */
    List<SmOrderRenewalTerm> queryRenewalTermByOrderId(@Param("orderIdList") List<String> orderIdList, @Param("termNum") Integer termNum, @Param("beforeDay") Integer beforeDay, @Param("defaultGraceDays") Integer defaultGraceDays);


    /**
     * 渠道初始化第一期续期数据
     *
     * @param channel
     * @param beforeDay
     * @param defaultGraceDays
     */
    void pushOtherFirstPhaseRenewalTerm(@Param("channel") String channel, @Param("beforeDay") Integer beforeDay, @Param("defaultGraceDays") Integer defaultGraceDays);

    /**
     * 根据订单号按期数补偿续期初始化数据
     *
     * @param orderIdList
     * @param termNum
     * @param beforeDay
     * @param defaultGraceDays
     */
    void pushCompensatePhaseRenewalTerm(@Param("orderIdList") List<String> orderIdList, @Param("termNum") Integer termNum, @Param("beforeDay") Integer beforeDay, @Param("defaultGraceDays") Integer defaultGraceDays);

    /**
     * 续期保单信息
     *
     * @param policyNo
     * @return
     */
    RenewalTermPolicyDetailVo getRenewalTermPolicyDetailVo(@Param("policyNo") String policyNo);

    /**
     * 续期保单信息
     *
     * @param policyNo
     * @return
     */
    List<RenewalTermPolicyDetailVo> listRenewalTermPolicyDetailVo(@Param("policyNo") String policyNo);

    /**
     * 获取保单信息
     *
     * @param channel
     * @param payUnit
     * @param policyNoList
     * @return
     */
    List<SmOrderPolicy> get(@Param("channel") String channel, @Param("payUnit") String payUnit, @Param("policyNoList") List<String> policyNoList);

    List<SmOrderPolicy> queryByPage(@Param("start") Date start,
                                    @Param("end") Date end,
                                    @Param("channel") String channel,
                                    @Param("offset") int offset,
                                    @Param("size") int size);

    void update2BackVisitInfo(@Param("policyNo") String policyNo,
                              @Param("visitStatus") String whaleVisitStatus,
                              @Param("successTime") String successTime,
                              @Param("visitWay") String whaleVisitWay);

    /**
     * 根据订单ids获取保单信息
     *
     * @param fhOrderIds
     * @return
     */
    default List<SmOrderPolicy> listByFhOrderIds(List<String> fhOrderIds) {
        Example e = new Example(SmOrderPolicy.class);
        e.createCriteria().andIn("fhOrderId", fhOrderIds);
        return selectByExample(e);
    }

    /**
     * 获取渠道订单支付方式为空的数据
     *
     * @param channel
     * @return
     */
    default List<SmOrderPolicy> listNullPayType(String channel) {
        Example e = new Example(SmOrderPolicy.class);
        e.createCriteria().andEqualTo("channel", channel).andIsNull("payType");
        return selectByExample(e);
    }

    int changePlan(@Param("orderId") String orderId, @Param("plan") SmPlanVO planVO);
    /**
     * 保单退费(全额退费)
     * @param orderId 订单ID
     * @param policyStatus 保单状态
     */
    int refund(@Param("orderId") String orderId, @Param("policyStatus")String policyStatus);

    /**
     * 保单重新缴费
     * @param orderId 订单ID
     * @param policyStatus 保单状态
     */
    int repay(@Param("orderId") String orderId, @Param("policyStatus")String policyStatus);

    @Update("update sm_order_policy set enabled_flag=-1,update_time=now() where fh_order_id=#{orderId} and enabled_flag=0;")
    int logicDelete(@Param("orderId") String orderId);
}
