package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.constant.product.EnumProductOccupation;
import com.cfpamf.ms.insur.admin.dao.safes.SmOccupationMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.ProductOccupationMapper;
import com.cfpamf.ms.insur.admin.pojo.dto.SmOccupationDTO;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.product.ProductOccupation;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.occupation.EnumOccupationConfig;
import com.cfpamf.ms.insur.admin.service.occupation.OccupationRuleHelper;
import com.cfpamf.ms.insur.app.pojo.vo.AppJobVO;
import com.cfpamf.ms.insur.base.constant.RedisCache;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.dao.safes.WxGlProductQuoteMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.SmOrderMinInfo;
import com.cfpamf.ms.insur.weixin.pojo.query.WxGlProductQuoteQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteListVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxGlProductQuoteResultVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxTreeVO;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

import static com.cfpamf.ms.insur.base.constant.CacheKeyConstants.PRODUCT_FORM_OCUP;

/**
 * 保险公司职业service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class OccupationService {

    /**
     * redis 缓存
     */
    @Autowired
    private RedisUtil<String, String> redisUtil;

    /**
     * 保险公司职业mapper
     */
    @Autowired
    private SmOccupationMapper mapper;

    /**
     * 产品mapper
     */
    @Autowired
    private SmProductMapper pmapper;

    @Autowired
    private SmOrderMapper smOrderMapper;

    @Autowired
    private SmOrderInsuredMapper orderInsuredMapper;

    @Value("${occupation-change-time:20230602120000}")
    private String OCCUPATION_CHANGE_TIME;

    @Value("${occupation-flag:1}")
    private int flag;

    /**
     * 这是个临时方案
     *
     * @param companyId
     * @param productId
     * @param policyNo
     * @return
     */
    public List<WxTreeVO> getOccupationList(int companyId, int productId, String policyNo) {
        log.info("开始查询职业表:{},{},{}", companyId, productId, policyNo);
        if (StringUtils.isBlank(policyNo)) {
            return getOccupationList(companyId, productId);
        }
        SmOrder order = smOrderMapper.queryByPolicyNo(policyNo);
        if (order == null) {
            return getOccupationList(companyId, productId);
        }
        return listOccupation4Correct(companyId, productId, order);
    }

    /**
     * 这是个临时方案
     * 默认从老版本中拉取职业表，后续版本会优化
     *
     * @param companyId
     * @param productId
     * @param orderId   新契约的订单Id
     * @return
     */
    public List<WxTreeVO> listOccupation4Correct(Integer companyId, Integer productId, String orderId) {

        SmOrder order = smOrderMapper.queryByOrderId(orderId);
        if (order == null) {
            log.warn("订单信息不存在:{},{},{}", companyId, productId, orderId);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单信息不存在,请检查参数");
        }
        return listOccupation4Correct(companyId, productId, order);
    }

    @Autowired
    private OccupationRuleHelper occupationRuleHelper;

    /**
     * 这是个临时方案
     * 默认从老版本中拉取职业表，后续版本会优化
     *
     * @param companyId
     * @param productId
     * @param order     新契约的订单Id
     * @return
     */
    public List<WxTreeVO> listOccupation4Correct(Integer companyId, Integer productId, SmOrder order) {

        if (order == null) {
            log.warn("订单信息不存在:{},{},{}", companyId, productId);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "订单信息不存在,请检查参数");
        }

        String orderId = order.getFhOrderId();
        List<SmOrderInsured> orderInsuredList = smOrderMapper.listSmOrderInsuredByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderInsuredList)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人信息不存在，无法获取新契约的职业表");
        }
        SmOrderInsured luckVo = orderInsuredList.get(0);
        EnumOccupationConfig config = occupationRuleHelper.checkRule(luckVo.getOccupationVersion(),companyId, order.getCreateTime());
        Set<String> applyGroup = orderInsuredList.stream().map(SmOrderInsured::getOccupationGroup).collect(Collectors.toSet());
        if (Objects.equals(config, EnumOccupationConfig.COMPANY)) {
            log.info("从保司配置中查询渠道配置的职业表:{},{},{}", companyId, productId, config);
            return listOccupationByCompany(companyId, productId, applyGroup);
        }
        return listOccupation(companyId, productId, applyGroup);
    }

    /**
     * 查询产品职业列表
     * 该数据只适用于新契约流程的职业表查询
     *
     * @param companyId
     * @param productId
     * @return
     */
    public List<WxTreeVO> getOccupationList(int companyId, int productId) {
        Set<String> includeGroup = new HashSet<>(0);
        if (productId > 0) {
            includeGroup = pmapper.listProductFormLimits(productId)
                    .stream()
                    .map(ProductFormLimitVO::getLimitCode)
                    .collect(Collectors.toSet());
        }
        return listOccupation(companyId, productId, includeGroup);
    }


    /**
     * 查询产品职业列表
     *
     * @param companyId
     * @param productId
     * @return
     */
    public List<WxTreeVO> getOccupationListByOrderId(int companyId, int productId, String orderId) {
        Set<String> occupationGroupList = getOccupationGroupListByOrderId(orderId);
        return listOccupation(companyId, productId, occupationGroupList);
    }

    public void switchJobVersion(String policyNo, String version) {
        List<SmOrderInsured> orderInsuredList = orderInsuredMapper.selectByPolicyNo(policyNo);
        if(CollectionUtils.isEmpty(orderInsuredList)){
            throw new MSBizNormalException("-1","保单号不存在");
        }
        for(SmOrderInsured orderInsured:orderInsuredList){
            orderInsured.setOccupationVersion(version);
            orderInsuredMapper.updateJobVersion(orderInsured);
        }
    }
    /**
     * 查询职业表
     *
     * @param companyId
     * @param productId
     * @param includeGroup
     * @return
     */
    private List<WxTreeVO> listOccupation(Integer companyId, Integer productId, Set<String> includeGroup) {
        Collection<SmOccupationVO> data = listProductOccupation(companyId, productId, includeGroup);
        List<ProductOccupation> unInsureList = queryUnInsureBusiness(productId);
        Map<String, ProductOccupation> unInsureMap = LambdaUtils.safeToMap(unInsureList, ProductOccupation::getOccupationCode);
        log.info("拒保职业表:{}", unInsureList);

        List<WxTreeVO> wxTreeVos = data.stream()
                .filter(entry -> {
                    return !unInsureMap.containsKey(entry.getOccupationCode());
                })
                .map(this::mapperToWxTreeVo)
                .collect(Collectors.toList());
        wxTreeVos.sort(Comparator.comparing(WxTreeVO::getValue));

        // 职业第二级类别编码
        List<String> categoryCodes2 = data.stream()
                .filter(entry -> !StringUtils.isEmpty(entry.getOccupationGroup()))
                .map(SmOccupationVO::getParentCode)
                .collect(Collectors.toList());

        Set<String> c2Filter = new HashSet<>(categoryCodes2);

        // 职业第一级类别编码
        List<String> categoryCodes1 = data.stream()
                .filter(entry -> c2Filter.contains(entry.getCategoryCode()))
                .map(SmOccupationVO::getParentCode)
                .collect(Collectors.toList());

        Set<String> c1Filter = new HashSet<>(categoryCodes1);
        return wxTreeVos.stream()
                .filter(o -> c1Filter.contains(o.getValue()) || c2Filter.contains(o.getValue()) || c2Filter.contains(o.getParent()))
                .collect(Collectors.toList());
    }


    /**
     * 查询该渠道配置的职业表
     *
     * @param companyId
     * @param productId
     * @param includeGroup
     * @return
     */
    private List<WxTreeVO> listOccupationByCompany(Integer companyId, Integer productId, Set<String> includeGroup) {
        List<SmOccupationVO> data = mapper.listOccupations(companyId, null, includeGroup, true);
        /**
         * 排除拒保职业
         */
        List<ProductOccupation> unInsureList = queryUnInsureBusiness(productId);
        log.info("当前险种配置的拒保职业表:{}-{}",productId,JSON.toJSONString(unInsureList));
        Map<String, ProductOccupation> unInsureMap = LambdaUtils.safeToMap(unInsureList, ProductOccupation::getOccupationCode);

        List<WxTreeVO> wxTreeVos = data.stream()
                .filter(entry -> {
                    return !unInsureMap.containsKey(entry.getOccupationCode());
                })
                .map(this::mapperToWxTreeVo)
                .collect(Collectors.toList());
        wxTreeVos.sort(Comparator.comparing(WxTreeVO::getValue));

        // 职业第二级类别编码
        List<String> categoryCodes2 = data.stream()
                .filter(entry -> !StringUtils.isEmpty(entry.getOccupationGroup()))
                .map(SmOccupationVO::getParentCode)
                .collect(Collectors.toList());

        Set<String> c2Filter = new HashSet<>(categoryCodes2);

        // 职业第一级类别编码
        List<String> categoryCodes1 = data.stream()
                .filter(entry -> c2Filter.contains(entry.getCategoryCode()))
                .map(SmOccupationVO::getParentCode)
                .collect(Collectors.toList());

        Set<String> c1Filter = new HashSet<>(categoryCodes1);
        return wxTreeVos.stream()
                .filter(o -> c1Filter.contains(o.getValue()) || c2Filter.contains(o.getValue()) || c2Filter.contains(o.getParent()))
                .collect(Collectors.toList());
    }


    /**
     * 列出产品的职业表:优先取产品配置的职业表,再取公司配置的职业表
     *
     * @param productId
     * @param companyId
     * @param includeGroupList
     * @return
     */
    private Collection<SmOccupationVO> listProductOccupation(Integer companyId, Integer productId, Set<String> includeGroupList) {
        log.info("开始查询产品&渠道配置的职业表数据:{},{},{}", companyId, productId, includeGroupList);
        List<ProductOccupation> productOccupationList = queryInsureBusiness(productId);

        if (CollectionUtils.isEmpty(productOccupationList)) {
            log.info("当前产品未配置职业表,开始查询公司的职业表:{},{}", productId, companyId);
            return mapper.listOccupations(companyId, null, includeGroupList, true);
        }

        Map<String, SmOccupationVO> dataMap = new HashMap<>(productOccupationList.size());
        for (ProductOccupation entry : productOccupationList) {
            if (!includeGroupList.contains(entry.getOccupationGroup())) {
                continue;
            }
            String key = entry.getCategory2() + entry.getOccupationCode();
            if (!dataMap.containsKey(key)) {
                SmOccupationVO data = new SmOccupationVO();
                data.setCompanyId(companyId);
                data.setParentCode(entry.getCategory2());
                data.setOccupationName(entry.getOccupationName());
                data.setOccupationCode(entry.getOccupationCode());
                data.setOccupationGroup(entry.getOccupationGroup());
                dataMap.put(key, data);
            }

            String key2 = entry.getCategory1() + entry.getCategory2();
            if (!dataMap.containsKey(key2)) {
                SmOccupationVO data = new SmOccupationVO();
                data.setCompanyId(companyId);
                data.setParentCode(entry.getCategory1());
                data.setCategoryCode(entry.getCategory2());
                data.setCategoryName(entry.getCategory2Name());
                dataMap.put(key2, data);
            }

            String key3 = "-" + entry.getCategory1();
            if (!dataMap.containsKey(key3)) {
                SmOccupationVO data = new SmOccupationVO();
                data.setCompanyId(companyId);
                data.setCategoryCode(entry.getCategory1());
                data.setCategoryName(entry.getCategory1Name());
                dataMap.put(key3, data);
            }
        }
        return dataMap.values();

    }

    @Autowired
    private ProductOccupationMapper productOccupationMapper;

    /**
     * 查询拒保职业列表
     */
    private List<ProductOccupation> queryUnInsureBusiness(Integer productId) {
        return productOccupationMapper.queryByProductId(productId, EnumProductOccupation.UN_INSURE_OCCUPATION.getCode());
    }

    /**
     * 查询拒保职业列表
     */
    private List<ProductOccupation> queryInsureBusiness(Integer productId) {
        return productOccupationMapper.queryByProductId(productId, EnumProductOccupation.INSURE_OCCUPATION.getCode());
    }

    /**
     * 查询产品职业列表
     *
     * @param companyId
     * @param productId
     * @return
     */
    public List<AppJobVO> getAppOccupationList(int companyId, int productId, String parentId) {
        List<String> includeGroups = new ArrayList<>();
        if (productId > 0) {
            List<String> ls = pmapper.listProductFormLimits(productId).stream().map(ProductFormLimitVO::getLimitCode).collect(Collectors.toList());
            if (!ls.isEmpty()) {
                includeGroups = ls;
            }
        }
        List<SmOccupationVO> occupationVos = mapper.listOccupationWithGroups(companyId, parentId, includeGroups);

        return occupationVos.stream().map(oc -> {
            AppJobVO appJobVO = new AppJobVO();
            if (!StringUtils.isEmpty(oc.getCategoryCode())) {
                appJobVO.setJobId(oc.getCategoryCode());
                appJobVO.setJobCode(oc.getCategoryCode());
                appJobVO.setJobName(oc.getCategoryName());
            } else {
                appJobVO.setJobId(oc.getOccupationCode());
                appJobVO.setJobCode(oc.getOccupationCode());
                appJobVO.setJobName(oc.getOccupationName());
            }
            return appJobVO;
        }).collect(Collectors.toList());
    }


    /**
     * excel导入职业
     *
     * @param companyId
     * @param file
     * @return
     */
    @CacheEvict(cacheNames = PRODUCT_FORM_OCUP, allEntries = true)
    public int importOccupation(int companyId, MultipartFile file) {
        Workbook workbook = ExcelReadUtil.analysisWorkbookFromFile(file);

        Map<String, SmOccupationDTO> occupationsMap = new TreeMap<>();
        List<SmOccupationDTO> occupationList = new ArrayList();
        Sheet sheet = workbook.getSheetAt(0);
        int numOfRows = sheet.getLastRowNum() + 1;
        String c1 = null;
        String n1 = null;
        String c2 = null;
        String n2 = null;
        for (int i = 0; i < numOfRows; i++) {
            Row row = sheet.getRow(i);
            String c1t = ExcelReadUtil.getCellValue(row.getCell(0));
            if (StringUtil.isNotEmpty(c1t)) {
                c1 = c1t;
            }
            String n1t = ExcelReadUtil.getCellValue(row.getCell(1));
            if (StringUtil.isNotEmpty(c1t)) {
                n1 = n1t;
            }
            String c2t = ExcelReadUtil.getCellValue(row.getCell(2));
            if (StringUtil.isNotEmpty(c2t)) {
                c2 = c2t;
            }
            String n2t = ExcelReadUtil.getCellValue(row.getCell(3));
            if (StringUtil.isNotEmpty(n2t)) {
                n2 = n2t;
            }
            String c3 = ExcelReadUtil.getCellValue(row.getCell(4));
            String n3 = ExcelReadUtil.getCellValue(row.getCell(5));
            String t3 = ExcelReadUtil.getCellValue(row.getCell(6));
            String modifyId = HttpRequestUtil.getUserId();
            if (c1 != null && n1 != null && occupationsMap.get(c1) == null) {
                SmOccupationDTO dto = new SmOccupationDTO();
                dto.setCompanyId(companyId);
                dto.setCategoryCode(c1);
                dto.setCategoryName(n1);
                dto.setModifyBy(modifyId);
                occupationsMap.put(c1, dto);
            }
            if (c2 != null && n2 != null && occupationsMap.get(c2) == null) {
                SmOccupationDTO dto = new SmOccupationDTO();
                dto.setCompanyId(companyId);
                dto.setParentCode(c1);
                dto.setCategoryCode(c2);
                dto.setCategoryName(n2);
                dto.setModifyBy(modifyId);
                occupationsMap.put(c2, dto);
            }
            if (c3 != null && n3 != null && t3 != null && occupationsMap.get(c3) == null) {
                SmOccupationDTO dto = new SmOccupationDTO();
                dto.setCompanyId(companyId);
                dto.setParentCode(c2);
                dto.setOccupationCode(c3);
                dto.setOccupationName(n3);
                dto.setOccupationGroup(t3);
                dto.setModifyBy(modifyId);
                occupationList.add(dto);
            }
        }
        mapper.deleteOccupations(companyId);
        mapper.insertOccupations(new ArrayList<>(occupationsMap.values()));
        mapper.insertOccupations(occupationList);
        clearOccupationCache(companyId);
        return occupationList.size();
    }

    /**
     * 清理职业列表缓存
     *
     * @param companyId
     */
    public void clearOccupationCache(int companyId) {
        List<Integer> productIds = pmapper.queryProductByCompanyId(companyId);
        if (!CollectionUtils.isEmpty(productIds)) {
            for (Integer id : productIds) {
                String cache1 = RedisCache.buildHotOccupationCache(id);
                String cache2 = RedisCache.buildOccupationCache(id);
                redisUtil.remove(cache1);
                redisUtil.remove(cache2);
            }
        }
    }

    public List<String> getOccupationGroupList(Integer companyId, Integer productId) {
        if (productId != null) {
            SmProductDetailVO detailVo = pmapper.getProductById(productId);
            companyId = detailVo.getCompanyId();
        }
        return mapper.getOccupationGroupList(companyId);
    }

    public List<SmOccupationTreeVO> getOccupationByCodes(int companyId, List<String> occCodes) {
        if (CollectionUtils.isEmpty(occCodes)) {
            return Collections.emptyList();
        }
        List<String> collect = occCodes.stream().distinct().collect(Collectors.toList());
        return mapper.getByCompanyIdAndOccCodes(companyId, collect);
    }

    public SmOccupationTreeVO getOccupationParentTree(int companyId, String occCode) {

        SmOccupationTreeVO treeVO = mapper.getByCompanyIdAndOccCode(companyId, occCode, null);
        SmOccupationTreeVO res = treeVO;
        if (treeVO == null) {
            return null;
        }
        int leaf = 0;
        while (++leaf < 10 && !StringUtils.isEmpty(treeVO.getParentCode())) {
            SmOccupationTreeVO temp = mapper.getByCompanyIdAndOccCode(companyId, null, treeVO.getParentCode());
            if (temp == null) {
                break;
            }
            treeVO.setParent(temp);
            treeVO = temp;
        }
        return res;
    }

    /**
     * 查询保险公司职业列表
     *
     * @param companyId
     * @return
     */
    public List<WxTreeVO> getCompanyOccupationList(int companyId) {
        return mapper.listOccupations(companyId, null, null, true)
                .stream().map(this::mapperToWxTreeVo).collect(Collectors.toList());
    }

    /**
     * mapper
     *
     * @param o
     * @return
     */
    private WxTreeVO mapperToWxTreeVo(SmOccupationVO o) {
        WxTreeVO wtv = new WxTreeVO();
        wtv.setParent(o.getParentCode());
        if (StringUtils.isEmpty(o.getOccupationName())) {
            wtv.setName(o.getCategoryName());
            wtv.setValue(o.getCategoryCode());
            wtv.setType(o.getOccupationGroup());
        } else {
            wtv.setName(o.getOccupationName());
            wtv.setValue(o.getOccupationCode());
            wtv.setType(o.getOccupationGroup());
        }
        wtv.setParent(o.getParentCode());
        return wtv;
    }

    /**
     * 查询产品职业列表top排行
     *
     * @param productId
     * @param size
     * @return
     */
    public List<WxTreeVO> getOccupationHotList(int productId, int size) {
        return mapper.getOccupationHotList(productId, size);
    }

    /**
     * 查询产品职业列表top排行
     *
     * @param productId
     * @param size
     * @return
     */
    @Deprecated
    public List<WxTreeVO> getOccupationHotListInCache(int productId, int size) {
        String topCacheKey = RedisCache.buildHotOccupationCache(productId);
        String cacheTops = redisUtil.get(topCacheKey);
        if (cacheTops != null) {
            return JSONArray.parseArray(cacheTops, WxTreeVO.class);
        }

        log.warn("{}-热门职业缓存失效,准备从DB加载...", productId);
        SmProductDetailVO productDetail = pmapper.getProductById(productId);

        if (productDetail == null) {
            log.warn("{}-热门职业搜索,产品信息为空...", productId);
            return Collections.emptyList();
        }

        List<WxTreeVO> tops = null;
        /**
         * 区分个险，团险
         */
        if (Objects.equals(productDetail.getProductAttrCode(), SmConstants.PRODUCT_ATTR_PERSON)) {
            tops = mapper.getOccupationHotList(productId, size);
        } else {
            tops = searchJob4Group(productId, size, productDetail);
        }

        if (!CollectionUtils.isEmpty(tops)) {
            redisUtil.set(topCacheKey, JSONArray.toJSONString(tops), CommonUtil.getTodayNextSeconds());
        }
        return tops;
    }

    /**
     * 团险·查询热门搜索职业
     *
     * @param productId
     * @param size
     * @param product
     * @return
     */
    private List<WxTreeVO> searchJob4Group(Integer productId, Integer size, SmProductDetailVO product) {
        String ocp = product.getGlOcpnGroup();
        Set<String> searchJobClass = new HashSet<>(0);
        if (StringUtils.isNotBlank(ocp)) {
            searchJobClass = new HashSet<>(Arrays.asList(ocp.split(",")));
        }
        WxGlProductQuoteMapper quotePlanMapper = SpringFactoryUtil.getBean(WxGlProductQuoteMapper.class);
        WxGlProductQuoteQuery quoteQuery = new WxGlProductQuoteQuery();
        quoteQuery.setProductId(productId);
        quoteQuery.setLimitItem(1000);
        quoteQuery.setStartDate(DateUtil.addMonth(new Date(), -6));
        List<WxGlProductQuoteListVO> quoteList = quotePlanMapper.listWxGlProductQuotePlans(quoteQuery);
        Map<String, Integer> ocupCountMap = new HashMap<>();
        final Set<String> finalSearchJobClass = searchJobClass;
        quoteList.forEach(ql -> {
            WxGlProductQuoteResultVO quoteResult = JSON.parseObject(ql.getQuoteDetail(), WxGlProductQuoteResultVO.class);
            if (quoteResult != null) {
                quoteResult.getQuotes().forEach(qr -> {
                    if (finalSearchJobClass.contains(qr.getOccupationGroup())) {
                        String occpCode = qr.getOccupationCode();
                        Integer count = ocupCountMap.get(occpCode);
                        if (count == null) {
                            count = 0;
                        }
                        count += qr.getPerQty();
                        ocupCountMap.put(occpCode, count);
                    }
                });
            }
        });
        /**
         * 此处需要扩大查询条数，历史数据的职业类别有可能会变更，可能不满足查询条件导致实际查询到的条数小于[size].
         * 所以此处先扩大范围，后面再做条数限制
         */
        List<String> hotOcupCodes = ocupCountMap.keySet().stream()
                .sorted(Comparator.comparing(ocupCountMap::get).reversed())
                .limit(size * 2)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(hotOcupCodes)) {
            List<WxTreeVO> tops = mapper.listOcup(product.getCompanyId(), hotOcupCodes, searchJobClass)
                    .stream().sorted(
                            (t1, t2) -> ocupCountMap.get(t2.getValue()).compareTo(ocupCountMap.get(t1.getValue()))
                    )
                    .limit(size)
                    .collect(Collectors.toList());
            return tops;
        }
        return Collections.emptyList();
    }

    /**
     * 查询该订单号被保人职业等级
     *
     * @param orderId
     * @return
     */
    public Set<String> getOccupationGroupListByOrderId(String orderId) {
        List<SmOrderSmsNotifyVO> smOrderSmsNotifyVOS = smOrderMapper.getOrderInsuredByOrderId(orderId);
        return smOrderSmsNotifyVOS.stream().map(x -> {
            return x.getOccupationGroup();
        }).collect(Collectors.toSet());
    }


    public SmOrderMinInfo getSmOrderMinInfo(String orderId) {
        SmOrderMinInfo smOrderMinInfo = smOrderMapper.querySmOrderMinInfo(orderId);
        return smOrderMinInfo;
    }

}
