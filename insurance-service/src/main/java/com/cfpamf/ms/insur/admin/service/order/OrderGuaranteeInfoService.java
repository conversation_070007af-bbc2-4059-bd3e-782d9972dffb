package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmProductHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.SmPlanRiskDutyHistoryMapper;
import com.cfpamf.ms.insur.admin.dao.safes.product.risk.SysRiskDutyMapper;
import com.cfpamf.ms.insur.admin.enums.EnumProductApiType;
import com.cfpamf.ms.insur.admin.enums.product.EnumProductCreateType;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderGuaranteeDutyInfoConvert;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.po.product.SmPlanRiskDutyHistory;
import com.cfpamf.ms.insur.admin.pojo.po.product.risk.SysRiskDuty;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.product.OrderGuaranteeDutyInfoVO;
import com.cfpamf.ms.insur.weixin.dao.safes.PolicyMapper;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OrderCoverageDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/1/13 15:33
 * @Version 1.0
 */
@Service
public class OrderGuaranteeInfoService {

    @Resource
    private SmOrderMapper orderMapper;

    @Resource
    private SmProductHistoryMapper productHistoryMapper;

    @Resource
    private SmOrderRiskDutyMapper orderRiskDutyMapper;

    @Resource
    private SmPlanHistoryMapper planHistoryMapper;

    @Resource
    private SmPlanRiskDutyHistoryMapper riskDutyHistoryMapper;

    @Resource
    private PolicyMapper policyMapper;

    @Resource
    private SysRiskDutyMapper sysRiskDutyMapper;

    public List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> initNonCarOrderInfo(SmOrderListVO orderVo) {
        return initNonCarOrderInfo(orderVo, null);
    }

    public List<OrderGuaranteeDutyInfoVO.OrderGuaranteeRiskInfo> initNonCarOrderInfo(SmOrderListVO orderVo, String idNumber) {
        Integer productId = orderVo.getProductId();
        String fhOrderId = orderVo.getFhOrderId();
        Integer version = orderMapper.queryOrderProductVersion(productId, orderVo.getSubmitTime());

        if (Objects.isNull(version)) {
            version = orderMapper.queryOrderProductVersion(productId, null);
            if (!Objects.isNull(version) && version > 1) {
                version = version - 1;
            }
        }
        if (Objects.isNull(version)) {
            return Collections.emptyList();
        }
        //查询产品信息
        SmProductDetailVO product = productHistoryMapper.getProductById(productId, version);
        List<OrderGuaranteeDutyInfoVO> guaranteeInfoVOList = null;
        //判断对接的方式是否为api
        if (Objects.equals(EnumProductApiType.API.getType(), product.getApiType())) {
            if (isNemTemplate(orderVo.getProductCreateType())) {
                guaranteeInfoVOList = getApiNewTemplateData(orderVo, idNumber, version);
            } else if (isGroupRiskTemplate(orderVo.getProductCreateType())) {
                guaranteeInfoVOList = getApiGroupRiskData(orderVo);
            } else if (isOldRiskTemplate(orderVo.getProductCreateType())) {
                //去历史表中查出当时配置的东西，根据计划反推保额
                guaranteeInfoVOList = planHistoryMapper.queryOrderGuaranteeInfo(
                        orderVo.getPlanId(),
                        version
                );
            } else {
                return null;
            }
        }

        if (Objects.equals(EnumProductApiType.H5.getType(), product.getApiType())) {
            guaranteeInfoVOList = getH5RiskData(fhOrderId);
        }

        if (CollectionUtils.isNotEmpty(guaranteeInfoVOList)) {
            return guaranteeInfoVOList.stream().peek(item -> item.setApiType(product.getApiType())).collect(
                            Collectors.groupingBy(
                                    OrderGuaranteeDutyInfoVO::toOrderGuaranteeRiskInfo
                            )
                    ).entrySet().stream()
                    .peek(item -> item.getKey().setGuaranteeInfoVOList(item.getValue()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    private List<OrderGuaranteeDutyInfoVO> getApiNewTemplateData(SmOrderListVO orderVo,
                                                                 String idNumber, Integer version) {
        SmOrderRiskDuty condition = new SmOrderRiskDuty();
        condition.setFhOrderId(orderVo.getFhOrderId());
        condition.setInsuredIdNumber(idNumber);
        List<SmOrderRiskDuty> riskDutyList = orderRiskDutyMapper.select(condition);


        if (CollectionUtils.isNotEmpty(riskDutyList)) {
            //是否计入险种保额
            SmPlanRiskDutyHistory riskDutyCondition = new SmPlanRiskDutyHistory();
            riskDutyCondition.setProductId(orderVo.getProductId());
            riskDutyCondition.setVersion(version);
            riskDutyCondition.setPlanId(orderVo.getPlanId());
            List<SmPlanRiskDutyHistory> riskDutyHistoryList = riskDutyHistoryMapper.selectEnabled(riskDutyCondition);

            if (CollectionUtils.isEmpty(riskDutyHistoryList)) {
                return null;
            }

            List<SysRiskDuty> sysRiskDutyList = sysRiskDutyMapper.selectByDutyKeyList(
                    riskDutyHistoryList.stream().map(SmPlanRiskDutyHistory::getDutyKey).collect(Collectors.toList())
            );

            if (CollectionUtils.isEmpty(riskDutyHistoryList)) {
                return null;
            }

            Map<DutyCombineKey, SmPlanRiskDutyHistory> dutyHistoryMap =
                    riskDutyHistoryList.stream().collect(
                            Collectors.toMap(DutyCombineKey::from, Function.identity())
                    );

            Map<DutyCombineKey, SysRiskDuty> dutyMap =
                    sysRiskDutyList.stream().collect(
                            Collectors.toMap(DutyCombineKey::from, Function.identity())
                    );

            Map<String, SmPlanRiskDutyHistory> dutyIdHistoryMap = new HashMap<>();

            for (Map.Entry<DutyCombineKey, SmPlanRiskDutyHistory> entry : dutyHistoryMap.entrySet()) {
                SysRiskDuty sysRiskDuty = dutyMap.get(entry.getKey());
                if (Objects.nonNull(sysRiskDuty)) {
                    dutyIdHistoryMap.put(String.valueOf(sysRiskDuty.getId()), entry.getValue());
                }
            }

            return riskDutyList.stream().map(item -> OrderGuaranteeInfoService.fromSmOrderRiskDuty(
                            item,
                            dutyIdHistoryMap
                    ))
                    .collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }

    private List<OrderGuaranteeDutyInfoVO> getApiGroupRiskData(SmOrderListVO orderVo) {

        OrderProductDTO orderProduct = policyMapper.queryOrderProduct(orderVo.getFhOrderId());
        if (orderProduct != null) {
            List<OrderCoverageDTO> coverages = orderProduct.parseCoverage();

            return coverages.stream().map(item -> {
                OrderGuaranteeDutyInfoVO guaranteeInfoVO = new OrderGuaranteeDutyInfoVO();
                guaranteeInfoVO.setRiskCode(item.getRiskCode());
                guaranteeInfoVO.setRiskName(item.getCvgItemName());
                guaranteeInfoVO.setRiskAmount(item.getCvgAmount());
                guaranteeInfoVO.setDutyAmount(item.getCvgAmount());
                guaranteeInfoVO.setDutyName(item.getCvgItemName());
                guaranteeInfoVO.setApiType(EnumProductApiType.API.getType());
                return guaranteeInfoVO;
            }).collect(Collectors.toList());

        }
        return Collections.emptyList();

    }

    private List<OrderGuaranteeDutyInfoVO> getH5RiskData(String fhOrderId) {

        SmOrderRiskDuty condition = new SmOrderRiskDuty();
        condition.setFhOrderId(fhOrderId);

        List<SmOrderRiskDuty> riskDutyList = orderRiskDutyMapper.select(condition);

        return riskDutyList.stream().map(OrderGuaranteeInfoService::convertToOrderGuaranteeDutyInfoVO)
                .collect(Collectors.toList());
    }

    public static OrderGuaranteeDutyInfoVO convertToOrderGuaranteeDutyInfoVO(SmOrderRiskDuty item) {
        if (item == null) {
            return null;
        }
        OrderGuaranteeDutyInfoVO result = OrderGuaranteeDutyInfoConvert.INS.cvtDuty(item);
        result.setRiskName(item.getRiskName());
        result.setRiskCode(item.getRiskCode());
        result.setRiskId(item.getRiskId());
        result.setRiskAmount(item.getRiskAmount());
        result.setDutyName(item.getDutyName());
        result.setDutyId(item.getDutyId());
        result.setDutyCode(item.getDutyCode());
        result.setDutyAmount(item.getAmount());
        result.setDutyPremium(item.getPremium());
        result.setRiskPremium(item.getRiskPremium());
        result.setApiType(EnumProductApiType.H5.getType());
        return result;
    }

    public static OrderGuaranteeDutyInfoVO fromSmOrderRiskDuty(SmOrderRiskDuty riskDuty,
                                                               Map<String, SmPlanRiskDutyHistory> dutyHistoryMap) {

        if (riskDuty == null) {
            return null;
        }

        OrderGuaranteeDutyInfoVO result = new OrderGuaranteeDutyInfoVO();
        result.setRiskName(riskDuty.getRiskName());
        result.setRiskId(riskDuty.getRiskId());
        result.setDutyName(riskDuty.getDutyName());
        result.setDutyId(riskDuty.getDutyId());
        result.setDutyAmount(riskDuty.getAmount());
        result.setDutyPremium(riskDuty.getPremium());
        result.setIncludedTotalInsuredAmount(
                Optional.ofNullable(dutyHistoryMap.get(riskDuty.getDutyId()))
                        .map(SmPlanRiskDutyHistory::getIncludedRiskInsuredAmount).orElse(0)
        );
        result.setProductCreateType(EnumProductCreateType.PERSON_LONG_INSURANCE.name());
        result.setApiType(EnumProductApiType.API.getType());
        return result;
    }

    /**
     * 新模板创建的
     *
     * @param productCreateType
     * @return
     */
    public static boolean isNemTemplate(String productCreateType) {
        return EnumProductCreateType.PERSON_LONG_INSURANCE.name().equals(productCreateType);
    }

    /**
     * 团险模版创建的
     *
     * @param productCreateType
     * @return
     */
    public static boolean isGroupRiskTemplate(String productCreateType) {
        return EnumProductCreateType.GROUP_INSURANCE.name().equals(productCreateType);
    }

    public static boolean isOldRiskTemplate(String productCreateType) {
        return EnumProductCreateType.PERSON_SHORT_INSURANCE.name().equals(productCreateType)
                || EnumProductCreateType.DEFAULT_TYPE.name().equals(productCreateType);
    }

    @Data
    @AllArgsConstructor
    static class DutyCombineKey {
        String key;
        Integer version;

        public static DutyCombineKey from(SysRiskDuty sysRiskDuty) {
            return new DutyCombineKey(sysRiskDuty.getDutyKey(), sysRiskDuty.getVersion());
        }

        public static DutyCombineKey from(SmPlanRiskDutyHistory dutyHistory) {
            return new DutyCombineKey(dutyHistory.getDutyKey(), dutyHistory.getDutyVersion());
        }

    }

}
