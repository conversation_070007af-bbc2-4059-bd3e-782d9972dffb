package com.cfpamf.ms.insur.admin.service;

import com.alibaba.druid.util.StringUtils;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.*;
import com.cfpamf.ms.insur.admin.enums.hr.EnumHandoverScene;
import com.cfpamf.ms.insur.admin.external.whale.api.WhaleApiService;
import com.cfpamf.ms.insur.admin.external.whale.model.CustomerInfoVO;
import com.cfpamf.ms.insur.admin.external.whale.model.HandoverVO;
import com.cfpamf.ms.insur.admin.pojo.dto.*;
import com.cfpamf.ms.insur.admin.pojo.po.TmpCustomerAdminChangeImportRecord;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.query.CustomerQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.util.TxUtil;
import com.cfpamf.ms.insur.base.config.BmsConfig;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.AbstractLockService;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.base.util.excel.ExcelReadUtils;
import com.cfpamf.ms.insur.common.datasource.annotation.DataSourceReadOnly;
import com.cfpamf.ms.insur.facade.vo.CustomerPolicyOMSVO;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.dao.safes.WxOrderMapper;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxCustBaseVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxUserVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.commons.util.IdUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 客户管理service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class CustomerService extends AbstractLockService {
    /**
     * bms配置
     */
    @Autowired
    private BmsConfig bmsConfig;

    /**
     * 客户mapper
     */
    @Autowired
    private CustomerMapper mapper;

    /**
     * 订单mapper
     */
    @Autowired
    private WxOrderMapper wxOrderMapper;

    /**
     * 订单mapper
     */
    @Autowired
    private SmOrderMapper smOrderMapper;

    /**
     * 用户mapper
     */
    @Autowired
    private AuthUserMapper userMapper;

    @Autowired
    private UserPostMapper userPostMapper;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    WhaleApiService apiService;
    @Autowired
    private TmpCustomerAdminChangeImportRecordMapper tmpCustomerAdminChangeImportRecordMapper;


    public void getCustomerInfoPreCheck(CustomerQuery query){
        if (query.getCreateDateStart() != null) {
            query.setCreateDateStart(DateUtil.getBeginOfDay(query.getCreateDateStart()));
        }
        if (query.getCreateDateEnd() != null) {
            query.setCreateDateEnd(DateUtil.getEndOfDay(query.getCreateDateEnd()));
        }
        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);
//        //  区域角色 没权限查看
//        if (permissionUtil.isRegionRole(ThreadUserUtil.USER_DETAIL_TL.get())) {
//            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
//        }
        permissionUtil.buildDataPermissionPageQuery(query);
        if (StringUtils.isEmpty(query.getOrganizationName())) {
            query.setOrganizationName(query.getOrgName());
        }
        if (!permissionUtil.isHeadRole(ThreadUserUtil.USER_DETAIL_TL.get()) && Objects.equals(query.getCustomerAdmin(), "blank")) {
            throw new BizException("", "当前非总部角色，且查询条件中负责人信息为blank");
        }
        if (!StringUtils.isEmpty(query.getOrganizationName())
                || !StringUtils.isEmpty(query.getUserId())
                || !StringUtils.isEmpty(query.getRegionName())
                || (!StringUtils.isEmpty(query.getCustomerAdmin()) && !Objects.equals(query.getCustomerAdmin(), "blank"))) {
            List<Integer> customerIds = mapper.listCustomerIdsByRecommendInfo(query);
            if (customerIds.isEmpty()) {
                throw new BizException("", "客户ID信息为空");
            }
            query.setCustomerIds(customerIds);
        }
        if (query.getAgentId() != null) {
            List<Integer> customerIds = mapper.listCustomerIdsByAgentInfo(query);
            if (customerIds.isEmpty()) {
                throw new BizException("", "客户ID信息为空");
            }
            query.setCustomerIds(customerIds);
        }
    }

    /**
     * 查询客户列表
     * 总部人员		全部的客户数据
     * 机构人员	机构负责人（主任、副主任、管理顾问、主任助理）	机构的客户数据
     * 机构督导、分支内务	机构的客户数据
     * 客户经理	自己的数据
     * 区域角色 没权限查看
     *
     * @param query
     * @return
     */
    @DataSourceReadOnly
    public PageInfo<CustomerListVO> getCustomerListByPage(CustomerQuery query) {
        try {
            // 参数预校验
            getCustomerInfoPreCheck(query);
        } catch (Exception e) {
            log.warn("客户信息查询参数校验出错：", e);
            return new PageInfo<>();
        }

        List<CustomerListVO> customerListVOS;

        // 如果是导出操作 则需要统计总数 并判断是否超过限定行数
        if(query.isDownload()){
            long total =countCustomers(query);
            if (total > BaseConstants.EXCEL_ROW_LIMIT) {
                throw new BizException(ExcptEnum.EXCEL_MAX_LIMIT_ERROR);
            }
            // 查询和处理结果
            customerListVOS = queryAndHandleCustomerList(query);
            return new PageInfo<>(customerListVOS);
        }else{

            PageInfo<CustomerListVO> pageInfo = new PageInfo<>();
            if (query.getPage() != null) {
                pageInfo.setPageNum(query.getPage());
                pageInfo.setSize(query.getSize());
                // 如果禁用数量统计 则不统计总数
                if(query.getForbidCountSwitch()){
                    pageInfo.setTotal(0);
                    pageInfo.setHasNextPage(true);
                }else{
                    long total = countCustomers(query);
                    pageInfo.setTotal(total);
                    pageInfo.setHasNextPage(query.getPage() * query.getSize() < pageInfo.getTotal());
                }
            }

            // 查询和处理结果
            customerListVOS = queryAndHandleCustomerList(query);
            pageInfo.setList(customerListVOS);

            return pageInfo;
        }

    }
    private long countCustomers(CustomerQuery query) {
        if (query.isSelection()) {
            return mapper.countCustomerSelection(query);
        } else {
            return  mapper.countCustomers(query);
        }
    }

    @DataSourceReadOnly
    public Long getCustomerCntByPage(CustomerQuery query) {
        try {
            // 参数预校验
            getCustomerInfoPreCheck(query);
        } catch (Exception e) {
            log.warn("客户信息查询参数校验出错：", e);
            return 0L;
        }

        return mapper.countCustomers(query);
    }

        /**
         * 处理查询列表请求，并返回列表结果
         * @param query 查询参数
         * @return
         */
    private List<CustomerListVO> queryAndHandleCustomerList(CustomerQuery query) {
        List<CustomerListVO> customerList = new ArrayList<>();
        if (query.isSelection()) {
            customerList = mapper.listCustomerSelection(query);
        } else {
            customerList = mapper.listCustomer(query);
        }
        if (!customerList.isEmpty()) {
            query.setCustomerIds(customerList.stream().map(CustomerListVO::getCustomerId).collect(Collectors.toList()));
            Map<Integer, CustomerListVO> customerAdminMap = mapper.listCustomerAdminInfo(query)
                    .stream()
                    .distinct()
                    .collect(Collectors.toMap(CustomerListVO::getCustomerId, Function.identity()));
            Map<Integer, CustomerListVO> customerAgentMap = mapper.listCustomerAgentInfo(query)
                    .stream()
                    .distinct()
                    .collect(Collectors.toMap(CustomerListVO::getCustomerId, Function.identity()));

            Map<String, AuthUserVO> authUserMap = getAuthUserMap(customerList);

            customerList.forEach(c -> {
                CustomerListVO ca = customerAdminMap.get(c.getCustomerId());
                if (ca != null) {
                    c.setCustomerAdminId(ca.getCustomerAdminId());
                    c.setCustomerAdminName(ca.getCustomerAdminName());
                    c.setCustomerAdminOrgName(ca.getCustomerAdminOrgName());
                    c.setCustomerAdminRegionName(ca.getCustomerAdminRegionName());
                    c.setCustomerAdminStatus(ca.getCustomerAdminStatus());
                }
                CustomerListVO cg = customerAgentMap.get(c.getCustomerId());
                if (cg != null) {
                    c.setCustomerAgentName(cg.getCustomerAgentName());
                    c.setCustomerAgentMobile(cg.getCustomerAgentMobile());
                }

                AuthUserVO authUserVO = authUserMap.get(c.getCurrentCustomerAdminId());
                if (authUserVO != null) {
                    c.setCurrentCustomerAdminName(authUserVO.getUserName());
                }
                AuthUserVO lastAuthUserVO = authUserMap.get(c.getLastCustomerAdminId());
                if (lastAuthUserVO != null) {
                    c.setLastCustomerAdminName(lastAuthUserVO.getUserName());
                }
            });

        }
//        if (!query.isNotMask()) {
//            permissionUtil.maskCustomerSensitiveFields(customerList, bmsConfig.getCustomerSensitiveCustomerList());
//        }
        DataMaskUtil.maskList(customerList);
        customerList.forEach(customerListVO -> {
            customerListVO.setEmail(null);
            customerListVO.setIdNumber(null);
            customerListVO.setCustomerAgentName(null);
        });
        return customerList;
    }

    private Map<String, AuthUserVO> getAuthUserMap(List<CustomerListVO> customerList) {
        List<String> customerAdminIds = new ArrayList<>();
        List<String> adminIds = customerList.stream().map(CustomerListVO::getCurrentCustomerAdminId).filter(org.apache.commons.lang3.StringUtils::isNoneBlank).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(adminIds)){
            customerAdminIds.addAll(adminIds);
        }
        adminIds = customerList.stream().map(CustomerListVO::getLastCustomerAdminId).filter(org.apache.commons.lang3.StringUtils::isNoneBlank).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(adminIds)){
            customerAdminIds.addAll(adminIds);
        }

        if(!CollectionUtils.isEmpty(customerAdminIds)){
            List<AuthUserVO> authUserVOS = userMapper.listUsersByJobNumbers(customerAdminIds);
            return  authUserVOS.stream().collect(Collectors.toMap(AuthUserVO::getUserId, Function.identity()));
        }else{
            return Collections.EMPTY_MAP;
        }
    }



    /**
     * 查询客户负责人员工变更记录
     * 总部人员		公司的员工变更记录
     * 机构人员	机构负责人（主任、副主任、管理顾问、主任助理）	该机构的员工变更记录
     * 机构督导、分支内务	该机构的员工变更记录
     * 其他人员	无
     *
     * @param query
     * @return
     */
    @DataSourceReadOnly
    public PageInfo<CustomerAdminLogVO> getCustomerAdminLogListByPage(CustomerQuery query) {
        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);
        getCustomerAdminLogPreCheck(permissionUtil,query);
        List<CustomerAdminLogVO> customerAdminLogList = mapper.listCustomerAdminLog(query);
        if (!query.isNotMask()) {
            permissionUtil.maskCustomerSensitiveFields(customerAdminLogList, bmsConfig.getCustomerSensitiveCustomerList());
        }
        PageInfo<CustomerAdminLogVO> pageInfo = new PageInfo<>(customerAdminLogList);
        if (query.getPage() != null) {
            pageInfo.setPageNum(query.getPage());
            pageInfo.setSize(query.getSize());
            if(query.getForbidCountSwitch()){
                pageInfo.setTotal(0);
                pageInfo.setHasNextPage(true);
            }else{
                long total = mapper.listCustomerAdminLogCount(query);
                pageInfo.setTotal(total);
                pageInfo.setHasNextPage(query.getPage() * query.getSize() < pageInfo.getTotal());
            }
        }
        return pageInfo;
    }

    private void getCustomerAdminLogPreCheck(PermissionUtil permissionUtil, CustomerQuery query) {
        permissionUtil.buildDataPermissionPageQuery(query);

        if (query.getCreateDateStart() != null) {
            query.setCreateDateStart(DateUtil.getBeginOfDay(query.getCreateDateStart()));
        }
        if (query.getCreateDateEnd() != null) {
            query.setCreateDateEnd(DateUtil.getEndOfDay(query.getCreateDateEnd()));
        }
        //用户有模块操作权限就可以直接操作
        //  区域角色 没权限查看
//        if (permissionUtil.isRegionRole(ThreadUserUtil.USER_DETAIL_TL.get())) {
//            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
//        }
        // 客户经理没权限查看

//        if (permissionUtil.isCustomerManager(ThreadUserUtil.USER_DETAIL_TL.get())) {
//            throw new BizException(ExcptEnum.NO_DATA_PERMISSION_801010);
//        }
        if (StringUtils.isEmpty(query.getOrganizationName())) {
            query.setOrganizationName(query.getOrgName());
        }
    }

    @DataSourceReadOnly
    public Long getCustomerAdminLogCntByPage(CustomerQuery query) {
        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);
        getCustomerAdminLogPreCheck(permissionUtil, query);
        return null != query.getPage() ? mapper.listCustomerAdminLogCount(query) : 0L;
    }


        /**
         * 查询客户详情
         *
         * @param customerId
         * @return
         */
    public CustomerDetailVO getCustomerDetailById(int customerId) {
        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);

        CustomerDetailVO detail = new CustomerDetailVO();
        detail.setBaseInfo(mapper.getCustomerById(String.valueOf(customerId)));

        WxCustBaseVO baseInfo = detail.getBaseInfo();
        baseInfo.setArea(baseInfo.getAreaName());
        String idNumber = detail.getBaseInfo().getIdNumber();
        permissionUtil.maskCustomerSensitiveFields(detail.getBaseInfo(), bmsConfig.getCustomerSensitiveCustomerDetail());

        detail.setApptPolicys(wxOrderMapper.listWxCustomerPolicyList(idNumber, 0));
        permissionUtil.maskCustomerSensitiveFields(detail.getApptPolicys(), bmsConfig.getCustomerSensitiveCustomerDetail());

        detail.setInsuredPolicys(wxOrderMapper.listWxCustomerPolicyList(idNumber, 1));
        permissionUtil.maskCustomerSensitiveFields(detail.getInsuredPolicys(), bmsConfig.getCustomerSensitiveCustomerDetail());

        detail.setRelationships(mapper.listWxCustomerShipById(customerId));
        permissionUtil.maskCustomerSensitiveFields(detail.getRelationships(), bmsConfig.getCustomerSensitiveCustomerDetail());

        List<CustPropertyVO> propertys = mapper.listWxCustomerPropertyById(String.valueOf(customerId));
        detail.setCars(propertys.stream().filter(p -> !StringUtils.isEmpty(p.getCarPlateNo())).collect(Collectors.toList()));
        detail.setHouses(propertys.stream().filter(p -> !StringUtils.isEmpty(p.getHourseNo()) || !StringUtils.isEmpty(p.getPropertyAddress())).collect(Collectors.toList()));
        detail.setApptPolicyQty(detail.getApptPolicys().size());
        detail.setApptPolicyAmount(detail.getApptPolicys().stream().map(CustomerPolicyVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        detail.setInstPolicyQty(detail.getInsuredPolicys().size());
        detail.setInstPolicyAmount(detail.getInsuredPolicys().stream().map(CustomerPolicyVO::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

        return detail;
    }

    /**
     * 查询客户列表下载
     *
     * @param query
     * @param resp
     */
    public void downloadCustomerList(CustomerQuery query, HttpServletResponse resp) {
        String fileName = "客户列表明细";
        try (OutputStream os = resp.getOutputStream()) {

            query.setAll(true);
            query.setDownload(true);
            List<CustomerListVO> customerList = null;
            try {
                customerList = getCustomerListByPage(query).getList();
                resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx");
                resp.setContentType("application/octet-stream");
            } catch (Exception e) {
                log.warn("订单下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                resp.setContentType("text/html;charset=UTF-8");
                String msg = e.getMessage() != null ? e.getMessage() : "下载失败,请联系管理员";
                os.write(msg.getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }
            customerList.forEach(u -> {
                String statusJoin = u.getCustomerAdminStatus();
                if (!StringUtils.isEmpty(statusJoin)) {
                    u.setCustomerAdminStatus(String.join(",", Stream.of(statusJoin.split(","))
                            .map(s -> {
                                if (Objects.equals(u.getCustomerAdminStatus(), BaseConstants.USER_STATUS_2)) {
                                    return "试用";
                                } else if (Objects.equals(u.getCustomerAdminStatus(), BaseConstants.USER_STATUS_3)) {
                                    return "正式";
                                } else if (Objects.equals(u.getCustomerAdminStatus(), BaseConstants.USER_STATUS_8)) {
                                    return "离职";
                                }
                                return "";
                            }).collect(Collectors.toList())));
                }
            });
            ExcelBuilderUtil.newInstance()
                    .createSheet("客户列表")
                    .buildSheetHead(CustomerListVO.class)
                    .addSheetData(customerList)
                    .write(os);
        } catch (Exception e) {
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    /**
     * 变更客户负责人
     *
     * @param dto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void changeCustomerAdmin(CustomerAdminChangeDTO dto) {
        validPermissionUtil(dto);
        List<CustomerListVO> customers;
        if (dto.getAllEmployeeFlag() != null && dto.getAllEmployeeFlag()) {
            log.info("开始变更客户负责人的所有记录");
            if (Objects.equals(dto.getOldAdminId(), "blank")) {
                customers = mapper.listCustomersLackAdmin();
            } else {
                customers = mapper.listCustomerByCustomerAdmin(dto.getOldAdminId());
            }
        } else {
            if (dto.getCustomerIds() == null || dto.getCustomerIds().isEmpty()) {
                return;
            }
            customers = mapper.listCustomerByIdList(dto.getCustomerIds());
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(customers)) {
            return;
        }

        //变更记录 2020-04-20 lsc
        JwtUserInfo user = HttpRequestUtil.getUserOrThrowExp();
        //插入变更记录
        List<CustomerRemdLogDTO> logList = new ArrayList<>(customers.size());
        customers.forEach(c -> {
            CustomerRemdLogDTO remdLogDTO = new CustomerRemdLogDTO();
            remdLogDTO.setCustomerId(c.getCustomerId());

            remdLogDTO.setCustomerNewAdminId(dto.getNewAdminId());
            //add by zhangjian 2020-07-07 统一工号
            remdLogDTO.setCustomerNewAdminJobCode(dto.getNewAdminJobCode());
            if (!Objects.equals(dto.getOldAdminId(), "blank")) {
                remdLogDTO.setCustomerOldAdminId(dto.getOldAdminId());
            }
            remdLogDTO.setCreateBy(user.getJobNumber());
            logList.add(remdLogDTO);
        });
        if(!CollectionUtils.isEmpty(logList)) {
            log.info("开始插入客户负责人变更记录");
            int i = mapper.insertCustomerRemdLogsV2(logList);
            log.info("客户负责人变更记录-成功插入{}行变更日志", i);
        }

        String oldAdminId = dto.getOldAdminId();
        if (!Objects.equals(oldAdminId, "blank")) {
            log.info("开始更新客户管护人V1:{}",oldAdminId);
            int r = mapper.updateCustomerAdminV1(dto);
            log.info("更新客户管护人完成V1:{}",r);
        } else {
            log.info("开始更新客户管护人:{}",oldAdminId);
            customers.forEach(c -> {
                CustomerRemdDTO remdDTO = new CustomerRemdDTO();
                remdDTO.setCustomerAdminId(dto.getNewAdminId());
                //add by zhangjian 2020-07-08 统一工号
                remdDTO.setCustomerAdminJobCode(dto.getNewAdminJobCode());
                remdDTO.setCustomerId(c.getCustomerId());
                mapper.insertCustomerRemd(remdDTO);
            });
            log.info("更新客户管护人完成");
        }
        //modify by zhangjian 2020-07-08 统一工号
        UserPost userPost = userPostMapper.selectUserPostByJobCode(dto.getNewAdminJobCode());
        List<String> idNumbers = null;
        if (dto.getAllEmployeeFlag() != null && !dto.getAllEmployeeFlag()) {
            idNumbers = customers.stream()
                    .map(CustomerListVO::getIdNumber)
                    .collect(Collectors.toList());
        }
        List<Integer> customerIds = customers.stream()
                .map(CustomerListVO::getCustomerId)
                .collect(Collectors.toList());
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(customerIds)){
            return;
        }
        log.info("开始更新订单相关信息的管护人V3:{},客户信息:{}",oldAdminId,idNumbers);
        int r1 = smOrderMapper.updateOrderCustomerAdminV3(dto.getOldAdminId(), dto.getNewAdminId(), dto.getNewAdminJobCode(), userPost.getMainJobNumber()
                , userPost.getOrgCode(), idNumbers);
        log.info("更新客户管护人完成V3:{}",r1);
        //变更断保，续保管护人
        log.info("开始更新变更断保，续保管护人:变更前客户经理{},变更后客户经理:{},客户信息:{}",dto.getOldAdminId(),dto.getNewAdminId(),idNumbers);
        smOrderMapper.updateHandOrderInterruption(dto.getOldAdminId(), dto.getNewAdminId(), idNumbers);
        log.info("短保&续保管护人变更完成");
        //通知小鲸处理变更
        List<CustomerInfoVO> customerListVOList = mapper.selectCustomerList(customerIds);
        String handoverType = "1";
        if(dto.getAllEmployeeFlag() != null && dto.getAllEmployeeFlag()){
            handoverType = "0";
        }
        HandoverVO handoverVO = createHandoverVo(EnumHandoverScene.CUSTOMER_ADMIN_CHANGE_EVENT,handoverType, dto.getOldAdminId(), dto.getNewAdminId(), customerListVOList);
        handoverVO.setApplyUserId(user.getJobNumber());
        apiService.disposeHandover(handoverVO);
        // 变更客户经理通知变更客户
        WxUserVO oldAdmin = null;
        if (!Objects.equals(dto.getOldAdminId(), "blank")) {
            oldAdmin = userMapper.getUserByUserId(dto.getOldAdminId());
        }
        WxUserVO userVO = SpringFactoryUtil.getBean(AuthUserMapper.class).getUserByUserId(dto.getNewAdminId());
        SpringFactoryUtil.getBean(SmsSenderUtil.class).sendCacMessage(customers.stream()
                        .map(CustomerListVO::getCellPhone)
                        .distinct()
                        .collect(Collectors.toList()),
                SmsSenderUtil.SmsCaCDTO.builder()
                        .originalAdminName(oldAdmin == null ? "无" : oldAdmin.getUserName())
                        .targetAdminName(userVO.getUserName())
                        .targetAdminMobile(userVO.getUserMobile())
                        .build()
        );
    }

    public HandoverVO createHandoverVo(EnumHandoverScene bizScene,String handoverType,String oldAdminId,String newAdminId,List<CustomerInfoVO> memberList){
        HandoverVO handoverVO = new HandoverVO();
        handoverVO.setRequestId(IdGenerator.getUuid());
        handoverVO.setBizScene(bizScene.getCode());
        handoverVO.setHandoverType(handoverType);
        handoverVO.setSourceReferrerWno(oldAdminId);
        handoverVO.setTargetReferrerWno(newAdminId);
        handoverVO.setList(memberList);
        return handoverVO;
    }

    /**
     * 更新客户在客户中心信息
     *
     * @param id
     * @param custNo
     * @return
     */
    public void updateCustomerRegisterInfo(int id, String custNo) {
        mapper.updateCustomerRegisterInfo(id, custNo);
    }

    public void validPermissionUtil(CustomerAdminChangeDTO dto) {
        //对于选无的做数据权限控制，只有总部人员才能操作
        PermissionUtil permissionUtil = SpringFactoryUtil.getBean(PermissionUtil.class);
        if (!permissionUtil.isHeadRole(ThreadUserUtil.USER_DETAIL_TL.get()) && Objects.equals(dto.getOldAdminId(), "blank")) {
            throw new BizException("", "请选择变更员工");
        }

        if (!Objects.equals(dto.getOldAdminId(), "blank")) {
            WxUserVO oldUser = userMapper.getUserByUserId(dto.getOldAdminId());
            if (oldUser == null) {
                throw new BizException(ExcptEnum.USER_NOT_EXIST.getCode(), "更换前" + ExcptEnum.USER_NOT_EXIST.getMsg());
            }
        }
        WxUserVO newAdmin = userMapper.getUserByUserId(dto.getNewAdminId());
        if (newAdmin == null) {
            throw new BizException(ExcptEnum.USER_NOT_EXIST.getCode(), "更换后" + ExcptEnum.USER_NOT_EXIST.getMsg());
        }
        if (Objects.equals(newAdmin.getStatus(), SmConstants.EMPLOYEE_STATUS_LEASE)) {
            throw new BizException(ExcptEnum.USER_NOT_EXIST.getCode(), "更换后客户经理已经离职");
        }
        //目前存在兼职用户工号是统一，只是岗位不一样，需要支持用户从离职岗位转换到在职岗位，所以屏蔽这个地方
        /*if (Objects.equals(dto.getNewAdminId(), dto.getOldAdminId())) {
            throw new BizException(ExcptEnum.CUSTOMER_ADMIN_SAME);
        }*/
    }

    /**
     * 查询客户保单信息
     *
     * @param idNumbers
     * @return
     */
    public List<com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO> getCustomerPolicyList(List<String> idNumbers) {
        if (idNumbers == null || idNumbers.isEmpty()) {
            return Collections.emptyList();
        }
        List<com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO.PolicyInfo> policys = mapper.listCustomerPolicys(idNumbers);
        List<com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO> customerPolicys = new ArrayList<>();

        idNumbers.forEach(in -> {
            com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO cp = new com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO();
            cp.setIdNumber(in);
            cp.setPolicyInfos(policys.stream()
                    .filter(p -> Objects.equals(p.getInsIdNumber(), in) || Objects.equals(p.getAppIdNumber(), in))
                    .collect(Collectors.toList())
            );
            customerPolicys.add(cp);
        });

        customerPolicys.forEach(cp -> {
            List<com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO.PolicyInfo> apList = cp.getPolicyInfos()
                    .stream()
                    .filter(p -> Objects.equals(p.getAppIdNumber(), cp.getIdNumber()))
                    .collect(Collectors.toList());
            cp.setAppQty(apList.size());
            cp.setAppAmount(apList.stream().map(com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO.PolicyInfo::getPolicyAmount)
                    .reduce(new BigDecimal(0), BigDecimal::add));

            List<com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO.PolicyInfo> isList = cp.getPolicyInfos()
                    .stream()
                    .filter(p -> Objects.equals(p.getInsIdNumber(), cp.getIdNumber()))
                    .collect(Collectors.toList());
            cp.setInsQty(isList.size());
            cp.setInsAmount(isList.stream().map(com.cfpamf.ms.insur.facade.vo.CustomerPolicyVO.PolicyInfo::getPolicyAmount)
                    .reduce(new BigDecimal(0), BigDecimal::add));
        });
        return customerPolicys;
    }

    /**
     * 入参：身份证
     * 回参：推荐人ID、推荐人姓名、、投保人CUSTNUMBER、投保人姓名、被保人CUSTNUMBER、被保人姓名、投保金额、保单状态、退保时间
     * 给OMS提供的查询保单接口
     *
     * @return
     */
    public List<CustomerPolicyOMSVO> listPolicy4OMS(List<String> idNumbers) {

        List<CustomerPolicyOMSVO> apps = mapper.listPolicy4OMS(idNumbers, false);
        List<CustomerPolicyOMSVO> insureds = mapper.listPolicy4OMS(idNumbers, true);
        List<CustomerPolicyOMSVO> res = Lists.newArrayList(apps);
        res.addAll(insureds);
        return res.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 系统自动
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoChangeCustomerAdmin(CustomerAdminChangeDTO dto) {
        if (org.apache.commons.lang.StringUtils.isBlank(dto.getOldAdminId())) {
            return;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(dto.getNewAdminId())) {
            return;
        }
        log.info("开始变更{}==>{}", dto.getOldAdminId(), dto.getNewAdminId());
        CustomerQuery query = new CustomerQuery();
        query.setCustomerAdmin(dto.getOldAdminId());

        query.setNotMask(true);
        query.setAll(true);
        List<Integer> customerIds = mapper.listCustomerIdsByRecommendInfo(query);
        if (CollectionUtils.isEmpty(customerIds)) {
            return;
        }

        query.setCustomerIds(customerIds);
        List<CustomerListVO> customers = mapper.listCustomer(query);
        if (CollectionUtils.isEmpty(customers)) {
            log.info("客户信息丢失{}", customerIds);
            return;
        }

        //插入变更记录
        int i = mapper.insertCustomerByOld2New(dto.getNewAdminId(), dto.getNewAdminJobCode(), dto.getOldAdminId());

        log.info("成功插入{}行变更日志", i);
        //开始变更预处理数据
        int i1 = mapper.updateCustomerAdminV1(dto);
        log.info("成功变更记录{}行客户", i1);

        UserPost userPost = dto.getNewAdminUserPost();
        //kaishi
        smOrderMapper.updateOrderCustomerAdminV3(dto.getOldAdminId(), dto.getNewAdminId(), dto.getNewAdminJobCode(), userPost.getMainJobNumber(), userPost.getOrgCode(),
                null);
        //变更断保，待办管护人
        smOrderMapper.updateOrderInterruption(dto.getOldAdminId(), dto.getNewAdminId());
        //通知小鲸处理变更
        List<CustomerInfoVO> customerListVOList = mapper.selectCustomerList(customerIds);

        HandoverVO handoverVO = createHandoverVo(EnumHandoverScene.HR_SYS_EVENT, "0", dto.getOldAdminId(), dto.getNewAdminId(), customerListVOList);
        handoverVO.setApplyTime(dto.getApplyTime());
        apiService.disposeHandover(handoverVO);
        // 变更客户经理通知变更客户
        WxUserVO oldAdmin = userMapper.getUserByUserId(dto.getOldAdminId());
        ;
        WxUserVO userVO = SpringFactoryUtil.getBean(AuthUserMapper.class).getUserByUserId(dto.getNewAdminId());
        SpringFactoryUtil.getBean(SmsSenderUtil.class).sendCacMessage(customers.stream()
                        .map(CustomerListVO::getCellPhone)
                        .distinct()
                        .collect(Collectors.toList()),
                SmsSenderUtil.SmsCaCDTO.builder()
                        .originalAdminName(oldAdmin == null ? "无" : oldAdmin.getUserName())
                        .targetAdminName(userVO.getUserName())
                        .targetAdminMobile(userVO.getUserMobile())
                        .build()
        );
    }

    @Autowired
    TransactionTemplate txTemplate;

    public void importCustomerAdminChangeRecord(CustomerAdminChangeImportDTO dto, String operator) throws Exception {
        String key = ":importCustomerAdminChangeRecord:" + operator;
        super.lock(key, 30, "上一次导入未处理完，请稍后重试！");

        SmOrderImportConfirmDTO<ConfirmData> importDTO = new SmOrderImportConfirmDTO<>();

        try {
            List<CustomerAdminChangeExcelDTO> dtos = ExcelReadUtils
                    .readWorkbookByStream(
                            DownloadUtil.downloadByUrl(dto.getFileUrl()), CustomerAdminChangeExcelDTO.class
                            , 0, false);
            /*//错误信息要还原 把导入 列表复制一份
            List<CustomerAdminChangeExcelDTO> excelClones = objectMapper.convertValue(dtos, new TypeReference<List<CustomerAdminChangeExcelDTO>>() {
            });*/
            if (!CollectionUtils.isEmpty(dtos)) {
                TxUtil.getInstance(txTemplate).doTransactionWithoutResult(
                        () -> {
                            importChangeRecord(dtos);
                        }
                );
            }
        } finally {
            unlock(key);
        }
    }

    public void importChangeRecord(List<CustomerAdminChangeExcelDTO> dtos) {
        List<String> userIds = new ArrayList<>();
        userIds.addAll(dtos.stream().map(CustomerAdminChangeExcelDTO::getBeforeCustomerAdminId).collect(Collectors.toList()));
        userIds.addAll(dtos.stream().map(CustomerAdminChangeExcelDTO::getAfterCustomerAdminId).collect(Collectors.toList()));
        List<AuthUserVO> authUserVOList = userMapper.listUsersByJobNumbers(userIds);
        List<TmpCustomerAdminChangeImportRecord> list = Lists.newArrayList(dtos.size());
        int max = 1;
        dtos.forEach(dto -> {
            TmpCustomerAdminChangeImportRecord record = new TmpCustomerAdminChangeImportRecord();
            record.setBatchNo(max);
            record.setPostName(dto.getPostName());
            Optional<AuthUserVO> oldOpt = authUserVOList.stream().filter(u -> Objects.equals(dto.getBeforeCustomerAdminId(), u.getUserId())).findFirst();
            if (oldOpt.isPresent()) {
                AuthUserVO old = oldOpt.get();

                if (!Objects.equals(old.getOrganizationName(), dto.getOrgName())) {
                    UserPost userPost = userPostMapper.selectUserPostByJobCode(old.getJobCode());
                    if (Objects.equals(userPost.getOrgName(), dto.getOrgName())) {
                        record.setRegionName(old.getRegionName());
                        record.setRegionCode(old.getRegionCode());
                        record.setOrgName(old.getOrganizationName());
                        record.setOrgCode(old.getOrgCode());
                    } else {
                        record.setStatus(9);
                        record.setRemark("导入的原管护负责人部门信息与数据库中不匹配");
                        record.setOrgName(dto.getOrgName());
                        record.setRegionName(dto.getRegionName());
                    }

                } else {
                    record.setRegionName(old.getRegionName());
                    record.setRegionCode(old.getRegionCode());
                    record.setOrgName(old.getOrganizationName());
                    record.setOrgCode(old.getOrgCode());
                }
                if (Objects.equals(old.getUserName(), dto.getBeforeCustomerAdminName())) {
                    record.setOldCustomerAdminName(old.getUserName());
                    record.setOldCustomerAdminId(old.getUserId());
                    record.setOldCustomerAdminJobCode(old.getJobCode());
                    record.setOldCustomerAdminMainJobNumber(old.getMainJobNumber());
                } else {
                    record.setStatus(9);
                    record.setRemark(record.getRemark() + "/导入的原管护负责人工号与姓名不匹配");
                }

                /*if(!Objects.equals(old.getStatus(),8)){
                    record.setStatus(9);
                    record.setRemark(record.getRemark()+"/导入的原管护负责人状态不为离职状态，请先确认");
                }*/

            } else {
                record.setStatus(9);
                record.setOrgName(dto.getOrgName());
                record.setRegionName(dto.getRegionName());
                record.setOldCustomerAdminName(dto.getBeforeCustomerAdminName());
                record.setOldCustomerAdminId(dto.getBeforeCustomerAdminId());

                record.setRemark("导入的原管护负责人工号【" + dto.getBeforeCustomerAdminId() + "】在用户表中不存在");
            }
            Optional<AuthUserVO> newOpt = authUserVOList.stream().filter(u -> Objects.equals(dto.getAfterCustomerAdminId(), u.getUserId())).findFirst();
            if (newOpt.isPresent()) {
                AuthUserVO user = newOpt.get();
                if (Objects.equals(user.getUserName(), dto.getAfterCustomerAdminName())) {
                    record.setNewCustomerAdminName(user.getUserName());
                    record.setNewCustomerAdminId(user.getUserId());
                    record.setNewCustomerAdminJobCode(user.getJobCode());
                    record.setNewCustomerAdminMainJobNumber(user.getMainJobNumber());
                    record.setNewCustomerAdminOrgCode(user.getOrgCode());
                } else {
                    record.setStatus(9);
                    record.setNewCustomerAdminId(dto.getAfterCustomerAdminId());
                    record.setNewCustomerAdminName(dto.getAfterCustomerAdminName());
                    record.setRemark(record.getRemark() + "/" + "导入的交接人工号与姓名不匹配");
                }
            } else {
                record.setStatus(9);
                record.setNewCustomerAdminId(dto.getAfterCustomerAdminId());
                record.setNewCustomerAdminName(dto.getAfterCustomerAdminName());
                record.setRemark(record.getRemark() + "/" + "导入的交接人工号【" + dto.getAfterCustomerAdminId() + "】在用户表中不存在");
            }
            if (record.getStatus() == null) {
                record.setStatus(0);
            }
            list.add(record);
        });


        tmpCustomerAdminChangeImportRecordMapper.insertList(list);
    }

    @Transactional(rollbackFor = Exception.class)
    public void doCustomerAdminChangeImportRecord(TmpCustomerAdminChangeImportRecord dto) {
        if (org.apache.commons.lang.StringUtils.isBlank(dto.getOldCustomerAdminId())) {
            return;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(dto.getNewCustomerAdminId())) {
            return;
        }
        CustomerQuery query = new CustomerQuery();
        query.setCustomerAdmin(dto.getOldCustomerAdminId());
        //query.setOrganizationName(dto.getOrgName());

        query.setNotMask(true);
        query.setAll(true);
        List<Integer> customerIds = mapper.listCustomerIdsByRecommendInfo(query);
        if (CollectionUtils.isEmpty(customerIds)) {
            //存在一些数据没有抽取到客户记录表中
            smOrderMapper.updateOrderCustomerAdminV2(dto.getOldCustomerAdminId(), dto.getOrgCode(), dto.getNewCustomerAdminId(), dto.getNewCustomerAdminJobCode(), dto.getNewCustomerAdminMainJobNumber(), dto.getOrgCode(), null);
            return;
        }
        query.setCustomerIds(customerIds);
        List<CustomerListVO> customers = mapper.listCustomer(query);
        List<CustomerRemdLogDTO> logList = new ArrayList<>(customers.size());
        if (!customers.isEmpty()) {
            query.setCustomerIds(customers.stream().map(CustomerListVO::getCustomerId).collect(Collectors.toList()));
            Map<Integer, CustomerListVO> customerAdminMap = mapper.listCustomerAdminInfo(query)
                    .stream()
                    .distinct()
                    .collect(Collectors.toMap(CustomerListVO::getCustomerId, Function.identity()));
            Map<Integer, CustomerListVO> customerAgentMap = mapper.listCustomerAgentInfo(query)
                    .stream()
                    .distinct()
                    .collect(Collectors.toMap(CustomerListVO::getCustomerId, Function.identity()));
            customers.forEach(c -> {
                CustomerListVO ca = customerAdminMap.get(c.getCustomerId());
                if (ca != null) {
                    c.setCustomerAdminId(ca.getCustomerAdminId());
                    c.setCustomerAdminName(ca.getCustomerAdminName());
                    c.setCustomerAdminOrgName(ca.getCustomerAdminOrgName());
                    c.setCustomerAdminRegionName(ca.getCustomerAdminRegionName());
                    c.setCustomerAdminStatus(ca.getCustomerAdminStatus());
                }
            });
        } else {
            return;
        }
        customers.forEach(c -> {
            CustomerRemdLogDTO remdLogDTO = new CustomerRemdLogDTO();
            remdLogDTO.setCustomerId(c.getCustomerId());
            remdLogDTO.setCustomerNewAdminId(dto.getNewCustomerAdminId());
            remdLogDTO.setCustomerNewAdminJobCode(dto.getNewCustomerAdminJobCode());
            remdLogDTO.setCustomerOldAdminId(dto.getOldCustomerAdminId());
            remdLogDTO.setCustomerOldAdminJobCode(dto.getOldCustomerAdminJobCode());
            remdLogDTO.setCreateBy("system");
            logList.add(remdLogDTO);
        });
        mapper.insertCustomerRemdLogs(logList);

        CustomerAdminChangeDTO changeDTO = new CustomerAdminChangeDTO();
        changeDTO.setOldAdminId(dto.getOldCustomerAdminId());
        changeDTO.setOldAdminJobCode(dto.getOldCustomerAdminJobCode());
        changeDTO.setNewAdminId(dto.getNewCustomerAdminId());
        changeDTO.setNewAdminJobCode(dto.getNewCustomerAdminJobCode());
        mapper.updateCustomerAdmin(changeDTO);

        //该员工下所有的记录都变更
        smOrderMapper.updateOrderCustomerAdminV2(dto.getOldCustomerAdminId(), dto.getOrgCode(), dto.getNewCustomerAdminId(), dto.getNewCustomerAdminJobCode(), dto.getNewCustomerAdminMainJobNumber(), dto.getOrgCode(), null);
        /*UserPost userPost = dto.getNewAdminUserPost();
        smOrderMapper.updateOrderCustomerAdminV2(dto.getOldCustomerAdminId(),dto.getOrgCode(), dto.getNewCustomerAdminId(),dto.getNewCustomerAdminJobCode(),dto.getNewCustomerAdminMainJobNumber(),dto.getOrgCode(), customers.stream()
                .map(CustomerListVO::getIdNumber)
                .collect(Collectors.toList()));*/

        // 变更客户经理通知变更客户
        // WxUserVO oldAdmin = userMapper.getUserByUserId(dto.getOldAdminId());;
        /*WxUserVO userVO = SpringFactoryUtil.getBean(AuthUserMapper.class).getUserByUserId(dto.getNewCustomerAdminId());
        SpringFactoryUtil.getBean(SmsSenderUtil.class).sendCacMessage(customers.stream()
                        .map(CustomerListVO::getCellPhone)
                        .distinct()
                        .collect(Collectors.toList()),
                SmsSenderUtil.SmsCaCDTO.builder()
                        .originalAdminName(oldAdmin == null ? "无" : oldAdmin.getUserName())
                        .targetAdminName(userVO.getUserName())
                        .targetAdminMobile(userVO.getUserMobile())
                        .build()
        );*/
    }

    /************ s51 根据订单id，原管护信贷员工号 变更 管护客户经理 *************/
    @Transactional(rollbackFor = Exception.class)
    public int updateCustomerAdminByOrderId(String fhOrderId, String oldCustomerAdminId, String newCustomerAdminId) {
        if (org.apache.commons.lang.StringUtils.isBlank(oldCustomerAdminId)) {
            return 0;
        }
        if (org.apache.commons.lang.StringUtils.isBlank(newCustomerAdminId)) {
            return 0;
        }
        //smOrderMapper.get
        return 0;
    }

    private void addCustomerRemdLog(Integer customerId, String newCustomerAdminId, String newJobCode, String oldCustomerAdminId, String oldJobCode) {
        CustomerRemdLogDTO remdLogDTO = new CustomerRemdLogDTO();
        remdLogDTO.setCustomerId(customerId);
        remdLogDTO.setCustomerNewAdminId(newCustomerAdminId);
        remdLogDTO.setCustomerNewAdminJobCode(newJobCode);
        remdLogDTO.setCustomerOldAdminId(oldCustomerAdminId);
        remdLogDTO.setCustomerOldAdminJobCode(oldJobCode);
        remdLogDTO.setCreateBy("system");
        mapper.insertCustomerRemdLog(remdLogDTO);
    }

}
