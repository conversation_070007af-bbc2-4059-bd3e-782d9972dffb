package com.cfpamf.ms.insur.admin.service.order;

import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.config.PolicyWarnProperties;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.auto.AutoOrderPolicyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.*;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.commission.CommissionRedoTypeEnum;
import com.cfpamf.ms.insur.admin.enums.correct.EnumWhaleCorrectProject;
import com.cfpamf.ms.insur.admin.enums.order.EnumRiskCorrectType;
import com.cfpamf.ms.insur.admin.enums.order.EnumWhaleChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.enums.order.OrderVisitResultEnum;
import com.cfpamf.ms.insur.admin.enums.whale.EnumWhalePolicyEvent;
import com.cfpamf.ms.insur.admin.event.OrderCommissionChangeEvent;
import com.cfpamf.ms.insur.admin.event.OrderImportEvent;
import com.cfpamf.ms.insur.admin.event.OrderRenewCommissionBatchEvent;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitResponse;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhInsuredPerson;
import com.cfpamf.ms.insur.admin.external.fh.dto.FhOrderInfo;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.channel.WhaleOrderChannelService;
import com.cfpamf.ms.insur.admin.external.whale.enums.*;
import com.cfpamf.ms.insur.admin.external.whale.model.*;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyResult;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyVo;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermProductNotifyVo;
import com.cfpamf.ms.insur.admin.pojo.convertor.OrderConvertor;
import com.cfpamf.ms.insur.admin.pojo.dto.JumpH5OtherParams;
import com.cfpamf.ms.insur.admin.pojo.dto.SmCreateOrderSubmitRequest;
import com.cfpamf.ms.insur.admin.pojo.dto.order.whale.PolicyCorrectCheck;
import com.cfpamf.ms.insur.admin.pojo.dto.commission.CommissionRedoDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.renewal.RenewalTermDTO;
import com.cfpamf.ms.insur.admin.pojo.form.product.PremiumDutyDetail;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.pojo.po.UserPost;
import com.cfpamf.ms.insur.admin.pojo.po.auto.order.AutoOrderPolicy;
import com.cfpamf.ms.insur.admin.pojo.po.order.*;
import com.cfpamf.ms.insur.admin.pojo.po.order.extend.SmOrderExtendWhale;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductDetailVO;
import com.cfpamf.ms.insur.admin.pojo.vo.order.activitie.SmOrderVillageActivityFourVo;
import com.cfpamf.ms.insur.admin.renewal.dao.OrderRenewalMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalTermMapper;
import com.cfpamf.ms.insur.admin.renewal.dao.SmOrderRenewalTermRiskMapper;
import com.cfpamf.ms.insur.admin.renewal.entity.FastRenewalTermOrder;
import com.cfpamf.ms.insur.admin.renewal.entity.OrderRenewal;
import com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm;
import com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTermRisk;
import com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalStatus;
import com.cfpamf.ms.insur.admin.renewal.enums.OrderRenewalType;
import com.cfpamf.ms.insur.admin.renewal.service.InsuranceRenewService;
import com.cfpamf.ms.insur.admin.service.OrderNoGenerator;
import com.cfpamf.ms.insur.admin.service.SmOrderManageService;
import com.cfpamf.ms.insur.admin.service.SmXjxhService;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.cfpamf.ms.insur.admin.renewal.vo.PersonVo;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.correct.ChannelReferrerCorrectService;
import com.cfpamf.ms.insur.admin.service.correct.CorrectManagerService;
import com.cfpamf.ms.insur.admin.service.correct.CustomerManagerChangeCorrectService;
import com.cfpamf.ms.insur.admin.service.correct.PolicyProductCorrectService;
import com.cfpamf.ms.insur.admin.service.order.activity.SmOrderVillageActivityFourService;
import com.cfpamf.ms.insur.admin.service.order.syn.SynCommissionInfoService;
import com.cfpamf.ms.insur.admin.service.order.syn.SynVehicleInfoService;
import com.cfpamf.ms.insur.admin.service.renewalterm.SmOrderRenewalTermService;
import com.cfpamf.ms.insur.admin.settlement.enums.PolicyProductTypeEnum;
import com.cfpamf.ms.insur.admin.util.AssertUtills;
import com.cfpamf.ms.insur.admin.util.CompareTools;
import com.cfpamf.ms.insur.base.config.tx.TxServiceManager;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.DingTalkService;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.LambdaUtils;
import com.cfpamf.ms.insur.base.util.LocalDateUtil;
import com.cfpamf.ms.insur.base.util.SpringFactoryUtil;
import com.cfpamf.ms.insur.common.enums.EnumMonitor;
import com.cfpamf.ms.insur.common.service.monitor.BusinessMonitorService;
import com.cfpamf.ms.insur.facade.dto.operation.resp.PreservationDto;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeItemDetailVo;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeItemVo;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.ChannelRecommenderChangeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.order.*;
import com.cfpamf.ms.insur.weixin.util.StringTools;
import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2020/11/30 16:43
 */
@Service
@Slf4j
public class WhaleOrderService extends AbstractSmOrderService {
    public static String DEFAULT_PLAN = "WHALE_DEFAULT";

    private static String payUnitYear = "y|Y";
    /**
     * 保单产品类型
     */
    private static String POLICY_PRODUCT_TYPE = "policyProductType";
    /**
     * 保全编号
     */
    private static String PRESERVATION_CODE = "preservationCode";

    @Autowired
    WhaleOrderServiceAdapterImpl adapter;

    @Autowired
    SmXjxhService xjxhService;

    @Autowired
    SmOrderPolicyService policyService;

    @Autowired
    private SmOrderRenewalTermMapper renewalTermMapper;

    @Autowired
    private SmOrderRenewalTermRiskMapper renewalTermRiskDutyMapper;

    @Autowired
    private DingTalkService dingTalkService;

    @Autowired
    SmOrderExtendWhaleMapper whaleMapper;

    @Autowired
    private SmOrderRenewalTermService orderRenewalTermService;
    @Autowired
    private SmOrderDistributionBatchPushPushMapper distributionBatchPushPushMapper;
    @Autowired
    SmOrderManageService manageService;

    @Autowired
    LifeCappService lifeCappService;

    @Autowired
    CappH5Service cappH5Service;

    @Autowired
    SmOrderInsuredMapper insuredMapper;

    @Autowired
    SmOrderRiskCorrectMapper riskCorrectMapper;

    @Autowired
    SmOrderExtendThDistService thDistService;

    @Lazy
    @Autowired
    EventBusEngine eventBusEngine;
    @Autowired
    private OrderRenewalMapper orderRenewalMapper;
    @Autowired
    private InsuranceRenewService insuranceRenewService;
    @Autowired
    private SmOrderWhaleNotifyService smOrderWhaleNotifyService;
    @Autowired
    OrderNoGenerator orderNoGenerator;
    @Autowired
    private CommonOrderService commonOrderService;
    @Autowired
    private SmOrderVillageActivityService smOrderVillageActivityService;
    @Autowired
    private SynVehicleInfoService synVehicleInfoService;
    @Autowired
    private SmOrderWhaleCorrectNotifyMapper smOrderWhaleCorrectNotifyMapper;
    @Autowired
    private SmOrderVillageActivityFourService smOrderVillageActivityFourService;
    @Autowired
    private AutoOrderPolicyMapper orderPolicyMapper;
    @Autowired
    private TxServiceManager txServiceManager;
    @Autowired
    private SynCommissionInfoService synCommissionInfoService;
    @Autowired
    private WhaleOrderChannelService whaleOrderChannelService;
    @Autowired
    private BusinessMonitorService businessMonitorService;
    @Autowired
    private SmCommissionDetailService commissionDetailService;

    @Value("${spring.profiles.active:dev}")
    private String env;

    @Override
    public String getH5JumpUrl(Integer productId, String userId, String params,
                               JumpH5OtherParams otherParams) {
        String mapChannel = EnumWhaleChannel.mapWhaleChannel(params);
        SmProductDetailVO product = productService.getProductById(productId);
        String h5Url = product.getH5Url();
        return adapter.genDynamicUrl(h5Url, userId, mapChannel, otherParams);
    }

    @Override
    protected String channel() {
        return EnumChannel.XJ.getCode();
    }

    @Override
    protected com.cfpamf.ms.insur.admin.external.ChannelOrderService orderService() {
        return adapter;
    }

    @Override
    public boolean support(String channel) {
        return Objects.equals(channel, channel());
    }

    @Override
    public Map<String, String> updateOrderPolicyInfo(String orderId) {

        List<SmOrderListVO> orderVos = orderMapper.listOrderInsuredDetailByOrderId(orderId);
        if (orderVos == null || orderVos.size() == 0) {
            throw new BizException(ExcptEnum.PRODUCT_ERROR_201009);
        }
        SmOrderListVO order = orderVos.iterator().next();
        if (Objects.equals(order.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS)) {
            return Collections.singletonMap(order.getInsuredIdNumber(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
        }
        busEngine.publish(new OrderCommissionChangeEvent(orderId));
        return null;
    }


    @Override
    protected OrderSubmitResponse submitSuccessAfter(String userUniqueId, SmPlanVO planVo, OrderSubmitRequest dto, OrderSubmitRequest change, OrderSubmitResponse submitResponse) {
        throw new UnsupportedOperationException("小鲸回调暂不支持");
    }

    @Override
    public OrderSubmitResponse submitOrder(String userUniqueId, OrderSubmitRequest dto) {
        throw new UnsupportedOperationException("小鲸回调不支持订单创建");
    }

    /**
     * 小程序是否绑定
     *
     * @param userId
     */
    public void whaleAuthStatus(String userId) {

    }

    private void validRepeat(WhaleContract contract) {
        List<InsuredInfoList> insuredInfos = contract.getInsuredInfoList();
        String queryIdNumber = null;
        if (!CollectionUtils.isEmpty(insuredInfos)) {
            queryIdNumber = insuredInfos.get(0).getInsuredIdCard();
        }
        String policyNo = contract.getContractBaseInfo().getPolicyNo();
        if (StringUtils.isBlank(policyNo)) {
            log.error("小鲸通知保单号不存在{}", JSON.toJSONString(contract));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "保单号不能为空");
        }
        List<SmOrderInsured> list = insuredMapper.selectByPolicyNoAndIdNumber(policyNo, queryIdNumber);

        if (!CollectionUtils.isEmpty(list)) {
            log.warn("小鲸推送重复数据{}", JSON.toJSONString(contract));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "数据已存在：" + policyNo);
        }
    }

    /**
     * 处理通知数据
     *
     * @param contractCode
     * @param type
     */
    private void handPolicyEvent(String contractCode, String type) {
        log.info("开始处理小鲸保单事件:{},{}",contractCode,type);
        if(Objects.equals(type, EnumWhalePolicyEvent.REPAY.name())){
            policyRepay(contractCode);
            return;
        }

        SmOrderExtendWhale smOrderExtendWhale = whaleMapper.selectByContractCode(contractCode);
        WhaleResp<WhaleContract> policy = adapter.getPolicyNoByBusinessScenario(contractCode,EnumChannelBusinessScenario.SYN_WHALE_POLICY);
        WhaleContract contract = policy.getData();

        //如果是新数据过来 先校验保单是否存在
        if (Objects.isNull(smOrderExtendWhale)
                &&(Objects.isNull(contract.getDataSource())
                || (contract.getDataSource() != 1 && contract.getDataSource() != 2))) {
            //校验保单是否存在
            validRepeat(contract);
        }
        //改良的方案:
        SmPlanVO planVO = adapter.convertPlan(contract);
        if (Objects.isNull(planVO)) {
            log.warn("小鲸通知产品计划未配置:{}",JSON.toJSONString(contract));
            throw new MSBizNormalException(ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getCode(), "小鲸通知产品计划未配置:"+contractCode);
        }
        log.info("小鲸保单险种映射:{}",JSON.toJSONString(planVO));
        //设置车险默认被保人
        synVehicleInfoService.whaleSynInsured(contract);
        SmCreateOrderSubmitRequest submitRequest = adapter.cvtByNotify(planVO, contract
                , Optional.ofNullable(smOrderExtendWhale).map(SmOrderExtendWhale::getFhOrderId).orElse(null));
        //设置农保导单渠道信息
        whaleOrderChannelService.setWhaleImportChannel(planVO,contract,submitRequest);
        //为了事物
        WhaleOrderService that = (WhaleOrderService) AopContext.currentProxy();
        if (Objects.isNull(smOrderExtendWhale) && (Objects.isNull(contract.getDataSource())
                || (contract.getDataSource() != 1 && contract.getDataSource() != 2))) {
            log.info("新增保单数据:{}", contractCode);
            commonOrderService.saveOrder(planVO, contract, submitRequest);
        } else {
            //农保保单特殊处理
            if ((Objects.nonNull(contract.getDataSource())
                    && (contract.getDataSource() == 1 || contract.getDataSource() == 2))) {
                AssertUtills.notNull(contract.getContractBaseInfo(), "小鲸通知保单信息不能为空");
                AssertUtills.isNotBlank(contract.getContractBaseInfo().getPolicyNo(), "保单号为空");
                final String policyNo = contract.getContractBaseInfo().getPolicyNo();
                final List<SmOrderInsured> insureds = insuredMapper.selectByPolicyNoList(Collections
                        .singletonList(policyNo));
                AssertUtills.isTrue(CollectionUtils.isNotEmpty(insureds), "农保保单系统不存在");
                final List<String> fhOrderIds = insureds.stream()
                        .map(SmOrderInsured::getFhOrderId)
                        .filter(StringUtils::isNotEmpty)
                        .distinct()
                        .collect(Collectors
                                .toList());
                AssertUtills.isTrue(CollectionUtils.isNotEmpty(fhOrderIds), "农保保单异常，订单号为空");
                AssertUtills.isTrue(fhOrderIds.size()==1, "保单对应的订单号有多条");
                final String nbFhOrderId = fhOrderIds.get(0);
                //smOrderExtendWhale.setFhOrderId(nbFhOrderId);
                submitRequest.setFhOrderId(nbFhOrderId);
                log.info("农保保单系统存在订单号:{}", nbFhOrderId);
            }
            final String fhOrderId = Optional.ofNullable(smOrderExtendWhale).map(SmOrderExtendWhale::getFhOrderId).orElse(null);
            log.info("修改数据 {},{}", fhOrderId,submitRequest.getFhOrderId());
            SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(submitRequest.getFhOrderId());
            log.info("原订单信息 {}", JSON.toJSONString(order));
            String newRecommendId = submitRequest.getProductInfo().getRecommendId();
            String newCustomerManagerId = submitRequest.getProductInfo().getCustomerAdminId();
            log.info("推荐人工号:{},管护人信息:{}", newRecommendId,newCustomerManagerId);
            String policyUrl = contract.getContractBaseInfo().getPolicyUrl();
            that.transactional(() -> {
                //暂存更新前的四级分销信息
                final SmOrderVillageActivityFourVo startFourVo = smOrderVillageActivityFourService
                        .queryActivityFourVo(submitRequest.getFhOrderId());
                if (Objects.nonNull(order)) {
                    //修改推荐人信息F
                    if(!Objects.equals(order.getRecommendId(), newRecommendId)) {
                        log.info("订单批改推荐人，订单号{}，原推荐人{}，变更后推荐人{}", submitRequest.getFhOrderId(), order.getRecommendId(), newRecommendId);
                        manageService.updateOrderRecommendByOrderId(submitRequest.getFhOrderId(), order.getRecommendId(), newRecommendId, true);
                    }
                    //修改推荐人信息F
                    if(!Objects.equals(order.getCustomerAdminId(), newCustomerManagerId)) {
                        log.info("订单批改管护人，订单号{}，原管护人{}，变更后管护人{}", submitRequest.getFhOrderId(), order.getCustomerAdminId(), newCustomerManagerId);
                        manageService.updateOrderCustomerManagerByOrderId(submitRequest.getFhOrderId(), order.getCustomerAdminId(), newCustomerManagerId, true);
                    }
                }

                SmOrderExtendWhale extendModel = commonOrderService.convertOrderExtendVo( fhOrderId, contract);
                whaleMapper.insertOrUpdate(extendModel);
                //同步活动信息
                smOrderVillageActivityService.whaleSynCreate(contract, submitRequest.getFhOrderId());
                //四级分销相关信息更新
                final SmOrderVillageActivityFourVo endFourVo = smOrderVillageActivityFourService
                        .queryActivityFourVo(submitRequest.getFhOrderId());
                smOrderVillageActivityFourService.whaleSynUp(startFourVo, endFourVo);
                //同步车险信息
                synVehicleInfoService.whaleSynCreate(contract, submitRequest.getFhOrderId(), false);
                //update 修改退保
                submitRequest.getInsuredPerson()
                        .forEach(ip -> {
                            //修改状态
                            // 被保险人
                            if (StringUtils.isNotBlank(policyUrl)) {
                                orderMapper.updateOrderAppStatusAndPolicyUrl(submitRequest.getFhOrderId(), EnumWhalePolicyStatus.cvtInsCode(contract.getContractBaseInfo().getPolicyStatus())
                                        , ip.getIdNumber(), policyUrl);
                            } else {
                                orderMapper.updateOrderAppStatus(submitRequest.getFhOrderId(), EnumWhalePolicyStatus.cvtInsCode(contract.getContractBaseInfo().getPolicyStatus())
                                        , ip.getIdNumber());
                            }
                            // item
                            updateItemAppStatus(EnumWhalePolicyStatus.cvtInsCode(contract.getContractBaseInfo().getPolicyStatus()),
                                    submitRequest.getFhOrderId(), ip.getIdNumber());

                        });
                //如果是长险，且退保，需要将待续期记录删除
                if(EnumWhalePolicyStatus.isSurrender(contract.getContractBaseInfo().getPolicyStatus())
                        //因为小鲸未吧长短险标志传过来，只能所有的退保记录都去查询一下
                        // && Objects.equals(contract.getContractBaseInfo().getLongShortFlag(),1)
                    ){
                    orderRenewalTermService.logicDelete(contract.getContractBaseInfo().getPolicyNo());
                }
                //修改回访状态
                smOrderPolicyService.addOrderPolicyInsureAmount(cvtOrderPolicy(contract, extendModel), Boolean.TRUE);
                return null;
            });
        }
        submitRequest.getInsuredPerson().stream()
                .peek(ip -> {
                    // 处理险总退保
                    ip.getDuties().stream().map(PremiumDutyDetail.class::cast)
                            .filter(duty -> Objects.equals(duty.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS))
                            // 退保操作
                            .forEach(s -> updateRiskAppStatus(s, submitRequest.getFhOrderId(), ip.getIdNumber(), ip.getProduct().getPlanId()));
                })
                .filter(ip -> Objects.equals(ip.getAppStatus(), SmConstants.POLICY_STATUS_CANCEL_SUCCESS))
                .forEach(ip -> {
                    //处理个人退保
                    orderMapper.updateOrderSurrenderTimeSimple(submitRequest.getFhOrderId(), ip.getIdNumber());

                });
        //险种变更需要重置续期保单的险种数据
        try {
            log.info("开始重置续期险种数据：{}", submitRequest.getFhOrderId());
            int r = orderRenewalTermService.resetRenewalTermProduct(submitRequest.getFhOrderId());
            log.info("续期险种数据重置完成：{},{}", submitRequest.getFhOrderId(), r);

        } catch (Exception e) {
            log.error("续期险种层数据重置失败:{}", submitRequest.getFhOrderId(), e);
        }
        busEngine.publish(new OrderCommissionChangeEvent(submitRequest.getFhOrderId()));
    }


    /**
     * 处理通知数据
     *
     * @param contractCode
     */
    private void policyRepay(String contractCode) {
        log.info("保单重新缴费：{}",contractCode);
        WhaleOrderService orderService = SpringFactoryUtil.getBean(WhaleOrderService.class);

        WhaleResp<WhaleContract> policy = adapter.getPolicyNoByBusinessScenario(contractCode,EnumChannelBusinessScenario.SYN_WHALE_POLICY);
        WhaleContract contract = policy.getData();
        if(contract.getPolicyProductType().equals(PolicyProductTypeEnum.GROUP.getCode())){
            log.warn("重新流程只支持非团险保单");
            return;
        }

        final String policyNo = contract.getContractBaseInfo().getPolicyNo();
        final List<SmOrderInsured> insureds = insuredMapper.selectByPolicyNoList(Collections.singletonList(policyNo));
        AssertUtills.isTrue(CollectionUtils.isNotEmpty(insureds), "农保保单系统不存在");
        final List<String> fhOrderIds = insureds.stream()
                .map(SmOrderInsured::getFhOrderId)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        AssertUtills.isTrue(CollectionUtils.isNotEmpty(fhOrderIds), "农保保单异常，订单号为空");
        final String orderId = fhOrderIds.get(0);
        log.info("保单重新缴费，开始处理订单状态:{},{}",orderId,policyNo);
        txServiceManager.excute(() -> {
            orderMapper.updateOrderAppStatus(orderId, SmConstants.POLICY_STATUS_SUCCESS, null);
            orderService.policyItemChange(orderId, SmConstants.POLICY_STATUS_SUCCESS);
            riskDutyMapper.policyRepay(orderId,SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
            smOrderPolicyService.policyRepay(orderId);
        });
        log.info("保单重新缴费，订单状态处理成功，开始重新计算佣金:{},{}",orderId,policyNo);
        CommissionRedoDTO redo = new CommissionRedoDTO();
        redo.setPolicyNo(policyNo);
        redo.setOrderId(orderId);
        redo.setOperationType(CommissionRedoTypeEnum.REDO_COMMISSION);
        commissionDetailService.redoCommission(redo);
        log.info("保单重新缴费，佣金重算完成，开始推送事件告警:{},{}",orderId,policyNo);
        boolean activityOrder = smOrderVillageActivityFourService.isFourthLevelDistributionOrder(orderId);
        if(activityOrder) {
//            String messageFormat = String.format("订单已经重新缴费，请关注四级分销信息:%s,%s",orderId,policyNo);
//            dingTalkService.sendChatMediaMessageWithGroupRobot(PolicyWarnProperties.distributionGroupToken,PolicyWarnProperties.distributionGroupSecret, buildMsg(messageFormat));
            repayAlarm("1",orderId,policyNo);
        }
    }

    /**
     * 重新缴费-告警
     * @param orderId 订单Id
     */
    public void repayAlarm(String appStatus,String orderId,String policyNo){
        boolean activityOrder = smOrderVillageActivityFourService.isFourthLevelDistributionOrder(orderId);
        if(!activityOrder) {
            log.info("该订单非四级分销单，无告警信息:{}",orderId);
            return;
        }
        List<SmOrderDistributionBatchPush> data = distributionBatchPushPushMapper.listByOrderId(orderId);
        if(CollectionUtils.isEmpty(data)){
            log.info("该订单事件暂未推送分销，无需告警信息:{}",orderId);
            return;
        }
        //如果之前推送过分销系统，则再次缴费时需要人工介入处理
        BigDecimal premium = BigDecimal.ZERO;
        boolean isAlarm = false;
        for(SmOrderDistributionBatchPush order:data){
            int state = order.getPushState();
            if(Objects.equals(state,1)){
                isAlarm = Objects.equals(order.getPolicyStatus(),appStatus);
                if(isAlarm){
                    premium = order.getPayAmount();
                    break;
                }
            }
        }
        if(isAlarm){
            log.info("开始推送四级分销告警:{},{}",env,orderId);
            businessMonitorService.addMonitorByKey(EnumMonitor.DISTRIBUTION_POLICY_REPAY, orderId,"该四级分销单已存在结算事件，请联系业务人员线下结算:"+orderId);
            String msg = buildMsg(orderId,policyNo,appStatus,premium);
            dingTalkService.sendChatMediaMessageWithGroupRobot(PolicyWarnProperties.getAlarmToken(env),PolicyWarnProperties.distributionGroupSecret,"保单数据监控", msg);
        }
    }



    private String buildMsg(String orderId,String policyNo,String appStatus,BigDecimal premium) {
        StringBuilder message = new StringBuilder("### 蓝医保产品需处理的保单数据监控 \n");
        message.append("\n｜类型｜保费｜订单号｜保单号｜\n");
        String type = appStatus.equals("1") ? "重新续保" : "退费";
        message.append("\n｜"+type+"｜");
        message.append(premium+"｜");
        message.append(orderId+"｜");
        message.append(policyNo+"｜");
        return message.toString();
    }

    /**
     * 生成续保信息
     *
     * @param createOrder
     */
    private void generateRenewalInfo(SmCreateOrderSubmitRequest createOrder) {

        if (Objects.isNull(createOrder)) {
            return;
        }
        List<FhInsuredPerson> insuredPersonList = createOrder.getInsuredPerson();
        if (CollectionUtils.isEmpty(insuredPersonList)) {
            return;
        }
        String sourcePolicyNo = insuredPersonList.get(0).getOldPolicyNo();
        Integer renewalPeriod = insuredPersonList.get(0).getRenewalPeriod();
        String newPolicyNo = insuredPersonList.get(0).getPolicyNo();
        if (StringUtils.isEmpty(sourcePolicyNo)) {
            return;
        }


        //查找原订单是否存在
        SmBaseOrderVO sourcePolicyOrder = orderMapper.getBaseOrderInfoByPolicyNo(sourcePolicyNo, null);

        if (Objects.isNull(sourcePolicyOrder)) {
            log.error("续保保单{}的原保单号{}不存在！！！", newPolicyNo, sourcePolicyNo);
            throw new BizException("", String.format("续保保单%s的原保单号%s不存在！！！", newPolicyNo, sourcePolicyNo));
        }

        List<PersonVo> sourceInsuredList = insuranceRenewService.initInsuredInfo(sourcePolicyNo, sourcePolicyOrder.getProductAttrCode());

        if (CollectionUtils.isEmpty(sourceInsuredList)) {
            return;
        }

        //根据保单维度来插入数据
        List<OrderRenewal> existedOrderRenewalList = orderRenewalMapper.listRenewalByOldPolicyNoList(Lists.newArrayList(sourcePolicyNo));
        List<OrderRenewal> renewedOrderList = existedOrderRenewalList.stream().filter(x -> Objects.equals(OrderRenewalStatus.RENEWED, x.getStatus())).collect(Collectors.toList());

        //如果该新保单号已经存在续保保单号中
        if (renewedOrderList.stream().anyMatch(x -> StringUtils.equals(x.getNewPolicyNo(), newPolicyNo))) {
            return;
        }
        PersonVo smOrderInsured = sourceInsuredList.get(0);
        OrderRenewal orderRenewal = new OrderRenewal();
        orderRenewal.setOldOrderId(smOrderInsured.getFhOrderId());
        orderRenewal.setOldPolicyNo(sourcePolicyNo);
        orderRenewal.setNewOrderId(createOrder.getFhOrderId());
        orderRenewal.setNewPolicyNo(createOrder.getPolicyNo());
        orderRenewal.setRenewalPeriod(renewalPeriod);
        orderRenewal.setType(OrderRenewalType.RENEWAL);
        orderRenewal.setStatus(OrderRenewalStatus.RENEWED);
        orderRenewal.setIdNumber(smOrderInsured.getIdCard());
        orderRenewal.setCreateBy(HttpRequestUtil.getUserId());
        orderRenewal.setUpdateBy(HttpRequestUtil.getUserId());
        orderRenewal.setNewPolicyNo(newPolicyNo);
        orderRenewal.setPlanId(createOrder.getPlanId());

        Date startTime = Optional.ofNullable(createOrder.getOrderInfo())
                .map(FhOrderInfo::getStartTime)
                .filter(StringUtils::isNotEmpty)
                .map(DateUtil::parseDate)
                .orElse(null);
        orderRenewal.setStartTime(startTime);
        orderRenewal.setProductId(StringUtils.isNotBlank(createOrder.getProductId()) ? Integer.valueOf(createOrder.getProductId()) : null);
        orderRenewalMapper.insertList(Lists.newArrayList(orderRenewal));

        insuranceRenewService.addRenewedPolicy(sourcePolicyOrder, sourceInsuredList, createOrder);
    }


    /**
     * x修改险种状态
     *
     * @param s
     * @param planId
     * @param fhOrderId
     * @param idNumber
     */
    public void updateRiskAppStatus(PremiumDutyDetail s, String fhOrderId, String idNumber, Integer planId) {

        SmOrderRiskDuty dutyUpdate = new SmOrderRiskDuty();
        dutyUpdate.setAppStatus(s.getAppStatus());
        dutyUpdate.setSurrenderTime(s.getSurrenderTime());
        dutyUpdate.setSurrenderValidTime(s.getSurrenderValidTime());
        dutyUpdate.setRefundAmount(s.getSurrenderAmount());
        dutyUpdate.setSurrenderType(s.getSurrenderType());

        SmOrderRiskDuty query = new SmOrderRiskDuty();
        query.setInsuredIdNumber(idNumber);
        query.setRiskCode(s.getRiskCode());
        query.setFhOrderId(fhOrderId);

        int i = riskDutyMapper.updateSurrenderByRiskCode(dutyUpdate, query);
        if (i > 0) {
            addRiskCorrect(s, fhOrderId, idNumber, planId);
        }

    }

    /**
     * 保单险种退费(全额退费)
     *
     */
    public void policyRefund(String orderId) {
        riskDutyMapper.policyRefund(orderId,SmConstants.POLICY_STATUS_CANCEL_SUCCESS);
    }

    private void addRiskCorrect(PremiumDutyDetail s, String fhOrderId, String idNumber, Integer planId) {

        StringJoiner sj = new StringJoiner("/");
        String correctId = sj.add(fhOrderId)
                .add(idNumber)
                .add(s.getRiskCode())
                .add(EnumRiskCorrectType.SUB.getType()).toString();

        log.info("开始保存险种退保记录:{}", sj);
        SmOrderRiskCorrect correct = riskCorrectMapper.selectByCorrectId(correctId);
        if (Objects.isNull(correct)) {
            SmOrderRiskCorrect add = new SmOrderRiskCorrect();
            add.setOrderId(fhOrderId);
            add.setPolicyNo(s.getPolicyNo());
            add.setCorrectId(correctId);
            add.setCorrectType(EnumRiskCorrectType.SUB.getType());
            add.setRiskId(s.getSysRiskId());
            add.setPlanId(planId);
            SmBaseOrderVO baseOrder = orderMapper.getBaseOrderInfoByOrderId(fhOrderId);
            log.info("当前管控客户经理为:{}", baseOrder.getCustomerAdminId());
            add.setCustomerAdminId(baseOrder.getCustomerAdminId());
            riskCorrectMapper.insertUseGeneratedKeys(add);
        }
    }


    public void updateItemAppStatus(String status, String fhOrderId, String idNumber) {
        SmOrderItem update = new SmOrderItem();
        update.setEnabledFlag(null);
        update.setAppStatus(status);
        Example example = new Example(SmOrderItem.class);
        example.createCriteria()
                .andEqualTo("idNumber", idNumber)
                .andEqualTo("fhOrderId", fhOrderId);
        orderItemMapper.updateByExampleSelective(update, example);
    }

    public void policyItemChange(String fhOrderId,String appStatus) {
        SmOrderItem update = new SmOrderItem();
        update.setEnabledFlag(0);
        update.setAppStatus(appStatus);
        Example example = new Example(SmOrderItem.class);
        example.createCriteria().andEqualTo("fhOrderId", fhOrderId);
        orderItemMapper.updateByExampleSelective(update, example);
    }

    /**
     * 承包成功 通知
     *
     * @param request
     * @param response
     */
    @Override
    public void handAcceptSuccess(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String contractCode = request.getParameter("contractCode");
        String policyOperation = request.getParameter("policyOperation");
        String preservationProject = request.getParameter("preservationProject");
        String policyProductType = request.getParameter(POLICY_PRODUCT_TYPE);
        String preservationCode = request.getParameter(PRESERVATION_CODE);
        policyOperation = StringUtils.isBlank(policyOperation) ? EnumWhalePolicyEvent.CREATE.name() : policyOperation;

        log.info("小鲸保单数据回传:{}-{}", contractCode, preservationCode);
        if (StringUtils.isBlank(contractCode)) {
            log.error("小鲸回调通知保单唯一编码为空{}", contractCode);
            return;
        }

        String lockKey = String.format("xj:callback:%s,%s", contractCode,preservationCode);
        boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
        if (!lockSuccess) {
            log.warn("小鲸保单事件冲突(获取锁失败),请稍后重试:{},{}",contractCode,preservationCode);
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }
        try {
            if (Objects.equals(policyProductType, EnumWhalePolicyProductType.GROUP.getCode())) {
                groupPolicyEvent(contractCode, preservationCode, preservationProject, policyOperation);
            } else {
                policyEvent(contractCode, preservationCode, preservationProject, policyOperation);
            }
        }catch (MSBizNormalException e){
            log.warn("小鲸保单事件处理失败",e);
            String errCode = e.getErrorCode();
            String errMsg = e.getMessage();
            String alarmFormat = String.format("错误告警:[%s],保单合同号：[%s]",errMsg,contractCode);
            //部分异常需要通知业务人员配置，开发人员无需介入，所以此处需要区分不同的异常告警信息
            if(Objects.equals(errCode,ExcptEnum.XJ_PRODUCT_MAP_NOT_CONFIG.getCode())){
                businessMonitorService.addMonitorByKey(EnumMonitor.WHALE_POLICY_RISK_UNKNOWN,contractCode,alarmFormat);
            }else{
                businessMonitorService.addMonitor(EnumMonitor.WHALE_POLICY_EVENT_ERROR,alarmFormat);
            }
            throw e;
        }
        catch (Exception e){
            log.warn("小鲸保单事件处理失败",e);
            String alarmFormat = String.format("小鲸保单事件处理失败:%s,%s,%s",contractCode,preservationCode,policyOperation);
            businessMonitorService.addMonitor(EnumMonitor.WHALE_POLICY_EVENT_ERROR,alarmFormat);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),e.getMessage());
        }
        finally{
            tokenService.unlockBusinessToken(lockKey);
        }
        response.getWriter().write("{\"code\": 20000 }");
    }

//    /**
//     * 承包成功 通知
//     *
//     * @param request
//     * @param response
//     */
//    public void handAcceptSuccessV1(HttpServletRequest request, HttpServletResponse response) throws IOException {
//
//        String contractCode = request.getParameter("contractCode");
//        String policyOperation = request.getParameter("policyOperation");
//        String policyProductType = request.getParameter(POLICY_PRODUCT_TYPE);
//        String preservationCode = request.getParameter(PRESERVATION_CODE);
//        String preservationProject = request.getParameter("preservationProject");
//
//        //默认新契约事件
//        policyOperation = StringUtils.isBlank(policyOperation) ? EnumWhalePolicyEvent.CREATE.name() : policyOperation;
//        /**
//         * [correctEvent]接口不支持所有保全事件，不支持的事件通知走原来的逻辑
//         * 目前支持:险种变更。
//         */
//        if (StringUtils.isNotBlank(preservationCode)&& EnumWhaleCorrectProject.acceptPreservationMsg(preservationProject)) {
//            //1. 处理一些较特殊的保全，历史流程比较复杂，接口职责不单一，所以拆除了部分保全单独处理
//            correctEvent(request);
//        } else {
//            //2. 普通的保单事件
//            policyEvent(contractCode, policyOperation, policyProductType, preservationCode);
//        }
//
//        response.getWriter().write("{\"code\": 20000 }");
//    }

    /**
     * 小鲸-保单事件变更
     * 团险
     * @param contractCode
     * @param eventType
     * @param preservationCode
     */
    private void groupPolicyEvent(String contractCode, String preservationCode, String preservationProject, String eventType) {
        log.info("小鲸团险保单事件推送:操作类型{}-合同编号{}{}-保全编号{}-{}", eventType, contractCode, preservationCode, preservationProject);
        if (StringUtils.isBlank(contractCode)) {
            log.warn("小鲸回调通知保单唯一编码为空{}", contractCode);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg());
        }
        //1. 一些通用的保全(吴锡开发的第一版)
        if(EnumWhaleCorrectProject.acceptPreservationMsg(preservationProject)){
            correctEvent(contractCode,preservationCode,preservationProject);
            return;
        }
        //2. 团险保单事件V2版本
        handGroupPolicyChange(contractCode, EnumWhalePolicyProductType.GROUP.getCode(), eventType, preservationCode);
    }

    /**
     * 小鲸-保单事件变更
     *
     * @param contractCode
     * @param preservationCode
     * @param preservationProject
     * @param eventType
     */
    private void policyEvent(String contractCode, String preservationCode, String preservationProject, String eventType) {
        log.info("小鲸个险保单事件推送:操作类型{}-合同编号{}{}-保全编号{}-{}", eventType, contractCode, preservationCode,preservationProject);
        if (StringUtils.isBlank(contractCode)) {
            log.warn("小鲸回调通知保单唯一编码为空{}", contractCode);
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg());
        }
        //1. 一些公共的保全
        if(EnumWhaleCorrectProject.acceptPreservationMsg(preservationProject)){
            correctEvent(contractCode,preservationCode,preservationProject);
            return;
        }
        //2. 一些特殊的保全
        if(EnumWhaleCorrectProject.personalPreservationV1(preservationProject)){
            correctEvent(contractCode,preservationCode,preservationProject);
            return;
        }
        //3. 个险新契约流程
        handPolicyEvent(contractCode, eventType);
    }

    /**
     * 小鲸-保单事件变更
     *
     * @param contractCode
     * @param preservationCode
     * @param preservationCode
     */
    public void correctEvent(String contractCode,String preservationCode,String preservationProject) {

        log.info("小鲸数据回传:{}-{}", contractCode, preservationCode);
        if (StringUtils.isBlank(contractCode)) {
            log.error("小鲸回调通知保单唯一编码为空{}", contractCode);
            return;
        }

        if (StringUtils.isBlank(preservationCode)) {
            log.error("小鲸回调通知保全唯一编码为空{}", preservationCode);
            return;
        }

        String lockKey = String.format("xj:callback:%s", preservationCode);
        boolean lockSuccess = tokenService.lockBusinessToken(lockKey, 10L);
        if (!lockSuccess) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501009);
        }

        try {
            SmOrderWhaleCorrectNotify existed = smOrderWhaleCorrectNotifyMapper.selectByContractCodeAndPreservationCode(preservationCode);
            //只有成功了的才不处理
            if (EnumWhaleCorrectProject.APPLICANT_INFO_CHANGE.getCode().equals(preservationProject)
                    ||EnumWhaleCorrectProject.COMMISSION_RATIO_UP.getCode().equals(preservationProject)) {
                if (Objects.nonNull(existed)) {
                    if (Objects.nonNull(existed.getStatus()) && existed.getStatus() == 1) {
                        log.warn("重复推送的保全事件{}-{}", contractCode, preservationCode);
                        return;
                    } else {
                        existed.setStatus(0);
                        existed.setUpdateTime(LocalDateTime.now());
                        existed.setRemark("");
                        smOrderWhaleCorrectNotifyMapper.updateByPrimaryKeySelective(existed);
                    }
                } else {
                    SmOrderWhaleCorrectNotify orderWhaleCorrectNotify = new SmOrderWhaleCorrectNotify();
                    orderWhaleCorrectNotify.setContractCode(contractCode);
                    orderWhaleCorrectNotify.setPreservationCode(preservationCode);
                    orderWhaleCorrectNotify.setStatus(0);
                    smOrderWhaleCorrectNotifyMapper.insert(orderWhaleCorrectNotify);
                }
            } else {
                if (Objects.nonNull(existed)) {
                    log.warn("重复推送的保全事件{}-{}", contractCode, preservationCode);
                    return;
                }
                SmOrderWhaleCorrectNotify orderWhaleCorrectNotify = new SmOrderWhaleCorrectNotify();
                orderWhaleCorrectNotify.setContractCode(contractCode);
                orderWhaleCorrectNotify.setPreservationCode(preservationCode);
                orderWhaleCorrectNotify.setStatus(0);
                smOrderWhaleCorrectNotifyMapper.insert(orderWhaleCorrectNotify);
            }
            CorrectManagerService correctManagerService = SpringFactoryUtil.getBean(CorrectManagerService.class);
            correctManagerService.execute(preservationProject, preservationCode);
        } finally {
            tokenService.unlockBusinessToken(lockKey);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void transactional(Supplier<T> supplier) {
        supplier.get();
    }


    /**
     * 保单信息
     *
     * @param vo
     * @param extend
     * @return
     */
    public SmOrderPolicy cvtOrderPolicy(WhaleContract vo, SmOrderExtendWhale extend) {

        SmOrderPolicy smOrderPolicy = new SmOrderPolicy();
        smOrderPolicy.setFhOrderId(extend.getFhOrderId());
        smOrderPolicy.setChannel(EnumChannel.XJ.getCode());
        smOrderPolicy.setPolicyNo(vo.getContractBaseInfo().getPolicyNo());
        smOrderPolicy.setThirdPolicyNo(vo.getContractBaseInfo().getThirdPolicyNo());
        smOrderPolicy.setPayType(EnumWhalePayPeriodType.valueOfCode(extend.getPaymentPeriodType()).getInsCode());
        smOrderPolicy.setPayPeriod(extend.getPaymentPeriod());

        smOrderPolicy.setPolicyState(EnumWhalePolicyStatus.cvtInsCode(vo.getContractBaseInfo().getPolicyStatus()));

        ContractExtendInfo contractExtendInfo = vo.getContractExtendInfo();
        if (StringUtils.isNotBlank(contractExtendInfo.getSurrenderTime())) {
            LocalDateTime parse = LocalDateTime.parse(contractExtendInfo.getSurrenderTime(), BaseConstants.FMT_DATETIME);
            smOrderPolicy.setSurrenderValidTime(parse.toLocalDate());
            smOrderPolicy.setSurrenderReason(contractExtendInfo.getSurrenderReason());
            smOrderPolicy.setSurrenderTime(LocalDateTime.now());
            smOrderPolicy.setCancelAmount(contractExtendInfo.getSurrenderAmount());
        }


        smOrderPolicy.setAmount(vo.sumAmount());
        smOrderPolicy.setPremium(vo.sumPremium());
        ContractExtendInfo extendInfo = vo.getContractExtendInfo();
        smOrderPolicy.setVisitWay(EnumWhaleVisitWay.cvtInsCode(extendInfo.getRevisitType()));
        smOrderPolicy.setVisitTime(extendInfo.getRevisitTime());

        EnumWhalePolicyStatus by = EnumWhalePolicyStatus.findBy(vo.getContractBaseInfo().getPolicyStatus());
        if (Objects.nonNull(by) && StringUtils.isNotBlank(by.getSurrenderType())) {
            smOrderPolicy.setSurrenderType(by.getSurrenderType());
        }
        //设置返回结果
        if (Objects.nonNull(extendInfo.getRevisitTime())) {
            smOrderPolicy.setVisitStatus(Objects.equals(extendInfo.getRevisitResult(), 1) ?
                    OrderVisitResultEnum.SUCCESS.getCode() :
                    OrderVisitResultEnum.FAILED.getCode());
        } else {
            smOrderPolicy.setVisitStatus(OrderVisitResultEnum.UNDO.getCode());
        }
        vo.getProductInfoList()
                .stream()
                .filter(ProductInfoList::isMain)
                .findFirst()
                .ifPresent(ygPro -> {
                    smOrderPolicy.setValidPeriod(ygPro.getInsuredPeriod() + "");
                    smOrderPolicy.setValidUnit(EnumWhaleInsuredPeriodType.valueOfCode(ygPro.getInsuredPeriodType()).getInsCode());
                    smOrderPolicy.setPayType(EnumWhalePayType.valueOfCode(ygPro.getPeriodType()).getInsCode());

                });

        Map<String, Object> extendMap = new HashMap<>();
        extendMap.put("receiptSignTime", vo.getContractExtendInfo().getReceiptSignTime());
        smOrderPolicy.setExtend(JSON.toJSONString(extendMap));
        return smOrderPolicy;
    }

    /**
     * ⭐️只能处理实收数据
     * 1.退保状态的保单，不做处理
     * 2、断保状态的保单，暂不处理
     * 3、如果农保查不到险种，则按照整单层实收
     * 4、历史数据需要补推
     * @param data
     */
    public List<RenewalTermNotifyResult> handleRenewalTermPolicy(List<RenewalTermNotifyVo> data) {
        List<String> policyNoList = data.stream().map(RenewalTermNotifyVo::getPolicyNo).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(policyNoList)){
            log.warn("WhaleOrderService.handleRenewalTermPolicy 保单列表为空，请核对参数：{}", JSON.toJSONString(data));
            return Collections.emptyList();
        }
        //1. 查询农保续期数据并校验状态
        List<FastOrderPolicy> policyList = smOrderPolicyMapper.queryByPolicyList(policyNoList);
        List<FastRenewalTermOrder> chongHoRenewalTermList = renewalTermMapper.queryByPolicyList(policyNoList);

//        List<SmOrderRiskDuty> riskDutyList = riskDutyMapper.listByPolicyNoList(policyNoList);
//        Map<String, SmOrderRiskDuty> riskDutyMap = LambdaUtils.safeToMap(riskDutyList, a -> {
//            return a.getPolicyNo() + a.getInsuredIdNumber() + a.getRiskCode();
//        });

        List<String> orderIdList = policyList.stream().map(FastOrderPolicy::getFhOrderId).collect(Collectors.toList());

        List<SmOrderRenewalTermRisk> renewalTermRiskList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orderIdList)){
            renewalTermRiskList = renewalTermRiskDutyMapper.queryRenewalTermList(orderIdList,SmConstants.POLICY_STATUS_SUCCESS);
        }

        //1.1 农保续期险种数据
        Map<String, SmOrderRenewalTermRisk> riskDutyMap = new HashMap<>();
        Set<String> riskSet = new HashSet<>();

        for(SmOrderRenewalTermRisk risk:renewalTermRiskList){
            String idNumber = risk.getInsuredIdNumber();
            if(StringUtils.isNotBlank(idNumber)){
                idNumber = idNumber.toUpperCase();
            }
            String key = risk.getOrderId() + idNumber + risk.getRiskCode();
            riskDutyMap.put(key,risk);
            riskSet.add(risk.getOrderId());

        }

        Map<String, FastOrderPolicy> policyMap = LambdaUtils.safeToMap(policyList, FastOrderPolicy::getPolicyNo);
        Map<String, FastRenewalTermOrder> chongHoRenewalTermMap = LambdaUtils.safeToMap(chongHoRenewalTermList, order -> {
            return order.getPolicyNo() + order.getTermNum();
        });

        List<SmOrderRenewalTerm> renewalTermEntityList = new ArrayList<>(data.size());

        List<SmOrderRenewalTermRisk> renewalTermRiskDutyList = new ArrayList<>();
        List<RenewalTermNotifyResult> notifyList = new ArrayList<>(data.size());
        RenewalTermNotifyResult result = null;
        //2.开始操作农保续期实收数据
        for (RenewalTermNotifyVo entry : data) {
            result = RenewalTermNotifyResult.ok(entry);
            notifyList.add(result);

            String policyNo = entry.getPolicyNo();
            Integer period = entry.getPeriod();

            //2.1 农保是否已实收
            if (!Objects.equals(entry.getStatus(), 1)) {
                result.lose("该保单未实收，不能处理实收流程");
                continue;
            }

            //2.2 校验保单原始数据，由于农保的待续期数据生成规则不一致，如果农保未生成待续期数据，则在实收时自动生成实收数据
            FastOrderPolicy policy = policyMap.get(policyNo);
            if (policy == null) {
                result.lose("该保单号不存在");
                continue;
            }
            String key = policyNo + period;
            FastRenewalTermOrder order = chongHoRenewalTermMap.get(key);

            if (order ==null) {
                result.lose("该保单当前期次不存在");
                continue;
            }
            if ( Objects.equals(order.getRenewalStatus(), 1)) {
                result.lose("保单当前期次已续保");
                continue;
            }
            //2.3 需要判断是否满足续期数据生成条件，这块跟佣金的生成规则有关联，不能盲目生成待续期数据
            if (!renewalTermCondition(policy)) {
                result.lose("该保单不满足续期条件");
                continue;
            }

            //2.4 如果农保的保单无续期险种数据，则实收整单层即可(工单：16579)
            if(!riskSet.contains(policy.getFhOrderId())){
                SmOrderRenewalTerm entity = convertRenewalTermEntity(policy, entry);
                renewalTermEntityList.add(entity);
                continue;
            }

            //2.5 按险种层实收，需要严格校验险种保费
            boolean r = true;
            List<RenewalTermProductNotifyVo> productList = entry.getProductList();
            if(productList==null){
                result.lose("该实收保单无险种数据，请检查险种层数据");
                continue;
            }
            for (RenewalTermProductNotifyVo product : productList) {
                String idNumber = product.getInsuredIdCard();
                if(StringUtils.isNotBlank(idNumber)){
                    idNumber=idNumber.toUpperCase();
                }
                String k = policy.getFhOrderId() + idNumber + product.getProductCode();
                SmOrderRenewalTermRisk duty = riskDutyMap.get(k);
                if (Objects.isNull(duty)) {
                    result.lose("保单的险种不存在:{0},{1}", entry, product.getProductCode());
                    r=false;
                    break;
                }
                String appStatus = cvt2AppStatus(product.getProductStatus());
                if (!Objects.equals(duty.getAppStatus(), appStatus)) {
                    result.lose("保单险种状态不一致:{0},{1}", entry, product.getProductCode());
                    r=false;
                    break;
                }
            }

            if (r) {
                SmOrderRenewalTerm entity = convertRenewalTermEntity(policy, entry);
                List<SmOrderRenewalTermRisk> rdl = convertRenewalTermProductEntity(policy, entry.getProductList());
                renewalTermEntityList.add(entity);
                renewalTermRiskDutyList.addAll(rdl);
            }
        }
        log.info("续期实收数据同步-开始更新续期实收数据:{}", JSON.toJSONString(renewalTermEntityList));
        if (!CollectionUtils.isEmpty(renewalTermEntityList)) {
            txServiceManager.excute(()->{
                int r1 = renewalTermMapper.batchInsertRenewalTermList(renewalTermEntityList);
                int r2 = 0;
                if(CollectionUtils.isNotEmpty(renewalTermRiskDutyList)) {
                    r2 = renewalTermRiskDutyMapper.batchUpdate(renewalTermRiskDutyList);
                }
                log.info("续期数据同步成功：{},{}", r1, r2);
            });

            List<RenewalTermDTO> eventList = convert2RenewalTermEvent(renewalTermEntityList);
            OrderRenewCommissionBatchEvent event = new OrderRenewCommissionBatchEvent(eventList);

            log.info("续期实收数据同步-开始推送佣金事件:{}", event);
            eventBusEngine.publish(event);

        }
        return notifyList;
    }

    private List<SmOrderRenewalTermRisk> convertRenewalTermProductEntity(FastOrderPolicy policy, List<RenewalTermProductNotifyVo> productList) {
        return productList.stream().map(product -> {
            SmOrderRenewalTermRisk duty = new SmOrderRenewalTermRisk();
            duty.setOrderId(policy.getFhOrderId());
            duty.setTermNum(product.getPeriod());
            duty.setInsuredIdNumber(product.getInsuredIdCard());
            duty.setPremium(product.getPremium());
            duty.setDuePremium(product.getDuePremium());
            duty.setRiskCode(product.getProductCode());
            return duty;
        }).collect(Collectors.toList());
    }

    private String cvt2AppStatus(String whalePolicyStatus) {
        if (EnumWhalePolicyStatus.offline(whalePolicyStatus)) {
            return SmConstants.POLICY_STATUS_CANCEL_SUCCESS;
        }
        return SmConstants.POLICY_STATUS_SUCCESS;
    }

    private List<RenewalTermDTO> convert2RenewalTermEvent(List<SmOrderRenewalTerm> renewalTermList) {
        List<RenewalTermDTO> data = new ArrayList<>(renewalTermList.size());
        for (SmOrderRenewalTerm entry : renewalTermList) {
            RenewalTermDTO dto = new RenewalTermDTO();
            BeanUtils.copyProperties(entry, dto);
            data.add(dto);
        }
        return data;
    }

    private SmOrderRenewalTerm convertRenewalTermEntity(FastOrderPolicy order, RenewalTermNotifyVo vo) {
        SmOrderRenewalTerm entity = new SmOrderRenewalTerm();
        entity.setOrderId(order.getFhOrderId());
        entity.setPolicyNo(order.getPolicyNo());
        entity.setChannel(order.getChannel());
        entity.setOrderAmount(order.getOrderAmount());
        entity.setTermNum(vo.getPeriod());
        entity.setTotalTerm(vo.getTotalPeriod());
        entity.setRenewalAmount(vo.getPaymentAmount());
        entity.setDueTime(vo.getDuePaymentTime());
        entity.setGraceDays(vo.getGraceDay());
        entity.setPaymentTime(vo.getPaymentTime());
        entity.setRenewalStatus(vo.getStatus());
        //⚠️⚠️⚠️续期保单实收时需要取当前保单的管护人⚠️⚠️⚠️
        entity.setCustomerAdminId(order.getCustomerAdminId());
        //⚠️⚠️⚠️按李雪的要求，续期实收同步时间调整成小鲸系统实际的实收操作时间，需要严格保证当日在小鲸实收的数据已经同步到农保系统⚠️⚠️⚠️
        Date paymentSubmitTime = vo.getPaymentSubmitTime();
        if(paymentSubmitTime!=null){
            entity.setRenewalSuccessSyncDate(paymentSubmitTime);
        }else{
            entity.setRenewalSuccessSyncDate(new Date());
        }
        entity.setCreateBy(vo.getOperator());
        entity.setUpdateBy(vo.getOperator());
        return entity;
    }

    /**
     * 缴费以年为单位 长险 未过订单期限 未产生已经续期期数的数据
     *
     * @param order
     * @return
     */
    public boolean renewalTermCondition(FastOrderPolicy order) {
        BigDecimal period = StringTools.convertNumber(order.getPayPeriod());
        BigDecimal one = new BigDecimal(1);
        if (Objects.isNull(period)) {
            return false;
        }

        if (CompareTools.safeCompare(period, one) <= 0) {
            return false;
        }
        if (!Objects.equals(order.getAppStatus(), SmConstants.POLICY_STATUS_SUCCESS)) {
            return false;
        }
        if (!Objects.equals(order.getLongInsurance(), 0)) {
            return false;
        }
        return true;
    }


    /**
     * 团险新契约事件
     *
     * @param contractCode      合同编号
     * @param policyProductType 个团车财类型
     * @param policyOperation   操作类型
     * @param preservationCode  保全编号
     */
    public void handGroupPolicyChange(String contractCode, String policyProductType, String policyOperation, String preservationCode) {
        // MODIFY
        if(Objects.equals(policyOperation, EnumWhalePolicyEvent.MODIFY.name())) {
            //1. 保单保全事件
            if(StringUtils.isNotBlank(preservationCode)) {
                List<SmOrderWhaleNotify> smOrderWhaleNotify = smOrderWhaleNotifyService.getByContractAndPreservationCode(contractCode, policyOperation, preservationCode);
                if (CollectionUtils.isEmpty(smOrderWhaleNotify)) {
                    smOrderWhaleNotifyService.addOrderWhaleNotifyRecord(contractCode, policyProductType, policyOperation, preservationCode, null);
                }
                return;
            }
            //2. 保单信息变更事件(同一个保单支持多类相同事件):保单推荐人信息变更，保单回执回访信息变更......
            saveCommonEvent(contractCode, policyProductType);
        } else {
            // 包含新契约事件，保单电子保单事件(同一个保单不能重复推送)
            List<SmOrderWhaleNotify> smOrderWhaleNotify = smOrderWhaleNotifyService.getByContractAndPreservationCode(contractCode, policyOperation, preservationCode);
            if (CollectionUtils.isEmpty(smOrderWhaleNotify)) {
                smOrderWhaleNotifyService.addOrderWhaleNotifyRecord(contractCode, policyProductType, policyOperation, preservationCode, null);
            }
        }
    }

    /**
     * 保单通用事件
     * 适用于保单推荐人变更
     * @param contractCode      合同编号
     */
    public void saveCommonEvent(String contractCode,String policyProductType) {
        smOrderWhaleNotifyService.commonChangeEvent(contractCode, policyProductType, EnumWhalePolicyEvent.COMMON_CHANGE.name());
    }

    private WhaleContract getGroupPolicyByContractCode(String contractCode) {
        WhaleResp<WhaleContract> resp = adapter.getGroupPolicy(contractCode);
        return resp.isSuccess() ? resp.getData() : null;
    }

    /**
     * 承保-电子保单通知事件
     *
     * @param notify
     */
    public void applyGroupEvent(SmOrderWhaleNotify notify) {
        String operator = notify.getPolicyOperation();
        EnumWhalePolicyEvent event = EnumWhalePolicyEvent.encode(operator);
        switch (event) {
            case E_POLICY_URL_NOTIFY:
                applyPolicyUrlEvent(notify);
                break;
            case CREATE:
                doWhaleGroupOrder(notify);
                break;
            default:
                log.error("暂不支持受理小鲸该事件，请确认数据是否正确:{}", notify);
                break;
        }
    }


    /**
     * TODO:承保-电子保单通知事件
     *
     * @param notify
     */
    public void applyPolicyUrlEvent(SmOrderWhaleNotify notify) {
        WhaleResp<WhaleContract> resp = adapter.getGroupPolicy(notify.getContractCode());
        //1. 校验小鲸返回报文是否正确
        if (!resp.isSuccess()) {
            String err = resp.getMsg();
            err = StringUtils.isBlank(err) ? "小鲸内部服务异常" : err;
            throw new MSBizNormalException(ExcptEnum.WHALE_COMMON_SERVER_ERROR.getCode(), err);
        }

        WhaleContract contract = resp.getData();
        if (contract == null || CollectionUtils.isEmpty(contract.getGroupInsuredInfoList())) {
            throw new MSBizNormalException(ExcptEnum.WHALE_COMMON_SERVER_ERROR.getCode(), "保单信息为空，无法受理该事件");
        }
        int ret = smOrderWhaleNotifyService.updateOrderGroupStatus(notify.getId(),notify.getStatus(), GroupNotify.StatusEnum.DOING.getCode());
        log.warn("状态更新为处理中:{}", ret);
        if(ret==0){
            log.warn("合同编号{}的记录id={}状态已变更",notify.getContractCode(),notify.getId());
            return ;
        }
        //2. 开始更新农保系统-新契约的电子保单地址
        String policyNo = contract.getContractBaseInfo().getPolicyNo();
        List<GroupInsuredInfo> insuredInfoList = contract.getGroupInsuredInfoList();
        String orderId = orderMapper.getFhOrderIdByPolicyNo(policyNo);

        List<SmOrderInsured> orderInsuredList = insuredInfoList.stream()
                .filter(entry -> StringUtils.isNotBlank(entry.getInsuredPolicyUrl()))
                .map(entry -> {
                    SmOrderInsured insured = new SmOrderInsured();
                    insured.setFhOrderId(orderId);
                    insured.setIdNumber(entry.getInsuredIdCard());
                    insured.setDownloadUrl(entry.getInsuredPolicyUrl());
                    return insured;
                }).collect(Collectors.toList());

        int r = 0;
        if (CollectionUtils.isNotEmpty(orderInsuredList)) {
            r = insuredMapper.batchUpdateEPolicyUrl(orderInsuredList);
        }
        log.info("新契约电子保单更新完成:{},{}", orderId, r);
        notify.setThPolicyNo(policyNo);
        notify.setPolicyNo(policyNo);
        smOrderWhaleNotifyService.updateSuccessDoneStatus(notify);
    }


    /**
     * 团险-承保事件
     *
     * @param notify
     */
    public void doWhaleGroupOrder(SmOrderWhaleNotify notify) {
        WhaleContract contract;
        if (Objects.equals(notify.getOpMethod(), GroupNotify.OpMethodEnum.API.getCode())) {
            WhaleResp<WhaleContract> resp = adapter.getGroupPolicyByBusinessScenario(notify.getContractCode()
                    , EnumChannelBusinessScenario.SYN_WHALE_POLICY.getCode());
            contract = resp.getData();
            smOrderWhaleNotifyService.updateOrderGroupNotifyContent(notify, JSON.toJSONString(contract));
        } else {
            SmOrderWhaleNotifyMsg notifyMsg = smOrderWhaleNotifyService.getNotifyMsg(notify.getId());
            if (notifyMsg == null || StringUtils.isBlank(notifyMsg.getNotifyContent())) {
                return;
            }
            contract = JSON.parseObject(notifyMsg.getNotifyContent(), WhaleContract.class);
        }
        //验证保单号
        validGroupRepeat(contract);

        int ret = smOrderWhaleNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DOING.getCode());
        log.warn("状态更新为处理中:{}", ret);
        if (ret == 0) {
            log.warn("合同编号{}的记录id={}状态已变更", notify.getContractCode(), notify.getId());
            return;
        }
        String fhOrderId = orderNoGenerator.getNextNo(EnumChannel.XJ.getCode());
        List<SmCreateOrderSubmitRequest> requestList = adapter.cvtByGroupNotify(contract, fhOrderId);
        OrderConvertor.mapperRecommendInfoList(requestList);

        orderCoreService.batchSaveWhaleOrderInfoGroup(requestList);
        //异步执行提成 客户提取等操作
        eventBusEngine.publish(new OrderImportEvent(requestList, Boolean.TRUE));

        notify.setOriginalOrderId(fhOrderId);
        notify.setThPolicyNo(contract.getContractBaseInfo().getPolicyNo());
        notify.setPolicyNo(notify.getThPolicyNo());
        smOrderWhaleNotifyService.updateSuccessDoneStatus(notify);

    }

    public void baseInfoChange(SmOrderWhaleNotify notify) {
        WhaleResp<WhaleContract> resp = adapter.getGroupPolicyByBusinessScenario(notify.getContractCode()
                    , EnumChannelBusinessScenario.SYN_WHALE_POLICY.getCode());
        WhaleContract  contract = resp.getData();
        int ret = smOrderWhaleNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DOING.getCode());
        log.warn("状态更新为处理中:{}", ret);
        if (ret == 0) {
            log.warn("合同编号{}的记录id={}状态已变更", notify.getContractCode(), notify.getId());
            return;
        }
        try {
            String policyNo = contract.getContractBaseInfo().getPolicyNo();
            if (StringUtils.isBlank(policyNo)) {
                log.warn("保单号为空，事件信息不完整:{}", notify.getId());
                return;
            }
            List<SmOrderInsured> orderInsuredList = insuredMapper.selectByPolicyNo(policyNo);
            if (CollectionUtils.isEmpty(orderInsuredList)) {
                log.warn("农保系统不存在该保单:{}", policyNo);
                return;
            }
            //开始对比保单的管护人和推荐人
            ChannelInfo channelInfo = contract.getChannelInfo();
            String whaleCustomerAdminId = channelInfo.getReferrerWno();
            String whaleRecommendId = channelInfo.getCustomerManagerChannelCode();
            SmOrderInsured luckMan = orderInsuredList.get(0);
            String orderId = luckMan.getFhOrderId();
            SmBaseOrderVO order = orderMapper.getBaseOrderInfoByOrderId(orderId);
            if (order == null) {
                log.warn("农保系统订单不存在，请检查数据是否完整:{}", orderId);
                return;
            }
            boolean commissionChange = StringUtils.isNotBlank(whaleRecommendId) && !Objects.equals(order.getRecommendId(), whaleRecommendId);
            txServiceManager.excute(() -> {
                //修改推荐人信息F
                if (StringUtils.isNotBlank(whaleRecommendId)) {
                    if (!Objects.equals(order.getRecommendId(), whaleRecommendId)) {
                        log.info("订单批改推荐人，订单号{}，原推荐人{}，变更后推荐人{}", orderId, order.getRecommendId(), whaleRecommendId);
                        manageService.updateOrderRecommendByOrderId(orderId, order.getRecommendId(), whaleRecommendId, true);
                    }
                }
                //修改推荐人信息F
                if (StringUtils.isNotBlank(whaleCustomerAdminId)) {
                    if (!Objects.equals(order.getCustomerAdminId(), whaleCustomerAdminId)) {
                        log.info("订单批改管护人，订单号{}，原管护人{}，变更后管护人{}", orderId, order.getCustomerAdminId(), whaleCustomerAdminId);
                        manageService.updateOrderCustomerManagerByOrderId(orderId, order.getCustomerAdminId(), whaleCustomerAdminId, true);
                    }
                }
            });
            if (commissionChange) {
                busEngine.publish(new OrderCommissionChangeEvent(orderId));
            }
            smOrderWhaleNotifyService.updateSuccessDoneStatus(notify);
        }
        catch (MSBizNormalException e){
            log.warn("保单推荐人变更失败",e);
            notify.setRemark(e.getMessage());
            smOrderWhaleNotifyService.updateFailStatus(notify);
        }
        catch (Exception e){
            log.warn("保单推荐人变更失败", e);
            notify.setRemark("保单推荐人变更失败");
            smOrderWhaleNotifyService.updateFailStatus(notify);
        }
    }

    /**
     * 批改单电子保单事件
     *
     * @param notify
     * @throws Exception
     */
    public void correctGroupEvent(SmOrderWhaleNotify notify) throws Exception {
        String operator = notify.getPolicyOperation();
        EnumWhalePolicyEvent event = EnumWhalePolicyEvent.encode(operator);
        switch (event) {
            //批改电子保单事件
            case E_POLICY_URL_NOTIFY:
                correctPolicyUrlEvent(notify);
                break;
            case MODIFY:
                doWhaleGroupEndorsement(notify);
                break;
            //批改事件
            default:
                log.error("暂不支持受理小鲸该事件，请确认数据是否正确:{}", notify);
                break;
        }

    }


    /**
     * 批改单电子保单事件
     *
     * @param notify
     * @throws Exception
     */
    public void correctPolicyUrlEvent(SmOrderWhaleNotify notify) {
        WhaleResp<PreservationDetail> resp = adapter.getPreservationDetail(notify.getPreservationCode());
        //1. 校验小鲸返回报文是否正确
        if (!resp.isSuccess()) {
            String err = resp.getMsg();
            err = StringUtils.isBlank(err) ? "小鲸内部服务异常" : err;
            throw new MSBizNormalException(ExcptEnum.WHALE_COMMON_SERVER_ERROR.getCode(), err);
        }

        PreservationDetail preservationDetail = resp.getData();
        if (preservationDetail == null || CollectionUtils.isEmpty(preservationDetail.getAddOrSubtractList())) {
            throw new MSBizNormalException(ExcptEnum.WHALE_COMMON_SERVER_ERROR.getCode(), "保单信息为空，无法受理该事件");
        }

        int ret = smOrderWhaleNotifyService.updateOrderGroupStatus(notify.getId(),notify.getStatus(), GroupNotify.StatusEnum.DOING.getCode());
        log.warn("状态更新为处理中:{}", ret);
        if(ret==0){
            log.warn("合同编号{}的记录id={}状态已变更",notify.getContractCode(),notify.getId());
            return ;
        }
        //2. 开始更新农保系统-新契约的电子保单地址
        String policyNo = preservationDetail.getPolicyCode();
        String endorsementNo = preservationDetail.getEndorsementNo();
        List<SmOrderInsured> insuredList = insuredMapper.listByEndorsementNo(policyNo, endorsementNo);
        if (CollectionUtils.isEmpty(insuredList)) {
            throw new MSBizNormalException(ExcptEnum.WHALE_COMMON_SERVER_ERROR.getCode(), "被保人列表为空");
        }
        List<PreservationAddOrSubtract> insuredInfoList = preservationDetail.getAddOrSubtractList();
        Map<String, PreservationAddOrSubtract> insuredMap = LambdaUtils.safeToMap(insuredInfoList, insured -> {
            return insured.getInsuredIdCard().toUpperCase();
        });

        List<SmOrderInsured> orderInsuredList = insuredList.stream()
                .map(entry -> {
                    String idCard = entry.getIdNumber().toUpperCase();
                    PreservationAddOrSubtract member = insuredMap.get(idCard);
                    if (member != null) {
                        entry.setDownloadUrl(member.getInsuredPolicyUrl());
                    }
                    return entry;
                })
                .filter(entry -> StringUtils.isNotBlank(entry.getDownloadUrl()))
                .collect(Collectors.toList());
        int r = 0;
        if(CollectionUtils.isNotEmpty(orderInsuredList)){
            r = insuredMapper.batchUpdateEPolicyUrl(orderInsuredList);
        }
        log.info("批改电子保单更新完成:{},{}", endorsementNo, r);
        notify.setThPolicyNo(policyNo);
        notify.setPolicyNo(policyNo);
        smOrderWhaleNotifyService.updateSuccessDoneStatus(notify);

    }

    /**
     * 小鲸-团险保单批改事件
     *
     * @param notify
     * @throws Exception
     */
    public void doWhaleGroupEndorsement(SmOrderWhaleNotify notify) throws Exception {
        PreservationDetail preservation;
        if (Objects.equals(notify.getOpMethod(), GroupNotify.OpMethodEnum.API.getCode())) {
            WhaleResp<PreservationDetail> resp = adapter.getPreservationDetail(notify.getPreservationCode());
            preservation = resp.getData();
            smOrderWhaleNotifyService.updateOrderGroupNotifyContent(notify, JSON.toJSONString(preservation));
        } else {
            SmOrderWhaleNotifyMsg notifyMsg = smOrderWhaleNotifyService.getNotifyMsg(notify.getId());
            if (notifyMsg == null || StringUtils.isBlank(notifyMsg.getNotifyContent())) {
                return;
            }
            preservation = JSON.parseObject(notifyMsg.getNotifyContent(), PreservationDetail.class);
        }
        int ret = smOrderWhaleNotifyService.updateOrderGroupStatus(notify.getId(), notify.getStatus(), GroupNotify.StatusEnum.DOING.getCode());
        log.warn("状态更新为处理中:{}", ret);

        if (ret == 0) {
            log.warn("合同编号{}的记录id={}状态已变更", notify.getContractCode(), notify.getId());
            return;
        }
        BusinessCheckVo r = adapter.validCorrectFlow(preservation);
        if(!r.isOk()){
            log.info("保全流程校验失败:{}",r.getErrMsg());
            throw new MSBizNormalException(String.valueOf(r.getCode()),r.getErrMsg());
        }

        List<SmCreateOrderSubmitRequest> requestList = convertOrderSubmitRequest(notify, preservation);

        OrderConvertor.mapperRecommendInfoList(requestList);
        orderCoreService.batchSaveWhaleOrderInfoGroup(requestList);
        //异步执行提成 客户提取等操作
        eventBusEngine.publish(new OrderImportEvent(requestList, Boolean.TRUE));
        smOrderWhaleNotifyService.updateSuccessDoneStatus(notify);

        //推送加佣保全事件
        PreservationDto preservationDto = new PreservationDto();
        preservationDto.setPreservationCode(preservation.getPreservationCode());
        preservationDto.setContractCode(preservation.getContractCode());
        preservationDto.setPolicyNo(preservation.getPolicyCode());
        preservationDto.setPreservationProject("POLICY:PRESERVATION:TYPE:POLICY_STATUS_CHANGE:GROUP_SURRENDER");
        log.info("团险退保-推送加佣保全事件:{}", JSON.toJSONString(preservationDto));
        operationFacade.doAddCommissionCorrect(preservationDto);
    }


    private List<SmCreateOrderSubmitRequest> convertOrderSubmitRequest(SmOrderWhaleNotify notify, PreservationDetail preservation) throws Exception {
        /**
         * 退保保全需要单独处理
         */
        String project = preservation.getPreservationProject();
        if (Objects.equals(project, EnumPreservationProject.SURRENDER.getCode())) {
            String policyNo = preservation.getPolicyCode();
            String orderId = orderMapper.getFhOrderIdByPolicyNo(policyNo);
            if (StringUtils.isBlank(orderId)) {
                throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "保单信息不存在:" + policyNo);
            }
            return adapter.cvtByGroupEndorsementNotify(orderId, policyNo, preservation);
        }

        //获取原单通知信息
        List<SmOrderWhaleNotify> originalOrderNotifyList = smOrderWhaleNotifyService.getByContractAndPreservationCode(preservation.getContractCode(), EnumWhalePolicyEvent.CREATE.name(), null);
        List<SmCreateOrderSubmitRequest> requestList;
        //原单不存在
        if (CollectionUtils.isEmpty(originalOrderNotifyList)
                && (preservation.getDataSource() == null ||
                (preservation.getDataSource() != 1 && preservation.getDataSource() != 2))) {
            WhaleContract contract = this.getGroupPolicyByContractCode(preservation.getContractCode());
            String fhOrderId = orderNoGenerator.getNextNo(EnumChannel.XJ.getCode());
            requestList = adapter.cvtByGroupEndorsementNotifyOriginalNotExist(fhOrderId, contract, preservation);
            notify.setOriginalOrderId(fhOrderId);
            notify.setThPolicyNo(contract.getContractBaseInfo().getPolicyNo());
            notify.setPolicyNo(notify.getThPolicyNo());
            return requestList;
        } else {
            //保单来源为1时特殊处理
            if (preservation.getDataSource() != null && preservation.getDataSource() == 1) {
                AssertUtills.isNotBlank(preservation.getPolicyCode(), "保单来源为1时，保全保单号不能为空");
                final String policyCode = preservation.getPolicyCode();
                //查询保单新契约订单号
                final List<SmOrderItem> smOrderItems = orderItemMapper.qryNewItemByPolicyNo(policyCode);
                AssertUtills.isTrue(CollectionUtils.isNotEmpty(smOrderItems), "保单来源为1时，订单项不存在");
                final List<String> fhOrderIds = smOrderItems
                        .stream()
                        .map(SmOrderItem::getFhOrderId)
                        .distinct()
                        .collect(Collectors.toList());
                AssertUtills.isTrue(CollectionUtils.isNotEmpty(fhOrderIds), "保单来源为1时，订单号不存在");
                AssertUtills.isTrue(fhOrderIds.size()==1, "保单来源为1时，订单号不唯一");

                requestList = adapter.cvtByGroupEndorsementNotify(fhOrderIds.get(0), policyCode, preservation);
                notify.setThPolicyNo(policyCode);
            }else {
                SmOrderWhaleNotify data = originalOrderNotifyList.get(0);
                requestList = adapter.cvtByGroupEndorsementNotify(data.getOriginalOrderId(), data.getPolicyNo(), preservation);
                notify.setThPolicyNo(data.getPolicyNo());
            }
            return requestList;
        }
    }


    private void validGroupRepeat(WhaleContract contract) {

        String policyNo = contract.getContractBaseInfo().getPolicyNo();
        if (StringUtils.isBlank(policyNo)) {
            log.error("小鲸通知保单号不存在{}", JSON.toJSONString(contract));
            throw new MSBizNormalException(ExcptEnum.XJ_POLICY_NOT_EXIST);
        }
        List<SmOrderInsured> list = insuredMapper.selectByPolicyNo(policyNo);

        if (!CollectionUtils.isEmpty(list)) {
            log.warn("小鲸推送重复数据{}", JSON.toJSONString(contract));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "数据已存在：" + policyNo);
        }
    }

    public Collection<GroupInsuredFixVo> listActiveInsured(String policyNo) {
        List<GroupInsuredFixVo> insuredList = insuredMapper.listGroupInsuredFix(policyNo);

        Map<String, GroupInsuredFixVo> insuredMap = new HashMap<>();
        for (GroupInsuredFixVo insured : insuredList) {
            String idNumber = insured.getIdNumber().toUpperCase();
            String appStatus = insured.getAppStatus();

            if (Objects.equals(SmConstants.POLICY_STATUS_SUCCESS, appStatus)) {
                insuredMap.put(idNumber, insured);
            } else if (Objects.equals(SmConstants.POLICY_STATUS_CANCEL_SUCCESS, appStatus)) {
                insuredMap.remove(idNumber);
            }
        }
        return insuredMap.values();
    }


    public String vehicleBusinessScore(String policyNo) {
        List<SmOrderInsured> orderInsuredList = insuredMapper.selectByPolicyNo(policyNo);
        if (CollectionUtils.isEmpty(orderInsuredList)) {
            return null;
        }
        String orderId = orderInsuredList.get(0).getFhOrderId();

        AutoOrderPolicy param = new AutoOrderPolicy();
        param.setOrderNo(orderId);
        List<AutoOrderPolicy> policyList = orderPolicyMapper.select(param);
        if (CollectionUtils.isEmpty(policyList)) {
            return null;
        }
        return policyList.get(0).getBusinessScore();
    }

    /**
     * 渠道推荐人信息查询
     * @param policyNoList
     * @return
     */
    public List<PolicyReferrerFixVo> policyReferrerInfo(List<String> policyNoList) {
        if(CollectionUtils.isEmpty(policyNoList)){
            return Collections.emptyList();
        }
        return orderMapper.queryReferrerInfo(policyNoList);
    }

    public List<PolicyReferrerFixVo> policyCorrectReferrerInfo(List<String> endorsementNoList) {
        if(CollectionUtils.isEmpty(endorsementNoList)){
            return Collections.emptyList();
        }
        return orderMapper.policyCorrectReferrerInfo(endorsementNoList);
    }

    public List<PolicySettlementTimeFixVo> policySettlementTime(List<String> data) {
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return orderMapper.policySettlementTime(data);
    }

    public List<PolicySettlementTimeFixVo> correctSettlementTime(List<String> data) {
        if(CollectionUtils.isEmpty(data)){
            return Collections.emptyList();
        }
        return orderMapper.correctSettlementTime(data);
    }

    /**
     * 部分订单由于推荐人离职，导致查询岗位信息为空，目前按产品的需求，该功能已修复(查询历史的岗位信息)
     * 历史数据需要修复
     * @param orderId 指定的订单信息
     */
    public void policyRecommendOrgFix(String orderId,Date startTime,Date endTime) {
        if(StringUtils.isBlank(orderId)&&(startTime==null||endTime==null)){
            log.info("参数条件不足");
            XxlJobHelper.log("参数条件不足");
            return;
        }
        log.info("开始处理");
        XxlJobHelper.log("开始处理");
        List<SmOrder> orderList = orderMapper.queryRecommendOrgEmptyList(orderId,startTime,endTime);
        if(CollectionUtils.isEmpty(orderList)){
            log.info("无处理数据");
            XxlJobHelper.log("无处理数据");
            return;
        }
        UserPostService userPostService = SpringFactoryUtil.getBean(UserPostService.class);
        int sum=0;
        for(SmOrder order:orderList){
            try {
                ++sum;
                String recommendId = order.getRecommendId();
                if(StringUtils.isBlank(recommendId)){
                    continue;
                }
                List<UserPost> postList = userPostService.listAllUserPostByJobNumber(recommendId);
                if (CollectionUtils.isEmpty(postList)) {
                    log.info("岗位为空");
                    XxlJobHelper.log("岗位为空");
                    continue;
                }
                Optional<UserPost> op = postList.stream().filter(p -> Objects.equals(order.getRecommendJobCode(), p.getJobCode())).findFirst();
                UserPost userPost = null;
                if (op.isPresent()) {
                    userPost = op.get();
                } else {
                    userPost = postList.get(0);
                }

                if(StringUtils.isBlank(order.getRecommendMasterName())) {
                    order.setRecommendMasterName(userPost.getUserMasterName());
                }
                if(StringUtils.isBlank(order.getRecommendAdminName())) {
                    order.setRecommendAdminName(userPost.getUserAdminName());
                }
                if(Objects.isNull(order.getRecommendEntryDate())) {
                    order.setRecommendEntryDate(LocalDateUtil.dateToLocaldatetime(userPost.getEntryDate()));
                }
                if(StringUtils.isBlank(order.getRecommendPostName())) {
                    order.setRecommendPostName(userPost.getPostName());
                }
                if(StringUtils.isBlank(order.getRecommendJobCode())) {
                    order.setRecommendJobCode(userPost.getJobCode());
                }
                if(StringUtils.isBlank(order.getRecommendMainJobNumber())) {
                    order.setRecommendMainJobNumber(userPost.getMainJobNumber());
                }
                if(StringUtils.isBlank(order.getRecommendOrgCode())) {
                    order.setRecommendOrgCode(userPost.getOrgCode());
                }
                if(StringUtils.isBlank(order.getCustomerAdminJobCode())) {
                    order.setCustomerAdminJobCode(userPost.getJobCode());
                }
                if(StringUtils.isBlank(order.getCustomerAdminMainJobNumber())) {
                    order.setCustomerAdminMainJobNumber(userPost.getMainJobNumber());
                }
                if(StringUtils.isBlank(order.getCustomerAdminOrgCode())) {
                    order.setCustomerAdminOrgCode(userPost.getOrgCode());
                }
                //更新相关数据
                log.info("岗位为空补偿更新{}",order);
                final int i= orderMapper.updateRecommendOrgEmptyList(order);
                log.info("更新成功：{},{},{},{}",order.getFhOrderId(),i,sum,orderList.size());
                XxlJobHelper.log("更新成功：{},{},{}",order.getFhOrderId(),sum,orderList.size());
            } catch (Exception e) {
                log.info("订单处理失败：{},{}",order.getFhOrderId(),e);
                XxlJobHelper.log("订单处理失败：{}",order.getFhOrderId());
            }
        }
    }

    public PreservationCheckResultVo checkBeforePreservation(ChannelRecommenderChangeVo param){
        PreservationCheckResultVo resultVo = new PreservationCheckResultVo();
        if(StringUtil.isBlank(param.getPolicyCode())){
            resultVo.setCode(PreservationCheckResultVo.NO_POLICY_NO);
            return resultVo;
        }
        String preservationProject = param.getPreservationProject();
        //1. 整单层-初始渠道推荐人变更
        if(Objects.equals(preservationProject,EnumPreservationProject.CUSTOMER_MANAGER_CHANGE.getCode())){
            CustomerManagerChangeCorrectService correctService = SpringFactoryUtil.getBean(CustomerManagerChangeCorrectService.class);
            return correctService.preCheck(param);
        }

        //2. 分单层-初始渠道推荐人变更
        if(Objects.equals(preservationProject,EnumPreservationProject.CHANGE_CHANNEL_REFERRER.getCode())){
            ChannelReferrerCorrectService correctService = SpringFactoryUtil.getBean(ChannelReferrerCorrectService.class);
            return correctService.preCheck(param);
        }

        return PreservationCheckResultVo.success();
    }

    public List<PreservationCheckResultVo> checkBeforePreservationBatch(List<ChannelRecommenderChangeVo> param){
        List<PreservationCheckResultVo> data = new ArrayList<>();
        Map<String,List<ChannelRecommenderChangeVo>> paramMap = LambdaUtils.groupBy(param,ChannelRecommenderChangeVo::getPreservationProject);
        for(Map.Entry<String,List<ChannelRecommenderChangeVo>> entry:paramMap.entrySet()){
            String project = entry.getKey();
            List<ChannelRecommenderChangeVo> recommenderList = entry.getValue();
            if(Objects.equals(project,EnumPreservationProject.CUSTOMER_MANAGER_CHANGE.getCode())){
                CustomerManagerChangeCorrectService correctService = SpringFactoryUtil.getBean(CustomerManagerChangeCorrectService.class);
                List<PreservationCheckResultVo> checkList = correctService.preCheck(recommenderList);
                data.addAll(checkList);
                continue;
            }
            if(Objects.equals(project,EnumPreservationProject.CHANGE_CHANNEL_REFERRER.getCode())){
                ChannelReferrerCorrectService correctService = SpringFactoryUtil.getBean(ChannelReferrerCorrectService.class);
                List<PreservationCheckResultVo> checkList = correctService.preCheck(recommenderList);
                data.addAll(checkList);
                continue;
            }
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(),String.format("未知的保全变更科目：%s",project));
        }
        return data;
    }

    /**
     * 保单批改流程校验
     * @param param 批改参数
     * @return 校验结果
     */
    public PreservationCheckResultVo checkCorrect(PolicyCorrectCheck param){
        PreservationCheckResultVo resultVo = new PreservationCheckResultVo();
        if(StringUtil.isBlank(param.getPolicyNo())){
            resultVo.setCode(PreservationCheckResultVo.NO_POLICY_NO);
            return resultVo;
        }
        String preservationProject = param.getPreservationProject();
        //1. 保单险种变更
        if(Objects.equals(preservationProject,EnumPreservationProject.CHANGE_PRODUCT.getCode())){
            PolicyProductCorrectService correctService = SpringFactoryUtil.getBean(PolicyProductCorrectService.class);
            return correctService.preCheck(param);
        }

        return PreservationCheckResultVo.success();
    }
//
//    private List<PreservationCheckResultDetail> checkResult(List<ChannelRecommenderChangeItemVo> itemList,List<OrderInsuredBaseInfoVo> insureds){
//        if(CollectionUtils.isEmpty(insureds)){
//
//        }
//        Map<String,List<OrderInsuredBaseInfoVo>> insuredMap = LambdaUtils.groupBy(insureds,OrderInsuredBaseInfoVo::getEndorsementNo);
//        Map<String,PreservationCheckResultDetail> resultDetailMap = Maps.newHashMap();
//        itemList.stream().forEach(o->{
//            log.info("ChannelRecommenderChangeItemVo={}",o);
//            List<ChannelRecommenderChangeItemDetailVo> itemDetailVos = o.getItemDetailVos();
//            //List<OrderInsuredBaseInfoVo> baseInfoVos = insuredMap.get(o.getEndorsementNo());
//            List<OrderInsuredBaseInfoVo> baseInfoVos = insuredMap.get(o.getEndorsementNo())!=null?insuredMap.get(o.getEndorsementNo()):Collections.EMPTY_LIST;
//            List<PreservationCheckResultDetail.PreservationLosePerson> losePersonList = Lists.newArrayList();
//            itemDetailVos.stream().forEach(p->{
//                PreservationCheckResultDetail.PreservationLosePerson person = null;
//                Optional<OrderInsuredBaseInfoVo> opt = baseInfoVos.stream().filter(m->Objects.equals(m.getIdNumber(),p.getInsuredIdNo())).findFirst();
//                if(opt.isPresent()){
//                    //农保的推荐人与小鲸推荐人不一致的被保人信息
//                    if(!Objects.equals(opt.get().getRecommendId(),p.getBeforeChannelRecommender())) {
//                        person = PreservationCheckResultDetail.PreservationLosePerson.builder()
//                                .idNumber(p.getInsuredIdNo())
//                                .insuredName(p.getInsuredIdName())
//                                .type(1).build();
//                    }
//                }else{
//                    //农保缺失
//                    person = PreservationCheckResultDetail.PreservationLosePerson.builder()
//                            .idNumber(p.getInsuredIdNo())
//                            .insuredName(p.getInsuredIdName())
//                            .type(2).build();
//                }
//                if(person!=null){
//                    losePersonList.add(person);
//                }
//            });
//            if(CollectionUtils.isNotEmpty(losePersonList)){
//                PreservationCheckResultDetail detail = new PreservationCheckResultDetail();
//                detail.setEndorsementNo(o.getEndorsementNo());
//                detail.setLosePersonList(losePersonList);
//                resultDetailMap.put(o.getEndorsementNo(),detail);
//            }
//        });
//        //找出农保有，小鲸缺失数据
//        insuredMap.forEach((k,v)->{
//            Optional<ChannelRecommenderChangeItemVo> opt = itemList.stream().filter(o->Objects.equals(k,o.getEndorsementNo())).findFirst();
//
//            if(opt.isPresent()){
//                v.stream().forEach(z->{
//                    Optional<ChannelRecommenderChangeItemDetailVo> itemDetailVoOpt = opt.get().getItemDetailVos().stream().filter(m->Objects.equals(z.getIdNumber(),m.getInsuredIdNo())).findFirst();
//                    if(!itemDetailVoOpt.isPresent()){
//                        PreservationCheckResultDetail.PreservationLosePerson person = PreservationCheckResultDetail.PreservationLosePerson.builder()
//                                .idNumber(z.getIdNumber())
//                                .insuredName(z.getInsuredName())
//                                .type(0).build();
//                        if(resultDetailMap.get(k)!=null){
//                            resultDetailMap.get(k).getLosePersonList().add(person);
//                        }else{
//                            PreservationCheckResultDetail resultDetail = new PreservationCheckResultDetail();
//                            List<PreservationCheckResultDetail.PreservationLosePerson> losePersonList = Lists.newArrayList();
//                            losePersonList.add(person);
//                            resultDetail.setEndorsementNo(k);
//                            resultDetail.setLosePersonList(losePersonList);
//                            resultDetailMap.put(k,resultDetail);
//                        }
//                    }
//                });
//            }else{
//                PreservationCheckResultDetail resultDetail = new PreservationCheckResultDetail();
//                List<PreservationCheckResultDetail.PreservationLosePerson> losePersonList = Lists.newArrayList();
//                resultDetail.setEndorsementNo(k);
//                resultDetail.setLosePersonList(losePersonList);
//                v.stream().forEach(z->{
//                    PreservationCheckResultDetail.PreservationLosePerson person = PreservationCheckResultDetail.PreservationLosePerson.builder()
//                            .idNumber(z.getIdNumber())
//                            .insuredName(z.getInsuredName())
//                            .type(0).build();
//                    losePersonList.add(person);
//                });
//                resultDetailMap.put(k,resultDetail);
//            }
//        });
//        return resultDetailMap.values().stream().collect(Collectors.toList());
//    }
}
