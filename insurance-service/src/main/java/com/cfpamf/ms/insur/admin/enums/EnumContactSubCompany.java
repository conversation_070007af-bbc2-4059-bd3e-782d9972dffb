package com.cfpamf.ms.insur.admin.enums;

import com.alibaba.druid.util.StringUtils;
import com.beust.jcommander.internal.Lists;
import com.cfpamf.ms.insur.base.util.CommonUtil;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 理赔资料需要邮寄地址 子公司编号
 * 暂时只有 中华财险：赤峰中心支公司 CIC-1504、包头中心支公司 CIC-1502
 * 2021年11月11日 增加  大家保险-中和大家保	湖南分公司	保单号不是2137开头	5月份以后保单是由湖南分公司承保
 * 深圳分公司	2137006362021A09GTB，保单2137开头	5月份以后保单是由深圳分公司承保
 * <AUTHOR>
 * @since 2020-04-21 15:00:23
 */
public enum EnumContactSubCompany {
    //中华财险 CIC-
    CIC1504("CIC-1504", "赤峰中心支公司"),
    CIC1502("CIC-1502", "包头中心支公司"),
    DJBX430("djbx-430", "湖南分公司"),
    DJBX4403("djbx-4403", "深圳分公司"),
    ;

    EnumContactSubCompany(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static EnumContactSubCompany getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Stream.of(values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }

    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        EnumContactSubCompany item = Stream.of(values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
        return item == null ? null : item.getName();
    }

    public static List<Map<String, String>> getByCodePrefix(String codePrefix) {
        List<Map<String, String>> list = new ArrayList<>(10);
        Stream.of(EnumContactSubCompany.values()).forEach(m -> {
            if (m.getCode().startsWith(String.format("%s-", codePrefix))) {
                list.add(CommonUtil.getMap(m.getCode(), m.getName()));
            }
        });
        return list;
    }

    public static List<Map<String, String>> getAllMap() {
        List<Map<String, String>> list = new ArrayList<>(EnumContactSubCompany.values().length);
        Stream.of(EnumContactSubCompany.values()).forEach(m -> list.add(CommonUtil.getMap(m.getCode(), m.getName())));
        return list;
    }

    public static String getCodeByPolicyNo(String companyIdentifier, String policyNo, LocalDateTime orderCreatTime) {
        if (StringUtils.isEmpty(companyIdentifier) || StringUtils.isEmpty(policyNo)) {
            return null;
        }
        if ("CIC".equals(companyIdentifier)) {
//            保单号：0120201504011819E100000005，保单号第七位起为1504，落地机构为赤峰；
//            保单号：01201915020800191400006033，保单号第七位起为1502，落地机构为包头；
            if (policyNo.length() < 11) {
                return null;
            }
            String suffix = policyNo.substring(6, 10);
            EnumContactSubCompany item = Stream.of(values()).filter(e -> e.getCode().equals(String.format("%s-%s", companyIdentifier, suffix))).findAny().orElse(null);
            return item == null ? null : item.getCode();
        }
        if("djbx".equals(companyIdentifier)){
            if(Objects.isNull(orderCreatTime)){
                return null;
            }
            //	保单号2137开头 且是五月份之后的保单 则又深圳分公司
            if(policyNo.startsWith("2137") && orderCreatTime.isAfter(LocalDateTime.of(2021,4,30,23,59,59))){
                return DJBX4403.getCode();
            }
            return DJBX430.getCode();
        }else {
            return null;
        }
    }

    /**
     * 获取支持的子公司的公司标识符集合
     * @return
     */
    public static List<String> getSupportCompanyIdentifier(){
        return Lists.newArrayList("CIC","djbx");
    }
}
