package com.cfpamf.ms.insur.base.advice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.ModuleVO;
import com.cfpamf.ms.insur.admin.pojo.dto.SystemLogDTO;
import com.cfpamf.ms.insur.admin.service.SystemLogService;
import com.cfpamf.ms.insur.base.annotation.AuthValidate;
import com.cfpamf.ms.insur.base.annotation.SystemLog;
import com.cfpamf.ms.insur.base.bean.CommonResult;
import com.cfpamf.ms.insur.base.constant.ApiPermsEnum;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Objects;

/**
 * api授权拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "author.filter.enable", havingValue = "true")
public class AuthInterceptor extends HandlerInterceptorAdapter {

    /**
     * 排除swagger-ui前端路径
     */
    private final static String SWAGGER_RESOURCE_URL = "/swagger-resources";

    /**
     * 排除swagger-ui后端路径
     */
    private final static String SWAGGER_API_URL = "/v2/api-docs";

    /**
     * 项目模式
     */
    @Value("${author.swagger.enable}")
    private boolean devMode;

    /**
     * 解析token工具类
     */
    @Autowired
    private JwtHelper jwtHelper;

    /**
     * 日志service
     */
    @Autowired
    private SystemLogService logService;

    /**
     * 验证authorization 是否用权限
     *
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 开发模式不启用权限控制
        if (devMode) {
            return true;
        }

        // swagger API文档 API接口不验证权限
        if (request.getRequestURI().startsWith(SWAGGER_RESOURCE_URL) || request.getRequestURI().startsWith(SWAGGER_API_URL)) {
            return true;
        }

        // 微信接口不需要权限
        if (request.getRequestURI().startsWith(BaseConstants.WX_VERSION)) {
            return true;
        }

        // app接口不需要权限
        if (request.getRequestURI().startsWith(BaseConstants.APP_VERSION)) {
            return true;
        }


        HandlerMethod handlerMethod = (HandlerMethod) handler;
        // 外部回调 不需要权限验证
        AuthValidate validate = handlerMethod.getMethodAnnotation(AuthValidate.class);
        if (validate == null || validate.value() == ApiPermsEnum.PUBLIC) {
            return true;
        }

        // 获取token信息
        String authorization = request.getHeader(BaseConstants.API_AUTH_NAME);
        if (StringUtil.isEmpty(authorization)) {
            authorization = request.getParameter(BaseConstants.API_AUTH_NAME);
        }

        // 接口权限验证
        CommonResult failResult = null;
        if (StringUtil.isNotEmpty(authorization)) {
            try {
                List<ModuleVO> hasPermissions = ThreadUserUtil.USER_MODULES_TL.get();
                String needModuleCode = validate.value().getAuthCode();
                // 当前登录人有接口权限
                if (hasPermissions.stream().anyMatch(p -> Objects.equals(p.getModuleCode(), needModuleCode))) {
                    return true;
                }
                failResult = CommonResult.failResult(new BizException(ExcptEnum.API_AUTH_ERROR));
            }
            // BMS权限验证失败
            catch (Exception e) {
                failResult = CommonResult.failResult(new BizException(ExcptEnum.API_AUTH_ERROR, e));
            }
        }
        // 没有authorization授权失效
        else {
            failResult = CommonResult.authFailResult();
        }

        // 保存未授权日志
        saveUnauthorizedLog(handlerMethod, request, authorization);
        // 返回授权失败
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json;charset=UTF-8");
        try (OutputStream os = response.getOutputStream()) {
            os.write(JSONObject.toJSONString(failResult).getBytes(Charset.defaultCharset()));
            os.flush();
        }
        return false;
    }

    /**
     * 保存未授权日志
     *
     * @param handlerMethod
     * @param request
     * @param authorization
     */
    private void saveUnauthorizedLog(HandlerMethod handlerMethod, HttpServletRequest request, String authorization) {
        String ip = request.getRemoteAddr();
        SystemLog systemLog = handlerMethod.getMethodAnnotation(SystemLog.class);
        String[] packages = handlerMethod.getBeanType().getName().split("\\.");
        logService.saveLog(SystemLogDTO.builder()
                .userId("")
                .userIp(ip)
                .actionType(systemLog.actionType().getName())
                .moduleName(systemLog.module())
                .description(systemLog.descrption())
                .method((packages.length > 0 ? packages[packages.length - 1] : "") + "." + handlerMethod.getMethod().getName() + "(...)")
                .url(request.getRequestURL().toString())
                .parameters(JSON.toJSONString(HttpRequestUtil.getParameterMap(request)))
                .result(SystemLogService.API_RESULT_FAIL)
                .message(CommonResult.AUTH_FAIL_MESSAGE + "authorization=" + (authorization == null ? "null" : (authorization.length() < 600 ? authorization : authorization.substring(0, 600))))
                .build());
    }


    /**
     * 清空用户相关的Thread Local
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

        ThreadUserUtil.USER_DETAIL_TL.remove();
        ThreadUserUtil.USER_MODULES_TL.remove();
    }

}
