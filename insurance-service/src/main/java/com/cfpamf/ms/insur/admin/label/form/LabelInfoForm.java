package com.cfpamf.ms.insur.admin.label.form;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LabelInfoForm implements Serializable {

    @ApiModelProperty("标签Code")
    Integer id;

    @ApiModelProperty("标签Code")
    String labelCode;
    @ApiModelProperty("标签名称")
    String labelName;
    @ApiModelProperty("标签描述")
    String labelDesc;

    @ApiModelProperty("标签状态")
    Integer labelStatus;
    @ApiModelProperty(value = "标签类型 理赔 claim",hidden = true)
    String labelType="claim";

    @ApiModelProperty("全量")
    List<LabelValueInfo> labelValueList;

    @ApiModelProperty("标签值新增列表")
    List<LabelValueInfo> addValueList;
    @ApiModelProperty("标签值更新列表")
    List<LabelValueInfo> updateValueList;
    @ApiModelProperty("标签值删除列表")
    List<LabelValueInfo> delValueList;

    @ApiModelProperty(value="操作人",hidden = true)
    String createBy;
}
