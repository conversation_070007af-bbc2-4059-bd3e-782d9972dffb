package com.cfpamf.ms.insur.admin.service.claim.impl;

import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimProgressTimelinessMapper;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgress;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimProgressTimeliness;
import com.cfpamf.ms.insur.admin.service.ClaimWorkflow;
import com.cfpamf.ms.insur.admin.service.claim.base.SmClaimProgressTimelinessService;
import com.cfpamf.ms.insur.base.constant.WorkFlowConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/4/16 14:54
 * @Version 1.0
 */
@Slf4j
@Service
public class SmClaimProgressTimelinessServiceImpl implements SmClaimProgressTimelinessService {

    @Autowired
    private SmClaimProgressTimelinessMapper progressTimelinessMapper;

    @Autowired
    protected ClaimWorkflow workflow;

    @Override
    public void extract(LocalDateTime startTime, LocalDateTime endTime) {

        log.info("抽取理赔{}至{}范围内理赔id", startTime, endTime);
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            startTime = LocalDateTime.now().withHour(0).withSecond(0).withMinute(0).minusDays(1);
            endTime = LocalDateTime.now().withHour(23).withSecond(59).withMinute(59);
        }

        Integer total = progressTimelinessMapper.countLatest(startTime, endTime);

        int batch = 20;
        int start = 0;
        while (start < total) {
            List<Integer> claimIds = progressTimelinessMapper.listLatest(startTime, endTime, start, batch);
            if (CollectionUtils.isEmpty(claimIds)) {
                break;
            }
            start = start + claimIds.size();

            List<SmClaimProgress> progressVOList = progressTimelinessMapper.listProgressByClaimId(claimIds);

            if (CollectionUtils.isNotEmpty(progressVOList)) {
                Map<Integer, List<SmClaimProgress>> claimProgressMap = progressVOList.stream().collect(
                        Collectors.groupingBy(
                                SmClaimProgress::getClaimId
                        )
                );

                List<SmClaimProgressTimeliness> claimProgressTimelinessList = claimProgressMap.values().stream()
                        .map(
                                x -> calProgressNodeTimeByClaimId(
                                        x.stream().sorted(Comparator.comparingInt(SmClaimProgress::getId)).collect(Collectors.toList())
                                )
                        )
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .collect(Collectors.toList());

                progressTimelinessMapper.deleteByClaimIds(claimIds);

                progressTimelinessMapper.insertList(claimProgressTimelinessList);

            }

        }


    }


    List<SmClaimProgressTimeliness> calProgressNodeTimeByClaimId(List<SmClaimProgress> progressList) {


        if (CollectionUtils.isEmpty(progressList)) {
            return null;
        }


        List<SmClaimProgressTimeliness> result = new ArrayList<>();
        for (int i = 0; i < progressList.size(); i++) {
            SmClaimProgressTimeliness progressTimeliness = new SmClaimProgressTimeliness();

            SmClaimProgress currentClaimProgress = progressList.get(i);

            SmClaimProgress lastClaimProgress = null;
            if (i > 0) {
                lastClaimProgress = progressList.get(i - 1);
            }

            progressTimeliness.setClaimId(currentClaimProgress.getClaimId());
            progressTimeliness.setCurrentCode(currentClaimProgress.getSCode());
            progressTimeliness.setCurrentCodeName(currentClaimProgress.getSName());
            progressTimeliness.setOptionCode(currentClaimProgress.getOCode());
            progressTimeliness.setOptionCodeName(currentClaimProgress.getOName());
            progressTimeliness.setOptionType(currentClaimProgress.getOType());
            progressTimeliness.setOptionValue(currentClaimProgress.getOValue());
            progressTimeliness.setEnabledFlag(0);
            progressTimeliness.setDataJson(currentClaimProgress.getDataJson());
            progressTimeliness.setSettlement(currentClaimProgress.getSettlement());
            progressTimeliness.setNodeEndTime(currentClaimProgress.getUpdateTime());
            progressTimeliness.setHandleUser(currentClaimProgress.getCreateBy());
            progressTimeliness.setProgressId(currentClaimProgress.getId());

            if (Objects.nonNull(lastClaimProgress)) {
                progressTimeliness.setNodeStartTime(lastClaimProgress.getUpdateTime());
                progressTimeliness.setCostTime(Duration.between(progressTimeliness.getNodeStartTime(), progressTimeliness.getNodeEndTime()).getSeconds());
            }

            if (Objects.equals(currentClaimProgress.getSCode(), ClaimWorkflow.STEP_TO_PAY)
                    && EnumClaimProcessType.api().contains(currentClaimProgress.getProcessType())
            ) {
                progressTimeliness.setNodeStartTime(currentClaimProgress.getCreateTime());
                progressTimeliness.setCostTime(Duration.between(progressTimeliness.getNodeStartTime(), progressTimeliness.getNodeEndTime()).getSeconds());
            }

            result.add(progressTimeliness);

        }

        return result;

    }


    public static void main(String[] args) {
        LocalDateTime.now().withHour(0).withSecond(0).withMinute(0).minusDays(1);
    }
}
