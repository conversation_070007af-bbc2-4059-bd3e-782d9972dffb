package com.cfpamf.ms.insur.admin.service;

import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderGroupNotifyMsgMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.TempSmOrderGroupNotifyMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.order.GroupNotify;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpV2CancelDetail;
import com.cfpamf.ms.insur.admin.external.tk.model.employer.TkEmpV2Order;
import com.cfpamf.ms.insur.admin.pojo.dto.order.GroupOrderDTO;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotify;
import com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderGroupNotifyMsg;
import com.cfpamf.ms.insur.admin.pojo.po.order.TempSmOrderGroupNotify;
import com.cfpamf.ms.insur.base.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

@Service
@Slf4j
public class SmOrderGroupNotifyService {
    @Autowired
    SmOrderGroupNotifyMapper smOrderGroupNotifyMapper;
    @Autowired
    SmOrderGroupNotifyMsgMapper smOrderGroupNotifyMsgMapper;
    @Autowired
    TempSmOrderGroupNotifyMapper tempSmOrderGroupNotifyMapper;

    public List<SmOrderGroupNotify> listFailByPolicyNo(String policyNo,String endorsementNo) {
        SmOrderGroupNotify query = new SmOrderGroupNotify();
        query.setGroupPolicyNo(policyNo);
        query.setStatus(-1);
        if(StringUtils.isNotBlank(endorsementNo)){
            query.setEndorsementNo(endorsementNo);
        }
        return smOrderGroupNotifyMapper.select(query);
    }

    public TempSmOrderGroupNotify queryTempNotifyContent(String policyNo,String endorsementNo) {
        return smOrderGroupNotifyMapper.queryTempNotifyContent(policyNo,endorsementNo);
    }

    public SmOrderGroupNotify getOrderGroupNotifyByPolicyNo(String policyNo, String batchNo) {
        SmOrderGroupNotify query = new SmOrderGroupNotify();
        query.setGroupPolicyNo(policyNo);
        if (StringUtils.isNotBlank(batchNo)) {
            query.setType(GroupNotify.TypeEnum.ENDORSEMENT.getCode());
            query.setEndorsementNo(batchNo);
        } else {
            query.setType(GroupNotify.TypeEnum.ORDER.getCode());
            query.setEndorsementNo("");
        }
        return smOrderGroupNotifyMapper.selectOne(query);
    }

    public TempSmOrderGroupNotify getTempOrderGroupNotifyByPolicyNo(Integer tempId) {
        return tempSmOrderGroupNotifyMapper.selectByPrimaryKey(tempId);
    }

    /**
     * 根据交易流水号查询团险批改信息，泰康新接口存在一个请求id对应多笔批改单的情况
     *
     * @param requestId
     * @return
     */
    public List<SmOrderGroupNotify> listOrderGroupNotifyByRequestId(String requestId) {
        SmOrderGroupNotify query = new SmOrderGroupNotify();
        query.setRequestId(requestId);

        return smOrderGroupNotifyMapper.select(query);
    }


    public SmOrderGroupNotify addOrderGroupNotifyRecord(String channel, GroupOrderDTO groupNotify) {
        SmOrderGroupNotify notify = new SmOrderGroupNotify();
        notify.setGroupType(GroupNotify.GroupType.GROUP.getCode());

        notify.setChannel(channel);
        notify.setGroupPolicyNo(groupNotify.getPolicyNo());
        notify.setPolicyState(groupNotify.getPolicyState());
        if (StringUtils.isNotBlank(groupNotify.getBatchNo())) {
            notify.setType(GroupNotify.TypeEnum.ENDORSEMENT.getCode());
            notify.setEndorsementNo(groupNotify.getBatchNo());
            notify.setRequestId(groupNotify.getPolicyNo() + groupNotify.getBatchNo());
        } else {
            notify.setType(GroupNotify.TypeEnum.ORDER.getCode());
            notify.setEndorsementNo("");
            notify.setRequestId(groupNotify.getPolicyNo());
        }
        notify.setOpMethod(GroupNotify.OpMethodEnum.API.getCode());
        notify.setStatus(GroupNotify.StatusEnum.UNDO.getCode());
        smOrderGroupNotifyMapper.insertSelective(notify);
        return notify;
    }


    public void addTempOrderGroupNotifyRecordByVersion(String requestId, String groupType, String channel, String policyNo,
                                                       String batchNo, Integer status, String policyState, String content, Integer interfaceVersion){
        TempSmOrderGroupNotify notify = new TempSmOrderGroupNotify();

        notify.setRequestId(requestId);
        notify.setChannel(channel);
        notify.setGroupPolicyNo(policyNo);
        notify.setPolicyState(policyState);
        notify.setGroupType(groupType);
        if (StringUtils.isNotBlank(batchNo)) {
            notify.setType(GroupNotify.TypeEnum.ENDORSEMENT.getCode());
            notify.setEndorsementNo(batchNo);
        } else {
            notify.setType(GroupNotify.TypeEnum.ORDER.getCode());
            notify.setEndorsementNo("");
        }
        notify.setOpMethod(GroupNotify.OpMethodEnum.CONTENT.getCode());
        notify.setStatus(status);
        notify.setNotifyContent(content);
        if (interfaceVersion == null) {
            notify.setInterfaceVersion(1);
        } else {
            notify.setInterfaceVersion(interfaceVersion);
        }

        int ret = tempSmOrderGroupNotifyMapper.insertSelective(notify);

    }


//    @Transactional(rollbackFor = Exception.class)
//    public SmOrderGroupNotify addOrderGroupNotifyRecordByVersion(String requestId, String groupType, String channel, String policyNo,
//                                                                 String batchNo, Integer status, String policyState, String content,
//                                                                 Integer interfaceVersion) {
//        SmOrderGroupNotify notify = createSmOrderGroupNotify(requestId, groupType, channel, policyNo, batchNo, status, policyState, interfaceVersion);
//        int ret = smOrderGroupNotifyMapper.insertSelective(notify);
//        if (ret == 0) {
//            throw new BizException("", "新增回调记录失败");
//        }
//        insertMsg(notify, content);
//
//        return notify;
//    }

    /**
     * 泰康雇主责任险回调报文
     * @param requestId
     * @param groupType
     * @param channel
     * @param data
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SmOrderGroupNotify addOrderGroupNotifyRecordV2(String requestId, String groupType, String channel, TkEmpV2CancelDetail data) {
        String policyNo = data.getPolicyNo();
        String endorsementNo = data.getEndorNo();
        String content = JSON.toJSONString(data);
        Long correctTimestamp = data.getEndorDate();

        SmOrderGroupNotify notify = createSmOrderGroupNotify(requestId, groupType, channel, policyNo, endorsementNo, GroupNotify.StatusEnum.DOING.getCode(), "1", correctTimestamp,2);
        int ret = smOrderGroupNotifyMapper.insertSelective(notify);
        if (ret == 0) {
            throw new BizException("", "新增回调记录失败");
        }
        insertMsg(notify, content);

        return notify;
    }

    /**
     * 泰康雇主责任险回调报文
     * @param requestId
     * @param groupType
     * @param channel
     * @param order
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SmOrderGroupNotify addOrderGroupNotifyRecordV2(String requestId, String groupType, String channel, TkEmpV2Order order) {
        String policyNo = order.getPolicy().getPolicyNo();
        String endorsementNo = null;
        String content = JSON.toJSONString(order);
        Long correctTimestamp = 0L;

        SmOrderGroupNotify notify = createSmOrderGroupNotify(requestId, groupType, channel, policyNo, endorsementNo, GroupNotify.StatusEnum.DOING.getCode(), "1", correctTimestamp,2);
        int ret = smOrderGroupNotifyMapper.insertSelective(notify);
        if (ret == 0) {
            throw new BizException("", "新增回调记录失败");
        }
        insertMsg(notify, content);

        return notify;
    }

    @Transactional(rollbackFor = Exception.class)
    public SmOrderGroupNotify addOrderGroupNotifyRecord(String requestId,
                                                        String groupType,
                                                        String channel,
                                                        String policyNo,
                                                        String batchNo,
                                                        Integer status,
                                                        String policyState,Long correctTimestamp, String content) {

        return addOrderGroupNotifyRecord(requestId,groupType,channel,policyNo,batchNo,status,policyState,correctTimestamp,content,null);
    }

    /**
     * 添加保司回调报文
     * @param requestId
     * @param groupType
     * @param channel
     * @param policyNo
     * @param batchNo
     * @param status
     * @param policyState
     * @param correctTimestamp
     * @param content
     * @param version
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SmOrderGroupNotify addOrderGroupNotifyRecord(String requestId,
                                                        String groupType,
                                                        String channel,
                                                        String policyNo,
                                                        String batchNo,
                                                        Integer status,
                                                        String policyState,
                                                        Long correctTimestamp,
                                                        String content,
                                                        Integer version) {

        SmOrderGroupNotify notify = createSmOrderGroupNotify(requestId, groupType, channel, policyNo, batchNo, status, policyState, correctTimestamp, version);

        int ret = smOrderGroupNotifyMapper.insertSelective(notify);
        if (ret == 0) {
            throw new BizException("", "新增回调记录失败");
        }
        insertMsg(notify, content);
        return notify;
    }

    private SmOrderGroupNotify createSmOrderGroupNotify(String requestId,
                                                        String groupType,
                                                        String channel,
                                                        String policyNo,
                                                        String batchNo,
                                                        Integer status,
                                                        String policyState,
                                                        Long correctTimestamp,
                                                        Integer interfaceVersion) {
        SmOrderGroupNotify notify = new SmOrderGroupNotify();

        notify.setRequestId(requestId);
        notify.setChannel(channel);
        notify.setGroupPolicyNo(policyNo);
        notify.setPolicyState(policyState);
        notify.setGroupType(groupType);
        if (StringUtils.isNotBlank(batchNo)) {
            notify.setType(GroupNotify.TypeEnum.ENDORSEMENT.getCode());
            notify.setEndorsementNo(batchNo);
        } else {
            notify.setType(GroupNotify.TypeEnum.ORDER.getCode());
            notify.setEndorsementNo("");
        }
        notify.setOpMethod(GroupNotify.OpMethodEnum.CONTENT.getCode());
        notify.setStatus(status);
        if (interfaceVersion == null) {
            notify.setInterfaceVersion(1);
        } else {
            notify.setInterfaceVersion(interfaceVersion);
        }
        notify.setCorrectTimestamp(correctTimestamp);
        return notify;
    }

    /**
     * 插入回调详情
     * @param notify
     * @param content
     */
    private void insertMsg(SmOrderGroupNotify notify, String content) {
        SmOrderGroupNotifyMsg msg = new SmOrderGroupNotifyMsg();
        msg.setNotifyId(notify.getId());
        msg.setNotifyContent(content);

        smOrderGroupNotifyMsgMapper.insert(msg);
    }


    @Transactional(rollbackFor = Exception.class)
    public int updateOrderGroupNotifyContent(String channel, String policyNo, String batchNo, Integer type, String content) {


        SmOrderGroupNotify query = new SmOrderGroupNotify();
        query.setGroupPolicyNo(policyNo);
        query.setEndorsementNo(batchNo != null ? batchNo : "");
        query.setType(type);
        query.setChannel(channel);
        SmOrderGroupNotify result = smOrderGroupNotifyMapper.selectOne(query);

        SmOrderGroupNotify update = new SmOrderGroupNotify();
        update.setId(result.getId());
        update.setOpMethod(GroupNotify.OpMethodEnum.CONTENT.getCode());
        int ret = smOrderGroupNotifyMapper.updateNotify(update);
        if (ret == 0) {
            throw new BizException("", "记录更新失败");
        }

        SmOrderGroupNotifyMsg msg = new SmOrderGroupNotifyMsg();
        msg.setNotifyId(result.getId());
        msg.setNotifyContent(content);
        smOrderGroupNotifyMsgMapper.insert(msg);
        return ret;
    }

    public int updateOrderGroupStatus(Integer id, Integer fromStatus, Integer toStatus) {
        Example example = new Example(SmOrderGroupNotify.class);
        example.createCriteria().andEqualTo("id", id)
                .andEqualTo("status", fromStatus);
        SmOrderGroupNotify group = new SmOrderGroupNotify();
        group.setStatus(toStatus);
        return smOrderGroupNotifyMapper.updateByExampleSelective(group, example);
    }

    public int updateTempOrderGroupStatus(Integer id, Integer fromStatus, Integer toStatus) {
        Example example = new Example(TempSmOrderGroupNotify.class);
        example.createCriteria().andEqualTo("id", id)
                .andEqualTo("status", fromStatus);
        TempSmOrderGroupNotify group = new TempSmOrderGroupNotify();
        group.setStatus(toStatus);
        return tempSmOrderGroupNotifyMapper.updateByExampleSelective(group, example);
    }

    /**
     * 更新回调记录
     *
     * @param id
     * @param notifyStatus
     * @return
     */
    public int updateOrderGroupStatus(Integer id, Integer notifyStatus) {
        Example example = new Example(SmOrderGroupNotify.class);
        example.createCriteria().andEqualTo("id", id);
        SmOrderGroupNotify data = new SmOrderGroupNotify();
        data.setStatus(notifyStatus);
        return smOrderGroupNotifyMapper.updateByExampleSelective(data, example);
    }

    public List<SmOrderGroupNotify> listGroupNotifyByStatus(String channel, Integer status) {
        return smOrderGroupNotifyMapper.listByStatus(channel, status);
    }

    public SmOrderGroupNotify getById(Integer id) {

        return smOrderGroupNotifyMapper.selectByPrimaryKey(id);
    }

    public SmOrderGroupNotifyMsg getNotifyMsg(Integer notifyId) {
        return smOrderGroupNotifyMsgMapper.getByNotifyId(notifyId);
    }


    public SmOrderGroupNotify getByRequestId(String requestId, String channel) {
        SmOrderGroupNotify query = new SmOrderGroupNotify();
        query.setRequestId(requestId);
        query.setChannel(channel);

        return smOrderGroupNotifyMapper.selectOne(query);
    }

    public List<SmOrderGroupNotify> listHistoryByPolicyNo(String policyNo,Long correctTimestamp) {
        return smOrderGroupNotifyMapper.listHistoryByPolicyNo(policyNo,correctTimestamp);
    }

    public List<SmOrderGroupNotify> listByPolicyNoAndStatus(String policyNo, String endorsementNo,Integer status) {
        return smOrderGroupNotifyMapper.listByPolicyNoAndStatus(policyNo,endorsementNo,status);
    }
}
