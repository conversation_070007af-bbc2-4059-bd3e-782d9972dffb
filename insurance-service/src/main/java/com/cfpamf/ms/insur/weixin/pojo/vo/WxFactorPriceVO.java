package com.cfpamf.ms.insur.weixin.pojo.vo;

import com.alibaba.druid.util.StringUtils;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanFactorPriceDT;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 微信产品因素价格vo
 *
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WxFactorPriceVO {

    /**
     * 计划Id
     */
    @ApiModelProperty(value = "内部计划Id")
    private Integer planId;

    /**
     * 保险期限
     */
    @ApiModelProperty(value = "保险期限选项Id")
    private Integer validPeriod;

    /**
     * 承保年龄代码
     */
    @ApiModelProperty(value = "承保年龄选项Id")
    private Integer underWritingAge;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别选项Id")
    private Integer sex;

    /**
     * 社保
     */
    @ApiModelProperty(value = "社保选项")
    private Integer socialSecurity;

    /**
     * 车辆座位数
     */
    @ApiModelProperty(value = "车辆座位数选项")
    private Integer vehicleSeatNumber;

    /**
     * 职业Id
     */
    @ApiModelProperty(value = "职业选项")
    private Integer occuplCategory;

    /**
     * 价格 小数位数0
     */
    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 根据规则是否可选
     */
    @ApiModelProperty(value = "根据规则是否可选")
    private Boolean available;

    @ApiModelProperty("是否吸烟")
    private Integer smoke;

    public WxFactorPriceVO() {
    }

    public WxFactorPriceVO(SmPlanFactorPriceDT vo) {
        this.planId = vo.getPlanId();
        this.validPeriod = vo.getValidPeriodOptional();
        this.underWritingAge = vo.getUnderWritingAgeOptional();
        this.occuplCategory = vo.getOccuplCategoryOptional();
        this.sex = vo.getSexOptional();
        this.socialSecurity = vo.getSocialSecurityOptional();
        this.vehicleSeatNumber = vo.getVehicleSeatNumberOptional();
        this.available = vo.getAvailable();
        this.smoke = vo.getSmokeOptional();
        this.price = StringUtils.isEmpty(vo.getPrice()) ? new BigDecimal(0) : new BigDecimal(vo.getPrice());
    }
}
