package com.cfpamf.ms.insur.admin.external.whale.client;

import com.alibaba.fastjson.JSONObject;
import com.cfpamf.ms.insur.admin.external.whale.model.*;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyResultV2;
import com.cfpamf.ms.insur.admin.external.whale.model.renewal.RenewalTermNotifyVo;
import com.cfpamf.ms.insur.admin.pojo.vo.order.activitie.DistributionExemptionVo;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.Path;
import java.util.List;

/**
 * <AUTHOR> 2020/4/10 15:36
 */

public interface WhaleOrderClient {

    @GetMapping("/channel/api/policy/info")
    WhaleResp<WhaleContract> getPolicy(@RequestParam("aid") String aid,
                                       @RequestParam("version") String version,
                                       @RequestParam("contractCode") String con,
                                       @RequestParam("businessScenario") String businessScenario);

    @PostMapping("/channel/api/product/mapping")
    WhaleResp<List<WhaleProductMappingVo>> productMapping(@RequestParam("aid") String aid, @RequestParam("version") String version,@RequestBody List<String> productCodeList);

    @GetMapping("/channel/api/group/policy/info")
    WhaleResp<WhaleContract> getGroupPolicy(@RequestParam("aid") String aid,
                                            @RequestParam("version") String version,
                                            @RequestParam("contractCode") String con,
                                            @RequestParam("channelCode") String channelCode,
                                            @RequestParam("businessScenario") String businessScenario);

    @GetMapping("/channel/api/policy/preservation/detail")
    WhaleResp<PreservationDetail> getPreservationDetail(@RequestParam("aid") String aid,
                                                        @RequestParam("version") String version,
                                                        @RequestParam("preservationCode") String preservationCode,
                                                        @RequestParam("channelCode") String channelCode);


    /**
     * 是否绑定
     *
     * @param aid
     * @param version
     * @param jobNumber
     * @return
     */
    @GetMapping("/channel/api/referrer/auth_status")
    WhaleResp<Boolean> authStatus(@RequestParam("aid") String aid
            , @RequestParam("version") String version, @RequestParam("jobNumber") String jobNumber);

    /**
     * 推送续期实收数据
     *
     * @param aid
     * @param version
     * @param renewalTerm
     * @return
     */
    @PostMapping("/channel/api/renewal-term/zhnx/paid")
    WhaleResp<RenewalTermNotifyResultV2> pushRenewalTerm(@RequestParam("aid") String aid
            , @RequestParam("version") String version, @RequestBody RenewalTermNotifyVo renewalTerm);
    /**
     * 有离职交接客户时通知小鲸处理
     *
     * @param aid
     * @param version
     * @param handoverVO
     * @return
     */
    @PostMapping("/channel/api/referrer/user/handover")
    WhaleResp<String> disposeHandover(@RequestParam("aid") String aid
            , @RequestParam("version") String version, @RequestBody HandoverVO handoverVO);

    /**
     * 四级分销豁免
     *
     * @param aid
     * @param version
     * @param handoverVO
     * @return
     */
    @PostMapping("/channel/api/activity/four/exemption")
    WhaleResp<String> activityFourExemption(@RequestParam("aid") String aid
            , @RequestParam("version") String version, @RequestBody DistributionExemptionVo handoverVO);

    /**
     * <p>
     * 提供公众号获取小程序token
     * </p>
     *
     * @param getAppletTokenInput
     * <AUTHOR>
     * @since 2024/8/29
     */
    @RequestMapping(value = "/wx/user/getAppletToken", method = RequestMethod.POST)
    WhaleResp<String> getAppletToken(
            @RequestParam("aid") String aid,
            @RequestParam("version") String version,
            @RequestBody GetAppletTokenInput getAppletTokenInput);
}
