package com.cfpamf.ms.insur.weixin.service;

import com.cfpamf.ms.insur.admin.dao.safes.SmProductMapper;
import com.cfpamf.ms.insur.admin.pojo.vo.SmProductClauseVO;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.google.common.io.ByteStreams;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 微信下载保险条款service
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WxDownloadService {

    /**
     * https 调用RestTemplate 单例不能每次新建！！
     */
    private static RestTemplate restTemplate = getHttpsTemplate();
    /**
     * 产品mapper
     */
    @Autowired
    private SmProductMapper mapper;

    /**
     * 保单下载失败跳转的页面
     */
    @Value("${company.error-redirect-url:}")
    private String errorUrl;

    /**
     * 构造https请求参数
     *
     * @return
     */
    private static RestTemplate getHttpsTemplate() {
        // RestTemplate 支持服务器内302重定向
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        HttpClient httpClient = HttpClientBuilder.create()
                .setRedirectStrategy(new LaxRedirectStrategy())
                .build();
        factory.setHttpClient(httpClient);
        factory.setReadTimeout(30_000);
        factory.setConnectTimeout(5_000);
        return new RestTemplate(factory);
    }

    /**
     * 微信下载保险条款
     *
     * @param id
     * @param resp
     * @throws IOException
     * @throws URISyntaxException
     */
    public void downloadClause(int id, HttpServletResponse resp) {
        SmProductClauseVO clauseVo = mapper.getProductClauseById(id);
        String clauseUrl = clauseVo.getClauseUrl();
        resp.reset();
        try (OutputStream os = resp.getOutputStream()) {
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(clauseVo.getClauseName(), StandardCharsets.UTF_8.name()));
            resp.setDateHeader("expires", System.currentTimeMillis() + 36000000000L);
            resp.setContentType("application/pdf");
            byte[] fileBytes = getBytesFromUrl(clauseUrl);
            resp.setContentLength(fileBytes.length);
            os.write(fileBytes);
        } catch (Exception e) {
            log.warn("保险条款下载失败id=" + id, e);
        }
    }


    /**
     * 保单下载
     *
     * @param url
     * @param policyNo
     * @return
     */
    public void downloadPolicy(String url, String policyNo, HttpServletResponse resp) throws IOException {
        @Cleanup
        OutputStream os = resp.getOutputStream();
        ResponseEntity<Resource> responseEntity = null;

        if (url.contains("chinahuanong")) {
            URL downloadUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) downloadUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(10 * 1000);
            conn.setReadTimeout(30 * 1000);

            resp.reset();
            resp.setDateHeader("expires", System.currentTimeMillis() + 36000000000L);
            resp.setContentType("application/pdf");
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode("保单_" + policyNo + ".pdf", StandardCharsets.UTF_8.name()));
            @Cleanup
            InputStream is = conn.getInputStream();
            ByteStreams.copy(is, os);
            resp.flushBuffer();
            return;
        } else{
            try {
                responseEntity = restTemplate.getForEntity(url, Resource.class);
            } catch (RestClientException rest) {

                log.warn("保单下载失败:{},{}", policyNo, url);
                log.warn("保单下载失败:{}", rest.getMessage(), rest);

                resp.sendRedirect(String.format(errorUrl, URLEncoder.encode("保单下载失败,请稍后重试", StandardCharsets.UTF_8.name())));
                return;
            }
            if (!responseEntity.hasBody()) {

                log.warn("保单下载为空:{},{},{}", policyNo, url, responseEntity);
                resp.sendRedirect(String.format(errorUrl, URLEncoder.encode("保单下载失败,请稍后重试！", StandardCharsets.UTF_8.name())));
                return;
            }

            resp.reset();
            resp.setDateHeader("expires", System.currentTimeMillis() + 36000000000L);
            resp.setContentType("application/pdf");
            resp.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode("保单_" + policyNo + ".pdf", StandardCharsets.UTF_8.name()));
            // 判断文件类别
            int headSize = 4;
            @Cleanup
            InputStream is = responseEntity.getBody().getInputStream();
            byte[] headBytes = new byte[headSize];
            is.read(headBytes, 0, 4);
            String headHexStr = Hex.encodeHexString(headBytes);
            is.reset();
            // PDF 类别
            if (Objects.equals(BaseConstants.PDF_HEAD_HEX_STR, headHexStr)) {
                ByteStreams.copy(is, os);
                return;
            }
            // ZIP文件
            @Cleanup
            ZipInputStream zis = new ZipInputStream(is);
            ZipEntry ze = null;
            if (((ze = zis.getNextEntry()) != null) && !ze.isDirectory()) {
                ByteStreams.copy(zis, os);
            }
            resp.flushBuffer();
        }
    }

    /**
     * 微信下载保险条款代理
     *
     * @param httpUrl
     * @return
     * @throws URISyntaxException
     */
    public byte[] getBytesFromUrl(String httpUrl) throws URISyntaxException {
        RequestEntity requestEntity = RequestEntity.get(new URI(httpUrl)).build();
        ResponseEntity responseEntity = restTemplate.exchange(requestEntity, byte[].class);
        return (byte[]) responseEntity.getBody();
    }
}
