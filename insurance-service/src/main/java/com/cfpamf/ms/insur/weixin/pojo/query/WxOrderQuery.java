package com.cfpamf.ms.insur.weixin.pojo.query;

import com.cfpamf.ms.insur.base.bean.Pageable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @description: 微信订单查询Query
 * @author: zhang<PERSON><PERSON>
 * @create: 2018-07-16 09:21
 **/
@Data
@ApiModel
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class WxOrderQuery extends Pageable {

    @ApiModelProperty(value = "推荐人Id", hidden = true)
    private String userId;

    @ApiModelProperty(value = "代理人Id", hidden = true)
    private Integer agentId;

    @ApiModelProperty(value = "微信openId")
    private String openId;

    @ApiModelProperty(value = "订单状态")
    private String orderState;

    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "承保状态")
    private String appStatus;

    @ApiModelProperty(value = "保单状态")
    private Boolean policyStatus;

    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "微信token")
    private String authorization;

    @ApiModelProperty(value = "订单创建开始时间")
    private Date startTime;

    @ApiModelProperty(value = "订单创建结束时间")
    private Date endTime;

    @ApiModelProperty(value = "查询关键字类型，见，EnumOrderQueryKeyType")
    private Integer keyType;
    @ApiModelProperty(value = "查询关键字")
    private String keyword;


    private String orderId;
    private String insuredName;
    @ApiModelProperty(hidden = true)
    private Integer insuredType;
    private String applicantName;
    @ApiModelProperty(hidden = true)
    private Integer applicantType;


    @ApiModelProperty(value = "可以理赔")
    private Boolean claimable;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    @ApiModelProperty(hidden = true)
    private Date claimStartTime;

    @ApiModelProperty(hidden = true)
    private Date claimEndTime;

    @ApiModelProperty(hidden = true)
    private String idNumber;

    @ApiModelProperty(hidden = true)
    private Integer idNumberType;

    @ApiModelProperty(value = "负责人Id")
    private String customerAdminId;

    @ApiModelProperty(value = "保险公司Id")
    private Integer companyId;

    @ApiModelProperty(value = "产品id")
    private Integer productId;

    @ApiModelProperty(value = "回访状态 未回访undo,回访成功 success,首访不成功 firstFailed,再访不成功 failedAgain")
    private List<String> visitStatus;

    @ApiModelProperty(value = "扣款签约账户绑定状态 绑定状态，未绑定 undo,绑定 bind,解绑 remove")
    private String bindStatus;

    @ApiModelProperty(value = "收件信息 无：0 有：1")
    private String bindAddress;

    @ApiModelProperty(value = "是否查询批改待支付 是：0 否：1")
    private String queryEndorFlag;

    @ApiModelProperty(value = "批改状态：0=已提交；1=已支付；2=已生效；-1=已撤销；10=未提交；20=处理中(待保司回调处理)")
    private Integer endorStatus;

    @ApiModelProperty(value = "模糊key")
    private String fuzzyKey;
}
