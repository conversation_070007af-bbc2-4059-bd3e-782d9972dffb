package com.cfpamf.ms.insur.weixin.pojo.vo.settlement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@ApiModel(value = "结算支出基础佣金统计")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WxSettlementCostTotalVO {
    @ApiModelProperty("当日佣金统计")
    private BigDecimal todayAmount;
    @ApiModelProperty("当月佣金统计")
    private BigDecimal currentMonthAmount;

    @ApiModelProperty("当年佣金统计")
    private BigDecimal currentYearAmount;

    @ApiModelProperty("累计佣金统计")
    private BigDecimal totalAmount;

}
