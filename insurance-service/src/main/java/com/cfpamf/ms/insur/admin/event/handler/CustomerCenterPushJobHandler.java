package com.cfpamf.ms.insur.admin.event.handler;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.result.Result;
import com.cfpamf.ms.customer.facade.api.InsuranceFacade;
import com.cfpamf.ms.customer.facade.enums.AddressTypeEnum;
import com.cfpamf.ms.customer.facade.enums.IDTypeEnum;
import com.cfpamf.ms.customer.facade.request.address.ModifyAddressBaseReq;
import com.cfpamf.ms.customer.facade.request.insurance.*;
import com.cfpamf.ms.customer.facade.vo.CustInfoVo;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerEducationMapper;
import com.cfpamf.ms.insur.admin.dao.safes.CustomerMapper;
import com.cfpamf.ms.insur.admin.event.CustomerPushEvent;
import com.cfpamf.ms.insur.admin.pojo.po.CustomerEducation;
import com.cfpamf.ms.insur.admin.pojo.vo.CustPropertyVO;
import com.cfpamf.ms.insur.admin.pojo.vo.CustomerRegisterVO;
import com.cfpamf.ms.insur.base.event.BaseEventHandler;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.google.common.eventbus.Subscribe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 推送客户中心客户定时任务
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
public class CustomerCenterPushJobHandler implements BaseEventHandler {

    /**
     * 客户中心用户注册facade
     */
    @Autowired
    private InsuranceFacade insuranceFacade;

    /**
     * 保险客户mapper
     */
    @Autowired
    private CustomerMapper customerMapper;

    /**
     * 教育经历
     */
    @Autowired
    private CustomerEducationMapper educationMapper;

    /**
     * 处理需要推送客户中心的事件
     */
    @Subscribe
    public void handerCustomerPushEvent(CustomerPushEvent event) {
        CustomerRegisterVO cr = customerMapper.getUnRegisterCustomer(event.getCustomerId());
        // 非身份证暂时不调用注册接口
        if (!org.apache.commons.lang3.StringUtils.contains(cr.getIdType(), "身份证")) {
            return;
        }
        String custNo = cr.getCustNo();
        boolean needUpdateCcCustNp = false;
        // 未实名认证
        if (custNo == null) {
            CustInfoVo custInfo = register(cr.getCustomerName(), cr.getIdNumber(), cr);
            if (custInfo != null) {
                custNo = custInfo.getCustNo();
                needUpdateCcCustNp = true;
            }
        }

        if (custNo != null) {
            // 推送最新客户家财信息
            List<CustPropertyVO> propertys = customerMapper.listWxCustomerPropertyById(String.valueOf(cr.getId()));
            List<CustomerEducation> educations = educationMapper.listByCustomerId(event.getCustomerId());
            FillCustInfoReq custInfoReq = buildFillCustInfoReq(custNo, cr, propertys, educations);
            fillCustInfo(custInfoReq);
            if (needUpdateCcCustNp) {
                customerMapper.updateCustomerRegisterInfo(cr.getId(), custNo);
            }
        }
    }

    /**
     * buildFillCustInfoReq
     *
     * @param custNo
     * @param cr
     * @param propertys
     * @param educations
     * @return
     */
    private FillCustInfoReq buildFillCustInfoReq(String custNo, CustomerRegisterVO cr, List<CustPropertyVO> propertys, List<CustomerEducation> educations) {
        FillCustInfoReq fciReq = new FillCustInfoReq();
        fciReq.setCustNo(custNo);
        fciReq.setOccupation(cr.getOccupation());

        //如果有地址信息
        if (!StringUtils.isEmpty(cr.getAddress())) {
            ModifyAddressBaseReq modifyAddressBaseReq = new ModifyAddressBaseReq();
            modifyAddressBaseReq.setAddr(cr.getAddress());
            modifyAddressBaseReq.setType(AddressTypeEnum.LIVE_ADDRESS.getType());
            modifyAddressBaseReq.setOperator(cr.getNewestAdmin());
            String area = cr.getArea();
            if (!StringUtils.isEmpty(area) &&
                    Objects.equals(cr.getAddressProvider(), 2)) {
                String[] areaCodes = area.split("\\|");
                modifyAddressBaseReq.setProvinceCode(areaCodes[0]);

                if (areaCodes.length >= 2 && !StringUtils.isEmpty(areaCodes[1])) {
                    modifyAddressBaseReq.setCityCode(areaCodes[1]);
                }
                if (areaCodes.length >= 3 && !StringUtils.isEmpty(areaCodes[2])) {
                    modifyAddressBaseReq.setCountyCode(areaCodes[2]);
                }

                if (areaCodes.length >= 4 && !StringUtils.isEmpty(areaCodes[3])) {
                    modifyAddressBaseReq.setTownCode(areaCodes[3]);
                }

                if (areaCodes.length >= 5 && !StringUtils.isEmpty(areaCodes[4])) {
                    modifyAddressBaseReq.setVillageCode(areaCodes[4]);
                }
            }
            fciReq.setAddress(modifyAddressBaseReq);
        }
        fciReq.setAnnualIncome(cr.getAnnualIncome());
        List<FillHousePropertyModel> houses = new ArrayList<>();
        List<FillVehiclePropertyModel> vehicles = new ArrayList<>();
        propertys.forEach(p -> {
            if (!StringUtils.isEmpty(p.getCarPlateNo())) {
                FillVehiclePropertyModel vehicle = new FillVehiclePropertyModel();
                if (!StringUtils.isEmpty(p.getApprovedNum())) {
                    vehicle.setApprovedNum(Integer.valueOf(p.getApprovedNum()));
                }
                vehicle.setCarNo(p.getCarPlateNo());
                vehicle.setVin(p.getChassisNumber());
                vehicle.setEngineNo(p.getEngineNo());
                vehicle.setCarManufacturerModel(p.getCarManufacturerModel());
                vehicles.add(vehicle);
            } else {
                FillHousePropertyModel house = new FillHousePropertyModel();
                house.setHouseNo(p.getHourseNo());
                house.setHouseType(p.getHourseType());
                house.setHouseAddress(p.getPropertyAddress());
                houses.add(house);
            }
        });

        //设置教育经历
        fciReq.setEducationPropertyModels(educations.stream().map(edu -> {
            FillEducationPropertyModel model = new FillEducationPropertyModel();
            model.setClassNo(edu.getSchoolClass());
            model.setSchoolNature(edu.getSchoolNature());
            model.setSchoolType(edu.getSchoolType());
            model.setStudentId(edu.getStudentId());
            model.setStudentType(edu.getStudentType());
            model.setName(edu.getSchoolName());
            return model;
        }).collect(Collectors.toList()));

        fciReq.setHousePropertyModels(houses);
        fciReq.setVehiclePropertyModels(vehicles);
        return fciReq;
    }

    /**
     * 客户中心实名认证
     *
     * @param customerName
     * @param idNumber
     * @return
     */
    private CustInfoVo register(String customerName, String idNumber, CustomerRegisterVO vo) {
        CreateCustInfoReq custReq = new CreateCustInfoReq();
        custReq.setCustName(customerName);
        custReq.setIdNo(idNumber);
        custReq.setIdType(IDTypeEnum.IDENTITY_CARD);
        custReq.setOperator(vo.getNewestAdmin());
        custReq.setOrgNo(vo.getOrgCode());
        log.info("insuranceFacade.createCustInfo(custReq){}", JSON.toJSONString(custReq));

        Result<CustInfoVo> result = insuranceFacade.createCustInfo(custReq);
        if (result.isSuccess()) {
            return result.getData();
        }
        return null;
    }

    /**
     * 客户中心保存保险客户信息
     *
     * @param custInfoReq
     */
    private void fillCustInfo(FillCustInfoReq custInfoReq) {
        log.info("insuranceFacade.fillCustInfo(custInfoReq){}", JSON.toJSONString(custInfoReq));
        Result<Void> result = insuranceFacade.fillCustInfo(custInfoReq);
        if (!result.isSuccess()) {
            throw new BizException(result.getErrorCode(), result.getErrorMsg());
        }
    }
}
