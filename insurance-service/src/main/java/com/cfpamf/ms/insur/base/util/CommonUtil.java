package com.cfpamf.ms.insur.base.util;

import com.alibaba.arms.tracing.Span;
import com.alibaba.arms.tracing.Tracer;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cfpamf.common.ms.util.CentNoUtil;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.pojo.quwey.ReportQuery;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.github.pagehelper.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 共同方法类
 *
 * <AUTHOR>
 */
public class CommonUtil {


    private CommonUtil() {
    }

    /**
     * isCollectionNotEmpty
     *
     * @param c
     * @return
     */
    public static boolean isCollectionNotEmpty(Collection c) {
        return c != null && !c.isEmpty();
    }

    /**
     * isCollectionEmpty
     *
     * @param c
     * @return
     */
    public static boolean isCollectionEmpty(Collection c) {
        return c == null || c.isEmpty();
    }

    /**
     * 去除字符串两边空格判断是否相等
     *
     * @param s1
     * @param s2
     * @return
     */
    public static boolean equalIgnoreBlank(String s1, String s2) {
        s1 = StringUtils.isEmpty(s1) ? null : s1.trim();
        s2 = StringUtils.isEmpty(s2) ? null : s2.trim();
        return Objects.equals(s1, s2);
    }

    /**
     * 获得某天最大时间 2017-10-15 23:59:59
     *
     * @param date
     * @return
     */
    public static Date getEndTimeOfDay(Date date) {
        if (date == null) {
            return null;
        }
        return DateUtil.getEndOfDay(date);
    }

    /**
     * 获得某天最小时间 2017-10-15 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getStartTimeOfDay(Date date) {
        if (date == null) {
            return null;
        }
        return DateUtil.getBeginOfDay(date);
    }

    /**
     * 获得当月第一天最小时间 2017-10-01 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getStartTimeOfMonth(Date date) {
        if (date == null) {
            return null;
        }
        return DateUtil.getBeginOfMonth(date);
    }


    /**
     * 判断保险名称全称包含简称
     *
     * @param fullName
     * @param abbre
     * @return
     */
    public static boolean containAbbre(String fullName, String abbre) {
        if (fullName == null && abbre == null) {
            return true;
        }
        if (fullName == null || abbre == null) {
            return false;
        }
        int jstart = 0;
        int jlen = fullName.length();
        for (int i = 0, len = abbre.length(); i < len; i++) {
            char c = abbre.charAt(i);
            for (; jstart < jlen; jstart++) {
                if (fullName.charAt(jstart) == c) {
                    break;
                }
            }
            if (jstart == jlen) {
                return false;
            }
        }
        return true;
    }

    /**
     * 字段去除左右空白 如果为空返回null
     *
     * @param value
     * @return
     */
    public static String trim(String value) {
        if (value != null) {
            value = DataMaskUtil.safeReplaceAll(value, "\\s*", "");
        }
        return StringUtils.isEmpty(value) ? null : value;
    }

    /**
     * 通过身份证号获取性别
     *
     * @param certNo
     * @return
     */
    public static String getSex(String certNo) {
        return Objects.equals(CentNoUtil.getSex(certNo), "M") ? "男" : "女";
    }

    /**
     * 通过编码获取性别中文
     * 中华联 CIC ：（3 未知 1 男 2 女），其他（Male 男  Female 女）   2020-02-24
     *
     * @param genderCode
     * @return
     */
    public static String getSexChinese(String genderCode) {
        if (StringUtils.isEmpty(genderCode)) {
            return null;
        }
        if ("3".equals(genderCode)) {
            return "未知";
        }
        if ("1".equals(genderCode) || "Male".equals(genderCode)) {
            return "男";
        }
        if ("2".equals(genderCode) || "Female".equals(genderCode)) {
            return "女";
        }
        return "未知";
    }

    /**
     * 映射显示状态
     *
     * @param payStatus 支付状态 订单状态  0 订单过期  "1" 待支付   "2"已支付  3 订单取消  99 支付异常
     * @param appStatus 保单状态  -2 无 -1失效  0 承保失败  1 承保成功  2 处理中  3 退保失败  4 退保成功  5 部分承保失败
     * @param endTime   终保时间
     * @return
     */
    public static String getShowStatusName(String payStatus, String appStatus, Date endTime) {
        if (StringUtil.isEmpty(payStatus) || StringUtil.isEmpty(appStatus) || endTime == null) {
            return "";
        }

        // 智能核保已完成
        if (SmConstants.ORDER_STATUS_CHECKED.equals(payStatus)) {
            return "进行中";
        }
        // 智能核保未完成
        if (SmConstants.ORDER_STATUS_TO_ORDER.equals(payStatus)) {
            return "进行中";
        }

        if ("1".equals(payStatus)) {
            return "待支付";
        }
        if ("99".equals(payStatus)) {
            return "支付异常";
        }
        if ("2".equals(payStatus) && "1".equals(appStatus) && (new Date()).before(endTime)) {
            return "保障中";
        }
        if ("2".equals(payStatus) && "1".equals(appStatus) && !(new Date()).before(endTime)) {
            return "已失效";
        }
        if ("2".equals(payStatus) && "-1".equals(appStatus)) {
            return "已失效";
        }
        if ("2".equals(payStatus) && "0".equals(appStatus)) {
            return "承保失败";
        }
        if ("2".equals(payStatus) && "2".equals(appStatus)) {
            return "处理中";
        }
        if ("2".equals(payStatus) && "4".equals(appStatus)) {
            return "退保成功";
        }
        if ("2".equals(payStatus) && "3".equals(appStatus)) {
            return "退保失败";
        }
        if ("0".equals(payStatus)) {
            return "订单关闭";
        }
        if ("3".equals(payStatus)) {
            return "订单关闭";
        }
        if ("11".equals(payStatus)) {
            return "支付中";
        }
        if ("98".equals(payStatus)) {
            return "出单失败";
        }

        return "数据异常";
    }

    /**
     * 填充查询条件  monthEndDayList月底最后一天列表
     *
     * @param query
     */
    public static void fillQueryMonthEndDayList(ReportQuery query) {
        try {
            if (StringUtil.isNotEmpty(query.getDateStr())) {
                //统计类型（1月度 2年度）
                if ("1".equals(query.getType())) {
                    query.setStartDate(LocalDateUtil.stringToUdate(query.getDateStr().substring(0, 7) + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    query.setEndDate(LocalDateUtil.stringToUdate(query.getDateStr(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                } else if ("2".equals(query.getType())) {
                    query.setStartDate(LocalDateUtil.stringToUdate(query.getDateStr().substring(0, 4) + "-01-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    query.setEndDate(LocalDateUtil.stringToUdate(query.getDateStr(), DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                } else {
                    query.setStartDate(LocalDateUtil.stringToUdate(query.getStartMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    query.setEndDate(DateUtil.getEndOfMonth(LocalDateUtil.stringToUdate(query.getEndMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
                }
            } else if (StringUtil.isNotEmpty(query.getMonthStr())) {
                //统计类型（1月度 2年度）
                if ("1".equals(query.getType())) {
                    query.setStartDate(LocalDateUtil.stringToUdate(query.getMonthStr() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    query.setEndDate(DateUtil.getEndOfMonth(LocalDateUtil.stringToUdate(query.getMonthStr() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
                } else if ("2".equals(query.getType())) {
                    query.setStartDate(LocalDateUtil.stringToUdate(query.getMonthStr().substring(0, 4) + "-01-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    query.setEndDate(DateUtil.getEndOfMonth(LocalDateUtil.stringToUdate(query.getMonthStr() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
                } else {
                    query.setStartDate(LocalDateUtil.stringToUdate(query.getStartMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                    query.setEndDate(DateUtil.getEndOfMonth(LocalDateUtil.stringToUdate(query.getEndMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
                }
            } else {
                query.setStartDate(LocalDateUtil.stringToUdate(query.getStartMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT));
                query.setEndDate(DateUtil.getEndOfMonth(LocalDateUtil.stringToUdate(query.getEndMonth() + "-01", DateUtil.CN_YEAR_MONTH_DAY_FORMAT)));
            }
        } catch (Exception e) {
            throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097);
        }
        if (query.getEndDate().before(query.getStartDate())) {
            throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097);
        }
        //当前月一样能取到数据，取当前日期减1
        Date nowDate = new Date();
        LocalDate localNowDate = LocalDateUtil.dateToLocalDate(nowDate);
        if (nowDate.before(query.getEndDate())) {
            query.setEndDate(DateUtil.addDay(nowDate, -1));
        }
        if (query.getEndDate().before(query.getStartDate())) {
            throw new BizException(ExcptEnum.HTTP_PARAM_ERROR_000097.getCode(), "数据还未生成");
        }
        LocalDate startDate = LocalDateUtil.dateToLocalDate(DateUtil.getBeginOfMonth(query.getStartDate()));
        LocalDate endDate = LocalDateUtil.dateToLocalDate(DateUtil.getBeginOfMonth(query.getEndDate()));
        List<String> monthEndDayList = new ArrayList<>(1);
        LocalDate monthEndDay;
        for (; !endDate.isBefore(startDate); startDate = startDate.plusMonths(1)) {
            monthEndDay = startDate.with(TemporalAdjusters.lastDayOfMonth());
            if (DateUtil.isTheSameMonth(LocalDateUtil.localDateToUdate(startDate), query.getEndDate())) {
                monthEndDay = LocalDateUtil.dateToLocalDate(query.getEndDate());
            }
            monthEndDayList.add(monthEndDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        query.setMonthEndDayList(monthEndDayList);
    }

    /**
     * 字符串忽略大小写比较
     *
     * @param s1
     * @param s2
     * @return
     */
    public static boolean equalsIngoreCase(String s1, String s2) {
        s1 = s1 == null ? null : s1.toLowerCase();
        s2 = s2 == null ? null : s2.toLowerCase();
        return Objects.equals(s1, s2);
    }

    /**
     * 获取现在到明天0点0分0秒的秒数
     *
     * @return
     */
    public static long getTodayNextSeconds() {
        long nowSeconds = System.currentTimeMillis() / 1000;
        return DateUtil.getEndOfDay(nowSeconds) - nowSeconds;
    }

    /**
     * 验证字符串是否身份证
     *
     * @param str
     * @return
     */
    public static boolean isIdCardNo(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }
        //第一代身份证正则表达式(15位)
        String isIDCard1 = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$";
        //第二代身份证正则表达式(18位)
        String isIDCard2 = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])((\\d{4})|\\d{3}[A-Z])$";
        return str.matches(isIDCard1) || str.matches(isIDCard2);
    }


    /**
     * 验证字符串是否身份证
     *
     * @param Str
     * @return
     */
    public static boolean isMobile(String Str) {
        String mobileRegex = "^(1)\\d{10}$";
        return Str.matches(mobileRegex);
    }


    public static String strJoin(String delimiter, List<String> strs) {
        if (strs == null || strs.size() == 0) {
            return null;
        }
        return String.join(delimiter, strs);
    }

    public static boolean checkAdult(Date date) {
        Calendar current = Calendar.getInstance();
        Calendar birthDay = Calendar.getInstance();
        birthDay.setTime(date);
        Integer year = current.get(Calendar.YEAR) - birthDay.get(Calendar.YEAR);
        if (year > 18) {
            return true;
        } else if (year < 18) {
            return false;
        }
        // 如果年相等，就比较月份
        Integer month = current.get(Calendar.MONTH) - birthDay.get(Calendar.MONTH);
        if (month > 0) {
            return true;
        } else if (month < 0) {
            return false;
        }
        // 如果月也相等，就比较天
        Integer day = current.get(Calendar.DAY_OF_MONTH) - birthDay.get(Calendar.DAY_OF_MONTH);
        return day >= 0;
    }

    /**
     * 读取网路文件流
     *
     * @param httpUrl
     * @return
     * @throws URISyntaxException
     */
    @SuppressWarnings("unchecked")
    public static byte[] getBytesFromUrl(String httpUrl) throws Exception {
        RestTemplate restTemplate = SpringFactoryUtil.getBean(RestTemplate.class);
        if (restTemplate == null) {
            restTemplate = new RestTemplate();
        }
        RequestEntity requestEntity = RequestEntity.get(new URI(httpUrl)).build();
        ResponseEntity responseEntity = restTemplate.exchange(requestEntity, byte[].class);
        return (byte[]) responseEntity.getBody();
    }

    /**
     * 通过订单号判断是否团险订单
     *
     * @return
     */
    public static boolean isGroupSafesOrder(String fhOrderId) {
        return fhOrderId.indexOf('_') > 0;
    }

    /**
     * 下划线转驼峰
     * toHump("cat_name") = "catName"
     * toHump("CAT_NAME") = "catName"
     */
    public static String toHump(String str) {
        String join = Stream.of(str.toLowerCase().split("_"))
                .map(a -> a.substring(0, 1).toUpperCase() + a.substring(1))
                .collect(Collectors.joining(""));
        return join.substring(0, 1).toLowerCase() + join.substring(1);
    }

    /**
     * 模板替换
     *
     * @param template
     * @param dict
     * @return
     */
    public static String strFormatUsingDict(String template, Map<String, Object> dict) {
        String patternString = "\\{(" + org.apache.commons.lang3.StringUtils.join(dict.keySet(), "|") + ")\\}";

        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(template);

        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, dict.getOrDefault(matcher.group(1), "").toString());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public static Map<String, String> getMap(String code, String name) {
        Map<String, String> jsonMap = new HashMap<>(2);
        jsonMap.put("code", code);
        jsonMap.put("name", name);
        return jsonMap;
    }

    /**
     * 数组形式json字符串转成List<String>
     *
     * @param str
     * @return
     */
    public static List<String> jsonStringToList(String str) {
        if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return null;
        }
        JSONArray arr = JSONArray.parseArray(str);
        return arr.toJavaList(String.class);
    }

    /**
     * 数组形式json字符串转成List<String>
     *
     * @param str
     * @return
     */
    public static List<Integer> jsonIntegerToList(String str) {
        if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return null;
        }
        JSONArray arr = JSONArray.parseArray(str);
        return arr.toJavaList(Integer.class);
    }

    public static String getTraceId() {
        Span span = Tracer.builder().getSpan();
        if (span != null) {
            return span.getTraceId();
        }
        return null;
    }

    /**
     * 计算被保人年龄
     *
     * @param birthDate
     * @param currentDate
     * @return
     */
    public static Integer calculateAge(LocalDate birthDate, LocalDate currentDate) {
        if (Objects.isNull(birthDate) || Objects.isNull(currentDate)) {
            return null;
        }
        Period period = Period.between(birthDate, currentDate);
        return period.getYears();
    }

    /**
     * 计算被保人年龄
     *
     * @param birthDate   日期格式：yyyy-MM-dd
     * @param currentDate
     * @return
     */
    public static Integer calculateAge(String birthDate, Date currentDate) {
        if (Objects.isNull(birthDate) || Objects.isNull(currentDate)) {
            return null;
        }
        if (birthDate.indexOf("*") > -1) {
            return null;
        }
        return calculateAge(LocalDate.parse(birthDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
    }

    public static  <T> T getCollectionFirst(List<T> list) {
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }
    
    public static void main(String[] arg) {

    }
}
