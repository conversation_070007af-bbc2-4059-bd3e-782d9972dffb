package com.cfpamf.ms.insur.admin.service.claim.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.claim.entity.ClaimImportHistoryQuery;
import com.cfpamf.ms.insur.admin.claim.entity.SmClaimImportResult;
import com.cfpamf.ms.insur.admin.constant.ClaimRiskType;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.dao.safes.AuthUserMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmClaimMapper;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimFinishNotifyMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimImportResultMapper;
import com.cfpamf.ms.insur.admin.dao.safes.claim.SmClaimReimbursementGtccMapper;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.admin.enums.EnumCompanyCode;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimFollowupType;
import com.cfpamf.ms.insur.admin.enums.claim.EnumClaimProcessType;
import com.cfpamf.ms.insur.admin.event.ClaimProcessNodeChangeEvent;
import com.cfpamf.ms.insur.admin.event.WxClaimNotifyEvent;
import com.cfpamf.ms.insur.admin.external.zhongan.api.ZaApiService;
import com.cfpamf.ms.insur.admin.external.zhongan.util.FileUtils;
import com.cfpamf.ms.insur.admin.label.enums.EventStrategyCaseVerifyEum;
import com.cfpamf.ms.insur.admin.pojo.dto.ProgressDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFileUnitDTO;
import com.cfpamf.ms.insur.admin.pojo.dto.SmClaimFollowUpDTO;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaim;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimEventStrategyPO;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimFinishNotify;
import com.cfpamf.ms.insur.admin.pojo.po.claim.SmClaimReimbursementGtcc;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimFollowUpListVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.SmClaimFollowupQueryVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimEvaluationExcelVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimEvaluationPageVo;
import com.cfpamf.ms.insur.admin.pojo.vo.claim.evaluation.SmClaimFollowUpExcelVo;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.admin.service.claim.ClaimEventStrategyQueryService;
import com.cfpamf.ms.insur.admin.service.claim.im.ClaimImport;
import com.cfpamf.ms.insur.admin.service.claim.impl.hasten.ClaimHastenProcessor;
import com.cfpamf.ms.insur.admin.util.TxUtil;
import com.cfpamf.ms.insur.base.config.ExecutorConfig;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.event.EventBusEngine;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BaseWorkflow;
import com.cfpamf.ms.insur.base.service.DLockTemplate;
import com.cfpamf.ms.insur.base.util.*;
import com.cfpamf.ms.insur.weixin.common.IdGenerator;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxClaimDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.claim.StepFinishNotifyDTO;
import com.cfpamf.ms.insur.weixin.pojo.vo.StepFinishReportVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxClaimDetailVO;
import com.cfpamf.ms.insur.weixin.service.WxCcClaimService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zhongan.filegateway.common.FileUploadResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/12/4 16:09
 * @Version 1.0
 */
@Service
@Slf4j
public class ClaimProcessDistinctServiceImpl {

    private static Set<Integer> forceProductIdList;

    /**
     * 事件工作流
     */
    @Lazy
    @Autowired
    protected EventBusEngine busEngine;

    /**
     * 众安开平产品
     */
    private static Set<Integer> zaKaiPingProductIdList;

    @Autowired
    private SmClaimMapper claimMapper;

    @Autowired
    private SmClaimTemplateInfoServiceImpl claimTemplateInfoService;

    @Autowired
    private ClaimWorkflow claimWorkflow;
    /**
     * Redis缓存
     */
    @Autowired
    private RedisUtil<String, Integer> redisUtil;

    @Autowired
    private ClaimServiceConfig claimServiceConfig;

    @Autowired
    private ZaApiService zaApiService;

    @Autowired
    private SmClaimServiceImpl claimService;


    @Autowired
    private ClaimEventStrategyQueryService claimEventStrategyQueryService;

    @Value("${claim.force.product-id}")
    public void setForceProductIdList(Set<Integer> forceProductIdList) {
        ClaimProcessDistinctServiceImpl.forceProductIdList = forceProductIdList;
    }

    @Value("${claim.kaiping.product-id}")
    public void setZaKaiPingProductIdList(Set<Integer> forceProductIdList) {
        ClaimProcessDistinctServiceImpl.zaKaiPingProductIdList = forceProductIdList;
    }

    @Autowired
    private SmClaimImportResultMapper claimImportResultMapper;

    /**
     * 判断流程类型的入口
     *
     * @param deduceContextParams
     * @return
     */
    public String deduceClaimProcessType(ClaimDeduceContextParams deduceContextParams) {

        if (Objects.isNull(deduceContextParams)) {
            throw new BizException(ExcptEnum.DATA_NOT_EXISTS.getCode(), "理赔信息缺失");
        }

//        if (forceProductIdList.contains(deduceContextParams.getProductId())) {
//            return EnumClaimProcessType.MAIL.getCode();
//        }

        if (zaKaiPingProductIdList.contains(deduceContextParams.getProductId())) {
            return EnumClaimProcessType.ZA_KAI_PING_API.getCode();
        }


        if (Objects.equals(EnumCompanyCode.C2056.getId(), deduceContextParams.getCompanyId())
                || Objects.equals(EnumChannel.ZA.getCode(), deduceContextParams.getChannel())) {
            return EnumClaimProcessType.ZA_API.getCode();
        }

        if (Objects.equals(deduceContextParams.getCompanyId(), EnumCompanyCode.GTCC.getId())) {
            return EnumClaimProcessType.GUO_TAI_API.getCode();
        }

        Boolean mailTemPlate = claimTemplateInfoService.judgeTemplateExists(deduceContextParams.getInsuredId(), deduceContextParams.getRiskType());

        if (mailTemPlate) {
            return EnumClaimProcessType.MAIL_TEMPLATE.getCode();
        }
        return EnumClaimProcessType.MAIL.getCode();
    }


    public String deduceByByInsuredId(ClaimDeduceContextParams p, String riskType) {
//        ClaimDeduceContextParams p = claimMapper.deduceByInsuredId(insuredId);

        if (Objects.isNull(p)) {
            throw new BizException(ExcptEnum.DATA_NOT_EXISTS.getCode(), "理赔信息缺失");
        }
        p.setRiskType(riskType);
        return deduceClaimProcessType(p);
    }

    /**
     * 小额保险订单mapper
     */
    @Autowired
    private SmOrderMapper orderMapper;


    /**
     * 理赔工作流
     */
    @Autowired
    protected ClaimWorkflow workflow;

    @Autowired
    private ClaimHastenProcessor hastenProcessor;

    /**
     * 微信理赔发生初始化
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public WxClaimDTO appearAndReport(WxClaimDTO dto) {
        log.info("WxClaimDTO request data: {}", JSONObject.toJSONString(dto));
        dto.setClaimNo(getNextClaimNo());
        if (StrUtil.isBlank(dto.getProcessType())) {
            ClaimDeduceContextParams p = claimMapper.deduceByInsuredId(dto.getInsuredId());
            dto.setProcessType(deduceByByInsuredId(p, dto.getRiskType()));
        }


        //初始化理赔
        if (ClaimRiskType.isImportantClaim(dto.getRiskType())) {
            // 出险类型为：残疾、意外身故、疾病身故、重大疾病时，才进入待指导报案阶段
            ClaimWorkflow.Step stepGuide = workflow.getStepByCode(ClaimWorkflow.STEP_GUIDE);
            if (stepGuide != null) {
                dto.setClaimState(stepGuide.getSCode());
                dto.setClaimResult(stepGuide.getSName());
            }
        } else {
            // 其余的案件直接进入待提交资料列表。
            ClaimWorkflow.Step stepDataPrepare = workflow.getStepByCode(ClaimWorkflow.STEP_DATA_PREPARE);
            if (stepDataPrepare != null) {
                dto.setClaimState(stepDataPrepare.getSCode());
                dto.setClaimResult(stepDataPrepare.getSName());
            }
        }

        //构建理赔流程数据
        SmOrderInsuredVO orderInsured = orderMapper.getOrderInsuredByInsId(dto.getInsuredId());

        SmBaseOrderVO smBaseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderInsured.getFhOrderId());
        if (Objects.nonNull(smBaseOrderVO)) {
            String customerAdminId = smBaseOrderVO.getCustomerAdminId();
            //匹配理赔专员
            dto.setSettlement(mapSettlement(customerAdminId));
        }

        //保存理赔信息
        claimMapper.insertSmClaim(dto);
        hastenProcessor.updateExpectDays(dto.getClaimState(), dto.getId());
        ClaimProgressService claimProgressService = claimServiceConfig.findClaimByChannelCode(dto.getProcessType());
        claimProgressService.callbackWhenReport(dto);

        ClaimWorkflow.Step stepAppear = workflow.getStepByCode(ClaimWorkflow.STEP_APPEAR);
        ProgressDTO pDto = new ProgressDTO();
        pDto.setClaimId(dto.getId());
        // 出险操作人取被保人名称
        String tempCreateBy = dto.getCreateBy();
        pDto.setCreateBy(orderInsured.getPersonName());
        BaseCommonClaimService.convertStepToDTO(stepAppear, pDto);
        pDto.setOTime(DateUtil.parseDate(dto.getRiskTime(), "yyyy-MM-dd HH:mm"));
        //保存出险理赔流程
        claimMapper.insertProgress(pDto);

        ClaimWorkflow.Step stepReport = workflow.getStepByCode(ClaimWorkflow.STEP_REPORT);
        pDto.setOTime(new Date());
        BaseCommonClaimService.convertStepToDTO(stepReport, pDto);
        // 其余的案件直接进入待提交资料列表。
        if (!(ClaimRiskType.isImportantClaim(dto.getRiskType()))) {
            // 出险类型为：残疾、身故、重大疾病时，才进入待指导报案阶段
            pDto.setOCode(ClaimWorkflow.OPTION_CODE_REPORT2);
            pDto.setOName("申请报案");
        }
        pDto.setCreateBy(tempCreateBy);
        //保存报案理赔流程
        claimMapper.insertProgress(pDto);

        // 申请报案直接进入提交资料微信推送提交资料消息
        try{
            String oCode = null;
            if (!(ClaimRiskType.isImportantClaim(dto.getRiskType()))) {
                // 出险类型为：残疾、身故、重大疾病时，才进入待指导报案阶段
                oCode=ClaimWorkflow.OPTION_CODE_REPORT2;
            }
            SpringFactoryUtil.getBean(EventBusEngine.class).post(new WxClaimNotifyEvent(dto.getId(), oCode, null, null));
        } catch(Exception e) {
            log.info("理赔报案消息推送异常-{}", e);
        }

        if (ClaimRiskType.isImportantClaim(dto.getRiskType())) {
            ProgressDTO guidePDto = new ProgressDTO();
            guidePDto.setClaimId(dto.getId());
            guidePDto.setSCode(ClaimWorkflow.STEP_GUIDE);
            guidePDto.setSName("指导报案");
            guidePDto.setOCode(ClaimWorkflow.OPTION_CODE_GUIDED);
            guidePDto.setOName("指导报案");
            guidePDto.setOType("2");
            ClaimRiskType importantRiskType = ClaimRiskType.getClaimRiskTypeByCode(dto.getRiskType());
            if (Objects.nonNull(importantRiskType)) {
                guidePDto.setOValue(importantRiskType.getGuideContent());
            }


            if (Objects.isNull(importantRiskType) || StringUtils.isEmpty(importantRiskType.getGuideContent())) {
                log.error("重大案件-{}未匹配到话术，请关注！", dto.getRiskType());
            }

            claimProgressService.saveProgress(guidePDto);
        }

        //添加案件流程节点事件触发器 add by zhangjian 2024-10-29
        busEngine.publish(new ClaimProcessNodeChangeEvent(dto.getId()));
        return dto;
    }

    public EventStrategyCaseVerifyEum getClaimEventStrategy(Integer claimId){
        List<SmClaimEventStrategyPO> strategyPOList = claimEventStrategyQueryService.listEventStrategyByClaimId(claimId);
        if(CollectionUtils.isEmpty(strategyPOList)){
            return EventStrategyCaseVerifyEum.PERSON;
        }
        Optional<SmClaimEventStrategyPO> personOpt = strategyPOList.stream().filter(o->Objects.equals(o.getCaseVerify(),EventStrategyCaseVerifyEum.PERSON.getCode())).findFirst();
        Optional<SmClaimEventStrategyPO> aiOpt = strategyPOList.stream().filter(o->Objects.equals(o.getCaseVerify(),EventStrategyCaseVerifyEum.AI.getCode())).findFirst();
        //todo 一个案件id命中多个事件策略，即存在人工审核又存在自动审核的策略的话，目前优先人工审核，后续需要产品给出一个规则
        if(personOpt.isPresent()){
            return EventStrategyCaseVerifyEum.PERSON;
        }
        if(aiOpt.isPresent()){
            return EventStrategyCaseVerifyEum.AI;
        }
        return EventStrategyCaseVerifyEum.PERSON;
    }

    /**
     * 获取下一个理赔编号
     *
     * @return
     */
    public String getNextClaimNo() {
        String tds = DateUtil.format(new Date(), BaseConstants.DATE_FORMAT_YYYYMMDD);
        String redisClaimKey = SmConstants.REDIS_KEY_CLAIM_NO + tds;
        Integer oldNo = redisUtil.get(redisClaimKey);
        if (oldNo == null) {
            DLockTemplate lockTemplate = SpringFactoryUtil.getBean(DLockTemplate.class);
            String dLockKey = "claim_no" + tds;
            try{
                lockTemplate.lock(dLockKey, 2);
                String maxClaimNo = claimMapper.getMaxClaimNo();
                if (maxClaimNo == null || !Objects.equals(maxClaimNo.substring(2, 10), tds)) {
                    redisUtil.setnx(redisClaimKey, 0);
                } else {
                    oldNo = Integer.valueOf(maxClaimNo.substring(10));
                    redisUtil.setnx(redisClaimKey, oldNo);
                }
                redisUtil.expire(redisClaimKey, CommonUtil.getTodayNextSeconds());
            } finally {
                lockTemplate.unLock(dLockKey);
            }
        }
        long newNo = redisUtil.increment(redisClaimKey, 1);
        return String.format(SmConstants.CLAIM_NO_PREFIX + tds + "%03d", newNo);
    }

    @Autowired
    private AuthUserMapper authUserMapper;

    /**
     * 字典service
     */
    @Autowired
    private DictionaryService dictService;

    @Autowired
    private ClaimHastenProcessor claimHastenProcessor;

    public String mapSettlement(String customerAdminId) {

        AuthUserVO authUserVO = authUserMapper.getAuthUserByUserId(customerAdminId);
        if(Objects.isNull(authUserVO)) {
            return null;
        }
        List<DictionaryVO> dictionaryVOList = dictService.listByType("claim_settlement_map");
        if (CollectionUtils.isEmpty(dictionaryVOList)) {
            return null;
        }

        //区域-理赔专员
        Map<String, String> settlementMap = dictionaryVOList.stream().collect(
                Collectors.toMap(DictionaryVO::getName, y -> y.getCode().split("-")[0], (t1, t2) -> t1)
        );

        if (settlementMap.containsKey(authUserVO.getRegionName())) {
            AuthUserVO userVO = authUserMapper.getAuthUserByUserId(settlementMap.get(authUserVO.getRegionName()));
            if (Objects.nonNull(userVO)) {
                return userVO.getUserName();
            }
        }

        return null;

    }


    public void autoPassStepToPay(int claimId) {
        autoPassStepToPayWithOname(claimId, "系统自动过审");
    }

    private void autoPassStepToPayWithOname(int claimId, String oName) {
        ProgressDTO progressStepToPay = new ProgressDTO();
        progressStepToPay.setClaimId(claimId);
        progressStepToPay.setSCode(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
        progressStepToPay.setSName("总部审核");
        progressStepToPay.setOName(oName);
        progressStepToPay.setOCode(ClaimWorkflow.OPTION_CODE_CHECK_BY_SAFES_CENTER);
        progressStepToPay.setOTime(new Date());
        claimMapper.insertProgress(progressStepToPay);
    }


    /**
     * 直接跳转到保司审核中
     *
     * @param claim
     */
    public void autoPassStepToPayAndUpdate(SmClaim claim) {
        autoPassStepToPayAndUpdateWithOname(claim, "系统自动过审");
    }

    public void autoPassStepToPayAndUpdateWithOname(SmClaim claim, String oName) {

        if (Objects.isNull(claim)) {
            throw new BizException("", "理赔信息缺失");
        }

        if (Objects.equals(claim.getClaimState(), ClaimWorkflow.STEP_TO_PAY)) {
            return;
        }

        Integer claimId = claim.getId();
        autoPassStepToPayWithOname(claimId, oName);


        // 更新邮寄资料时间/结案时间/邮寄纸质资料时间 当前环节开始时间/ 保险业务中心审核的次数
        BaseWorkflow.Step nextStep = workflow.getStepByCode(ClaimWorkflow.STEP_TO_PAY);
        claimMapper.updateClaimStatus(
                claimId
                , ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER
                , "system"
                , null
                , nextStep
                , claimHastenProcessor.calHastenTime(nextStep.getSCode(), claimId)
        );
        if (Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_TO_PAY) ||
                Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE) ||
                Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE2) ||
                Objects.equals(nextStep.getSCode(), ClaimWorkflow.STEP_DATA_PREPARE3)
        ) {
            claimHastenProcessor.updateExpectDays(nextStep.getSCode(), claimId);
        }

    }


//    public static void setDetail(WxClaimDetailVO claimDetailVO) {
//
//        if (Objects.isNull(claimDetailVO)) {
//            return;
//        }
//
//        if (StringUtils.isEmpty(claimDetailVO.getProcessType())) {
//            ClaimDeduceContextParams p = new ClaimDeduceContextParams();
//            p.setChannel(x.getChannel());
//            p.setCompanyId(x.getCompanyId());
//            p.setProductId(x.getProductId());
//            x.setProcessType(deduceClaimProcessType(p));
//        }
//    }


    public SmClaim getByClaimId(Integer claimId) {
        return claimMapper.getByClaimId(claimId);
    }

    public void uploadToZa(List<SmClaimFileUnitVO> supplementsFileList) {

        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(supplementsFileList)) {
            List<SmClaimFileUnitVO> updateFiles = supplementsFileList.parallelStream()
                    .filter(fileUnitVO -> StrUtil.isEmpty(fileUnitVO.getZaKey()))
                    .map(
                            fileUnitVO -> {
                                //线程上传一下
                                File file = FileUtils.downNetImage(fileUnitVO.getFileUrl(), IdGenerator.getUuid(), ".jpg");
                                //上传到众安文件服务
                                if (Objects.nonNull(file)) {
                                    FileUploadResponse response = zaApiService.uploadFile(file.getPath());
                                    if (Objects.isNull(response.getData()) || !Objects.equals(response.getCode(), "0000")) {
                                        throw new BizException("", "文件上传到众安网关失败");
                                    }
                                    SmClaimFileUnitVO result = new SmClaimFileUnitVO();
                                    result.setCfId(fileUnitVO.getCfId());
                                    result.setZaKey(response.getData());
                                    fileUnitVO.setZaKey(response.getData());
                                    return result;
                                }
                                throw new BizException("", "文件上传至众安时下载失败");
                            }
                    ).collect(Collectors.toList());

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(updateFiles)) {
                return;
            }

            for (SmClaimFileUnitVO updateFile : updateFiles) {
                claimMapper.updateClaimFileZaKey(updateFile.getCfId(), updateFile.getZaKey());
            }

        }
    }



    @Autowired
    private TransactionTemplate transactionTemplate;

    public void handleHistoryFileApproveNode(List<Integer> claimIds) {

        List<SmClaim> claimList = null;
        if (CollectionUtil.isEmpty(claimIds)) {
            claimIds = claimMapper.queryHistoryHandeledClaim();
        }
        claimList = claimMapper.queryByIdList(claimIds);

        if (CollectionUtil.isEmpty(claimList)) {
            return;
        }

        for (SmClaim claim : claimList) {
            TxUtil.getInstance(transactionTemplate).doTransactionWithoutResult(
                    () -> {
                        handleClaimFile(claim);
                    }
            );
        }

    }

    private void handleClaimFile(SmClaim claim) {

        log.info("处理历史文件消息-{}", claim.getId());
        List<SmClaimFileUnitVO> list = claimMapper.listSmClaimFileUnitByClaimId(claim.getId());
        if (CollectionUtil.isEmpty(list)) {
            log.info("文件列表为空-{}", claim.getId());
            return;
        }

        List<ProgressVO> progressList = claimMapper.listSmClaimProgressList(claim.getId());

        List<ProgressVO> rejectProgressList = progressList.stream()
                .filter(x -> Objects.equals(x.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA))
                .sorted(Comparator.comparingInt(ProgressVO::getProgressId))
                .collect(Collectors.toList());


        Set<String> progressSet = progressList.stream().map(ProgressVO::getSCode).collect(Collectors.toSet());

        Set<String> rejectSet = progressList.stream().filter(
                x -> Objects.equals(x.getOCode(), ClaimWorkflow.OPTION_CODE_MORE_DATA)
        ).filter(x -> StrUtil.isNotEmpty(x.getOCode()))
                .map(ProgressVO::getSCode)
                .collect(Collectors.toSet());


        //无驳回记录的情况
        if (CollectionUtil.isEmpty(rejectSet)) {

            log.info("无驳回记录-{}", claim.getId());
            Set<String> approveNode = new HashSet<>();

            if (progressSet.contains(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC)) {
                approveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
            }

            if (progressSet.contains(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
                approveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                approveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
            }

            if (progressSet.contains(ClaimWorkflow.STEP_TO_PAY)) {
                approveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                approveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
                approveNode.add(ClaimWorkflow.STEP_TO_PAY);
            }
            log.info("无驳回记录流程节点为-{}", progressSet);


            if (CollectionUtil.isNotEmpty(approveNode)) {

                List<Integer> collect = list.stream().filter(x -> StrUtil.isNotEmpty(x.getFileApproveNode()))
                        .map(SmClaimFileUnitVO::getCfId).collect(Collectors.toList());

                log.info("无驳回记录操作流程节点为-{}, 文件id-{}", approveNode, collect);
                if (CollectionUtil.isNotEmpty(collect)) {
                    claimMapper.tagFile(
                            new JSONArray(new ArrayList<>(approveNode)).toJSONString()
                            , collect
                    );
                }


            }

            return;

        }

        if (!rejectSet.isEmpty()) {

            log.info("操作理赔驳回记录-{}", claim.getId());
            Set<String> oldFlagApproveNode = new HashSet<>();
            Set<String> newFlagApproveNode = new HashSet<>();

            Map<Integer, List<SmClaimFileUnitVO>> newSignMap =
                    list.stream().filter(x -> StrUtil.isNotEmpty(x.getFileApproveNode()))
                            .filter(x -> Objects.nonNull(x.getNewFlag()))
                            .collect(
                                    Collectors.groupingBy(SmClaimFileUnitVO::getNewFlag)
                            );

            //有驳回记录的，且只有机构
            if (rejectSet.contains(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC)) {
                //更新newFlag=0的文件
                oldFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);

            }

            if (rejectSet.contains(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
                //更新newFlag=0的文件
                oldFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                oldFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
            }

            if (rejectSet.contains(ClaimWorkflow.STEP_TO_PAY)) {
                //更新newFlag=0的文件
                oldFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                oldFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
                oldFlagApproveNode.add(ClaimWorkflow.STEP_TO_PAY);
            }

            if (newSignMap.containsKey(0)) {

                List<Integer> collect = newSignMap.get(0).stream().map(SmClaimFileUnitVO::getCfId).collect(Collectors.toList());

                log.info("old驳回记录操作流程节点为-{}, 文件id-{}", oldFlagApproveNode, collect);

                if (CollectionUtil.isNotEmpty(collect)) {
                    claimMapper.tagFile(
                            new JSONArray(new ArrayList<>(oldFlagApproveNode)).toJSONString()
                            , collect
                    );
                }

            }

            //根据当前状态来判断是否审核过，更新newFlag=1的文件
            //有驳回记录的，且只有机构
            if (Objects.equals(claim.getClaimState(), ClaimWorkflow.STEP_DATA_CHECK_BY_PIC)) {
                //无操作
            } else if (Objects.equals(claim.getClaimState(), ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
                //更新newFlag=0的文件
                newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
            } else if (Objects.equals(claim.getClaimState(), ClaimWorkflow.STEP_TO_PAY)) {
                //更新newFlag=0的文件
                newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
            } else if (
                    Sets.newHashSet(
                            ClaimWorkflow.STEP_DATA_PREPARE
                            , ClaimWorkflow.STEP_DATA_PREPARE2
                            , ClaimWorkflow.STEP_DATA_PREPARE3).contains(claim.getClaimState())
            ) {
                //查找上一次的驳回节点
                if (CollectionUtil.isNotEmpty(rejectProgressList)) {
                    String lastRejectCode = rejectProgressList.get(rejectProgressList.size() - 1).getSCode();
                    if (Objects.equals(lastRejectCode, ClaimWorkflow.STEP_DATA_CHECK_BY_PIC)) {
                        newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                    } else if (Objects.equals(lastRejectCode, ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER)) {
                        newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                        newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
                    } else if (Objects.equals(lastRejectCode, ClaimWorkflow.STEP_TO_PAY)) {
                        newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                        newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
                        newFlagApproveNode.add(ClaimWorkflow.STEP_TO_PAY);
                    }
                }
            } else {
                newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_PIC);
                newFlagApproveNode.add(ClaimWorkflow.STEP_DATA_CHECK_BY_SAFES_CENTER);
                newFlagApproveNode.add(ClaimWorkflow.STEP_TO_PAY);

            }

            if (newSignMap.containsKey(1)) {

                List<Integer> collect = newSignMap.get(1).stream().map(SmClaimFileUnitVO::getCfId).collect(Collectors.toList());
                log.info("new驳回记录操作流程节点为-{}, 文件id-{}", newFlagApproveNode, collect);
                if (CollectionUtil.isNotEmpty(collect)) {
                    claimMapper.tagFile(
                            new JSONArray(new ArrayList<>(newFlagApproveNode)).toJSONString()
                            , collect
                    );
                }

            }


        }

    }

    public void manualHandleNode(Set<String> newFlagApproveNodeSet, List<String> cfIdList) {

        List<Integer> cfIdCovList =  cfIdList.stream().map(Integer::valueOf).collect(Collectors.toList());
        claimMapper.tagFile(
                new JSONArray(new ArrayList<>(newFlagApproveNodeSet)).toJSONString()
                , cfIdCovList
        );
    }

    public void hastenReply(SmClaimFollowUpDTO follow) {

        int claimId = follow.getClaimId();
        SmClaim claim =  claimMapper.getByClaimId(claimId);

        follow.setType(EnumClaimFollowupType.REPLY.getCode());
        if (Objects.isNull(claim)) {
            throw new BizException("", "理赔信息不存在");
        }

        follow.setCurrentNode(claim.getClaimState());

        claimMapper.insertClaimFollowUp(follow);

    }


    public PageInfo<SmClaimFollowUpListVo> followUpPageList(SmClaimFollowupQueryVo followupQueryVo) {
        PageInfo<SmClaimFollowUpListVo> followUpListVoPageInfo = PageHelper.startPage(followupQueryVo.getPage(), followupQueryVo.getSize())
                .doSelectPageInfo(
                        () -> claimMapper.pageFollowupList(followupQueryVo)
                );
        if (CollectionUtil.isNotEmpty(followUpListVoPageInfo.getList())) {

//            List<Integer> claimIdList = followUpListVoPageInfo.getList().stream().map(SmClaimFollowUpListVo::getClaimId).collect(Collectors.toList());
//            List<SmClaimFollowUpVO> followUpVOList = claimMapper.listClaimFollowUpListByIdList(claimIdList);
//            Map<Integer, List<SmClaimFollowUpVO>> hastenMap = Optional.ofNullable(followUpVOList)
//                    .orElse(Collections.emptyList())
//                    .stream()
//                    .collect(
//                            Collectors.groupingBy(SmClaimFollowUpVO::getClaimId)
//                    );

            for (SmClaimFollowUpListVo followUpListVo : followUpListVoPageInfo.getList()) {
                followUpListVo.setCurrentStateName(followUpListVo.getCurrentState());
                BaseWorkflow.Step step = claimWorkflow.getStepByCode(followUpListVo.getCurrentState());
                if (Objects.nonNull(step)) {
                    followUpListVo.setCurrentStateName(step.getSName());
                }
//                if (hastenMap.containsKey(followUpListVo.getClaimId())) {
//                    followUpListVo.setFollowUpVOList(hastenMap.get(followUpListVo.getClaimId()));
//                }
            }

        }
        return followUpListVoPageInfo;
    }
    public void downloadFollowUp(SmClaimFollowupQueryVo followupQueryVo, HttpServletResponse response) {
        log.info("开始导入理赔咨询列表:{}", JSONObject.toJSONString(followupQueryVo));
        int pageSize = 5000;
        followupQueryVo.setPage(1);
        followupQueryVo.setSize(pageSize);
        String fileName = "理赔咨询列表";
        Class clazz = SmClaimFollowUpExcelVo.class;
        try (OutputStream os = response.getOutputStream()) {
            PageInfo<SmClaimFollowUpListVo> pageInfo = this.followUpPageList(followupQueryVo);
            if (pageInfo.getTotal() > 65535) {
                log.warn("客户下载{}", ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg());
                response.setContentType("text/html;charset=UTF-8");
                os.write(ExcptEnum.EXCEL_MAX_LIMIT_ERROR.getMsg().getBytes(StandardCharsets.UTF_8.name()));
                os.flush();
                return;
            }

            response.setHeader("Content-disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.name()) + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xls");
            response.setContentType("application/octet-stream");
            ExcelBuilderUtil excelReader = ExcelBuilderUtil.newInstance()
                    .createSheet("理赔咨询列表")
                    .buildSheetHead(clazz);
            int nextPage = 1;
            long maxPage = pageInfo.getTotal() / followupQueryVo.getSize() + 1;
            while (nextPage <= maxPage) {
                followupQueryVo.setPage(nextPage);
                followupQueryVo.setQueryPage(false);
                pageInfo = this.followUpPageList(followupQueryVo);
                List<SmClaimFollowUpListVo> pageList = pageInfo.getList();
                if(CollectionUtil.isEmpty(pageList)){
                    break;
                }
                excelReader.addSheetData(convertVo(pageList));
                nextPage++;
            }
            excelReader.write(os);
        } catch (Exception e) {
            log.info("文件导出异常",e);
            throw new BizException(ExcptEnum.FILE_DOWNLOAD_FAIL_501006, e);
        }
    }

    private List<SmClaimFollowUpExcelVo> convertVo(List<SmClaimFollowUpListVo> pageList) {
        return pageList.stream()
                .map(entry->{
                    SmClaimFollowUpExcelVo vo = new SmClaimFollowUpExcelVo();
                    BeanUtils.copyProperties(entry,vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }


    public void updateFileInfo(SmClaimFileUnitDTO fileUnitDTO) {
        claimMapper.updateFileInfo(fileUnitDTO);
    }

    public void updateProgressInfo(ProgressDTO progressDTO) {
        claimMapper.updateProgressInfo(progressDTO);
    }



    public void updateHistoryTimeout() {
        claimMapper.updateHistory();
    }


    public ClaimProgressService getClaimProgress(String processType) {
        return claimServiceConfig.findClaimByChannelCode(processType);
    }

    @Autowired
    @Qualifier(ExecutorConfig.IMPORT_EXECUTOR)
    AsyncTaskExecutor taskExecutor;
    String lockKey = "CLAIM_IMPORT_CASE_CLOSED";

    @Autowired
    private DLockTemplate dLockTemplate;

    private Map<String, ClaimImport<?>> importServiceMap;
    @Autowired
    public void initImportService(@Autowired List<ClaimImport<?>> list) {
        this.importServiceMap = list.stream().collect(Collectors.toMap(ClaimImport::getType, Function.identity()));
    }

    public String parseCaseClosedFile(String url, String type) {

        ClaimImport<?> claimImport = importServiceMap.get(type);
        if (Objects.isNull(claimImport)) {
            throw new BizException("", "导入项目不存在");
        }
        boolean locked = false;

        locked = dLockTemplate.tryLock(type, 60 * 10);
        if (!locked) {
            throw new BizException("", "有存在清单导入处理中，请稍后");
        }

        String handle_batch_id = UUID.randomUUID().toString().replace("-", "");
//        if (CollectionUtils.isEmpty(finalClosedSuccessImportArrayList)) {
//            throw new BizException("", "请勿导入空文件");
//        }
        String userName = HttpRequestUtil.getUserName();
        taskExecutor.execute(() -> {
            try{
                SmClaimImportResult importResult = claimImport.parseFile(url, userName);
                if (Objects.nonNull(importResult)) {
                    importResult.setBatchId(handle_batch_id);
                    importResult.setBusinessType(type);
                    importResult.setImportUrl(url);
                    importResult.setFileName(FileUtil.getName(url));
                    importResult.setCreateBy(userName);
                    claimImportResultMapper.insert(importResult);
                }
            } catch(Exception e) {

                SmClaimImportResult importResult = new SmClaimImportResult();
                importResult.setBatchId(handle_batch_id);
                importResult.setBusinessType(type);
                importResult.setImportUrl(url);
                importResult.setFileName(FileUtil.getName(url));
                importResult.setRemark(e.getMessage());
                importResult.setCreateBy(userName);
                claimImportResultMapper.insert(importResult);
                log.warn("导入案件失败-{}", e.getMessage());
            } finally {
                dLockTemplate.unLock(type);
            }

        });
        return handle_batch_id;

    }

    public PageInfo<SmClaimImportResult> pageImportHistory(ClaimImportHistoryQuery query) {

        PageHelper.startPage(query.getPage(), query.getSize());
        List<SmClaimImportResult> claimList = claimImportResultMapper.pageList(query);
        return new PageInfo<>(claimList);

    }

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private SmClaimReimbursementGtccMapper gtccMapper;

    public StepFinishReportVO getStepFinish(Integer claimId) {
        if (Objects.isNull(claimId)) {
            return null;
        }
        WxClaimDetailVO claimDetailVO = SpringFactoryUtil.getBean(WxCcClaimService.class).getWxClaimById(claimId, null, null);

        if (Objects.isNull(claimDetailVO)) {
            throw new BizException("", "未查到结案详情信息");
        }

        StepFinishReportVO finishReportVO = new StepFinishReportVO();
        BeanUtil.copyProperties(claimDetailVO, finishReportVO);
        List<ProgressVO> progressList = claimMapper.listSmClaimProgressList(claimId);
        ProgressVO progressVO = null;
        if (Objects.equals(finishReportVO.getFinishState(), ClaimWorkflow.FINISH_STATE_PAYED)) {
            progressVO =  progressList.stream().filter(x -> Objects.equals(x.getOCode(), ClaimWorkflow.FINISH_STATE_PAYED)).reduce((t1, t2) -> t1.getProgressId() > t2.getProgressId() ? t1 : t2).orElse(null);
            if (Objects.isNull(progressVO)) {
                return null;
            }
            finishReportVO.setInsuranceClaimConclusion("正常赔付");
        } else if (Objects.equals(finishReportVO.getFinishState(), ClaimWorkflow.FINISH_STATE_PAYREJECTED)) {
            progressVO =  progressList.stream().filter(x -> Objects.equals(x.getOCode(), ClaimWorkflow.FINISH_STATE_PAYREJECTED)).reduce((t1, t2) -> t1.getProgressId() > t2.getProgressId() ? t1 : t2).orElse(null);
            Map<String, String> typeMap = dictionaryService
                    .groupByTypes(Lists.newArrayList("CLAIM_FINISH_TYPE", "CLAIM_FINISH_REJECT_TYPE", "CLAIM_FINISH_CANCEL_TYPE"))
                    .values().stream().flatMap(Collection::stream).collect(Collectors.toMap(DictionaryVO::getCode, DictionaryVO::getName, (t1, t2) -> t1));
            if (Objects.isNull(progressVO)) {
                return null;
            }
            finishReportVO.setRejectType(typeMap.get(finishReportVO.getFinishType()));
            finishReportVO.setInsuranceClaimConclusion("无法赔付");
            finishReportVO.setRejectReason(getProgressRejectMemo(progressVO, finishReportVO));
        }

        if (Objects.equals(finishReportVO.getProcessType(), EnumClaimProcessType.GUO_TAI_API.getCode())) {
            SmClaimReimbursementGtcc reimbursementGtcc = gtccMapper.selectByClaimId(claimId);
            if (Objects.nonNull(reimbursementGtcc)) {
                finishReportVO.setRiskAddress(reimbursementGtcc.getAccidentDetailAddress());
            }

        }

        if (Objects.isNull(progressVO)) {
            throw new BizException("", "未查到结案节点信息");
        }

        finishReportVO.setTriggerFinishReportTime(new Date());
        finishReportVO.setRiskTypeName(ClaimRiskType.getNameByCode(finishReportVO.getRiskType()));
        if (Objects.nonNull(finishReportVO.getFinishTime())) {
            finishReportVO.setCloseDate(finishReportVO.getFinishTime());
        }
        finishReportVO.setCloseDate(progressVO.getCreateTime());
        StepFinishNotifyDTO finishNotifyDTO = selectFinishNotifyByClaimId(claimId);
        if (Objects.nonNull(finishNotifyDTO)) {
            BeanUtils.copyProperties(finishNotifyDTO, finishReportVO);
        }
        return finishReportVO;
    }


    @Autowired
    ObjectMapper objectMapper;

    public String getProgressRejectMemo(ProgressVO memo, StepFinishReportVO claimVO) {
        if (Objects.isNull(memo)) {
            return "";
        }
        try{
            if (Objects.equals(claimVO.getProcessType(), EnumClaimProcessType.ZA_API.getCode())) {
                return Optional.ofNullable(objectMapper.readTree(memo.getDataJson()).get("refuseReason")).map(JsonNode::asText).orElseGet(memo::getOValue);
            }
            if (Objects.equals(claimVO.getProcessType(), EnumClaimProcessType.GUO_TAI_API.getCode())) {
                return Optional.ofNullable(objectMapper.readTree(memo.getDataJson()).get("refuseReasonName")).map(JsonNode::asText).orElseGet(memo::getOValue);
            }
        } catch(IOException e) {
            log.warn("获取结案失败", e);
        }
        return memo.getOValue();
    }

    @Autowired
    private DLockTemplate lockTemplate;

    @Autowired
    SmClaimFinishNotifyMapper finishNotifyMapper;
    @Transactional(rollbackFor = Exception.class)
    public String addFinishNotify(StepFinishNotifyDTO finishNotifyDTO) {
        if (Objects.isNull(finishNotifyDTO.getId())) {
            throw new BizException("", "理赔信息不存在");
        }
        SmClaimFinishNotify finishNotify = finishNotifyMapper.selectByClaimId(finishNotifyDTO.getId());
        SmClaimFinishNotify addObj = new SmClaimFinishNotify();
        String dLockKey = "claim:finish:notify:lock";
        String redisKey = "claim:finish:notify:num";

        if (Objects.isNull(finishNotify)) {
            try {
                long nextNo;
                lockTemplate.lock(dLockKey, 2);
                Integer num = redisUtil.get(redisKey);
                if (Objects.isNull(num)) {
                    String maxNo = finishNotifyMapper.maxNo();
                    if (StrUtil.isEmpty(maxNo) || !StrUtil.sub(maxNo, 4, 8).equals(String.valueOf(cn.hutool.core.date.DateUtil.date().year()))) {
                        nextNo = Long.parseLong(StrUtil.format("{}{}", cn.hutool.core.date.DateUtil.date().year(), String.format("%06d", 1)));
                    } else {
                        nextNo = Integer.parseInt(StrUtil.sub(maxNo, 8, maxNo.length())) + 1;
                    }
                }else {
                    if(StrUtil.sub(num.toString(), 0, 4).equals(String.valueOf(cn.hutool.core.date.DateUtil.date().year()))) {
                        nextNo = num + 1;
                    } else {
                        nextNo = Long.parseLong(StrUtil.format("{}{}", cn.hutool.core.date.DateUtil.date().year(), String.format("%06d", 1)));
                    }
                }
                redisUtil.set(redisKey, (int) nextNo);
                addObj.setFinishReportNo(StrUtil.format("LPJA{}", nextNo));
            } finally {
                lockTemplate.unLock(dLockKey);
            }

            addObj.setClaimId(finishNotifyDTO.getId());
        } else {
            SmClaimFinishNotify updateObj = new SmClaimFinishNotify();
            updateObj.setId(finishNotify.getId());
            updateObj.setEnabledFlag(1);
            updateObj.setUpdateBy(HttpRequestUtil.getUserId());
            finishNotifyMapper.updateByPrimaryKeySelective(updateObj);
            addObj.setFinishReportNo(finishNotify.getFinishReportNo());
            addObj.setClaimId(finishNotifyDTO.getId());
        }
        addObj.setCreateBy(HttpRequestUtil.getUserId());
        addObj.setRiskAddress(StrUtil.isBlank(finishNotifyDTO.getRiskAddress()) ? null : finishNotifyDTO.getRiskAddress());
        addObj.setRiskTypeName(StrUtil.isBlank(finishNotifyDTO.getRiskTypeName()) ? null : finishNotifyDTO.getRiskTypeName());
        addObj.setRiskDesc(StrUtil.isBlank(finishNotifyDTO.getRiskDesc()) ? null : finishNotifyDTO.getRiskDesc());
        addObj.setRejectReason(StrUtil.isBlank(finishNotifyDTO.getRejectReason()) ? null : finishNotifyDTO.getRejectReason());
        addObj.setClaimId(finishNotifyDTO.getId());
        addObj.setUpdateTime(new Date());
        addObj.setCreateTime(new Date());
        finishNotifyMapper.insert(addObj);
        return addObj.getFinishReportNo();
    }


    public StepFinishNotifyDTO selectFinishNotifyByClaimId(int claimId) {
        SmClaimFinishNotify claimFinishNotify = finishNotifyMapper.selectByClaimId(claimId);
        if (Objects.isNull(claimFinishNotify)) {
            return null;
        }
        StepFinishNotifyDTO result = new StepFinishNotifyDTO();
        BeanUtil.copyProperties(claimFinishNotify, result);
        result.setId(claimFinishNotify.getClaimId());
        return result;
    }


    public void truncateLoanCustomer() {
        claimMapper.truncateLoanCustomer();
    }

}

