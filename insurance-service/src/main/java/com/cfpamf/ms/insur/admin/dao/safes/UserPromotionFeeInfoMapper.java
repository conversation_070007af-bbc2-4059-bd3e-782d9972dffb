package com.cfpamf.ms.insur.admin.dao.safes;

import com.cfpamf.ms.insur.admin.pojo.po.UserPromotionFeeInfo;
import com.cfpamf.ms.insur.base.dao.MyMappler;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 员工推广费记录表数据库访问mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserPromotionFeeInfoMapper extends MyMappler<UserPromotionFeeInfo> {

    /**
     * 批量插入员工推广费记录
     *
     * @param userPromotionFeeInfoList 员工推广费记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<UserPromotionFeeInfo> userPromotionFeeInfoList);

    /**
     * 根据用户ID查询员工推广费记录列表
     *
     * @param userId 用户ID
     * @return 员工推广费记录列表
     */
    default List<UserPromotionFeeInfo> selectByUserId(String userId) {
        UserPromotionFeeInfo query = new UserPromotionFeeInfo();
        query.setUserId(userId);
        return selectEnabled(query);
    }

    /**
     * 根据用户ID和数据类型查询员工推广费记录列表
     *
     * @param userId   用户ID
     * @param dataType 数据类型
     * @return 员工推广费记录列表
     */
    default List<UserPromotionFeeInfo> selectByUserIdAndDataType(String userId, String dataType) {
        UserPromotionFeeInfo query = new UserPromotionFeeInfo();
        query.setUserId(userId);
        query.setDataType(dataType);
        return selectEnabled(query);
    }

    /**
     * 根据用户ID和统计日期查询员工推广费记录
     *
     * @param userId          用户ID
     * @param statisticalDate 统计日期
     * @param dataType        数据类型
     * @return 员工推广费记录
     */
    default UserPromotionFeeInfo selectByUserIdAndDate(String userId, LocalDate statisticalDate, String dataType) {
        UserPromotionFeeInfo query = new UserPromotionFeeInfo();
        query.setUserId(userId);
        query.setStatisticalDate(statisticalDate);
        query.setDataType(dataType);
        return selectOne(query);
    }

    /**
     * 根据统计日期范围查询员工推广费记录列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 员工推广费记录列表
     */
    List<UserPromotionFeeInfo> selectByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 统计用户推广费总额
     *
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param dataType  数据类型
     * @return 推广费总额
     */
    BigDecimal sumAmountByUserIdAndDateRange(@Param("userId") String userId,
                                             @Param("startDate") LocalDate startDate,
                                             @Param("endDate") LocalDate endDate,
                                             @Param("dataType") String dataType);

    /**
     * 统计用户保费总额
     *
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param dataType  数据类型
     * @return 保费总额
     */
    BigDecimal sumPremiumByUserIdAndDateRange(@Param("userId") String userId,
                                              @Param("startDate") LocalDate startDate,
                                              @Param("endDate") LocalDate endDate,
                                              @Param("dataType") String dataType);

    /**
     * 根据条件查询记录数量
     *
     * @param userId    用户ID
     * @param dataType  数据类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 记录数量
     */
    int countByCondition(@Param("userId") String userId,
                         @Param("dataType") String dataType,
                         @Param("startDate") LocalDate startDate,
                         @Param("endDate") LocalDate endDate);

    /**
     * 分页查询员工推广费记录
     *
     * @param userId    用户ID
     * @param dataType  数据类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param offset    偏移量
     * @param limit     限制数量
     * @return 员工推广费记录列表
     */
    List<UserPromotionFeeInfo> selectByConditionWithPage(@Param("userId") String userId,
                                                          @Param("dataType") String dataType,
                                                          @Param("startDate") LocalDate startDate,
                                                          @Param("endDate") LocalDate endDate,
                                                          @Param("offset") int offset,
                                                          @Param("limit") int limit);
}
