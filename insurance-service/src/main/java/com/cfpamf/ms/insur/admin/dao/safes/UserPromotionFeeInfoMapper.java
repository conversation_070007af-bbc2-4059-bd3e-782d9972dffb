package com.cfpamf.ms.insur.admin.dao.safes;

import com.cfpamf.ms.insur.admin.pojo.po.UserPromotionFeeInfo;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 员工推广费记录表数据库访问mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserPromotionFeeInfoMapper {

    /**
     * 插入员工推广费记录
     *
     * @param userPromotionFeeInfo 员工推广费记录
     * @return 影响行数
     */
    @Insert("INSERT INTO user_promotion_fee_info (statistical_date, user_id, user_name, data_type, premium, amount, pt, deleted, create_user, create_time, update_user, update_time, revision) " +
            "VALUES (#{statisticalDate}, #{userId}, #{userName}, #{dataType}, #{premium}, #{amount}, #{pt}, #{deleted}, #{createUser}, #{createTime}, #{updateUser}, #{updateTime}, #{revision})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserPromotionFeeInfo userPromotionFeeInfo);

    /**
     * 批量插入员工推广费记录
     *
     * @param userPromotionFeeInfoList 员工推广费记录列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<UserPromotionFeeInfo> userPromotionFeeInfoList);

    /**
     * 根据ID删除员工推广费记录
     *
     * @param id 记录ID
     * @return 影响行数
     */
    @Update("UPDATE user_promotion_fee_info SET deleted = -1, update_time = NOW() WHERE id = #{id}")
    int deleteById(@Param("id") Integer id);

    /**
     * 根据ID更新员工推广费记录
     *
     * @param userPromotionFeeInfo 员工推广费记录
     * @return 影响行数
     */
    @Update("UPDATE user_promotion_fee_info SET " +
            "statistical_date = #{statisticalDate}, " +
            "user_id = #{userId}, " +
            "user_name = #{userName}, " +
            "data_type = #{dataType}, " +
            "premium = #{premium}, " +
            "amount = #{amount}, " +
            "pt = #{pt}, " +
            "update_user = #{updateUser}, " +
            "update_time = #{updateTime}, " +
            "revision = revision + 1 " +
            "WHERE id = #{id} AND revision = #{revision}")
    int updateById(UserPromotionFeeInfo userPromotionFeeInfo);

    /**
     * 根据ID查询员工推广费记录
     *
     * @param id 记录ID
     * @return 员工推广费记录
     */
    @Select("SELECT * FROM user_promotion_fee_info WHERE id = #{id} AND deleted = 0")
    UserPromotionFeeInfo selectById(@Param("id") Integer id);

    /**
     * 根据用户ID查询员工推广费记录列表
     *
     * @param userId 用户ID
     * @return 员工推广费记录列表
     */
    @Select("SELECT * FROM user_promotion_fee_info WHERE user_id = #{userId} AND deleted = 0 ORDER BY statistical_date DESC")
    List<UserPromotionFeeInfo> selectByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID和数据类型查询员工推广费记录列表
     *
     * @param userId   用户ID
     * @param dataType 数据类型
     * @return 员工推广费记录列表
     */
    @Select("SELECT * FROM user_promotion_fee_info WHERE user_id = #{userId} AND data_type = #{dataType} AND deleted = 0 ORDER BY statistical_date DESC")
    List<UserPromotionFeeInfo> selectByUserIdAndDataType(@Param("userId") String userId, @Param("dataType") String dataType);

    /**
     * 根据统计日期范围查询员工推广费记录列表
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 员工推广费记录列表
     */
    @Select("SELECT * FROM user_promotion_fee_info WHERE statistical_date >= #{startDate} AND statistical_date <= #{endDate} AND deleted = 0 ORDER BY statistical_date DESC, user_id")
    List<UserPromotionFeeInfo> selectByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据用户ID和统计日期查询员工推广费记录
     *
     * @param userId          用户ID
     * @param statisticalDate 统计日期
     * @param dataType        数据类型
     * @return 员工推广费记录
     */
    @Select("SELECT * FROM user_promotion_fee_info WHERE user_id = #{userId} AND statistical_date = #{statisticalDate} AND data_type = #{dataType} AND deleted = 0")
    UserPromotionFeeInfo selectByUserIdAndDate(@Param("userId") String userId, @Param("statisticalDate") LocalDate statisticalDate, @Param("dataType") String dataType);

    /**
     * 统计用户推广费总额
     *
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param dataType  数据类型
     * @return 推广费总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM user_promotion_fee_info " +
            "WHERE user_id = #{userId} " +
            "AND statistical_date >= #{startDate} " +
            "AND statistical_date <= #{endDate} " +
            "AND data_type = #{dataType} " +
            "AND deleted = 0")
    BigDecimal sumAmountByUserIdAndDateRange(@Param("userId") String userId,
                                             @Param("startDate") LocalDate startDate,
                                             @Param("endDate") LocalDate endDate,
                                             @Param("dataType") String dataType);

    /**
     * 统计用户保费总额
     *
     * @param userId    用户ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param dataType  数据类型
     * @return 保费总额
     */
    @Select("SELECT COALESCE(SUM(premium), 0) FROM user_promotion_fee_info " +
            "WHERE user_id = #{userId} " +
            "AND statistical_date >= #{startDate} " +
            "AND statistical_date <= #{endDate} " +
            "AND data_type = #{dataType} " +
            "AND deleted = 0")
    BigDecimal sumPremiumByUserIdAndDateRange(@Param("userId") String userId,
                                              @Param("startDate") LocalDate startDate,
                                              @Param("endDate") LocalDate endDate,
                                              @Param("dataType") String dataType);

    /**
     * 查询所有有效记录数量
     *
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM user_promotion_fee_info WHERE deleted = 0")
    int countAll();

    /**
     * 根据条件查询记录数量
     *
     * @param userId    用户ID
     * @param dataType  数据类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 记录数量
     */
    int countByCondition(@Param("userId") String userId,
                         @Param("dataType") String dataType,
                         @Param("startDate") LocalDate startDate,
                         @Param("endDate") LocalDate endDate);

    /**
     * 分页查询员工推广费记录
     *
     * @param userId    用户ID
     * @param dataType  数据类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param offset    偏移量
     * @param limit     限制数量
     * @return 员工推广费记录列表
     */
    List<UserPromotionFeeInfo> selectByConditionWithPage(@Param("userId") String userId,
                                                          @Param("dataType") String dataType,
                                                          @Param("startDate") LocalDate startDate,
                                                          @Param("endDate") LocalDate endDate,
                                                          @Param("offset") int offset,
                                                          @Param("limit") int limit);
}
