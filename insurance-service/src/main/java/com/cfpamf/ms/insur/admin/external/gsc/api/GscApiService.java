package com.cfpamf.ms.insur.admin.external.gsc.api;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.external.OrderQueryRequest;
import com.cfpamf.ms.insur.admin.external.OrderSubmitRequest;
import com.cfpamf.ms.insur.admin.external.gsc.GscModelConvter;
import com.cfpamf.ms.insur.admin.external.gsc.client.GscOrderClient;
import com.cfpamf.ms.insur.admin.external.gsc.client.GscPayClient;
import com.cfpamf.ms.insur.admin.external.gsc.model.GscApiProperties;
import com.cfpamf.ms.insur.admin.external.gsc.model.req.*;
import com.cfpamf.ms.insur.admin.external.gsc.model.resp.*;
import com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO;
import com.cfpamf.ms.insur.admin.pojo.vo.SmPlanVO;
import com.cfpamf.ms.insur.admin.service.SmOutApiErrorService;
import com.cfpamf.ms.insur.admin.service.SmProductService;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.CommonUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.company.CompanyPayFormWrapperVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.company.GscPayFormVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import javax.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoField.*;

/**
 * 国寿财api调用服务
 * <AUTHOR> 2020/4/10 10:18
 */
@Slf4j
@EnableConfigurationProperties(GscApiProperties.class)
@Configuration
public class GscApiService {

    public static final String STRING_ONE = "1";

    static final DateTimeFormatter PAY_FMT;

    static {
        PAY_FMT = new DateTimeFormatterBuilder()
                .parseCaseInsensitive()
                .append(DateTimeFormatter.ISO_LOCAL_DATE)
                .appendLiteral('T')
                .append(new DateTimeFormatterBuilder()
                        .appendValue(HOUR_OF_DAY, 2)
                        .appendLiteral(':')
                        .appendValue(MINUTE_OF_HOUR, 2)
                        .optionalStart()
                        .appendLiteral(':')
                        .appendValue(SECOND_OF_MINUTE, 2)
                        .toFormatter())
                .toFormatter();
    }

    /**
     * 保险参数配置
     */
    @Autowired
    protected GscApiProperties properties;

    @Autowired
    protected SmOutApiErrorService errorService;

    @Autowired
    protected SmProductService productService;

    @Autowired
    protected GscOrderClient orderClient;

    @Autowired
    protected GscPayClient payClient;

    @Autowired
    protected SmOrderMapper orderMapper;
    @Autowired
    protected GscModelConvter gscModelConvter;

    @Autowired
    protected ObjectMapper mapper;

    protected XmlMapper xmlMapper = new XmlMapper();

    {
//            xmlMapper.enable(ToXmlGenerator.Feature.WRITE_XML_DECLARATION);
        xmlMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        xmlMapper.setDefaultUseWrapper(false);
        xmlMapper.registerModules(new JavaTimeModule());
        xmlMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        xmlMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

//    private


    public GscOrderResp submitOrder(OrderSubmitRequest request) {

        GscProposalSaveRequest proposalSaveRequest = gscModelConvter.convertOrderInfo(request);
        GscOrderResp submit = orderClient.submit(proposalSaveRequest);
        submit.setOthOrderCode(proposalSaveRequest.getMainEhm().getOthOrderCode());
        return submit;
    }


    public SignResp submitOrderNew(OrderSubmitRequest request) {

        GscOrderSaveRequest gscOrderSaveRequest = gscModelConvter.convertOrderInfoHouse(request);
        SignResp signResp = orderClient.submitNew(gscOrderSaveRequest);
        signResp.setOthSerialNo(gscOrderSaveRequest.getOthOrderNo());
        return signResp;
    }

    protected GscPolicyNewResp getPolicyUrlNew(SmOrderListVO vo) {

        SmPlanVO planById = productService.getPlanById(vo.getPlanId());
        // 给客户发送邮件和短信

        String[] split = planById.getFhProductId().split("\\|");
        GscNewPolicyReq req = new GscNewPolicyReq();
        //第三个是销售信息 暂时不用管

        req.setPolicyNo(vo.getPolicyNo());
        req.setProjectCode(split[0]);
        req.setProgramCode(split[1]);
        req.setProductCode(split[2]);
        req.setOthSerialNo(UUID.randomUUID().toString().replaceAll("-", ""));
        req.setUserAcct(properties.getUserAcct());
        req.setPassword(properties.getPassword());
        req.setEmailSendWay("ASYNC");
        req.setRequestType("03");
        req.setRequestCode("request");
        req.setTransDate(DateUtil.format(new Date(), GscModelConvter.FMT_NEW));
        req.setEpolicyReturnType("0111");
        return orderClient.getPolicyNew(req);
    }

    protected GscPolicyResp getPolicyUrl(String orderId, String riskCode, String policyNo) {
        List<SmOrderListVO> smOrderListVOS = orderMapper.listOrderInsuredDetailByOrderId(orderId);

        if (CollectionUtils.isEmpty(smOrderListVOS)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "");
        }
        // 给客户发送邮件和短信
        SmOrderListVO smOrderListVO = smOrderListVOS.get(0);
        GscPolicyReq gscPolicyReq = new GscPolicyReq();
        gscPolicyReq.setRequestType("01");
        gscPolicyReq.setProductCode(riskCode);
        gscPolicyReq.setSystemCode("XTP");
        gscPolicyReq.setProductName(smOrderListVO.getProductName() + "-" + smOrderListVO.getPlanName());
        gscPolicyReq.setPolicyNo(policyNo);
        gscPolicyReq.setEpolicyReturnType("101");
//        CompanyApiContext.get().setBizKey(orderId);
        GscPolicyResp policy = orderClient.getPolicy(gscPolicyReq);
        if (StringUtils.isNotBlank(policy.getUrl())) {
            //获取保单地址
//            CompanyApiContext.get().setBizKey(orderId);
            gscPolicyReq.setEpolicyReturnType("010");
            return orderClient.getPolicy(gscPolicyReq);
        }
        return policy;
    }

    @SneakyThrows
    public CompanyPayFormWrapperVO<GscPayFormVO> getPayForm(String orderId) {

        GscPayWrapperReq<GscPayBody> paySubmitReq = getPaySubmitReq(orderId);

        CompanyPayFormWrapperVO<GscPayFormVO> res = new CompanyPayFormWrapperVO<>();
        res.setActionUrl(properties.getPayGateway());
        res.setReq(buildForm("toMCD", paySubmitReq, true));
        return res;
    }

    @SneakyThrows
    private GscPayFormVO buildForm(String cmd, Object req, boolean needEncode) {
        GscPayFormVO gscPayFormVO = new GscPayFormVO();
        gscPayFormVO.setCmd(cmd);
        gscPayFormVO.setSys("1");
        gscPayFormVO.setF("1");
        String reqBody = "<?xml version='1.0' encoding='GBK'?>" + xmlMapper.writeValueAsString(req);
        log.info("国寿财{}调用明文为:{}", cmd, reqBody);
        if (needEncode) {
            String encode = URLEncoder.encode(reqBody, StandardCharsets.UTF_8.name());
            gscPayFormVO.setReq(encode);
        } else {
            gscPayFormVO.setReq(reqBody);
        }

        return gscPayFormVO;
    }

    @SneakyThrows
    public GscPayResp payQuery(GscPayWrapperReq<GscPayQueryBody> query) {
        String reqBody = "<?xml version='1.0' encoding='GBK'?>" + xmlMapper.writeValueAsString(query);
        log.info("国寿财支付查询调用明文为:{}", reqBody);

        GscPayWrapperReq<GscPayResp> toQry1 = payClient.query(reqBody);
        return toQry1.getBody();
    }

    /**
     * 获取支付预下单的请求参数
     *
     * @param orderId
     * @return
     */
    public GscPayWrapperReq<GscPayBody> getPaySubmitReq(String orderId) {
        SmBaseOrderVO baseOrderVO = orderMapper.getBaseOrderInfoByOrderId(orderId);
        List<SmOrderListVO> smOrderListVOS = orderMapper.listOrderInsuredDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(smOrderListVOS)) {
            throw new BizException(ExcptEnum.PARAMS_ERROR.getCode(), "该订单不存在:" + orderId);
        }
        SmOrderListVO smOrderListVO = smOrderListVOS.get(0);
        BigDecimal hundred = new BigDecimal("100");
        GscPayBody req = new GscPayBody();
        req.setTotalnum(STRING_ONE);
        req.setTotalamt(baseOrderVO.getTotalAmount().multiply(hundred).intValue() + "");
        req.setFlag(STRING_ONE);

        GscPayBody.GscPayReqEntity reqEntity = new GscPayBody.GscPayReqEntity();
        reqEntity.setPid(baseOrderVO.getFhOrderId());
        reqEntity.setReqSeq(STRING_ONE);
        reqEntity.setCallUrl(properties.getPayCallbackUrl());
        reqEntity.setInsurNum(smOrderListVOS.size() + "");

        //单价
        String price = baseOrderVO.getTotalAmount().divide(new BigDecimal(baseOrderVO.getQty()),
                2, RoundingMode.HALF_UP).multiply(hundred)
                .intValue() + "";
        List<GscPayBody.GscPayInsurModelWrapper> collect = smOrderListVOS.stream()
                .map(orderVo -> {

                    GscPayBody.GscPayInsurModel model = new GscPayBody.GscPayInsurModel();

                    model.setInsurFlag("0");
                    model.setInsurNo(baseOrderVO.getAppNo());
                    model.setPrice(req.getTotalamt());
                    model.setEffDate(DateUtil.format(baseOrderVO.getStartTime(), DateUtil.EN_YEAR_MONTH_DAY_FORMAT));

                    return new GscPayBody.GscPayInsurModelWrapper(model);
                }).collect(Collectors.toList());

        reqEntity.setInsurList(collect);
        reqEntity.setTransAmt(req.getTotalamt());
        reqEntity.setCurCode("CNY");

        req.setReqList(Collections.singletonList(reqEntity));
        String signContent = reqEntity.getCallUrl() + reqEntity.getPid() + req.getTotalamt() + properties.getPayKey();
        String s = DigestUtils.md5DigestAsHex(signContent.getBytes(StandardCharsets.UTF_8)).toUpperCase();
        String nowISO = LocalDateTime.now()
                .format(PAY_FMT);
        //签名域
        GscPaySignReq gscPaySignReq = new GscPaySignReq();
        gscPaySignReq.setSignTime(nowISO);
        gscPaySignReq.setSignContent(s);

        //公共域
        GscPayPubReq pub = genPubArea(baseOrderVO.getFhOrderId(), "PAY");
        GscPayWrapperReq<GscPayBody> wrapper = new GscPayWrapperReq<>();
        wrapper.setBody(req);
        wrapper.setSignarea(gscPaySignReq);
        wrapper.setPubarea(pub);

        return wrapper;

    }

    /**
     * 生成公共域
     *
     * @param fhOrderId
     * @param biz
     * @return
     */
    protected GscPayPubReq genPubArea(@NotBlank String fhOrderId, @NotBlank String biz) {

        GscPayPubReq pub = new GscPayPubReq();

        String nowISO = LocalDateTime.now().format(PAY_FMT);
        pub.setCorpCode(properties.getPayCorpCode());
        pub.setChannelCode("9999");
        pub.setTransType("100");
        pub.setSubmitTime(nowISO);
        //  订单id
        pub.setPkgId(genPkgId(fhOrderId, biz));

        return pub;
    }

    /**
     * 生成国寿财支付调用的pkgId
     *
     * @param fhOrderId
     * @param biz
     * @return
     */
    protected String genPkgId(@NotBlank String fhOrderId, @NotBlank String biz) {

        return biz + fhOrderId + System.currentTimeMillis() / 1000;
    }

    public GscPayWrapperReq<GscPayQueryBody> getPayQuery(OrderQueryRequest request) {
//        String appNo = request.getAppNo();

        return null;
    }

    public void payHtml(String orderId, HttpServletResponse response) throws IOException {

        CompanyPayFormWrapperVO<GscPayFormVO> payForm = getPayForm(orderId);
        String team = "<form id=\"formId\" name=\"formId\" method=\"post\" action=\"{action}\">\n" +
                "    <input id=\"cmd\"\n" +
                "           name=\"cmd\"\n" +
                "           value=\"{cmd}\"/>\n" +
                "    <input id=\"req\" name=\"req\" value=\"{req}\"/>\n" +
                "    <input id=\"f\" name=\"f\" value=\"{f}\"/>\n" +
                "    <input id=\"sys\" name=\"sys\" value=\"{sys}\"/>\n" +
                "\n" +
                "</form>" +
                "<script>document.getElementById('formId').submit()</script>";
        Map<String, Object> map = xmlMapper.convertValue(payForm.getReq(), Map.class);
        map.put("action", payForm.getActionUrl());
        String s = CommonUtil.strFormatUsingDict(team, map);
        response.setContentType("text/html;charset=utf-8;");
        response.getWriter().write(s);
        response.getWriter().flush();
    }

    @SneakyThrows
    public GscPayResp parseNotify(String decode) {
        GscPayWrapperReq<GscPayResp> o = xmlMapper.readValue(decode, new TypeReference<GscPayWrapperReq<GscPayResp>>() {
        });
        return o.getBody();
    }
}
