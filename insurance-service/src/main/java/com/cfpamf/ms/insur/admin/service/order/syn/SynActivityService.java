package com.cfpamf.ms.insur.admin.service.order.syn;

import com.cfpamf.common.ms.util.DateUtil;
import com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderDDDMapper;
import com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper;
import com.cfpamf.ms.insur.admin.external.whale.WhaleOrderServiceAdapterImpl;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleContract;
import com.cfpamf.ms.insur.admin.external.whale.model.WhaleResp;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrder;
import com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured;
import com.cfpamf.ms.insur.admin.service.UserPostService;
import com.cfpamf.ms.insur.admin.service.order.SmOrderVillageActivityService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yangdonglin
 * @create: 2023/11/27 18:35
 * @description: 同步活动信息
 */
@Service
@Slf4j
public class SynActivityService {

    @Autowired
    private UserPostService userPostService;
    @Autowired
    private SmOrderMapper smOrderMapper;
    @Autowired
    private WhaleOrderServiceAdapterImpl adapter;
    @Autowired
    private SmOrderInsuredMapper smOrderInsuredMapper;
    @Autowired
    private SmOrderDDDMapper orderMapper;
    @Autowired
    private SmOrderVillageActivityService smOrderVillageActivityService;
    @Autowired
    private SynRecommendService synRecommendService;

    /**
     * 更新小鲸活动信息工具
     *
     * @param whaleCodes
     */
    public void whaleSynActivityTools(final List<String> whaleCodes) {
        if (CollectionUtils.isEmpty(whaleCodes)) {
            log.info("合同编号为空{}", whaleCodes);
            XxlJobHelper.log("合同编号为空{}", whaleCodes);
            return;
        }
        for (String code : whaleCodes) {
            try {
                final StringBuilder information = new StringBuilder();
                information.append(code);
                final WhaleResp<WhaleContract> policy = adapter.getPolicyNo(code, "CREATE");
                if (Objects.isNull(policy) || Objects.isNull(policy.getData())) {
                    log.info("查询小鲸返回为空{}", code);
                    XxlJobHelper.log("查询小鲸返回为空{}", code);
                    continue;
                }
                final WhaleContract policyNo = policy.getData();
                if (Objects.isNull(policyNo.getContractBaseInfo()) || StringUtils.isBlank(policyNo.getContractBaseInfo().getPolicyNo())) {
                    log.info("保单信息为空{}", code);
                    XxlJobHelper.log("保单信息为空{}", code);
                    continue;
                }
                String policyNoCode = policyNo.getContractBaseInfo().getPolicyNo();
                //查询被保人信息
                final SmOrderInsured qryInsured = new SmOrderInsured();
                qryInsured.setPolicyNo(policyNoCode);
                final List<String> fhOrderIds = Optional.ofNullable(smOrderInsuredMapper.select(qryInsured))
                        .filter(CollectionUtils::isNotEmpty)
                        .map(e -> e.stream().map(SmOrderInsured::getFhOrderId)
                                .filter(StringUtils::isNotBlank)
                                .distinct()
                                .collect(Collectors.toList())).orElse(new ArrayList<>());
                if (fhOrderIds.size() != 1) {
                    log.info("农保订单信息不唯一{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final String fhOrderId = fhOrderIds.get(0);
                if (StringUtils.isBlank(fhOrderId)) {
                    log.info("农保订单号为空{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final SmOrder smOrder = new SmOrder();
                smOrder.setFhOrderId(fhOrderId);
                final List<SmOrder> smOrders = orderMapper.select(smOrder);
                if (CollectionUtils.isEmpty(smOrders) || smOrders.size() != 1 || Objects.isNull(smOrders.get(0))) {
                    log.info("农保订单信息不唯一{}", code);
                    XxlJobHelper.log("农保订单信息不唯一{}", code);
                    continue;
                }
                final SmOrder thisSmOrder = smOrders.get(0);
                try {
                    if (Objects.isNull(thisSmOrder.getPaymentTime())
                            || !DateUtil.format(new Date(), DateUtil.CN_YEAR_MONTH_FORMAT).equals(thisSmOrder.getPaymentTime().format(DateTimeFormatter.ofPattern(DateUtil.CN_YEAR_MONTH_FORMAT)))) {
                        log.info("渠道推荐人跨月变更了:{},{}", code, thisSmOrder.getPaymentTime());
                        information.append("【渠道推荐人跨月变更:失败】");
                    } else {
                        if (synRecommendService.modifyChannelRecommender(code, policyNo, fhOrderId, thisSmOrder)) {
                            log.info("渠道推荐人更新失败{}", code);
                            XxlJobHelper.log("渠道推荐人更新失败{}", code);
                        } else {
                            log.info("渠道推荐人更新成功{}", code);
                            information.append("【渠道推荐人更新:成功】");
                        }
                    }
                } catch (Exception e) {
                    log.info("更新活动信息失败{},{}", code, e);
                    XxlJobHelper.log("更新活动信息失败{},错误信息", code, e.getMessage());
                }
                try {
                    smOrderVillageActivityService.whaleSynCreate(policyNo, fhOrderId);
                    information.append("【活动信息更新:成功】");
                } catch (Exception e) {
                    log.info("活动信息更新失败{},{}", code, e);
                    XxlJobHelper.log("活动信息更新失败{},错误信息", code, e.getMessage());
                }
                log.info(information.toString());
                XxlJobHelper.log(information.toString());
            } catch (Exception e) {
                log.info("更新失败{},{}", code, e);
                XxlJobHelper.log("更新失败{},{}", code, e);
            }
        }
    }
}
