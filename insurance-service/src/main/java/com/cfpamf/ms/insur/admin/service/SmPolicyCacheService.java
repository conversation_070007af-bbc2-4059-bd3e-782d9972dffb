package com.cfpamf.ms.insur.admin.service;

import com.cfpamf.ms.insur.admin.dao.safes.SmPolicyCacheMapper;
import com.cfpamf.ms.insur.admin.event.OrderPolicyUrlChangeEvent;
import com.cfpamf.ms.insur.admin.pojo.po.SmPolicyCache;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 2020/3/20 14:36
 */
public interface SmPolicyCacheService {
    void savePolicyCache(OrderPolicyUrlChangeEvent event, SmPolicyCache smPolicyCache, String ossKey);

}
