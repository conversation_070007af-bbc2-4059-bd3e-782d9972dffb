<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.app.dao.AppOrderMapper">

    <select id="listAppPolicyList" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppPolicyListVO">
        SELECT t3.id AS policyId,
        t1.fhOrderId,
        t1.unitPrice*t1.qty AS totalAmount,
        t1.payStatus,
        t3.appStatus,
        t2.personName AS applicantPersonName,
        t3.personName AS insuredPersonName,
        t3.policyNo AS policyNo,
        t4.productName,
        t5.planName,
        t3.appStatus,
        t3.downloadURL,
        DATE_FORMAT(t1.startTime ,'%Y-%m-%d') AS startTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d') AS endTime,
        DATE_FORMAT(t1.endTime ,'%Y-%m-%d %H:%i:%s') AS fullEndTime,
        DATE_FORMAT(t1.create_time ,'%Y-%m-%d %T') AS createTime
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t1.fhOrderId=t3.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_plan t5 ON t5.id=t1.planId
        WHERE
        <if test="isInsured == false ">
            t2.idNumber = #{idNumber}
        </if>
        <if test="isInsured == true ">
            t3.idNumber = #{idNumber}
        </if>
        <if test="queryHistory==null">
            <![CDATA[ AND t1.endTime >= CURRENT_TIMESTAMP() ]]>
            AND (
            t1.payStatus = '1'
            OR
            (
            t1.payStatus = '2' AND t3.appStatus='1'
            )
            )
        </if>
        <if test="queryHistory!=null and queryHistory">
            AND t1.payStatus = '2' AND ( t3.appStatus != '1' OR <![CDATA[ t1.endTime < CURRENT_TIMESTAMP() ]]>)
        </if>
        ORDER BY t1.create_time DESC
    </select>
</mapper>
