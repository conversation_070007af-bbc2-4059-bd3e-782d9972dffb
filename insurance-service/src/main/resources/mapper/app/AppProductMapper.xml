<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.app.dao.AppProductMapper">

    <select id="listProductCategory" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppCategoryVO">
        SELECT id AS categoryId, code AS categoryCode, name AS categoryName
        FROM dictionary
        WHERE type = 'productType'
          AND enabled_flag = 0
        ORDER BY sorting ASC
    </select>

    <select id="listAppProducts" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppProductListVO">
        SELECT t1.id AS productId, t1.channel,t1.headImageUrl,
        t1.productShortName,t1.productName, t1.productFeature, t1.productTags AS productTagsJoin,
        t1.thumbnailImageUrl, t1.minAmount, t1.saleQty
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        AND t1.productAttrCode = 'person' AND t1.onlineChannel LIKE '%capp%'
        <if test='categoryId != null'>
            AND (t1.productCategoryId = #{categoryId} OR t1.productCategoryId LIKE CONCAT(#{categoryId}, ',%')
            OR t1.productCategoryId LIKE CONCAT('%,',#{categoryId}, ',%') OR t1.productCategoryId LIKE
            CONCAT('%,',#{categoryId}))
        </if>
        <if test='productName != null'>
            AND t1.productName LIKE CONCAT('%',#{productName},'%')
        </if>
        AND t1.id IN
        (
        SELECT DISTINCT t1.product_Id productId
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_Id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='regionName == null'>
            AND t1.org_Path IS NULL
        </if>
        <if test='regionName != null'>
            AND (t1.org_Path IS NULL OR t1.org_Name = #{regionName})
        </if>
        )
        ORDER BY t1.sortNum ,t1.companyId ASC, t1.productName ASC
    </select>

    <select id="listAppHotProducts" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppProductListVO">
        SELECT t1.id AS productId,t1.productShortName,t1.headImageUrl,t1.productName, t1.productTags AS productTagsJoin,
        t1.productFeature,
        t1.thumbnailImageUrl, t1.minAmount
        FROM sm_product t1
        WHERE t1.state=1 AND t1.enabled_flag=0
        <if test="active!=null">
            and t1.activeFlag = #{active}
        </if>
        AND t1.productAttrCode = 'person' AND t1.onlineChannel LIKE '%capp%'
        AND t1.id IN
        (
        SELECT DISTINCT t1.product_Id productId
        FROM sm_plan_sales_org t1
        LEFT JOIN sm_product t2 ON t1.product_Id = t2.id
        WHERE
        t1.enabled_flag = 0
        AND t2.state=1
        <if test='regionName == null'>
            AND t1.org_Path IS NULL
        </if>
        <if test='regionName != null'>
            AND (t1.org_Path IS NULL OR t1.org_Name = #{regionName})
        </if>
        )
        ORDER BY <if test="active==1">
        t1.sortNum,
    </if> t1.monthSaleCount desc,t1.productName ASC
    </select>

    <select id="getProductDetailById" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppProductDetailVO">
        SELECT t1.id          AS productId,
               t1.channel,
               t1.productName,
               t1.productShortName,
               t1.productFeature,
               t1.headImageUrl,
               t1.productTags AS productTagsJoin,
               t1.introduceImageUrl,
               t1.companyId,
               t2.companyLogoImageUrl,
               t2.companyName,
               t1.attentions,
               t1.buyLimit,
               t1.effectWaitingDayMin,
               t1.effectWaitingDayMax,
               t1.attentions,
               t1.healthNotification,
               t1.minAmount
        FROM sm_product t1
                 LEFT JOIN sm_company t2 ON t1.companyId = t2.id
        WHERE t1.id = #{productId}
          AND t1.state = 1
          AND t1.enabled_flag = 0
    </select>

    <select id="listProductClausesByProductId" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppProductClauseVO">
        SELECT *, id AS clauseId
        FROM sm_product_clause
        WHERE productId = #{id}
          AND enabled_flag = 0
        ORDER BY id ASC
    </select>

    <select id="listAppProductPlanCoverages" resultType="com.cfpamf.ms.insur.app.pojo.vo.AppPlanCoverageVO">
        SELECT t1.spcaId, t1.cvgAmount, t1.cvgNotice, t2.cvgType, t2.cvgItemName, t2.cvgNameDetail
        FROM sm_product_coverage_amount t1
                 LEFT JOIN sm_product_coverage t2 ON t1.spcId = t2.spcId
        WHERE t1.planId = #{planId}
          AND t1.enabled_flag = 0
    </select>
</mapper>
