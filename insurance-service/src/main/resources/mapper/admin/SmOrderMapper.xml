<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.SmOrderMapper">
    <select id="getOrderDetailCommissionByFhOrderId"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.OrderDetailCommissionDTO">
        select soc.fhOrderId,
        sum(ifnull(soc.paymentAmount, 0)) as paymenyCommission,
        sum(ifnull(soc.settlementAmount, 0)) as settlementCommission,
        u1.userId as recommendId,
        u1.userName as recommendName,
        u1.userMobile as recommendMobile,
        u1.regionCode,
        u1.regionName as regionName,
        u1.orgCode,
        u1.organizationFullName as orgName,
        u2.userId as customerAdminId,
        u2.userName as customerAdminName,
        u2.userMobile as customerAdminMobile
        from sm_order_commission soc
        left join auth_user u1 on soc.recommendId = u1.userId and u1.enabled_flag = 0
        left join auth_user u2 on soc.customerAdminId = u2.userId and u2.enabled_flag = 0
        where soc.fhOrderId = #{fhOrderId}
        group by soc.fhOrderId, u1.userId, u1.userName, u1.userMobile, u1.regionCode, u1.regionName, u1.orgCode,
        u1.organizationFullName, u2.userId, u2.userName, u2.userMobile

    </select>

    <select id="listOrdersV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderV3ListVO">
        SELECT t1.create_time AS createTime,t1.fhOrderId AS fhOrderId,t1.payStatus AS payStatus,
        t1.paymentTime,
        t1.productId,t4.productName AS productName,t1.planId,t11.planName as planName,t4.productType,t4.productAttrCode,
        (case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty end) AS totalAmount,
        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN IF(t3.appStatus = 4 or t3.appStatus = 1,
        t13.conversion_amount, 0) ELSE
        IFNULL(t10.converted_premium,
        (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and (t4.productAttrCode = 'group' or t4.productAttrCode = 'employer')) THEN
        -t1.unitPrice*t1.qty ELSE
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'person') THEN 0 ELSE
        t1.unitPrice*t1.qty END)
        END)
        ELSE
        0 END)
        ) END) AS convertedAmount,
        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN t13.conversion_rate ELSE t10.proportion END) AS
        convertedProportion,
        t3.policyNo AS policyNo,(case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty
        end) as premium,sop.amount as insuredAmount,t1.validPeriod,
        t1.startTime AS startTime, t1.endTime AS endTime,
        t5.companyName AS companyName,t4.companyId AS companyId, t3.appStatus AS appStatus,t3.downloadURL AS
        downloadURL,
        t2.personName AS applicantPersonName, t2.personGender AS applicantPersonGender,
        t3.id insuredSn,t3.personName AS insuredPersonName,t3.personGender AS insuredPersonGender, t3.relationship AS
        insuredRelationship, t3.idNumber AS insuredIdNumber,
        t9.userName as customerAdminName,t9.userMobile AS customerAdminMobile, t9.userId AS customerAdminId,
        t6.userName AS recommendUserName,t6.userMobile AS recommendUserMobile, t6.userId AS recommendUserId,
        t6.regionName as recommendRegionName,t6.organizationFullName as recommendOrgName,t1.recommendEntryDate AS
        entryDate,
        if(sot.id is null,(CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN t13.payment_rate ELSE
        t8.paymentProportion END) , (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN t13.payment_rate ELSE
        t8.paymentProportion END) * .7) AS paymentProportion,
        if(sot.id is null, t13.payment_amount, t13.payment_amount * .7) AS paymenyCommission,
        t1.channel,t1.subChannel
        <if test="orderType == 1">
            ,t12.distribution_amount distributionAmount,t12.distribution_state distributionState,
            t12.distribution_order_no distributionOrderNo
        </if>

        <if test='tabType != null and tabType == "OTHER"'>
            ,sop.visit_status returnVisitStatus,sop.visit_time returnVisitTime
        </if>
        ,orbi.bind_status as bindStatus
        ,if(sot.id is null,false,true) talkOrder,sot.invite_name inviteName
        ,sot.invite_type inviteType,sop.surrender_type as surrenderType
        ,t1.recommend_channel as recommendChannel
        ,sod.drainage_platform as drainagePlatform
        ,sod.drainage_share_code as drainageShareCode
        ,sod.drainage_share_name as drainageShareName,
        t13.term_num as termNum,
        t13.create_time as detailCreateTime,
        t13.original_amount as originalAmount,
        t14.label_value as selfInsured
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id=if(t1.commissionId =-1,t7.commission_id,t1.commissionId)
        left join auth_user t9 on t9.userId = t1.customerAdminId AND t9.enabled_flag = 0
        LEFT JOIN sm_plan t11 ON t11.id=t1.planId
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus and
        t10.enabled_flag = 0
        left join sm_order_renew_bind_info orbi on t3.fhorderid = orbi.fh_order_id and t3.policyNo = orbi.policy_no

        <if test='ownerPersonName!= null or plateEngineNum != null '>
            left join auto_order_car aoc on t1.fhOrderId = aoc.order_no
        </if>
        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>
        left join sm_order_policy sop on sop.fh_order_id = t1.fhOrderId
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_drainage sod on sod.order_id = t1.fhOrderId and sod.enabled_flag = 0
        left join sm_order_label t14 on t14.fh_order_id = t1.fhOrderId  and t14.label_type='self_insurance'
        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        WHERE t1.enabled_flag=0

        <if test="selfInsured == 1">
             and t14.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t14.fh_order_id IS NULL OR t14.label_value != "Y")
        </if>



        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>


        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus !=""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>
        <!--回访状态-->
        <if test='returnVisitStatus != null and returnVisitStatus!=""'>
            and sop.visit_status = #{returnVisitStatus}

        </if>
        <!--回访时间-->
        <if test='returnVisitTimeStart != null'>
            <![CDATA[ AND sop.visit_time>=#{returnVisitTimeStart} ]]>
        </if>
        <if test='returnVisitTimeEnd != null'>
            <![CDATA[ AND sop.visit_time<=#{returnVisitTimeEnd} ]]>
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>


        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>
        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>

        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test="recommendChannel != null and recommendChannel != ''">
            and t1.recommend_channel = #{recommendChannel}
        </if>
        <if test="drainagePlatform != null and drainagePlatform != ''">
            and sod.drainage_platform = #{drainagePlatform}
        </if>
        <if test="drainageShareAccount != null and drainageShareAccount != ''">
            and (
            sod.drainage_share_name = #{drainageShareAccount}
            or sod.drainage_share_account = #{drainageShareAccount}
            or sod.drainage_share_code = #{drainageShareAccount}
            )
        </if>
        <!--<if test="bindStatus!=null and bindStatus!=''">
            and orbi.bind_status = #{bindStatus}
        </if>-->
        <choose>
            <when test='bindStatus!=null and bindStatus=="undo"'>
                and (orbi.bind_status = #{bindStatus} or orbi.bind_status is null)
            </when>
            <when test='bindStatus!=null and bindStatus!="undo"'>
                and orbi.bind_status = #{bindStatus}
            </when>
            <otherwise>
            </otherwise>
        </choose>

        ORDER BY t1.create_time DESC

        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>
    <select id="countOrdersV3" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id=if(t1.commissionId =-1,t7.commission_id,t1.commissionId)
        left join auth_user t9 on t9.userId = t1.customerAdminId AND t9.enabled_flag = 0
        LEFT JOIN sm_plan t11 ON t11.id=t1.planId
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus and
        t10.enabled_flag = 0
        left join sm_order_renew_bind_info orbi on t3.fhorderid = orbi.fh_order_id and t3.policyNo = orbi.policy_no

        <if test='ownerPersonName!= null or plateEngineNum != null '>
            left join auto_order_car aoc on t1.fhOrderId = aoc.order_no
        </if>
        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>
        left join sm_order_policy sop on sop.fh_order_id = t1.fhOrderId
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_drainage sod on sod.order_id = t1.fhOrderId and sod.enabled_flag = 0
        left join sm_order_label t14 on t14.fh_order_id = t1.fhOrderId  and t14.label_type='self_insurance'
        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        WHERE t1.enabled_flag=0

        <if test="selfInsured == 1">
            and t14.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t14.fh_order_id IS NULL OR t14.label_value != "Y")
        </if>



        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>


        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus !=""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>
        <!--回访状态-->
        <if test='returnVisitStatus != null and returnVisitStatus!=""'>
            and sop.visit_status = #{returnVisitStatus}

        </if>
        <!--回访时间-->
        <if test='returnVisitTimeStart != null'>
            <![CDATA[ AND sop.visit_time>=#{returnVisitTimeStart} ]]>
        </if>
        <if test='returnVisitTimeEnd != null'>
            <![CDATA[ AND sop.visit_time<=#{returnVisitTimeEnd} ]]>
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>


        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>
        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>

        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test="recommendChannel != null and recommendChannel != ''">
            and t1.recommend_channel = #{recommendChannel}
        </if>
        <if test="drainagePlatform != null and drainagePlatform != ''">
            and sod.drainage_platform = #{drainagePlatform}
        </if>
        <if test="drainageShareAccount != null and drainageShareAccount != ''">
            and (
            sod.drainage_share_name = #{drainageShareAccount}
            or sod.drainage_share_account = #{drainageShareAccount}
            or sod.drainage_share_code = #{drainageShareAccount}
            )
        </if>
        <!--<if test="bindStatus!=null and bindStatus!=''">
            and orbi.bind_status = #{bindStatus}
        </if>-->
        <choose>
            <when test='bindStatus!=null and bindStatus=="undo"'>
                and (orbi.bind_status = #{bindStatus} or orbi.bind_status is null)
            </when>
            <when test='bindStatus!=null and bindStatus!="undo"'>
                and orbi.bind_status = #{bindStatus}
            </when>
            <otherwise>
            </otherwise>
        </choose>
    </select>

    <select id="getOrderSummaryV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        SUM((case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty end)*(CASE WHEN
        (t1.recommendId IS NULL OR t8.paymentProportion IS NULL) THEN 0 ELSE
        if(sot.id is null,t8.paymentProportion,t8.paymentProportion * .7) END)/100.00) AS paymentAmount,
        SUM((case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty
        end)*(IF(t8.settlementProportion IS NULL,0,t8.settlementProportion))/100.00) AS
        settlementAmount ,
        SUM((case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty
        end)*t1.add_commission_proportion/100.00) AS
        addCommissionAmount,
        sum( IFNULL(t10.converted_premium,
        (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'group') THEN -(case when t7.total_amount is not null then
        t7.total_amount else t1.unitPrice * t1.qty end) ELSE
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'person') THEN 0 ELSE
        (case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty end) END)
        END)
        ELSE
        0 END)
        )) AS
        convertedAmount ,
        SUM(case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty end) AS orderAmount,
        COUNT(t1.fhOrderId) AS totalQty
        <if test="orderType ==1">
            ,sum(t12.distribution_amount) distributionAmount
        </if>
        FROM sm_order t1

        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_commission_setting t8 ON t8.id= if(t1.commissionId =-1,t7.commission_id,t1.commissionId)
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus
        left join sm_order_renew_bind_info orbi on t3.fhorderid = orbi.fh_order_id and t3.policyNo = orbi.policy_no
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        <if test='applicantdName != null'>
            LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        </if>
        <if test='regionName != null or orgName != null or orgPath != null'>
            LEFT JOIN auth_user t9 ON t9.userId=t1.customerAdminId AND t9.enabled_flag = 0
        </if>
        <if test="orderType ==1">
            left join sm_order_distribution t12 on t1.fhOrderId = t12.fh_order_id
        </if>
        left join sm_order_policy sop on sop.fh_order_id = t1.fhOrderId

        <if test='ownerPersonName!= null or plateEngineNum != null '>
            left join auto_order_car aoc on t1.fhOrderId = aoc.order_no
        </if>

        left join sm_order_label  t13  on t13.fh_order_id = t1.fhOrderId  and t13.label_type='self_insurance'
        WHERE t1.enabled_flag=0

        <if test="selfInsured == 1">
            and t13.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t13.fh_order_id IS NULL OR t13.label_value != "Y")
        </if>
        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus != ""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>
        <!--回访状态-->
        <if test='returnVisitStatus != null and returnVisitStatus!=""'>
            and sop.visit_status = #{returnVisitStatus}

        </if>
        <!--回访时间-->
        <if test='returnVisitTimeStart != null'>
            <![CDATA[ AND sop.visit_time>=#{returnVisitTimeStart} ]]>
        </if>
        <if test='returnVisitTimeEnd != null'>
            <![CDATA[ AND sop.visit_time<=#{returnVisitTimeEnd} ]]>
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>


        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>
        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>
        <!-- <if test="bindStatus!=null and bindStatus!=''">
             and orbi.bind_status = #{bindStatus}
         </if>-->
        <choose>
            <when test='bindStatus!=null and bindStatus=="undo"'>
                and (orbi.bind_status = #{bindStatus} or orbi.bind_status is null)
            </when>
            <when test='bindStatus!=null and bindStatus!="undo"'>
                and orbi.bind_status = #{bindStatus}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <![CDATA[ AND t1.create_time<'2022-01-01 00:00:00']]>
    </select>


    <select id="listAutoOrdersV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderV3ListVO">
        SELECT ao.create_time AS createTime,ao.order_no AS fhOrderId,t1.payStatus AS payStatus, t1.paymentTime as
        paymentTime,
        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN t14.proportion ELSE t1.add_commission_proportion END)
        AS addCommissionProportion,
        t1.productId as productId,t4.productName AS productName,t1.planId as planId,t11.planName as
        planName,t4.productType,t4.productAttrCode,
        ao.total_amount AS totalAmount,

        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN IF(t3.appStatus = 4 or t3.appStatus = 1,
        t13.conversion_amount, 0) ELSE
        IFNULL(t10.converted_premium,
        (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and (t4.productAttrCode = 'group' or t4.productAttrCode = 'employer')) THEN
        -t1.unitPrice*t1.qty ELSE
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'person') THEN 0 ELSE
        t1.unitPrice*t1.qty END)
        END)
        ELSE
        0 END)
        ) END) AS convertedAmount,

        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN t13.conversion_rate ELSE t10.proportion END) AS
        convertedProportion,
        t3.policyNo AS policyNo,ao.premium as premium,ao.amount as insuredAmount,t1.validPeriod,
        t1.startTime AS startTime, t1.endTime AS endTime,
        t5.companyName AS companyName,t4.companyId AS companyId, t3.appStatus AS appStatus,t3.downloadURL AS
        downloadURL,
        t2.personName AS applicantPersonName, t2.personGender AS applicantPersonGender,
        t3.personName AS insuredPersonName,t3.personGender AS insuredPersonGender, t3.relationship AS
        insuredRelationship,
        t9.userName as customerAdminName,t9.userMobile AS customerAdminMobile, t9.userId AS customerAdminId,
        t6.userName AS recommendUserName,t6.userMobile AS recommendUserMobile, t6.userId AS recommendUserId,
        t6.regionName as recommendRegionName,t6.organizationFullName as recommendOrgName,t1.recommendEntryDate AS
        entryDate,
        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN t13.payment_rate ELSE t8.paymentProportion END) AS
        paymentProportion,
        t13.payment_amount AS paymenyCommission,
        (case when (t3.appStatus = 4 and ifnull(t14.add_commission_amount, 0) > 0) then -1*t14.add_commission_amount
        else t14.add_commission_amount end ) AS addCommissionAmount,
        ao.tax as tax,aoc.plate_num as plateNum,aoc.car_model_name as carModelName,aoc.use_props as useProps,
        t15.label_value as selfInsured,
        aoc.owner_person_name as ownerPersonName,t1.channel,t1.subChannel,ao.joint_issuing_flag as
        jointIssuingFlag,ao.insurance_place as insurancePlace,ao.bing_driving_Flag as bingDrivingFlag
        ,ao.continue_purchase_flag as continuePurchaseFlag
        <if test="orderType == 1">
            ,t12.distribution_amount distributionAmount,t12.distribution_state distributionState,
            t12.distribution_order_no distributionOrderNo
        </if>

        FROM auto_order ao
        left join sm_order t1 on ao.order_no = t1.fhOrderId
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=ao.order_no
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=ao.order_no
        LEFT JOIN sm_product t4 ON t4.id=ao.product_id
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id = t1.commissionId
        left join auth_user t9 on t9.userId = t1.customerAdminId AND t9.enabled_flag = 0
        LEFT JOIN sm_plan t11 ON t11.id=ao.plan_id
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus and
        t10.enabled_flag = 0
        left join auto_order_car aoc on ao.order_no = aoc.order_no

        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>

        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        left join sm_add_commission_detail t14 on t14.order_id = t3.fhOrderId and t3.idNumber = t14.insured_id_number
        left join sm_order_label t15 on t15.fh_order_id = t1.fhOrderId  and t15.label_type='self_insurance'

        WHERE t1.enabled_flag=0
        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>
        <if test="selfInsured == 1">
            and t15.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t15.fh_order_id IS NULL OR t15.label_value != "Y")
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus !=""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>

        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>


        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>

        ORDER BY t1.create_time DESC

        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>
    <select id="countAutoOrdersV3" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM auto_order ao
        left join sm_order t1 on ao.order_no = t1.fhOrderId
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=ao.order_no
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=ao.order_no
        LEFT JOIN sm_product t4 ON t4.id=ao.product_id
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id = t1.commissionId
        left join auth_user t9 on t9.userId = t1.customerAdminId AND t9.enabled_flag = 0
        LEFT JOIN sm_plan t11 ON t11.id=ao.plan_id
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus and
        t10.enabled_flag = 0
        left join auto_order_car aoc on ao.order_no = aoc.order_no

        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>

        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        left join sm_add_commission_detail t14 on t14.order_id = t3.fhOrderId and t3.idNumber = t14.insured_id_number
        left join sm_order_label t15 on t15.fh_order_id = t1.fhOrderId  and t15.label_type='self_insurance'

        WHERE t1.enabled_flag=0
        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>
        <if test="selfInsured == 1">
            and t15.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t15.fh_order_id IS NULL OR t15.label_value != "Y")
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus !=""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>

        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>


        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>
    </select>

    <select id="getAutoOrderSummaryV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        SUM( t1.unitPrice * t1.qty *(CASE WHEN (t1.recommendId IS NULL OR t8.paymentProportion IS NULL) THEN 0 ELSE
        t8.paymentProportion END)/100.00) AS paymentAmount,
        SUM(t1.unitPrice * t1.qty *(IF(t8.settlementProportion IS NULL,0,t8.settlementProportion))/100.00) AS
        settlementAmount ,
        SUM(t1.unitPrice*t1.qty*t1.add_commission_proportion/100.00) AS
        addCommissionAmount,
        sum( IFNULL(t10.converted_premium,
        (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'group') THEN - t1.unitPrice * t1.qty ELSE
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'person') THEN 0 ELSE
        t1.unitPrice * t1.qty END)
        END)
        ELSE
        0 END)
        )) AS
        convertedAmount ,
        SUM( t1.unitPrice * t1.qty) AS orderAmount,
        COUNT(t1.fhOrderId) AS totalQty
        <if test="orderType ==1">
            ,sum(t12.distribution_amount) distributionAmount
        </if>
        FROM auto_order ao
        left join sm_order t1 on ao.order_no = t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_commission_setting t8 ON t8.id=t1.commissionId
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus
        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>
        <if test='applicantdName != null'>
            LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        </if>
        <if test='regionName != null or orgName != null or orgPath != null'>
            LEFT JOIN auth_user t9 ON t9.userId=t1.recommendId AND t9.enabled_flag = 0
        </if>
        <if test='ownerPersonName!= null or plateEngineNum != null '>
            left join auto_order_car aoc on ao.order_no = aoc.order_no
        </if>

        WHERE t1.enabled_flag=0
        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus !=""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>

        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType2}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>

        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>

    </select>


    <insert id="insertOrder" useGeneratedKeys="true">
        INSERT INTO sm_order (channel, subChannel, fhOrderId, productId, planId, wxOpenId, unitPrice, qty, totalAmount,
        startTime, endTime, validPeriod, underWritingAge, recommendId, agentId, customerAdminId,
        recommendJobCode, customerAdminJobCode, recommendMainJobNumber,
        customerAdminMainJobNumber, recommendOrgCode, customerAdminOrgCode,
        submitTime, noticeCode, noticeMsg, orderState, payStatus, commissionId, appNo,
        recommendMasterName, recommendAdminName, recommendEntryDate, recommendPostName,
        create_by, create_time, update_by, update_time, enabled_flag,
        orderOutType, productType,orderType, recommend_channel,apply_time,is_life_service_order)
        VALUES (#{channel}, #{subChannel}, #{fhOrderId}, #{productId}, #{planId}, #{wxOpenId}, #{unitPrice}, #{qty},
        #{orderInfo.totalAmount}, #{orderInfo.startTime}, #{orderInfo.endTime}, #{orderInfo.validPeriod},
        #{orderInfo.underWritingAge}, #{productInfo.recommendId}, #{agentId}, #{productInfo.recommendId},
        #{recommendJobCode}, #{customerAdminJobCode}, #{recommendMainJobNumber}, #{customerAdminMainJobNumber},
        #{recommendOrgCode}, #{customerAdminOrgCode},
        #{orderInfo.submitTime}, #{noticeCode}, #{noticeMsg}, #{orderState}, #{payStatus}, #{commissionId},
        #{appNo}, #{recommendMasterName}, #{recommendAdminName}, #{recommendEntryDate}, #{recommendPostName},
        #{modifyBy}, #{createTime}, #{modifyBy}, CURRENT_TIMESTAMP(), 0,
        #{orderOutType}, #{productType},#{orderType}, #{recommendChannel},#{orderInfo.applyTime},#{isLifeServicePartner})
    </insert>
    <insert id="saveTempOrder" useGeneratedKeys="true">
        INSERT INTO sm_order (fhOrderId,
        productId,
        startTime,
        endTime,
        qty,
        orderState,
        payStatus,
        wxOpenId,
        create_by,
        create_time,
        update_by,
        update_time)
        VALUES (#{orderId},
        #{order.productId},
        #{order.startTime},
        #{order.endTime},
        #{order.qty},
        #{order.orderState},
        #{order.payStatus},
        #{order.openId},
        #{userId},
        CURRENT_TIMESTAMP(),
        #{userId},
        CURRENT_TIMESTAMP())
    </insert>

    <select id="getAppNo" resultType="java.lang.String">
        SELECT appNo
        FROM sm_order
        where fhOrderId = #{orderId}
    </select>

    <insert id="saveQuoteOrder" useGeneratedKeys="true">
        insert into sm_order (fhOrderId,
        productId,
        planId,
        startTime,
        endTime,
        unitPrice,
        qty,
        totalAmount,
        pay_type,
        orderState,
        payStatus,
        wxOpenId,
        validPeriod,
        recommendId,
        agentId,
        customerAdminId,
        recommendJobCode,
        customerAdminJobCode,
        recommendMainJobNumber,
        customerAdminMainJobNumber,
        recommendOrgCode,
        customerAdminOrgCode,
        commissionId,
        orderOutType,
        orderType,
        submitTime,
        create_by,
        channel,
        create_time,
        update_by,
        update_time)
        VALUES (#{orderId},
        #{data.productId},
        #{planId},
        #{data.startTime},
        #{data.endTime},
        #{data.totalAmount},
        #{data.qty},
        #{data.totalAmount},
        #{data.payType},
        #{data.orderState},
        #{data.payStatus},
        #{data.openId},
        #{data.validPeriod},
        #{data.agent.recommendId},
        #{data.agent.agentId},
        #{data.agent.recommendId},
        #{data.agent.recommendJobCode},
        #{data.agent.customerAdminJobCode},
        #{data.agent.recommendMainJobNumber},
        #{data.agent.customerAdminMainJobNumber},
        #{data.agent.recommendOrgCode},
        #{data.agent.customerAdminOrgCode},
        #{data.commission.id},
        #{data.orderOutType},
        #{data.orderType},
        #{data.submitTime},
        #{userId},
        #{data.channel},
        CURRENT_TIMESTAMP(),
        #{userId},
        CURRENT_TIMESTAMP())
    </insert>
    <update id="updateQuoteOrder" useGeneratedKeys="true">
        UPDATE sm_order SET
        startTime=#{data.startTime},
        endTime=#{data.endTime},
        qty=#{data.qty},
        orderState=#{data.orderState},
        payStatus=#{data.payStatus},
        <if test=" data.openId != null ">
            wxOpenId=#{data.openId},
        </if>
        <if test=" data.channel != null ">
            channel=#{data.channel},
        </if>
        <if test=" data.subChannel != null ">
            subChannel=#{data.subChannel},
        </if>
        <if test=" planId != null ">
            planId=#{planId},
        </if>
        <if test=" data.qty != null ">
            qty=#{data.qty},
        </if>
        <if test=" data.totalAmount != null ">
            totalAmount=#{data.totalAmount},
            unitPrice=#{data.totalAmount},
        </if>
        <if test=" data.validPeriod != null ">
            validPeriod=#{data.validPeriod},
        </if>
        <if test=" data.orderOutType != null ">
            orderOutType=#{data.orderOutType},
        </if>
        <if test=" data.payType != null ">
            pay_type=#{data.payType},
        </if>
        <if test=" data.agent.recommendId != null ">
            recommendId=#{data.agent.recommendId},
            customerAdminId=#{data.agent.recommendId},
        </if>
        agentId=#{data.agent.agentId},
        recommendJobCode=#{data.agent.recommendJobCode},
        customerAdminJobCode=#{data.agent.customerAdminJobCode},
        recommendMainJobNumber=#{data.agent.recommendMainJobNumber},
        customerAdminMainJobNumber=#{data.agent.customerAdminMainJobNumber},
        recommendOrgCode=#{data.agent.recommendOrgCode},
        customerAdminOrgCode=#{data.agent.customerAdminOrgCode},
        update_by=#{userId},
        update_time=CURRENT_TIMESTAMP()
        WHERE
        fhOrderId=#{orderId}
    </update>

    <insert id="insertOrderApplicant" useGeneratedKeys="true">
        INSERT INTO sm_order_applicant (fhOrderId, personName, personGender, idType, idNumber, birthday, cellPhone,
        email, area, address, idPeriodStart, idPeriodEnd, create_time, update_time,
        areaName, addressProvider)
        VALUES (#{fhOrderId}, #{proposerInfo.personName}, #{proposerInfo.personGender}, #{proposerInfo.idType},
        #{proposerInfo.idNumber}, #{proposerInfo.birthday}, #{proposerInfo.cellPhone}, #{proposerInfo.email},
        #{proposerInfo.area}, #{proposerInfo.address}, #{proposerInfo.idPeriodStart},
        #{proposerInfo.idPeriodEnd}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP(), #{proposerInfo.areaName},
        #{proposerInfo.addressProvider})
    </insert>


    <insert id="saveQuoteApplicant" useGeneratedKeys="true">
        INSERT INTO sm_order_applicant (fhOrderId,
        personName,
        idType,
        idNumber,
        cellPhone,
        email,
        address,
        provice_code,
        city_code,
        country_code,
        create_time,
        update_time,
        addressProvider,
        business_license,
        vatInvoice,
        extend_field)
        VALUES (#{orderId},
        #{data.personName},
        #{data.idType},
        #{data.idNumber},
        #{data.cellPhone},
        #{data.email},
        #{data.address},
        #{data.proviceCode},
        #{data.cityCode},
        #{data.countryCode},
        CURRENT_TIMESTAMP(),
        CURRENT_TIMESTAMP(),
        #{data.addressProvider},
        #{data.businessLicense},
        #{vatInvoice},
        #{data.extendField})
    </insert>

    <update id="updateQuoteApplicant">
        UPDATE sm_order_applicant
        SET personName =#{data.personName},
        idType =#{data.idType},
        idNumber =#{data.idNumber},
        cellPhone =#{data.cellPhone},
        email =#{data.email},
        address =#{data.address},
        provice_code =#{data.proviceCode},
        city_code =#{data.cityCode},
        country_code =#{data.countryCode},
        update_time =CURRENT_TIMESTAMP(),
        addressProvider =#{data.addressProvider},
        business_license =#{data.businessLicense},
        vatInvoice =#{vatInvoice},
        extend_field =#{data.extendField}
        WHERE fhOrderId = #{orderId}
    </update>


    <insert id="saveTempInsured" useGeneratedKeys="true">
        INSERT INTO sm_order_insured (
        fhOrderId,
        personName,
        idType,
        idNumber,
        birthday,
        occupationCode,
        occupation,
        occupation_group,
        create_time,
        update_time)
        VALUES
        <foreach collection="data" item="item" index="index" separator=",">
            (
            #{orderId},
            #{item.personName},
            #{item.idType},
            #{item.idNumber},
            #{item.birthday},
            #{item.occupationCode},
            #{item.occupationValue},
            #{item.occupationGroup},
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP()
            )
        </foreach>
    </insert>

    <insert id="saveQuoteInsureds" useGeneratedKeys="true">
        INSERT INTO sm_order_insured (
        fhOrderId,
        personName,
        idType,
        idNumber,
        birthday,
        occupationCode,
        occupation_group,
        occupation_version,
        appStatus,
        create_time,
        update_time)
        VALUES
        <foreach collection="data" item="item" index="index" separator=",">
            (
            #{orderId},
            #{item.personName},
            #{item.idType},
            #{item.idNumber},
            #{item.birthday},
            #{item.occupationCode},
            #{item.occupationGroup},
            #{item.occupationVersion},
            -2,
            CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP()
            )
        </foreach>
    </insert>

    <insert id="insertOrderInsured" useGeneratedKeys="true">
        INSERT INTO sm_order_insured
        (fhOrderId,relationship,personName,personGender,idType,idNumber,birthday,cellPhone,email,annualIncome,flightNo,flightTime,occupationCode,destinationCountryText,area,address,
        studentType, schoolType, schoolNature, schoolName, schoolClass, studentId, hdrType, appStatus, policyNo,
        downloadURL, idPeriodStart, idPeriodEnd, isSecurity, create_time, update_time,surrender_time, areaName,
        addressProvider,smoke)
        VALUES
        <foreach collection="dto.insuredPerson" item="item" index="index" separator=",">
            (#{dto.fhOrderId}, #{item.relationship}, #{item.personName}, #{item.personGender}, #{item.idType},
            #{item.idNumber}, #{item.birthday}, #{item.cellPhone}, #{item.email},#{item.annualIncome}, #{item.flightNo},
            #{item.flightTime}, #{item.occupationCode}, #{item.destinationCountryText}, #{item.area}, #{item.address},
            #{item.studentType},#{item.schoolType},#{item.schoolNature},#{item.schoolName},#{item.schoolClass},#{item.studentId},
            #{item.hdrType}, #{item.appStatus}, #{item.policyNo}, #{item.downloadURL}, #{item.idPeriodStart},
            #{item.idPeriodEnd}, #{item.isSecurity}, CURRENT_TIMESTAMP(),
            CURRENT_TIMESTAMP(),#{item.surrenderTime},#{item.areaName},#{item.addressProvider},#{item.smoke})
        </foreach>
    </insert>


    <insert id="insertOrderBatch" useGeneratedKeys="true">
        INSERT INTO sm_order (
        channel, subChannel, fhOrderId, productId, planId, unitPrice, qty, totalAmount,
        startTime, endTime, validPeriod, underWritingAge, recommendId, agentId, customerAdminId,
        recommendJobCode,customerAdminJobCode,recommendMainJobNumber,customerAdminMainJobNumber,recommendOrgCode,customerAdminOrgCode,
        submitTime, noticeCode, noticeMsg, orderState,paymentTime, payStatus, commissionId,
        appNo,policy_no,endorsement_no,
        create_by, create_time, update_by, update_time, enabled_flag,orderType,pay_type,extend_field,apply_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.channel}, #{dto.subChannel}, #{dto.fhOrderId}, #{dto.productId}, #{dto.planId}, #{dto.unitPrice},
            #{dto.qty},
            #{dto.orderInfo.totalAmount}, #{dto.orderInfo.startTime}, #{dto.orderInfo.endTime},
            #{dto.orderInfo.validPeriod},
            #{dto.orderInfo.underWritingAge}, #{dto.productInfo.recommendId}, #{dto.agentId},
            #{dto.productInfo.customerAdminId},
            #{dto.recommendJobCode},#{dto.customerAdminJobCode},#{dto.recommendMainJobNumber},#{dto.customerAdminMainJobNumber},
            #{dto.recommendOrgCode},#{dto.customerAdminOrgCode},
            #{dto.orderInfo.submitTime}, #{dto.noticeCode}, #{dto.noticeMsg}, #{dto.orderState},CURRENT_TIMESTAMP(),
            #{dto.payStatus}, #{dto.commissionId},
            #{dto.appNo},#{dto.policyNo},#{dto.endorsementNo},
            #{dto.modifyBy}, #{dto.createTime}, #{dto.modifyBy},#{dto.orderInfo.submitTime} ,
            0,#{dto.orderType},#{dto.payType},#{dto.extendField},#{dto.orderInfo.applyTime})
        </foreach>
    </insert>

    <insert id="insertOrderApplicantBatch" useGeneratedKeys="true">
        INSERT INTO sm_order_applicant (fhOrderId, personName, personGender, idType, idNumber, birthday, cellPhone,
        email, area, address, idPeriodStart, idPeriodEnd, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.fhOrderId}, #{dto.proposerInfo.personName}, #{dto.proposerInfo.personGender},
            #{dto.proposerInfo.idType},
            #{dto.proposerInfo.idNumber}, #{dto.proposerInfo.birthday}, #{dto.proposerInfo.cellPhone},
            #{dto.proposerInfo.email},
            #{dto.proposerInfo.area}, #{dto.proposerInfo.address}, #{dto.proposerInfo.idPeriodStart},
            #{dto.proposerInfo.idPeriodEnd}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <insert id="insertOrderInsuredBatch" useGeneratedKeys="true">
        INSERT INTO sm_order_insured
        (fhOrderId,relationship,personName,personGender,idType,idNumber,birthday,cellPhone,email,annualIncome,flightNo,flightTime,occupationCode,occupation_group,destinationCountryText,area,address,
        studentType, schoolType, schoolNature, schoolName, schoolClass, studentId, hdrType, appStatus, policyNo,
        downloadURL, idPeriodStart, idPeriodEnd, isSecurity,insured_time,surrender_time, create_time, update_time)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            <foreach collection="dto.insuredPerson" item="item" index="index" separator=",">
                (#{dto.fhOrderId}, #{item.relationship}, #{item.personName}, #{item.personGender}, #{item.idType},
                #{item.idNumber}, #{item.birthday}, #{item.cellPhone}, #{item.email},#{item.annualIncome},
                #{item.flightNo},
                #{item.flightTime}, #{item.occupationCode},#{item.occupationGroup}, #{item.destinationCountryText},
                #{item.area},
                #{item.address},
                #{item.studentType},#{item.schoolType},#{item.schoolNature},#{item.schoolName},#{item.schoolClass},#{item.studentId},
                #{item.hdrType}, #{item.appStatus}, #{item.policyNo}, #{item.downloadURL}, #{item.idPeriodStart},
                #{item.idPeriodEnd}, #{item.isSecurity},CURRENT_TIMESTAMP(),
                #{item.surrenderTime}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
            </foreach>
        </foreach>
    </insert>

    <insert id="insertOrderInsuredBatchV2" useGeneratedKeys="true">
        INSERT INTO sm_order_insured
        (fhOrderId,relationship,personName,personGender,idType,idNumber,birthday,cellPhone,email,annualIncome,flightNo,flightTime,occupationCode,occupation_group,destinationCountryText,area,address,
        studentType, schoolType, schoolNature, schoolName, schoolClass, studentId, hdrType, appStatus, policyNo,
        downloadURL, idPeriodStart, idPeriodEnd, isSecurity,insured_time,surrender_time, create_time, update_time)
        VALUES
        <foreach collection="orderList" item="order" index="index" separator=",">
            <foreach collection="order.insuredPerson" item="item" index="index" separator=",">
                (#{order.fhOrderId}, #{item.relationship}, #{item.personName}, #{item.personGender}, #{item.idType},
                #{item.idNumber}, #{item.birthday}, #{item.cellPhone}, #{item.email},#{item.annualIncome},
                #{item.flightNo},
                #{item.flightTime}, #{item.occupationCode},#{item.occupationGroup}, #{item.destinationCountryText},
                #{item.area},
                #{item.address},
                #{item.studentType},#{item.schoolType},#{item.schoolNature},#{item.schoolName},#{item.schoolClass},#{item.studentId},
                #{item.hdrType}, #{item.appStatus}, #{item.policyNo}, #{item.downloadURL}, #{item.idPeriodStart},
                #{item.idPeriodEnd}, #{item.isSecurity},#{item.insuredTime},
                #{item.surrenderTime}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
            </foreach>
        </foreach>
    </insert>

    <insert id="insertOrderInsuredList" useGeneratedKeys="true">
        INSERT INTO sm_order_insured
        (fhOrderId,relationship,personName,personGender,idType,idNumber,birthday,cellPhone,email,annualIncome,flightNo,flightTime,occupationCode,occupation_group,destinationCountryText,area,address,
        studentType, schoolType, schoolNature, schoolName, schoolClass, studentId, hdrType, appStatus, policyNo,
        downloadURL, idPeriodStart, idPeriodEnd, isSecurity,insured_time,surrender_time, create_time, update_time)
        VALUES
        <foreach collection="insuredList" item="item" index="index" separator=",">
            (#{item.fhOrderId}, #{item.relationship}, #{item.personName}, #{item.personGender}, #{item.idType},
            #{item.idNumber}, #{item.birthday}, #{item.cellPhone}, #{item.email},#{item.annualIncome},
            #{item.flightNo},
            #{item.flightTime}, #{item.occupationCode},#{item.occupationGroup}, #{item.destinationCountryText},
            #{item.area},
            #{item.address},
            #{item.studentType},#{item.schoolType},#{item.schoolNature},#{item.schoolName},#{item.schoolClass},#{item.studentId},
            #{item.hdrType}, #{item.appStatus}, #{item.policyNo}, #{item.downloadURL}, #{item.idPeriodStart},
            #{item.idPeriodEnd}, #{item.isSecurity},CURRENT_TIMESTAMP(),
            #{item.surrenderTime}, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
        </foreach>
    </insert>

    <insert id="insertOrderPropertyInfo" useGeneratedKeys="true">
        INSERT INTO sm_order_propertyinfo (fhOrderId, propertyInfoIsExist, propertyToRecognizee, housingTheirType,
        hourseType, hourseNo, propertyArea1, propertyArea2, propertyAdress,
        propertyZip, hourseAge, carPlateNo, carNumbType,
        carDrivingNo, licenseNumber, chassisNumber, carColor, registrationDate,
        anufacturerModel, driveArea, approvedNum, engineNo, petLicenceNO,
        petImmuneNO, petImmuneType, houseKeepinger, houseKeepingCensusAddress,
        houseKeepingerIDType,
        houseKeepingerIDNo, houseKeepingerAddress, carNature, create_time,
        update_time)
        VALUES (#{fhOrderId}, #{propertyInfo.propertyInfoIsExist}, #{propertyInfo.propertyToRecognizee},
        #{propertyInfo.housingTheirType}, #{propertyInfo.hourseType}, #{propertyInfo.hourseNo},
        #{propertyInfo.propertyArea1},
        #{propertyInfo.propertyArea2}, #{propertyInfo.propertyAdress}, #{propertyInfo.propertyZip},
        #{propertyInfo.hourseAge}, #{propertyInfo.carPlateNo}, #{propertyInfo.carNumbType},
        #{propertyInfo.carDrivingNo}, #{propertyInfo.licenseNumber}, #{propertyInfo.chassisNumber},
        #{propertyInfo.carColor}, #{propertyInfo.registrationDate}, #{propertyInfo.anufacturerModel},
        #{propertyInfo.driveArea}, #{propertyInfo.approvedNum}, #{propertyInfo.engineNo},
        #{propertyInfo.petLicenceNO},
        #{propertyInfo.petImmuneNO}, #{propertyInfo.petImmuneType}, #{propertyInfo.houseKeepinger},
        #{propertyInfo.houseKeepingCensusAddress}, #{propertyInfo.houseKeepingerIDType},
        #{propertyInfo.houseKeepingerIDNo}, #{propertyInfo.houseKeepingerAddress}, #{propertyInfo.carNature},
        CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
    </insert>


    <select id="listOrders" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT t1.id AS id, t4.channel, t1.create_time AS createTime, t1.paymentTime, t1.fhOrderId AS
        fhOrderId,t1.add_commission_proportion as addCommissionProportion,
        t1.orderState AS orderState, t4.productName AS productName,t11.planName as planName, t1.agentId, t1.productId,
        t1.planId,
        t1.payStatus AS payStatus,
        (case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty end) AS totalAmount,

        t3.policyNo AS policyNo, t3.appStatus AS
        appStatus, t1.startTime AS startTime, t1.endTime AS endTime, t1.qty AS orderQty,
        t1.recommendMasterName AS userMasterName, t1.recommendAdminName AS userAdminName, t1.recommendEntryDate AS
        entryDate, t1.recommendPostName AS postName,
        t5.companyName AS companyName, t3.downloadURL AS downloadURL, t2.personName AS applicantPersonName, t2.idNumber
        AS aplicantIdNumber, t4.companyId AS companyId ,t4.productType,
        t2.cellPhone AS applicantCellPhone, t2.email AS applicantEmail, t3.personName AS insuredPersonName, t3.idNumber
        AS insuredIdNumber, t2.personGender AS applicantPersonGender,
        t3.cellPhone AS insuredCellPhone, t3.email AS insuredEmail, t6.userName AS recommendUserName, t6.userId AS
        recommendUserId, t3.personGender AS insuredPersonGender, t3.relationship AS insuredRelationship,
        t6.userMobile AS recommendUserMobile, t6.regionName AS recommendRegionName, t6.organizationFullName AS
        recommendOrganizationName,

        if(sot.id is null,t8.paymentProportion,t8.paymentProportion * .7) as paymentProportion,

        t8.settlementProportion AS
        settlementProportion, t9.agentMobile, t9.agentName, t9.agentJobNumber,t4.productAttrCode,
        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00') THEN IF(t3.appStatus = 4 or t3.appStatus = 1,
        t13.conversion_amount, 0)
        ELSE IFNULL(t10.converted_premium,
        (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and (t4.productAttrCode = 'group' or t4.productAttrCode = 'employer')) THEN
        -t1.unitPrice*t1.qty ELSE
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'person') THEN 0 ELSE
        t1.unitPrice*t1.qty END)
        END)
        ELSE
        0 END)
        )
        END
        )
        AS convertedAmount,
        t10.proportion AS convertedProportion
        <if test="orderType == 1">
            ,t12.distribution_amount distributionAmount,t12.distribution_state distributionState,
            t12.distribution_order_no distributionOrderNo,t12.distribution_cust_name
            distributionCustName,t12.distribution_level distributionLevel
            ,distribution_type distributionType,t12.is_life_service_partner isLifeServicePartner
        </if>

        ,t1.subChannel
        ,if(sot.id is null,false,true) talkOrder,sot.invite_name inviteName,sot.invite_type inviteType
        ,sod.drainage_share_code AS drainageShareCode,
        sod.drainage_share_name AS drainageShareName,
        sod.drainage_share_account AS drainageShareAccount,
        sod.drainage_platform AS drainagePlatform,
        t1.recommend_channel AS recommendChannel,
        if(t7.th_policy_no is not null,t7.th_policy_no,t3.policyNo) as thPolicyNo,
        t7.th_endorsement_no as endorsementNo,
        t14.label_value as selfInsured,
        t1.submitTime,
        IF(t3.appStatus=1,t1.submitTime,t3.surrender_time) AS account_time
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id=if(t1.commissionId =-1,t7.commission_id,t1.commissionId)
        LEFT JOIN sm_agent t9 ON t9.agentId=t1.agentId
        LEFT JOIN sm_plan t11 ON t11.id=t1.planId
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus and
        t10.enabled_flag = 0
        left join sm_order_drainage sod on sod.order_id = t1.fhOrderId and sod.enabled_flag = 0
        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_label t14 on t14.fh_order_id = t1.fhOrderId  and t14.label_type='self_insurance'
        WHERE t1.enabled_flag=0
        <if test="selfInsured == 1">
            and t14.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t14.fh_order_id IS NULL OR t14.label_value != "Y")
        </if>
        <if test='productType != null and productType == "CX"'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType != null and productType == "OTHER"'>
            AND (t4.productType!='CX' or t4.productType is null)
        </if>

        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>
        <!-- 是否生服合伙人订单 -->
        <if test="isLifeServicePartner != null">
            and t1.is_life_service_order =#{isLifeServicePartner}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='policyFromDateStart != null'>
            <![CDATA[ AND t1.startTime>=#{policyFromDateStart} ]]>
        </if>
        <if test='policyFromDateEnd != null'>
            <![CDATA[ AND t1.startTime<=#{policyFromDateEnd} ]]>
        </if>
        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationFullName=#{orgName}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t6.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>

        <if test="recommendChannel != null and recommendChannel != ''">
            and t1.recommend_channel = #{recommendChannel}
        </if>
        <if test="drainagePlatform != null and drainagePlatform != ''">
            and sod.drainage_platform = #{drainagePlatform}
        </if>
        <if test="drainageShareAccount != null and drainageShareAccount != ''">
            and (
            sod.drainage_share_name = #{drainageShareAccount}
            or sod.drainage_share_account = #{drainageShareAccount}
            or sod.drainage_share_code = #{drainageShareAccount}
            )
        </if>

        <if test="orderType == 1 and distributionType!=null">
          and t12.distribution_type= #{distributionType}
        </if>

        ORDER BY t1.create_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>

    </select>

    <select id="listOrderCommission" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT
        t1.channel,
        t1.accountTime,
        t1.orderCreateTime AS createTime,
        t1.paymentTime,
        t1.fhOrderId,
        t1.totalAmount,
        t1.startTime,
        t1.endTime,
        t2.regionName AS recommendRegionName,
        t2.organizationFullName AS recommendOrganizationName,
        t2.userName AS recommendUserName,
        t2.userMobile AS recommendUserMobile,
        t2.userId AS recommendUserId,
        t1.recommendMasterName AS userMasterName,
        t1.recommendAdminName AS userAdminName ,
        t1.recommendEntryDate AS entryDate,
        t1.recommendPostName AS postName,
        t1.appPersonName AS applicantPersonName,
        t1.appIdNumber AS aplicantIdNumber,
        t1.appCellPhone AS applicantCellPhone,
        t1.insPersonName AS insuredPersonName,
        t1.insIdNumber AS insuredIdNumber,
        t1.insCellPhone AS insuredCellPhone,
        t3.productName,
        t3.id AS productId,
        tp.planName,
        t4.companyName,
        t1.appStatus,
        t1.policyNo,
        t1.downloadURL,
        if(sot.id is null,t1.paymentProportion,t1.paymentProportion * .7) as paymentProportion,
        if(sot.id is null,t1.paymentAmount,t1.paymentAmount * .7) as paymenyCommission,
        t1.settlementProportion AS settlementProportion,
        t1.settlementAmount AS settlementCommission,
        IFNULL(t9.converted_premium,
        (CASE WHEN (t6.appStatus = 4 and t3.productAttrCode = 'person') THEN 0 ELSE
        t1.totalAmount END)
        ) AS convertedAmount,
        t10.amount,
        t10.pay_type,
        t10.pay_period,
        t10.valid_period,
        IFNULL(t9.proportion,0) AS convertedProportion,
        t5.agentMobile, t5.agentName, t5.agentJobNumber,
        t6.appStatus As finalStatus
        ,if(sot.id is null,false,true) talkOrder,sot.invite_name inviteName
        ,sot.invite_type inviteType,
        t3.productType,t7.startTime,
        t11.label_value as selfInsured
        FROM
        sm_order_commission t1
        LEFT JOIN auth_user t2
        ON t1.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t1.productId = t3.id
        LEFT JOIN sm_plan tp
        ON t1.planId = tp.id
        LEFT JOIN sm_company t4
        ON t1.companyId = t4.id
        LEFT JOIN sm_agent t5
        ON t5.agentId = t1.agentId
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.fhOrderId and t1.insIdNumber= t6.idNumber and t1.appStatus = t6.appStatus
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_converted_premium t9
        on t1.fhOrderId = t9.fh_order_id and t9.ins_id_number= t1.insIdNumber and t9.app_status = t1.appStatus
        left join sm_order_policy t10
        on t1.fhOrderId = t10.fh_order_id
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_label t11 on t11.fh_order_id = t1.fhOrderId  and t11.label_type='self_insurance'
        WHERE 1=1
        <if test="selfInsured == 1">
            and t11.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t11.fh_order_id IS NULL OR t11.label_value != "Y")
        </if>
        <if test="orderType!=null">
            and t7.orderType = #{orderType}
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.orderCreateTime>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.orderCreateTime<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t1.appPersonName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t1.appIdNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t1.appCellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.insPersonName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.insIdNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t1.insCellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='companyId != null'>
            AND t1.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.accountTime>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.accountTime<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.appStatus = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

        ORDER BY t1.accountTime DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countOrderCommission" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        sm_order_commission t1
        LEFT JOIN auth_user t2
        ON t1.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t1.productId = t3.id
        LEFT JOIN sm_plan tp
        ON t1.planId = tp.id
        LEFT JOIN sm_company t4
        ON t1.companyId = t4.id
        LEFT JOIN sm_agent t5
        ON t5.agentId = t1.agentId
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.fhOrderId and t1.insIdNumber= t6.idNumber and t1.appStatus = t6.appStatus
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_converted_premium t9
        on t1.fhOrderId = t9.fh_order_id and t9.ins_id_number= t1.insIdNumber and t9.app_status = t1.appStatus
        left join sm_order_policy t10
        on t1.fhOrderId = t10.fh_order_id
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_label t11 on t11.fh_order_id = t1.fhOrderId  and t11.label_type='self_insurance'
        WHERE 1=1
        <if test="selfInsured == 1">
            and t11.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t11.fh_order_id IS NULL OR t11.label_value != "Y")
        </if>
        <if test="orderType!=null">
            and t7.orderType = #{orderType}
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.orderCreateTime>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.orderCreateTime<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t1.appPersonName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t1.appIdNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t1.appCellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.insPersonName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.insIdNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t1.insCellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='companyId != null'>
            AND t1.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.accountTime>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.accountTime<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.appStatus = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

    </select>

    <select id="countOrders" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id=if(t1.commissionId =-1,t7.commission_id,t1.commissionId)
        LEFT JOIN sm_agent t9 ON t9.agentId=t1.agentId
        LEFT JOIN sm_plan t11 ON t11.id=t1.planId
        LEFT JOIN sm_order_converted_premium t10
        on t3.fhOrderId = t10.fh_order_id and t10.ins_id_number= t3.idNumber and t10.app_status = t3.appStatus and
        t10.enabled_flag = 0
        left join sm_order_drainage sod on sod.order_id = t1.fhOrderId and sod.enabled_flag = 0
        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        <if test="orderType == 1">
            left join sm_order_distribution t12 on t12.fh_order_id = t1.fhOrderId
        </if>
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_label t14 on t14.fh_order_id = t1.fhOrderId  and t14.label_type='self_insurance'
        WHERE t1.enabled_flag=0
        <if test="selfInsured == 1">
            and t14.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t14.fh_order_id IS NULL OR t14.label_value != "Y")
        </if>
        <if test='productType != null and productType == "CX"'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType != null and productType == "OTHER"'>
            AND (t4.productType!='CX' or t4.productType is null)
        </if>

        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>

        <!-- 是否生服合伙人订单 -->
        <if test="isLifeServicePartner != null">
            and t1.is_life_service_order =#{isLifeServicePartner}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='policyFromDateStart != null'>
            <![CDATA[ AND t1.startTime>=#{policyFromDateStart} ]]>
        </if>
        <if test='policyFromDateEnd != null'>
            <![CDATA[ AND t1.startTime<=#{policyFromDateEnd} ]]>
        </if>
        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationFullName=#{orgName}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t6.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>

        <if test="recommendChannel != null and recommendChannel != ''">
            and t1.recommend_channel = #{recommendChannel}
        </if>
        <if test="drainagePlatform != null and drainagePlatform != ''">
            and sod.drainage_platform = #{drainagePlatform}
        </if>
        <if test="drainageShareAccount != null and drainageShareAccount != ''">
            and (
            sod.drainage_share_name = #{drainageShareAccount}
            or sod.drainage_share_account = #{drainageShareAccount}
            or sod.drainage_share_code = #{drainageShareAccount}
            )
        </if>

        <if test="orderType == 1 and distributionType!=null">
            and t12.distribution_type= #{distributionType}
        </if>


    </select>

    <select id="getOrderSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        SUM(t1.unitPrice*t1.qty*(CASE WHEN (t1.recommendId IS NULL OR t8.paymentProportion IS NULL) THEN 0 ELSE
        if(sot.id is null,t8.paymentProportion,t8.paymentProportion * .7) END)/100.00) AS paymentAmount,
        SUM(t1.unitPrice*t1.qty*(IF(t8.settlementProportion IS NULL,0,t8.settlementProportion))/100.00) AS
        settlementAmount ,
        SUM(t1.unitPrice*t1.qty*t1.add_commission_proportion/100.00) AS
        addCommissionAmount,
        SUM(
        (CASE WHEN (t1.create_time > '2022-01-01 00:00:00')
        THEN (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN t13.conversion_amount ELSE 0 END)
        ELSE
        IFNULL(t9.converted_premium,
        (CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'group') THEN -t1.unitPrice*t1.qty ELSE
        (CASE WHEN (t3.appStatus = 4 and t4.productAttrCode = 'person') THEN 0 ELSE
        t1.unitPrice*t1.qty END)
        END)
        ELSE
        0 END)
        )
        END
        )
        ) AS convertedAmount,
        SUM(t1.unitPrice*t1.qty) AS orderAmount,
        COUNT(t1.fhOrderId) AS totalQty
        <if test="orderType ==1">
            ,sum(t10.distribution_amount) distributionAmount
        </if>
        FROM sm_order t1
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        <if test='applicantdName != null'>
            LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        </if>
        LEFT JOIN sm_product t4 ON t4.id=t1.productId

        <if test='regionName != null or orgName != null or orgPath != null'>
            LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        </if>
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        LEFT JOIN sm_commission_setting t8 ON t8.id=if(t1.commissionId =-1,t7.commission_id,t1.commissionId)
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        LEFT JOIN sm_order_converted_premium t9
        on t3.fhOrderId = t9.fh_order_id and t9.ins_id_number= t3.idNumber and t9.app_status = t3.appStatus
        <if test="orderType ==1">
            left join sm_order_distribution t10 on t1.fhOrderId = t10.fh_order_id
        </if>
        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        left join sm_order_label  t14  on t14.fh_order_id = t1.fhOrderId  and t14.label_type='self_insurance'
        WHERE t1.enabled_flag = 0
        <if test="selfInsured == 1">
            and t14.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t14.fh_order_id IS NULL OR t14.label_value != "Y")
        </if>
        <if test='productType != null and productType == "CX"'>
            AND t4.productType=#{productType}
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='productType != null and productType == "OTHER"'>
            AND (t4.productType!='CX' or t4.productType is null)
        </if>
        <if test="orderType!= null">
            and t1.orderType = #{orderType}
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='policyFromDateStart != null'>
            <![CDATA[ AND t1.startTime>=#{policyFromDateStart} ]]>
        </if>
        <if test='policyFromDateEnd != null'>
            <![CDATA[ AND t1.startTime<=#{policyFromDateEnd} ]]>
        </if>
        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo=#{policyNo}
        </if>
        <if test='appStatus != null'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationName = #{orgName}
        </if>
        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>
        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>


        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone = #{applicantdName}
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>


        <if test="orderType == 1 and distributionType!=null">
            and t10.distribution_type= #{distributionType}
        </if>
        <if test='orgPath !=null '>
            and t6.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

        <if test="orderType ==1">
            <if test="distributionOrderNo !=null and distributionOrderNo != ''">
                and t10.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
            </if>

            <if test="distributionState !=null and distributionState !=''">
                and t10.distribution_state = #{distributionState}
            </if>
        </if>

    </select>

    <select id="getOrderCommissionSummary" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        SUM(case when l.label_value='Y' then d.amount else t1.totalAmount end) AS orderAmount,

        SUM(if(sot.id is null,case when l.label_value='Y' then d.payment_amount else t1.paymentAmount end,case when
        l.label_value='Y' then d.payment_amount else t1.paymentAmount end * .7)) AS paymentAmount,
        SUM(case when l.label_value='Y' then d.settlement_amount else t1.settlementAmount end) AS settlementAmount,
        ROUND(SUM(t1.totalAmount*t4.add_commission_proportion/100.00),2) AS addCommissionAmount,
        SUM(IFNULL( case when l.label_value='Y' then d.conversion_amount else t9.converted_premium end ,t1.totalAmount))
        as convertedAmount
        FROM
        sm_order_commission t1
        LEFT JOIN auth_user t2
        ON t1.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t1.productId = t3.id
        left join sm_order t4 on t4.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_converted_premium t9
        on t1.fhOrderId = t9.fh_order_id and t9.ins_id_number= t1.insIdNumber and t9.app_status = t1.appStatus
        left join sm_product_label l on t3.id = l.product_id and l.label_type='calc_new_commission' and l.enabled_flag =
        0
        left join sm_commission_detail d on d.order_id = t1.fhorderid
        and d.insured_id_number = t1.insIdNumber and d.policy_no = t1.policyNo and d.policy_status = t1.appStatus
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_label  t10 on t10.fh_order_id = t1.fhOrderId  and t10.label_type='self_insurance'
        WHERE t4.orderType = 0
        <if test="selfInsured == 1">
            and t10.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t10.fh_order_id IS NULL OR t10.label_value != "Y")
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.orderCreateTime>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.orderCreateTime<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
        </if>
        <if test='policyNo !=  null'>
            AND t1.policyNo LIKE CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t1.appPersonName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t1.appIdNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t1.appCellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t1.insPersonName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t1.insIdNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t1.insCellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='companyId != null'>
            AND t1.companyId=#{companyId}
        </if>

        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t1.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.accountTime>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.accountTime<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.appStatus = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
    </select>

    <select id="listUnExtractCustomerOrders" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmExtractOrderVO">
        SELECT DISTINCT t2.personName AS applicantPersonName, t2.idNumber AS aplicantIdNumber, t3.personGender AS
        insuredPersonGender, t2.personGender AS applicantPersonGender,
        t1.fhOrderId AS fhOrderId, t2.cellPhone AS applicantCellPhone, t2.email AS applicantEmail, t3.personName AS
        insuredPersonName, t3.idNumber AS insuredIdNumber,
        t3.cellPhone AS insuredCellPhone, t3.email AS insuredEmail, t1.recommendId AS
        recommendUserId,t1.recommendJobCode,
        t1.recommendMainJobNumber,t1.recommendOrgCode,
        t3.idType AS
        insuredIdTypeName, t1.qty*t1.unitPrice AS policyAmount,
        t3.address AS insuredAddress, t15.regionName AS insuredArea, t4.companyId AS companyId, t2.idType AS
        aplicantIdTypeName,
        t3.annualIncome AS insuredAnnualIncome, t14.occupationName AS insuredOccupationName, t3.relationship AS
        insuredRelationship,
        t6.carPlateNo AS carPlateNo, t6.anufacturerModel AS anufacturerModel, t6.chassisNumber AS chassisNumber,
        t2.birthday AS aplicantBirthday, t3.birthday AS insuredBirthday,
        t6.propertyInfoIsExist AS propertyInfoIsExist, t6.engineNo AS engineNo, t6.approvedNum AS approvedNum,
        t6.hourseType AS hourseType,
        t6.hourseNo AS hourseNo, t6.propertyAdress AS propertyAddress, t6.hourseAge AS hourseAge, t1.agentId,
        t3.studentType,t3.studentId ,t3.schoolType,t3.schoolName,t3.schoolClass,t3.schoolNature,
        t2.areaName as applicantAreaName,t2.addressProvider applicantAddressProvider,t2.area applicantAreaCode,
        t3.areaName as insuredAreaName,t3.addressProvider insuredAddressProvider,t3.area insuredAreaCode,
        au.orgCode, t2.address as applicantAddress
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_propertyinfo t6 ON t6.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t1.productId=t4.id
        LEFT JOIN sm_company_occupation t14 ON t14.occupationCode=t3.occupationCode AND t14.companyId=t4.companyId
        LEFT JOIN sm_company_region t15 ON t15.regionCode=t3.area AND t15.companyId=t4.companyId
        LEFT JOIN auth_user au ON au.userId=t1.recommendId AND au.enabled_flag=0
        WHERE t1.extractFlag IS NULL
        AND t2.idNumber IS NOT NULL AND t3.idNumber IS NOT NULL
        <if test='appStatus != null'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='fhOrderId != null'>
            AND t1.fhOrderId=#{fhOrderId}
        </if>
        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>
        <if test="fhOrderId == null">
            <![CDATA[ and t1.submitTime< date_sub(now(), INTERVAL 30 MINUTE) ]]>
        </if>
        LIMIT 1000
    </select>

    <select id="listOrderInsuredPersonNamesByOrderId" resultType="com.cfpamf.ms.insur.base.bean.IdName">
        SELECT t1.fhOrderId AS id, t1.personName AS name
        FROM sm_order_insured t1
        WHERE t1.fhOrderId IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listJobChangeOrderIds" resultType="java.lang.String">
        SELECT
            DISTINCT t1.fhOrderId
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_product t3 ON t1.productId=t3.id
        WHERE 1=1
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='appStatus != null'>
            AND t2.appStatus=#{appStatus}
        </if>
        <if test='startDate != null'>
            <![CDATA[ AND startTime>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND startTime<=#{endDate} ]]>
        </if>
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
    </select>

    <select id="listJobChangeOrders" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT DISTINCT t1.fhOrderId, t1.startTime, t1.endTime,
        t2.personName AS insuredPersonName, t2.idNumber AS insuredIdNumber, t2.policyNo,
        t21.cellPhone AS applicantCellPhone, t1.qty * t1.unitPrice AS totalAmount
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t21 ON t1.fhOrderId=t21.fhOrderId
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId=t2.fhOrderId
        WHERE 1=1
        <if test='appStatus != null'>
            AND t2.appStatus=#{appStatus}
        </if>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='startDate != null'>
            <![CDATA[ AND t1.create_time>=#{startDate} ]]>
        </if>
        <if test='endDate != null'>
            <![CDATA[ AND t1.create_time<=#{endDate} ]]>
        </if>
        <if test='channel != null'>
            AND t1.channel = #{channel}
        </if>
    </select>

    <select id="getOrderSmsNotifyByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSmsNotifyVO">
        SELECT t1.fhOrderId,t2.id as insuredId, t1.startTime, t1.endTime, t2.personName AS insuredPersonName,
        t2.idNumber AS
        insuredIdNumber, t2.policyNo, t6.agentName, t6.agentMobile,
        t3.productName, t4.planName, t5.userName AS recommendUserName, t5.userMobile AS recommendUserMobile,
        t21.cellPhone AS applicantCellPhone, t1.qty * t1.unitPrice AS totalAmount,
        t3.id productId,t21.idNumber aplicantIdNumber,t1.subChannel,t3.productAttrCode
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t21 ON t1.fhOrderId=t21.fhOrderId
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_product t3 ON t1.productId=t3.id
        LEFT JOIN sm_plan t4 ON t1.planId=t4.id
        LEFT JOIN auth_user t5 ON t5.userId=t1.recommendId AND t5.enabled_flag = 0
        LEFT JOIN sm_agent t6 ON t6.agentId=t1.agentId
        left join sm_order_sms sos on sos.insured_id = t2.id and sos.state = 1
        WHERE 1=1
        <if test='fhOrderId != null'>
            AND t1.fhOrderId=#{fhOrderId}
        </if>
        and sos.id is null
    </select>

    <select id="getGroupOrderNotifyMessage" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSmsNotifyVO">
        SELECT t1.fhOrderId,
        t1.startTime,
        t1.endTime,
        t2.id as insuredId,
        t2.personName AS applicantPersonName,
        t2.idNumber AS aplicantIdNumber,
        t1.policy_no as policyNo,
        t1.appNo,
        t6.agentName,
        t6.agentMobile,
        t3.productName,
        t4.planName,
        t5.userName AS recommendUserName,
        t5.userMobile AS recommendUserMobile,
        t2.cellPhone AS applicantCellPhone,
        t1.totalAmount,
        t3.id productId,
        t1.subChannel,
        t3.productAttrCode
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t1.fhOrderId=t2.fhOrderId
        LEFT JOIN sm_product t3 ON t1.productId=t3.id
        LEFT JOIN sm_plan t4 ON t1.planId=t4.id
        LEFT JOIN auth_user t5 ON t5.userId=t1.recommendId AND t5.enabled_flag = 0
        LEFT JOIN sm_agent t6 ON t6.agentId=t1.agentId
        WHERE t1.fhOrderId = #{orderId}
        and not exists (select 1 from sm_order_sms s where s.insured_id = t2.id and s.state = 1)
    </select>

    <select id="getOrderInsuredByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSmsNotifyVO">
        SELECT t1.fhOrderId,
        t2.id as insuredId,
        t1.startTime,
        t1.endTime,
        t2.personName AS insuredPersonName,
        t2.idNumber AS
        insuredIdNumber,
        t2.cellPhone as insuredCellPhone,
        t2.policyNo,
        t6.agentName,
        t6.agentMobile,
        t3.productName,
        t4.planName,
        t5.userName AS recommendUserName,
        t5.userMobile AS recommendUserMobile,
        t21.cellPhone AS applicantCellPhone,
        t21.personName AS applicantPersonName,
        t1.qty * t1.unitPrice AS totalAmount,
        t3.id productId,
        t21.idNumber aplicantIdNumber,
        t1.subChannel,
        t1.channel,
        t3.productAttrCode,
        t1.unitPrice,
        t7.total_amount as itemTotalAmount,
        t2.appStatus,
        t1.productType as productType,
        t1.paymentTime,
        t1.create_time createTime,
        t2.occupation_group as occupationGroup
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t21 ON t1.fhOrderId = t21.fhOrderId
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t1.productId = t3.id
        LEFT JOIN sm_plan t4 ON t1.planId = t4.id
        LEFT JOIN auth_user t5 ON t5.userId = t1.recommendId AND t5.enabled_flag = 0
        LEFT JOIN sm_agent t6 ON t6.agentId = t1.agentId
        left join sm_order_item t7 on t2.fhOrderId = t7.fh_order_id and t2.idNumber = t7.id_number and
        t2.appStatus = t7.app_status


        WHERE t1.fhOrderId = #{fhOrderId}
    </select>

    <update id="updateOrderCommission">
        UPDATE sm_order
        SET payStatus='2'
        , commissionId=#{commissionId}
        , update_time=CURRENT_TIMESTAMP()
        WHERE fhOrderId = #{orderId}
        AND commissionId IS NULL <![CDATA[  and orderType <> 1
        ]]>
    </update>


    <update id="updateOrderCommissionOnly">
        UPDATE sm_order
        SET commissionId=#{commissionId}
        WHERE fhOrderId = #{orderId}
        <![CDATA[  and orderType <> 1
        ]]>
    </update>


    <update id="updateOrderPaymentTime">
        UPDATE sm_order
        SET paymentTime = CURRENT_TIMESTAMP()
        , update_time=CURRENT_TIMESTAMP()
        WHERE fhOrderId = #{orderId}
        AND paymentTime IS NULL
    </update>

    <update id="updateOrderSpecPaymentTime">
        UPDATE sm_order
        SET paymentTime = #{payTime}
        , update_time=CURRENT_TIMESTAMP()
        WHERE fhOrderId = #{orderId}
        AND paymentTime IS NULL
    </update>

    <update id="updateOrderPaymentId">
        UPDATE sm_order SET payId=#{payId}
        <if test="payUrl!=null">
            , payUrl = #{payUrl}
        </if>
        ,update_time=CURRENT_TIMESTAMP() WHERE fhOrderId=#{orderId}
    </update>

    <select id="listOrderInsuredDetailByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT
        t2.productType,
        t1.*,
        t1.pay_type as payType,
        t1.add_commission_proportion as addCommissionProportion,
        t1.qty AS orderQty,
        t2.id AS productId,
        t2.channel,
        t2.productName,
        t2.productAttrCode,
        t2.create_type productCreateType,
        t2.companyId AS companyId,
        t6.companyName AS companyName,
        t3.planName,
        t3.fhProductId AS fhProductId,
        t4.personName AS applicantPersonName,
        t4.area as applicantArea,
        t4.address as applicantAddress,
        t4.areaName as applicantAreaName,
        t4.email AS applicantEmail,
        t4.birthday AS applicantBirthday,
        t5.birthday AS insuredBirthday,
        t5.email AS insuredEmail,
        t5.idType AS insuredIdType,
        t4.cellPhone AS applicantCellPhone,
        t4.idType AS aplicantIdType,
        t4.idNumber AS aplicantIdNumber,
        t5.idNumber AS insuredIdNumber,
        t4.personGender AS applicantPersonGender,
        t5.relationship AS insuredRelationship,
        t5.personName AS insuredPersonName,
        t5.cellPhone AS insuredCellPhone,
        t5.area AS insuredArea,
        t5.areaName as insuredAreaName,
        t5.address AS insuredAddress,
        t5.annualIncome AS insuredAnnualIncome,
        t5.occupationCode AS insuredOccupationCode,
        t5.otherIdentifiers AS otherIdentifiers,
        t5.schoolNature,
        t5.schoolName,
        t5.schoolClass,
        t5.studentId,
        t5.studentType,
        t5.schoolType,
        t5.appStatus,
        t1.endorsement_no as endorsementNo,
        t5.policyNo,
        t5.downloadURL,
        t4.idPeriodStart AS applicantIdPeriodStart,
        t4.idPeriodEnd AS applicantIdPeriodEnd,
        t5.idPeriodStart AS insuredIdPeriodStart,
        t5.idPeriodEnd AS insuredIdPeriodEnd,
        t5.personGender AS insuredPersonGender,
        t5.isSecurity,
        t1.payId,
        t1.orderType,
        t1.update_time updateTime,
        t1.orderOutType,
        t5.id insuredId,
        t7.drainage_share_code AS drainageShareCode,
        t7.drainage_share_name AS drainageShareName,
        t7.drainage_share_account AS drainageShareAccount,
        t7.drainage_platform AS drainagePlatform,
        t1.recommend_channel AS recommendChannel,
        t2.friendly_reminder as friendlyReminder,
        t2.following_steps as followingSteps
        FROM sm_order t1
        LEFT JOIN sm_product t2 ON t1.productId = t2.id
        LEFT JOIN sm_plan t3 ON t1.planId = t3.id
        LEFT JOIN sm_order_applicant t4 ON t4.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t5.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_company t6 ON t6.id = t2.companyId
        LEFT JOIN sm_order_drainage t7 ON t7.order_id = t1.fhOrderId and t7.enabled_flag = 0
        WHERE t1.fhOrderId = #{orderId}
    </select>

    <select id="listOrderCorrectRecords" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderCorrectRecordVO">
        SELECT t1.*, t3.productName, t4.userName AS updateBy
        FROM sm_order_correct t1
        LEFT JOIN sm_order t2 ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t3 ON t3.id = t2.productId
        LEFT JOIN auth_user t4 ON t4.userId = t1.create_by AND t4.enabled_flag = 0
        WHERE 1=1
        <if test="startDate!=null">
            <![CDATA[ AND t1.create_time>=#{startDate}]]>
        </if>
        <if test="endDate!=null">
            <![CDATA[ AND t1.create_time<=#{endDate}]]>
        </if>
        <if test="policyNo!=null">
            AND t1.policyNo LIKE CONCAT(#{policyNo}, '')
        </if>
        <if test="fieldCode!=null">
            AND t1.fieldCode = #{fieldCode}
        </if>
        ORDER BY t1.update_time DESC
    </select>

    <update id="updateOrderPolicyInfo">
        UPDATE sm_order_insured SET appStatus=#{policyInfo.appStatus},
        policyNo=#{policyInfo.policyNo},
        downloadURL=#{policyInfo.downloadURL},
        <if test='policyInfo.insuredDate != null'>
            insured_time=#{policyInfo.insuredDate},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE 1=1
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
        AND fhOrderId=#{orderId}
    </update>


    <update id="updateOrderPolicyInfoV2">
        UPDATE sm_order_insured SET appStatus=#{policyInfo.appStatus},
        <if test='policyInfo.policyNo != null and policyInfo.policyNo != ""'>
            policyNo=#{policyInfo.policyNo},
        </if>
        <if test='policyInfo.downloadURL != null and policyInfo.downloadURL != ""'>
            downloadURL=#{policyInfo.downloadURL},
        </if>
        <if test='policyInfo.insuredDate != null'>
            insured_time=#{policyInfo.insuredDate},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE 1=1
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
        AND fhOrderId=#{orderId}
    </update>

    <update id="updateOrderPolicyInfoByInduredId">
        UPDATE sm_order_insured SET appStatus=#{policyInfo.appStatus},
        policyNo=#{policyInfo.policyNo},
        downloadURL=#{policyInfo.downloadURL},
        <if test='policyInfo.insuredDate != null'>
            insured_time=#{policyInfo.insuredDate},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE id = #{insuredId}
    </update>

    <update id="updateOrderPolicyInfoByInduredIdV2">
        UPDATE sm_order_insured SET appStatus=#{policyInfo.appStatus},
        <if test='policyInfo.policyNo != null and policyInfo.policyNo != ""'>
            policyNo=#{policyInfo.policyNo},
        </if>
        <if test='policyInfo.downloadURL != null and policyInfo.downloadURL != ""'>
            downloadURL=#{policyInfo.downloadURL},
        </if>
        <if test='policyInfo.insuredDate != null'>
            insured_time=#{policyInfo.insuredDate},
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE id = #{insuredId}
    </update>
    <update id="updateOrderPayStatus">
        UPDATE sm_order
        SET payStatus = #{payStatus}
        <if test="payStatus == '2'">
            ,enabled_flag = 0
        </if>
        WHERE fhOrderId = #{fhOrderId}
    </update>

    <update id="updateOrderAppStatus">
        UPDATE sm_order_insured SET appStatus = #{appStatus} WHERE fhOrderId=#{fhOrderId}
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
    </update>

    <update id="updateOrderAppStatusAndPolicyUrl">
        UPDATE sm_order_insured SET appStatus = #{appStatus}
        <if test="policyUrl != null and policyUrl!=''">
            ,downloadURL = #{policyUrl}
        </if>
        WHERE fhOrderId=#{fhOrderId}
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
    </update>

    <update id="updateOrderItemStatus">
        UPDATE sm_order_item SET app_status = #{appStatus}
        WHERE fh_order_id=#{fhOrderId}
        <if test='idNumber != null'>
            AND id_number=#{idNumber}
        </if>
    </update>

    <update id="updateOrderSurrenderTime">
        UPDATE sm_order_insured SET appStatus='4',
        downloadURL=null,
        surrender_time=CURRENT_TIMESTAMP(),
        update_time=CURRENT_TIMESTAMP()
        WHERE surrender_time IS NULL AND appStatus = '4'
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
        AND fhOrderId=#{orderId}
    </update>

    <update id="updateOrderSurrenderTimeSimple">
        UPDATE sm_order_insured SET
        appStatus='4',
        surrender_time=ifnull(surrender_time,now())
        WHERE
        fhOrderId=#{orderId}
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
    </update>
    <update id="updateOrderSurrender">
        UPDATE sm_order_insured SET
        appStatus='4',
        surrender_time=#{surrenderTime}
        WHERE
        fhOrderId=#{orderId}
        <if test='idNumber != null'>
            AND idNumber=#{idNumber}
        </if>
    </update>


    <update id="updateOrderSurrenderTimeByInsuredId">
        UPDATE sm_order_insured
        SET appStatus='4',
        downloadURL=null,
        surrender_time=CURRENT_TIMESTAMP(),
        update_time=CURRENT_TIMESTAMP()
        WHERE surrender_time IS NULL
        AND appStatus = '4'
        and id = #{insuredId}
    </update>
    <update id="updateOrderSurrenderTimeBatch">
        UPDATE sm_order_insured SET appStatus='4',
        surrender_time=CURRENT_TIMESTAMP(),
        update_time=CURRENT_TIMESTAMP()
        WHERE surrender_time IS NULL
        <if test="idNumbers!= null and idNumbers.size()>0">
            and idNumber in
            <foreach collection="idNumbers" item="idNumber" close=")" open="(" separator=",">
                #{idNumber}
            </foreach>
        </if>
        AND fhOrderId=#{orderId}
    </update>

    <update id="updateOrderSurrenderTimeByPolicyNo">
        UPDATE sm_order_insured SET appStatus='4',
        surrender_time=CURRENT_TIMESTAMP(),
        update_time=CURRENT_TIMESTAMP()
        WHERE surrender_time IS NULL
        <if test="idNumbers!= null and idNumbers.size()>0">
            and idNumber in
            <foreach collection="idNumbers" item="idNumber" close=")" open="(" separator=",">
                #{idNumber}
            </foreach>
        </if>
        AND policyNo=#{policyNo}
    </update>
    <update id="updateSurrender">
        UPDATE sm_order_insured
        SET appStatus='4',
        <if test="surrenderTime!=null">
            surrender_time=#{surrenderTime},
        </if>
        <if test="surrenderTime==null">
            surrender_time=CURRENT_TIMESTAMP(),
        </if>
        update_time=CURRENT_TIMESTAMP()
        WHERE surrender_time IS NULL
        <if test="idNumbers!= null and idNumbers.size()>0">
            and idNumber in
            <foreach collection="idNumbers" item="idNumber" close=")" open="(" separator=",">
                #{idNumber}
            </foreach>
        </if>
        AND policyNo=#{policyNo}
    </update>


    <select id="listOrderInsuredsByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO">
        SELECT
            t1.*,
            t1.cellPhone AS insuredCellPhone,
            t4.cellPhone,
            IFNULL(y2.occupation_name,t2.occupationName) as occupationName,
            t3.regionName AS regionName,
            t1.area AS regionCode,
            t1.occupationCode AS occupationCode,
            t1.occupation_group as occupationGroup,
            t5.branch_id as branchId,
            t1.id as insuredId
        FROM sm_order_insured t1
        LEFT JOIN sm_order_item t5 on t1.fhOrderId=t5.fh_order_id and t1.idNumber=t5.id_number
        LEFT JOIN sm_order so on t1.fhOrderId = so.fhOrderId
        LEFT JOIN sm_product sp on sp.id = so.productId
        LEFT JOIN sm_product_occupation y2 on sp.id=y2.product_id and t1.occupationCode = y2.occupation_code and y2.type=1 and y2.enabled_flag = 0
        LEFT JOIN sm_company_occupation t2 ON t1.occupationCode = t2.occupationCode and t2.companyId = sp.companyId and t2.enabled_flag = 0
        LEFT JOIN sm_company_region t3 ON t1.area = t3.regionCode and t3.enabled_flag = 0 and t1.addressProvider = 1 and t3.companyId = sp.companyId
        LEFT JOIN sm_order_applicant t4 ON t1.fhOrderId = t4.fhOrderId
        WHERE t1.fhOrderId = #{orderId}
    </select>

    <select id="getPropertyInfoByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPropertyVO">
        SELECT *
        FROM sm_order_propertyinfo
        WHERE fhOrderId = #{orderId}
        LIMIT 1
    </select>

    <select id="getBaseOrderInfoByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO">
        SELECT
            t1.*,
            t2.policyNo as policyNo,
            p.productName,
            p.productAttrCode
        FROM sm_order t1
        left join sm_product p on t1.productId=p.id
        left join sm_order_insured t2 on t2.fhOrderId=t1.fhOrderId
        WHERE t1.fhOrderId = #{orderId}
        LIMIT 1
    </select>

    <select id="getBaseOrderInfoByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO">
        SELECT
        t1.*,
        t1.policy_no as policyNo,
        p.productName,
        p.productAttrCode
        FROM sm_order t1
        left join sm_order_insured t2 on t1.fhOrderId=t2.fhOrderId
        left join sm_product p on t1.productId=p.id
        WHERE t2.policyNo = #{policyNo}
        <if test="status!=null">
            and t2.appStatus=#{status}
        </if>
        LIMIT 1
    </select>


    <select id="getEndorOrderById" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO">
        SELECT o.*, e.policy_no as policyNo
        FROM sm_order_endor e
        left join sm_order o on e.raw_order_id = o.fhOrderId
        WHERE e.order_id = #{orderId}
        LIMIT 1
    </select>

    <select id="getEndorOrderByEndorNo" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO">
        SELECT e.order_id as orderId, o.policy_no as policyNo, e.endorsement_no as endorsementNo, e.status as payStatus
        FROM sm_order_endor e
        left join sm_order o on e.raw_order_id = o.fhOrderId
        WHERE e.endorsement_no = #{endorsementNo}
        LIMIT 1
    </select>

    <select id="getFastOrderByOrderId" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO">
        SELECT
            fhOrderId as orderId,
            policy_no as policyNo,
            appNo as proposalNo,
            endorsement_no as endorsementNo,
            pay_type as payType,
            payStatus
        FROM sm_order
        WHERE fhOrderId = #{orderId}
        LIMIT 1
    </select>


    <select id="getFastOrderByEndorsementNo" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO">
        SELECT
        o.fhOrderId as orderId,
        e.order_id as endorId,
        o.policy_no as policyNo,
        e.proposal_no as proposalNo,
        e.endorsement_no as endorsementNo,
        o.payStatus
        FROM sm_order_endor e
        left join sm_order o on e.raw_order_id=o.fhOrderId
        WHERE e.endorsement_no=#{endorsementNo}
        LIMIT 1
    </select>

    <select id="getFastOrderByEndorId" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.order.FastOrderDTO">
        SELECT
        o.fhOrderId as orderId,
        e.order_id as endorId,
        o.policy_no as policyNo,
        e.proposal_no as proposalNo,
        e.endorsement_no as endorsementNo,
        o.payStatus
        FROM sm_order_endor e
        left join sm_order o on e.raw_order_id=o.fhOrderId
        WHERE e.order_id=#{endorId}
        LIMIT 1
    </select>


    <select id="listBaseOrderInsuredByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderInsuredVO">
        SELECT t1.*, t2.appStatus, t2.idNumber AS insuredIdNumber,t2.personName as personName
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
        WHERE t1.fhOrderId = #{orderId}
        and t2.enabled_flag=0
    </select>

    <update id="updateOrderExtractCustomerFlag">
        UPDATE sm_order SET extractFlag=1, update_time=CURRENT_TIMESTAMP() WHERE fhOrderId IN
        <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <insert id="deleteOrder" useGeneratedKeys="true">
        UPDATE sm_order
        SET enabled_flag = 1
        WHERE fhOrderId = #{fhOrderId}
        AND payStatus != '2';
    </insert>

    <insert id="deleteOrderForce">
        UPDATE sm_order
        SET enabled_flag = 1
        WHERE fhOrderId = #{fhOrderId}
    </insert>

    <select id="getOrderInsuredByInsId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderInsuredVO">
        SELECT t1.*, t2.planFactorPriceOptionJson,t2.customerAdminId,t2.customerAdminOrgCode,t2.productId, t4.companyName, t3.companyId, t3.channel,t3.productType, t5.agentName,
        t5.agentMobile
        FROM sm_order_insured t1
        LEFT JOIN sm_order t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_company t4 ON t4.id = t3.companyId
        LEFT JOIN sm_agent t5 ON t5.agentId = t2.agentId
        WHERE t1.id = #{id}
        LIMIT 1
    </select>

    <select id="getOrderToCorrectByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderToCorrectVO">
        SELECT
        t1.appStatus,
        t1.policyNo,
        t1.personName AS insuredPersonName,
        t1.cellPhone AS insuredCellPhone,
        t1.email AS insuredEmail,
        t1.idNumber AS insuredIdNumber,
        t1.fhOrderId,
        t2.personName AS appPersonName,
        t2.cellPhone AS appCellPhone,
        t2.email AS appEmail,
        t2.idNumber AS appIdNumber,
        t3.recommendId,
        t3.agentId,
        t4.companyId,
        t3.channel,
        t4.productAttrCode,
        t3.create_time createTime,t3.planId,t3.productId,
        t1.surrender_time as surrenderTime,
        t3.paymentTime
        FROM sm_order_insured t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order t3 ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id = t3.productId
        WHERE t1.policyNo = #{policyNo}
        <if test="fieldCode=='appName'">
            AND t2.personName = #{oldValue}
        </if>
        <if test="fieldCode=='appMobile'">
            AND t2.cellPhone = #{oldValue}
        </if>
        <if test="fieldCode=='appIdNumber'">
            AND t2.idNumber = #{oldValue}
        </if>
        <if test="fieldCode=='insName'">
            AND t1.personName = #{oldValue}
        </if>
        <if test="fieldCode=='insMobile'">
            AND t1.cellPhone = #{oldValue}
        </if>
        <if test="fieldCode=='insIdNumber'">
            AND t1.idNumber= #{oldValue}
        </if>
        <if test="fieldCode=='appStatus'">
            AND t1.appStatus= #{oldValue}
        </if>
        <if test="fieldCode=='occupationCode'">
            and t1.occupationCode = #{oldValue}
        </if>
        <if test="fieldCode=='totalAmount'">
            and t3.totalAmount = #{oldValue}
        </if>
        <if test="fieldCode=='submitTime'">
            and t3.create_time = #{oldValue}
        </if>
        <if test="idNumber!=null and idNumber!=''">
            AND t1.idNumber= #{idNumber}
        </if>
        LIMIT 1
    </select>

    <select id="batchQueryCorrectOrder" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderToCorrectVO">
        SELECT
        t1.appStatus,
        t1.policyNo,
        t1.personName AS insuredPersonName,
        t1.cellPhone AS insuredCellPhone,
        t1.email AS insuredEmail,
        t1.idNumber AS insuredIdNumber,
        t1.fhOrderId,
        t2.personName AS appPersonName,
        t2.cellPhone AS appCellPhone,
        t2.email AS appEmail,
        t2.idNumber AS appIdNumber,
        t3.recommendId,
        t3.agentId,
        t3.channel,
        t3.create_time createTime,
        t3.planId,
        t3.productId,
        t3.paymentTime,
        t4.companyId,
        t4.productAttrCode
        FROM sm_order_insured t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId = t1.fhOrderId and t2.enabled_flag = 0
        LEFT JOIN sm_order t3 ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id = t3.productId
        WHERE t1.enabled_flag = 0
        and t1.policyNo in
        <foreach collection="policyNoList" item="policyNo" open="(" close=")" separator=",">
            #{policyNo}
        </foreach>
        <if test="projectCode=='appName'">
            AND t2.personName in
            <foreach collection="beforeMessage" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectCode=='appMobile'">
            AND t2.cellPhone in
            <foreach collection="beforeMessage" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectCode=='appIdNumber'">
            AND t2.idNumber in
            <foreach collection="beforeMessage" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectCode=='insName'">
            AND t1.personName in
            <foreach collection="beforeMessage" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectCode=='insMobile'">
            AND t1.cellPhone in
            <foreach collection="beforeMessage" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="projectCode=='insIdNumber'">
            AND t1.idNumber in
            <foreach collection="beforeMessage" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>


    <update id="updateOrderInsuredIdNo">
        UPDATE sm_order_insured
        SET idNumber=#{idNumber},
        update_time=CURRENT_TIMESTAMP()
        WHERE fhOrderId = #{fhOrderId}
        AND personName = #{personName}
    </update>

    <update id="updateOrderApptIdNo">
        UPDATE sm_order_applicant
        SET idNumber=#{idNumber},
        update_time=CURRENT_TIMESTAMP()
        WHERE fhOrderId = #{fhOrderId}
    </update>

    <update id="updateOrderRenewInfo">
        UPDATE sm_order
        SET renewOrderId=#{renewOrderId}
        WHERE fhOrderId = #{fhOrderId}
    </update>

    <update id="updateOrderCustomerAdmin">
        <!--条件中的 or t1.customerAdminJobCode is null 等刷完数以后，最好去掉 2020-07-22-->
        UPDATE sm_order t1 LEFT JOIN sm_order_applicant t2
        ON t1.fhOrderId = t2.fhOrderId
        SET t1.customerAdminId = #{newAdminId},t1.customerAdminJobCode =
        #{newAdminJobCode},t1.customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,t1.customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE
        <choose>
            <when test="oldAdminId=='blank'">
                (t1.customerAdminId is null or t1.customerAdminId = '')
            </when>
            <otherwise>
                t1.customerAdminId = #{oldAdminId}
            </otherwise>
        </choose>
        and (t1.customerAdminJobCode=#{oldAdminJobCode} or
        t1.customerAdminJobCode is null)
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND t2.idNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;
        UPDATE sm_order t1 LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        SET t1.customerAdminId = #{newAdminId},t1.customerAdminJobCode =
        #{newAdminJobCode},t1.customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,t1.customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE
        <choose>
            <when test="oldAdminId=='blank'">
                (t1.customerAdminId is null or t1.customerAdminId = '')
            </when>
            <otherwise>
                t1.customerAdminId = #{oldAdminId}
            </otherwise>
        </choose>
        AND (t1.customerAdminJobCode=#{oldAdminJobCode} or t1.customerAdminJobCode is null)
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND t2.idNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;

        UPDATE sm_order_commission
        SET customerAdminId = #{newAdminId},customerAdminJobCode =
        #{newAdminJobCode},customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE
        <choose>
            <when test="oldAdminId=='blank'">
                (customerAdminId is null or customerAdminId = '')
            </when>
            <otherwise>
                customerAdminId = #{oldAdminId}
            </otherwise>
        </choose>
        and (customerAdminJobCode=#{oldAdminJobCode} or customerAdminJobCode is null)
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND appIdNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR insIdNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;
    </update>
    <!--modify by zhangjian 2021-04-13 条件改一下用工号与机构编码来-->
    <update id="updateOrderCustomerAdminV3">

        UPDATE sm_order t1 LEFT JOIN sm_order_applicant t2
        ON t1.fhOrderId = t2.fhOrderId
        SET t1.customerAdminId = #{newAdminId},t1.customerAdminJobCode =
        #{newAdminJobCode},t1.customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,t1.customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE 1=1
        <if test="oldAdminId != 'blank'">
            and t1.customerAdminId = #{oldAdminId}
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND t2.idNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;
        UPDATE sm_order t1 LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        left join sm_product t3 on t1.productId = t3.id
        SET t1.customerAdminId = #{newAdminId},t1.customerAdminJobCode =
        #{newAdminJobCode},t1.customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,t1.customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE t3.productAttrCode = 'person'
        <if test="oldAdminId != 'blank'">
            and t1.customerAdminId = #{oldAdminId}
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND t2.idNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;

        UPDATE sm_order_commission t
        left join sm_product t1 on t.productId = t1.id
        SET customerAdminId = #{newAdminId},customerAdminJobCode =
        #{newAdminJobCode},customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE 1=1
        <if test="oldAdminId != 'blank'">
            and customerAdminId = #{oldAdminId}
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND (
            appIdNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR (insIdNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            and t1.productAttrCode = 'person')
            )
        </if>
        ;
    </update>
    <update id="updateOrderCustomerAdminV2">

        UPDATE sm_order t1 LEFT JOIN sm_order_applicant t2
        ON t1.fhOrderId = t2.fhOrderId
        SET t1.customerAdminId = #{newAdminId},t1.customerAdminJobCode =
        #{newAdminJobCode},t1.customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,t1.customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE t1.customerAdminId = #{oldAdminId} and t1.customerAdminOrgCode=#{oldCustomerAdminOrgCode}
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND t2.idNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;
        UPDATE sm_order t1 LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        SET t1.customerAdminId = #{newAdminId},t1.customerAdminJobCode =
        #{newAdminJobCode},t1.customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,t1.customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE t1.customerAdminId = #{oldAdminId} and t1.customerAdminOrgCode=#{oldCustomerAdminOrgCode}
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND t2.idNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;

        UPDATE sm_order_commission
        SET customerAdminId = #{newAdminId},customerAdminJobCode =
        #{newAdminJobCode},customerAdminMainJobNumber=#{newAdminMainJobNumber}
        ,customerAdminOrgCode=#{newCustomerAdminOrgCode}
        WHERE customerAdminId = #{oldAdminId} and customerAdminOrgCode=#{oldCustomerAdminOrgCode}
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND appIdNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
            OR insIdNumber IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;
    </update>

    <select id="countOrderProductEffectWaitingDay" resultType="java.lang.Integer">
        SELECT t2.effectWaitingDayMin
        FROM sm_order t1
        LEFT JOIN sm_product t2 ON t2.id = t1.productId
        WHERE t1.fhOrderId = #{orderId}
    </select>

    <select id="countProductPolicyQty" resultType="java.lang.Integer">
        SELECT CASE WHEN SUM(qty) IS NULL THEN 0 ELSE SUM(qty) END
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        WHERE t1.productId = #{productId}
        <![CDATA[  AND t1.endTime > DATE_SUB(CURDATE(), INTERVAL -1 MONTH) ]]>
        AND t2.idNumber = #{idNumber}
        AND t1.payStatus = 2
        AND t2.appStatus IN ('1', '2')
        <if test="occupationCodes!=null and occupationCodes.size()>0">
            and t2.occupationCode in
            <foreach collection="occupationCodes" item="occupationCode" separator=","
                     open="(" close=")">
                #{occupationCode}
            </foreach>
        </if>
    </select>

    <select id="countProductsPolicyQty" resultType="java.lang.Integer">

        SELECT CASE WHEN SUM(qty) IS NULL THEN 0 ELSE SUM(qty) END
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        WHERE t1.productId in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
        <![CDATA[  AND t1.endTime > DATE_SUB(#{startTime}, INTERVAL -1 MONTH) ]]>
        AND t2.idNumber = #{idNumber}
        AND t1.payStatus = 2
        AND t2.appStatus IN ('1', '2')
        <if test="occupationCodes!=null and occupationCodes.size()>0">
            and t2.occupationCode in
            <foreach collection="occupationCodes" item="occupationCode" separator=","
                     open="(" close=")">
                #{occupationCode}
            </foreach>
        </if>
    </select>

    <select id="countProductsPolicyQtyLimit30Day" resultType="java.lang.Integer">
        SELECT CASE WHEN SUM(qty) IS NULL THEN 0 ELSE SUM(qty) END
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        WHERE t1.productId = #{productId}
        AND t2.idNumber = #{idNumber}
        AND t1.payStatus = 2
        AND t2.appStatus IN ('1', '2')
        <![CDATA[  AND t1.endTime > #{startTime} ]]>
        <if test="occupationCodes!=null and occupationCodes.size()>0">
            and t2.occupationCode in
            <foreach collection="occupationCodes" item="occupationCode" separator=","
                     open="(" close=")">
                #{occupationCode}
            </foreach>
        </if>
    </select>

    <insert id="insertOrderPayedCommission">
        INSERT INTO sm_order_commission (channel,
        accountTime,
        orderCreateTime,
        paymentTime,
        fhOrderId,
        productId,
        planId,
        companyId,
        qty,
        recommendId,
        recommendJobCode,
        recommendMainJobNumber,
        recommendOrgCode,
        customerAdminId,
        customerAdminJobCode,
        customerAdminMainJobNumber,
        customerAdminOrgCode,
        agentId,
        totalAmount,
        startTime,
        endTime,
        appPersonName,
        appIdNumber,
        appCellPhone,
        insPersonName,
        insIdNumber,
        insCellPhone,
        appStatus,
        policyNo,
        downloadURL,
        recommendMasterName,
        recommendAdminName,
        recommendEntryDate,
        recommendPostName,
        paymentProportion,
        paymentAmount,
        settlementProportion,
        settlementAmount,
        convertedProportion,
        convertedAmount)
        SELECT t1.channel,
        t1.paymentTime,
        t1.create_time,
        t1.paymentTime,
        t1.fhOrderId,
        t1.productId,
        t1.planId,
        t4.companyId,
        1,
        t1.recommendId,
        t1.recommendJobCode,
        t1.recommendMainJobNumber,
        t1.recommendOrgCode,
        t1.customerAdminId,
        t1.customerAdminJobCode,
        t1.customerAdminMainJobNumber,
        t1.customerAdminOrgCode,
        t1.agentId,
        t1.unitPrice * t1.qty,
        t1.startTime,
        t1.endTime,
        t2.personName,
        t2.idNumber,
        t2.cellPhone,
        t3.personName,
        t3.idNumber,
        t3.cellPhone,
        CASE WHEN t3.appStatus = '4' THEN '1' ELSE t3.appStatus END AS appStatus,
        t3.policyNo,
        t3.downloadURL,
        t1.recommendMasterName,
        t1.recommendAdminName,
        t1.recommendEntryDate,
        t1.recommendPostName,
        t5.paymentProportion,
        ((t1.unitPrice * t1.qty) - ifnull(ao.tax, 0)) * t5.paymentProportion / 100,
        t5.settlementProportion,
        t1.unitPrice * t1.qty * t5.settlementProportion / 100,
        t7.proportion,
        t1.unitPrice * t1.qty * COALESCE(t7.proportion, 100) / 100
        FROM sm_order t1
        left join auto_order ao on ao.order_no = t1.fhOrderId
        LEFT JOIN sm_order_applicant t2
        ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3
        ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        LEFT JOIN sm_commission_setting t5
        ON t5.id = t1.commissionId
        LEFT JOIN sm_product_converted_premium_config t7
        on t1.planId = t7.plan_id
        <![CDATA[ AND t7.end_term_validity >= t1.paymentTime ]]>
        <![CDATA[ AND t7.start_term_validity <= t1.paymentTime ]]>
        WHERE t1.fhOrderId = #{fhOrderId}
        AND t1.payStatus = '2'
        and t1.paymentTime is not null
        AND (
        LOCATE('_', t1.fhOrderId) = 0 OR (LOCATE('_', t1.fhOrderId) > 0 AND t3.appStatus = '1')
        )
    </insert>

    <insert id="insertOrderSurrenderCommission">
        INSERT INTO sm_order_commission (channel,
        accountTime,
        orderCreateTime,
        paymentTime,
        fhOrderId,
        productId,
        planId,
        companyId,
        qty,
        recommendId,
        recommendJobCode,
        recommendMainJobNumber,
        recommendOrgCode,
        customerAdminId,
        customerAdminJobCode,
        customerAdminMainJobNumber,
        customerAdminOrgCode,
        agentId,
        totalAmount,
        startTime,
        endTime,
        appPersonName,
        appIdNumber,
        appCellPhone,
        insPersonName,
        insIdNumber,
        insCellPhone,
        appStatus,
        policyNo,
        downloadURL,
        recommendMasterName,
        recommendAdminName,
        recommendEntryDate,
        recommendPostName,
        paymentProportion,
        paymentAmount,
        settlementProportion,
        settlementAmount,
        convertedProportion,
        convertedAmount)
        SELECT t1.channel,
        t3.surrender_time,
        t1.create_time,
        t1.paymentTime,
        t1.fhOrderId,
        t1.productId,
        t1.planId,
        t4.companyId,
        -1,
        t1.recommendId,
        t1.recommendJobCode,
        t1.recommendMainJobNumber,
        t1.recommendOrgCode,
        t1.customerAdminId,
        t1.customerAdminJobCode,
        t1.customerAdminMainJobNumber,
        t1.customerAdminOrgCode,
        t1.agentId,
        if((sop.surrender_type is not null and sop.surrender_type != 'WT'), 0, -t1.unitPrice * t1.qty),
        t1.startTime,
        t1.endTime,
        t2.personName,
        t2.idNumber,
        t2.cellPhone,
        t3.personName,
        t3.idNumber,
        t3.cellPhone,
        4,
        t3.policyNo,
        t3.downloadURL,
        t1.recommendMasterName,
        t1.recommendAdminName,
        t1.recommendEntryDate,
        t1.recommendPostName,
        t5.paymentProportion,
        if((sop.surrender_type is not null and sop.surrender_type != 'WT'), 0,
        -1 * ((t1.unitPrice * t1.qty) - ifnull(ao.tax, 0)) * t5.paymentProportion / 100),
        t5.settlementProportion,
        if((sop.surrender_type is not null and sop.surrender_type != 'WT'), 0,
        -t1.unitPrice * t1.qty * t5.settlementProportion / 100),
        t7.proportion,
        if((sop.surrender_type is not null and sop.surrender_type != 'WT'), 0,
        -t1.unitPrice * t1.qty * COALESCE(t7.proportion, 100) / 100)

        FROM sm_order t1
        left join auto_order ao on ao.order_no = t1.fhOrderId

        LEFT JOIN sm_order_applicant t2
        ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3
        ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        LEFT JOIN sm_commission_setting t5
        ON t5.id = t1.commissionId
        LEFT JOIN sm_product_converted_premium_config t7
        on t1.planId = t7.plan_id
        <![CDATA[ AND t7.end_term_validity >= t1.paymentTime ]]>
        <![CDATA[ AND t7.start_term_validity <= t1.paymentTime ]]>
        LEFT JOIN sm_order_policy sop on t1.fhOrderId = sop.fh_order_id and sop.visit_status='success'
        WHERE t1.fhOrderId = #{fhOrderId}
        AND t3.idNumber = #{idNumber}
        AND t1.payStatus = '2'
        AND t3.appStatus = '4'
        and t3.surrender_time is not null
    </insert>

    <insert id="insertOrderCorrect">
        INSERT INTO sm_order_correct (fhOrderId,
        policyNo,
        fieldCode,
        fieldName,
        oldValue,
        newValue,
        update_by, idNumber)
        VALUES (#{fhOrderId},
        #{policyNo},
        #{fieldCode},
        #{fieldName},
        #{oldValue},
        #{newValue},
        #{updateBy}, #{idNumber});
    </insert>

    <insert id="batchInsertCorrectOrder">
        INSERT INTO sm_order_correct (
            fhOrderId,
            policyNo,
            fieldCode,
            fieldName,
            oldValue,
            newValue,
            update_by,
            idNumber
        )
        values
        <foreach collection="data" separator="," item="order">
            (
                #{order.fhOrderId},
                #{order.policyNo},
                #{order.fieldCode},
                #{order.fieldName},
                #{order.oldValue},
                #{order.newValue},
                #{order.updateBy},
                #{order.idNumber}
            )
        </foreach>
    </insert>


    <update id="updateOrderCommissionPolicyInfo">
        UPDATE
        sm_order_commission t1
        LEFT JOIN sm_order t2
        ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3
        ON t3.fhOrderId = t1.fhOrderId
        AND t3.idNumber = t1.insIdNumber
        LEFT JOIN sm_commission_setting t4
        ON t4.id = t2.commissionId
        SET t1.appStatus = (CASE WHEN t3.appStatus = '4' THEN '1' ELSE t3.appStatus END),
        t1.recommendId = t2.recommendId,
        t1.customerAdminId = t2.customerAdminId,
        t1.policyNo = t3.policyNo,
        t1.downloadURL = t3.downloadURL,
        t1.paymentProportion = t4.paymentProportion,
        t1.paymentAmount = t2.unitPrice * t2.qty * t4.paymentProportion / 100,
        t1.settlementProportion = t4.settlementProportion,
        t1.settlementAmount = t2.unitPrice * t2.qty * t4.settlementProportion / 100
        WHERE t1.fhOrderId = #{fhOrderId}
        AND t1.appStatus != 4
    </update>

    <delete id="deleteOrderSurrenderCommission">
        DELETE
        FROM sm_order_commission
        WHERE fhOrderId = #{fhOrderId}
        AND insIdNumber = #{idNumber}
        AND appStatus = '4'
    </delete>

    <delete id="deleteOrderCommission">
        DELETE
        FROM sm_order_commission
        WHERE fhOrderId = #{fhOrderId}
    </delete>

    <update id="updateOrderErrorRecommendInfo">
        UPDATE sm_order SET id = id
        <if test="fieldCode=='recommendName'">
            ,recommendId = #{newValue}
            ,customerAdminId = #{newValue}
            ,recommendJobCode = #{newRecommendJobCode}
            ,recommendMainJobNumber=#{newRecommendMainJobNumber}
            ,recommendOrgCode=#{newRecommendOrgCode}
            ,customerAdminJobCode = #{newRecommendJobCode}
            ,customerAdminMainJobNumber=#{newRecommendMainJobNumber}
            ,customerAdminOrgCode = #{newRecommendOrgCode}
            ,recommendPostName = #{recommendPostName}
        </if>
        <if test="fieldCode=='agentName'">
            ,agentId = #{newValue}
        </if>
        <if test="paymentTime!=null">
            ,paymentTime = #{paymentTime}
        </if>
        WHERE fhOrderId = #{fhOrderId}
    </update>

    <update id="batchUpdateOrderRecommendInfo">
        <foreach collection="orderList" item="order" separator=";">
            UPDATE sm_order SET id = id
            <if test="order.projectCode=='recommendName'">
                ,recommendId = #{order.newValue}
                ,customerAdminId = #{order.newValue}
                ,recommendJobCode = #{order.recommendJobCode}
                ,recommendMainJobNumber=#{order.recommendMainJobNumber}
                ,recommendOrgCode=#{order.recommendOrgCode}
                ,customerAdminJobCode = #{order.recommendJobCode}
                ,customerAdminMainJobNumber=#{order.recommendMainJobNumber}
                ,customerAdminOrgCode = #{order.recommendOrgCode}
                ,recommendPostName = #{order.recommendPostName}
            </if>
            <if test="order.paymentTime!=null">
                ,paymentTime = #{order.paymentTime}
            </if>
            WHERE fhOrderId = #{order.orderId}
        </foreach>
    </update>


    <update id="updateOrderErrorRecommendInfoV2">
        UPDATE sm_order SET id = id
        <if test="fieldCode=='recommendName'">
            ,recommendId = #{newValue}
            ,customerAdminId = #{newValue}
            ,recommendJobCode = #{newRecommendJobCode}
            ,recommendMainJobNumber=#{newRecommendMainJobNumber}
            ,recommendOrgCode=#{newRecommendOrgCode}
            ,customerAdminJobCode = #{newRecommendJobCode}
            ,customerAdminMainJobNumber=#{newRecommendMainJobNumber}
            ,customerAdminOrgCode = #{newRecommendOrgCode}
            ,recommendPostName = #{recommendPostName}
        </if>
        <if test="fieldCode=='agentName'">
            ,agentId = #{newValue}
        </if>
        <if test="paymentTime!=null">
            ,paymentTime = #{paymentTime}
        </if>
        WHERE fhOrderId = #{fhOrderId} and (recommendId is null or recommendId='')
    </update>

    <update id="updateOrderErrorAppInfo">
        UPDATE sm_order_applicant SET id = id
        <if test="fieldCode=='appName'">
            ,personName = #{newValue}
        </if>
        <if test="fieldCode=='appMobile'">
            ,cellPhone = #{newValue}
        </if>
        <if test="fieldCode=='appIdNumber'">
            ,idNumber = #{newValue}
            ,personGender = #{sexCode}
            ,birthday = #{birthday}
        </if>
        WHERE fhOrderId = #{fhOrderId}
    </update>

    <update id="batchUpdateOrderAppInfo" parameterType="java.util.List">
        <foreach collection="data" separator=";" item="order">
            UPDATE sm_order_applicant SET id = id
            <if test="order.projectCode=='appName'">
                ,personName = #{order.newValue}
            </if>
            <if test="order.projectCode=='appMobile'">
                ,cellPhone = #{order.newValue}
            </if>
            <if test="order.projectCode=='appIdNumber'">
                ,idNumber = #{order.newValue}
                ,personGender = #{order.sex}
                ,birthday = #{order.birthday}
            </if>
            WHERE fhOrderId = #{order.orderId}
        </foreach>
    </update>

    <update id="batchUpdateOrderInsuredInfo" parameterType="java.util.List">
        <foreach collection="data" separator=";" item="order">
            UPDATE sm_order_insured
            SET id = id
            <if test="order.projectCode=='insName'">
                ,personName = #{order.newValue}
            </if>
            <if test="order.projectCode=='insMobile'">
                ,cellPhone = #{order.newValue}
            </if>
            <if test="order.projectCode=='insIdNumber'">
                ,idNumber = #{order.newValue}
                ,personGender = #{order.sex}
                ,birthday = #{order.birthday}
            </if>
            WHERE fhOrderId = #{order.orderId}
            <if test="order.projectCode=='insName'">
                AND personName = #{order.oldValue}
            </if>
            <if test="order.projectCode=='insMobile'">
                AND cellPhone = #{order.oldValue}
            </if>
            <if test="order.projectCode=='insIdNumber'">
                AND idNumber = #{order.oldValue}
            </if>
        </foreach>
    </update>

    <update id="updateOrderErrorInsInfo">
        UPDATE sm_order_insured SET id = id
        <if test="fieldCode=='insName'">
            ,personName = #{newValue}
        </if>
        <if test="fieldCode=='insMobile'">
            ,cellPhone = #{newValue}
        </if>
        <if test="fieldCode=='insIdNumber'">
            ,idNumber = #{newValue}
            ,personGender = #{sexCode}
            ,birthday = #{birthday}
            ,idType = #{idType}
        </if>
        <if test="fieldCode=='insRelation'">
            ,relationship = #{newValue}
        </if>
        <if test="fieldCode=='occupationCode'">
            ,occupationCode = #{newValue}
        </if>
        WHERE fhOrderId = #{fhOrderId}
        <if test="fieldCode=='insName'">
            AND personName = #{oldValue}
        </if>
        <if test="fieldCode=='insMobile'">
            AND cellPhone = #{oldValue}
        </if>
        <if test="fieldCode=='occupationCode'">
            AND occupationCode = #{oldValue}
        </if>
        <if test="fieldCode=='insIdNumber'">
            AND idNumber = #{oldValue}
        </if>
        <if test="idNumber!=null and idNumber!=''">
            AND idNumber = #{idNumber}
        </if>
    </update>

    <update id="batchUpdateCommissionInfo">
        <foreach collection="data" item="order" separator=";">
            UPDATE sm_order_commission
            SET id = id
            <if test="order.projectCode=='appName'">
                ,appPersonName = #{order.newValue}
            </if>
            <if test="order.projectCode=='appMobile'">
                ,appCellPhone = #{order.newValue}
            </if>
            <if test="order.projectCode=='appIdNumber'">
                ,appIdNumber = #{order.newValue}
            </if>
            <if test="order.projectCode=='insName'">
                ,insPersonName = #{order.newValue}
            </if>
            <if test="order.projectCode=='insMobile'">
                ,insCellPhone = #{order.newValue}
            </if>
            <if test="order.projectCode=='insIdNumber'">
                ,insIdNumber = #{order.newValue}
            </if>
            <if test="order.projectCode=='recommendName'">
                ,recommendId = #{order.newValue}
                ,customerAdminId = #{order.newValue}
                ,recommendJobCode = #{order.recommendJobCode}
                ,recommendMainJobNumber=#{order.recommendMainJobNumber}
                ,recommendOrgCode=#{order.recommendOrgCode}
                ,customerAdminJobCode = #{order.recommendJobCode}
                ,customerAdminMainJobNumber=#{order.recommendMainJobNumber}
                ,customerAdminOrgCode=#{order.recommendOrgCode}
                ,recommendPostName = #{order.recommendPostName}
            </if>
            WHERE fhOrderId = #{order.orderId}
            <if test="order.projectCode=='insName'">
                AND appPersonName = #{order.oldValue}
            </if>
            <if test="order.projectCode=='appMobile'">
                AND appCellPhone = #{order.oldValue}
            </if>
            <if test="order.projectCode=='appIdNumber'">
                AND appIdNumber = #{order.oldValue}
            </if>
            <if test="order.projectCode=='insName'">
                AND insPersonName = #{order.oldValue}
            </if>
            <if test="order.projectCode=='insMobile'">
                AND insCellPhone = #{order.oldValue}
            </if>
            <if test="order.projectCode=='insIdNumber'">
                AND insIdNumber = #{order.oldValue}
            </if>
        </foreach>
    </update>

    <update id="updateOrderErrorCommissionInfo">
        UPDATE sm_order_commission SET id = id
        <if test="fieldCode=='appName'">
            ,appPersonName = #{newValue}
        </if>
        <if test="fieldCode=='appMobile'">
            ,appCellPhone = #{newValue}
        </if>
        <if test="fieldCode=='appIdNumber'">
            ,appIdNumber = #{newValue}
        </if>
        <if test="fieldCode=='insName'">
            ,insPersonName = #{newValue}
        </if>
        <if test="fieldCode=='insMobile'">
            ,insCellPhone = #{newValue}
        </if>
        <if test="fieldCode=='insIdNumber'">
            ,insIdNumber = #{newValue}
        </if>
        <if test="fieldCode=='recommendName'">
            ,recommendId = #{newValue}
            ,customerAdminId = #{newValue}
            ,recommendJobCode = #{newRecommendJobCode}
            ,recommendMainJobNumber=#{newRecommendMainJobNumber}
            ,recommendOrgCode=#{newRecommendOrgCode}
            ,customerAdminJobCode = #{newRecommendJobCode}
            ,customerAdminMainJobNumber=#{newRecommendMainJobNumber}
            ,customerAdminOrgCode=#{newRecommendOrgCode}
            ,recommendPostName = #{recommendPostName}
        </if>
        <if test="fieldCode=='agentName'">
            ,agentId = #{newValue}
        </if>
        <if test="paymentTime!=null">
            ,paymentTime = #{paymentTime}
            ,accountTime = #{paymentTime}
        </if>
        <if test="fieldCode=='submitTime'">
            submitTime=#{newValue},
            create_time = #{newValue}
        </if>
        <if test="fieldCode=='totalAmount'">
            totalAmount=#{newValue},
            unitPrice = round(#{newValue}/qty,2)
        </if>
        <if test="fieldCode=='planId'">
            planId = #{planId},
            productId = #{productId}
        </if>
        WHERE fhOrderId = #{fhOrderId}
        <if test="fieldCode=='insName'">
            AND appPersonName = #{oldValue}
        </if>
        <if test="fieldCode=='appMobile'">
            AND appCellPhone = #{oldValue}
        </if>
        <if test="fieldCode=='appIdNumber'">
            AND appIdNumber = #{oldValue}
        </if>
        <if test="fieldCode=='insName'">
            AND insPersonName = #{oldValue}
        </if>
        <if test="fieldCode=='insMobile'">
            AND insCellPhone = #{oldValue}
        </if>
        <if test="fieldCode=='insIdNumber'">
            AND insIdNumber = #{oldValue}
        </if>
    </update>


    <update id="updateOrderErrorCommissionRecommendInfoV2">
        UPDATE sm_order_commission SET id = id

        <if test="fieldCode=='recommendName'">
            ,recommendId = #{newValue}
            ,customerAdminId = #{newValue}
            ,recommendJobCode = #{newRecommendJobCode}
            ,recommendMainJobNumber=#{newRecommendMainJobNumber}
            ,recommendOrgCode=#{newRecommendOrgCode}
            ,customerAdminJobCode = #{newRecommendJobCode}
            ,customerAdminMainJobNumber=#{newRecommendMainJobNumber}
            ,customerAdminOrgCode=#{newRecommendOrgCode}
            ,recommendPostName = #{recommendPostName}
        </if>
        <if test="paymentTime!=null">
            ,paymentTime = #{paymentTime}
            ,accountTime = #{paymentTime}
        </if>

        WHERE fhOrderId = #{fhOrderId} and (recommendId is null or recommendId = '')
        <if test="fieldCode=='insIdNumber'">
            AND insIdNumber = #{oldValue}
        </if>
    </update>

    <update id="updateSmOrderInsuredBatch" parameterType="java.util.List">
        <foreach collection="insuredList" item="item" separator=";">
            update sm_order_insured
            set
            appStatus = #{item.appStatus},
            email = #{item.email},
            cellPhone = #{item.cellPhone},
            personGender = #{item.personGender},
            birthday = #{item.birthday},
            personName = #{item.personName},
            surrender_time = #{item.surrenderTime}
            where fhOrderId = #{item.fhOrderId}
            and idNumber = #{item.idNumber}
            and policyNo = #{item.policyNo}
        </foreach>
    </update>

    <update id="updateOrderUnit">
        update sm_order so
        inner join (select soi.fhOrderId, count(1) pnums
        from sm_order_insured soi
        where soi.fhOrderId in
        <foreach collection="fhOrderIds" item="item" separator=","
                 open="(" close=")">
            #{item}
        </foreach>
        group by soi.fhOrderId) t on so.fhOrderId = t.fhOrderId
        set so.unitPrice = round(so.totalAmount / t.pnums,2)
        where 1 = 1
    </update>


    <select id="getOrderCommissionAccountTime" resultType="java.util.Date">
        SELECT accountTime
        FROM sm_order_commission
        WHERE fhOrderId = #{fhOrderId}
        AND insIdNumber = #{insIdNumber}
        AND accountTime IS NOT NULL;
    </select>
    <select id="listOrderIdNotPay" resultType="java.lang.String">
        select *
        from sm_order
        where
        <![CDATA[ submitTime > #{start}
          and submitTime < #{end}
          and payStatus <> 2
        ]]>

    </select>
    <select id="selectOrderIdByOrderIds" resultType="java.lang.String">
        select fhOrderId from sm_order where fhOrderId in
        <foreach collection="fhOrderIds" item="item" separator=","
                 open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="listOrderInsuredByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured">
        SELECT
            soi.idNumber,
            soi.policyNo,
            soi.personName,
            soi.personGender,
            soi.appStatus,
            so.fhOrderId,
            so.channel,
            so.planId,
            so.customerAdminId
        FROM
            sm_order_insured soi
                LEFT JOIN
            sm_order so ON soi.fhOrderId = so.fhOrderId
        WHERE
                soi.fhOrderId LIKE (
                SELECT
                    CONCAT( fhOrderId, '%')
                FROM
                    sm_order_insured
                WHERE
                    policyNo = #{policyNo}
            LIMIT 1
            )
        ORDER BY
            soi.create_time;
    </select>


    <select id="listIndividualOrderInsuredByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured">
    select idNumber,
    policyNo,
    personName,
    personGender,
    appStatus,
    so.fhOrderId,
    so.channel,
    so.planId,
    so.customerAdminId
    from sm_order_insured soi
    left join sm_order so on soi.fhOrderId = so.fhOrderId
    where policyNo = #{policyNo}
    order by soi.create_time
    </select>

    <select id="listOneInsured" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured">
        select
            idNumber,
            policyNo,
            personName,
            personGender,
            appStatus,
            so.fhOrderId,
            so.channel,
            so.planId,
            so.customerAdminId
        from sm_order_insured soi
        left join sm_order so on soi.fhOrderId = so.fhOrderId
        where policyNo = #{policyNo}
        order by soi.create_time
        limit 1
    </select>

    <select id="queryInsuredList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.FastOrderInsuredPo">
        select
        a.id,
        a.fhOrderId,
        c.submitTime,
        b.active_branch_id as activeBranchId,
        b.branch_id as branchId,
        b.total_amount as premium,
        b.insured_amount as amount,
        b.product_id as productId,
        b.plan_id as planId,
        b.plan_code as planCode,
        a.personName,
        a.personGender,
        a.birthday,
        a.idType,
        a.idNumber,
        a.occupationCode,
        a.occupation_group as occupationGroup,
        a.isSecurity,
        a.cellPhone,
        a.appStatus,
        a.relationship,
        a.downloadURL,
        a.email,
        a.insured_time as insuredTime,
        b.th_policy_no as policyNo
        from sm_order_insured a
        left join sm_order_item b on a.fhOrderId = b.fh_order_id and a.idNumber=b.id_number and b.enabled_flag=0
        left join sm_order c on a.fhOrderId=c.fhOrderId
        where b.th_policy_no = #{policyNo}
        <if test="branchIdList!=null and branchIdList.size>0 ">
            and b.branch_id in
            <foreach collection="branchIdList" close=")" open="(" item="entry" separator=",">
                #{entry}
            </foreach>
        </if>
        and a.enabled_flag=0
        order by a.id
    </select>

    <select id="listCommissionOrderIdByLate" resultType="java.lang.String">
        select so.fhOrderId, so.paymentTime, so.commissionId
        from sm_order so
        left join sm_order_commission soc on so.fhOrderId = soc.fhOrderId
        where so.payStatus = 2
        and soc.id is null
        and so.paymentTime is not null
        and so.commissionId is not null
        <![CDATA[   and so.submitTime > #{start}
          and so.paymentTime < #{end}
        ]]>
    </select>
    <select id="listCommissionCancelOrderIdByLateCancel" resultType="java.lang.String">
        select so.fhOrderId, so.paymentTime, so.commissionId
        from sm_order so
        left join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
        left join sm_order_commission soc on so.fhOrderId = soc.fhOrderId and soc.appStatus = 4
        where so.payStatus = 2
        and soc.id is null
        and so.paymentTime is not null
        and soi.surrender_time is not null
        and so.commissionId is not null
        <![CDATA[ and so.submitTime > #{start}
          and soi.surrender_time < #{end}
          ]]>
        and soi.appStatus = 4
    </select>
    <select id="listOrdersV2" resultType="com.cfpamf.ms.insur.admin.pojo.vo.order.SmOrderNewVO">

        SELECT t1.id AS id,
        t4.channel,
        t1.create_time AS createTime,
        t1.paymentTime,
        t1.fhOrderId AS fhOrderId,
        t1.orderState AS orderState,
        t4.productName AS productName,
        t1.agentId,
        t1.productId,
        t1.planId,
        t1.payStatus AS payStatus,
        t1.startTime AS startTime,
        t5.companyName AS companyName,
        t2.personName AS applicantPersonName,
        t2.idNumber
        AS aplicantIdNumber,
        t4.companyId AS companyId,
        t2.cellPhone AS applicantCellPhone,
        t2.email AS applicantEmail,
        t2.personGender AS applicantPersonGender,
        t6.userName AS recommendUserName,
        t6.userId AS recommendUserId,
        t6.userMobile AS recommendUserMobile,
        t6.regionName AS recommendRegionName,
        t6.organizationFullName AS recommendOrganizationName,
        t8.paymentProportion AS paymentProportion,
        t8.settlementProportion AS settlementProportion,
        t4.productAttrCode,
        t1.subChannel,
        group_concat(t3.personName) AS insuredPersonName,
        group_concat(distinct t3.policyNo) policyNo,
        group_concat(distinct t3.appStatus) appStatus

        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id = t1.productId
        LEFT JOIN sm_company t5 ON t5.id = t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId = t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id = t1.commissionId

        where t1.fhOrderId in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        group by t1.fhOrderId
    </select>
    <select id="listFhOrderIdOrders" resultType="java.lang.String">
        SELECT distinct t1.fhOrderId
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        LEFT JOIN sm_company t5 ON t5.id=t4.companyId
        LEFT JOIN auth_user t6 ON t6.userId=t1.recommendId AND t6.enabled_flag = 0
        LEFT JOIN sm_commission_setting t8 ON t8.id=t1.commissionId
        LEFT JOIN sm_agent t9 ON t9.agentId=t1.agentId
        WHERE t1.enabled_flag=0
        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='policyFromDateStart != null'>
            <![CDATA[ AND t1.startTime>=#{policyFromDateStart} ]]>
        </if>
        <if test='policyFromDateEnd != null'>
            <![CDATA[ AND t1.startTime<=#{policyFromDateEnd} ]]>
        </if>
        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t1.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t1.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo=#{policyNo}
        </if>
        <if test='appStatus != null'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='payStatus != null'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='regionName != null'>
            AND t6.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t6.organizationFullName=#{orgName}
        </if>
        <if test='applicantdName != null'>
            AND ((t2.personName LIKE CONCAT(#{applicantdName},'%')) OR (t2.idNumber LIKE CONCAT(#{applicantdName},'%'))
            OR (t2.cellPhone LIKE CONCAT(#{applicantdName},'%')))
        </if>
        <if test='insuredName != null'>
            AND ((t3.personName LIKE CONCAT(#{insuredName},'%')) OR (t3.idNumber LIKE CONCAT(#{insuredName},'%')) OR
            (t3.cellPhone LIKE CONCAT(#{insuredName},'%')))
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='agentId != null'>
            AND t1.agentId=#{agentId}
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>

        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>


        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='userId != null'>
            AND t1.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t6.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>

        ORDER BY t1.create_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="listSmOrderInsuredByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        SELECT t1.*
        FROM sm_order_insured t1
        WHERE t1.fhOrderId = #{orderId}
        and t1.enabled_flag = 0
    </select>
    <select id="countApplicantByPhone" resultType="java.lang.Integer">

        select count(distinct if(t1.idNumber = #{idCard}, null, t1.idNumber)) cts
        from sm_order_applicant t1
        left join sm_order t2 on t1.fhOrderId = t2.fhOrderId
        where t1.cellPhone = #{phone}
        and t1.create_time > #{startTime}
        and subChannel != 'import'
    </select>


    <select id="selectOrderApplicantByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant">
        select *
        from sm_order_applicant
        where fhOrderId = #{fhOrderId}
    </select>

    <select id="listOrderId" resultType="java.lang.String">
        select fhOrderId from sm_order where fhOrderId like concat(#{orderId},'%');
    </select>



    <select id="listSmOrderByOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrder">
        select * from sm_order where fhOrderId in
        <foreach collection="fhOrderIds" item="item" separator=","
                 open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="listOriginalOrderByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrder">
        select *
        from sm_order
        where fhOrderId LIKE CONCAT(#{fhOrderId}, '%')
        and payStatus = 2;
    </select>

    <select id="findOrderCommissionByOderIdList" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        select fh_order_id as fhOrderId,sum(IFNULL(converted_premium , 0)) as convertedAmount from
        sm_order_converted_premium
        where enabled_flag = '0' and fh_order_id in
        <foreach item="item" index="index" open="(" separator="," close=")" collection="orderIdList">
            #{item}
        </foreach>
        group by fh_order_id
    </select>


    <select id="listOriginalOrderByOrderIdAndIdNumber"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderInsuredVO">

        select
        s.*,
        i.appStatus,
        i.idNumber AS insuredIdNumber
        from sm_order s,sm_order_insured i
        where s.fhOrderId = i.fhOrderId
        and s.payStatus=2
        and i.appStatus=1
        and s.fhOrderId LIKE CONCAT(#{fhOrderId},'%')
        and i.idNumber in
        <foreach collection="idNumberList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by s.id desc;
    </select>
    <select id="getSurrenderTime" resultType="java.util.Date">
        select max(surrender_time) surrenderTime
        from sm_order_insured
        where fhOrderId = #{fhOrderId}
    </select>


    <!--2021-02-22 众安团险后佣金计算调整脚本-->
    <insert id="insertOrderPayedCommissionV2">
        INSERT INTO sm_order_commission (channel,
        accountTime,
        orderCreateTime,
        paymentTime,
        fhOrderId,
        productId,
        planId,
        companyId,
        qty,
        recommendId,
        recommendJobCode,
        recommendMainJobNumber,
        recommendOrgCode,
        customerAdminId,
        customerAdminJobCode,
        customerAdminMainJobNumber,
        customerAdminOrgCode,
        agentId,
        totalAmount,
        startTime,
        endTime,
        appPersonName,
        appIdNumber,
        appCellPhone,
        insPersonName,
        insIdNumber,
        insCellPhone,
        appStatus,
        policyNo,
        downloadURL,
        recommendMasterName,
        recommendAdminName,
        recommendEntryDate,
        recommendPostName,
        paymentProportion,
        paymentAmount,
        settlementProportion,
        settlementAmount,
        convertedProportion,
        convertedAmount)
        SELECT t1.channel,
        t1.paymentTime,
        t1.create_time,
        t1.paymentTime,
        t1.fhOrderId,
        t1.productId,
        t1.planId,
        t4.companyId,
        1,
        t1.recommendId,
        t1.recommendJobCode,
        t1.recommendMainJobNumber,
        t1.recommendOrgCode,
        t1.customerAdminId,
        t1.customerAdminJobCode,
        t1.customerAdminMainJobNumber,
        t1.customerAdminOrgCode,
        t1.agentId,
        t6.total_amount,
        t1.startTime,
        t1.endTime,
        t2.personName,
        t2.idNumber,
        t2.cellPhone,
        t3.personName,
        t3.idNumber,
        t3.cellPhone,
        CASE WHEN t3.appStatus = '4' THEN '1' ELSE t3.appStatus END AS appStatus,
        t3.policyNo,
        t3.downloadURL,
        t1.recommendMasterName,
        t1.recommendAdminName,
        t1.recommendEntryDate,
        t1.recommendPostName,
        t5.paymentProportion,
        (t6.total_amount - ifnull(ao.tax, 0)) * t5.paymentProportion / 100,
        t5.settlementProportion,
        t6.total_amount * t5.settlementProportion / 100,
        t7.proportion,
        t6.total_amount * COALESCE(t7.proportion, 100) / 100
        FROM sm_order t1
        left join auto_order ao on ao.order_no = t1.fhOrderId
        LEFT JOIN sm_order_applicant t2
        ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3
        ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        LEFT join sm_order_item t6
        on t1.fhOrderId = t6.fh_order_id
        LEFT JOIN sm_commission_setting t5
        ON t5.id = if(t1.commissionId > 0, t1.commissionId, t6.commission_id)
        LEFT JOIN sm_product_converted_premium_config t7
        on t1.planId = t7.plan_id
        <![CDATA[ AND t7.end_term_validity >= t1.paymentTime ]]>
        <![CDATA[ AND t7.start_term_validity <= t1.paymentTime ]]>
        WHERE t1.fhOrderId = #{fhOrderId}
        AND t1.payStatus = '2'
        and t1.paymentTime is not null
        and t3.idNumber = t6.id_number
        and t3.appStatus = t6.app_status
        AND (
        LOCATE('_', t1.fhOrderId) = 0 OR (LOCATE('_', t1.fhOrderId) > 0 AND t3.appStatus = '1')
        )
    </insert>

    <insert id="insertOrderSurrenderCommissionV2">
        INSERT INTO sm_order_commission (channel,
        accountTime,
        orderCreateTime,
        paymentTime,
        fhOrderId,
        productId,
        planId,
        companyId,
        qty,
        recommendId,
        recommendJobCode,
        recommendMainJobNumber,
        recommendOrgCode,
        customerAdminId,
        customerAdminJobCode,
        customerAdminMainJobNumber,
        customerAdminOrgCode,
        agentId,
        totalAmount,
        startTime,
        endTime,
        appPersonName,
        appIdNumber,
        appCellPhone,
        insPersonName,
        insIdNumber,
        insCellPhone,
        appStatus,
        policyNo,
        downloadURL,
        recommendMasterName,
        recommendAdminName,
        recommendEntryDate,
        recommendPostName,
        paymentProportion,
        paymentAmount,
        settlementProportion,
        settlementAmount,
        convertedProportion,
        convertedAmount)
        SELECT t1.channel,
        -- t3.surrender_time,
        ifnull(t3.surrender_time, t1.paymentTime),
        t1.create_time,
        t1.paymentTime,
        t1.fhOrderId,
        t1.productId,
        t1.planId,
        t4.companyId,
        -1,
        t1.recommendId,
        t1.recommendJobCode,
        t1.recommendMainJobNumber,
        t1.recommendOrgCode,
        t1.customerAdminId,
        t1.customerAdminJobCode,
        t1.customerAdminMainJobNumber,
        t1.customerAdminOrgCode,
        t1.agentId,
        -t6.total_amount,
        t1.startTime,
        t1.endTime,
        t2.personName,
        t2.idNumber,
        t2.cellPhone,
        t3.personName,
        t3.idNumber,
        t3.cellPhone,
        4,
        t3.policyNo,
        t3.downloadURL,
        t1.recommendMasterName,
        t1.recommendAdminName,
        t1.recommendEntryDate,
        t1.recommendPostName,
        t5.paymentProportion,
        -1 * (t6.total_amount - ifnull(ao.tax, 0)) * t5.paymentProportion / 100,
        t5.settlementProportion,
        -t6.total_amount * t5.settlementProportion / 100,
        t7.proportion,
        -t6.total_amount * COALESCE(t7.proportion, 100) / 100
        FROM sm_order t1
        left join auto_order ao on ao.order_no = t1.fhOrderId
        LEFT JOIN sm_order_applicant t2
        ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3
        ON t3.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_product t4
        ON t4.id = t1.productId
        LEFT join sm_order_item t6
        on t1.fhOrderId = t6.fh_order_id
        LEFT JOIN sm_commission_setting t5
        ON t5.id = if(t1.commissionId > 0, t1.commissionId, t6.commission_id)
        LEFT JOIN sm_product_converted_premium_config t7
        on t1.planId = t7.plan_id
        <![CDATA[ AND t7.end_term_validity >= t1.paymentTime ]]>
        <![CDATA[ AND t7.start_term_validity <= t1.paymentTime ]]>
        WHERE t1.fhOrderId = #{fhOrderId}
        AND t3.idNumber = #{idNumber}
        AND t1.payStatus = '2'
        AND t3.appStatus = '4'
        and t3.idNumber = t6.id_number
        and t3.appStatus = t6.app_status
        and t3.surrender_time is not null
    </insert>

    <update id="updateOrderCommissionPolicyInfoV2">
        UPDATE
        sm_order_commission t1
        LEFT JOIN sm_order t2
        ON t2.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t3
        ON t3.fhOrderId = t1.fhOrderId
        AND t3.idNumber = t1.insIdNumber
        left join sm_order_item t6 on t3.fhOrderId = t6.fh_order_id and t3.policyNo = t6.policy_no and
        t3.idNumber = t6.id_number and t3.appStatus = t6.app_status
        LEFT JOIN sm_commission_setting t4
        ON t4.id = if(t2.commissionId > 0, t2.commissionId, t6.commission_id)
        SET t1.appStatus = (CASE WHEN t3.appStatus = '4' THEN '1' ELSE t3.appStatus END),
        t1.policyNo = t3.policyNo,
        t1.recommendId = t2.recommendId,
        t1.customerAdminId = t2.customerAdminId,
        t1.downloadURL = t3.downloadURL,
        t1.paymentProportion = t4.paymentProportion,
        t1.paymentAmount = t6.total_amount * t4.paymentProportion / 100,
        t1.settlementProportion = t4.settlementProportion,
        t1.settlementAmount = t6.total_amount * t4.settlementProportion / 100
        WHERE t1.fhOrderId = #{fhOrderId}
        AND t1.appStatus != 4
    </update>
    <update id="updateOrderErrorInfo">
        UPDATE sm_order SET
        <if test="fieldCode=='submitTime'">
            submitTime=#{newValue},
            create_time = #{newValue}
        </if>
        <if test="fieldCode=='totalAmount'">
            totalAmount=#{newValue},
            unitPrice = round(#{newValue}/qty,2)
        </if>
        <if test="fieldCode=='planId'">
            planId = #{planId},
            productId = #{productId}
        </if>
        WHERE fhOrderId = #{fhOrderId}
    </update>


    <insert id="insertOrderCommissionBatch" useGeneratedKeys="true">
        INSERT INTO sm_order_commission (channel, subChannel, fhOrderId, productId, planId, unitPrice, qty, totalAmount,
        startTime, endTime, validPeriod, underWritingAge, recommendId, agentId, customerAdminId,
        recommendJobCode,customerAdminJobCode,recommendMainJobNumber,customerAdminMainJobNumber,recommendOrgCode,customerAdminOrgCode,
        submitTime, noticeCode, noticeMsg, orderState,paymentTime, payStatus, commissionId, appNo,
        create_by, create_time, update_by, update_time, enabled_flag)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            (#{dto.channel}, #{dto.subChannel}, #{dto.fhOrderId}, #{dto.productId}, #{dto.planId}, #{dto.unitPrice},
            #{dto.qty},
            #{dto.orderInfo.totalAmount}, #{dto.orderInfo.startTime}, #{dto.orderInfo.endTime},
            #{dto.orderInfo.validPeriod},
            #{dto.orderInfo.underWritingAge}, #{dto.productInfo.recommendId}, #{dto.agentId},
            #{dto.productInfo.customerAdminId},
            #{dto.recommendJobCode},#{dto.customerAdminJobCode},#{dto.recommendMainJobNumber},#{dto.customerAdminMainJobNumber},
            #{dto.recommendOrgCode},#{dto.customerAdminOrgCode},
            #{dto.orderInfo.submitTime}, #{dto.noticeCode}, #{dto.noticeMsg}, #{dto.orderState},CURRENT_TIMESTAMP(),
            #{dto.payStatus}, #{dto.commissionId},
            #{dto.appNo}, #{dto.modifyBy}, #{dto.createTime}, #{dto.modifyBy},#{dto.orderInfo.submitTime} , 0)
        </foreach>
    </insert>

    <update id="updateSmOrderBatch" parameterType="java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update sm_order set recommendId=#{dto.productInfo.recommendId},recommendJobCode=#{dto.recommendJobCode},
            recommendMainJobNumber=#{dto.recommendMainJobNumber},recommendOrgCode=#{dto.recommendOrgCode},
            customerAdminId=#{dto.productInfo.customerAdminId},customerAdminJobCode=#{dto.customerAdminJobCode},
            customerAdminMainJobNumber=#{dto.customerAdminMainJobNumber},customerAdminOrgCode=#{dto.customerAdminOrgCode},
            startTime=#{dto.orderInfo.startTime},endTime=#{dto.orderInfo.endTime},totalAmount=#{dto.orderInfo.totalAmount},
            qty= #{dto.qty},unitPrice= #{dto.unitPrice},commissionId= #{dto.commissionId}
            where fhOrderId=#{dto.fhOrderId}

        </foreach>
    </update>
    <!-- 车险用(用于一个订单一个被保人) ，非车险不能只能根据订单id与保单号来更新-->
    <update id="updateCarSmOrderInsuredBatch" parameterType="java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            <foreach collection="dto.insuredPerson" item="item" index="index" separator=";">
                update sm_order_insured set relationship=#{item.relationship},personName=#{item.personName},
                personGender= #{item.personGender},idType=#{item.idType},
                idNumber=#{item.idNumber},birthday= #{item.birthday},
                cellPhone=#{item.cellPhone},email=#{item.email},policyNo=#{item.policyNo}
                where fhOrderId=#{dto.fhOrderId}
            </foreach>
        </foreach>
    </update>

    <update id="updateSmOrderInsuredCancelBatch" parameterType="java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            <foreach collection="dto.insuredPerson" item="item" index="index" separator=";">
                update sm_order_insured set appStatus=#{item.appStatus},surrender_time=now()
                where fhOrderId=#{dto.fhOrderId} and policyNO=#{item.policyNo}
            </foreach>
        </foreach>
    </update>
    <!-- 车险用,批量更新投保人-->
    <update id="updateCarSmOrderApplicantBatch" parameterType="java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update sm_order_applicant set personName=#{dto.proposerInfo.personName},
            personGender= #{dto.proposerInfo.personGender},idType=#{dto.proposerInfo.idType},
            idNumber=#{dto.proposerInfo.idNumber},birthday= #{dto.proposerInfo.birthday},
            cellPhone=#{dto.proposerInfo.cellPhone},email=#{dto.proposerInfo.email},area=#{dto.proposerInfo.area},
            address=#{dto.proposerInfo.address}
            where fhOrderId=#{dto.fhOrderId}
        </foreach>
    </update>

    <update id="updateCarOrderCommissionBatch" parameterType="java.util.List">
        <foreach collection="dtos" item="dto" index="index" separator=";">
            update sm_order set commissionId=#{dto.commissionId}
            <if test='dto.recommendUserId != null and dto.recommendUserId!=""'>
                ,
                recommendId=#{dto.recommendUserId,jdbcType=VARCHAR},recommendJobCode=#{dto.recommendJobCode,jdbcType=VARCHAR},recommendOrgCode=#{dto.recommendOrgCode,jdbcType=VARCHAR},recommendMainJobNumber=#{dto.recommendMainJobNumber,jdbcType=VARCHAR}
                ,
                customerAdminId=#{dto.recommendUserId,jdbcType=VARCHAR},customerAdminJobCode=#{dto.recommendJobCode,jdbcType=VARCHAR},customerAdminOrgCode=#{dto.recommendOrgCode,jdbcType=VARCHAR},customerAdminMainJobNumber=#{dto.recommendMainJobNumber,jdbcType=VARCHAR}
            </if>
            where fhOrderId=#{dto.orderNo,jdbcType=VARCHAR}
        </foreach>
    </update>


    <update id="updateCarOrderCommissionBatch2" parameterType="java.util.List">
        update sm_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="commissionId =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    when fhOrderId=#{item.orderNo} then #{item.commissionId}
                </foreach>
            </trim>

            <trim prefix="recommendId =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendUserId != null and item.recommendUserId!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendUserId}
                    </if>
                </foreach>
            </trim>

            <trim prefix="recommendJobCode =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendJobCode != null and item.recommendJobCode!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendJobCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="recommendOrgCode =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendOrgCode != null and item.recommendOrgCode!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendOrgCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="recommendMainJobNumber =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendMainJobNumber != null and item.recommendMainJobNumber!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendMainJobNumber}
                    </if>
                </foreach>
            </trim>


            <trim prefix="customerAdminId =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendUserId != null and item.recommendUserId!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendUserId}
                    </if>
                </foreach>
            </trim>

            <trim prefix="customerAdminJobCode =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendJobCode != null and item.recommendJobCode!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendJobCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="customerAdminOrgCode =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendOrgCode != null and item.recommendOrgCode!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendOrgCode}
                    </if>
                </foreach>
            </trim>

            <trim prefix="customerAdminMainJobNumber =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.recommendMainJobNumber != null and item.recommendMainJobNumber!=""'>
                        when fhOrderId=#{item.orderNo} then #{item.recommendMainJobNumber}
                    </if>
                </foreach>
            </trim>

        </trim>
        where fhOrderId in
        <foreach collection="dtos" index="index" item="item" separator="," open="(" close=")">
            #{item.orderNo}
        </foreach>
    </update>


    <select id="listOrderApplicantByFhOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderApplicant">
        SELECT t1.*
        FROM sm_order_applicant t1
        WHERE t1.fhOrderId IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listSmOrderInsuredByFhOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        SELECT t1.*
        FROM sm_order_insured t1
        WHERE t1.fhOrderId IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="listNeedVisitOrderByProductIds" resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderPolicyDTO">
        SELECT distinct soi.fhOrderId,soi.policyNo,so.channel
        FROM sm_order_insured soi
        left join sm_order so on so.fhOrderId = soi.fhOrderId
        WHERE soi.policyNo not in (select policy_no from sm_order_policy) and so.productId IN
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        limit 100
    </select>
    <select id="countSpecialProductsPolicyQty" resultType="java.lang.Integer">
        SELECT CASE WHEN SUM(qty) IS NULL THEN 0 ELSE SUM(qty) END
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2
        ON t1.fhOrderId = t2.fhOrderId
        WHERE t1.productId in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
        <![CDATA[  AND t1.endTime > #{startTime} ]]>
        AND t2.idNumber = #{idNumber}
        AND t1.payStatus = 2
        AND t2.appStatus IN ('1', '2')
        <if test="occupationCodes!=null and occupationCodes.size()>0">
            and t2.occupationCode in
            <foreach collection="occupationCodes" item="occupationCode" separator=","
                     open="(" close=")">
                #{occupationCode}
            </foreach>
        </if>
    </select>

    <insert id="saveTempOrderProduct" parameterType="com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO">
        INSERT INTO sm_order_product (order_id,
        product_id,
        product_name,
        plan_id,
        plan_name,
        coverage,
        update_by,
        version)
        VALUES (#{orderId},
        #{product.productId},
        #{product.productName},
        #{product.planId},
        #{product.planName},
        #{product.coverage},
        #{product.updateBy},
        #{product.version})
    </insert>

    <select id="queryOrderProduct" parameterType="java.lang.String"
            resultType="com.cfpamf.ms.insur.weixin.pojo.dto.product.OrderProductDTO">
        select *
        from sm_order_product
        where order_id = #{orderId}
    </select>

    <delete id="clearTempOrder" parameterType="java.lang.String">
        delete
        from sm_order
        where fhOrderId = #{orderId}
        and orderState = 10;
    </delete>
    <delete id="clearTempApplicant" parameterType="java.lang.String">
        delete
        from sm_order_applicant
        where fhOrderId = #{orderId};
    </delete>

    <delete id="clearTempInsured" parameterType="java.lang.String">
        delete
        from sm_order_insured
        where fhOrderId = #{orderId};
    </delete>

    <delete id="clearTempItem" parameterType="java.lang.String">
        delete
        from sm_order_item
        where fh_order_id = #{orderId};
    </delete>


    <delete id="clearTempOrderProduct" parameterType="java.lang.String">
        delete
        from sm_order_product
        where order_id = #{orderId};
    </delete>

    <insert id="savePayNotify">
        insert into sm_order_extend_zan
        (fh_order_id, za_order_id, notify_content, state)
        values (#{orderId}, #{outOrderId}, #{message}, #{state})
    </insert>

    <select id="queryPayment" resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.za.PaymentDTO">
        SELECT o.fhOrderId,
        o.totalAmount,
        o.payStatus,
        o.productId,
        p.productName
        FROM sm_order o
        left join sm_product p on o.productId = p.id
        WHERE fhOrderId = #{orderId}
        limit 1
    </select>

    <update id="updateOrder">
        update sm_order SET
        update_time=CURRENT_TIMESTAMP()
        <if test="data.totalAmount != null ">
            , totalAmount=#{data.totalAmount}
        </if>
        <if test="data.appNo != null ">
            , appNo=#{data.appNo}
        </if>
        <if test="data.policyNo != null ">
            , policy_no=#{data.policyNo}
        </if>
        <if test="data.endorsementNo != null ">
            , endorsement_no=#{data.endorsementNo}
        </if>
        <if test="data.payStatus != null ">
            , payStatus=#{data.payStatus}
        </if>
        <if test="data.orderState != null ">
            , orderState=#{data.orderState}
        </if>
        <if test="data.paymentTime != null ">
            , paymentTime=#{data.paymentTime}
        </if>
        <if test="data.payType != null ">
            , pay_type=#{data.payType}
        </if>
        <if test="data.payUrl != null ">
            , payUrl=#{data.payUrl}
        </if>
        <if test="data.extendField != null ">
            , extend_field=#{data.extendField}
        </if>
        <if test="data.renewOrderId != null ">
            , renewOrderId=#{data.renewOrderId}
        </if>
        <if test="data.firstOrderId != null ">
            , first_new_order_id=#{data.firstOrderId}
        </if>
        <if test="data.applyTime != null ">
            , apply_time=#{data.applyTime}
        </if>
        where fhOrderId=#{orderId}
    </update>

    <select id="queryDutyConfig" resultType="com.cfpamf.ms.insur.admin.pojo.dto.product.SysDutyConfig">
        SELECT *
        from sys_duty_config
        where 1=1
        <if test=" channel != null ">
            and channel=#{channel}
        </if>
        <if test=" type != null ">
            and type = #{type}
        </if>
    </select>
    <!-- s51 根据订单id，原推荐人、管护信贷员工号 变更 管护客户经理 -->
    <update id="updateOrderRecommendIdByOrderId">
        UPDATE sm_order t1
        SET t1.customerAdminId = #{newRecommendId},t1.customerAdminJobCode = #{newRecommendJobCode},
        t1.customerAdminMainJobNumber=#{newRecommendMainJobNumber} ,t1.customerAdminOrgCode=#{newRecommendOrgCode},
        t1.recommendId=#{newRecommendId},t1.recommendJobCode = #{newRecommendJobCode},
        t1.recommendMainJobNumber=#{newRecommendMainJobNumber},t1.recommendOrgCode=#{newRecommendOrgCode},
        t1.recommendAdminName=#{newRecommendAdminName},recommendPostName=#{newRecommendPostName},
        t1.recommendMasterName=#{newRecommendMasterName}
        where t1.fhOrderId = #{fhOrderId}
        <if test="oldRecommendId != null ">
            and t1.recommendId =#{oldRecommendId}
        </if>
    </update>
    <!-- s51 根据订单id，原管护人、管护信贷员工号 变更 管护客户经理 -->
    <update id="updateOrderCustomerAdminIdByOrderId">
        UPDATE sm_order t1
        SET t1.customerAdminId = #{newRecommendId},
            t1.customerAdminJobCode = #{newRecommendJobCode},
            t1.customerAdminMainJobNumber=#{newRecommendMainJobNumber},
            t1.customerAdminOrgCode=#{newRecommendOrgCode}
        where t1.fhOrderId = #{fhOrderId}
        <if test="oldRecommendId != null ">
            and t1.customerAdminId =#{oldRecommendId}
        </if>
    </update>

    <update id="updateOrderCommissionRecommendIdByOrderId">
        UPDATE sm_order_commission t1
        SET t1.customerAdminId = #{newRecommendId},t1.customerAdminJobCode = #{newRecommendJobCode},
        t1.customerAdminMainJobNumber=#{newRecommendMainJobNumber} ,t1.customerAdminOrgCode=#{newRecommendOrgCode},
        t1.recommendId=#{newRecommendId},t1.recommendJobCode = #{newRecommendJobCode},
        t1.recommendMainJobNumber=#{newRecommendMainJobNumber},t1.recommendOrgCode=#{newRecommendOrgCode},
        t1.recommendAdminName=#{newRecommendAdminName},recommendPostName=#{newRecommendPostName},
        t1.recommendMasterName=#{newRecommendMasterName}
        <if test="paymentTime!=null">
            ,t1.accountTime = #{paymentTime}
        </if>
        where t1.fhOrderId = #{fhOrderId}
        <if test="oldRecommendId != null ">
            and t1.recommendId =#{oldRecommendId}
        </if>
    </update>

    <select id="listOrderBaseDetailByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.OrderBaseDetailDTO">
        SELECT t1.*,
               t4.personName   AS applicantPersonName,
               t5.idType       AS insuredIdType,
               t4.cellPhone    AS applicantCellPhone,
               t4.idType       AS aplicantIdType,
               t4.idNumber     AS aplicantIdNumber,
               t5.idNumber     AS insuredIdNumber,
               t4.personGender AS applicantPersonGender,
               t5.relationship AS insuredRelationship,
               t5.personName   AS insuredPersonName,
               t5.cellPhone    AS insuredCellPhone,
               t5.downloadURL,
               t5.personGender AS insuredPersonGender,
               t5.policyNo AS policyNo,
               t1.update_time     updateTime
        FROM sm_order t1
        LEFT JOIN sm_order_applicant t4 ON t4.fhOrderId = t1.fhOrderId
        LEFT JOIN sm_order_insured t5 ON t5.fhOrderId = t1.fhOrderId
        WHERE t1.fhOrderId = #{fhOrderId}
    </select>

    <update id="batchUpdateIns">
        UPDATE sm_order_insured
        SET policyNo=#{policyNo},
        appStatus=#{status},
        update_time=CURRENT_TIMESTAMP()
        <if test="epolicyUrl!=null">
            ,downloadURL = #{epolicyUrl}
        </if>
        where fhOrderId = #{orderId}
    </update>

    <update id="updateNotifyState">
        UPDATE sm_order_extend_zan
        SET
        <if test="errorMsg!=null">
            error_msg=#{errorMsg},
        </if>
        state =#{state}
        where fh_order_id=#{orderId}
    </update>
    <update id="updateOrderCommissionCancelAmount">
        UPDATE sm_order_commission
        set totalAmount = #{cancelAmount}
        where policyNo = #{policyNo}
        and appStatus = #{appStatus}
    </update>


    <!-- s52 雇主责任险退保 -->
    <select id="listPolicyInsuredByFhOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmPolicyInsured">

        select soi.id as id,
        idNumber,
        policyNo,
        personName,
        personGender,
        appStatus,
        so.fhOrderId,
        so.channel,
        so.planId,
        so.customerAdminId,
        so.paymentTime
        from sm_order_insured soi
        left join sm_order so on soi.fhOrderId = so.fhOrderId
        where so.payStatus = 2
        and soi.fhOrderId LIKE CONCAT(#{fhOrderId}, '%')
        order by soi.create_time desc
    </select>

    <!-- s52 雇主责任险退保 -->
    <select id="getOrderAddCommissionProportion"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.OrderAddCommissionProportionDTO">

        select fhOrderId, add_commission_proportion as addCommissionProportion
        from sm_order
        where fhOrderId in
        <foreach collection="orderIdList" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
    </select>

    <!--s54 佣金改造-->
    <select id="getOrderCommissionInsuredByOrderId"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.OrderCommissionInsuredInfoDTO">
        SELECT t1.fhOrderId,
        t2.id as insuredId,
        t1.startTime,
        t1.endTime,
        t1.submitTime,
        t7.th_policy_no as originalPolicyNo,
        t1.commissionId,
        t2.personName AS insuredPersonName,
        t2.idNumber AS
        insuredIdNumber,
        t2.cellPhone as insuredCellPhone,
        t2.policyNo,
        t3.productName,
        t3.productType,

               (case when t7.plan_id is not null then t7.plan_id else t1.planId end)                       AS planId,
               (case
                    when t1.commissionId = -1 then t7.commission_id
                    else t1.commissionId end)                                                              AS commissionId,
               t4.planName,
               t4.fhProductId,
               (case
                    when t7.total_amount is not null then t7.total_amount
                    else t1.unitPrice * t1.qty end)                                                        AS totalAmount,
               t3.id                                                                                          productId,
               t1.subChannel,
               t1.channel,
               t3.productAttrCode,
               t3.companyId,
               t1.unitPrice,
               t2.appStatus,
               t1.payStatus,
               t1.paymentTime,
               t2.surrender_time as surrenderTime,
               t1.recommendId,
               t1.submitTime orderCreateTime,
               t3.long_insurance longInsurance
        FROM sm_order t1
        LEFT JOIN sm_order_insured t2 ON t1.fhOrderId = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t1.productId = t3.id
        LEFT JOIN sm_plan t4 ON t1.planId = t4.id
        left join sm_order_item t7 on t2.fhOrderId = t7.fh_order_id and t2.idNumber = t7.id_number and
        t2.appStatus = t7.app_status


        WHERE t1.fhOrderId = #{fhOrderId}
    </select>

    <select id="listOrderCommissionV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT
        t7.channel,
        t7.create_time as createTime,
        t1.account_time as accountTime,
        t7.paymentTime,
        t1.order_id as fhOrderId,
        case when t1.policy_status='1' then t1.amount else t1.amount * -1 end as totalAmount,
        t7.startTime,
        t7.endTime,
        t2.regionName AS recommendRegionName,
        t2.organizationFullName AS recommendOrganizationName,
        t2.userName AS recommendUserName,
        t2.userMobile AS recommendUserMobile,
        t2.userId AS recommendUserId,
        t7.recommendMasterName AS userMasterName,
        t7.recommendAdminName AS userAdminName ,
        t7.recommendEntryDate AS entryDate,
        t7.recommendPostName AS postName,
        soa.personName AS applicantPersonName,
        soa.idNumber AS aplicantIdNumber,
        soa.cellPhone AS applicantCellPhone,
        t6.personName AS insuredPersonName,
        t1.insured_id_number AS insuredIdNumber,
        t6.cellPhone AS insuredCellPhone,
        t3.productName,
        t3.id AS productId,
        tp.planName,
        t4.companyName,
        t1.policy_status as appStatus,
        t1.policy_no policyNo,
        if(sot.id is null,t1.payment_rate,t1.payment_rate * .7) as paymentProportion,
        if(sot.id is null,t1.payment_amount,t1.payment_amount * .7) as paymenyCommission,
        t1.settlement_rate AS settlementProportion,
        t1.settlement_amount AS settlementCommission,
        t1.conversion_amount AS convertedAmount,
        t10.amount,
        t10.pay_type,
        t10.pay_period,
        t10.valid_period,
        t1.conversion_rate AS convertedProportion,
        t6.appStatus As finalStatus
        ,if(sot.id is null,false,true) talkOrder,sot.invite_name inviteName
        ,sot.invite_type inviteType
        ,t1.conversion_risk_json conversionRiskJson
        ,t1.payment_risk_json paymentRiskJson
        ,t1.settlement_risk_json settlementRiskJson,
        t1.create_time detailCreateTime,
        t7.startTime,t3.productType,t9.activity_code,t9.product_activity_code as villageActivity,t9.type
        FROM
        sm_commission_detail t1
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id
        LEFT JOIN sm_plan tp
        ON t7.planId = tp.id
        LEFT JOIN sm_company t4
        ON t3.companyId = t4.id
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber and t1.policy_status = t6.appStatus
        left join sm_order_policy t10
        on t1.order_id = t10.fh_order_id
        left join sm_order_talk sot on sot.fh_order_id = t1.order_id
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        left join sm_reconciliation_policy_cache t11
        on t11.policy_no = REVERSE(SUBSTR( REVERSE( t1.`policy_No` ) FROM INSTR( REVERSE( t1.`policy_No` ), '_' ) + 1 ))
        and t11.app_status = t1.policy_status
        left join sm_add_commission_detail t14 on t14.order_id = t6.fhOrderId and t6.idNumber = t14.insured_id_number
        left join sm_order_village_activity t9 on t7.fhOrderId=t9.fh_order_id and t9.type=100
        WHERE 1=1
        <if test="orderType!=null">
            and t7.orderType = #{orderType}
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (t9.product_activity_code is null or t9.product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and t9.product_activity_code is not null and t9.product_activity_code !=''
            </if>
        </if>
        <if test="longInsurance!=null">
            and t3.long_insurance = #{longInsurance}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='productType2 != null'>
            and t3.productType = #{productType2}
        </if>

        <if test='startTimeStart != null'>
            <![CDATA[ AND t7.startTime>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND t7.startTime<=#{startTimeEnd}  ]]>
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and t11.reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and t11.reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and t11.reconciliation_status in (1,2,3,4,5,6,7,9)
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and t14.proportion > 0
            </if>
            <if test="addCommissionStatus == 1">
                and (t14.proportion is null or t14.proportion = 0)
            </if>
        </if>

        ORDER BY t1.account_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>
    </select>

    <select id="countOrderCommissionV3" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        sm_commission_detail t1
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id

        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber
        left join sm_order_talk sot on sot.fh_order_id = t1.order_id
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        left join sm_reconciliation_policy_cache t11
        on t11.policy_no = REVERSE(SUBSTR( REVERSE( t1.`policy_No` ) FROM INSTR( REVERSE( t1.`policy_No` ), '_' ) + 1 ))
        and t11.app_status = t1.policy_status
        left join sm_add_commission_detail t14 on t14.order_id = t6.fhOrderId and t6.idNumber = t14.insured_id_number
        left join sm_order_village_activity t9 on t7.fhOrderId=t9.fh_order_id and t9.type=100
        WHERE 1=1
        <if test="orderType!=null">
            and t7.orderType = #{orderType}
        </if>
        <if test="longInsurance!=null">
            and t3.long_insurance = #{longInsurance}
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (t9.product_activity_code is null or t9.product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and t9.product_activity_code is not null and t9.product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>
        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='startTimeStart != null'>
            <![CDATA[ AND t7.startTime>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND t7.startTime<=#{startTimeEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>
        <if test='productType2 != null'>
            and t3.productType = #{productType2}
        </if>
        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and t11.reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and t11.reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and t11.reconciliation_status in (1,2,3,4,5,6,7,9)
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and t14.proportion > 0
            </if>
            <if test="addCommissionStatus == 1">
                and (t14.proportion is null or t14.proportion = 0)
            </if>
        </if>

    </select>
    <select id="getOrderCommissionSummaryV3" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        SUM(case when t1.policy_status= 1 then t1.amount else -1 * t1.amount end) AS orderAmount,
        SUM(if(sot.id is null,t1.payment_amount,t1.payment_amount * .7)) AS paymentAmount,
        SUM(t1.settlement_amount) AS settlementAmount,
        ROUND(SUM(
        case
        when (t1.policy_status = 4 and ifnull(t14.add_commission_amount, 0) > 0) then -1*t14.add_commission_amount
        when (t1.policy_status = 1 and 0 > ifnull(t14.add_commission_amount, 0)) then -1*t14.add_commission_amount
        else t14.add_commission_amount end )
        ,2) AS addCommissionAmount,
        SUM(IFNULL(t1.conversion_amount, 0)) as convertedAmount
        FROM
        sm_commission_detail t1
        left join sm_order t7 on t7.fhOrderId = t1.order_id
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_order_converted_premium t9
        on t1.order_id = t9.fh_order_id and t9.ins_id_number= t1.insured_id_number
        left join sm_order_talk sot on sot.fh_order_id = t7.fhOrderId
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        left join sm_add_commission_detail t14 on t14.order_id = t6.fhOrderId and t6.idNumber = t14.insured_id_number
        left join sm_order_village_activity t15 on t7.fhOrderId=t15.fh_order_id and t15.type=100
        left join sm_reconciliation_policy_cache t11
        on t11.policy_no = REVERSE(SUBSTR( REVERSE( t1.`policy_No` ) FROM INSTR( REVERSE( t1.`policy_No` ), '_' ) + 1 ))
        and t11.app_status = t1.policy_status
        WHERE t7.orderType = 0 and t1.enabled_flag=0
        and t3.long_insurance != 0
        <if test="orderType!=null">
            and t7.orderType = #{orderType}
        </if>
        <if test='villageActivity != null'>
            <if test="villageActivity == 1">
                and (t15.product_activity_code is null or t15.product_activity_code ='')
            </if>
            <if test="villageActivity == 0">
                and t15.product_activity_code is not null and t15.product_activity_code !=''
            </if>
        </if>
        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>

        <if test='productIds != null and productIds.size() > 0'>
            AND t7.productId in
            <foreach collection="productIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t1.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t1.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='startTimeStart != null'>
            <![CDATA[ AND t7.startTime>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND t7.startTime<=#{startTimeEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='productType2 != null'>
            and t3.productType = #{productType2}
        </if>
        <if test="reconciliationStatus != null">
            <if test="reconciliationStatus == 0">
                and t11.reconciliation_status = 0
            </if>
            <if test="reconciliationStatus == 1">
                and t11.reconciliation_status is null
            </if>
            <if test="reconciliationStatus == 2">
                and t11.reconciliation_status in (1,2,3,4,5,6,7,9)
            </if>
        </if>
        <if test="addCommissionStatus != null">
            <if test="addCommissionStatus == 0">
                and t14.proportion > 0
            </if>
            <if test="addCommissionStatus == 1">
                and (t14.proportion is null or t14.proportion = 0)
            </if>
        </if>
    </select>


    <select id="queryOrderProductVersion" resultType="java.lang.Integer">
        SELECT MAX(version) FROM sm_product_version
        <where>
            product_id = #{productId}
            <if test='submitTime != null'>
                AND #{submitTime} &gt;= create_time
            </if>
        </where>
    </select>

    <!-- 客户信息抽取用 -->
    <select id="list30UnExtractFhOrderId" resultType="java.lang.String">
        select distinct so.fhOrderId
        from
            sm_order so FORCE INDEX(idx_smOrder_paymentTime)
            left join sm_order_insured soi on so.fhOrderId = soi.fhOrderId
            left join sm_order_applicant soa on soa.fhOrderId = so.fhOrderId
        where
            so.payStatus = '2'
            and so.`paymentTime` > DATE_ADD(NOW(), INTERVAL -1 MONTH)
            and extractFlag is null
            and soi.idNumber is not null
            and soa.idNumber is not null
        order by so.id desc
        limit 300
    </select>

    <!-- 客户信息抽取用 -->
    <select id="listSmOrderCommission" resultType="com.cfpamf.ms.insur.admin.pojo.dto.commission.SmOrderCommissionDTO">
        select
        c.channel,
        c.accountTime,
        c.orderCreateTime,
        c.paymentTime,
        c.fhOrderId,
        c.productId,
        c.planId,
        c.recommendId,
        c.customerAdminId,
        c.totalAmount,
        c.insIdNumber,
        c.appStatus,
        c.policyNo,
        c.paymentProportion,
        c.paymentAmount,
        c.settlementProportion,
        c.settlementAmount,
        IFNULL(t9.converted_premium,
        (CASE WHEN (c.appStatus = 4 and t3.productAttrCode = 'person') THEN 0 ELSE
        c.totalAmount END)
        ) AS convertedAmount,
        IFNULL(t9.proportion,0) AS convertedProportion
        from sm_order_commission c
        LEFT JOIN sm_product t3
        ON c.productId = t3.id
        LEFT JOIN sm_order_converted_premium t9
        on c.fhOrderId = t9.fh_order_id and t9.ins_id_number= c.insIdNumber and t9.app_status = c.appStatus
        where c.create_time>'2021-12-01 00:00:00'

        <if test="productIds!= null and productIds.size()>0">
            and c.productId in
            <foreach collection="productIds" item="productId" close=")" open="(" separator=",">
                #{productId}
            </foreach>
        </if>
        <if test="productIds== null or productIds.size()==0">
            and c.productId not in (select product_id from sm_product_label where label_type='calc_new_commission' and
            label_value='Y' )
        </if>
        <if test='fhOrderId != null'>
            AND c.fhOrderId = #{fhOrderId}
        </if>
        <if test='accountStart != null'>
            AND c.accountTime &gt;= #{accountStart}
        </if>
        <if test='accountEnd != null'>
            <![CDATA[  AND c.accountTime <= #{accountEnd} ]]>
        </if>
    </select>
    <select id="getOrderSummaryV4" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderSummaryVO">
        SELECT
        SUM(CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN IF(sot.id is null, t13.payment_amount,
        t13.payment_amount * .7) ELSE 0 END) AS paymentAmount,
        SUM(CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN t13.settlement_amount ELSE 0 END) AS settlementAmount
        ,
        SUM(CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN
        (CASE WHEN (t3.appStatus = 4 and IFNULL(t14.add_commission_amount, 0) > 0) THEN -t14.add_commission_amount
        ELSE t14.add_commission_amount
        END
        )
        ELSE 0
        END
        )AS addCommissionAmount,
        SUM(CASE WHEN (t3.appStatus = 4 or t3.appStatus = 1 ) THEN t13.conversion_amount ELSE 0 END) AS convertedAmount
        ,
        SUM(case when t7.total_amount is not null then t7.total_amount else t1.unitPrice * t1.qty end) AS orderAmount,
        COUNT(t1.fhOrderId) AS totalQty
        <if test="orderType ==1">
            ,sum(t12.distribution_amount) distributionAmount
        </if>
        FROM sm_order t1
        LEFT JOIN sm_order_insured t3 ON t3.fhOrderId=t1.fhOrderId
        LEFT JOIN sm_product t4 ON t4.id=t1.productId
        left join sm_order_item t7 on t3.fhOrderId = t7.fh_order_id and t3.idNumber = t7.id_number and
        t3.appStatus=t7.app_status
        left join sm_order_renew_bind_info orbi on t3.fhorderid = orbi.fh_order_id and t3.policyNo = orbi.policy_no
        left join sm_order_talk sot on sot.fh_order_id = t1.fhOrderId
        left join sm_order_drainage sod on sod.order_id = t1.fhOrderId and sod.enabled_flag = 0
        left join sm_commission_detail t13 on t13.order_id = t3.fhOrderId and t3.idNumber = t13.insured_id_number and
        t13.policy_status = t3.appStatus
        left join sm_add_commission_detail t14 on t14.order_id = t3.fhOrderId and t3.idNumber = t14.insured_id_number and t14.term_num = t13.term_num
        <if test='applicantdName != null'>
            LEFT JOIN sm_order_applicant t2 ON t2.fhOrderId=t1.fhOrderId
        </if>
        <if test='regionName != null or orgName != null or orgPath != null'>
            LEFT JOIN auth_user t9 ON t9.userId=t1.customerAdminId AND t9.enabled_flag = 0
        </if>
        <if test="orderType ==1">
            left join sm_order_distribution t12 on t1.fhOrderId = t12.fh_order_id
        </if>
        left join sm_order_policy sop on sop.fh_order_id = t1.fhOrderId
        <if test='ownerPersonName!= null or plateEngineNum != null '>
            left join auto_order_car aoc on t1.fhOrderId = aoc.order_no
        </if>

        left join sm_order_label  t15  on t15.fh_order_id = t1.fhOrderId  and t15.label_type='self_insurance'
        WHERE t1.enabled_flag=0

        <if test="selfInsured == 1">
            and t15.label_value = "Y"
        </if>
        <if test="selfInsured == 0">
            and  (t15.fh_order_id IS NULL OR t15.label_value != "Y")
        </if>

        <if test="orderType!=null">
            and t1.orderType = #{orderType}
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t1.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t1.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.fhOrderId = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.fhOrderId LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='payStatus != null and payStatus != ""'>
            AND t1.payStatus=#{payStatus}
        </if>
        <if test='policyNo !=  null'>
            AND t3.policyNo like CONCAT(#{policyNo},'%')
        </if>
        <if test='appStatus != null and appStatus != ""'>
            AND t3.appStatus=#{appStatus}
        </if>
        <if test='productId != null'>
            AND t1.productId=#{productId}
        </if>
        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                t2.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                t2.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                t2.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t3.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t3.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t3.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='recommendId != null'>
            AND t1.recommendId=#{recommendId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t4.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t4.productAttrCode in ('group','employer')
        </if>
        <!--回访状态-->
        <if test='returnVisitStatus != null and returnVisitStatus!=""'>
            and sop.visit_status = #{returnVisitStatus}

        </if>
        <!--回访时间-->
        <if test='returnVisitTimeStart != null'>
            <![CDATA[ AND sop.visit_time>=#{returnVisitTimeStart} ]]>
        </if>
        <if test='returnVisitTimeEnd != null'>
            <![CDATA[ AND sop.visit_time<=#{returnVisitTimeEnd} ]]>
        </if>
        <if test='companyId != null'>
            AND t4.companyId=#{companyId}
        </if>
        <if test='regionName != null'>
            AND t9.regionName=#{regionName}
        </if>
        <if test='orgCode != null'>
            AND t9.orgCode=#{orgCode}
        </if>

        <if test='customerAdminId != null'>
            AND t1.customerAdminId=#{customerAdminId}
        </if>

        <if test='channel != null'>
            AND t4.channel=#{channel}
        </if>

        <if test='productType != null'>
            AND t4.productType=#{productType}
        </if>
        <if test='productType == null'>
            <if test='tabType != null and tabType == "CX"'>
                AND t4.productType='CX'
            </if>
            <if test='tabType != null and tabType == "OTHER"'>
                AND (t4.productType!='CX' or t4.productType is null)
            </if>
        </if>


        <if test='subChannel != null'>
            AND t1.subChannel=#{subChannel}
        </if>

        <if test='policyToDateStart != null'>
            <![CDATA[ AND t1.endTime>=#{policyToDateStart} ]]>
        </if>
        <if test='policyToDateEnd != null'>
            <![CDATA[ AND t1.endTime<=#{policyToDateEnd} ]]>
        </if>

        <if test='userId != null'>
            AND t1.customerAdminId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t9.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test="distributionOrderNo !=null and distributionOrderNo != ''">
            and t12.distribution_order_no like CONCAT(#{distributionOrderNo},'%')
        </if>

        <if test="distributionState !=null and distributionState !=''">
            and t12.distribution_state = #{distributionState}
        </if>
        <if test='ownerPersonName != null'>
            AND
            <if test="ownerType == 1">
                aoc.owner_person_name LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 2">
                aoc.owner_id_number LIKE CONCAT(#{ownerPersonName},'%')
            </if>
            <if test="ownerType == 3">
                aoc.owner_cell_phone LIKE CONCAT(#{ownerPersonName},'%')
            </if>
        </if>
        <if test='plateEngineNum != null'>
            AND (aoc.engine_num LIKE CONCAT(#{plateEngineNum},'%') or aoc.plate_num LIKE CONCAT(#{plateEngineNum},'%'))
        </if>
        <!-- <if test="bindStatus!=null and bindStatus!=''">
             and orbi.bind_status = #{bindStatus}
         </if>-->
        <choose>
            <when test='bindStatus!=null and bindStatus=="undo"'>
                and (orbi.bind_status = #{bindStatus} or orbi.bind_status is null)
            </when>
            <when test='bindStatus!=null and bindStatus!="undo"'>
                and orbi.bind_status = #{bindStatus}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        <if test="recommendChannel != null and recommendChannel != ''">
            and t1.recommend_channel = #{recommendChannel}
        </if>
        <if test="drainagePlatform != null and drainagePlatform != ''">
            and sod.drainage_platform = #{drainagePlatform}
        </if>
        <if test="drainageShareAccount != null and drainageShareAccount != ''">
            and (
            sod.drainage_share_name = #{drainageShareAccount}
            or sod.drainage_share_account = #{drainageShareAccount}
            or sod.drainage_share_code = #{drainageShareAccount}
            )
        </if>
        <![CDATA[ AND t1.create_time>='2022-01-01 00:00:00' ]]>
    </select>
    <select id="getOrderDetailCommissionByFhOrderIdV4"
            resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.OrderDetailCommissionDTO">
        select sod.order_id,
        sum(ifnull(sod.payment_amount, 0)) as paymenyCommission,
        sum(ifnull(sod.settlement_amount, 0)) as settlementCommission,
        u1.userId as recommendId,
        u1.userName as recommendName,
        u1.userMobile as recommendMobile,
        u1.regionCode,
        u1.regionName as regionName,
        u1.orgCode,
        u1.organizationFullName as orgName,
        u2.userId as customerAdminId,
        u2.userName as customerAdminName,
        u2.userMobile as customerAdminMobile
        from sm_commission_detail sod
        left join sm_order t1 on sod.order_id = t1.fhOrderId
        left join sm_order_insured t3 on t3.fhOrderId = sod.order_id and t3.idNumber = sod.insured_id_number and
        t3.policyNo = sod.policy_no and t3.appStatus = sod.policy_status
        left join auth_user u1 on t1.recommendId = u1.userId and u1.enabled_flag = 0
        left join auth_user u2 on t1.customerAdminId = u2.userId and u2.enabled_flag = 0
        where sod.order_id = #{fhOrderId} and sod.enabled_flag = 0
        group by sod.order_id, u1.userId, u1.userName, u1.userMobile, u1.regionCode, u1.regionName, u1.orgCode,
        u1.organizationFullName, u2.userId, u2.userName, u2.userMobile
    </select>


    <update id="updateOrderReplaceInsInfo">
        UPDATE sm_order_insured SET
        idNumber = #{newIdNumber}
        ,personGender = #{personGender}
        ,birthday = #{birthday}
        ,personName=#{newInsuredName}

        WHERE fhOrderId = #{fhOrderId} AND idNumber = #{oldIdNumber}

    </update>
    <update id="updateOrderInterruption">
        update customer_interruption SET last_customer_admin=#{newAdminId}
        where last_customer_admin=#{oldAdminId};
        update customer_interruption_policy SET customer_admin=#{newAdminId}
        where customer_admin=#{oldAdminId};
        update phoenix_emp_todo SET job_number=#{newAdminId}
        where job_number=#{oldAdminId} and state = 'TODO';
        update insurance_renewal SET customer_admin_id=#{newAdminId}
        where customer_admin_id=#{oldAdminId};
        update customer_loan SET customer_admin=#{newAdminId}
        where customer_admin=#{oldAdminId};
    </update>
    <update id="updateHandOrderInterruption">
        update customer_interruption SET last_customer_admin=#{newAdminId}
        where 1=1
        <if test="oldAdminId != 'blank'">
            and last_customer_admin = #{oldAdminId}
        </if>
        <if test="oldAdminId == 'blank'">
            and last_customer_admin = ''
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND id_number IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;
        update customer_interruption_policy SET customer_admin=#{newAdminId}
        where 1=1
        <if test="oldAdminId != 'blank'">
            and customer_admin=#{oldAdminId}
        </if>
        <if test="oldAdminId == 'blank'">
            and customer_admin = ''
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND id_number IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;

        update phoenix_emp_todo SET job_number=#{newAdminId}
        where job_number=#{oldAdminId} and biz_type not in ('INTERRUPTION','LOAN') and state = 'TODO'
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND JSON_UNQUOTE(JSON_EXTRACT(todo_property, '$.idNumber')) IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;
        update phoenix_emp_todo SET job_number=#{newAdminId}
        where job_number=#{oldAdminId} and biz_type in ('INTERRUPTION','LOAN') and state = 'TODO'
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND target_id IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;

        update insurance_renewal SET customer_admin_id=#{newAdminId}
        where 1=1
        <if test="oldAdminId != 'blank'">
            and customer_admin_id=#{oldAdminId}
        </if>
        <if test="oldAdminId == 'blank'">
            and customer_admin_id = ''
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND applicant_id_number IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;

        update insurance_renewal SET customer_admin_id=#{newAdminId}
        where 1=1
        <if test="oldAdminId != 'blank'">
            and customer_admin_id=#{oldAdminId}
        </if>
        <if test="oldAdminId == 'blank'">
            and customer_admin_id = ''
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND insured_id_number IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;

        update customer_loan SET customer_admin=#{newAdminId}
        where 1=1
        <if test="oldAdminId != 'blank'">
            and customer_admin=#{oldAdminId}
        </if>
        <if test="oldAdminId == 'blank'">
            and customer_admin = ''
        </if>
        <if test="idNumbers !=null and idNumbers.size() > 0">
            AND id_number IN
            <foreach collection="idNumbers" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>;
    </update>

    <select id="listOrderAddCommission" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmOrderListVO">
        SELECT
        t7.channel,
        t7.create_time as createTime,
        t8.account_time as accountTime,
        t7.paymentTime,
        t1.order_id as fhOrderId,
        case when t1.status='1' then t1.amount else t1.amount * -1 end as totalAmount,
        t7.startTime,
        t7.endTime,
        t2.regionName AS recommendRegionName,
        t2.organizationFullName AS recommendOrganizationName,
        t2.userName AS recommendUserName,
        t2.userMobile AS recommendUserMobile,
        t2.userId AS recommendUserId,
        t7.recommendMasterName AS userMasterName,
        t7.recommendAdminName AS userAdminName ,
        t7.recommendEntryDate AS entryDate,
        t7.recommendPostName AS postName,
        soa.personName AS applicantPersonName,
        soa.idNumber AS aplicantIdNumber,
        soa.cellPhone AS applicantCellPhone,
        t6.personName AS insuredPersonName,
        t1.insured_id_number AS insuredIdNumber,
        t6.cellPhone AS insuredCellPhone,
        t3.productName,
        t3.id AS productId,
        tp.planName,
        t4.companyName,
        t1.status as appStatus,
        t1.policy_no policyNo,
        if(sot.id is null,t8.payment_rate,t8.payment_rate * .7) as paymentProportion,
        if(sot.id is null,t8.payment_amount,t8.payment_amount * .7) as paymenyCommission,
        t8.settlement_rate AS settlementProportion,
        t8.settlement_amount AS settlementCommission,
        t8.conversion_amount AS convertedAmount,
        t10.amount,
        t10.pay_type,
        t10.pay_period,
        t10.valid_period,
        t8.conversion_rate AS convertedProportion,
        t6.appStatus As finalStatus
        ,if(sot.id is null,false,true) talkOrder,sot.invite_name inviteName
        ,sot.invite_type inviteType
        ,t8.conversion_risk_json conversionRiskJson
        ,t8.payment_risk_json paymentRiskJson
        ,t8.settlement_risk_json settlementRiskJson,
        t7.startTime,t3.productType
        FROM
        sm_add_commission_detail t1
        LEFT JOIN sm_order t7
        ON t7.fhOrderId = t1.order_id
        LEFT JOIN auth_user t2
        ON t7.recommendId = t2.userId AND t2.enabled_flag = 0
        LEFT JOIN sm_product t3
        ON t7.productId = t3.id
        LEFT JOIN sm_plan tp
        ON t7.planId = tp.id
        LEFT JOIN sm_company t4
        ON t3.companyId = t4.id
        LEFT JOIN sm_order_insured t6
        on t6.fhOrderId = t1.order_id and t1.insured_id_number= t6.idNumber and t1.policy_no = t6.policyNo
        left join sm_order_policy t10
        on t1.order_id = t10.fh_order_id
        left join sm_order_talk sot on sot.fh_order_id = t1.order_id
        left join sm_order_applicant soa on soa.fhOrderId = t7.fhOrderId
        left join sm_commission_detail t8 on t8.order_id = t1.order_id and t8.insured_id_number = t1.insured_id_number
        and t8.policy_no = t1.policy_no
        WHERE 1=1
        <if test="orderType!=null">
            and t7.orderType = #{orderType}
        </if>

        <if test="longInsurance!=null">
            and t3.long_insurance = #{longInsurance}
        </if>

        <if test='createDateStart != null'>
            <![CDATA[ AND t7.create_time>=#{createDateStart} ]]>
        </if>
        <if test='createDateEnd != null'>
            <![CDATA[ AND t7.create_time<=#{createDateEnd} ]]>
        </if>
        <if test='paymentDateStart != null'>
            <![CDATA[ AND t7.paymentTime>=#{paymentDateStart} ]]>
        </if>
        <if test='paymentDateEnd != null'>
            <![CDATA[ AND t7.paymentTime<=#{paymentDateEnd} ]]>
        </if>
        <if test='orderNo != null'>
            <if test="fullMatch">
                AND t1.order_id = #{orderNo}
            </if>
            <if test="!fullMatch">
                AND t1.order_id LIKE CONCAT(#{orderNo},'%')
            </if>
        </if>
        <if test='talkOrder != null'>
            <if test="talkOrder == true">
                and sot.id > 0
            </if>
            <if test="talkOrder == false">
                and sot.id is null
            </if>
        </if>
        <if test='inviteName != null'>
            and sot.invite_name = #{inviteName}
        </if>
        <if test='policyNo !=  null'>
            AND t1.policy_no like CONCAT(#{policyNo},'%')
        </if>
        <if test='regionName != null'>
            AND t2.regionName=#{regionName}
        </if>
        <if test='orgName != null'>
            AND t2.organizationFullName=#{orgName}
        </if>

        <if test='applicantdName != null'>
            AND
            <if test="applicantdType == 1">
                soa.personName LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 2">
                soa.idNumber LIKE CONCAT(#{applicantdName},'%')
            </if>
            <if test="applicantdType == 3">
                soa.cellPhone LIKE CONCAT(#{applicantdName},'%')
            </if>
        </if>
        <if test='insuredName != null'>
            AND
            <if test="insuredType == 1">
                t6.personName LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 2">
                t6.idNumber LIKE CONCAT(#{insuredName},'%')
            </if>
            <if test="insuredType == 3">
                t6.cellPhone LIKE CONCAT(#{insuredName},'%')
            </if>
        </if>
        <if test='productId != null'>
            AND t7.productId=#{productId}
        </if>
        <if test='recommendId != null'>
            AND t7.recommendId=#{recommendId}
        </if>

        <if test='companyId != null'>
            AND t3.companyId=#{companyId}
        </if>
        <if test='productAttrCode != null and productAttrCode=="person"'>
            AND t3.productAttrCode=#{productAttrCode}
        </if>
        <if test='productAttrCode != null and productAttrCode=="group"'>
            AND t3.productAttrCode in ('group','employer')
        </if>

        <if test='channel != null'>
            AND t7.channel=#{channel}
        </if>
        <if test='accountDateStart != null'>
            <![CDATA[ AND t8.account_time>=#{accountDateStart}  ]]>
        </if>
        <if test='accountDateEnd != null'>
            <![CDATA[ AND t8.account_time<=#{accountDateEnd}  ]]>
        </if>
        <if test='appStatus != null '>
            AND t1.policy_status = #{appStatus}
        </if>

        <if test='userId != null'>
            AND t7.recommendId=#{userId}
        </if>

        <if test='orgPath !=null '>
            and t2.orgCode in (select orgCode from organization where orgPath like CONCAT(#{orgPath},'%'))
        </if>
        <if test='productType2 != null'>
            and t3.productType = #{productType2}
        </if>

        <if test='startTimeStart != null'>
            <![CDATA[ AND t7.startTime>=#{startTimeStart}  ]]>
        </if>
        <if test='startTimeEnd != null'>
            <![CDATA[ AND t7.startTime<=#{startTimeEnd}  ]]>
        </if>

        ORDER BY t8.account_time DESC
        <if test='startRow != null'>
            LIMIT #{startRow}, #{size}
        </if>

    </select>
    <select id="countApply" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.InsuredApply">
        select
        o.fhOrderId,
        i.idNumber,
        i.appStatus,
        o.startTime,
        o.endTime
        from
        sm_order o left join sm_order_insured i on o.fhOrderId=i.fhOrderId
        where o.payStatus = 2
        and i.appStatus IN ('1','2', '4')
        and o.productId in
        <foreach collection="productIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and i.idNumber in
        <foreach collection="idNumbers" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="listGscLast15Day" resultType="java.lang.String">
        SELECT `fhOrderId`
        FROM sm_order
        WHERE `channel`= 'gsc'
        and `create_time`> TIMESTAMPADD(DAY, -15, now())
        and `payStatus` in ('1', '0') and `enabled_flag`= 0
    </select>

    <select id="getByPolicyNoStatus" resultType="com.cfpamf.ms.insur.admin.pojo.vo.SmBaseOrderVO">
        SELECT
        t1.*,
        t2.policyNo as policyNo,
        p.productName
        FROM sm_order t1
        left join sm_order_insured t2 on t1.fhOrderId=t2.fhOrderId
        left join sm_product p on t1.productId=p.id
        WHERE t2.policyNo = #{policyNo}
        <if test="status!=null">
            and t2.appStatus=#{status}
        </if>
        LIMIT 1
    </select>
    
    
    <select id="queryByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrder">
        select
            a.*
        from sm_order a left join sm_order_insured b on a.fhOrderId=b.fhOrderId and b.enabled_flag=0
        where b.policyNo=#{policyNo}
        limit 1;
    </select>

    <select id="queryByOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrder">
        select
            a.*
        from sm_order a
        where a.fhOrderId=#{orderId}
    </select>

    <select id="queryPolicyProvideWhaleData" resultType="com.cfpamf.ms.insur.admin.pojo.vo.third.CustomerProviderVO">
        select t1.policyNo, t2.recommendId as customerAdminId from sm_order_insured t1 left join sm_order t2 on t1.fhOrderId = t2.fhOrderId
        where
         t1.enabled_flag = 0
        and t2.enabled_flag = 0
        and t1.policyNo in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        group by t1.policyNo
        limit 3000
    </select>


    <select id="queryPolicyProvideWhaleDataByEndorId"
            resultType="com.cfpamf.ms.insur.admin.pojo.vo.third.SeperateCustomerMangerProviderVO">
        select t1.th_endorsement_no as endorsementNo, t2.recommendId as customerAdminId, t1.id_number from sm_order_item t1 inner join sm_order t2 on t1.fh_order_id = t2.fhOrderId
        where
         t1.enabled_flag = 0
        and t2.enabled_flag = 0
        and t1.th_endorsement_no in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        group by t1.th_endorsement_no, t1.id_number, t2.recommendId
        limit 3000

    </select>

    <select id="queryReferrerInfo"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicyReferrerFixVo">
        select
            distinct a.policyNo,b.customerAdminId,b.recommendId,b.paymentTime as accountTime
        from sm_order_insured a left join sm_order b on a.fhOrderId = b.fhOrderId
        where
        a.enabled_flag = 0
        and b.enabled_flag = 0
        and a.policyNo in
        <foreach collection="data" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="policyCorrectReferrerInfo"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicyReferrerFixVo">
        select
            distinct a.th_policy_no as policyNo,a.th_endorsement_no as endorsementNo,b.customerAdminId,b.recommendId,
        from sm_order_item a left join sm_order b on a.fh_order_id = b.fhOrderId
        where
        a.enabled_flag = 0
        and b.enabled_flag = 0
        and a.th_endorsement_no in
        <foreach collection="data" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryRecommendOrgEmptyList" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrder">
        select
            *
        from `sm_order`
        WHERE recommendorgcode is null
        and recommendId is not null
        and recommendId != ''
        <if test=" startTime!=null ">
            and `submitTime` >= #{startTime}
        </if>
        <if test=" endTime!=null ">
            and `submitTime` &lt; #{endTime}
        </if>
        <if test=" orderId!=null and orderId!='' ">
            and fhOrderId=#{orderId}
        </if>
    </select>
    <select id="policySettlementTime"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicySettlementTimeFixVo">
        select
            distinct policyNo,a.paymentTime,b.surrender_time as correctInputTime,c.surrender_no as endorsementNo
        from `sm_order` a left join sm_order_insured b on a.fhOrderId=b.fhOrderId and b.enabled_flag=0
        left join sm_order_policy c on a.fhOrderId =c.fh_order_id and c.enabled_flag=0
        WHERE  b.policyNo in
        <foreach collection="data" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="correctSettlementTime"
            resultType="com.cfpamf.ms.insur.weixin.pojo.vo.order.PolicySettlementTimeFixVo">
        select
            distinct th_policy_no as policyNo,
            th_endorsement_no as endorsementNo,
            case when b.appStatus='1' then a.paymentTime when b.appStatus='4' then b.surrender_time end as correctInputTime
        from `sm_order` a left join sm_order_insured b on a.fhOrderId=b.fhOrderId
        left join sm_order_item c on b.fhOrderId=c.fh_order_id and b.idNumber=c.id_number
        WHERE
        1=1
        <if test="data != null and data.size>0">
            c.th_endorsement_no in
            <foreach collection="data" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        limit 5000;
    </select>


    <update id="updateRecommendOrgEmptyList">
        update sm_order
        set
            recommendMasterName = #{recommendMasterName},
            recommendAdminName = #{recommendAdminName},
            recommendEntryDate = #{recommendEntryDate},
            recommendPostName = #{recommendPostName},
            recommendJobCode = #{recommendJobCode},
            recommendMainJobNumber = #{recommendMainJobNumber},
            recommendOrgCode = #{recommendOrgCode},
            customerAdminJobCode = #{customerAdminJobCode},
            customerAdminMainJobNumber = #{customerAdminMainJobNumber},
            customerAdminOrgCode = #{customerAdminOrgCode}
        where  fhOrderId = #{fhOrderId} limit 1
    </update>
    <update id="changePlan">
        update sm_order
            set
            productId=#{plan.productId},
            planId=#{plan.planId},
            channel=#{plan.channel}
        where fhOrderId = #{orderId}
    </update>
</mapper>
