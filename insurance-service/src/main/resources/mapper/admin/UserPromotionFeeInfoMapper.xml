<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.UserPromotionFeeInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.cfpamf.ms.insur.admin.pojo.po.UserPromotionFeeInfo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="statistical_date" property="statisticalDate" jdbcType="DATE"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="data_type" property="dataType" jdbcType="VARCHAR"/>
        <result column="premium" property="premium" jdbcType="DECIMAL"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="pt" property="pt" jdbcType="VARCHAR"/>
        <result column="enabled_flag" property="enabledFlag" jdbcType="INTEGER"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="revision" property="revision" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, statistical_date, user_id, user_name, data_type, premium, amount, pt, enabled_flag,
        create_user, create_time, update_user, update_time, revision
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO user_promotion_fee_info (
            statistical_date, user_id, user_name, data_type, premium, amount, pt, enabled_flag,
            create_user, create_time, update_user, update_time, revision
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.statisticalDate}, #{item.userId}, #{item.userName}, #{item.dataType},
                #{item.premium}, #{item.amount}, #{item.pt}, #{item.enabledFlag},
                #{item.createUser}, #{item.createTime}, #{item.updateUser}, #{item.updateTime}, #{item.revision}
            )
        </foreach>
    </insert>

    <!-- 根据统计日期范围查询员工推广费记录列表 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="startDate != null">
            AND statistical_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date &lt;= #{endDate}
        </if>
        ORDER BY statistical_date DESC, user_id
    </select>

    <!-- 统计用户推广费总额 -->
    <select id="sumAmountByUserIdAndDateRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amount), 0)
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
    </select>

    <!-- 统计用户保费总额 -->
    <select id="sumPremiumByUserIdAndDateRange" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(premium), 0)
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
    </select>

    <!-- 根据条件查询记录数量 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*)
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
    </select>

    <!-- 分页查询员工推广费记录 -->
    <select id="selectByConditionWithPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        ORDER BY statistical_date DESC, user_id
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 根据用户ID和日期范围统计推广费和保费汇总 -->
    <select id="selectSummaryByUserIdAndDateRange" resultType="java.util.Map">
        SELECT
            user_id as userId,
            user_name as userName,
            data_type as dataType,
            SUM(premium) as totalPremium,
            SUM(amount) as totalAmount,
            COUNT(*) as recordCount
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="userId != null and userId != ''">
            AND user_id = #{userId}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        GROUP BY user_id, user_name, data_type
        ORDER BY totalAmount DESC
    </select>

    <!-- 根据统计日期和数据类型查询汇总信息 -->
    <select id="selectSummaryByDateAndType" resultType="java.util.Map">
        SELECT
            statistical_date as statisticalDate,
            data_type as dataType,
            COUNT(*) as userCount,
            SUM(premium) as totalPremium,
            SUM(amount) as totalAmount
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        GROUP BY statistical_date, data_type
        ORDER BY statistical_date DESC
    </select>

    <!-- 查询用户推广费排行榜 -->
    <select id="selectTopUsersByAmount" resultType="java.util.Map">
        SELECT
            user_id as userId,
            user_name as userName,
            data_type as dataType,
            SUM(premium) as totalPremium,
            SUM(amount) as totalAmount,
            COUNT(*) as recordCount
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        GROUP BY user_id, user_name, data_type
        ORDER BY totalAmount DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据月份统计推广费趋势 -->
    <select id="selectMonthlyTrend" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(statistical_date, '%Y-%m') as month,
            data_type as dataType,
            COUNT(DISTINCT user_id) as userCount,
            SUM(premium) as totalPremium,
            SUM(amount) as totalAmount
        FROM user_promotion_fee_info
        WHERE enabled_flag = 0
        <if test="startDate != null">
            AND statistical_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND statistical_date <= #{endDate}
        </if>
        <if test="dataType != null and dataType != ''">
            AND data_type = #{dataType}
        </if>
        GROUP BY DATE_FORMAT(statistical_date, '%Y-%m'), data_type
        ORDER BY month DESC
    </select>

</mapper>
