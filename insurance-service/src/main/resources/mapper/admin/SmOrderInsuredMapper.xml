<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderInsuredMapper">
    <select id="getByLikeOrderId" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        select * from sm_order_insured
        where fhOrderId like concat(#{fhOrderId},'%')
        <if test="insuredIdNumber!=null">
            and idNumber = #{insuredIdNumber}
        </if>
        <if test="policyStatus !=null">
            and appStatus = #{policyStatus}
        </if>
        order by create_time desc
        limit 1
    </select>
    <select id="queryWhaleGscRefreshPolicy"
            resultType="com.cfpamf.ms.insur.admin.external.whale.model.GscPolicyAllParams">
        SELECT t1.`idNumber` as idCard, t1.`policyNo` as policyNo, t2.fhOrderId as fhOrderId, t1.id as insuredId
        FROM `sm_order_insured` t1
                 INNER JOIN `sm_order` t2 ON t1.`fhOrderId` = t2.`fhOrderId`
                 INNER JOIN `sm_product` t3 ON t3.id = t2.`productId`
        WHERE t1.`policyNo` IS NOT NULL
          and t1.`policyNo` != ''
          and t2.fhOrderId like 'XJ%'
          and t3.`companyId` = 216
          and t2.`create_time` BETWEEN #{startTime} and #{endTime}
          and (LOCATE('chinalife',downloadURL) > 0)
        limit #{start},#{end}
    </select>
    <select id="queryWhaleGscRefreshPolicyTotal" resultType="java.lang.Integer">
        SELECT count(*)
        FROM `sm_order_insured` t1
                 INNER JOIN `sm_order` t2 ON t1.`fhOrderId` = t2.`fhOrderId`
                 INNER JOIN `sm_product` t3 ON t3.id = t2.`productId`
        WHERE t1.`policyNo` IS NOT NULL
          and t1.`policyNo` != ''
          and t2.fhOrderId like 'XJ%'
          and t3.`companyId` = 216
          and t2.`create_time` BETWEEN #{startTime} and #{endTime} and (LOCATE('chinalife',downloadURL) > 0)
    </select>


    <update id="batchSurrender" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update sm_order_insured t,sm_order_item i
            set
            t.appStatus = 4,
            i.app_status = 4,
            i.th_endorsement_no=#{item.endorsementNo}
            where t.fhOrderId = i.fh_order_id
            and t.idNumber = i.id_number
            and i.branch_id = #{item.branchId}
            and i.app_status= 1
            and i.id_number= #{item.beforeIdNumber}
        </foreach>
    </update>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update sm_order_insured t,sm_order_item i
            set
            i.th_endorsement_no = #{item.endorsementNo,jdbcType=VARCHAR},
            t.appStatus = 1,
            i.app_status = 1
            where t.fhOrderId = i.fh_order_id
            and t.idNumber = i.id_number
            and i.branch_id = #{item.branchId}
            and i.id_number = #{item.idNumber}
            and i.app_status = 2
        </foreach>
    </update>

    <select id="countByFhProductIdAndPolicyNo" resultType="java.lang.Integer">
        select count(*) from sm_order_insured i
          INNER JOIN sm_order o on o.fhOrderId  = i.fhOrderId
          INNER JOIN sm_plan p on p.id  = o.planId
        where
          i.enabled_flag = 0
          and i.policyNo in
        <foreach collection="policyNoList" item="item" separator=","
                 open="(" close=")">
            #{item}
        </foreach>
          and p.fhProductId in
        <foreach collection="fhProductIdList" item="item" separator=","
                 open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="listGroupInsuredFix" resultType="com.cfpamf.ms.insur.weixin.pojo.vo.order.GroupInsuredFixVo">
        select
               b.`th_policy_no` as policyNo,
               a.`idNumber` ,
               a.`personName` ,
               `appStatus`,
               c.`startTime` ,
               c.`endTime`
        from `sm_order_insured` a
        left join `sm_order_item` b on a.`fhOrderId` =b.`fh_order_id` and a.`idNumber` =b.`id_number` and b.`enabled_flag` =0
        left join `sm_order` c on a.`fhOrderId` =c.`fhOrderId`
        where b.`th_policy_no` = #{policyNo}
        and a.enabled_flag=0
        order by a.id;
    </select>

    <select id="listGroupInsuredByIdCard" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        select * from sm_order_insured a
        left join sm_order_item b on a.fhOrderId = b.fh_order_id and a.idNumber=b.id_number and b.enabled_flag=0
        where (b.th_policy_no = #{policyNo} or a.policyNo=#{policyNo})
        and a.idNumber=#{idCard}
        and a.enabled_flag=0
        and a.appStatus in ('1','4')
        order by a.id;
    </select>

    <select id="queryFastInsuredListByCodeList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.FastOrderInsuredPo">
        select
            a.id,
            a.fhOrderId,
            c.submitTime,
            b.active_branch_id as activeBranchId,
            b.branch_id as branchId,
            b.total_amount as premium,
            b.insured_amount as amount,
            b.product_id as productId,
            b.plan_id as planId,
            b.plan_code as planCode,
            a.personName,
            a.personGender,
            a.birthday,
            a.idType,
            a.idNumber,
            a.occupationCode,
            a.occupation_group as occupationGroup,
            a.isSecurity,
            a.cellPhone,
            a.appStatus,
            a.relationship,
            a.downloadURL,
            a.email,
            a.insured_time as insuredTime,
            b.th_policy_no as policyNo,
            b.th_endorsement_no as endorsementNo
        from sm_order_insured a
        left join sm_order_item b on a.fhOrderId = b.fh_order_id and a.idNumber=b.id_number and b.enabled_flag=0
        left join sm_order c on a.fhOrderId=c.fhOrderId
        where b.th_policy_no = #{policyNo}
        <if test="branchIdList!=null and branchIdList.size>0 ">
            and b.branch_id in
            <foreach collection="branchIdList" close=")" open="(" item="entry" separator=",">
                #{entry}
            </foreach>
        </if>
        and (
            th_endorsement_no is null
            or th_endorsement_no = ''
            <if test="endorsementList!=null and endorsementList.size>0">
                or th_endorsement_no in
                <foreach collection="endorsementList" open="(" close=")" item="code" separator=",">
                    #{code}
                </foreach>
            </if>
        )
        and a.enabled_flag=0
        order by a.id
    </select>

    <update id="batchUpdateEPolicyUrl">
        <if test="data!=null and data.size>0">
            <foreach collection="data" item="insured" separator=";">
                update sm_order_insured
                    set downloadURL= #{insured.downloadUrl}
                where fhOrderId=#{insured.fhOrderId}
                and idNumber=#{insured.idNumber}
            </foreach>
        </if>
    </update>
    
    <select id="listByEndorsementNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        select
            a.*
        from sm_order_insured a
        left join sm_order_item b on a.fhOrderId=b.fh_order_id and a.idNumber=b.id_number and b.enabled_flag=0
        where b.th_policy_no=#{policyNo}
        and b.th_endorsement_no=#{endorsementNo}
        and a.enabled_flag=0
        and a.appStatus in ('1','4')
    </select>

    <select id="getAllByInsuredId" resultType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        SELECT oi.id,oi.fhOrderId,oi.personName,oi.idNumber,oi.policyNo
        FROM sm_order_insured oi
        LEFT JOIN sm_order o ON oi.`fhOrderId`  = o.fhOrderId
        WHERE `oi`.`idNumber` IN (SELECT idNumber FROM `sm_order_insured` WHERE `id` = #{insuredId} )
        AND o.`productId` = (SELECT o.`productId`  FROM `sm_order_insured` oi LEFT JOIN sm_order o ON oi.`fhOrderId` = o.fhOrderId WHERE oi.`id` = #{insuredId})
        AND `o`.`startTime` = (SELECT o.`startTime`  FROM `sm_order_insured` oi LEFT JOIN sm_order o ON oi.`fhOrderId`  = o.fhOrderId WHERE oi.`id` = #{insuredId})
    </select>

    <update id="updateJobVersion" parameterType="com.cfpamf.ms.insur.admin.pojo.po.SmOrderInsured">
        update sm_order_insured set occupation_version=#{entry.occupationVersion} where id=#{entry.id};
    </update>
</mapper>

