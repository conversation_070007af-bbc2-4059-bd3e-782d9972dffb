<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cfpamf.ms.insur.admin.renewal.dao.PolicyRenewalBaseInfoMapper">


    <insert id="insertRenewalBaseInfo">
        INSERT INTO policy_renewal_base_info
        ( old_policy_no,  new_policy_no, applicant_person_name,applicant_id_number,
            insured_person_name,insured_id_number,start_time,invalid_time,premium,
            product_code,product_name,commodity_name,sales_type,class_one_name,class_two_name,risk_catagory_name,
            group_type,product_id,plan_id,
            grace_day,before_expiration_day,after_expiration_day,renewal_start_time,renewal_end_time,
            customer_org_code,customer_org_name,customer_admin_id,recommend_org_code,recommend_org_name,recommend_id,
            ins_status,todo_state,can_renewal_product_names,old_order_id,give_flag,whale_grace_day,whale_before_expiration_day,
            whale_after_expiration_day,
            whale_renewal_start_time,whale_renewal_end_time)
        SELECT old_policy_no,  new_policy_no, applicant_person_name,applicant_id_number,
            insured_person_name,insured_id_number,start_time,invalid_time,premium,
            product_code,product_name,commodity_name,sales_type,class_one_name,class_two_name,risk_catagory_name,
            group_type,product_id,plan_id,
            grace_day,before_expiration_day,after_expiration_day,renewal_start_time,renewal_end_time,
            customer_org_code,customer_org_name,customer_admin_id,recommend_org_code,recommend_org_name,recommend_id,
            ins_status,todo_state,can_renewal_product_names,old_order_id,give_flag,whale_grace_day,whale_before_expiration_day,
            whale_after_expiration_day,
            whale_renewal_start_time,whale_renewal_end_time
        FROM dwd_policy_renewal_base_info
        where ins_status in ('waited','init')
        ON DUPLICATE KEY UPDATE
        ins_status             = if((policy_renewal_base_info.ins_status='renewed' or policy_renewal_base_info.ins_status='renewing'),policy_renewal_base_info.ins_status,values(ins_status)),
        grace_day              = values(grace_day),
        before_expiration_day  = values(before_expiration_day),
        after_expiration_day   = values(after_expiration_day),
        renewal_start_time     = values(renewal_start_time),
        customer_org_code = values(customer_org_code),
        customer_org_name = values(customer_org_name),
        customer_admin_id = values(customer_admin_id),
        recommend_org_code = values(recommend_org_code),
        recommend_org_name = values(recommend_org_name),
        recommend_id = values(recommend_id),
        renewal_end_time       = values(renewal_end_time),
        can_renewal_product_names = values(can_renewal_product_names),
        old_order_id = values(old_order_id),
        whale_grace_day=values(whale_grace_day),
        whale_before_expiration_day=values(whale_before_expiration_day),
        whale_after_expiration_day=values(whale_after_expiration_day),
        whale_renewal_start_time = values(whale_renewal_start_time),
        whale_renewal_end_time = values(whale_renewal_end_time)
    </insert>

    <update id="updateRenewedStatusByPolicyNo">
        update policy_renewal_base_info
        set  new_policy_no=#{newPolicyNo},ins_status=#{insStatus},
        renewal_type=#{renewalType},new_order_time=#{newOrderTime},new_premium=#{newPremium},
        new_product_code=#{newProductCode},new_product_name=#{newProductName},
        renewed_success_time = if(renewed_success_time is null,now(),renewed_success_time),
        todo_status='DONE',
        todo_finish_time= if(todo_finish_time is null,now(),todo_finish_time),
        update_time = now()
        where old_policy_no = #{oldPolicyNo}
    </update>


    <!-- 断保状态变更-->
    <update id="updateRenewShortBaseCancel">
        update
        policy_renewal_base_info
        set ins_status  ='cancel',todo_status='TIMEOUT',
        update_time = now()
        where
        <![CDATA[
          renewal_end_time < CURDATE()
        ]]>
        and ins_status = 'waited'


    </update>





</mapper>