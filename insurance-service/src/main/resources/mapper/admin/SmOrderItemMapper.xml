<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderItemMapper">
    <insert id="insertOrderItemBatch" useGeneratedKeys="true">
        INSERT INTO sm_order_item
        (
        fh_order_id,
        th_policy_no,
        th_endorsement_no,
        active_branch_id,
        branch_id,
        type,
        product_id,
        plan_id,
        plan_code,
        app_status,
        id_type,
        id_number,
        unit_price,
        qty,
        total_amount,
        endorsement_amount,
        policy_no,
        insured_amount)
        VALUES
        <foreach collection="dtos" item="dto" index="index" separator=",">
            <foreach collection="dto.orderItemList" item="item" index="index" separator=",">
                (#{item.fhOrderId},
                #{item.thPolicyNo},
                #{item.thEndorsementNo},
                #{item.activeBranchId},
                #{item.branchId},
                #{item.type},
                #{item.productId},
                #{item.planId},
                #{item.planCode},
                #{item.appStatus},
                #{item.idType},
                #{item.idNumber},
                #{item.unitPrice},
                #{item.qty},
                #{item.totalAmount},
                #{item.endorsementAmount},
                #{item.policyNo},
                #{item.insuredAmount})
            </foreach>
        </foreach>
    </insert>

    <insert id="insertOrderItemEntityList" useGeneratedKeys="true">
        INSERT INTO sm_order_item
        (
        fh_order_id,
        th_policy_no,
        th_endorsement_no,
        active_branch_id,
        branch_id,
        type,
        product_id,
        plan_id,
        plan_code,
        app_status,
        id_type,
        id_number,
        unit_price,
        qty,
        total_amount,
        endorsement_amount,
        policy_no,
        insured_amount)
        VALUES
        <foreach collection="entityList" item="item" index="index" separator=",">
            (#{item.fhOrderId},
            #{item.thPolicyNo},
            #{item.thEndorsementNo},
            #{item.activeBranchId},
            #{item.branchId},
            #{item.type},
            #{item.productId},
            #{item.planId},
            #{item.planCode},
            #{item.appStatus},
            #{item.idType},
            #{item.idNumber},
            #{item.unitPrice},
            #{item.qty},
            #{item.totalAmount},
            #{item.endorsementAmount},
            #{item.policyNo},
            #{item.insuredAmount})
        </foreach>
    </insert>

    <update id="updateOrderItemSurrenderTimeBatch">
        UPDATE sm_order_item SET app_status='4',
        update_time=CURRENT_TIMESTAMP()
        WHERE app_status = 1
        <if test="idNumbers!= null and idNumbers.size()>0">
            and id_number in
            <foreach collection="idNumbers" item="idNumber" close=")" open="(" separator=",">
                #{idNumber}
            </foreach>
        </if>
        AND fh_order_id=#{orderId}
    </update>

    <update id="updateOrderItemBatch" parameterType="java.util.List">
        <foreach collection="orderItemList" item="item" separator=";">
            update sm_order_item
            set
            app_status = #{item.appStatus},
            endorsement_amount = #{item.endorsementAmount}
            where fh_order_id = #{item.fhOrderId}
            and id_number = #{item.idNumber}
            and th_policy_no = #{item.policyNo}
        </foreach>
    </update>

    <update id="updateOrderItemSurrenderTimeByPolicyNo">
        UPDATE sm_order_item SET app_status='4',
        update_time=CURRENT_TIMESTAMP()
        WHERE
        policy_no=#{policyNo}
        <if test="idNumbers!= null and idNumbers.size()>0">
            and id_number in
            <foreach collection="idNumbers" item="idNumber" close=")" open="(" separator=",">
                #{idNumber}
            </foreach>
        </if>

    </update>
    <update id="updateCommission">
        UPDATE sm_order_item set commission_id = #{commissionId}
        where id_number = #{idNumber} and fh_order_id = #{orderId} and commission_id is null
    </update>
    <update id="updateOrderPolicyInfo">
        UPDATE sm_order_item SET
        app_status=#{policyInfo.appStatus},
        policy_no=#{policyInfo.policyNo},
        th_policy_no=#{policyInfo.policyNo}
        WHERE fh_order_id = #{orderId}
        AND id_number=#{idNumber}
    </update>
    <update id="updateOrderSurrenderTimeSimple">
        UPDATE sm_order_item SET
        app_status='4',
        update_time=CURRENT_TIMESTAMP()
        WHERE
        fh_order_id=#{orderId}
        <if test='idNumber != null'>
            AND id_number=#{idNumber}
        </if>
    </update>

    <select id="listOrderItemByFhOrderIds" resultType="com.cfpamf.ms.insur.admin.pojo.dto.order.SmOrderItemDTO">
        select soi.fh_order_id as fhOrderId,soi.policy_no as policyNo,so.personName as personName,soi.id_number as
        idNumber,sp.productId,soi.plan_id as planId
        ,soi.app_status as appStatus
        from sm_order_item soi,sm_plan sp,sm_order_insured so
        where soi.plan_id = sp.id and soi.fh_order_id = so.fhorderid and soi.policy_no = so.policyNo and soi.id_number=so.idNumber

        <if test="fhOrderIdList != null and fhOrderIdList.size()>0">
            and fh_order_id in
            <foreach collection="fhOrderIdList" item="fhOrderId" close=")" open="(" separator=",">
                #{fhOrderId}
            </foreach>
        </if>
        <if test="fhOrderIdList == null or fhOrderIdList.size()==0">
            and 1!=1
        </if>
    </select>
    <update id="batchCorrectIdNumber" parameterType="java.util.List">
        <foreach collection="data" item="order" separator=";" >
            UPDATE sm_order_item
            SET id_number = #{order.newValue},
            update_time = CURRENT_TIMESTAMP()
            WHERE fh_order_id = #{order.orderId}
            AND   id_number = #{order.oldValue}
        </foreach>
    </update>
    <update id="logicDel" parameterType="java.lang.Integer">
        update sm_order_item set enabled_flag=-1 where id=#{id}
    </update>
    <update id="changePlan">
        UPDATE sm_order_item
        set
            product_id=#{plan.productId},
            plan_id=#{plan.planId},
            plan_code=#{plan.planCode}
        where fh_order_id=#{orderId}
    </update>

    <select id="queryThPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem">
        SELECT fh_order_id,th_policy_no,th_endorsement_no
        FROM sm_order_item
        WHERE enabled_flag = 0
        AND th_policy_no in
        <foreach collection="thPolicyNos" item="thPolicyNo" close=")" open="(" separator=",">
            #{thPolicyNo}
        </foreach>
    </select>
    
    <select id="countEndorsementNo" resultType="java.lang.Integer">
        select count(1) from sm_order_item
        where th_policy_no=#{policyNo}
        and th_endorsement_no=#{endorsementNo}
        and app_status in (1,4)
        and enabled_flag=0
    </select>

    <select id="countPolicyNo" resultType="java.lang.Integer">
        select count(1) from sm_order_item
        where th_policy_no=#{policyNo}
          and app_status in (1,4)
          and enabled_flag=0
    </select>

    <select id="listSomeItemByEndorsementList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem">
        SELECT *
        FROM sm_order_item
        WHERE enabled_flag = 0
          AND th_policy_no = #{policyNo}
          <if test="idCardList!=null and idCardList.size>0">
            and id_number in
                <foreach collection="idCardList" open="(" close=")" item="idCard" separator=",">
                    #{idCard}
                </foreach>
          </if>
          and (
                    th_endorsement_no is null
                    or th_endorsement_no = ''
                <if test="endorsementNoList!=null and endorsementNoList.size>0">
                    or th_endorsement_no in
                    <foreach collection="endorsementNoList" open="(" close=")" item="code" separator=",">
                        #{code}
                    </foreach>
                </if>
            )

    </select>
    <select id="listSomeItemByBranchIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem">
        SELECT *
        FROM sm_order_item
        WHERE enabled_flag = 0
        AND th_policy_no = #{policyNo}
        <if test="idCardList!=null and idCardList.size>0">
            and branch_id in
            <foreach collection="branchIdList" open="(" close=")" item="branchId" separator=",">
                #{branchId}
            </foreach>
        </if>
        and (
            th_endorsement_no is null
            or th_endorsement_no = ''
            <if test="endorsementNoList!=null and endorsementNoList.size>0">
                or th_endorsement_no in
                <foreach collection="endorsementNoList" open="(" close=")" item="code" separator=",">
                    #{code}
                </foreach>
            </if>
        )

    </select>

    <select id="qryNewItemByPolicyNo" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderItem">
        SELECT * FROM sm_order_item WHERE th_policy_no = #{policyNo} AND fh_order_id not LIKE '%\_%';
    </select>
</mapper>

