<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderRiskDutyMapper">
    <update id="updateSurrenderByRiskCode">
        UPDATE sm_order_risk_duty
        SET app_status          ='4',
            surrender_time=ifnull(surrender_time, now())
            <if test="update.surrenderValidTime != null">,surrender_valid_time=#{update.surrenderValidTime}</if>
            <if test="update.surrenderType != null">,surrender_type=#{update.surrenderType}</if>
            <if test="update.refundAmount != null">,refund_amount=#{update.refundAmount}</if>
        WHERE fh_order_id = #{query.fhOrderId}
          and insured_id_number = #{query.insuredIdNumber}
          and risk_code = #{query.riskCode}
    </update>
    
    <select id="listByOrderIdList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty">
        select
            *
        from sm_order_risk_duty
        where fh_order_id in
        <foreach collection="orderIdList" open="(" close=")" separator="," item="orderId">
            #{orderId}
        </foreach>
        <if test="appStatus!=null and appStatus!='' ">
            and app_status = #{appStatus}
        </if>
        and enabled_flag=0

    </select>

    <select id="listByPolicyNoList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderRiskDuty">
        select
            *
        from sm_order_risk_duty
        where policy_no in
        <foreach collection="policyNoList" open="(" close=")" separator="," item="policyNo">
            #{policyNo}
        </foreach>
        and enabled_flag=0

    </select>

    <update id="updateRiskState">
        UPDATE sm_order_risk_duty
            SET app_status = #{update.appStatus}
        WHERE
            fh_order_id = #{update.fhOrderId} and app_status ='-2'
    </update>
    <update id="policyRefund">
        UPDATE sm_order_risk_duty
        SET app_status = #{policyStatus},
        refund_amount = risk_premium,
        surrender_time=now(),
        update_time=now()
        WHERE fh_order_id = #{orderId} and app_status='1'
    </update>
    <update id="policyRepay">
        UPDATE sm_order_risk_duty
        SET app_status = #{policyStatus},
        refund_amount = null,
        surrender_time= null,
        update_time=now()
        WHERE fh_order_id = #{orderId} and app_status='4'
    </update>
</mapper>
