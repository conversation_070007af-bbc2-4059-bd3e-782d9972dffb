<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cfpamf.ms.insur.admin.dao.safes.order.SmOrderPolicyMapper">
    <update id="batchUpdateOrderVisitInfo">
        update sm_order_policy
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="visit_status =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    when policy_no=#{item.policyNo} then #{item.visitStatus}
                </foreach>
            </trim>

            <trim prefix="visit_way =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.visitWay != null and item.visitWay!=""'>
                        when policy_no=#{item.policyNo} then #{item.visitWay}
                    </if>
                </foreach>
            </trim>

            <trim prefix="visit_time =case" suffix="end,">
                <foreach collection="dtos" item="item" index="index">
                    <if test='item.visitTime != null'>
                        when policy_no=#{item.policyNo} then #{item.visitTime}
                    </if>
                </foreach>
            </trim>

            update_time=now()
        </trim>
        where channel = #{channel} and policy_no in
        <foreach collection="dtos" index="index" item="item" separator="," open="(" close=")">
            #{item.policyNo}
        </foreach>
    </update>

    <select id="getUndoVisitPolicyList" resultType="com.cfpamf.ms.insur.weixin.pojo.dto.policy.PolicyVisitNotifyDTO">
        SELECT t3.wxOpenId,
        t4.personName as applicantName,
        t5.productName as productName,
        t2.paymentTime as paymentTime,
        TIMESTAMPDIFF(DAY,t2.paymentTime, now()) as undoVisitDay
        FROM `sm_order_policy` t1
        LEFT JOIN sm_order t2 on t1.fh_order_id = t2.fhOrderId
        left join auth_user t3 on t2.customerAdminId = t3.userId AND t3.enabled_flag = 0
        left join sm_order_applicant t4 on t1.fh_order_id = t4.fhOrderId
        left join sm_product t5 on t2.productId = t5.id
        WHERE
        <![CDATA[TIMESTAMPDIFF(DAY,t2.paymentTime, now()) <= 13 ]]>
        and t1.need_visit = 1
        and t1.visit_status != 'success'
        <if test="null != pushProductIdList and pushProductIdList.size > 0">
            and t5.id in
            <foreach collection="pushProductIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="queryByPolicyList" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.FastOrderPolicy">
        SELECT
            t1.*,
            t2.totalAmount as orderAmount,
            t2.customerAdminId,
            t3.long_insurance as longInsurance,
            t4.appStatus
        FROM `sm_order_policy` t1
        LEFT JOIN `sm_order` t2 ON t1.fh_order_id = t2.fhOrderId
        LEFT JOIN `sm_product` t3 ON t2.productId = t3.id
        LEFT JOIN `sm_order_insured` t4 ON t1.fh_order_id = t4.fhOrderId AND t1.policy_no = t4.policyNo and t4.enabled_flag=0
        WHERE  t1.policy_no in
        <foreach collection="data" item="policyNo" separator="," open="(" close=")">
            #{policyNo}
        </foreach>

    </select>

    <update id="pushRenewalTerm">
        --  缴费以年为单位 长险 未过订单期限 未产生已经续期期数的数据
        insert into sm_order_renewal_term(
            term_num,renewal_status,order_id,policy_no,order_amount,total_term,due_time,customer_admin_id,channel,payment_success_notice_flag,renewal_amount,grace_days
        )
        SELECT TIMESTAMPDIFF(
                   YEAR, t2.startTime,
                         now()) + 2                                                                  AS termNum,
               0                                                                                     as renewalStatus,
               t2.fhOrderId                                                                          AS orderId,
               t1.policy_no                                                                          AS policyNo,
               t2.totalAmount                                                                        AS orderAmount,
               t1.pay_period                                                                         AS totalTerm,
               DATE_ADD(t2.startTime, INTERVAL (TIMESTAMPDIFF( YEAR,t2.startTime,now())+1) year) AS dueTime,
               t2.customerAdminId                                                                    AS customerAdminId,
               t1.channel                                                                            AS channel,
               0,
               t2.totalAmount,
               #{defaultGraceDays}
        FROM `sm_order_policy` t1
                 LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
                 LEFT JOIN sm_product t3 ON t2.productId = t3.id
                 LEFT JOIN sm_order_insured t4 ON t1.fh_order_id = t4.fhOrderId AND t1.policy_no = t4.policyNo
                 LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and
                                                       t5.term_num = TIMESTAMPDIFF(YEAR, t2.startTime, now()) + 2
        WHERE t1.channel = #{channel}
          and t1.enabled_flag=0
          AND t1.pay_unit = 'y'
          AND t4.appStatus = '1'
          AND t3.long_insurance = 0
          AND t1.pay_period > 1
          and not exists (select 1 from sm_order_renewal_term t6 where t6.policy_no = t1.policy_no and t6.term_num = TIMESTAMPDIFF(YEAR, t2.startTime, now()) + 1 and t6.renewal_status=2 and t6.enabled_flag=0)
          AND t5.id is null
        <![CDATA[ AND TIMESTAMPDIFF(YEAR, t2.startTime, now()) < t1.pay_period - 1
          AND TIMESTAMPDIFF(
                  DAY, now(),
                       DATE_ADD(t2.startTime, INTERVAL ( TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 1 ) YEAR )
                  ) <= #{beforeDay}
          and TIMESTAMPDIFF(
                  DAY, now(),
                       DATE_ADD(t2.startTime, INTERVAL ( TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 1 ) YEAR )
                  ) > 0
        ]]>
        on duplicate key update update_time=now()
    </update>
    <update id="pushOtherRenewalTerm">
        --  缴费以年为单位 长险 未过订单期限 未产生已经续期期数的数据
        insert into sm_order_renewal_term(
            term_num,renewal_status,order_id,policy_no,order_amount,total_term,due_time,customer_admin_id,channel,payment_success_notice_flag,renewal_amount,grace_days
        )
        SELECT TIMESTAMPDIFF(
                   YEAR, t2.startTime,
                         now()) + 2                                                                  AS termNum,
               0                                                                                     as renewalStatus,
               t2.fhOrderId                                                                          AS orderId,
               t1.policy_no                                                                          AS policyNo,
               t2.totalAmount                                                                        AS orderAmount,
               t1.pay_period                                                                         AS totalTerm,
               DATE_ADD(t2.startTime, INTERVAL (TIMESTAMPDIFF( YEAR,t2.startTime,now())+1) year) AS dueTime,
               t2.customerAdminId                                                                    AS customerAdminId,
               t1.channel                                                                            AS channel,
               0,
               t2.totalAmount,
               #{defaultGraceDays}
        FROM `sm_order_policy` t1
                 LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
                 LEFT JOIN sm_product t3 ON t2.productId = t3.id
                 LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and
                                                       t5.term_num = TIMESTAMPDIFF(YEAR, t2.startTime, now()) + 2
        WHERE  t1.channel not in ('fx','xm')
          and t1.enabled_flag=0
          AND ifnull(t1.pay_period,0) > 1

          AND exists( select 1 from sm_order_insured t4 where t4.fhOrderId=t1.fh_order_id and t4.policyNo=t1.policy_no and t4.appStatus='1')
          and not exists (select 1 from sm_order_renewal_term t6 where t6.policy_no = t1.policy_no and t6.term_num = TIMESTAMPDIFF(YEAR, t2.startTime, now()) + 1 and t6.renewal_status=2 and t6.enabled_flag=0)
          AND t3.long_insurance = 0
        <if test="channel!=null">
          AND t1.channel = #{channel}
        </if>
          AND t5.id is null
        <![CDATA[ AND TIMESTAMPDIFF(YEAR, t2.startTime, now()) < t1.pay_period - 1
          AND TIMESTAMPDIFF(
                  DAY, now(),
                       DATE_ADD(t2.startTime, INTERVAL ( TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 1 ) YEAR )
                  ) <= #{beforeDay}
          and TIMESTAMPDIFF(
                  DAY, now(),
                       DATE_ADD(t2.startTime, INTERVAL ( TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 1 ) YEAR )
                  ) > 0
        ]]>
        on duplicate key update update_time=now()
    </update>

    <update id="pushFirstPhaseRenewalTerm">
        insert into sm_order_renewal_term(
            term_num,renewal_status,order_id,policy_no,order_amount,total_term,due_time,customer_admin_id,channel,payment_success_notice_flag,renewal_amount,payment_time,grace_days,renewal_success_sync_date
        )

        SELECT 1                  AS termNum,
               1                  as renewalStatus,
               t2.fhOrderId       AS orderId,
               t1.policy_no       AS policyNo,
               t2.totalAmount     AS orderAmount,
               t1.pay_period      AS totalTerm,
               t2.startTime     AS dueTime,
               t2.customerAdminId AS customerAdminId,
               t1.channel         AS channel,
               0,
               t2.totalAmount     as renewal_amount,
               t2.paymentTime     as payment_time,
               #{defaultGraceDays},
               now()
        FROM `sm_order_policy` t1
                 LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
                 LEFT JOIN sm_product t3 ON t2.productId = t3.id
                 LEFT JOIN sm_order_insured t4 ON t1.fh_order_id = t4.fhOrderId
            AND t1.policy_no = t4.policyNo
                 LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and t5.term_num = 1
        WHERE t1.channel = #{channel}
          AND t1.pay_unit = 'y'
          and t1.enabled_flag=0
          AND t4.appStatus = '1'
          AND t3.long_insurance = 0
          AND t1.pay_period > 1
          AND t5.id is null
        on duplicate key update update_time=now()
    </update>

    <update id="pushOtherFirstPhaseRenewalTerm">
        insert into sm_order_renewal_term(
            term_num,renewal_status,order_id,policy_no,order_amount,total_term,due_time,customer_admin_id,channel,payment_success_notice_flag,renewal_amount,payment_time,grace_days,renewal_success_sync_date
        )

        SELECT 1                  AS termNum,
               1                  as renewalStatus,
               t2.fhOrderId       AS orderId,
               t1.policy_no       AS policyNo,
               t2.totalAmount     AS orderAmount,
               t1.pay_period      AS totalTerm,
               t2.startTime     AS dueTime,
               t2.customerAdminId AS customerAdminId,
               t1.channel         AS channel,
               0,
               t2.totalAmount     as renewal_amount,
               t2.paymentTime     as payment_time,
               #{defaultGraceDays},
               now()
        FROM `sm_order_policy` t1
                 LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
                 LEFT JOIN sm_product t3 ON t2.productId = t3.id

                 LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and t5.term_num = 1
        WHERE t1.channel not in ('fx','xm')
          and t1.enabled_flag=0
          and ifnull(t1.pay_period,0) > 1
          AND exists( select 1 from sm_order_insured t4 where t4.fhOrderId=t1.fh_order_id and t4.policyNo=t1.policy_no and t4.appStatus='1')
          AND t3.long_insurance = 0
        <if test="channel!=null">
            AND t1.channel = #{channel}
        </if>
          AND t5.id is null
        on duplicate key update update_time=now()
    </update>

    <update id="pushCompensatePhaseRenewalTerm">
        insert into sm_order_renewal_term(
            term_num,renewal_status,order_id,policy_no,order_amount,total_term,due_time,customer_admin_id,channel,payment_success_notice_flag,renewal_amount,grace_days
        )

        SELECT #{termNum}                  AS termNum,
        0                  as renewalStatus,
        t2.fhOrderId       AS orderId,
        t1.policy_no       AS policyNo,
        t2.totalAmount     AS orderAmount,
        t1.pay_period      AS totalTerm,
        DATE_ADD(t2.startTime, INTERVAL #{termNum}-1 year) AS dueTime,
        t2.customerAdminId AS customerAdminId,
        t1.channel         AS channel,
        0,
        t2.totalAmount     as renewal_amount,
        #{defaultGraceDays}
        FROM `sm_order_policy` t1
        LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and t5.term_num = #{termNum}
        WHERE t1.channel not in ('fx','xm')
        and ifnull(t1.pay_period,0) > 1
        AND exists( select 1 from sm_order_insured t4 where t4.fhOrderId=t1.fh_order_id and t4.policyNo=t1.policy_no and t4.appStatus='1')
        and not exists (select 1 from sm_order_renewal_term t6 where t6.policy_no = t1.policy_no and t6.term_num = #{termNum}-1 and t6.renewal_status=2 and t6.enabled_flag=0)
        AND t3.long_insurance = 0
        AND t5.id is null
        and t2.fhOrderId in
        <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        on duplicate key update update_time=now()
    </update>

    <!-- 生成续期数据-V2-Start -->
    <select id="queryFirstPhaseRenewalTerm" resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm">
        SELECT
            1                  AS termNum,
            1                  as renewalStatus,
            t2.fhOrderId       AS orderId,
            t1.policy_no       AS policyNo,
            t2.totalAmount     AS orderAmount,
            t1.pay_period      AS totalTerm,
            t2.startTime       AS dueTime,
            t2.customerAdminId AS customerAdminId,
            t1.channel         AS channel,
            0                  as paymentSuccessNoticeFlag,
            t2.totalAmount     as renewal_amount,
            t2.paymentTime     as payment_time,
            #{defaultGraceDays} as grace_days,
            now()               as renewal_success_sync_date
        FROM `sm_order_policy` t1
        LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and t5.term_num = 1
        WHERE t1.enabled_flag=0
        <if test=" excludeChannel!=null and excludeChannel.size>0 ">
            and t1.channel not in 
            <foreach collection="excludeChannel" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="channel!=null">
            AND t1.channel = #{channel}
        </if>
        and IFNULL(t1.pay_period,0) > 1
        AND exists( select 1 from sm_order_insured t4 where t4.fhOrderId=t1.fh_order_id and t4.policyNo=t1.policy_no and t4.appStatus='1')
        AND t3.long_insurance = 0
        AND t5.id is null
        limit #{offset},#{size}
    </select>

    <select id="queryRenewalTermByOrderId" resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm">
        SELECT
            #{termNum}         AS termNum,
            0                  as renewalStatus,
            t2.fhOrderId       AS orderId,
            t1.policy_no       AS policyNo,
            t2.totalAmount     AS orderAmount,
            t1.pay_period      AS totalTerm,
            DATE_ADD(t2.startTime, INTERVAL #{termNum}-1 year) AS dueTime,
            t2.customerAdminId AS customerAdminId,
            t1.channel         AS channel,
            0                  as paymentSuccessNoticeFlag,
            t2.totalAmount     as renewal_amount,
            #{defaultGraceDays} as grace_days
        FROM `sm_order_policy` t1
        LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and t5.term_num = #{termNum}
        WHERE t1.channel not in ('fx','xm')
        and ifnull(t1.pay_period,0) > 1
        AND exists( select 1 from sm_order_insured t4 where t4.fhOrderId=t1.fh_order_id and t4.policyNo=t1.policy_no and t4.appStatus='1')
        and not exists (select 1 from sm_order_renewal_term t6 where t6.policy_no = t1.policy_no and t6.term_num = #{termNum}-1 and t6.renewal_status=2 and t6.enabled_flag=0)
        AND t3.long_insurance = 0
        AND t5.id is null
        and t2.fhOrderId in
        <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryToRenewalTermList" resultType="com.cfpamf.ms.insur.admin.renewal.entity.SmOrderRenewalTerm">
        SELECT
        TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 2                                         AS termNum,
        0                                                                                     as renewalStatus,
        t2.fhOrderId                                                                          AS orderId,
        t1.policy_no                                                                          AS policyNo,
        t2.totalAmount                                                                        AS orderAmount,
        t1.pay_period                                                                         AS totalTerm,
        DATE_ADD(t2.startTime, INTERVAL (TIMESTAMPDIFF( YEAR,t2.startTime,now())+1) year)     AS dueTime,
        t2.customerAdminId                                                                    AS customerAdminId,
        t1.channel                                                                            AS channel,
        0                  as paymentSuccessNoticeFlag,
        t2.totalAmount     as renewal_amount,
        #{defaultGraceDays} as grace_days
        FROM `sm_order_policy` t1
        LEFT JOIN sm_order t2 ON t1.fh_order_id = t2.fhOrderId
        LEFT JOIN sm_product t3 ON t2.productId = t3.id
        LEFT JOIN sm_order_renewal_term t5 on t1.policy_no = t5.policy_no and t5.term_num = TIMESTAMPDIFF(YEAR, t2.startTime, now()) + 2
        WHERE t1.enabled_flag=0
        <if test=" excludeChannel!=null and excludeChannel.size>0 ">
            and t1.channel not in
            <foreach collection="excludeChannel" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        AND ifnull(t1.pay_period,0) > 1
        AND exists( select 1 from sm_order_insured t4 where t4.fhOrderId=t1.fh_order_id and t4.policyNo=t1.policy_no and t4.appStatus='1')
        and not exists (select 1 from sm_order_renewal_term t6 where t6.policy_no = t1.policy_no and t6.term_num = TIMESTAMPDIFF(YEAR, t2.startTime, now()) + 1 and t6.renewal_status=2 and t6.enabled_flag=0)
        AND t3.long_insurance = 0
        <if test="channel!=null">
            AND t1.channel = #{channel}
        </if>
        AND t5.id is null
        <![CDATA[ AND TIMESTAMPDIFF(YEAR, t2.startTime, now()) < t1.pay_period - 1
          AND TIMESTAMPDIFF( DAY, now(), DATE_ADD(t2.startTime, INTERVAL ( TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 1 ) YEAR )) <= #{beforeDay}
          and TIMESTAMPDIFF( DAY, now(), DATE_ADD(t2.startTime, INTERVAL ( TIMESTAMPDIFF( YEAR, t2.startTime, now()) + 1 ) YEAR )) > 0
        ]]>
        limit #{offset},#{size}
    </select>


    <!-- 生成续期数据-V2-End  -->

    <select id="getRenewalTermPolicyDetailVo"
            resultMap="renewalTermPolicyDetailVoMap">
        select t5.productName                                                       as productName,
               t1.policy_no                                                         as policyNo,
               t1.start_date                                                        as policyNoStartTime,
               t2.customerAdminId                                                   as customerAdminId,
               t2.customerAdminMainJobNumber                                        as customerAdminMainJobNumber,
               t1.premium                                                           as premium,
               (case when pay_unit = 'Y' or pay_unit = 'y' then '年交' else '未知' end) as paymentFrequency,
               t4.personName                                                        as applicantPersonName,
               t4.cellPhone                                                         as applicantPersonCellPhone,
               t4.email                                                             as applicantPersonEmail,
               t4.idNumber                                                          as applicantPersonIdNumber,
               t4.birthday                                                          as applicantPersonBirthday,
               t4.address                                                           as applicantPersonAddress,
               t6.personName                                                        as insuredPersonName,
               t6.cellPhone                                                         as insuredPersonCellPhone,
               t6.email                                                             as insuredPersonEmail,
               t6.idNumber                                                          as insuredPersonIdNumber,
               t6.birthday                                                          as insuredPersonBirthday,
               t6.address                                                           as insuredPersonAddress

        FROM sm_order_policy t1
                 LEFT JOIN sm_order t2
                           on t1.fh_order_id = t2.fhOrderId and t2.enabled_flag=0
                 LEFT JOIN auth_user t3 on t2.customerAdminId = t3.userId AND t3.enabled_flag = 0
                 LEFT JOIN sm_order_applicant t4 on t1.fh_order_id = t4.fhOrderId
                 LEFT JOIN sm_product t5 on t2.productId = t5.id
                 LEFT JOIN sm_order_insured t6 ON t1.fh_order_id = t6.fhOrderId
        where t1.policy_no = #{policyNo} and  t1.`enabled_flag` =0
    </select>

    <select id="listRenewalTermPolicyDetailVo"
            resultMap="renewalTermPolicyDetailVoMap">
        select t5.productName                                                       as productName,
               t1.policy_no                                                         as policyNo,
               t1.start_date                                                        as policyNoStartTime,
               t2.customerAdminId                                                   as customerAdminId,
               t2.customerAdminMainJobNumber                                        as customerAdminMainJobNumber,
               t1.premium                                                           as premium,
               (case when pay_unit = 'Y' or pay_unit = 'y' then '年交' else '未知' end) as paymentFrequency,
               t4.personName                                                        as applicantPersonName,
               t4.cellPhone                                                         as applicantPersonCellPhone,
               t4.email                                                             as applicantPersonEmail,
               t4.idNumber                                                          as applicantPersonIdNumber,
               t4.birthday                                                          as applicantPersonBirthday,
               t4.address                                                           as applicantPersonAddress,
               t6.personName                                                        as insuredPersonName,
               t6.cellPhone                                                         as insuredPersonCellPhone,
               t6.email                                                             as insuredPersonEmail,
               t6.idNumber                                                          as insuredPersonIdNumber,
               t6.birthday                                                          as insuredPersonBirthday,
               t6.address                                                           as insuredPersonAddress

        FROM sm_order_policy t1
                 LEFT JOIN sm_order t2
                           on t1.fh_order_id = t2.fhOrderId and t2.enabled_flag=0
                 LEFT JOIN auth_user t3 on t2.customerAdminId = t3.userId AND t3.enabled_flag = 0
                 LEFT JOIN sm_order_applicant t4 on t1.fh_order_id = t4.fhOrderId
                 LEFT JOIN sm_product t5 on t2.productId = t5.id
                 LEFT JOIN sm_order_insured t6 ON t1.fh_order_id = t6.fhOrderId
        where t1.policy_no = #{policyNo} and  t1.`enabled_flag` =0
    </select>

    <resultMap id="renewalTermPolicyDetailVoMap"
               type="com.cfpamf.ms.insur.admin.pojo.vo.renewal.RenewalTermPolicyDetailVo">

        <result column="productName" property="productName"/>
        <result column="policyNo" property="policyNo"/>
        <result column="policyNoStartTime" property="policyNoStartTime"/>
        <result column="premium" property="premium"/>
        <result column="paymentFrequency" property="paymentFrequency"/>
        <result column="customerAdminId" property="customerAdminId"/>
        <result column="customerAdminMainJobNumber" property="customerAdminMainJobNumber"/>

        <!--        被保人信息映射-->
        <result column="insuredPersonName" property="insuredPerson.name"/>
        <result column="insuredPersonEmail" property="insuredPerson.email"/>
        <result column="insuredPersonIdNumber" property="insuredPerson.idCard"/>
        <result column="insuredPersonCellPhone" property="insuredPerson.mobileNumber"/>
        <result column="insuredPersonBirthday" property="insuredPerson.birthday"/>
        <result column="insuredPersonAddress" property="insuredPerson.address"/>
        <!--        投保人信息映射-->
        <result column="applicantPersonName" property="applicantPerson.name"/>
        <result column="applicantPersonEmail" property="applicantPerson.email"/>
        <result column="applicantPersonIdNumber" property="applicantPerson.idCard"/>
        <result column="applicantPersonCellPhone" property="applicantPerson.mobileNumber"/>
        <result column="applicantPersonBirthday" property="applicantPerson.birthday"/>
        <result column="applicantPersonAddress" property="applicantPerson.address"/>

    </resultMap>

    <select id="get" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy">
        select *
        from sm_order_policy
        where channel = #{channel}
        and pay_unit = #{payUnit}
        and policy_no in
        <foreach collection="policyNoList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryByPage" resultType="com.cfpamf.ms.insur.admin.pojo.po.order.SmOrderPolicy">
        select
            *
        from sm_order_policy t
        where
        policy_state = '1'
        and channel = #{channel}
        and visit_status not in ('noneed','success')
        <if test="start!=null">
            and create_time &gt;= #{start}
        </if>
        <if test="end!=null">
            and create_time &lt; #{end}
        </if>
        limit #{offset}, #{size}
    </select>

    <update id="update2BackVisitInfo">
        update sm_order_policy
        set
            visit_status=#{visitStatus}
            , visit_way=#{visitWay}
        <if test="successTime!=null and successTime!='' ">
            , visit_time=#{successTime}
        </if>
        where policy_no = #{policyNo}
    </update>
    <update id="changePlan">
        update sm_order_policy
        set
            channel=#{plan.channel},

    </update>
    <update id="refund">
        update sm_order_policy
        set
        policy_state = #{policyStatus},
        cancel_amount = premium,
        surrender_time = now(),
        update_time=now()
        where fh_order_id=#{orderId}
    </update>
    <update id="repay">
        update sm_order_policy
        set
        policy_state = #{policyStatus},
        cancel_amount = null,
        surrender_time = null,
        surrender_valid_time = null,
        update_time=now()
        where fh_order_id=#{orderId}
    </update>
</mapper>
