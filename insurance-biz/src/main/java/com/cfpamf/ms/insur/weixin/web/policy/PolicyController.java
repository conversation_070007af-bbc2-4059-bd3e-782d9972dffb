
package com.cfpamf.ms.insur.weixin.web.policy;

import com.alibaba.fastjson.JSON;
import com.cfpamf.cmis.common.utils.DateUtils;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.insur.admin.enums.EnumChannel;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.base.util.ValidatorUtils;
import com.cfpamf.ms.insur.base.util.pdf.FontProviderFactory;
import com.cfpamf.ms.insur.base.util.pdf.PDFKit;
import com.cfpamf.ms.insur.base.util.pdf.WhaleFontProvider;
import com.cfpamf.ms.insur.dto.group.ProxyStatement;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.OfflinePayDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.policy.EndorListDTO;
import com.cfpamf.ms.insur.weixin.pojo.po.order.PayNotice;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.common.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.apply.zhongan.order.ProductReq;
import com.cfpamf.ms.insur.weixin.pojo.vo.group.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.HealthNoticeVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.ProductDetailVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.ProductExtendVo;
import com.cfpamf.ms.insur.weixin.service.policy.PolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 修订版本：S49
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@RequestMapping(BaseConstants.WX_VERSION)
@Api(tags = {"团险投保流程Api"}, value = "团险投保流程Api")
public class PolicyController {

    @Autowired
    private PolicyService policyService;

    @ApiOperation(value = "团险-查询商品详情页面")
    @ApiImplicitParams({@ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long")})
    @GetMapping("/product/basic")
    public ProductDetailVo productInfo(@RequestParam(value = "productId") int productId) {
        String region = policyService.checkAuthority().getRegionName();
        log.info("查询商品信息:productId-{}，region-{}", productId, region);
        return policyService.queryProductInfo(productId, region);
    }

    @ApiOperation(value = "产品展示-扩展信息（投保须知，常见问题，保险条款）")
    @ApiImplicitParams({@ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long")})
    @GetMapping("/product/extend")
    public ProductExtendVo extend(@RequestParam(value = "productId") int productId) {
        return policyService.queryExtend(productId);
    }

    @ApiOperation(value = "产品展示-健康告知")
    @ApiImplicitParams({@ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long")})
    @GetMapping("/product/health")
    public HealthNoticeVo health(@RequestParam(value = "productId") int productId) {
        return policyService.queryHealth(productId);
    }

    @ApiOperation(value = "团险-试算保费")
    @PostMapping("/{channel}/group/inquiry")
    public GroupInquiryResponse inquiry(@RequestBody GroupInquiry req, @PathVariable("channel") String channel) {
        if (CollectionUtils.isEmpty(req.getPersonList())) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "被保人参数不能为空");
        }
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        ValidatorUtils.validateParam(req);
        log.info("开始试算保费:{},{}", channel, req);
        return policyService.inquiry(channel, req);
    }

    /**
     * 针对后台配置的[配置条件]检查订单信息 [保障期限] 跟产品沟通过，该项不做校验
     *
     * @param req
     */
    @ApiOperation(value = "团险-内部投保校验")
    @PostMapping("/group/check")
    public boolean check(@RequestBody GroupCheckReq req) {
        return policyService.applyCheck(req);
    }

    @ApiOperation(value = "团险-保司报价")
    @PostMapping("/{channel}/group/quotePrice")
    public GroupQuoteResponse quotePrice(@RequestBody GroupUnderwriting req, @PathVariable("channel") String channel) {
        log.info("团单试算:{},{}", JSON.toJSON(req), channel);
        ProductReq product = req.getProduct();
        if (product == null) {
            throw new BizException("-1", "产品套餐信息不能为空");
        }
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        ValidatorUtils.validateParam(req);
        return policyService.quotePrice4Group(channel, req);
    }

    @ApiOperation(value = "团险-核保接口(创建团单)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/underwriting")
    public GroupQuoteResponse underwriting(@RequestBody GroupUnderwriting req, @PathVariable("channel") String channel) {
        log.info("团单核保:{},{}", JSON.toJSON(req), channel);
        String openId = req.getOpenId();
        String token = HttpRequestUtil.getToken();
        String sessionKey = "";
        if (StringUtils.isBlank(openId)&&StringUtils.isBlank(token)) {
            log.warn("请求参数openId,token为空:{},{}", openId,token);
            throw new MSBizNormalException(ExcptEnum.INVALID_TOKEN_501019);
        }
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        ValidatorUtils.validateParam(req);
        if(!StringUtils.isBlank(openId)){
            sessionKey = openId;
        }else {
            sessionKey = BaseConstants.USER_TOKEN+token;
        }
        Agent agent = pickAgent(sessionKey);
        req.setAgent(agent);
        req.setChannel(channel);
        return policyService.underwriting(channel, req);
    }

    @ApiOperation(value = "团险-批改保费试算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/endor/calpremium")
    public GroupEndorResponse endorCalPremium(@RequestBody GroupEndorsement req,
                                              @PathVariable("channel") String channel) {
        log.info("批改单试算:{},{}", JSON.toJSON(req), channel);
        ValidatorUtils.validateParam(req);
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.endorCalPremium(channel, req);
    }

    @ApiOperation(value = "团险-批改信息校验(批改核保)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/endor/underwriting")
    public GroupEndorResponse endorUnderwriting(@RequestBody GroupEndorsement req,
                                                @PathVariable("channel") String channel) {
        ValidatorUtils.validateParam(req);
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.endorUnderwriting(channel, req);
    }

    @ApiOperation(value = "团险-批改信息提交")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/endor/commit")
    public GroupEndorResponse submitEndor(@RequestBody GroupEndorsement req, @PathVariable("channel") String channel) {
        log.info("批改单提交:{},{}", channel, JSON.toJSON(req));
        String openId = req.getOpenId();
        String token = HttpRequestUtil.getToken();
        String sessionKey = "";
        if (StringUtils.isBlank(openId)&&StringUtils.isBlank(token)) {
            log.warn("请求参数openId,token为空:{},{}", openId,token);
            throw new MSBizNormalException(ExcptEnum.INVALID_TOKEN_501019);
        }
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        ValidatorUtils.validateParam(req);
        if(!StringUtils.isBlank(openId)){
            sessionKey = openId;
        }else {
            sessionKey = BaseConstants.USER_TOKEN+token;
        }
        Agent agent = pickAgent(sessionKey);
        req.setAgent(agent);
        req.setChannel(channel);
        return policyService.endorCommit(channel, req);
    }

    /**
     * 查询代理人
     *
     * @param openId
     * @return
     */
    private Agent pickAgent(String openId) {
        Agent agent = new Agent();
        WxSessionVO session = policyService.getWxSession(openId);
        if (session.getAgentId() != null) {
            agent.setAgentId(session.getAgentId());
        }
        agent.setJobCode(session.getJobCode());
        if (!StringUtils.isBlank(session.getUserId())) {
            agent.setRecommendId(session.getUserId());
            agent.setRecommendOrgCode(session.getOrgCode());
            agent.setRecommendMainJobNumber(session.getMainJobNumber());
        }
        return agent;
    }

    @ApiOperation(value = "团险-批改生效")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/endor/effect")
    public Boolean effectEndor(@RequestBody GroupEndorEffectReq req, @PathVariable("channel") String channel) {
        if (StringUtils.isBlank(req.getChannel())) {
            req.setChannel(channel);
        }
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.effectEndor(channel, req);
    }

    @ApiOperation(value = "团险-继续批改流程")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/endor/supply")
    public GroupEndorResponse endorSupply(@Valid @RequestBody GroupEndorSupply supply,
                                          @PathVariable("channel") String channel) {
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.endorSupply(channel, supply.getOrderId());
    }

    @ApiOperation(value = "团险-撤销批改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")})
    @PostMapping("/{channel}/group/endor/revoke")
    public Boolean revokeEndor(@Valid @RequestBody GroupRevokeVo req, @PathVariable("channel") String channel) {
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.revokeEndor(channel, req);
    }

    @ApiOperation(value = "团险-撤销批改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")
    })
    @PostMapping("/{channel}/admin/endor/revoke")
    public Boolean revokeEndor4Admin(@Valid @RequestBody GroupRevokeVo req, @PathVariable("channel") String channel) {
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.revokeEndor4Admin(channel, req);
    }

    @ApiOperation(value = "团险-替换人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "String")
    })
    @PostMapping("/{channel}/member/change")
    public MemberChange memberChange(@RequestBody GroupEndorsement request, @PathVariable("channel") String channel) {
        log.info("团单替换人员:{},{}", channel, request);
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        ValidatorUtils.validateParam(request);
        request.setChannel(channel);
        return policyService.memberChange(channel, request);
    }

    /*********************************** 👇👇👇👇👇[S53]👇👇👇👇👇 *******************************************/

    @ApiOperation(value = "申请明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invoiceStatus", value = "开票状态：0=未申请,1=已申请", required = false, dataType = "String")})
    @GetMapping("/invoice/apply/list")
    public List<EndorListDTO> getEndorList(@RequestParam(value = "orderId") String orderId,
                                           @RequestParam(value = "policyNo") String policyNo,
                                           @RequestParam(value = "invoiceStatus", required = false) String invoiceStatus) {
        return policyService.getEndorList4Invoice(orderId, policyNo, invoiceStatus);
    }

    @ApiOperation(value = "团险模块-发票详情")
    @ApiImplicitParams({})
    @GetMapping("/group/invoice/detail")
    public InvoiceVo policyList(@RequestParam("id") Integer id) {
        return policyService.queryInvoice(id);
    }

    @ApiOperation(value = "团险模块-开票")
    @PostMapping("/{channel}/group/invoice")
    public InvoiceResponse openInvoice(@RequestBody InvoiceVo data,
                                       @PathVariable(value = "channel") String channel) {
        log.info("团单-开票申请：{},{}", channel, data);
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        ValidatorUtils.validateParam(data);
        return policyService.openInvoice(channel, data);
    }

    @ApiOperation(value = "团险模块-发票节点信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "订单Id", value = "订单Id", required = true, dataType = "String")})
    @GetMapping("/invoice/item/list")
    public List<PolicyItem> invoiceItemList(@RequestParam("orderId") String orderId) {
        return policyService.invoiceItemList(orderId);
    }

    /**
     * 线下转账
     */
    @ApiOperation(value = "团险模块-下载付费通知书")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "订单Id", value = "orderId", required = true, dataType = "String"),
            @ApiImplicitParam(name = "订单类型：0=原单,1=批改单", value = "type", required = true, dataType = "String"),
    })
    @GetMapping("/download/notice")
    public void downPayNotice(@RequestParam("orderId") String orderId, @RequestParam("type") String type, HttpServletResponse response) throws IOException {
        PayNotice notice = policyService.queryPayNotice(orderId, type);
        log.info("付费单数据:{},{},{}", orderId, type, notice);
        if (notice == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg());
        }

        String fileName = URLEncoder.encode("notice.pdf", StandardCharsets.UTF_8.name());

        if (StringUtils.isBlank(notice.getEffectiveTime())) {
            Calendar c = Calendar.getInstance();
            c.add(Calendar.DATE, 1);
            notice.setEffectiveTime(DateUtils.format(c.getTime(), DateUtils.FORMAT_TIME));
        }

        log.info("开始生成付费单文件:{}", notice);

        if (EnumChannel.ZA.getCode().equals(notice.getChannel())) {
            genPDF4ZhongAn(notice, fileName, response);
        } else {
            genPDF4TaiKang(notice, fileName, response);
        }
        return;
    }

    private void genPDF4ZhongAn(PayNotice notice, String fileName, HttpServletResponse response) throws IOException {
        Instant instant = new Date().toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime time = instant.atZone(zoneId).toLocalDateTime();
        LocalDateTime lc = time.plusDays(3);
        String timeFormat = "{0}年{1}月{2}日24时";
        String payTime = MessageFormat.format(timeFormat, String.valueOf(lc.getYear()), lc.getMonthValue(), lc.getDayOfMonth());
        notice.setPayTime(payTime);

        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        response.setContentType("application/octet-stream");

        String ftlResource = "tempFile" + File.separatorChar + "pay.ftl";
        String imageResource = "tempFile" + File.separatorChar + "za.jpeg";

        PDFKit kit = new PDFKit();
        OutputStream os = response.getOutputStream();
        WhaleFontProvider fontProvider = FontProviderFactory.getFontProvider();
        kit.exportToStream(ftlResource, imageResource, notice, fontProvider, os);
        os.flush();
    }

    private void genPDF4TaiKang(PayNotice notice, String fileName, HttpServletResponse response) throws IOException {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.parse(notice.getEffectiveTime(), formatter);
        time = time.plusDays(-1);
        String timeFormat = "{0}年{1}月{2}日24时";
        String payTime = MessageFormat.format(timeFormat, String.valueOf(time.getYear()), time.getMonthValue(), time.getDayOfMonth());
        notice.setPayTime(payTime);

        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        response.setContentType("application/octet-stream");

        String ftlResource = "tempFile" + File.separatorChar + "tkpay.ftl";
        String imageResource = "tempFile" + File.separatorChar + "tk.jpg";

        PDFKit kit = new PDFKit();
        OutputStream os = response.getOutputStream();
        WhaleFontProvider fontProvider = FontProviderFactory.getFontProvider();
        kit.exportToStream(ftlResource, imageResource, notice, fontProvider, os);
        os.flush();
    }


    /**
     * 线下转账
     */
    @ApiOperation(value = "团险模块-下载委托书模板")
    @ApiImplicitParams({@ApiImplicitParam(name = "type", value = "type", required = true, dataType = "String")})
    @GetMapping("/download/proxy-statement")
    public void downProxyStatement(@RequestParam("type") String type, HttpServletResponse response) throws IOException {

        ProxyStatement data = getProxyStatement(type);
        if (data == null) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), ExcptEnum.PARAMS_ERROR.getMsg());
        }
        InputStream is = new ClassPathResource("tempFile" + File.separatorChar + data.getDocName()).getInputStream();
        String fileName = new String(data.getShowName().getBytes("UTF-8"), "iso-8859-1");

        response.setHeader("Content-disposition", "attachment; filename=" + fileName);
        response.setContentType("application/octet-stream");

        OutputStream os = null;
        try {
            os = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) > 0) {
                os.write(buffer, 0, len);
            }
            os.flush();
        } catch (Exception e) {
            log.warn("文件下载失败", e);
        } finally {
            if (is != null) {
                IOUtils.closeQuietly(is);
            }
            if (os != null) {
                IOUtils.closeQuietly(os);
            }
        }
    }

    private ProxyStatement getProxyStatement(String type) {
        if (Objects.equals("1", type)) {
            return new ProxyStatement("proxy_statement_person.doc", "个人代付委托书.doc");
        }
        return new ProxyStatement("proxy_statement_company.doc", "企业代付委托书.doc");
    }

    @ApiOperation(value = "团险模块-提交线下支付材料")
    @PostMapping(value = "/{channel}/offline-pay")
    public OfflinePayVo offlinePay(@PathVariable("channel") String channel, @RequestBody OfflinePayDTO data) {
        log.info("线下支付材料提交:{},{}", JSON.toJSON(data), channel);
        data.setChannel(channel);

        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        String orderId = policyService.submitOfflinePay(channel, data);
        OfflinePayVo vo = new OfflinePayVo();
        vo.setOrderId(orderId);
        return vo;
    }

    @ApiOperation(value = "团险模块-获取批改信息")
    @GetMapping(value = "/group/order/info")
    public GroupOrderVo groupOrder(@RequestParam(value = "orderId", required = false) String orderId,
                                   @RequestParam(value = "endorId", required = false) String endorId) {
        log.info("获取批改信息:{},{}", orderId, endorId);
        return policyService.groupOrder(orderId, endorId);
    }


    @ApiOperation(value = "团险模块-撤销单")
    @PostMapping(value = "/{channel}/cancel/order")
    public GroupEndorResponse cancel(@RequestBody GroupRevokeVo req, @PathVariable("channel") String channel) {
        log.info("撤销单信息:{},{}", channel, req);
        if (!EnumChannel.checkChannel(channel)) {
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "渠道参数错误:" + channel);
        }
        return policyService.cancel(channel, req);
    }

}
