package com.cfpamf.ms.insur.weixin.web;

import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import org.apache.commons.lang3.StringUtils;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.pojo.dto.SmProductFormFieldCombDTO;
import com.cfpamf.ms.insur.admin.pojo.query.PosterGenQuery;
import com.cfpamf.ms.insur.admin.pojo.vo.*;
import com.cfpamf.ms.insur.admin.service.*;
import com.cfpamf.ms.insur.base.bean.Pageable;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.weixin.constant.WxConstant;
import com.cfpamf.ms.insur.weixin.pojo.dto.order.SmOrderMinInfo;
import com.cfpamf.ms.insur.weixin.pojo.query.WxProductQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.*;
import com.cfpamf.ms.insur.weixin.pojo.vo.product.ProductSelectionVO;
import com.cfpamf.ms.insur.weixin.service.WxDownloadService;
import com.cfpamf.ms.insur.weixin.service.WxHomeProductService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信API接口（小额保险）
 *
 * <AUTHOR>
 */
@Api(value = "首页/产品接口", tags = {"首页/产品接口"})
@RequestMapping(BaseConstants.WX_VERSION)
@RestController
@Slf4j
public class WxHomeProductController {

    /**
     * 微信产品查询service
     */
    @Autowired
    private WxHomeProductService wxHomeProductService;

    /**
     * 微信下载保险条款service
     */
    @Autowired
    private WxDownloadService wxDownloadService;

    /**
     * 字典service
     */
    @Autowired
    private DictionaryService dictService;

    /**
     * 保险公司职业service
     */
    @Autowired
    private OccupationService occupationService;

    @Autowired
    private BmsService bmsService;

    @Autowired
    private UserService userService;

    /**
     * 保险公司配置service
     */
    @Autowired
    private SmCmpySettingService cmpySettingService;

    /**
     * 保险公司service
     */
    @Autowired
    private CompanyService companyService;


    /**
     * 微信首页轮播图
     *
     * @return
     */
    @ApiOperation(value = "微信首页轮播图")
    @GetMapping("/home/<USER>")
    public List<SystemSettingVO> getWxHomeLbtImage(@RequestParam(required = false) String openId,
                                                   @RequestParam(required = false) String authorization) {
        String orgName = null;
        if (!StringUtils.isEmpty(openId) && !StringUtils.isEmpty(authorization)) {
            WxSessionVO session = wxHomeProductService.checkAuthority(openId, authorization);
            //modify by zhangjian 2020-11-11去掉办公室判断
            //if (orgFullName != null ) {
            if (wxHomeProductService.isAreaOffice(session.getHrOrgId())) {
                //2020-08-10 modify by zhangjian
                //orgName = orgFullName.substring(0, orgFullName.length() - 3);
                orgName = wxHomeProductService.getBranchOrgName(session.getOrgCode());
            } else {
                orgName = session.getRegionName();
            }
        }
        return wxHomeProductService.getWxHomeLbtImage(orgName);
    }

    @ApiOperation(value = "微信首页轮播图")
    @GetMapping("/home/<USER>")
    public List<SystemSettingVO> getWxHomeLbtImageWithoutOpenId(@RequestParam(required = false) String authorization) {
        String orgName = null;
        if (!StringUtils.isEmpty(authorization)) {
            UserDetailVO userDetail = bmsService.getUserDetailByToken(authorization);
            AuthUserVO authUserVO = userService.getAuthUserByUserId(userDetail.getJobNumber());
            if (StringUtils.isBlank(userDetail.getJobNumber()) || Objects.isNull(authUserVO)) {
                log.info("微信用户没有接口权限");
                return new ArrayList<>();
            }
            //WxSessionVO session = wxHomeProductService.checkAuthority(openId, authorization);
            //modify by zhangjian 2020-11-11去掉办公室判断
            //if (orgFullName != null ) {
            if (wxHomeProductService.isAreaOffice(String.valueOf(userDetail.getHrOrgId()))) {
                //2020-08-10 modify by zhangjian
                //orgName = orgFullName.substring(0, orgFullName.length() - 3);
                orgName = wxHomeProductService.getBranchOrgName(userDetail.getOrgCode());
            } else {
                orgName = authUserVO.getRegionName();
            }
        }
        return wxHomeProductService.getWxHomeLbtImage(orgName);
    }


    /**
     * 查询微信产品分类
     *
     * @return
     */
    @ApiOperation(value = "查询微信产品分类")
    @GetMapping("/product/category")
    public List<DictionaryVO> getWxProductCategory() {
        return dictService.getWxProductCategory();
    }

    /**
     * 查询微信产品列表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "查询微信产品列表", tags = "CORE")
    @GetMapping("/product")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "categoryId", value = "产品品种类Id", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "channel", value = "渠道", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "查询关键字", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "companyId", value = "保险公司Id", required = false, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "token", required = false, dataType = "string", paramType = "query")
    })
    public PageInfo<WxProductListVO> getWxProductsByPage(WxProductQuery query) {
        if (!StringUtils.isEmpty(query.getOpenId()) && !StringUtils.isEmpty(query.getAuthorization())) {
            WxSessionVO session = wxHomeProductService.checkAuthority(query.getOpenId(), query.getAuthorization());
            // 中和小保只销售中华联合产品  不加试点区域限制
            if (Objects.equals(session.getSystem(), SmConstants.CHANNEL_CIC)) {
                query.setSystem(session.getSystem());
                query.setChannel(session.getChannel());
            } else if (StringUtils.isEmpty(query.getChannel())) {
                query.setChannel(session.getChannel());
            }
            //String orgFullName = session.getOrganizationFullName();
            //if (orgFullName != null && orgFullName.endsWith("办公室")) {
            // 2021-01-14 去掉前端区域办公室看区域产品的权限，恢复只能看总部的权限
            // if(wxHomeProductService.isAreaOffice(session.getHrOrgId())){
            //2020-08-10 modify by zhangjian
            //query.setOrgName(orgFullName.substring(0, orgFullName.length() - 3));
            //     query.setOrgName(wxHomeProductService.getBranchOrgName(session.getOrgCode()));
            // } else {
            query.setOrgName(session.getRegionName());
            //  }
        }
        if (query.getCategoryId() == null) {
            query.setCategoryId(WxConstant.PRODUCT_CATEGORY_ALL);
        }
        if (Objects.equals(query.getCategoryId(), WxConstant.PRODUCT_CATEGORY_HOT)) {
            PageInfo<WxProductListVO> wxHotProductsByPage = wxHomeProductService.getWxHotProductsByPage(query);
            wxHomeProductService.resetH5Url(wxHotProductsByPage.getList());
            return wxHotProductsByPage;
        }
        PageInfo<WxProductListVO> wxProductsByPage = wxHomeProductService.getWxProductsByPage(query);
        wxHomeProductService.resetH5Url(wxProductsByPage.getList());
        return wxProductsByPage;
    }

    /**
     * 查询商品详情页面
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "微信端-查询商品详情页面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "planIds", value = "需要展示的计划id 多个用,隔开", example = "8,9", required = true, dataType = "string", paramType = "query"),

    })
    @GetMapping("/product/{productId}")
    public WxProductDetailVO getWxProductDetailById(@PathVariable int productId,
                                                    @RequestParam(value = "planIds", required = false) String planIds) {
        return wxHomeProductService.getWxProductDetailByIdAndRegionName(productId,
                wxHomeProductService.checkAuthority().getRegionName(), planIds);
    }

    /**
     * 查询微信商品详情页面
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询微信长期险商品详情页面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "planIds", value = "需要展示的计划id 多个用,隔开", example = "8,9", required = true, dataType = "string", paramType = "query"),

    })
    @GetMapping("/product/long_insurance/{productId}")
    public WxProductDetailVO getWxLongInsuranceProductDetailById(@PathVariable int productId,
                                                                 @RequestParam(value = "planIds", required = false) String planIds) {
        return wxHomeProductService.getWxLongInsuranceProductDetailByIdAndRegionName(productId,
                wxHomeProductService.checkAuthority().getRegionName(), planIds);
    }

    /**
     * 查询微信产品海报详情
     *
     * @param productId
     * @param query
     * @return
     */
    @ApiOperation(value = "查询微信产品海报详情")
    @PostMapping("/product/{productId}/poster")
    public WxProductPosterBase64VO getWxProductPoster(@PathVariable int productId, @RequestBody PosterGenQuery query) {
        query.setProductId(productId);
        return wxHomeProductService.getWxProductPoster(query);
    }

    /**
     * 微信查询产品所有计划
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "微信查询产品所有计划", tags = "CORE")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/product/{productId}/plan")
    public List<SmPlanVO> getWxProductPlans(@PathVariable int productId) {
        return wxHomeProductService.getWxProductPlans(productId, wxHomeProductService.checkAuthority().getRegionName());
    }

    /**
     * 查询微信商品保障计划详情
     *
     * @param planId
     * @return
     */
    @ApiOperation(value = "查询微信商品保障计划详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "long", paramType = "path")
    })
    @GetMapping("/product/plan/{planId}/coverage")
    public List<WxPlanCoverageVO> getWxProductPlanCoverages(@PathVariable int planId) {
        return wxHomeProductService.getWxProductPlanCoverages(planId);
    }

    /**
     * 微信查询产品所有计划提成
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "微信查询产品所有计划提成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "planIds", value = "需要展示的计划id 多个用,隔开", example = "8,9", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "authorization", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/product/{productId}/commission")
    public SmPlanCmsVO getWxProductCms(@PathVariable int productId,
                                       @RequestParam(value = "planIds", required = false)
                                       String planIds,
                                       @RequestParam(required = false) String createType,
                                       @RequestParam(required = false) String openId, @RequestParam String authorization) {
        return wxHomeProductService.getWxProductCms(productId, openId, authorization, planIds);
    }

    /**
     * 微信查询产品所有计划选项
     *
     * @param planId
     * @return
     */
    @ApiOperation(value = "微信查询产品所有计划选项")
    @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/product/plan/{planId}/price/factor")
    public SmPriceFactorOptionalsVO getWxFactorOptionals(@PathVariable int planId) {
        return wxHomeProductService.getWxFactorOptionalsVo(planId);
    }

    /**
     * 微信查询保险某个计划选项所有价格
     *
     * @param planId
     * @return
     */
    @ApiOperation(value = "微信查询保险某个计划选项所有价格")
    @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/product/plan/{planId}/price")
    public List<WxFactorPriceVO> getAllPlanFactorPrices(@PathVariable int planId) {
        return wxHomeProductService.getAllPlanFactorPrices(planId).stream().map(WxFactorPriceVO::new).collect(Collectors.toList());
    }

    /**
     * 查询价格因素IdName对应关系
     *
     * @return
     */
    @ApiOperation(value = "查询价格因素IdName对应关系")
    @GetMapping("/price/factors")
    public Map<String, String> getWxPriceFactors() {
        List<DictionaryVO> vos = dictService.getDictionarysByPage(DictionaryVO.TYPE_PRICE_FACTOR, null, null, true).getList();
        Map<String, String> treeMap = new LinkedHashMap<>();
        vos.forEach(v -> treeMap.put(v.getCode(), v.getName()));
        return treeMap;
    }

    /**
     * 查询保险页面基本的保险公司配置参数
     *
     * @param planId
     * @return
     */
    @ApiOperation(value = "查询保险页面基本的保险公司配置参数")
    @ApiImplicitParam(name = "planId", value = "计划ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/product/plan/{planId}/setting")
    public WxFormFieldsVO getWxFormFieldSettings(@PathVariable int planId) {
        return wxHomeProductService.getWxFormFieldSettings(planId);
    }

    /**
     * 查询产品地区列表
     *
     * @param productId
     * @param parentId
     * @return
     * @deprecated 因为效率问题已废弃
     */
    @ApiOperation(value = "查询产品地区列表", hidden = true)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "保险公司ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "parentId", value = "父节点Id", required = false, dataType = "string", paramType = "query")
    })
    @GetMapping("/product/{productId}/setting/region")
    @Deprecated
    public List<SmCompanyRegionVO> getWxRegionList(@PathVariable int productId, @RequestParam(required = false) String parentId) {
        int companyId = getProductCompanyId(productId);
        return cmpySettingService.getRegionList(companyId, parentId);
    }

    /**
     * 查询产品职业列表
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询产品职业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    })
    @GetMapping("/product/{productId}/setting/occuplCategory")
    public List<WxTreeVO> getOccupationList(@PathVariable int productId, @RequestParam(value = "policyNo", required = false) String policyNo) {
        log.info("开始查询产品&保单的职业表:{},{}", productId, policyNo);
        int companyId = getProductCompanyId(productId);
        return occupationService.getOccupationList(companyId, productId, policyNo);
    }

    /**
     * 查询产品职业列表top排行
     *
     * @param productId
     * @param size
     * @return
     */
    @ApiOperation(value = "查询产品职业列表top排行")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "size", value = "返回数量", required = true, dataType = "long", paramType = "query")
    })
    @GetMapping("/product/{productId}/setting/occuplCategory/hot")
    public List<WxTreeVO> getOccupationHotList(@PathVariable int productId,  @RequestParam int size) {
        return wxHomeProductService.getOccupationHotList(productId, size);
    }

    /**
     * 查询投保信息录入表单配置
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询投保信息录入表单配置")
    @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/product/{productId}/insureForm")
    public List<SmProductFormFieldCombDTO> getWxProductFormFields(@PathVariable int productId) {
        return wxHomeProductService.getProductFormFields(productId);
    }

    /**
     * 查询产品可续保产品
     *
     * @param productId
     * @return
     */
    @ApiOperation(value = "查询产品可续保产品")
    @ApiImplicitParam(name = "productId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/product/{productId}/renews")
    public List<WxProductRenewVO> getWxProductRenews(@PathVariable int productId,
                                                     @RequestParam(required = false) String openId,
                                                     @RequestParam(required = false) String authorization) {
        return wxHomeProductService.getWxProductRenews(productId, openId, authorization);
    }

    /**
     * 查询保险公司职业列表
     *
     * @param companyId
     * @return
     */
    @ApiOperation(value = "查询保险公司职业列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "产品ID", required = true, dataType = "long", paramType = "path")
    })
    @GetMapping("/company/{companyId}/setting/occuplCategory")
    public List<WxTreeVO> getCompanyOccupationList(@PathVariable int companyId) {
        return occupationService.getCompanyOccupationList(companyId);
    }

    /**
     * 查询保险公司地区列表
     *
     * @param companyId
     * @return
     */
    @ApiOperation(value = "查询保险公司地区列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "保险公司ID", required = true, dataType = "long", paramType = "path")
    })
    @GetMapping("/company/{companyId}/setting/region")
    public List<WxTreeVO> getCompanyRegionList(@PathVariable int companyId, @RequestParam(value = "productId", required = false) Integer productId) {
        return cmpySettingService.getCompanyRegionList(companyId, productId);
    }

    /**
     * 保险条款下载
     *
     * @param clauseId
     * @param resp
     * @throws IOException
     * @throws URISyntaxException
     */
    @ApiOperation(value = "保险条款下载")
    @ApiImplicitParam(name = "id", value = "条款ID", required = true, dataType = "long", paramType = "path")
    @GetMapping("/download/clause/{clauseId}")
    public void downloadClause(@PathVariable int clauseId, HttpServletResponse resp) {
        wxDownloadService.downloadClause(clauseId, resp);
    }

    /**
     * 保险公司报案电话
     *
     * @return
     */
    @ApiOperation(value = "保险公司报案电话")
    @GetMapping("/company/mobile")
    public List<CompanyVO> getCompanyList() {
        return companyService
                .getCompanysByPage(null, null)
                .getList();
    }

    /**
     * 保险公司列表(按产品个数倒序排序)
     *
     * @return
     */
    @ApiOperation(value = "保险公司列表(按产品个数倒序排序)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "第几页", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页数量", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/company/list")
    public PageInfo<CompanyVO> getCompanysByPageOrderByProductCount(Pageable pageable, @RequestParam(value = "displayFlag", required = false) Integer displayFlag) {
        return companyService.getCompanysByPageOrderByProductCount(null, pageable, displayFlag);
    }

    /**
     * 保险公司详情
     *
     * @return
     */
    @ApiOperation(value = "保险公司详情")
    @GetMapping("/company/{companyId}")
    @ApiImplicitParam(name = "companyId", value = "保险公司Id", required = true, dataType = "int", paramType = "path")
    public CompanyVO getCompanyList(@PathVariable int companyId) {
        return companyService.getCompanyById(companyId);
    }


    /**
     * 获取保险公司产品Id
     *
     * @param productId
     * @return
     */
    private int getProductCompanyId(int productId) {
        return wxHomeProductService.getProductCompanyId(productId);
    }

    /**
     * @return
     */
    @ApiOperation(value = "微信订单保险查询列表产品选择数据接口")
    @GetMapping("/product/selectList")
    public PageInfo<ProductSelectionVO> selectProduct(Pageable pageable) {
        if (pageable.getSize() > 1000) {
            pageable.setSize(1000);
        }
        return wxHomeProductService.productSelectionPage(pageable);
    }

    /**
     * 查询保险公司银行区域列表
     *
     * @param channel
     * @return
     */
    @ApiOperation(value = "查询保险公司银行区域列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "string", paramType = "path")
    })
    @GetMapping("/company/{channel}/setting/bankRegion")
    public List<WxTreeVO> getBankRegion(@PathVariable String channel) {
        return cmpySettingService.getCompanyRegionList(channel);
    }

    /**
     * 查询保险公司银行区域列表
     *
     * @param channel
     * @return
     */
    @ApiOperation(value = "查询保险公司银行支行地址列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "channel", value = "保司渠道编码,例如:za", required = true, dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "bankCode", value = "银行编码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "bankAreaCode", value = "银行区域编码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/company/{channel}/setting/getBankLocationList")
    public List<WxBankLocationVO> getBankLocationList(@PathVariable String channel, @RequestParam(value = "bankCode", required = true) String bankCode, @RequestParam(value = "bankAreaCode", required = true) String bankAreaCode) {
        return cmpySettingService.getCompanyBankLocationList(channel, bankCode, bankAreaCode);
    }

    /**
     * 查询产品职业列表
     *
     * @param orderId
     * @return
     */
    @ApiOperation(value = "查询产品职业列表根据根据上一个订单号范围内")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "产品ID", required = true, dataType = "long", paramType = "path"),
            @ApiImplicitParam(name = "orderId", value = "订单编码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/product/setting/getOccupationByOrderId")
    public List<WxTreeVO> getOccupationByOrderId(@RequestParam(value = "orderId") String orderId) {
        SmOrderMinInfo smOrderMinInfo = occupationService.getSmOrderMinInfo(orderId);
        Integer productId = smOrderMinInfo.getProductId();
        int companyId = getProductCompanyId(productId);
        return occupationService.getOccupationListByOrderId(companyId, productId, orderId);
    }
}
