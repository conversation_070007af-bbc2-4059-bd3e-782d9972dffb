package com.cfpamf.ms.insur.weixin.web;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.common.ms.exception.MSBizNormalException;
import com.cfpamf.ms.bms.facade.vo.UserDetailVO;
import com.cfpamf.ms.insur.admin.constant.SmConstants;
import com.cfpamf.ms.insur.admin.external.whale.api.WhaleApiService;
import com.cfpamf.ms.insur.admin.external.whale.client.WhaleOrderClient;
import com.cfpamf.ms.insur.admin.external.whale.model.GetAppletTokenInput;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.web.AbstractController;
import com.cfpamf.ms.insur.base.config.WechatConfig;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.BmsService;
import com.cfpamf.ms.insur.base.service.SecurityService;
import com.cfpamf.ms.insur.base.util.Base64Util;
import com.cfpamf.ms.insur.base.util.ThreadUserUtil;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.service.WxCcUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.exception.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * wx微信页面跳转接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Api(value = "页面跳转接口", tags = {"页面跳转接口"})
@RequestMapping(BaseConstants.WX_VERSION)
@RestController
public class WxPageIndexController extends AbstractController {

    /**
     * 微信跳转公共参数后缀
     */
    private final static String COMMON_REDIRECT_PARAMS_URL = "&openId=%s&authorization=%s&channel=%s&sid=%s&userId=%s";

    /**
     * 微信首页登陆参数/跳转个人二维码
     */
    private final static String LOGIN_PARAM_ACCOUNT_QR = "accountQR";

    /**
     * 微信首页登陆参数/跳转微信产品首页
     */
    private final static String LOGIN_PARAM_INDEX = "index";

    /**
     * 微信配置
     */
    @Autowired
    private WechatConfig wxConfig;

    /**
     * 微信用户中心service
     */
    @Autowired
    private WxCcUserService ccUserService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private WhaleApiService whaleApiService;


    /**
     * 微信授权跳转
     * 默认首页，如果传入rdl则跳转到rdl指向的页面
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "小额保险首页授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "rdl", value = "跳转url", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "sid", value = "推荐员Id", required = false, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/login")
    public void getSmWxAuth(@RequestParam String code,
                            @RequestParam(required = false) String state,
                            @RequestParam(required = false) String sid,
                            @RequestParam(required = false) String rdl,
                            HttpServletResponse response) throws IOException {
        log.info("微信小额保险回调参数code={},state={},sid={},rdl={}", code, state, sid, rdl);
        // not format null
        if (StringUtils.isEmpty(state)) {
            state = "";
        }
        WxSessionVO session = null;
        try {
            session = ccUserService.createWxSession(code, state, sid);
        } catch (BizException be) {

            log.warn("生成session失败", be);
            String message = be.getMessage();
            //如果是bms调用出错直接重定向
            if (org.apache.commons.lang3.StringUtils.startsWith(message, BmsService.ERROR_PREFIX)) {
                response.sendRedirect(String.format(wxConfig.getLoginErrorUrl(),
                        URLEncoder.encode(be.getMessage(), StandardCharsets.UTF_8.name())));
            } else {
                response.sendRedirect(String.format(wxConfig.getLoginErrorUrl(),
                        URLEncoder.encode("登录失败:" + be.getMessage(), StandardCharsets.UTF_8.name())));
            }
            return;
        }

        // 默认跳转首页
        String redirectUrl = wxConfig.getFrontIndexUrl();
        if (!StringUtils.isEmpty(rdl) && Base64Util.isEncrypt(rdl)) {
            // 跳往重定向的页面加上公共参数
            redirectUrl = new String(Base64Util.decryptBASE64(rdl), StandardCharsets.UTF_8.name());
            if (!redirectUrl.contains("?")) {
                redirectUrl += "?";
            }
            redirectUrl = redirectUrl + COMMON_REDIRECT_PARAMS_URL + "&_t=" + System.currentTimeMillis();
            response.sendRedirect(String.format(redirectUrl, session.getWxOpenId(), session.getAuthorization(), state, session.getId(), session.getUserId()));
            return;
        }
        // 默认跳转首页加渠道参数
        redirectUrl += "&channel=%s&_t=" + System.currentTimeMillis();
        response.sendRedirect(String.format(redirectUrl, session.getWxOpenId(), session.getAuthorization(), state));
    }

    /**
     * 微信分享二维码页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信分享二维码页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/accountQR")
    public void getWxAccountAuth(@RequestParam String code,
                                 @RequestParam(required = false) String state,
                                 HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontAccountQRUrl(), session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_ACCOUNT_QR));
        }
    }

    /**
     * 微信续保页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信续保页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/renew")
    public void getWxRenewAuth(@RequestParam String code,
                               @RequestParam(required = false) String state,
                               HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontRenewUrl(), session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信续保页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信续保页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claimProgress")
    public void getWxClaimProgressAuth(@RequestParam String code,
                                       @RequestParam(required = false) String state,
                                       @RequestParam(required = false) String claimState,
                                       HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (StringUtils.isEmpty(claimState)) {
            claimState = "";
        }
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimProcessUrl(), session.getWxOpenId(), session.getAuthorization()) + "&claimState=" + claimState);
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信理赔详情页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔详情页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "claimId", value = "理赔Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claimDetail")
    public void getWxClaimDetailAuth(@RequestParam String code,
                                     @RequestParam String claimId,
                                     @RequestParam(required = false) String type,
                                     @RequestParam(required = false) String role,
                                     @RequestParam(required = false) String state,
                                     HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimDetailUrl(), session.getWxOpenId(), session.getAuthorization(), claimId, type, role));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    @ApiOperation(value = "微信理赔回复详情页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "claimId", value = "理赔Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "formType", value = "类型", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/followup/detail")
    public void getWxClaimDetailAuth(@RequestParam String code,
                                     @RequestParam String claimId,
                                     @RequestParam(required = false) String formType,
                                     @RequestParam(required = false) String state,
                                     HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontFollowUpDetailUrl(), claimId, formType, session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }


    @ApiOperation(value = "微信理赔快讯申请详情页面")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "claimId", value = "理赔Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claimPostApplyDetail")
    public void getClaimPostApplyDetail(@RequestParam String code, @RequestParam String claimId,
                                        @RequestParam(required = false) String type,
                                        @RequestParam(required = false) String role,
                                        @RequestParam(required = false) String state,
                                        HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            log.info("微信理赔快讯申请详情页面跳转-{}", wxConfig.getFrontClaimPostApplyUrl());
            response.sendRedirect(String.format(wxConfig.getFrontClaimPostApplyUrl(), session.getWxOpenId(), session.getAuthorization(), claimId, type, role));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信续保详情页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信续保详情页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "orderId", value = "订单Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/renewDetail")
    public void getWxRenewalDetailAuth(@RequestParam String code,
                                     @RequestParam String orderId,
                                     @RequestParam(required = false) String type,
                                     @RequestParam(required = false) String role,
                                     @RequestParam(required = false) String state,
                                     HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontRenewDetailUrl(), session.getWxOpenId(), session.getAuthorization(), orderId, type, role));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }


    /**
     * 微信理赔注销详情页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔注销详情页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "claimCancelReportId", value = "注销理赔流程Id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claimCancelDetail")
    public void getWxClaimCancelDetailAuth(@RequestParam String code,
                                           @RequestParam String claimCancelReportId,
                                           @RequestParam(required = false) String type,
                                           @RequestParam(required = false) String role,
                                           @RequestParam(required = false) String state,
                                           HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            String formatUrl = String.format(wxConfig.getFrontClaimCancelDetailUrl(), session.getWxOpenId(), session.getAuthorization(), claimCancelReportId, type, role);
            log.info("redirect url :{}", formatUrl);
            response.sendRedirect(formatUrl);
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 续期保单详情页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "续期保单详情页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "renewalTermId", value = "续期id", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "类型", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/renewalTermPolicyDetail")
    public void getWxRenewalTermPolicyDetailAuth(@RequestParam String code,
                                           @RequestParam String renewalTermId,
                                           @RequestParam(required = false) String type,
                                           @RequestParam(required = false) String role,
                                           @RequestParam(required = false) String state,
                                           HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            String formatUrl = String.format(wxConfig.getFrontRenewalTermPolicyDetailUrl(), session.getWxOpenId(), session.getAuthorization(), renewalTermId, type, role);
            log.info("redirect url :{}", formatUrl);
            response.sendRedirect(formatUrl);
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信理赔催办授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔催办授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claimTodo")
    public void getWxClaimTodoAuth(@RequestParam String code,
                                   @RequestParam(required = false) String state,
                                   HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimTodoUrl(), session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信理赔资料审核授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔资料审核授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claimApproval")
    public void getWxClaimApprovalAuth(@RequestParam String code,
                                       @RequestParam(required = false) String state,
                                       HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimApprovalUrl(), session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 退保资料详情
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔资料审核授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/cancel/{cancelId}/{role}/{insuredId}")
    public void getCancelDetailAuth(@RequestParam String code,
                                    @RequestParam(required = false) String state,
                                    @PathVariable("role") String role,
                                    @PathVariable("insuredId") Integer insuredId,
                                    @PathVariable("cancelId") Integer cancelId,
                                    HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontCancelDetail(), role, insuredId + "", cancelId + "", session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信分享重定向
     *
     * @param openId
     * @return
     */
    @ApiOperation(value = "微信分享重定向")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "rdl", value = "重定向URL", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/share")
    public void redirectWxIndexShare(@RequestParam(required = false) String openId,
                                     @RequestParam String rdl,
                                     HttpServletResponse response) throws IOException {
        log.info("微信分享参数 openId={}, rdl={}", openId, rdl);
        if (!rdl.contains("?")) {
            rdl += "?";
        }
        rdl += "&from=share";
        rdl = Base64Util.encryptBASE64(rdl.getBytes(StandardCharsets.UTF_8));
        WxSessionVO session = ccUserService.getWxSession(openId);
        if (session == null) {
            throw new BizException(ExcptEnum.INVALID_TOKEN_501019);
        }
        String channel = "";
        if (!StringUtils.isEmpty(session.getChannel())) {
            channel = session.getChannel();
        }
        // /index/login
        Integer sid = session.getId();
        if (!session.isBindEmployee()
                && org.apache.commons.lang3.StringUtils.isNotBlank(session.getUserId())) {// 如果当前用户不是员工 并且有工号信息
            sid = Optional.ofNullable(ccUserService.getUserByUserId(session.getUserId()))
                    .map(AuthUserVO::getId).orElse(null);

        }
        String backIndexUrl = String.format(wxConfig.getBackIndexUrl(), rdl, sid);
        String redirectUrl = String.format(wxConfig.getBackJumpUrl(), URLEncoder.encode(backIndexUrl, StandardCharsets.UTF_8.name()), channel);
        log.info("微信分享重定向链接={}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    @ApiOperation(value = "客户告知书页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/customerNoticeSign")
    public void getCustNotiyAuth(@RequestParam String code,
                                 @RequestParam(required = false) String state,
                                 HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontCustNotifyUrl(), session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    @ApiOperation(value = "电子回访流程页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/returnVisitProcess/{time}")
    public void getReturnVisitProcessAuth(@RequestParam String code,
                                          @RequestParam(required = false) String state,
                                          @PathVariable("time") Long time,
                                          HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontReturnVisitProcessUrl(), time, session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信通用授权跳转接口
     *
     * @param code     微信授权code
     * @param state    授权成功后跳转到前端页面
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信通用授权跳转接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/authorize_redirect")
    public void wxPageRedirect(@RequestParam String code,
                               @RequestParam(required = false) String rdl,
                               @RequestParam(required = false) String state,
                               HttpServletResponse response) throws IOException {
        log.info("[微信授权跳转逻辑]-Code:[{}],Rdl:[{}],State:[{}]", code, rdl, state);
        WxSessionVO session = null;
        try {
            session = ccUserService.createWxSession(code, null);
        } catch (Exception e) {
            log.warn("微信授权失败", e);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("text/html;charset=utf-8");
            PrintWriter writer = response.getWriter();
            writer.write("非法登录");
            return;
        }
        // 已经绑定代理人或者内部员工
        if (!session.isBindWeixin()) {
            // state为重定向到前端的页面URL
            if (!StringUtils.isEmpty(rdl) && Base64Util.isEncrypt(rdl)) {
                // 跳往重定向的页面加上公共参数
                String redirectUrl = new String(Base64Util.decryptBASE64(rdl), StandardCharsets.UTF_8.name());
                //个人中心自动判断是否
                log.info("{}:{}", state, rdl);
                log.info("{}", session);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(state)
                        && Objects.equals("autoUserHome", state)
                        && Objects.equals(session.getUserType(), SmConstants.USER_TYPE_AGENT)) {
                    redirectUrl = redirectUrl.replace("/userHome", "/agentHome");
                }else {// 关闭试点全国开放。上线无问题后可以移除此处代码
                    redirectUrl = redirectUrl.replace("/userHome", "/assistant/salesAssistantHome");
                }

                if (!redirectUrl.contains("?")) {
                    redirectUrl += "?";
                }
                redirectUrl = redirectUrl + COMMON_REDIRECT_PARAMS_URL + "&_t=" + System.currentTimeMillis();
                response.sendRedirect(String.format(redirectUrl, session.getWxOpenId(), session.getAuthorization(), "", session.getId(), session.getUserId()));
            } else {
                response.sendRedirect(String.format(wxConfig.getFrontIndexUrl(), session.getWxOpenId(), session.getAuthorization()));
            }
        } else {
            String publicKey = "";
            try {
                publicKey = securityService.getPublicKey();
                log.info("publicKey: {}", publicKey);
                //将publickey base64加密避免urlecode出现错误
                publicKey = Base64Util.encryptBASE64(publicKey.getBytes(StandardCharsets.UTF_8.name()));
                log.info("publicKey base64: {}", publicKey);
            } catch (NoSuchAlgorithmException e) {
                throw new RuntimeException(e);
            }
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), publicKey, "assistant/salesAssistantHome"));//此处默认跳转保险助手
        }
    }


    /**
     * 微信通用授权跳转接口
     *
     * @param code     微信授权code
     * @param state    授权成功后跳转到前端页面
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信通用授权跳转接口-无需登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/common_redirect")
    public void wxNoUSerPageRedirect(@RequestParam String code,
                                     @RequestParam(required = false) String rdl,
                                     @RequestParam(required = false) String state,
                                     HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, null);
        // state为重定向到前端的页面URL
        // state为重定向到前端的页面URL
        if (!StringUtils.isEmpty(rdl) && Base64Util.isEncrypt(rdl)) {
            // 跳往重定向的页面加上公共参数
            String redirectUrl = new String(Base64Util.decryptBASE64(rdl), StandardCharsets.UTF_8.name());
            if (!redirectUrl.contains("?")) {
                redirectUrl += "?";
            }
            redirectUrl = redirectUrl + COMMON_REDIRECT_PARAMS_URL + "&_t=" + System.currentTimeMillis();
            response.sendRedirect(String.format(redirectUrl, session.getWxOpenId(), session.getAuthorization(), "", session.getId(), session.getUserId()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontIndexUrl(), session.getWxOpenId(), session.getAuthorization()));
        }
    }

    /**
     * 微信通用授权跳转接口
     *
     * @param code     微信授权code
     * @param state    授权成功后跳转到前端页面
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信第三方回调地址-无需登录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码-bizcode", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/third_redirect")
    public void wxThirdPageRedirect(@RequestParam String code,
                                    @RequestParam(required = false) String rdl,
                                    @RequestParam(required = false) String state,
                                    HttpServletResponse response) throws IOException {

        log.info("第三方微信授权重定向{} {}", rdl, state);
        WxSessionVO session = ccUserService.createWxSessionByBizCode(code, state);
        // state为重定向到前端的页面URL
        // state为重定向到前端的页面URL
        if (!StringUtils.isEmpty(rdl) && Base64Util.isEncrypt(rdl)) {
            // 跳往重定向的页面加上公共参数
            String redirectUrl = new String(Base64Util.decryptBASE64(rdl), StandardCharsets.UTF_8.name());
            if (!redirectUrl.contains("?")) {
                redirectUrl += "?";
            }
            redirectUrl = redirectUrl + COMMON_REDIRECT_PARAMS_URL + "&_t=" + System.currentTimeMillis();
            response.sendRedirect(String.format(redirectUrl, session.getWxOpenId(), session.getAuthorization(), "", session.getId(), session.getUserId()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontIndexUrl(), session.getWxOpenId(), session.getAuthorization()));
        }
    }

    @ApiOperation(value = "微信培训详情页面授权")
    @GetMapping("/index/trainingDetail")
    public void getWxTrainingDetailAuth(@RequestParam String code,
                                     @RequestParam String trainingId,
                                     @RequestParam(required = false) String state,
                                     HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontTrainingDetailUrl(), trainingId, session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    @ApiOperation(value = "微信培训审核页面授权")
    @GetMapping("/index/trainingAudit")
    public void getWxTrainingAuditAuth(@RequestParam String code,
                                     @RequestParam String trainingId,
                                     @RequestParam(required = false) String state,
                                     HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontTrainingAuditUrl(), trainingId, session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    @ApiOperation(value = "微信消息理赔模板跳转授权")
    @GetMapping("/index/claim/template")
    public void getWxClaimTemplateRedirect(@RequestParam String code,
                                       @RequestParam Integer claimId,
                                       @RequestParam(required = false) String state,
                                       HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimTemplateUrl(), claimId, session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信理赔催办授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔催办授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claim/home")
    public void getWxClaimHomeAuth(@RequestParam String code,
                                   @RequestParam(required = false) String state,
                                   HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimHome(), session.getWxOpenId(), session.getAuthorization()));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 给小程序过来的客户经理授权
     *
     * @param userId
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "小程序过来的客户经理授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "员工工号", required = true, dataType = "string", paramType = "query"),
    })
    @GetMapping("/index/getSmWxToken")
    public String getSmWxToken(
                            @RequestParam(required = true) String userId) throws IOException {
        log.info("小程序公众号token打通 userId={}", userId);
        String token = ccUserService.createWxSession(userId);
        return token;
    }

    /**
     * 获取小程序token
     *
     * @param userId
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "获取小程序token")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "appId", value = "appId", required = true, dataType = "string", paramType = "query"),
    })
    @GetMapping("/index/getAppletToken")
    public String getAppletToken(@RequestParam(required = true) String appId) throws IOException {
        ThreadUserUtil.USER_DETAIL_TL.remove();
        UserDetailVO userDetailVO = ThreadUserUtil.USER_DETAIL_TL.get();
        //人员状态 1.待入职 2.试用 3.正式 4.调出 5.待调入 6.退休 8.离职 12.非正式
        if(Objects.equals(8,userDetailVO.getEmployeeStatus())){
            log.warn("小程序和公众号token互通:员工已离职 authUserVO= {}", JSON.toJSONString(userDetailVO));
            throw new MSBizNormalException(ExcptEnum.PARAMS_ERROR.getCode(), "员工已离职");
        }
        log.info("小程序公众号token打通 userId={}", appId);
        GetAppletTokenInput getAppletTokenInput = new GetAppletTokenInput();
        getAppletTokenInput.setAppId(appId);
        getAppletTokenInput.setReferrerWno(userDetailVO.getJobNumber());
        //根据token获取
        return whaleApiService.getAppletToken(getAppletTokenInput);
    }

    /**
     * 微信理赔催办授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信理赔催办授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/claim/finish/report")
    public void getWxClaimFinishReportAuth(@RequestParam String code,
                                           @RequestParam(required = false) String state,
                                           @RequestParam Integer claimId,
                                           HttpServletResponse response) throws IOException {
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getFrontClaimFinishReportUrl(), session.getWxOpenId(), session.getAuthorization(), claimId));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

    /**
     * 微信素材详情页面授权
     *
     * @param code
     * @param state
     * @param response
     * @throws WxErrorException
     * @throws IOException
     */
    @ApiOperation(value = "微信素材详情页面授权")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "微信code", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "state", value = "状态码", required = true, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "materialId", value = "订单Id", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/index/materialDetail")
    public void getWxMaterialDetailAuth(@RequestParam String code,
                                       @RequestParam String materialId,
                                       @RequestParam(required = false) String state,
                                       HttpServletResponse response) throws IOException {
        log.info("code={}", code);
        log.info("materialId={}", materialId);
        WxSessionVO session = ccUserService.createWxSession(code, state);
        if (!session.isBindWeixin()) {
            response.sendRedirect(String.format(wxConfig.getMaterialDetailUrl(), session.getWxOpenId(), session.getAuthorization(), materialId));
        } else {
            response.sendRedirect(String.format(wxConfig.getFrontLoginUrl(), session.getWxOpenId(), session.getAuthorization(), LOGIN_PARAM_INDEX));
        }
    }

}
