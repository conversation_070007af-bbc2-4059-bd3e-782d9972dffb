package com.cfpamf.ms.insur.weixin.web;

import com.cfpamf.ms.bms.facade.vo.FdSimpleUserVO;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.admin.pojo.dto.QrcodeDTO;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserNameVO;
import com.cfpamf.ms.insur.admin.pojo.vo.AuthUserVO;
import com.cfpamf.ms.insur.admin.service.SystemFileService;
import com.cfpamf.ms.insur.admin.service.UserService;
import com.cfpamf.ms.insur.admin.service.login.LoginWatchDog;
import com.cfpamf.ms.insur.admin.web.AbstractController;
import com.cfpamf.ms.insur.base.bean.CommonResult;
import com.cfpamf.ms.insur.base.constant.BaseConstants;
import com.cfpamf.ms.insur.base.exception.BizException;
import com.cfpamf.ms.insur.base.exception.ExcptEnum;
import com.cfpamf.ms.insur.base.service.SecurityService;
import com.cfpamf.ms.insur.base.util.DataMaskUtil;
import com.cfpamf.ms.insur.base.util.HttpRequestUtil;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxBindDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserEmailDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.WxUserSettingDTO;
import com.cfpamf.ms.insur.weixin.pojo.dto.user.WxUserUnbindDTO;
import com.cfpamf.ms.insur.weixin.pojo.query.settlement.WxUserCommissionDetailQuery;
import com.cfpamf.ms.insur.weixin.pojo.vo.LoginUserInfoVo;
import com.cfpamf.ms.insur.weixin.pojo.vo.WxSessionVO;
import com.cfpamf.ms.insur.weixin.pojo.vo.settlement.WxUserCommissionDetailVo;
import com.cfpamf.ms.insur.weixin.service.WxCcUserService;
import com.cfpamf.ms.insur.weixin.service.settlement.WxSettlementCostService;
import com.cfpamf.ms.insur.weixin.util.IpUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * wx微信用户中心/个人中心接口
 *
 * <AUTHOR>
 **/
@Slf4j
@Api(value = "用户中心/个人中心接口", tags = {"用户中心/个人中心接口"})
@RequestMapping(BaseConstants.WX_VERSION + "/center")
@RestController
public class WxCcUserController extends AbstractController {

    /**
     * 微信用户中心service
     */
    @Autowired
    private WxCcUserService ccUserService;

    /**
     * SecurityService
     */
    @Autowired
    private SecurityService securityService;

    @Autowired
    UserService userService;

    @Autowired
    private SystemFileService fileService;

    @Autowired
    private LoginWatchDog watchDog;

    @Autowired
    private WxSettlementCostService wxSettlementCostService;

    /**
     * 获取微信用户信息
     *
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "获取微信用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "authorization", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user")
    public WxSessionVO getBindWxUser(@RequestParam(required = false) String openId, @RequestParam String authorization) {
        return ccUserService.getBindWxUser(openId, authorization);
    }

    /**
     * 修改微信用户配置信息
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改微信用户配置信息")
    @PostMapping("/user/setting")
    public void updateWxUserSetting(@RequestBody WxUserSettingDTO dto) {
        ccUserService.updateWxUserSetting(dto);
    }

    /**
     * 修改微信用户邮箱
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "修改微信用户邮箱")
    @PutMapping("/user/email")
    public void updateWxUserEmail(@RequestBody WxUserEmailDTO dto) {
        ccUserService.updateWxUserEmail(dto);
    }

    /**
     * 微信用户和信贷系统用户绑定
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "微信用户和信贷系统用户绑定")
    @PostMapping("/user/bind")
    public WxSessionVO bindWxUser(@RequestBody WxBindDTO dto, HttpServletRequest request, HttpServletResponse response) {
        String ip = IpUtil.getIpAddr(request);
        String loginId = securityService.decryptCipher(dto.getLoginID());
        String pwd = securityService.decryptCipher(dto.getLoginPwd());

        dto.setIp(ip);
        dto.setLoginID(loginId);
        dto.setLoginPwd(pwd);

        request.getSession().getId();
        log.info("[微信端用户登录]-Ip:[{}],OpenId:[{}],loginId:[{}]", ip, dto.getWxOpenId(), loginId);
        if (!watchDog.check(ip, loginId)) {
            throw new BizException(ExcptEnum.EVIL_ORDER_ERROR_801002.getCode(), "当前账户已被冻结，请五分钟后再试");
        }
        try {
            return ccUserService.bindUserWxInfo(dto);
        } finally {
            watchDog.visit(ip, loginId);
        }
    }


    /**
     * 微信解绑用户V2
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "微信解绑用户V2")
    @PostMapping("/user/unbind/v2")
    public void unbindWxUser(@RequestBody WxUserUnbindDTO dto) {
        String loginId = securityService.decryptCipher(dto.getLoginID());
        String pwd = securityService.decryptCipher(dto.getLoginPwd());
        dto.setLoginID(loginId);
        dto.setLoginPwd(pwd);
        dto.setWxOpenId(HttpRequestUtil.getWxOpenId());
        dto.setAuthorization(HttpRequestUtil.getToken());
        log.info("[微信端用户解绑]-OpenId:[{}],loginId:[{}]", dto.getWxOpenId(), dto.getLoginID());
        ccUserService.unbindWxUser(dto);
    }

    /**
     * 获取微信用户所员工有账号
     *
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "获取微信用户所员工有账号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/account")
    public List<FdSimpleUserVO> getWxUserBindJobNumbers(@RequestParam(required = false) String openId, @RequestParam String authorization) {
        List<FdSimpleUserVO> list = ccUserService.getWxUserBindJobNumbers(openId, authorization);
        if (!CollectionUtils.isEmpty(list)) {
            list.stream().forEach(fdSimpleUserVO -> {
                fdSimpleUserVO.setMobile(DataMaskUtil.maskMobile(fdSimpleUserVO.getMobile()));
            });
        }
        return list;
    }

    /**
     * 微信用户切换
     *
     * @param userId
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "微信用户切换")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "员工id", required = true, dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "jobCode", value = "岗位编码", required = true, dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "orgPath", value = "员工组织path", required = true, dataType = "string", paramType = "path"),
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/switch/{userId}")
    public WxSessionVO switchWxUser(@PathVariable String userId, @RequestParam String jobCode, @RequestParam String orgPath, @RequestParam(required = false) String openId, @RequestParam String authorization) {
        return ccUserService.switchWxUser(userId, jobCode, orgPath, openId, authorization);
    }

    /**
     * 微信用户解绑
     *
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "微信用户解绑")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query"),
            @ApiImplicitParam(name = "authorization", value = "授权token", required = true, dataType = "string", paramType = "query")
    })
    @GetMapping("/user/unbind")
    public void unbindWxUser(@RequestParam(required = false) String openId, @RequestParam String authorization) {
        ccUserService.unbindWxUser(openId, authorization);
    }

    /**
     * 获取微信用户头像信息base64字符串
     *
     * @param openId
     * @return
     */
    @ApiOperation(value = "获取微信用户头像信息base64字符串")
    @ApiImplicitParam(name = "openId", value = "微信openId", required = false, dataType = "string", paramType = "query")
    @GetMapping("/user/image")
    public Object getWxUserImageBase64(@RequestParam String openId) {
        return CommonResult.successResult(ccUserService.getWxUserImageBase64(openId));
    }

    /**
     * 二维码分享/二维码生成
     *
     * @param dto
     * @return
     */
    @ApiOperation(value = "二维码分享/二维码生成")
    @PostMapping("/qrcode")
    public Object getQrcodeBase64(@RequestBody QrcodeDTO dto) {
        if (dto.getMargin() == null) {
            dto.setMargin(0);
        }
        return CommonResult.successResult(ccUserService.getQrcodeBase64(dto));
    }


    /**
     * 微信用户上传文件
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "上传文件")
    @PostMapping("/user/file")
    public Object uploadFile(@RequestParam("file") MultipartFile file) {
        return CommonResult.successResult(fileService.uploadFilePrivate(file));
    }


    /**
     * 微信用户上传文件（文件是微信mediaId，需重新下载）
     *
     * @param mediaIds
     * @return
     */
    @ApiOperation(value = "上传文件（文件是微信mediaId，需重新下载）")
    @PostMapping("/user/mediaIdFile")
    public Object uploadMediaIdFile(@RequestParam("mediaIds") List<String> mediaIds) {
        return CommonResult.successResult(fileService.uploadMediaIdFilePrivate(mediaIds));
    }

    /**
     * @param openId
     * @param authorization
     * @return
     */
    @ApiOperation(value = "获取用户所属区域，区域办公室的返回的是总部，不再返回对应额区域机构树上的区域名称")
    @GetMapping("/user/getBranchRegionName")
    public String getBranchRegionName(@RequestParam(required = false) String openId, @RequestParam String authorization) {
        return ccUserService.getBranchRegionName(openId, authorization);
    }

    @ApiOperation(value = "根据工号查询用户名")
    @GetMapping("/getUserByUserId")
    public AuthUserNameVO getUsersByUserId(@RequestParam("userId") String userId) {
        AuthUserVO dbData = userService.getAuthUserByUserId(userId);
        if (Objects.isNull(dbData)) {
            return null;
        }
        return new AuthUserNameVO(userId, dbData.getUserName());
    }

    @ApiOperation(value = "根据工号查询用户名")
    @GetMapping("/user/listUserByOrg")
    public List<AuthUserNameVO> listUserByOrg() {
        return userService.listUserByOrg(null, ccUserService.checkAuthority().getOrganizationName())
                .stream().map(de -> new AuthUserNameVO(de.getUserId(), de.getUserName())).collect(Collectors.toList());
    }


    /**
     * 微信用户上传文件
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "上传文件到众安")
    @PostMapping("/za/user/file")
    public Object uploadFileAndZa(@RequestParam("file") MultipartFile file) {
        return CommonResult.successResult(fileService.uploadFilePrivateAndZa(file));
    }


    /**
     * 微信用户上传文件（文件是微信mediaId，需重新下载）
     *
     * @param mediaIds
     * @return
     */
    @ApiOperation(value = "上传文件众安（文件是微信mediaId，需重新下载）")
    @PostMapping("/za/user/mediaIdFile")
    public Object uploadMediaIdFileAndZa(@RequestParam("mediaIds") List<String> mediaIds) {
        return CommonResult.successResult(fileService.uploadMediaIdFilePrivateToZa(mediaIds));
    }
    @ApiOperation(value = "微信端工资条明细")
    @PostMapping("/user/commissionDetail")
    public WxUserCommissionDetailVo getUserCommissionDetail(WxUserCommissionDetailQuery query){
        return wxSettlementCostService.getWxUserCommissionDetail(query);
    }

}
