package com.cfpamf.ms.insur;

import com.cfpamf.ms.insur.base.config.DcDataSourceConfig;
import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.activiti.spring.boot.JpaProcessEngineAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 项目启动Main函数
 *
 * <AUTHOR>
 */
@Slf4j
@EnableAsync
@EnableCaching
@EnableSwagger2
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(exclude = {
        PageHelperAutoConfiguration.class,
        org.activiti.spring.boot.SecurityAutoConfiguration.class,
        SecurityAutoConfiguration.class,
        ManagementWebSecurityAutoConfiguration.class,
//        org.springframework.boot.autoconfigure.security.SecurityAutoConfiguration.class,
////        org.springframework.boot.actuate.autoconfigure.ManagementWebSecurityAutoConfiguration.class,
        JpaProcessEngineAutoConfiguration.class})
@ImportResource(locations = {"classpath:kaptcha.xml"})
@EnableFeignClients("com.cfpamf.ms")
@ComponentScan(basePackages = {"com.cfpamf.ms", "com.cfpamf.cmis", "com.cfpamf.ms.insur"})
public class Application {

    public static void main(String[] args) {

        SpringApplication.run(Application.class, args);
        log.info("===================================================================");
        log.info("======= insurance 2.1.3.v20211112 start success  ==================");
        log.info("===================================================================");
    }

    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setBufferRequestBody(false);
        return new RestTemplate(requestFactory);
    }
}
