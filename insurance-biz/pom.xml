<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cfpamf.ms</groupId>
        <artifactId>insurance</artifactId>
        <version>1.3.5.v20220509-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <version>1.3.4.v20200217</version>
    <artifactId>insurance-biz</artifactId>
    <packaging>jar</packaging>

    <name>insurance-biz</name>
    <description>小额保险业务微服务</description>
    <distributionManagement>
        <repository>
            <id>CFPAMF</id>
            <name>CFPAMF Repository</name>
            <url>http://nexus.pub.cfpamf.com/repository/releases/</url>
        </repository>
        <snapshotRepository>
            <id>CFPAMF</id>
            <name>CFPAMF Repository</name>
            <url>http://nexus.pub.cfpamf.com/repository/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>public</id>
            <name>Public Repositories</name>
            <url>http://nexus.pub.cfpamf.com/repository/public/</url>
            <releases>
            </releases>
            <snapshots>
            </snapshots>
        </repository>
    </repositories>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot-mybatis>1.3.2</spring-boot-mybatis>
        <tk-mybatis>1.1.5</tk-mybatis>
        <spring-session-redis>1.3.1.RELEASE</spring-session-redis>
        <spring-config-version>1.3.3.RELEASE</spring-config-version>
        <mybatis-pagehelper>1.2.10</mybatis-pagehelper>
        <!--        <apache-poi-version>4.1.0</apache-poi-version>-->
        <apache-poi-version>3.17</apache-poi-version>
        <aliyun-oss-version>3.1.0</aliyun-oss-version>
        <weixin-sdk-version>2.9.9.BETA</weixin-sdk-version>
        <zxing-version>3.3.0</zxing-version>
        <jxls-version>1.0.5</jxls-version>
        <jackson-version>2.8.10</jackson-version>
        <cfpamf-bms-version>1.0.1.v20200713-SNAPSHOT</cfpamf-bms-version>
        <cfpamf-custservice-version>2.3.2-SNAPSHOT</cfpamf-custservice-version>
        <cfpamf-rabbitmq-version>1.0.1</cfpamf-rabbitmq-version>
        <kaptcha-version>2.3.2</kaptcha-version>
        <fastjson-version>1.2.76</fastjson-version>
<!--        <mysql-version>5.1.40</mysql-version>-->
        <mysql-version>8.0.26</mysql-version>
        <postgresql-version>42.2.5</postgresql-version>
        <jsoup-version>1.11.2</jsoup-version>
        <apache-cxf-version>3.2.4</apache-cxf-version>
        <activiti.version>6.0.0</activiti.version>
        <powermock.version>2.0.9</powermock.version>
        <groovy.version>2.4.12</groovy.version>
        <druid.version>1.1.22</druid.version>
        <insurance-pay.version>1.0.3-SNAPSHOT</insurance-pay.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.2.10</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cfpamf.ms</groupId>
            <artifactId>insurance-service</artifactId>
            <version>1.3.5.v20220509-SNAPSHOT</version>
<!--            <version>1.3.5.v20220729-SNAPSHOT</version>-->
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-version}</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>${postgresql-version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.7</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>

            </resource>
        </resources>
    </build>
    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <env>local</env>
                <config_uri>mse-b81bd222-nacos-ans.mse.aliyuncs.com</config_uri>
                <username></username>
                <password></password>
                <namespace>612ffc01-c1b8-4698-9a3d-2d4f9404694f</namespace>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
                <config_uri>mse-b01f8ee2-nacos-ans.mse.aliyuncs.com</config_uri>
                <username></username>
                <password></password>
<!--                <config_uri>http://nacos-headless.dev-public.svc.cluster.local:8848</config_uri>-->
<!--                <username>nacos</username>-->
<!--                <password>nacos</password>-->
                <namespace>77a7a970-651e-4052-8e30-c476c85bdf50</namespace>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
<!--                <config_uri>http://nacos-headless.test-public.svc.cluster.local:8848</config_uri>-->
<!--                <username>nacos</username>-->
<!--                <password>nacos</password>-->
<!--                <namespace>612ffc01-c1b8-4698-9a3d-2d4f9404694f</namespace>-->
                <config_uri>mse-b81bd222-nacos-ans.mse.aliyuncs.com</config_uri>
                <username></username>
                <password></password>
                <namespace>612ffc01-c1b8-4698-9a3d-2d4f9404694f</namespace>
            </properties>
        </profile>
        <profile>
            <id>staging</id>
            <properties>
                <env>staging</env>
                <config_uri>mse-d67aed82-nacos-ans.mse.aliyuncs.com</config_uri>
                <username></username>
                <password></password>
<!--                <config_uri>http://nacos-headless.staging-public.svc.cluster.local:8848</config_uri>-->
<!--                <username>nacos</username>-->
<!--                <password>nacos</password>-->
                <namespace>56fa4ab0-b572-4808-86c7-ae17bd2d24bb</namespace>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
                <config_uri>mse-c04b0392-nacos-ans.mse.aliyuncs.com</config_uri>
                <username></username>
                <password></password>
<!--                <config_uri>http://nacos-headless.public.svc.cluster.local:8848</config_uri>-->
<!--                <username>app_read</username>-->
<!--                <password>app_read</password>-->
                <namespace>dc3010be-ff2e-43ca-b620-59c5bd6dc74b</namespace>
            </properties>
        </profile>
    </profiles>
</project>
