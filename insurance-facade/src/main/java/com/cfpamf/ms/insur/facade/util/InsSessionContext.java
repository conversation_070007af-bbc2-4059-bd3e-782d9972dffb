package com.cfpamf.ms.insur.facade.util;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.cfpamf.ms.bms.facade.util.JwtHelper;
import com.cfpamf.ms.bms.facade.vo.JwtUserInfo;
import com.cfpamf.ms.insur.facade.dto.InsUserInfo;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 保险session上下文
 *
 * <AUTHOR>
 */
@Component
public class InsSessionContext implements ApplicationContextAware {

    /**
     * Spring应用上下文环境
     */
    private static ApplicationContext applicationContext;


    /**
     * 获取保险员工信息
     *
     * @return
     */
    public static InsUserInfo getInsUserInfo() {
        JwtUserInfo jwtUserInfo = getJwtUserInfo();
        return JSON.parseObject(jwtUserInfo.getRandomCode(), InsUserInfo.class);
    }


    /**
     * 获取内部员工信息
     *
     * @return
     */
    public static JwtUserInfo getJwtUserInfo() {
        HttpServletRequest request = getRequest();
        String authorization = request.getHeader("authorization");
        if (StringUtils.isEmpty(authorization)) {
            authorization = request.getParameter("authorization");
        }
        JwtHelper jwtHelper = applicationContext.getBean(JwtHelper.class);
        if (StringUtils.isEmpty(authorization)) {
            throw new RuntimeException("authorization参数错误");
        }
        return jwtHelper.getUserFromToken(authorization);
    }

    /**
     * 获取session
     *
     * @return
     */
    private static HttpServletRequest getRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    }

    /**
     * 注入spring ApplicationContext
     *
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        InsSessionContext.applicationContext = applicationContext;
    }
}
